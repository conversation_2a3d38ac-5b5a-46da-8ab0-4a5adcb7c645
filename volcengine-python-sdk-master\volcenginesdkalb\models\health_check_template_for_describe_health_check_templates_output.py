# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HealthCheckTemplateForDescribeHealthCheckTemplatesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'description': 'str',
        'health_check_domain': 'str',
        'health_check_http_code': 'str',
        'health_check_http_version': 'str',
        'health_check_interval': 'int',
        'health_check_method': 'str',
        'health_check_protocol': 'str',
        'health_check_template_id': 'str',
        'health_check_template_name': 'str',
        'health_check_timeout': 'int',
        'health_check_uri': 'str',
        'healthy_threshold': 'int',
        'port': 'int',
        'project_name': 'str',
        'tags': 'list[TagForDescribeHealthCheckTemplatesOutput]',
        'unhealthy_threshold': 'int',
        'update_time': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'description': 'Description',
        'health_check_domain': 'HealthCheckDomain',
        'health_check_http_code': 'HealthCheckHttpCode',
        'health_check_http_version': 'HealthCheckHttpVersion',
        'health_check_interval': 'HealthCheckInterval',
        'health_check_method': 'HealthCheckMethod',
        'health_check_protocol': 'HealthCheckProtocol',
        'health_check_template_id': 'HealthCheckTemplateId',
        'health_check_template_name': 'HealthCheckTemplateName',
        'health_check_timeout': 'HealthCheckTimeout',
        'health_check_uri': 'HealthCheckURI',
        'healthy_threshold': 'HealthyThreshold',
        'port': 'Port',
        'project_name': 'ProjectName',
        'tags': 'Tags',
        'unhealthy_threshold': 'UnhealthyThreshold',
        'update_time': 'UpdateTime'
    }

    def __init__(self, create_time=None, description=None, health_check_domain=None, health_check_http_code=None, health_check_http_version=None, health_check_interval=None, health_check_method=None, health_check_protocol=None, health_check_template_id=None, health_check_template_name=None, health_check_timeout=None, health_check_uri=None, healthy_threshold=None, port=None, project_name=None, tags=None, unhealthy_threshold=None, update_time=None, _configuration=None):  # noqa: E501
        """HealthCheckTemplateForDescribeHealthCheckTemplatesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._description = None
        self._health_check_domain = None
        self._health_check_http_code = None
        self._health_check_http_version = None
        self._health_check_interval = None
        self._health_check_method = None
        self._health_check_protocol = None
        self._health_check_template_id = None
        self._health_check_template_name = None
        self._health_check_timeout = None
        self._health_check_uri = None
        self._healthy_threshold = None
        self._port = None
        self._project_name = None
        self._tags = None
        self._unhealthy_threshold = None
        self._update_time = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if health_check_domain is not None:
            self.health_check_domain = health_check_domain
        if health_check_http_code is not None:
            self.health_check_http_code = health_check_http_code
        if health_check_http_version is not None:
            self.health_check_http_version = health_check_http_version
        if health_check_interval is not None:
            self.health_check_interval = health_check_interval
        if health_check_method is not None:
            self.health_check_method = health_check_method
        if health_check_protocol is not None:
            self.health_check_protocol = health_check_protocol
        if health_check_template_id is not None:
            self.health_check_template_id = health_check_template_id
        if health_check_template_name is not None:
            self.health_check_template_name = health_check_template_name
        if health_check_timeout is not None:
            self.health_check_timeout = health_check_timeout
        if health_check_uri is not None:
            self.health_check_uri = health_check_uri
        if healthy_threshold is not None:
            self.healthy_threshold = healthy_threshold
        if port is not None:
            self.port = port
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags
        if unhealthy_threshold is not None:
            self.unhealthy_threshold = unhealthy_threshold
        if update_time is not None:
            self.update_time = update_time

    @property
    def create_time(self):
        """Gets the create_time of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The create_time of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param create_time: The create_time of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The description of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param description: The description of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def health_check_domain(self):
        """Gets the health_check_domain of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_domain of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._health_check_domain

    @health_check_domain.setter
    def health_check_domain(self, health_check_domain):
        """Sets the health_check_domain of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_domain: The health_check_domain of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._health_check_domain = health_check_domain

    @property
    def health_check_http_code(self):
        """Gets the health_check_http_code of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_http_code of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._health_check_http_code

    @health_check_http_code.setter
    def health_check_http_code(self, health_check_http_code):
        """Sets the health_check_http_code of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_http_code: The health_check_http_code of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._health_check_http_code = health_check_http_code

    @property
    def health_check_http_version(self):
        """Gets the health_check_http_version of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_http_version of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._health_check_http_version

    @health_check_http_version.setter
    def health_check_http_version(self, health_check_http_version):
        """Sets the health_check_http_version of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_http_version: The health_check_http_version of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._health_check_http_version = health_check_http_version

    @property
    def health_check_interval(self):
        """Gets the health_check_interval of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_interval of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: int
        """
        return self._health_check_interval

    @health_check_interval.setter
    def health_check_interval(self, health_check_interval):
        """Sets the health_check_interval of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_interval: The health_check_interval of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: int
        """

        self._health_check_interval = health_check_interval

    @property
    def health_check_method(self):
        """Gets the health_check_method of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_method of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._health_check_method

    @health_check_method.setter
    def health_check_method(self, health_check_method):
        """Sets the health_check_method of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_method: The health_check_method of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._health_check_method = health_check_method

    @property
    def health_check_protocol(self):
        """Gets the health_check_protocol of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_protocol of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._health_check_protocol

    @health_check_protocol.setter
    def health_check_protocol(self, health_check_protocol):
        """Sets the health_check_protocol of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_protocol: The health_check_protocol of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._health_check_protocol = health_check_protocol

    @property
    def health_check_template_id(self):
        """Gets the health_check_template_id of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_template_id of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._health_check_template_id

    @health_check_template_id.setter
    def health_check_template_id(self, health_check_template_id):
        """Sets the health_check_template_id of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_template_id: The health_check_template_id of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._health_check_template_id = health_check_template_id

    @property
    def health_check_template_name(self):
        """Gets the health_check_template_name of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_template_name of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._health_check_template_name

    @health_check_template_name.setter
    def health_check_template_name(self, health_check_template_name):
        """Sets the health_check_template_name of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_template_name: The health_check_template_name of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._health_check_template_name = health_check_template_name

    @property
    def health_check_timeout(self):
        """Gets the health_check_timeout of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_timeout of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: int
        """
        return self._health_check_timeout

    @health_check_timeout.setter
    def health_check_timeout(self, health_check_timeout):
        """Sets the health_check_timeout of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_timeout: The health_check_timeout of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: int
        """

        self._health_check_timeout = health_check_timeout

    @property
    def health_check_uri(self):
        """Gets the health_check_uri of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The health_check_uri of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._health_check_uri

    @health_check_uri.setter
    def health_check_uri(self, health_check_uri):
        """Sets the health_check_uri of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param health_check_uri: The health_check_uri of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._health_check_uri = health_check_uri

    @property
    def healthy_threshold(self):
        """Gets the healthy_threshold of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The healthy_threshold of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: int
        """
        return self._healthy_threshold

    @healthy_threshold.setter
    def healthy_threshold(self, healthy_threshold):
        """Sets the healthy_threshold of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param healthy_threshold: The healthy_threshold of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: int
        """

        self._healthy_threshold = healthy_threshold

    @property
    def port(self):
        """Gets the port of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The port of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param port: The port of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def project_name(self):
        """Gets the project_name of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The project_name of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param project_name: The project_name of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The tags of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: list[TagForDescribeHealthCheckTemplatesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param tags: The tags of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: list[TagForDescribeHealthCheckTemplatesOutput]
        """

        self._tags = tags

    @property
    def unhealthy_threshold(self):
        """Gets the unhealthy_threshold of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The unhealthy_threshold of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: int
        """
        return self._unhealthy_threshold

    @unhealthy_threshold.setter
    def unhealthy_threshold(self, unhealthy_threshold):
        """Sets the unhealthy_threshold of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param unhealthy_threshold: The unhealthy_threshold of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: int
        """

        self._unhealthy_threshold = unhealthy_threshold

    @property
    def update_time(self):
        """Gets the update_time of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501


        :return: The update_time of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.


        :param update_time: The update_time of this HealthCheckTemplateForDescribeHealthCheckTemplatesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HealthCheckTemplateForDescribeHealthCheckTemplatesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HealthCheckTemplateForDescribeHealthCheckTemplatesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HealthCheckTemplateForDescribeHealthCheckTemplatesOutput):
            return True

        return self.to_dict() != other.to_dict()
