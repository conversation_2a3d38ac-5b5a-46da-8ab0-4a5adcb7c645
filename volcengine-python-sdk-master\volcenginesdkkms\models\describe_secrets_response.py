# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSecretsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_info': 'PageInfoForDescribeSecretsOutput',
        'secrets': 'list[SecretForDescribeSecretsOutput]'
    }

    attribute_map = {
        'page_info': 'PageInfo',
        'secrets': 'Secrets'
    }

    def __init__(self, page_info=None, secrets=None, _configuration=None):  # noqa: E501
        """DescribeSecretsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_info = None
        self._secrets = None
        self.discriminator = None

        if page_info is not None:
            self.page_info = page_info
        if secrets is not None:
            self.secrets = secrets

    @property
    def page_info(self):
        """Gets the page_info of this DescribeSecretsResponse.  # noqa: E501


        :return: The page_info of this DescribeSecretsResponse.  # noqa: E501
        :rtype: PageInfoForDescribeSecretsOutput
        """
        return self._page_info

    @page_info.setter
    def page_info(self, page_info):
        """Sets the page_info of this DescribeSecretsResponse.


        :param page_info: The page_info of this DescribeSecretsResponse.  # noqa: E501
        :type: PageInfoForDescribeSecretsOutput
        """

        self._page_info = page_info

    @property
    def secrets(self):
        """Gets the secrets of this DescribeSecretsResponse.  # noqa: E501


        :return: The secrets of this DescribeSecretsResponse.  # noqa: E501
        :rtype: list[SecretForDescribeSecretsOutput]
        """
        return self._secrets

    @secrets.setter
    def secrets(self, secrets):
        """Sets the secrets of this DescribeSecretsResponse.


        :param secrets: The secrets of this DescribeSecretsResponse.  # noqa: E501
        :type: list[SecretForDescribeSecretsOutput]
        """

        self._secrets = secrets

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSecretsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSecretsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSecretsResponse):
            return True

        return self.to_dict() != other.to_dict()
