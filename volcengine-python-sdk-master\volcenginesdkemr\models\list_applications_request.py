# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListApplicationsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_names': 'list[str]',
        'cluster_id': 'str',
        'max_results': 'int',
        'next_token': 'str'
    }

    attribute_map = {
        'application_names': 'ApplicationNames',
        'cluster_id': 'ClusterId',
        'max_results': 'MaxResults',
        'next_token': 'NextToken'
    }

    def __init__(self, application_names=None, cluster_id=None, max_results=None, next_token=None, _configuration=None):  # noqa: E501
        """ListApplicationsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_names = None
        self._cluster_id = None
        self._max_results = None
        self._next_token = None
        self.discriminator = None

        if application_names is not None:
            self.application_names = application_names
        self.cluster_id = cluster_id
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token

    @property
    def application_names(self):
        """Gets the application_names of this ListApplicationsRequest.  # noqa: E501


        :return: The application_names of this ListApplicationsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._application_names

    @application_names.setter
    def application_names(self, application_names):
        """Sets the application_names of this ListApplicationsRequest.


        :param application_names: The application_names of this ListApplicationsRequest.  # noqa: E501
        :type: list[str]
        """

        self._application_names = application_names

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListApplicationsRequest.  # noqa: E501


        :return: The cluster_id of this ListApplicationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListApplicationsRequest.


        :param cluster_id: The cluster_id of this ListApplicationsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def max_results(self):
        """Gets the max_results of this ListApplicationsRequest.  # noqa: E501


        :return: The max_results of this ListApplicationsRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListApplicationsRequest.


        :param max_results: The max_results of this ListApplicationsRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                max_results is not None and max_results > 100):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                max_results is not None and max_results < 1):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value greater than or equal to `1`")  # noqa: E501

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListApplicationsRequest.  # noqa: E501


        :return: The next_token of this ListApplicationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListApplicationsRequest.


        :param next_token: The next_token of this ListApplicationsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListApplicationsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListApplicationsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListApplicationsRequest):
            return True

        return self.to_dict() != other.to_dict()
