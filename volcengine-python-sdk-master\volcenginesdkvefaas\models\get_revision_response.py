# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetRevisionResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'code_size': 'int',
        'code_size_limit': 'int',
        'creation_time': 'str',
        'description': 'str',
        'envs': 'list[EnvForGetRevisionOutput]',
        'exclusive_mode': 'bool',
        'health_check_config': 'HealthCheckConfigForGetRevisionOutput',
        'id': 'str',
        'initializer_sec': 'int',
        'instance_type': 'str',
        'max_concurrency': 'int',
        'max_replicas': 'int',
        'memory_mb': 'int',
        'name': 'str',
        'nas_storage': 'NasStorageForGetRevisionOutput',
        'request_timeout': 'int',
        'revision_creation_time': 'str',
        'revision_description': 'str',
        'revision_number': 'int',
        'runtime': 'str',
        'source': 'str',
        'source_location': 'str',
        'source_type': 'str',
        'tls_config': 'TlsConfigForGetRevisionOutput',
        'tos_mount_config': 'TosMountConfigForGetRevisionOutput',
        'vpc_config': 'VpcConfigForGetRevisionOutput'
    }

    attribute_map = {
        'code_size': 'CodeSize',
        'code_size_limit': 'CodeSizeLimit',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'envs': 'Envs',
        'exclusive_mode': 'ExclusiveMode',
        'health_check_config': 'HealthCheckConfig',
        'id': 'Id',
        'initializer_sec': 'InitializerSec',
        'instance_type': 'InstanceType',
        'max_concurrency': 'MaxConcurrency',
        'max_replicas': 'MaxReplicas',
        'memory_mb': 'MemoryMB',
        'name': 'Name',
        'nas_storage': 'NasStorage',
        'request_timeout': 'RequestTimeout',
        'revision_creation_time': 'RevisionCreationTime',
        'revision_description': 'RevisionDescription',
        'revision_number': 'RevisionNumber',
        'runtime': 'Runtime',
        'source': 'Source',
        'source_location': 'SourceLocation',
        'source_type': 'SourceType',
        'tls_config': 'TlsConfig',
        'tos_mount_config': 'TosMountConfig',
        'vpc_config': 'VpcConfig'
    }

    def __init__(self, code_size=None, code_size_limit=None, creation_time=None, description=None, envs=None, exclusive_mode=None, health_check_config=None, id=None, initializer_sec=None, instance_type=None, max_concurrency=None, max_replicas=None, memory_mb=None, name=None, nas_storage=None, request_timeout=None, revision_creation_time=None, revision_description=None, revision_number=None, runtime=None, source=None, source_location=None, source_type=None, tls_config=None, tos_mount_config=None, vpc_config=None, _configuration=None):  # noqa: E501
        """GetRevisionResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._code_size = None
        self._code_size_limit = None
        self._creation_time = None
        self._description = None
        self._envs = None
        self._exclusive_mode = None
        self._health_check_config = None
        self._id = None
        self._initializer_sec = None
        self._instance_type = None
        self._max_concurrency = None
        self._max_replicas = None
        self._memory_mb = None
        self._name = None
        self._nas_storage = None
        self._request_timeout = None
        self._revision_creation_time = None
        self._revision_description = None
        self._revision_number = None
        self._runtime = None
        self._source = None
        self._source_location = None
        self._source_type = None
        self._tls_config = None
        self._tos_mount_config = None
        self._vpc_config = None
        self.discriminator = None

        if code_size is not None:
            self.code_size = code_size
        if code_size_limit is not None:
            self.code_size_limit = code_size_limit
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if envs is not None:
            self.envs = envs
        if exclusive_mode is not None:
            self.exclusive_mode = exclusive_mode
        if health_check_config is not None:
            self.health_check_config = health_check_config
        if id is not None:
            self.id = id
        if initializer_sec is not None:
            self.initializer_sec = initializer_sec
        if instance_type is not None:
            self.instance_type = instance_type
        if max_concurrency is not None:
            self.max_concurrency = max_concurrency
        if max_replicas is not None:
            self.max_replicas = max_replicas
        if memory_mb is not None:
            self.memory_mb = memory_mb
        if name is not None:
            self.name = name
        if nas_storage is not None:
            self.nas_storage = nas_storage
        if request_timeout is not None:
            self.request_timeout = request_timeout
        if revision_creation_time is not None:
            self.revision_creation_time = revision_creation_time
        if revision_description is not None:
            self.revision_description = revision_description
        if revision_number is not None:
            self.revision_number = revision_number
        if runtime is not None:
            self.runtime = runtime
        if source is not None:
            self.source = source
        if source_location is not None:
            self.source_location = source_location
        if source_type is not None:
            self.source_type = source_type
        if tls_config is not None:
            self.tls_config = tls_config
        if tos_mount_config is not None:
            self.tos_mount_config = tos_mount_config
        if vpc_config is not None:
            self.vpc_config = vpc_config

    @property
    def code_size(self):
        """Gets the code_size of this GetRevisionResponse.  # noqa: E501


        :return: The code_size of this GetRevisionResponse.  # noqa: E501
        :rtype: int
        """
        return self._code_size

    @code_size.setter
    def code_size(self, code_size):
        """Sets the code_size of this GetRevisionResponse.


        :param code_size: The code_size of this GetRevisionResponse.  # noqa: E501
        :type: int
        """

        self._code_size = code_size

    @property
    def code_size_limit(self):
        """Gets the code_size_limit of this GetRevisionResponse.  # noqa: E501


        :return: The code_size_limit of this GetRevisionResponse.  # noqa: E501
        :rtype: int
        """
        return self._code_size_limit

    @code_size_limit.setter
    def code_size_limit(self, code_size_limit):
        """Sets the code_size_limit of this GetRevisionResponse.


        :param code_size_limit: The code_size_limit of this GetRevisionResponse.  # noqa: E501
        :type: int
        """

        self._code_size_limit = code_size_limit

    @property
    def creation_time(self):
        """Gets the creation_time of this GetRevisionResponse.  # noqa: E501


        :return: The creation_time of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this GetRevisionResponse.


        :param creation_time: The creation_time of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this GetRevisionResponse.  # noqa: E501


        :return: The description of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetRevisionResponse.


        :param description: The description of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def envs(self):
        """Gets the envs of this GetRevisionResponse.  # noqa: E501


        :return: The envs of this GetRevisionResponse.  # noqa: E501
        :rtype: list[EnvForGetRevisionOutput]
        """
        return self._envs

    @envs.setter
    def envs(self, envs):
        """Sets the envs of this GetRevisionResponse.


        :param envs: The envs of this GetRevisionResponse.  # noqa: E501
        :type: list[EnvForGetRevisionOutput]
        """

        self._envs = envs

    @property
    def exclusive_mode(self):
        """Gets the exclusive_mode of this GetRevisionResponse.  # noqa: E501


        :return: The exclusive_mode of this GetRevisionResponse.  # noqa: E501
        :rtype: bool
        """
        return self._exclusive_mode

    @exclusive_mode.setter
    def exclusive_mode(self, exclusive_mode):
        """Sets the exclusive_mode of this GetRevisionResponse.


        :param exclusive_mode: The exclusive_mode of this GetRevisionResponse.  # noqa: E501
        :type: bool
        """

        self._exclusive_mode = exclusive_mode

    @property
    def health_check_config(self):
        """Gets the health_check_config of this GetRevisionResponse.  # noqa: E501


        :return: The health_check_config of this GetRevisionResponse.  # noqa: E501
        :rtype: HealthCheckConfigForGetRevisionOutput
        """
        return self._health_check_config

    @health_check_config.setter
    def health_check_config(self, health_check_config):
        """Sets the health_check_config of this GetRevisionResponse.


        :param health_check_config: The health_check_config of this GetRevisionResponse.  # noqa: E501
        :type: HealthCheckConfigForGetRevisionOutput
        """

        self._health_check_config = health_check_config

    @property
    def id(self):
        """Gets the id of this GetRevisionResponse.  # noqa: E501


        :return: The id of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetRevisionResponse.


        :param id: The id of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def initializer_sec(self):
        """Gets the initializer_sec of this GetRevisionResponse.  # noqa: E501


        :return: The initializer_sec of this GetRevisionResponse.  # noqa: E501
        :rtype: int
        """
        return self._initializer_sec

    @initializer_sec.setter
    def initializer_sec(self, initializer_sec):
        """Sets the initializer_sec of this GetRevisionResponse.


        :param initializer_sec: The initializer_sec of this GetRevisionResponse.  # noqa: E501
        :type: int
        """

        self._initializer_sec = initializer_sec

    @property
    def instance_type(self):
        """Gets the instance_type of this GetRevisionResponse.  # noqa: E501


        :return: The instance_type of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this GetRevisionResponse.


        :param instance_type: The instance_type of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def max_concurrency(self):
        """Gets the max_concurrency of this GetRevisionResponse.  # noqa: E501


        :return: The max_concurrency of this GetRevisionResponse.  # noqa: E501
        :rtype: int
        """
        return self._max_concurrency

    @max_concurrency.setter
    def max_concurrency(self, max_concurrency):
        """Sets the max_concurrency of this GetRevisionResponse.


        :param max_concurrency: The max_concurrency of this GetRevisionResponse.  # noqa: E501
        :type: int
        """

        self._max_concurrency = max_concurrency

    @property
    def max_replicas(self):
        """Gets the max_replicas of this GetRevisionResponse.  # noqa: E501


        :return: The max_replicas of this GetRevisionResponse.  # noqa: E501
        :rtype: int
        """
        return self._max_replicas

    @max_replicas.setter
    def max_replicas(self, max_replicas):
        """Sets the max_replicas of this GetRevisionResponse.


        :param max_replicas: The max_replicas of this GetRevisionResponse.  # noqa: E501
        :type: int
        """

        self._max_replicas = max_replicas

    @property
    def memory_mb(self):
        """Gets the memory_mb of this GetRevisionResponse.  # noqa: E501


        :return: The memory_mb of this GetRevisionResponse.  # noqa: E501
        :rtype: int
        """
        return self._memory_mb

    @memory_mb.setter
    def memory_mb(self, memory_mb):
        """Sets the memory_mb of this GetRevisionResponse.


        :param memory_mb: The memory_mb of this GetRevisionResponse.  # noqa: E501
        :type: int
        """

        self._memory_mb = memory_mb

    @property
    def name(self):
        """Gets the name of this GetRevisionResponse.  # noqa: E501


        :return: The name of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetRevisionResponse.


        :param name: The name of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def nas_storage(self):
        """Gets the nas_storage of this GetRevisionResponse.  # noqa: E501


        :return: The nas_storage of this GetRevisionResponse.  # noqa: E501
        :rtype: NasStorageForGetRevisionOutput
        """
        return self._nas_storage

    @nas_storage.setter
    def nas_storage(self, nas_storage):
        """Sets the nas_storage of this GetRevisionResponse.


        :param nas_storage: The nas_storage of this GetRevisionResponse.  # noqa: E501
        :type: NasStorageForGetRevisionOutput
        """

        self._nas_storage = nas_storage

    @property
    def request_timeout(self):
        """Gets the request_timeout of this GetRevisionResponse.  # noqa: E501


        :return: The request_timeout of this GetRevisionResponse.  # noqa: E501
        :rtype: int
        """
        return self._request_timeout

    @request_timeout.setter
    def request_timeout(self, request_timeout):
        """Sets the request_timeout of this GetRevisionResponse.


        :param request_timeout: The request_timeout of this GetRevisionResponse.  # noqa: E501
        :type: int
        """

        self._request_timeout = request_timeout

    @property
    def revision_creation_time(self):
        """Gets the revision_creation_time of this GetRevisionResponse.  # noqa: E501


        :return: The revision_creation_time of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._revision_creation_time

    @revision_creation_time.setter
    def revision_creation_time(self, revision_creation_time):
        """Sets the revision_creation_time of this GetRevisionResponse.


        :param revision_creation_time: The revision_creation_time of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._revision_creation_time = revision_creation_time

    @property
    def revision_description(self):
        """Gets the revision_description of this GetRevisionResponse.  # noqa: E501


        :return: The revision_description of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._revision_description

    @revision_description.setter
    def revision_description(self, revision_description):
        """Sets the revision_description of this GetRevisionResponse.


        :param revision_description: The revision_description of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._revision_description = revision_description

    @property
    def revision_number(self):
        """Gets the revision_number of this GetRevisionResponse.  # noqa: E501


        :return: The revision_number of this GetRevisionResponse.  # noqa: E501
        :rtype: int
        """
        return self._revision_number

    @revision_number.setter
    def revision_number(self, revision_number):
        """Sets the revision_number of this GetRevisionResponse.


        :param revision_number: The revision_number of this GetRevisionResponse.  # noqa: E501
        :type: int
        """

        self._revision_number = revision_number

    @property
    def runtime(self):
        """Gets the runtime of this GetRevisionResponse.  # noqa: E501


        :return: The runtime of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._runtime

    @runtime.setter
    def runtime(self, runtime):
        """Sets the runtime of this GetRevisionResponse.


        :param runtime: The runtime of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._runtime = runtime

    @property
    def source(self):
        """Gets the source of this GetRevisionResponse.  # noqa: E501


        :return: The source of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this GetRevisionResponse.


        :param source: The source of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._source = source

    @property
    def source_location(self):
        """Gets the source_location of this GetRevisionResponse.  # noqa: E501


        :return: The source_location of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._source_location

    @source_location.setter
    def source_location(self, source_location):
        """Sets the source_location of this GetRevisionResponse.


        :param source_location: The source_location of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._source_location = source_location

    @property
    def source_type(self):
        """Gets the source_type of this GetRevisionResponse.  # noqa: E501


        :return: The source_type of this GetRevisionResponse.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this GetRevisionResponse.


        :param source_type: The source_type of this GetRevisionResponse.  # noqa: E501
        :type: str
        """

        self._source_type = source_type

    @property
    def tls_config(self):
        """Gets the tls_config of this GetRevisionResponse.  # noqa: E501


        :return: The tls_config of this GetRevisionResponse.  # noqa: E501
        :rtype: TlsConfigForGetRevisionOutput
        """
        return self._tls_config

    @tls_config.setter
    def tls_config(self, tls_config):
        """Sets the tls_config of this GetRevisionResponse.


        :param tls_config: The tls_config of this GetRevisionResponse.  # noqa: E501
        :type: TlsConfigForGetRevisionOutput
        """

        self._tls_config = tls_config

    @property
    def tos_mount_config(self):
        """Gets the tos_mount_config of this GetRevisionResponse.  # noqa: E501


        :return: The tos_mount_config of this GetRevisionResponse.  # noqa: E501
        :rtype: TosMountConfigForGetRevisionOutput
        """
        return self._tos_mount_config

    @tos_mount_config.setter
    def tos_mount_config(self, tos_mount_config):
        """Sets the tos_mount_config of this GetRevisionResponse.


        :param tos_mount_config: The tos_mount_config of this GetRevisionResponse.  # noqa: E501
        :type: TosMountConfigForGetRevisionOutput
        """

        self._tos_mount_config = tos_mount_config

    @property
    def vpc_config(self):
        """Gets the vpc_config of this GetRevisionResponse.  # noqa: E501


        :return: The vpc_config of this GetRevisionResponse.  # noqa: E501
        :rtype: VpcConfigForGetRevisionOutput
        """
        return self._vpc_config

    @vpc_config.setter
    def vpc_config(self, vpc_config):
        """Sets the vpc_config of this GetRevisionResponse.


        :param vpc_config: The vpc_config of this GetRevisionResponse.  # noqa: E501
        :type: VpcConfigForGetRevisionOutput
        """

        self._vpc_config = vpc_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetRevisionResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetRevisionResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetRevisionResponse):
            return True

        return self.to_dict() != other.to_dict()
