# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CityListForListDXPAccessPointsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ap_list': 'list[APListForListDXPAccessPointsOutput]',
        'city': 'str'
    }

    attribute_map = {
        'ap_list': 'APList',
        'city': 'City'
    }

    def __init__(self, ap_list=None, city=None, _configuration=None):  # noqa: E501
        """CityListForListDXPAccessPointsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ap_list = None
        self._city = None
        self.discriminator = None

        if ap_list is not None:
            self.ap_list = ap_list
        if city is not None:
            self.city = city

    @property
    def ap_list(self):
        """Gets the ap_list of this CityListForListDXPAccessPointsOutput.  # noqa: E501


        :return: The ap_list of this CityListForListDXPAccessPointsOutput.  # noqa: E501
        :rtype: list[APListForListDXPAccessPointsOutput]
        """
        return self._ap_list

    @ap_list.setter
    def ap_list(self, ap_list):
        """Sets the ap_list of this CityListForListDXPAccessPointsOutput.


        :param ap_list: The ap_list of this CityListForListDXPAccessPointsOutput.  # noqa: E501
        :type: list[APListForListDXPAccessPointsOutput]
        """

        self._ap_list = ap_list

    @property
    def city(self):
        """Gets the city of this CityListForListDXPAccessPointsOutput.  # noqa: E501


        :return: The city of this CityListForListDXPAccessPointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._city

    @city.setter
    def city(self, city):
        """Sets the city of this CityListForListDXPAccessPointsOutput.


        :param city: The city of this CityListForListDXPAccessPointsOutput.  # noqa: E501
        :type: str
        """

        self._city = city

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CityListForListDXPAccessPointsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CityListForListDXPAccessPointsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CityListForListDXPAccessPointsOutput):
            return True

        return self.to_dict() != other.to_dict()
