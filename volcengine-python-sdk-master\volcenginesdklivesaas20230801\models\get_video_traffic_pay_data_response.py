# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVideoTrafficPayDataResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'post_pay_video_traffic_result': 'list[PostPayVideoTrafficResultForGetVideoTrafficPayDataOutput]',
        'pre_pay_video_traffic_result': 'list[PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput]'
    }

    attribute_map = {
        'post_pay_video_traffic_result': 'PostPayVideoTrafficResult',
        'pre_pay_video_traffic_result': 'PrePayVideoTrafficResult'
    }

    def __init__(self, post_pay_video_traffic_result=None, pre_pay_video_traffic_result=None, _configuration=None):  # noqa: E501
        """GetVideoTrafficPayDataResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._post_pay_video_traffic_result = None
        self._pre_pay_video_traffic_result = None
        self.discriminator = None

        if post_pay_video_traffic_result is not None:
            self.post_pay_video_traffic_result = post_pay_video_traffic_result
        if pre_pay_video_traffic_result is not None:
            self.pre_pay_video_traffic_result = pre_pay_video_traffic_result

    @property
    def post_pay_video_traffic_result(self):
        """Gets the post_pay_video_traffic_result of this GetVideoTrafficPayDataResponse.  # noqa: E501


        :return: The post_pay_video_traffic_result of this GetVideoTrafficPayDataResponse.  # noqa: E501
        :rtype: list[PostPayVideoTrafficResultForGetVideoTrafficPayDataOutput]
        """
        return self._post_pay_video_traffic_result

    @post_pay_video_traffic_result.setter
    def post_pay_video_traffic_result(self, post_pay_video_traffic_result):
        """Sets the post_pay_video_traffic_result of this GetVideoTrafficPayDataResponse.


        :param post_pay_video_traffic_result: The post_pay_video_traffic_result of this GetVideoTrafficPayDataResponse.  # noqa: E501
        :type: list[PostPayVideoTrafficResultForGetVideoTrafficPayDataOutput]
        """

        self._post_pay_video_traffic_result = post_pay_video_traffic_result

    @property
    def pre_pay_video_traffic_result(self):
        """Gets the pre_pay_video_traffic_result of this GetVideoTrafficPayDataResponse.  # noqa: E501


        :return: The pre_pay_video_traffic_result of this GetVideoTrafficPayDataResponse.  # noqa: E501
        :rtype: list[PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput]
        """
        return self._pre_pay_video_traffic_result

    @pre_pay_video_traffic_result.setter
    def pre_pay_video_traffic_result(self, pre_pay_video_traffic_result):
        """Sets the pre_pay_video_traffic_result of this GetVideoTrafficPayDataResponse.


        :param pre_pay_video_traffic_result: The pre_pay_video_traffic_result of this GetVideoTrafficPayDataResponse.  # noqa: E501
        :type: list[PrePayVideoTrafficResultForGetVideoTrafficPayDataOutput]
        """

        self._pre_pay_video_traffic_result = pre_pay_video_traffic_result

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVideoTrafficPayDataResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVideoTrafficPayDataResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVideoTrafficPayDataResponse):
            return True

        return self.to_dict() != other.to_dict()
