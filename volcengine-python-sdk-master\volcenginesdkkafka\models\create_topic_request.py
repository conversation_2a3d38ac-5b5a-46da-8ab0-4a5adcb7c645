# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTopicRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_policies': 'list[AccessPolicyForCreateTopicInput]',
        'all_authority': 'bool',
        'cleanup_policy': 'list[str]',
        'description': 'str',
        'instance_id': 'str',
        'parameters': 'str',
        'partition_number': 'int',
        'replica_number': 'int',
        'tags': 'list[TagForCreateTopicInput]',
        'topic_name': 'str'
    }

    attribute_map = {
        'access_policies': 'AccessPolicies',
        'all_authority': 'AllAuthority',
        'cleanup_policy': 'CleanupPolicy',
        'description': 'Description',
        'instance_id': 'InstanceId',
        'parameters': 'Parameters',
        'partition_number': 'PartitionNumber',
        'replica_number': 'ReplicaNumber',
        'tags': 'Tags',
        'topic_name': 'TopicName'
    }

    def __init__(self, access_policies=None, all_authority=None, cleanup_policy=None, description=None, instance_id=None, parameters=None, partition_number=None, replica_number=None, tags=None, topic_name=None, _configuration=None):  # noqa: E501
        """CreateTopicRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_policies = None
        self._all_authority = None
        self._cleanup_policy = None
        self._description = None
        self._instance_id = None
        self._parameters = None
        self._partition_number = None
        self._replica_number = None
        self._tags = None
        self._topic_name = None
        self.discriminator = None

        if access_policies is not None:
            self.access_policies = access_policies
        if all_authority is not None:
            self.all_authority = all_authority
        if cleanup_policy is not None:
            self.cleanup_policy = cleanup_policy
        if description is not None:
            self.description = description
        self.instance_id = instance_id
        if parameters is not None:
            self.parameters = parameters
        self.partition_number = partition_number
        if replica_number is not None:
            self.replica_number = replica_number
        if tags is not None:
            self.tags = tags
        self.topic_name = topic_name

    @property
    def access_policies(self):
        """Gets the access_policies of this CreateTopicRequest.  # noqa: E501


        :return: The access_policies of this CreateTopicRequest.  # noqa: E501
        :rtype: list[AccessPolicyForCreateTopicInput]
        """
        return self._access_policies

    @access_policies.setter
    def access_policies(self, access_policies):
        """Sets the access_policies of this CreateTopicRequest.


        :param access_policies: The access_policies of this CreateTopicRequest.  # noqa: E501
        :type: list[AccessPolicyForCreateTopicInput]
        """

        self._access_policies = access_policies

    @property
    def all_authority(self):
        """Gets the all_authority of this CreateTopicRequest.  # noqa: E501


        :return: The all_authority of this CreateTopicRequest.  # noqa: E501
        :rtype: bool
        """
        return self._all_authority

    @all_authority.setter
    def all_authority(self, all_authority):
        """Sets the all_authority of this CreateTopicRequest.


        :param all_authority: The all_authority of this CreateTopicRequest.  # noqa: E501
        :type: bool
        """

        self._all_authority = all_authority

    @property
    def cleanup_policy(self):
        """Gets the cleanup_policy of this CreateTopicRequest.  # noqa: E501


        :return: The cleanup_policy of this CreateTopicRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cleanup_policy

    @cleanup_policy.setter
    def cleanup_policy(self, cleanup_policy):
        """Sets the cleanup_policy of this CreateTopicRequest.


        :param cleanup_policy: The cleanup_policy of this CreateTopicRequest.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["delete", "compact"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(cleanup_policy).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `cleanup_policy` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(cleanup_policy) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._cleanup_policy = cleanup_policy

    @property
    def description(self):
        """Gets the description of this CreateTopicRequest.  # noqa: E501


        :return: The description of this CreateTopicRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateTopicRequest.


        :param description: The description of this CreateTopicRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateTopicRequest.  # noqa: E501


        :return: The instance_id of this CreateTopicRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateTopicRequest.


        :param instance_id: The instance_id of this CreateTopicRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def parameters(self):
        """Gets the parameters of this CreateTopicRequest.  # noqa: E501


        :return: The parameters of this CreateTopicRequest.  # noqa: E501
        :rtype: str
        """
        return self._parameters

    @parameters.setter
    def parameters(self, parameters):
        """Sets the parameters of this CreateTopicRequest.


        :param parameters: The parameters of this CreateTopicRequest.  # noqa: E501
        :type: str
        """

        self._parameters = parameters

    @property
    def partition_number(self):
        """Gets the partition_number of this CreateTopicRequest.  # noqa: E501


        :return: The partition_number of this CreateTopicRequest.  # noqa: E501
        :rtype: int
        """
        return self._partition_number

    @partition_number.setter
    def partition_number(self, partition_number):
        """Sets the partition_number of this CreateTopicRequest.


        :param partition_number: The partition_number of this CreateTopicRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and partition_number is None:
            raise ValueError("Invalid value for `partition_number`, must not be `None`")  # noqa: E501

        self._partition_number = partition_number

    @property
    def replica_number(self):
        """Gets the replica_number of this CreateTopicRequest.  # noqa: E501


        :return: The replica_number of this CreateTopicRequest.  # noqa: E501
        :rtype: int
        """
        return self._replica_number

    @replica_number.setter
    def replica_number(self, replica_number):
        """Sets the replica_number of this CreateTopicRequest.


        :param replica_number: The replica_number of this CreateTopicRequest.  # noqa: E501
        :type: int
        """

        self._replica_number = replica_number

    @property
    def tags(self):
        """Gets the tags of this CreateTopicRequest.  # noqa: E501


        :return: The tags of this CreateTopicRequest.  # noqa: E501
        :rtype: list[TagForCreateTopicInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateTopicRequest.


        :param tags: The tags of this CreateTopicRequest.  # noqa: E501
        :type: list[TagForCreateTopicInput]
        """

        self._tags = tags

    @property
    def topic_name(self):
        """Gets the topic_name of this CreateTopicRequest.  # noqa: E501


        :return: The topic_name of this CreateTopicRequest.  # noqa: E501
        :rtype: str
        """
        return self._topic_name

    @topic_name.setter
    def topic_name(self, topic_name):
        """Sets the topic_name of this CreateTopicRequest.


        :param topic_name: The topic_name of this CreateTopicRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and topic_name is None:
            raise ValueError("Invalid value for `topic_name`, must not be `None`")  # noqa: E501

        self._topic_name = topic_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTopicRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTopicRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTopicRequest):
            return True

        return self.to_dict() != other.to_dict()
