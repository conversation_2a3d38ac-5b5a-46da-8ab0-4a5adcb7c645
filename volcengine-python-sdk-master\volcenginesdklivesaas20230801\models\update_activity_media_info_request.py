# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateActivityMediaInfoRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'interaction_script_id': 'int',
        'name': 'str'
    }

    attribute_map = {
        'id': 'Id',
        'interaction_script_id': 'InteractionScriptId',
        'name': 'Name'
    }

    def __init__(self, id=None, interaction_script_id=None, name=None, _configuration=None):  # noqa: E501
        """UpdateActivityMediaInfoRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._interaction_script_id = None
        self._name = None
        self.discriminator = None

        self.id = id
        if interaction_script_id is not None:
            self.interaction_script_id = interaction_script_id
        if name is not None:
            self.name = name

    @property
    def id(self):
        """Gets the id of this UpdateActivityMediaInfoRequest.  # noqa: E501


        :return: The id of this UpdateActivityMediaInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateActivityMediaInfoRequest.


        :param id: The id of this UpdateActivityMediaInfoRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def interaction_script_id(self):
        """Gets the interaction_script_id of this UpdateActivityMediaInfoRequest.  # noqa: E501


        :return: The interaction_script_id of this UpdateActivityMediaInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._interaction_script_id

    @interaction_script_id.setter
    def interaction_script_id(self, interaction_script_id):
        """Sets the interaction_script_id of this UpdateActivityMediaInfoRequest.


        :param interaction_script_id: The interaction_script_id of this UpdateActivityMediaInfoRequest.  # noqa: E501
        :type: int
        """

        self._interaction_script_id = interaction_script_id

    @property
    def name(self):
        """Gets the name of this UpdateActivityMediaInfoRequest.  # noqa: E501


        :return: The name of this UpdateActivityMediaInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateActivityMediaInfoRequest.


        :param name: The name of this UpdateActivityMediaInfoRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateActivityMediaInfoRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateActivityMediaInfoRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateActivityMediaInfoRequest):
            return True

        return self.to_dict() != other.to_dict()
