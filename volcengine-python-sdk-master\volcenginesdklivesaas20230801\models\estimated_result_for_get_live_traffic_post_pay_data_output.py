# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EstimatedResultForGetLiveTrafficPostPayDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_creator': 'ActivityCreatorForGetLiveTrafficPostPayDataOutput',
        'activity_id': 'int',
        'live_traffic': 'float'
    }

    attribute_map = {
        'activity_creator': 'ActivityCreator',
        'activity_id': 'ActivityId',
        'live_traffic': 'LiveTraffic'
    }

    def __init__(self, activity_creator=None, activity_id=None, live_traffic=None, _configuration=None):  # noqa: E501
        """EstimatedResultForGetLiveTrafficPostPayDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_creator = None
        self._activity_id = None
        self._live_traffic = None
        self.discriminator = None

        if activity_creator is not None:
            self.activity_creator = activity_creator
        if activity_id is not None:
            self.activity_id = activity_id
        if live_traffic is not None:
            self.live_traffic = live_traffic

    @property
    def activity_creator(self):
        """Gets the activity_creator of this EstimatedResultForGetLiveTrafficPostPayDataOutput.  # noqa: E501


        :return: The activity_creator of this EstimatedResultForGetLiveTrafficPostPayDataOutput.  # noqa: E501
        :rtype: ActivityCreatorForGetLiveTrafficPostPayDataOutput
        """
        return self._activity_creator

    @activity_creator.setter
    def activity_creator(self, activity_creator):
        """Sets the activity_creator of this EstimatedResultForGetLiveTrafficPostPayDataOutput.


        :param activity_creator: The activity_creator of this EstimatedResultForGetLiveTrafficPostPayDataOutput.  # noqa: E501
        :type: ActivityCreatorForGetLiveTrafficPostPayDataOutput
        """

        self._activity_creator = activity_creator

    @property
    def activity_id(self):
        """Gets the activity_id of this EstimatedResultForGetLiveTrafficPostPayDataOutput.  # noqa: E501


        :return: The activity_id of this EstimatedResultForGetLiveTrafficPostPayDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this EstimatedResultForGetLiveTrafficPostPayDataOutput.


        :param activity_id: The activity_id of this EstimatedResultForGetLiveTrafficPostPayDataOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def live_traffic(self):
        """Gets the live_traffic of this EstimatedResultForGetLiveTrafficPostPayDataOutput.  # noqa: E501


        :return: The live_traffic of this EstimatedResultForGetLiveTrafficPostPayDataOutput.  # noqa: E501
        :rtype: float
        """
        return self._live_traffic

    @live_traffic.setter
    def live_traffic(self, live_traffic):
        """Sets the live_traffic of this EstimatedResultForGetLiveTrafficPostPayDataOutput.


        :param live_traffic: The live_traffic of this EstimatedResultForGetLiveTrafficPostPayDataOutput.  # noqa: E501
        :type: float
        """

        self._live_traffic = live_traffic

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EstimatedResultForGetLiveTrafficPostPayDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EstimatedResultForGetLiveTrafficPostPayDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EstimatedResultForGetLiveTrafficPostPayDataOutput):
            return True

        return self.to_dict() != other.to_dict()
