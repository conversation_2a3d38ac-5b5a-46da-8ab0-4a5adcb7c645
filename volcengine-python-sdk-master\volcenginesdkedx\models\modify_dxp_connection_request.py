# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDXPConnectionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'str',
        'contact_name': 'str',
        'contact_phone': 'str',
        'field_engineer': 'list[FieldEngineerForModifyDXPConnectionInput]',
        'instance_id': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'contact_name': 'ContactName',
        'contact_phone': 'ContactPhone',
        'field_engineer': 'FieldEngineer',
        'instance_id': 'InstanceId'
    }

    def __init__(self, bandwidth=None, contact_name=None, contact_phone=None, field_engineer=None, instance_id=None, _configuration=None):  # noqa: E501
        """ModifyDXPConnectionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._contact_name = None
        self._contact_phone = None
        self._field_engineer = None
        self._instance_id = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if contact_name is not None:
            self.contact_name = contact_name
        if contact_phone is not None:
            self.contact_phone = contact_phone
        if field_engineer is not None:
            self.field_engineer = field_engineer
        self.instance_id = instance_id

    @property
    def bandwidth(self):
        """Gets the bandwidth of this ModifyDXPConnectionRequest.  # noqa: E501


        :return: The bandwidth of this ModifyDXPConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this ModifyDXPConnectionRequest.


        :param bandwidth: The bandwidth of this ModifyDXPConnectionRequest.  # noqa: E501
        :type: str
        """

        self._bandwidth = bandwidth

    @property
    def contact_name(self):
        """Gets the contact_name of this ModifyDXPConnectionRequest.  # noqa: E501


        :return: The contact_name of this ModifyDXPConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._contact_name

    @contact_name.setter
    def contact_name(self, contact_name):
        """Sets the contact_name of this ModifyDXPConnectionRequest.


        :param contact_name: The contact_name of this ModifyDXPConnectionRequest.  # noqa: E501
        :type: str
        """

        self._contact_name = contact_name

    @property
    def contact_phone(self):
        """Gets the contact_phone of this ModifyDXPConnectionRequest.  # noqa: E501


        :return: The contact_phone of this ModifyDXPConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._contact_phone

    @contact_phone.setter
    def contact_phone(self, contact_phone):
        """Sets the contact_phone of this ModifyDXPConnectionRequest.


        :param contact_phone: The contact_phone of this ModifyDXPConnectionRequest.  # noqa: E501
        :type: str
        """

        self._contact_phone = contact_phone

    @property
    def field_engineer(self):
        """Gets the field_engineer of this ModifyDXPConnectionRequest.  # noqa: E501


        :return: The field_engineer of this ModifyDXPConnectionRequest.  # noqa: E501
        :rtype: list[FieldEngineerForModifyDXPConnectionInput]
        """
        return self._field_engineer

    @field_engineer.setter
    def field_engineer(self, field_engineer):
        """Sets the field_engineer of this ModifyDXPConnectionRequest.


        :param field_engineer: The field_engineer of this ModifyDXPConnectionRequest.  # noqa: E501
        :type: list[FieldEngineerForModifyDXPConnectionInput]
        """

        self._field_engineer = field_engineer

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDXPConnectionRequest.  # noqa: E501


        :return: The instance_id of this ModifyDXPConnectionRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDXPConnectionRequest.


        :param instance_id: The instance_id of this ModifyDXPConnectionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDXPConnectionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDXPConnectionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDXPConnectionRequest):
            return True

        return self.to_dict() != other.to_dict()
