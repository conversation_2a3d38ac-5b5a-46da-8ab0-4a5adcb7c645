# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeContentTaskByTaskIdResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sub_tasks': 'list[SubTaskForDescribeContentTaskByTaskIdOutput]',
        'vendors_meta_data': 'list[VendorsMetaDataForDescribeContentTaskByTaskIdOutput]'
    }

    attribute_map = {
        'sub_tasks': 'SubTasks',
        'vendors_meta_data': 'VendorsMetaData'
    }

    def __init__(self, sub_tasks=None, vendors_meta_data=None, _configuration=None):  # noqa: E501
        """DescribeContentTaskByTaskIdResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._sub_tasks = None
        self._vendors_meta_data = None
        self.discriminator = None

        if sub_tasks is not None:
            self.sub_tasks = sub_tasks
        if vendors_meta_data is not None:
            self.vendors_meta_data = vendors_meta_data

    @property
    def sub_tasks(self):
        """Gets the sub_tasks of this DescribeContentTaskByTaskIdResponse.  # noqa: E501


        :return: The sub_tasks of this DescribeContentTaskByTaskIdResponse.  # noqa: E501
        :rtype: list[SubTaskForDescribeContentTaskByTaskIdOutput]
        """
        return self._sub_tasks

    @sub_tasks.setter
    def sub_tasks(self, sub_tasks):
        """Sets the sub_tasks of this DescribeContentTaskByTaskIdResponse.


        :param sub_tasks: The sub_tasks of this DescribeContentTaskByTaskIdResponse.  # noqa: E501
        :type: list[SubTaskForDescribeContentTaskByTaskIdOutput]
        """

        self._sub_tasks = sub_tasks

    @property
    def vendors_meta_data(self):
        """Gets the vendors_meta_data of this DescribeContentTaskByTaskIdResponse.  # noqa: E501


        :return: The vendors_meta_data of this DescribeContentTaskByTaskIdResponse.  # noqa: E501
        :rtype: list[VendorsMetaDataForDescribeContentTaskByTaskIdOutput]
        """
        return self._vendors_meta_data

    @vendors_meta_data.setter
    def vendors_meta_data(self, vendors_meta_data):
        """Sets the vendors_meta_data of this DescribeContentTaskByTaskIdResponse.


        :param vendors_meta_data: The vendors_meta_data of this DescribeContentTaskByTaskIdResponse.  # noqa: E501
        :type: list[VendorsMetaDataForDescribeContentTaskByTaskIdOutput]
        """

        self._vendors_meta_data = vendors_meta_data

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeContentTaskByTaskIdResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeContentTaskByTaskIdResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeContentTaskByTaskIdResponse):
            return True

        return self.to_dict() != other.to_dict()
