# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CustomHostForCreateTaskInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'raw_host_port': 'str',
        'resolve_list': 'list[str]'
    }

    attribute_map = {
        'raw_host_port': 'RawHostPort',
        'resolve_list': 'ResolveList'
    }

    def __init__(self, raw_host_port=None, resolve_list=None, _configuration=None):  # noqa: E501
        """CustomHostForCreateTaskInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._raw_host_port = None
        self._resolve_list = None
        self.discriminator = None

        if raw_host_port is not None:
            self.raw_host_port = raw_host_port
        if resolve_list is not None:
            self.resolve_list = resolve_list

    @property
    def raw_host_port(self):
        """Gets the raw_host_port of this CustomHostForCreateTaskInput.  # noqa: E501


        :return: The raw_host_port of this CustomHostForCreateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._raw_host_port

    @raw_host_port.setter
    def raw_host_port(self, raw_host_port):
        """Sets the raw_host_port of this CustomHostForCreateTaskInput.


        :param raw_host_port: The raw_host_port of this CustomHostForCreateTaskInput.  # noqa: E501
        :type: str
        """

        self._raw_host_port = raw_host_port

    @property
    def resolve_list(self):
        """Gets the resolve_list of this CustomHostForCreateTaskInput.  # noqa: E501


        :return: The resolve_list of this CustomHostForCreateTaskInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._resolve_list

    @resolve_list.setter
    def resolve_list(self, resolve_list):
        """Sets the resolve_list of this CustomHostForCreateTaskInput.


        :param resolve_list: The resolve_list of this CustomHostForCreateTaskInput.  # noqa: E501
        :type: list[str]
        """

        self._resolve_list = resolve_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CustomHostForCreateTaskInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CustomHostForCreateTaskInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CustomHostForCreateTaskInput):
            return True

        return self.to_dict() != other.to_dict()
