# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyNLBServerGroupAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bypass_security_group_enabled': 'bool',
        'connection_drain_enabled': 'bool',
        'connection_drain_timeout': 'int',
        'description': 'str',
        'health_check': 'HealthCheckForModifyNLBServerGroupAttributesInput',
        'preserve_client_ip_enabled': 'bool',
        'proxy_protocol_type': 'str',
        'scheduler': 'str',
        'server_group_id': 'str',
        'server_group_name': 'str',
        'session_persistence_enabled': 'bool',
        'session_persistence_timeout': 'int',
        'timestamp_remove_enabled': 'bool'
    }

    attribute_map = {
        'bypass_security_group_enabled': 'BypassSecurityGroupEnabled',
        'connection_drain_enabled': 'ConnectionDrainEnabled',
        'connection_drain_timeout': 'ConnectionDrainTimeout',
        'description': 'Description',
        'health_check': 'HealthCheck',
        'preserve_client_ip_enabled': 'PreserveClientIpEnabled',
        'proxy_protocol_type': 'ProxyProtocolType',
        'scheduler': 'Scheduler',
        'server_group_id': 'ServerGroupId',
        'server_group_name': 'ServerGroupName',
        'session_persistence_enabled': 'SessionPersistenceEnabled',
        'session_persistence_timeout': 'SessionPersistenceTimeout',
        'timestamp_remove_enabled': 'TimestampRemoveEnabled'
    }

    def __init__(self, bypass_security_group_enabled=None, connection_drain_enabled=None, connection_drain_timeout=None, description=None, health_check=None, preserve_client_ip_enabled=None, proxy_protocol_type=None, scheduler=None, server_group_id=None, server_group_name=None, session_persistence_enabled=None, session_persistence_timeout=None, timestamp_remove_enabled=None, _configuration=None):  # noqa: E501
        """ModifyNLBServerGroupAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bypass_security_group_enabled = None
        self._connection_drain_enabled = None
        self._connection_drain_timeout = None
        self._description = None
        self._health_check = None
        self._preserve_client_ip_enabled = None
        self._proxy_protocol_type = None
        self._scheduler = None
        self._server_group_id = None
        self._server_group_name = None
        self._session_persistence_enabled = None
        self._session_persistence_timeout = None
        self._timestamp_remove_enabled = None
        self.discriminator = None

        if bypass_security_group_enabled is not None:
            self.bypass_security_group_enabled = bypass_security_group_enabled
        if connection_drain_enabled is not None:
            self.connection_drain_enabled = connection_drain_enabled
        if connection_drain_timeout is not None:
            self.connection_drain_timeout = connection_drain_timeout
        if description is not None:
            self.description = description
        if health_check is not None:
            self.health_check = health_check
        if preserve_client_ip_enabled is not None:
            self.preserve_client_ip_enabled = preserve_client_ip_enabled
        if proxy_protocol_type is not None:
            self.proxy_protocol_type = proxy_protocol_type
        if scheduler is not None:
            self.scheduler = scheduler
        self.server_group_id = server_group_id
        if server_group_name is not None:
            self.server_group_name = server_group_name
        if session_persistence_enabled is not None:
            self.session_persistence_enabled = session_persistence_enabled
        if session_persistence_timeout is not None:
            self.session_persistence_timeout = session_persistence_timeout
        if timestamp_remove_enabled is not None:
            self.timestamp_remove_enabled = timestamp_remove_enabled

    @property
    def bypass_security_group_enabled(self):
        """Gets the bypass_security_group_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The bypass_security_group_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._bypass_security_group_enabled

    @bypass_security_group_enabled.setter
    def bypass_security_group_enabled(self, bypass_security_group_enabled):
        """Sets the bypass_security_group_enabled of this ModifyNLBServerGroupAttributesRequest.


        :param bypass_security_group_enabled: The bypass_security_group_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._bypass_security_group_enabled = bypass_security_group_enabled

    @property
    def connection_drain_enabled(self):
        """Gets the connection_drain_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The connection_drain_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._connection_drain_enabled

    @connection_drain_enabled.setter
    def connection_drain_enabled(self, connection_drain_enabled):
        """Sets the connection_drain_enabled of this ModifyNLBServerGroupAttributesRequest.


        :param connection_drain_enabled: The connection_drain_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._connection_drain_enabled = connection_drain_enabled

    @property
    def connection_drain_timeout(self):
        """Gets the connection_drain_timeout of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The connection_drain_timeout of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._connection_drain_timeout

    @connection_drain_timeout.setter
    def connection_drain_timeout(self, connection_drain_timeout):
        """Sets the connection_drain_timeout of this ModifyNLBServerGroupAttributesRequest.


        :param connection_drain_timeout: The connection_drain_timeout of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: int
        """

        self._connection_drain_timeout = connection_drain_timeout

    @property
    def description(self):
        """Gets the description of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The description of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyNLBServerGroupAttributesRequest.


        :param description: The description of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def health_check(self):
        """Gets the health_check of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The health_check of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: HealthCheckForModifyNLBServerGroupAttributesInput
        """
        return self._health_check

    @health_check.setter
    def health_check(self, health_check):
        """Sets the health_check of this ModifyNLBServerGroupAttributesRequest.


        :param health_check: The health_check of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: HealthCheckForModifyNLBServerGroupAttributesInput
        """

        self._health_check = health_check

    @property
    def preserve_client_ip_enabled(self):
        """Gets the preserve_client_ip_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The preserve_client_ip_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._preserve_client_ip_enabled

    @preserve_client_ip_enabled.setter
    def preserve_client_ip_enabled(self, preserve_client_ip_enabled):
        """Sets the preserve_client_ip_enabled of this ModifyNLBServerGroupAttributesRequest.


        :param preserve_client_ip_enabled: The preserve_client_ip_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._preserve_client_ip_enabled = preserve_client_ip_enabled

    @property
    def proxy_protocol_type(self):
        """Gets the proxy_protocol_type of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The proxy_protocol_type of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._proxy_protocol_type

    @proxy_protocol_type.setter
    def proxy_protocol_type(self, proxy_protocol_type):
        """Sets the proxy_protocol_type of this ModifyNLBServerGroupAttributesRequest.


        :param proxy_protocol_type: The proxy_protocol_type of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: str
        """

        self._proxy_protocol_type = proxy_protocol_type

    @property
    def scheduler(self):
        """Gets the scheduler of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The scheduler of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._scheduler

    @scheduler.setter
    def scheduler(self, scheduler):
        """Sets the scheduler of this ModifyNLBServerGroupAttributesRequest.


        :param scheduler: The scheduler of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: str
        """

        self._scheduler = scheduler

    @property
    def server_group_id(self):
        """Gets the server_group_id of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The server_group_id of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this ModifyNLBServerGroupAttributesRequest.


        :param server_group_id: The server_group_id of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and server_group_id is None:
            raise ValueError("Invalid value for `server_group_id`, must not be `None`")  # noqa: E501

        self._server_group_id = server_group_id

    @property
    def server_group_name(self):
        """Gets the server_group_name of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The server_group_name of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._server_group_name

    @server_group_name.setter
    def server_group_name(self, server_group_name):
        """Sets the server_group_name of this ModifyNLBServerGroupAttributesRequest.


        :param server_group_name: The server_group_name of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: str
        """

        self._server_group_name = server_group_name

    @property
    def session_persistence_enabled(self):
        """Gets the session_persistence_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The session_persistence_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._session_persistence_enabled

    @session_persistence_enabled.setter
    def session_persistence_enabled(self, session_persistence_enabled):
        """Sets the session_persistence_enabled of this ModifyNLBServerGroupAttributesRequest.


        :param session_persistence_enabled: The session_persistence_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._session_persistence_enabled = session_persistence_enabled

    @property
    def session_persistence_timeout(self):
        """Gets the session_persistence_timeout of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The session_persistence_timeout of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: int
        """
        return self._session_persistence_timeout

    @session_persistence_timeout.setter
    def session_persistence_timeout(self, session_persistence_timeout):
        """Sets the session_persistence_timeout of this ModifyNLBServerGroupAttributesRequest.


        :param session_persistence_timeout: The session_persistence_timeout of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: int
        """

        self._session_persistence_timeout = session_persistence_timeout

    @property
    def timestamp_remove_enabled(self):
        """Gets the timestamp_remove_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501


        :return: The timestamp_remove_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._timestamp_remove_enabled

    @timestamp_remove_enabled.setter
    def timestamp_remove_enabled(self, timestamp_remove_enabled):
        """Sets the timestamp_remove_enabled of this ModifyNLBServerGroupAttributesRequest.


        :param timestamp_remove_enabled: The timestamp_remove_enabled of this ModifyNLBServerGroupAttributesRequest.  # noqa: E501
        :type: bool
        """

        self._timestamp_remove_enabled = timestamp_remove_enabled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyNLBServerGroupAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyNLBServerGroupAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyNLBServerGroupAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
