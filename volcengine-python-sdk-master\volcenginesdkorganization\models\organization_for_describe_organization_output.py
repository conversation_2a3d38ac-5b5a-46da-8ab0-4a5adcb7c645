# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OrganizationForDescribeOrganizationOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_time': 'str',
        'delete_uk': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'owner': 'str',
        'status': 'int',
        'type': 'int',
        'updated_time': 'str'
    }

    attribute_map = {
        'created_time': 'CreatedTime',
        'delete_uk': 'DeleteUk',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'id': 'ID',
        'name': 'Name',
        'owner': 'Owner',
        'status': 'Status',
        'type': 'Type',
        'updated_time': 'UpdatedTime'
    }

    def __init__(self, created_time=None, delete_uk=None, deleted_time=None, description=None, id=None, name=None, owner=None, status=None, type=None, updated_time=None, _configuration=None):  # noqa: E501
        """OrganizationForDescribeOrganizationOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_time = None
        self._delete_uk = None
        self._deleted_time = None
        self._description = None
        self._id = None
        self._name = None
        self._owner = None
        self._status = None
        self._type = None
        self._updated_time = None
        self.discriminator = None

        if created_time is not None:
            self.created_time = created_time
        if delete_uk is not None:
            self.delete_uk = delete_uk
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if owner is not None:
            self.owner = owner
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type
        if updated_time is not None:
            self.updated_time = updated_time

    @property
    def created_time(self):
        """Gets the created_time of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The created_time of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this OrganizationForDescribeOrganizationOutput.


        :param created_time: The created_time of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def delete_uk(self):
        """Gets the delete_uk of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The delete_uk of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: str
        """
        return self._delete_uk

    @delete_uk.setter
    def delete_uk(self, delete_uk):
        """Sets the delete_uk of this OrganizationForDescribeOrganizationOutput.


        :param delete_uk: The delete_uk of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: str
        """

        self._delete_uk = delete_uk

    @property
    def deleted_time(self):
        """Gets the deleted_time of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The deleted_time of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this OrganizationForDescribeOrganizationOutput.


        :param deleted_time: The deleted_time of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The description of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this OrganizationForDescribeOrganizationOutput.


        :param description: The description of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The id of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this OrganizationForDescribeOrganizationOutput.


        :param id: The id of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The name of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this OrganizationForDescribeOrganizationOutput.


        :param name: The name of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def owner(self):
        """Gets the owner of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The owner of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner

    @owner.setter
    def owner(self, owner):
        """Sets the owner of this OrganizationForDescribeOrganizationOutput.


        :param owner: The owner of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: str
        """

        self._owner = owner

    @property
    def status(self):
        """Gets the status of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The status of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this OrganizationForDescribeOrganizationOutput.


        :param status: The status of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The type of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: int
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this OrganizationForDescribeOrganizationOutput.


        :param type: The type of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: int
        """

        self._type = type

    @property
    def updated_time(self):
        """Gets the updated_time of this OrganizationForDescribeOrganizationOutput.  # noqa: E501


        :return: The updated_time of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_time

    @updated_time.setter
    def updated_time(self, updated_time):
        """Sets the updated_time of this OrganizationForDescribeOrganizationOutput.


        :param updated_time: The updated_time of this OrganizationForDescribeOrganizationOutput.  # noqa: E501
        :type: str
        """

        self._updated_time = updated_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OrganizationForDescribeOrganizationOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OrganizationForDescribeOrganizationOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OrganizationForDescribeOrganizationOutput):
            return True

        return self.to_dict() != other.to_dict()
