# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ScanTypeForDetectVulnInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'app': 'bool',
        'dev_linux': 'bool',
        'dev_py': 'bool',
        'linux': 'bool',
        'webcms': 'bool',
        'windows': 'bool'
    }

    attribute_map = {
        'app': 'App',
        'dev_linux': 'DevLinux',
        'dev_py': 'DevPy',
        'linux': 'Linux',
        'webcms': 'Webcms',
        'windows': 'Windows'
    }

    def __init__(self, app=None, dev_linux=None, dev_py=None, linux=None, webcms=None, windows=None, _configuration=None):  # noqa: E501
        """ScanTypeForDetectVulnInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._app = None
        self._dev_linux = None
        self._dev_py = None
        self._linux = None
        self._webcms = None
        self._windows = None
        self.discriminator = None

        if app is not None:
            self.app = app
        if dev_linux is not None:
            self.dev_linux = dev_linux
        if dev_py is not None:
            self.dev_py = dev_py
        if linux is not None:
            self.linux = linux
        if webcms is not None:
            self.webcms = webcms
        if windows is not None:
            self.windows = windows

    @property
    def app(self):
        """Gets the app of this ScanTypeForDetectVulnInput.  # noqa: E501


        :return: The app of this ScanTypeForDetectVulnInput.  # noqa: E501
        :rtype: bool
        """
        return self._app

    @app.setter
    def app(self, app):
        """Sets the app of this ScanTypeForDetectVulnInput.


        :param app: The app of this ScanTypeForDetectVulnInput.  # noqa: E501
        :type: bool
        """

        self._app = app

    @property
    def dev_linux(self):
        """Gets the dev_linux of this ScanTypeForDetectVulnInput.  # noqa: E501


        :return: The dev_linux of this ScanTypeForDetectVulnInput.  # noqa: E501
        :rtype: bool
        """
        return self._dev_linux

    @dev_linux.setter
    def dev_linux(self, dev_linux):
        """Sets the dev_linux of this ScanTypeForDetectVulnInput.


        :param dev_linux: The dev_linux of this ScanTypeForDetectVulnInput.  # noqa: E501
        :type: bool
        """

        self._dev_linux = dev_linux

    @property
    def dev_py(self):
        """Gets the dev_py of this ScanTypeForDetectVulnInput.  # noqa: E501


        :return: The dev_py of this ScanTypeForDetectVulnInput.  # noqa: E501
        :rtype: bool
        """
        return self._dev_py

    @dev_py.setter
    def dev_py(self, dev_py):
        """Sets the dev_py of this ScanTypeForDetectVulnInput.


        :param dev_py: The dev_py of this ScanTypeForDetectVulnInput.  # noqa: E501
        :type: bool
        """

        self._dev_py = dev_py

    @property
    def linux(self):
        """Gets the linux of this ScanTypeForDetectVulnInput.  # noqa: E501


        :return: The linux of this ScanTypeForDetectVulnInput.  # noqa: E501
        :rtype: bool
        """
        return self._linux

    @linux.setter
    def linux(self, linux):
        """Sets the linux of this ScanTypeForDetectVulnInput.


        :param linux: The linux of this ScanTypeForDetectVulnInput.  # noqa: E501
        :type: bool
        """

        self._linux = linux

    @property
    def webcms(self):
        """Gets the webcms of this ScanTypeForDetectVulnInput.  # noqa: E501


        :return: The webcms of this ScanTypeForDetectVulnInput.  # noqa: E501
        :rtype: bool
        """
        return self._webcms

    @webcms.setter
    def webcms(self, webcms):
        """Sets the webcms of this ScanTypeForDetectVulnInput.


        :param webcms: The webcms of this ScanTypeForDetectVulnInput.  # noqa: E501
        :type: bool
        """

        self._webcms = webcms

    @property
    def windows(self):
        """Gets the windows of this ScanTypeForDetectVulnInput.  # noqa: E501


        :return: The windows of this ScanTypeForDetectVulnInput.  # noqa: E501
        :rtype: bool
        """
        return self._windows

    @windows.setter
    def windows(self, windows):
        """Sets the windows of this ScanTypeForDetectVulnInput.


        :param windows: The windows of this ScanTypeForDetectVulnInput.  # noqa: E501
        :type: bool
        """

        self._windows = windows

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ScanTypeForDetectVulnInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ScanTypeForDetectVulnInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ScanTypeForDetectVulnInput):
            return True

        return self.to_dict() != other.to_dict()
