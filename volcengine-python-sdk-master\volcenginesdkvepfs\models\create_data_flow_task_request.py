# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDataFlowTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data_storage': 'str',
        'data_storage_path': 'str',
        'data_type': 'str',
        'entry_list_file_info': 'EntryListFileInfoForCreateDataFlowTaskInput',
        'export_symlink_policy': 'str',
        'file_system_id': 'str',
        'fileset_id': 'str',
        'same_name_file_policy': 'str',
        'sub_path': 'str',
        'task_action': 'str'
    }

    attribute_map = {
        'data_storage': 'DataStorage',
        'data_storage_path': 'DataStoragePath',
        'data_type': 'DataType',
        'entry_list_file_info': 'EntryListFileInfo',
        'export_symlink_policy': 'ExportSymlinkPolicy',
        'file_system_id': 'FileSystemId',
        'fileset_id': 'FilesetId',
        'same_name_file_policy': 'SameNameFilePolicy',
        'sub_path': 'SubPath',
        'task_action': 'TaskAction'
    }

    def __init__(self, data_storage=None, data_storage_path=None, data_type=None, entry_list_file_info=None, export_symlink_policy=None, file_system_id=None, fileset_id=None, same_name_file_policy=None, sub_path=None, task_action=None, _configuration=None):  # noqa: E501
        """CreateDataFlowTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data_storage = None
        self._data_storage_path = None
        self._data_type = None
        self._entry_list_file_info = None
        self._export_symlink_policy = None
        self._file_system_id = None
        self._fileset_id = None
        self._same_name_file_policy = None
        self._sub_path = None
        self._task_action = None
        self.discriminator = None

        if data_storage is not None:
            self.data_storage = data_storage
        if data_storage_path is not None:
            self.data_storage_path = data_storage_path
        self.data_type = data_type
        if entry_list_file_info is not None:
            self.entry_list_file_info = entry_list_file_info
        if export_symlink_policy is not None:
            self.export_symlink_policy = export_symlink_policy
        self.file_system_id = file_system_id
        if fileset_id is not None:
            self.fileset_id = fileset_id
        if same_name_file_policy is not None:
            self.same_name_file_policy = same_name_file_policy
        if sub_path is not None:
            self.sub_path = sub_path
        self.task_action = task_action

    @property
    def data_storage(self):
        """Gets the data_storage of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The data_storage of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_storage

    @data_storage.setter
    def data_storage(self, data_storage):
        """Sets the data_storage of this CreateDataFlowTaskRequest.


        :param data_storage: The data_storage of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._data_storage = data_storage

    @property
    def data_storage_path(self):
        """Gets the data_storage_path of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The data_storage_path of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_storage_path

    @data_storage_path.setter
    def data_storage_path(self, data_storage_path):
        """Sets the data_storage_path of this CreateDataFlowTaskRequest.


        :param data_storage_path: The data_storage_path of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._data_storage_path = data_storage_path

    @property
    def data_type(self):
        """Gets the data_type of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The data_type of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this CreateDataFlowTaskRequest.


        :param data_type: The data_type of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and data_type is None:
            raise ValueError("Invalid value for `data_type`, must not be `None`")  # noqa: E501
        allowed_values = ["Metadata", "MetaAndData"]  # noqa: E501
        if (self._configuration.client_side_validation and
                data_type not in allowed_values):
            raise ValueError(
                "Invalid value for `data_type` ({0}), must be one of {1}"  # noqa: E501
                .format(data_type, allowed_values)
            )

        self._data_type = data_type

    @property
    def entry_list_file_info(self):
        """Gets the entry_list_file_info of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The entry_list_file_info of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: EntryListFileInfoForCreateDataFlowTaskInput
        """
        return self._entry_list_file_info

    @entry_list_file_info.setter
    def entry_list_file_info(self, entry_list_file_info):
        """Sets the entry_list_file_info of this CreateDataFlowTaskRequest.


        :param entry_list_file_info: The entry_list_file_info of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: EntryListFileInfoForCreateDataFlowTaskInput
        """

        self._entry_list_file_info = entry_list_file_info

    @property
    def export_symlink_policy(self):
        """Gets the export_symlink_policy of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The export_symlink_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._export_symlink_policy

    @export_symlink_policy.setter
    def export_symlink_policy(self, export_symlink_policy):
        """Sets the export_symlink_policy of this CreateDataFlowTaskRequest.


        :param export_symlink_policy: The export_symlink_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Skip", "NormalFile"]  # noqa: E501
        if (self._configuration.client_side_validation and
                export_symlink_policy not in allowed_values):
            raise ValueError(
                "Invalid value for `export_symlink_policy` ({0}), must be one of {1}"  # noqa: E501
                .format(export_symlink_policy, allowed_values)
            )

        self._export_symlink_policy = export_symlink_policy

    @property
    def file_system_id(self):
        """Gets the file_system_id of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The file_system_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this CreateDataFlowTaskRequest.


        :param file_system_id: The file_system_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and file_system_id is None:
            raise ValueError("Invalid value for `file_system_id`, must not be `None`")  # noqa: E501

        self._file_system_id = file_system_id

    @property
    def fileset_id(self):
        """Gets the fileset_id of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The fileset_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._fileset_id

    @fileset_id.setter
    def fileset_id(self, fileset_id):
        """Sets the fileset_id of this CreateDataFlowTaskRequest.


        :param fileset_id: The fileset_id of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._fileset_id = fileset_id

    @property
    def same_name_file_policy(self):
        """Gets the same_name_file_policy of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The same_name_file_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._same_name_file_policy

    @same_name_file_policy.setter
    def same_name_file_policy(self, same_name_file_policy):
        """Sets the same_name_file_policy of this CreateDataFlowTaskRequest.


        :param same_name_file_policy: The same_name_file_policy of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Skip", "KeepLatest", "OverWrite"]  # noqa: E501
        if (self._configuration.client_side_validation and
                same_name_file_policy not in allowed_values):
            raise ValueError(
                "Invalid value for `same_name_file_policy` ({0}), must be one of {1}"  # noqa: E501
                .format(same_name_file_policy, allowed_values)
            )

        self._same_name_file_policy = same_name_file_policy

    @property
    def sub_path(self):
        """Gets the sub_path of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The sub_path of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._sub_path

    @sub_path.setter
    def sub_path(self, sub_path):
        """Sets the sub_path of this CreateDataFlowTaskRequest.


        :param sub_path: The sub_path of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """

        self._sub_path = sub_path

    @property
    def task_action(self):
        """Gets the task_action of this CreateDataFlowTaskRequest.  # noqa: E501


        :return: The task_action of this CreateDataFlowTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_action

    @task_action.setter
    def task_action(self, task_action):
        """Sets the task_action of this CreateDataFlowTaskRequest.


        :param task_action: The task_action of this CreateDataFlowTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and task_action is None:
            raise ValueError("Invalid value for `task_action`, must not be `None`")  # noqa: E501
        allowed_values = ["Import", "Export", "Inventory"]  # noqa: E501
        if (self._configuration.client_side_validation and
                task_action not in allowed_values):
            raise ValueError(
                "Invalid value for `task_action` ({0}), must be one of {1}"  # noqa: E501
                .format(task_action, allowed_values)
            )

        self._task_action = task_action

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDataFlowTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDataFlowTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDataFlowTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
