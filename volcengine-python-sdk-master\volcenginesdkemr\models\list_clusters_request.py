# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListClustersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_types': 'list[str]',
        'cluster_id': 'str',
        'cluster_ids': 'list[str]',
        'cluster_name': 'str',
        'cluster_states': 'list[str]',
        'cluster_types': 'list[str]',
        'create_time_after': 'str',
        'create_time_before': 'str',
        'max_results': 'int',
        'next_token': 'str',
        'project_name': 'str',
        'release_version': 'str',
        'tags': 'list[TagForListClustersInput]'
    }

    attribute_map = {
        'charge_types': 'ChargeTypes',
        'cluster_id': 'ClusterId',
        'cluster_ids': 'ClusterIds',
        'cluster_name': 'ClusterName',
        'cluster_states': 'ClusterStates',
        'cluster_types': 'ClusterTypes',
        'create_time_after': 'CreateTimeAfter',
        'create_time_before': 'CreateTimeBefore',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'project_name': 'ProjectName',
        'release_version': 'ReleaseVersion',
        'tags': 'Tags'
    }

    def __init__(self, charge_types=None, cluster_id=None, cluster_ids=None, cluster_name=None, cluster_states=None, cluster_types=None, create_time_after=None, create_time_before=None, max_results=None, next_token=None, project_name=None, release_version=None, tags=None, _configuration=None):  # noqa: E501
        """ListClustersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_types = None
        self._cluster_id = None
        self._cluster_ids = None
        self._cluster_name = None
        self._cluster_states = None
        self._cluster_types = None
        self._create_time_after = None
        self._create_time_before = None
        self._max_results = None
        self._next_token = None
        self._project_name = None
        self._release_version = None
        self._tags = None
        self.discriminator = None

        if charge_types is not None:
            self.charge_types = charge_types
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_ids is not None:
            self.cluster_ids = cluster_ids
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if cluster_states is not None:
            self.cluster_states = cluster_states
        if cluster_types is not None:
            self.cluster_types = cluster_types
        if create_time_after is not None:
            self.create_time_after = create_time_after
        if create_time_before is not None:
            self.create_time_before = create_time_before
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if project_name is not None:
            self.project_name = project_name
        if release_version is not None:
            self.release_version = release_version
        if tags is not None:
            self.tags = tags

    @property
    def charge_types(self):
        """Gets the charge_types of this ListClustersRequest.  # noqa: E501


        :return: The charge_types of this ListClustersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._charge_types

    @charge_types.setter
    def charge_types(self, charge_types):
        """Sets the charge_types of this ListClustersRequest.


        :param charge_types: The charge_types of this ListClustersRequest.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["PRE", "POST"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(charge_types).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `charge_types` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(charge_types) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._charge_types = charge_types

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListClustersRequest.  # noqa: E501


        :return: The cluster_id of this ListClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListClustersRequest.


        :param cluster_id: The cluster_id of this ListClustersRequest.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_ids(self):
        """Gets the cluster_ids of this ListClustersRequest.  # noqa: E501


        :return: The cluster_ids of this ListClustersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_ids

    @cluster_ids.setter
    def cluster_ids(self, cluster_ids):
        """Sets the cluster_ids of this ListClustersRequest.


        :param cluster_ids: The cluster_ids of this ListClustersRequest.  # noqa: E501
        :type: list[str]
        """

        self._cluster_ids = cluster_ids

    @property
    def cluster_name(self):
        """Gets the cluster_name of this ListClustersRequest.  # noqa: E501


        :return: The cluster_name of this ListClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this ListClustersRequest.


        :param cluster_name: The cluster_name of this ListClustersRequest.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def cluster_states(self):
        """Gets the cluster_states of this ListClustersRequest.  # noqa: E501


        :return: The cluster_states of this ListClustersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_states

    @cluster_states.setter
    def cluster_states(self, cluster_states):
        """Sets the cluster_states of this ListClustersRequest.


        :param cluster_states: The cluster_states of this ListClustersRequest.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["PENDING_FOR_PAYMENT", "CREATING", "RUNNING", "TERMINATING", "TERMINATED", "TERMINATED_WITH_ERROR", "FAILED", "SHUTDOWN"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(cluster_states).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `cluster_states` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(cluster_states) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._cluster_states = cluster_states

    @property
    def cluster_types(self):
        """Gets the cluster_types of this ListClustersRequest.  # noqa: E501


        :return: The cluster_types of this ListClustersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_types

    @cluster_types.setter
    def cluster_types(self, cluster_types):
        """Sets the cluster_types of this ListClustersRequest.


        :param cluster_types: The cluster_types of this ListClustersRequest.  # noqa: E501
        :type: list[str]
        """

        self._cluster_types = cluster_types

    @property
    def create_time_after(self):
        """Gets the create_time_after of this ListClustersRequest.  # noqa: E501


        :return: The create_time_after of this ListClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_after

    @create_time_after.setter
    def create_time_after(self, create_time_after):
        """Sets the create_time_after of this ListClustersRequest.


        :param create_time_after: The create_time_after of this ListClustersRequest.  # noqa: E501
        :type: str
        """

        self._create_time_after = create_time_after

    @property
    def create_time_before(self):
        """Gets the create_time_before of this ListClustersRequest.  # noqa: E501


        :return: The create_time_before of this ListClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_before

    @create_time_before.setter
    def create_time_before(self, create_time_before):
        """Sets the create_time_before of this ListClustersRequest.


        :param create_time_before: The create_time_before of this ListClustersRequest.  # noqa: E501
        :type: str
        """

        self._create_time_before = create_time_before

    @property
    def max_results(self):
        """Gets the max_results of this ListClustersRequest.  # noqa: E501


        :return: The max_results of this ListClustersRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListClustersRequest.


        :param max_results: The max_results of this ListClustersRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListClustersRequest.  # noqa: E501


        :return: The next_token of this ListClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListClustersRequest.


        :param next_token: The next_token of this ListClustersRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def project_name(self):
        """Gets the project_name of this ListClustersRequest.  # noqa: E501


        :return: The project_name of this ListClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListClustersRequest.


        :param project_name: The project_name of this ListClustersRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def release_version(self):
        """Gets the release_version of this ListClustersRequest.  # noqa: E501


        :return: The release_version of this ListClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._release_version

    @release_version.setter
    def release_version(self, release_version):
        """Sets the release_version of this ListClustersRequest.


        :param release_version: The release_version of this ListClustersRequest.  # noqa: E501
        :type: str
        """

        self._release_version = release_version

    @property
    def tags(self):
        """Gets the tags of this ListClustersRequest.  # noqa: E501


        :return: The tags of this ListClustersRequest.  # noqa: E501
        :rtype: list[TagForListClustersInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ListClustersRequest.


        :param tags: The tags of this ListClustersRequest.  # noqa: E501
        :type: list[TagForListClustersInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListClustersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListClustersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListClustersRequest):
            return True

        return self.to_dict() != other.to_dict()
