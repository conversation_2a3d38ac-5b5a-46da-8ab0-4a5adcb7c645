# coding: utf-8

"""
    aiotvideo20231001

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FontForDownloadLocalMediaInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alignment': 'str',
        'font_size': 'int',
        'primary_color': 'str'
    }

    attribute_map = {
        'alignment': 'Alignment',
        'font_size': 'FontSize',
        'primary_color': 'PrimaryColor'
    }

    def __init__(self, alignment=None, font_size=None, primary_color=None, _configuration=None):  # noqa: E501
        """FontForDownloadLocalMediaInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alignment = None
        self._font_size = None
        self._primary_color = None
        self.discriminator = None

        if alignment is not None:
            self.alignment = alignment
        if font_size is not None:
            self.font_size = font_size
        if primary_color is not None:
            self.primary_color = primary_color

    @property
    def alignment(self):
        """Gets the alignment of this FontForDownloadLocalMediaInput.  # noqa: E501


        :return: The alignment of this FontForDownloadLocalMediaInput.  # noqa: E501
        :rtype: str
        """
        return self._alignment

    @alignment.setter
    def alignment(self, alignment):
        """Sets the alignment of this FontForDownloadLocalMediaInput.


        :param alignment: The alignment of this FontForDownloadLocalMediaInput.  # noqa: E501
        :type: str
        """

        self._alignment = alignment

    @property
    def font_size(self):
        """Gets the font_size of this FontForDownloadLocalMediaInput.  # noqa: E501


        :return: The font_size of this FontForDownloadLocalMediaInput.  # noqa: E501
        :rtype: int
        """
        return self._font_size

    @font_size.setter
    def font_size(self, font_size):
        """Sets the font_size of this FontForDownloadLocalMediaInput.


        :param font_size: The font_size of this FontForDownloadLocalMediaInput.  # noqa: E501
        :type: int
        """

        self._font_size = font_size

    @property
    def primary_color(self):
        """Gets the primary_color of this FontForDownloadLocalMediaInput.  # noqa: E501


        :return: The primary_color of this FontForDownloadLocalMediaInput.  # noqa: E501
        :rtype: str
        """
        return self._primary_color

    @primary_color.setter
    def primary_color(self, primary_color):
        """Sets the primary_color of this FontForDownloadLocalMediaInput.


        :param primary_color: The primary_color of this FontForDownloadLocalMediaInput.  # noqa: E501
        :type: str
        """

        self._primary_color = primary_color

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FontForDownloadLocalMediaInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FontForDownloadLocalMediaInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FontForDownloadLocalMediaInput):
            return True

        return self.to_dict() != other.to_dict()
