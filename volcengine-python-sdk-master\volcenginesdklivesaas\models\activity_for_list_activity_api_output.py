# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ActivityForListActivityAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cover_image': 'str',
        'create_time': 'int',
        'host_account_id': 'int',
        'host_account_name': 'str',
        'id': 'int',
        'is_lock_preview': 'int',
        'live_layout': 'int',
        'live_mode': 'int',
        'live_review_status': 'int',
        'live_time': 'int',
        'name': 'str',
        'online_status': 'int',
        'site_tags': 'list[SiteTagForListActivityAPIOutput]',
        'status': 'int',
        'stream_start_time': 'int',
        'sub_account_name': 'str',
        'text_site_tags': 'list[TextSiteTagForListActivityAPIOutput]',
        'vertical_cover_image': 'str',
        'view_url': 'str'
    }

    attribute_map = {
        'cover_image': 'CoverImage',
        'create_time': 'CreateTime',
        'host_account_id': 'HostAccountId',
        'host_account_name': 'HostAccountName',
        'id': 'Id',
        'is_lock_preview': 'IsLockPreview',
        'live_layout': 'LiveLayout',
        'live_mode': 'LiveMode',
        'live_review_status': 'LiveReviewStatus',
        'live_time': 'LiveTime',
        'name': 'Name',
        'online_status': 'OnlineStatus',
        'site_tags': 'SiteTags',
        'status': 'Status',
        'stream_start_time': 'StreamStartTime',
        'sub_account_name': 'SubAccountName',
        'text_site_tags': 'TextSiteTags',
        'vertical_cover_image': 'VerticalCoverImage',
        'view_url': 'ViewUrl'
    }

    def __init__(self, cover_image=None, create_time=None, host_account_id=None, host_account_name=None, id=None, is_lock_preview=None, live_layout=None, live_mode=None, live_review_status=None, live_time=None, name=None, online_status=None, site_tags=None, status=None, stream_start_time=None, sub_account_name=None, text_site_tags=None, vertical_cover_image=None, view_url=None, _configuration=None):  # noqa: E501
        """ActivityForListActivityAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cover_image = None
        self._create_time = None
        self._host_account_id = None
        self._host_account_name = None
        self._id = None
        self._is_lock_preview = None
        self._live_layout = None
        self._live_mode = None
        self._live_review_status = None
        self._live_time = None
        self._name = None
        self._online_status = None
        self._site_tags = None
        self._status = None
        self._stream_start_time = None
        self._sub_account_name = None
        self._text_site_tags = None
        self._vertical_cover_image = None
        self._view_url = None
        self.discriminator = None

        if cover_image is not None:
            self.cover_image = cover_image
        if create_time is not None:
            self.create_time = create_time
        if host_account_id is not None:
            self.host_account_id = host_account_id
        if host_account_name is not None:
            self.host_account_name = host_account_name
        if id is not None:
            self.id = id
        if is_lock_preview is not None:
            self.is_lock_preview = is_lock_preview
        if live_layout is not None:
            self.live_layout = live_layout
        if live_mode is not None:
            self.live_mode = live_mode
        if live_review_status is not None:
            self.live_review_status = live_review_status
        if live_time is not None:
            self.live_time = live_time
        if name is not None:
            self.name = name
        if online_status is not None:
            self.online_status = online_status
        if site_tags is not None:
            self.site_tags = site_tags
        if status is not None:
            self.status = status
        if stream_start_time is not None:
            self.stream_start_time = stream_start_time
        if sub_account_name is not None:
            self.sub_account_name = sub_account_name
        if text_site_tags is not None:
            self.text_site_tags = text_site_tags
        if vertical_cover_image is not None:
            self.vertical_cover_image = vertical_cover_image
        if view_url is not None:
            self.view_url = view_url

    @property
    def cover_image(self):
        """Gets the cover_image of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The cover_image of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._cover_image

    @cover_image.setter
    def cover_image(self, cover_image):
        """Sets the cover_image of this ActivityForListActivityAPIOutput.


        :param cover_image: The cover_image of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: str
        """

        self._cover_image = cover_image

    @property
    def create_time(self):
        """Gets the create_time of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The create_time of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ActivityForListActivityAPIOutput.


        :param create_time: The create_time of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def host_account_id(self):
        """Gets the host_account_id of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The host_account_id of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._host_account_id

    @host_account_id.setter
    def host_account_id(self, host_account_id):
        """Sets the host_account_id of this ActivityForListActivityAPIOutput.


        :param host_account_id: The host_account_id of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._host_account_id = host_account_id

    @property
    def host_account_name(self):
        """Gets the host_account_name of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The host_account_name of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._host_account_name

    @host_account_name.setter
    def host_account_name(self, host_account_name):
        """Sets the host_account_name of this ActivityForListActivityAPIOutput.


        :param host_account_name: The host_account_name of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: str
        """

        self._host_account_name = host_account_name

    @property
    def id(self):
        """Gets the id of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The id of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ActivityForListActivityAPIOutput.


        :param id: The id of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def is_lock_preview(self):
        """Gets the is_lock_preview of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The is_lock_preview of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_lock_preview

    @is_lock_preview.setter
    def is_lock_preview(self, is_lock_preview):
        """Sets the is_lock_preview of this ActivityForListActivityAPIOutput.


        :param is_lock_preview: The is_lock_preview of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_lock_preview = is_lock_preview

    @property
    def live_layout(self):
        """Gets the live_layout of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The live_layout of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_layout

    @live_layout.setter
    def live_layout(self, live_layout):
        """Sets the live_layout of this ActivityForListActivityAPIOutput.


        :param live_layout: The live_layout of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._live_layout = live_layout

    @property
    def live_mode(self):
        """Gets the live_mode of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The live_mode of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_mode

    @live_mode.setter
    def live_mode(self, live_mode):
        """Sets the live_mode of this ActivityForListActivityAPIOutput.


        :param live_mode: The live_mode of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._live_mode = live_mode

    @property
    def live_review_status(self):
        """Gets the live_review_status of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The live_review_status of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_review_status

    @live_review_status.setter
    def live_review_status(self, live_review_status):
        """Sets the live_review_status of this ActivityForListActivityAPIOutput.


        :param live_review_status: The live_review_status of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._live_review_status = live_review_status

    @property
    def live_time(self):
        """Gets the live_time of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The live_time of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._live_time

    @live_time.setter
    def live_time(self, live_time):
        """Sets the live_time of this ActivityForListActivityAPIOutput.


        :param live_time: The live_time of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._live_time = live_time

    @property
    def name(self):
        """Gets the name of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The name of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ActivityForListActivityAPIOutput.


        :param name: The name of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def online_status(self):
        """Gets the online_status of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The online_status of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._online_status

    @online_status.setter
    def online_status(self, online_status):
        """Sets the online_status of this ActivityForListActivityAPIOutput.


        :param online_status: The online_status of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._online_status = online_status

    @property
    def site_tags(self):
        """Gets the site_tags of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The site_tags of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: list[SiteTagForListActivityAPIOutput]
        """
        return self._site_tags

    @site_tags.setter
    def site_tags(self, site_tags):
        """Sets the site_tags of this ActivityForListActivityAPIOutput.


        :param site_tags: The site_tags of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: list[SiteTagForListActivityAPIOutput]
        """

        self._site_tags = site_tags

    @property
    def status(self):
        """Gets the status of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The status of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ActivityForListActivityAPIOutput.


        :param status: The status of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def stream_start_time(self):
        """Gets the stream_start_time of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The stream_start_time of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._stream_start_time

    @stream_start_time.setter
    def stream_start_time(self, stream_start_time):
        """Sets the stream_start_time of this ActivityForListActivityAPIOutput.


        :param stream_start_time: The stream_start_time of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: int
        """

        self._stream_start_time = stream_start_time

    @property
    def sub_account_name(self):
        """Gets the sub_account_name of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The sub_account_name of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_account_name

    @sub_account_name.setter
    def sub_account_name(self, sub_account_name):
        """Sets the sub_account_name of this ActivityForListActivityAPIOutput.


        :param sub_account_name: The sub_account_name of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: str
        """

        self._sub_account_name = sub_account_name

    @property
    def text_site_tags(self):
        """Gets the text_site_tags of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The text_site_tags of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: list[TextSiteTagForListActivityAPIOutput]
        """
        return self._text_site_tags

    @text_site_tags.setter
    def text_site_tags(self, text_site_tags):
        """Sets the text_site_tags of this ActivityForListActivityAPIOutput.


        :param text_site_tags: The text_site_tags of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: list[TextSiteTagForListActivityAPIOutput]
        """

        self._text_site_tags = text_site_tags

    @property
    def vertical_cover_image(self):
        """Gets the vertical_cover_image of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The vertical_cover_image of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._vertical_cover_image

    @vertical_cover_image.setter
    def vertical_cover_image(self, vertical_cover_image):
        """Sets the vertical_cover_image of this ActivityForListActivityAPIOutput.


        :param vertical_cover_image: The vertical_cover_image of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: str
        """

        self._vertical_cover_image = vertical_cover_image

    @property
    def view_url(self):
        """Gets the view_url of this ActivityForListActivityAPIOutput.  # noqa: E501


        :return: The view_url of this ActivityForListActivityAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._view_url

    @view_url.setter
    def view_url(self, view_url):
        """Sets the view_url of this ActivityForListActivityAPIOutput.


        :param view_url: The view_url of this ActivityForListActivityAPIOutput.  # noqa: E501
        :type: str
        """

        self._view_url = view_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ActivityForListActivityAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ActivityForListActivityAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ActivityForListActivityAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
