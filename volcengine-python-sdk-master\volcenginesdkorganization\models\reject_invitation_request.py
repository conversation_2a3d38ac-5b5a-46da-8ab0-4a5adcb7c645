# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RejectInvitationRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'invite_id': 'str',
        'link_id': 'str',
        'reason': 'str'
    }

    attribute_map = {
        'invite_id': 'InviteId',
        'link_id': 'LinkId',
        'reason': 'Reason'
    }

    def __init__(self, invite_id=None, link_id=None, reason=None, _configuration=None):  # noqa: E501
        """RejectInvitationRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._invite_id = None
        self._link_id = None
        self._reason = None
        self.discriminator = None

        self.invite_id = invite_id
        self.link_id = link_id
        if reason is not None:
            self.reason = reason

    @property
    def invite_id(self):
        """Gets the invite_id of this RejectInvitationRequest.  # noqa: E501


        :return: The invite_id of this RejectInvitationRequest.  # noqa: E501
        :rtype: str
        """
        return self._invite_id

    @invite_id.setter
    def invite_id(self, invite_id):
        """Sets the invite_id of this RejectInvitationRequest.


        :param invite_id: The invite_id of this RejectInvitationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and invite_id is None:
            raise ValueError("Invalid value for `invite_id`, must not be `None`")  # noqa: E501

        self._invite_id = invite_id

    @property
    def link_id(self):
        """Gets the link_id of this RejectInvitationRequest.  # noqa: E501


        :return: The link_id of this RejectInvitationRequest.  # noqa: E501
        :rtype: str
        """
        return self._link_id

    @link_id.setter
    def link_id(self, link_id):
        """Sets the link_id of this RejectInvitationRequest.


        :param link_id: The link_id of this RejectInvitationRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and link_id is None:
            raise ValueError("Invalid value for `link_id`, must not be `None`")  # noqa: E501

        self._link_id = link_id

    @property
    def reason(self):
        """Gets the reason of this RejectInvitationRequest.  # noqa: E501


        :return: The reason of this RejectInvitationRequest.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this RejectInvitationRequest.


        :param reason: The reason of this RejectInvitationRequest.  # noqa: E501
        :type: str
        """

        self._reason = reason

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RejectInvitationRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RejectInvitationRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RejectInvitationRequest):
            return True

        return self.to_dict() != other.to_dict()
