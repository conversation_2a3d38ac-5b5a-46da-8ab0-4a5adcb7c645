# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDBEndpointReqForModifyDBEndpointConnectionModeInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_add_new_nodes': 'bool',
        'connection_pool_type': 'str',
        'description': 'str',
        'endpoint_name': 'str',
        'endpoint_type': 'str',
        'idle_connection_reclaim': 'bool',
        'implicit_trans_split': 'bool',
        'instance_id': 'str',
        'master_node_routing': 'bool',
        'master_protector_timeout': 'int',
        'multi_statements_mode': 'str',
        'nodes': 'str',
        'overload_protection': 'bool',
        'read_only_node_distribution_type': 'str',
        'read_only_node_max_delay_time': 'int',
        'read_only_node_weight': 'list[ReadOnlyNodeWeightForModifyDBEndpointConnectionModeInput]',
        'read_write_mode': 'str',
        'read_write_spliting': 'bool'
    }

    attribute_map = {
        'auto_add_new_nodes': 'AutoAddNewNodes',
        'connection_pool_type': 'ConnectionPoolType',
        'description': 'Description',
        'endpoint_name': 'EndpointName',
        'endpoint_type': 'EndpointType',
        'idle_connection_reclaim': 'IdleConnectionReclaim',
        'implicit_trans_split': 'ImplicitTransSplit',
        'instance_id': 'InstanceId',
        'master_node_routing': 'MasterNodeRouting',
        'master_protector_timeout': 'MasterProtectorTimeout',
        'multi_statements_mode': 'MultiStatementsMode',
        'nodes': 'Nodes',
        'overload_protection': 'OverloadProtection',
        'read_only_node_distribution_type': 'ReadOnlyNodeDistributionType',
        'read_only_node_max_delay_time': 'ReadOnlyNodeMaxDelayTime',
        'read_only_node_weight': 'ReadOnlyNodeWeight',
        'read_write_mode': 'ReadWriteMode',
        'read_write_spliting': 'ReadWriteSpliting'
    }

    def __init__(self, auto_add_new_nodes=None, connection_pool_type=None, description=None, endpoint_name=None, endpoint_type=None, idle_connection_reclaim=None, implicit_trans_split=None, instance_id=None, master_node_routing=None, master_protector_timeout=None, multi_statements_mode=None, nodes=None, overload_protection=None, read_only_node_distribution_type=None, read_only_node_max_delay_time=None, read_only_node_weight=None, read_write_mode=None, read_write_spliting=None, _configuration=None):  # noqa: E501
        """CreateDBEndpointReqForModifyDBEndpointConnectionModeInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_add_new_nodes = None
        self._connection_pool_type = None
        self._description = None
        self._endpoint_name = None
        self._endpoint_type = None
        self._idle_connection_reclaim = None
        self._implicit_trans_split = None
        self._instance_id = None
        self._master_node_routing = None
        self._master_protector_timeout = None
        self._multi_statements_mode = None
        self._nodes = None
        self._overload_protection = None
        self._read_only_node_distribution_type = None
        self._read_only_node_max_delay_time = None
        self._read_only_node_weight = None
        self._read_write_mode = None
        self._read_write_spliting = None
        self.discriminator = None

        if auto_add_new_nodes is not None:
            self.auto_add_new_nodes = auto_add_new_nodes
        if connection_pool_type is not None:
            self.connection_pool_type = connection_pool_type
        if description is not None:
            self.description = description
        if endpoint_name is not None:
            self.endpoint_name = endpoint_name
        if endpoint_type is not None:
            self.endpoint_type = endpoint_type
        if idle_connection_reclaim is not None:
            self.idle_connection_reclaim = idle_connection_reclaim
        if implicit_trans_split is not None:
            self.implicit_trans_split = implicit_trans_split
        if instance_id is not None:
            self.instance_id = instance_id
        if master_node_routing is not None:
            self.master_node_routing = master_node_routing
        if master_protector_timeout is not None:
            self.master_protector_timeout = master_protector_timeout
        if multi_statements_mode is not None:
            self.multi_statements_mode = multi_statements_mode
        if nodes is not None:
            self.nodes = nodes
        if overload_protection is not None:
            self.overload_protection = overload_protection
        if read_only_node_distribution_type is not None:
            self.read_only_node_distribution_type = read_only_node_distribution_type
        if read_only_node_max_delay_time is not None:
            self.read_only_node_max_delay_time = read_only_node_max_delay_time
        if read_only_node_weight is not None:
            self.read_only_node_weight = read_only_node_weight
        if read_write_mode is not None:
            self.read_write_mode = read_write_mode
        if read_write_spliting is not None:
            self.read_write_spliting = read_write_spliting

    @property
    def auto_add_new_nodes(self):
        """Gets the auto_add_new_nodes of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The auto_add_new_nodes of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_add_new_nodes

    @auto_add_new_nodes.setter
    def auto_add_new_nodes(self, auto_add_new_nodes):
        """Sets the auto_add_new_nodes of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param auto_add_new_nodes: The auto_add_new_nodes of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: bool
        """

        self._auto_add_new_nodes = auto_add_new_nodes

    @property
    def connection_pool_type(self):
        """Gets the connection_pool_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The connection_pool_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: str
        """
        return self._connection_pool_type

    @connection_pool_type.setter
    def connection_pool_type(self, connection_pool_type):
        """Sets the connection_pool_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param connection_pool_type: The connection_pool_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: str
        """

        self._connection_pool_type = connection_pool_type

    @property
    def description(self):
        """Gets the description of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The description of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param description: The description of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def endpoint_name(self):
        """Gets the endpoint_name of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The endpoint_name of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_name

    @endpoint_name.setter
    def endpoint_name(self, endpoint_name):
        """Sets the endpoint_name of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param endpoint_name: The endpoint_name of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: str
        """

        self._endpoint_name = endpoint_name

    @property
    def endpoint_type(self):
        """Gets the endpoint_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The endpoint_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_type

    @endpoint_type.setter
    def endpoint_type(self, endpoint_type):
        """Sets the endpoint_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param endpoint_type: The endpoint_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: str
        """

        self._endpoint_type = endpoint_type

    @property
    def idle_connection_reclaim(self):
        """Gets the idle_connection_reclaim of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The idle_connection_reclaim of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: bool
        """
        return self._idle_connection_reclaim

    @idle_connection_reclaim.setter
    def idle_connection_reclaim(self, idle_connection_reclaim):
        """Sets the idle_connection_reclaim of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param idle_connection_reclaim: The idle_connection_reclaim of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: bool
        """

        self._idle_connection_reclaim = idle_connection_reclaim

    @property
    def implicit_trans_split(self):
        """Gets the implicit_trans_split of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The implicit_trans_split of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: bool
        """
        return self._implicit_trans_split

    @implicit_trans_split.setter
    def implicit_trans_split(self, implicit_trans_split):
        """Sets the implicit_trans_split of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param implicit_trans_split: The implicit_trans_split of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: bool
        """

        self._implicit_trans_split = implicit_trans_split

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The instance_id of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param instance_id: The instance_id of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def master_node_routing(self):
        """Gets the master_node_routing of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The master_node_routing of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: bool
        """
        return self._master_node_routing

    @master_node_routing.setter
    def master_node_routing(self, master_node_routing):
        """Sets the master_node_routing of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param master_node_routing: The master_node_routing of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: bool
        """

        self._master_node_routing = master_node_routing

    @property
    def master_protector_timeout(self):
        """Gets the master_protector_timeout of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The master_protector_timeout of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: int
        """
        return self._master_protector_timeout

    @master_protector_timeout.setter
    def master_protector_timeout(self, master_protector_timeout):
        """Sets the master_protector_timeout of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param master_protector_timeout: The master_protector_timeout of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: int
        """

        self._master_protector_timeout = master_protector_timeout

    @property
    def multi_statements_mode(self):
        """Gets the multi_statements_mode of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The multi_statements_mode of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: str
        """
        return self._multi_statements_mode

    @multi_statements_mode.setter
    def multi_statements_mode(self, multi_statements_mode):
        """Sets the multi_statements_mode of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param multi_statements_mode: The multi_statements_mode of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: str
        """

        self._multi_statements_mode = multi_statements_mode

    @property
    def nodes(self):
        """Gets the nodes of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The nodes of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: str
        """
        return self._nodes

    @nodes.setter
    def nodes(self, nodes):
        """Sets the nodes of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param nodes: The nodes of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: str
        """

        self._nodes = nodes

    @property
    def overload_protection(self):
        """Gets the overload_protection of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The overload_protection of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: bool
        """
        return self._overload_protection

    @overload_protection.setter
    def overload_protection(self, overload_protection):
        """Sets the overload_protection of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param overload_protection: The overload_protection of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: bool
        """

        self._overload_protection = overload_protection

    @property
    def read_only_node_distribution_type(self):
        """Gets the read_only_node_distribution_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The read_only_node_distribution_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: str
        """
        return self._read_only_node_distribution_type

    @read_only_node_distribution_type.setter
    def read_only_node_distribution_type(self, read_only_node_distribution_type):
        """Sets the read_only_node_distribution_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param read_only_node_distribution_type: The read_only_node_distribution_type of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: str
        """

        self._read_only_node_distribution_type = read_only_node_distribution_type

    @property
    def read_only_node_max_delay_time(self):
        """Gets the read_only_node_max_delay_time of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The read_only_node_max_delay_time of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: int
        """
        return self._read_only_node_max_delay_time

    @read_only_node_max_delay_time.setter
    def read_only_node_max_delay_time(self, read_only_node_max_delay_time):
        """Sets the read_only_node_max_delay_time of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param read_only_node_max_delay_time: The read_only_node_max_delay_time of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: int
        """

        self._read_only_node_max_delay_time = read_only_node_max_delay_time

    @property
    def read_only_node_weight(self):
        """Gets the read_only_node_weight of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The read_only_node_weight of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: list[ReadOnlyNodeWeightForModifyDBEndpointConnectionModeInput]
        """
        return self._read_only_node_weight

    @read_only_node_weight.setter
    def read_only_node_weight(self, read_only_node_weight):
        """Sets the read_only_node_weight of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param read_only_node_weight: The read_only_node_weight of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: list[ReadOnlyNodeWeightForModifyDBEndpointConnectionModeInput]
        """

        self._read_only_node_weight = read_only_node_weight

    @property
    def read_write_mode(self):
        """Gets the read_write_mode of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The read_write_mode of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: str
        """
        return self._read_write_mode

    @read_write_mode.setter
    def read_write_mode(self, read_write_mode):
        """Sets the read_write_mode of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param read_write_mode: The read_write_mode of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: str
        """

        self._read_write_mode = read_write_mode

    @property
    def read_write_spliting(self):
        """Gets the read_write_spliting of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501


        :return: The read_write_spliting of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :rtype: bool
        """
        return self._read_write_spliting

    @read_write_spliting.setter
    def read_write_spliting(self, read_write_spliting):
        """Sets the read_write_spliting of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.


        :param read_write_spliting: The read_write_spliting of this CreateDBEndpointReqForModifyDBEndpointConnectionModeInput.  # noqa: E501
        :type: bool
        """

        self._read_write_spliting = read_write_spliting

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDBEndpointReqForModifyDBEndpointConnectionModeInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDBEndpointReqForModifyDBEndpointConnectionModeInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDBEndpointReqForModifyDBEndpointConnectionModeInput):
            return True

        return self.to_dict() != other.to_dict()
