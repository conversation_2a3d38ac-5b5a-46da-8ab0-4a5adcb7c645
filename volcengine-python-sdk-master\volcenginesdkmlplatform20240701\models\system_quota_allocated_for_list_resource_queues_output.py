# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SystemQuotaAllocatedForListResourceQueuesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cpu': 'float',
        'gpu_count': 'float',
        'gpu_count_infos': 'list[GpuCountInfoForListResourceQueuesOutput]',
        'gpu_memory_infos': 'list[GpuMemoryInfoForListResourceQueuesOutput]',
        'gpu_rdma_infos': 'list[GpuRdmaInfoForListResourceQueuesOutput]',
        'memory_gi_b': 'float',
        'volume_size_gi_b': 'float',
        'volume_size_infos': 'list[VolumeSizeInfoForListResourceQueuesOutput]'
    }

    attribute_map = {
        'cpu': 'Cpu',
        'gpu_count': 'GpuCount',
        'gpu_count_infos': 'GpuCountInfos',
        'gpu_memory_infos': 'GpuMemoryInfos',
        'gpu_rdma_infos': 'GpuRdmaInfos',
        'memory_gi_b': 'MemoryGiB',
        'volume_size_gi_b': 'VolumeSizeGiB',
        'volume_size_infos': 'VolumeSizeInfos'
    }

    def __init__(self, cpu=None, gpu_count=None, gpu_count_infos=None, gpu_memory_infos=None, gpu_rdma_infos=None, memory_gi_b=None, volume_size_gi_b=None, volume_size_infos=None, _configuration=None):  # noqa: E501
        """SystemQuotaAllocatedForListResourceQueuesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cpu = None
        self._gpu_count = None
        self._gpu_count_infos = None
        self._gpu_memory_infos = None
        self._gpu_rdma_infos = None
        self._memory_gi_b = None
        self._volume_size_gi_b = None
        self._volume_size_infos = None
        self.discriminator = None

        if cpu is not None:
            self.cpu = cpu
        if gpu_count is not None:
            self.gpu_count = gpu_count
        if gpu_count_infos is not None:
            self.gpu_count_infos = gpu_count_infos
        if gpu_memory_infos is not None:
            self.gpu_memory_infos = gpu_memory_infos
        if gpu_rdma_infos is not None:
            self.gpu_rdma_infos = gpu_rdma_infos
        if memory_gi_b is not None:
            self.memory_gi_b = memory_gi_b
        if volume_size_gi_b is not None:
            self.volume_size_gi_b = volume_size_gi_b
        if volume_size_infos is not None:
            self.volume_size_infos = volume_size_infos

    @property
    def cpu(self):
        """Gets the cpu of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501


        :return: The cpu of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :rtype: float
        """
        return self._cpu

    @cpu.setter
    def cpu(self, cpu):
        """Sets the cpu of this SystemQuotaAllocatedForListResourceQueuesOutput.


        :param cpu: The cpu of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :type: float
        """

        self._cpu = cpu

    @property
    def gpu_count(self):
        """Gets the gpu_count of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501


        :return: The gpu_count of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :rtype: float
        """
        return self._gpu_count

    @gpu_count.setter
    def gpu_count(self, gpu_count):
        """Sets the gpu_count of this SystemQuotaAllocatedForListResourceQueuesOutput.


        :param gpu_count: The gpu_count of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :type: float
        """

        self._gpu_count = gpu_count

    @property
    def gpu_count_infos(self):
        """Gets the gpu_count_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501


        :return: The gpu_count_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :rtype: list[GpuCountInfoForListResourceQueuesOutput]
        """
        return self._gpu_count_infos

    @gpu_count_infos.setter
    def gpu_count_infos(self, gpu_count_infos):
        """Sets the gpu_count_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.


        :param gpu_count_infos: The gpu_count_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :type: list[GpuCountInfoForListResourceQueuesOutput]
        """

        self._gpu_count_infos = gpu_count_infos

    @property
    def gpu_memory_infos(self):
        """Gets the gpu_memory_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501


        :return: The gpu_memory_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :rtype: list[GpuMemoryInfoForListResourceQueuesOutput]
        """
        return self._gpu_memory_infos

    @gpu_memory_infos.setter
    def gpu_memory_infos(self, gpu_memory_infos):
        """Sets the gpu_memory_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.


        :param gpu_memory_infos: The gpu_memory_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :type: list[GpuMemoryInfoForListResourceQueuesOutput]
        """

        self._gpu_memory_infos = gpu_memory_infos

    @property
    def gpu_rdma_infos(self):
        """Gets the gpu_rdma_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501


        :return: The gpu_rdma_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :rtype: list[GpuRdmaInfoForListResourceQueuesOutput]
        """
        return self._gpu_rdma_infos

    @gpu_rdma_infos.setter
    def gpu_rdma_infos(self, gpu_rdma_infos):
        """Sets the gpu_rdma_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.


        :param gpu_rdma_infos: The gpu_rdma_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :type: list[GpuRdmaInfoForListResourceQueuesOutput]
        """

        self._gpu_rdma_infos = gpu_rdma_infos

    @property
    def memory_gi_b(self):
        """Gets the memory_gi_b of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501


        :return: The memory_gi_b of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :rtype: float
        """
        return self._memory_gi_b

    @memory_gi_b.setter
    def memory_gi_b(self, memory_gi_b):
        """Sets the memory_gi_b of this SystemQuotaAllocatedForListResourceQueuesOutput.


        :param memory_gi_b: The memory_gi_b of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :type: float
        """

        self._memory_gi_b = memory_gi_b

    @property
    def volume_size_gi_b(self):
        """Gets the volume_size_gi_b of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501


        :return: The volume_size_gi_b of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :rtype: float
        """
        return self._volume_size_gi_b

    @volume_size_gi_b.setter
    def volume_size_gi_b(self, volume_size_gi_b):
        """Sets the volume_size_gi_b of this SystemQuotaAllocatedForListResourceQueuesOutput.


        :param volume_size_gi_b: The volume_size_gi_b of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :type: float
        """

        self._volume_size_gi_b = volume_size_gi_b

    @property
    def volume_size_infos(self):
        """Gets the volume_size_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501


        :return: The volume_size_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :rtype: list[VolumeSizeInfoForListResourceQueuesOutput]
        """
        return self._volume_size_infos

    @volume_size_infos.setter
    def volume_size_infos(self, volume_size_infos):
        """Sets the volume_size_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.


        :param volume_size_infos: The volume_size_infos of this SystemQuotaAllocatedForListResourceQueuesOutput.  # noqa: E501
        :type: list[VolumeSizeInfoForListResourceQueuesOutput]
        """

        self._volume_size_infos = volume_size_infos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SystemQuotaAllocatedForListResourceQueuesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SystemQuotaAllocatedForListResourceQueuesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SystemQuotaAllocatedForListResourceQueuesOutput):
            return True

        return self.to_dict() != other.to_dict()
