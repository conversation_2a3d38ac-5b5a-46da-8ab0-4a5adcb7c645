# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConditionsForExportBaselineCheckListInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'checklist_name': 'str',
        'if_white': 'bool'
    }

    attribute_map = {
        'checklist_name': 'ChecklistName',
        'if_white': 'IfWhite'
    }

    def __init__(self, checklist_name=None, if_white=None, _configuration=None):  # noqa: E501
        """ConditionsForExportBaselineCheckListInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._checklist_name = None
        self._if_white = None
        self.discriminator = None

        if checklist_name is not None:
            self.checklist_name = checklist_name
        if if_white is not None:
            self.if_white = if_white

    @property
    def checklist_name(self):
        """Gets the checklist_name of this ConditionsForExportBaselineCheckListInput.  # noqa: E501


        :return: The checklist_name of this ConditionsForExportBaselineCheckListInput.  # noqa: E501
        :rtype: str
        """
        return self._checklist_name

    @checklist_name.setter
    def checklist_name(self, checklist_name):
        """Sets the checklist_name of this ConditionsForExportBaselineCheckListInput.


        :param checklist_name: The checklist_name of this ConditionsForExportBaselineCheckListInput.  # noqa: E501
        :type: str
        """

        self._checklist_name = checklist_name

    @property
    def if_white(self):
        """Gets the if_white of this ConditionsForExportBaselineCheckListInput.  # noqa: E501


        :return: The if_white of this ConditionsForExportBaselineCheckListInput.  # noqa: E501
        :rtype: bool
        """
        return self._if_white

    @if_white.setter
    def if_white(self, if_white):
        """Sets the if_white of this ConditionsForExportBaselineCheckListInput.


        :param if_white: The if_white of this ConditionsForExportBaselineCheckListInput.  # noqa: E501
        :type: bool
        """

        self._if_white = if_white

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConditionsForExportBaselineCheckListInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConditionsForExportBaselineCheckListInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConditionsForExportBaselineCheckListInput):
            return True

        return self.to_dict() != other.to_dict()
