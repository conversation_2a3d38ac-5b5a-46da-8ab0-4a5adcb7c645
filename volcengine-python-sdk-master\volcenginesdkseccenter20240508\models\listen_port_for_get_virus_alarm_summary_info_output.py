# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListenPortForGetVirusAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'listen_addr': 'str',
        'pid': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'listen_addr': 'ListenAddr',
        'pid': 'Pid'
    }

    def __init__(self, agent_id=None, listen_addr=None, pid=None, _configuration=None):  # noqa: E501
        """ListenPortForGetVirusAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._listen_addr = None
        self._pid = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if listen_addr is not None:
            self.listen_addr = listen_addr
        if pid is not None:
            self.pid = pid

    @property
    def agent_id(self):
        """Gets the agent_id of this ListenPortForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The agent_id of this ListenPortForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ListenPortForGetVirusAlarmSummaryInfoOutput.


        :param agent_id: The agent_id of this ListenPortForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def listen_addr(self):
        """Gets the listen_addr of this ListenPortForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The listen_addr of this ListenPortForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._listen_addr

    @listen_addr.setter
    def listen_addr(self, listen_addr):
        """Sets the listen_addr of this ListenPortForGetVirusAlarmSummaryInfoOutput.


        :param listen_addr: The listen_addr of this ListenPortForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._listen_addr = listen_addr

    @property
    def pid(self):
        """Gets the pid of this ListenPortForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pid of this ListenPortForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this ListenPortForGetVirusAlarmSummaryInfoOutput.


        :param pid: The pid of this ListenPortForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListenPortForGetVirusAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListenPortForGetVirusAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListenPortForGetVirusAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
