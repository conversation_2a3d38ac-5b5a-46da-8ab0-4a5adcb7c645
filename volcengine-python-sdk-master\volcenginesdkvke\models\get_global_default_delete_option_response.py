# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetGlobalDefaultDeleteOptionResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'default_delete_all_resources': 'bool',
        'last_change_time': 'str'
    }

    attribute_map = {
        'default_delete_all_resources': 'DefaultDeleteAllResources',
        'last_change_time': 'LastChangeTime'
    }

    def __init__(self, default_delete_all_resources=None, last_change_time=None, _configuration=None):  # noqa: E501
        """GetGlobalDefaultDeleteOptionResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._default_delete_all_resources = None
        self._last_change_time = None
        self.discriminator = None

        if default_delete_all_resources is not None:
            self.default_delete_all_resources = default_delete_all_resources
        if last_change_time is not None:
            self.last_change_time = last_change_time

    @property
    def default_delete_all_resources(self):
        """Gets the default_delete_all_resources of this GetGlobalDefaultDeleteOptionResponse.  # noqa: E501


        :return: The default_delete_all_resources of this GetGlobalDefaultDeleteOptionResponse.  # noqa: E501
        :rtype: bool
        """
        return self._default_delete_all_resources

    @default_delete_all_resources.setter
    def default_delete_all_resources(self, default_delete_all_resources):
        """Sets the default_delete_all_resources of this GetGlobalDefaultDeleteOptionResponse.


        :param default_delete_all_resources: The default_delete_all_resources of this GetGlobalDefaultDeleteOptionResponse.  # noqa: E501
        :type: bool
        """

        self._default_delete_all_resources = default_delete_all_resources

    @property
    def last_change_time(self):
        """Gets the last_change_time of this GetGlobalDefaultDeleteOptionResponse.  # noqa: E501


        :return: The last_change_time of this GetGlobalDefaultDeleteOptionResponse.  # noqa: E501
        :rtype: str
        """
        return self._last_change_time

    @last_change_time.setter
    def last_change_time(self, last_change_time):
        """Sets the last_change_time of this GetGlobalDefaultDeleteOptionResponse.


        :param last_change_time: The last_change_time of this GetGlobalDefaultDeleteOptionResponse.  # noqa: E501
        :type: str
        """

        self._last_change_time = last_change_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetGlobalDefaultDeleteOptionResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetGlobalDefaultDeleteOptionResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetGlobalDefaultDeleteOptionResponse):
            return True

        return self.to_dict() != other.to_dict()
