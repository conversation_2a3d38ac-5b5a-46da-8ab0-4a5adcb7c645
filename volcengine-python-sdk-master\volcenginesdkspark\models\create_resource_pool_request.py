# coding: utf-8

"""
    spark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateResourcePoolRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'billing_type': 'str',
        'name': 'str',
        'project_id': 'str',
        'resources': 'list[ResourceForcreateResourcePoolInput]',
        'security_group_id_list': 'list[str]',
        'subnet_id_list': 'list[str]',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'billing_type': 'BillingType',
        'name': 'Name',
        'project_id': 'ProjectId',
        'resources': 'Resources',
        'security_group_id_list': 'SecurityGroupIdList',
        'subnet_id_list': 'SubnetIdList',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, billing_type=None, name=None, project_id=None, resources=None, security_group_id_list=None, subnet_id_list=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """CreateResourcePoolRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._billing_type = None
        self._name = None
        self._project_id = None
        self._resources = None
        self._security_group_id_list = None
        self._subnet_id_list = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        self.billing_type = billing_type
        self.name = name
        self.project_id = project_id
        if resources is not None:
            self.resources = resources
        if security_group_id_list is not None:
            self.security_group_id_list = security_group_id_list
        if subnet_id_list is not None:
            self.subnet_id_list = subnet_id_list
        self.vpc_id = vpc_id
        self.zone_id = zone_id

    @property
    def billing_type(self):
        """Gets the billing_type of this CreateResourcePoolRequest.  # noqa: E501


        :return: The billing_type of this CreateResourcePoolRequest.  # noqa: E501
        :rtype: str
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this CreateResourcePoolRequest.


        :param billing_type: The billing_type of this CreateResourcePoolRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and billing_type is None:
            raise ValueError("Invalid value for `billing_type`, must not be `None`")  # noqa: E501

        self._billing_type = billing_type

    @property
    def name(self):
        """Gets the name of this CreateResourcePoolRequest.  # noqa: E501


        :return: The name of this CreateResourcePoolRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateResourcePoolRequest.


        :param name: The name of this CreateResourcePoolRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) > 30):
            raise ValueError("Invalid value for `name`, length must be less than or equal to `30`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) < 0):
            raise ValueError("Invalid value for `name`, length must be greater than or equal to `0`")  # noqa: E501

        self._name = name

    @property
    def project_id(self):
        """Gets the project_id of this CreateResourcePoolRequest.  # noqa: E501


        :return: The project_id of this CreateResourcePoolRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this CreateResourcePoolRequest.


        :param project_id: The project_id of this CreateResourcePoolRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and project_id is None:
            raise ValueError("Invalid value for `project_id`, must not be `None`")  # noqa: E501

        self._project_id = project_id

    @property
    def resources(self):
        """Gets the resources of this CreateResourcePoolRequest.  # noqa: E501


        :return: The resources of this CreateResourcePoolRequest.  # noqa: E501
        :rtype: list[ResourceForcreateResourcePoolInput]
        """
        return self._resources

    @resources.setter
    def resources(self, resources):
        """Sets the resources of this CreateResourcePoolRequest.


        :param resources: The resources of this CreateResourcePoolRequest.  # noqa: E501
        :type: list[ResourceForcreateResourcePoolInput]
        """

        self._resources = resources

    @property
    def security_group_id_list(self):
        """Gets the security_group_id_list of this CreateResourcePoolRequest.  # noqa: E501


        :return: The security_group_id_list of this CreateResourcePoolRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_id_list

    @security_group_id_list.setter
    def security_group_id_list(self, security_group_id_list):
        """Sets the security_group_id_list of this CreateResourcePoolRequest.


        :param security_group_id_list: The security_group_id_list of this CreateResourcePoolRequest.  # noqa: E501
        :type: list[str]
        """

        self._security_group_id_list = security_group_id_list

    @property
    def subnet_id_list(self):
        """Gets the subnet_id_list of this CreateResourcePoolRequest.  # noqa: E501


        :return: The subnet_id_list of this CreateResourcePoolRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._subnet_id_list

    @subnet_id_list.setter
    def subnet_id_list(self, subnet_id_list):
        """Sets the subnet_id_list of this CreateResourcePoolRequest.


        :param subnet_id_list: The subnet_id_list of this CreateResourcePoolRequest.  # noqa: E501
        :type: list[str]
        """

        self._subnet_id_list = subnet_id_list

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateResourcePoolRequest.  # noqa: E501


        :return: The vpc_id of this CreateResourcePoolRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateResourcePoolRequest.


        :param vpc_id: The vpc_id of this CreateResourcePoolRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this CreateResourcePoolRequest.  # noqa: E501


        :return: The zone_id of this CreateResourcePoolRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this CreateResourcePoolRequest.


        :param zone_id: The zone_id of this CreateResourcePoolRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and zone_id is None:
            raise ValueError("Invalid value for `zone_id`, must not be `None`")  # noqa: E501

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateResourcePoolRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateResourcePoolRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateResourcePoolRequest):
            return True

        return self.to_dict() != other.to_dict()
