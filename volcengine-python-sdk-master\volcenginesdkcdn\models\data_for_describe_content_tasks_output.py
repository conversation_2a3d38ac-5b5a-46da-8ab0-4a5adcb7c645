# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForDescribeContentTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'int',
        'delete': 'bool',
        'layer': 'str',
        'process': 'str',
        'refresh_prefix': 'bool',
        'remark': 'str',
        'status': 'str',
        'task_id': 'str',
        'task_type': 'str',
        'url': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'delete': 'Delete',
        'layer': 'Layer',
        'process': 'Process',
        'refresh_prefix': 'RefreshPrefix',
        'remark': 'Remark',
        'status': 'Status',
        'task_id': 'TaskID',
        'task_type': 'TaskType',
        'url': 'Url'
    }

    def __init__(self, create_time=None, delete=None, layer=None, process=None, refresh_prefix=None, remark=None, status=None, task_id=None, task_type=None, url=None, _configuration=None):  # noqa: E501
        """DataForDescribeContentTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._delete = None
        self._layer = None
        self._process = None
        self._refresh_prefix = None
        self._remark = None
        self._status = None
        self._task_id = None
        self._task_type = None
        self._url = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if delete is not None:
            self.delete = delete
        if layer is not None:
            self.layer = layer
        if process is not None:
            self.process = process
        if refresh_prefix is not None:
            self.refresh_prefix = refresh_prefix
        if remark is not None:
            self.remark = remark
        if status is not None:
            self.status = status
        if task_id is not None:
            self.task_id = task_id
        if task_type is not None:
            self.task_type = task_type
        if url is not None:
            self.url = url

    @property
    def create_time(self):
        """Gets the create_time of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The create_time of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForDescribeContentTasksOutput.


        :param create_time: The create_time of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def delete(self):
        """Gets the delete of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The delete of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: bool
        """
        return self._delete

    @delete.setter
    def delete(self, delete):
        """Sets the delete of this DataForDescribeContentTasksOutput.


        :param delete: The delete of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: bool
        """

        self._delete = delete

    @property
    def layer(self):
        """Gets the layer of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The layer of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._layer

    @layer.setter
    def layer(self, layer):
        """Sets the layer of this DataForDescribeContentTasksOutput.


        :param layer: The layer of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._layer = layer

    @property
    def process(self):
        """Gets the process of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The process of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._process

    @process.setter
    def process(self, process):
        """Sets the process of this DataForDescribeContentTasksOutput.


        :param process: The process of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._process = process

    @property
    def refresh_prefix(self):
        """Gets the refresh_prefix of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The refresh_prefix of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: bool
        """
        return self._refresh_prefix

    @refresh_prefix.setter
    def refresh_prefix(self, refresh_prefix):
        """Sets the refresh_prefix of this DataForDescribeContentTasksOutput.


        :param refresh_prefix: The refresh_prefix of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: bool
        """

        self._refresh_prefix = refresh_prefix

    @property
    def remark(self):
        """Gets the remark of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The remark of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this DataForDescribeContentTasksOutput.


        :param remark: The remark of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._remark = remark

    @property
    def status(self):
        """Gets the status of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The status of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForDescribeContentTasksOutput.


        :param status: The status of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def task_id(self):
        """Gets the task_id of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The task_id of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this DataForDescribeContentTasksOutput.


        :param task_id: The task_id of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_id = task_id

    @property
    def task_type(self):
        """Gets the task_type of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The task_type of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_type

    @task_type.setter
    def task_type(self, task_type):
        """Sets the task_type of this DataForDescribeContentTasksOutput.


        :param task_type: The task_type of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_type = task_type

    @property
    def url(self):
        """Gets the url of this DataForDescribeContentTasksOutput.  # noqa: E501


        :return: The url of this DataForDescribeContentTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this DataForDescribeContentTasksOutput.


        :param url: The url of this DataForDescribeContentTasksOutput.  # noqa: E501
        :type: str
        """

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForDescribeContentTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForDescribeContentTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForDescribeContentTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
