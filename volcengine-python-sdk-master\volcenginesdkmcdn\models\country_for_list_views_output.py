# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CountryForListViewsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'code': 'str',
        'id': 'str',
        'isps': 'list[IspForListViewsOutput]',
        'name': 'str',
        'provinces': 'list[ProvinceForListViewsOutput]'
    }

    attribute_map = {
        'code': 'Code',
        'id': 'Id',
        'isps': 'Isps',
        'name': 'Name',
        'provinces': 'Provinces'
    }

    def __init__(self, code=None, id=None, isps=None, name=None, provinces=None, _configuration=None):  # noqa: E501
        """CountryForListViewsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._code = None
        self._id = None
        self._isps = None
        self._name = None
        self._provinces = None
        self.discriminator = None

        if code is not None:
            self.code = code
        if id is not None:
            self.id = id
        if isps is not None:
            self.isps = isps
        if name is not None:
            self.name = name
        if provinces is not None:
            self.provinces = provinces

    @property
    def code(self):
        """Gets the code of this CountryForListViewsOutput.  # noqa: E501


        :return: The code of this CountryForListViewsOutput.  # noqa: E501
        :rtype: str
        """
        return self._code

    @code.setter
    def code(self, code):
        """Sets the code of this CountryForListViewsOutput.


        :param code: The code of this CountryForListViewsOutput.  # noqa: E501
        :type: str
        """

        self._code = code

    @property
    def id(self):
        """Gets the id of this CountryForListViewsOutput.  # noqa: E501


        :return: The id of this CountryForListViewsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this CountryForListViewsOutput.


        :param id: The id of this CountryForListViewsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def isps(self):
        """Gets the isps of this CountryForListViewsOutput.  # noqa: E501


        :return: The isps of this CountryForListViewsOutput.  # noqa: E501
        :rtype: list[IspForListViewsOutput]
        """
        return self._isps

    @isps.setter
    def isps(self, isps):
        """Sets the isps of this CountryForListViewsOutput.


        :param isps: The isps of this CountryForListViewsOutput.  # noqa: E501
        :type: list[IspForListViewsOutput]
        """

        self._isps = isps

    @property
    def name(self):
        """Gets the name of this CountryForListViewsOutput.  # noqa: E501


        :return: The name of this CountryForListViewsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CountryForListViewsOutput.


        :param name: The name of this CountryForListViewsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def provinces(self):
        """Gets the provinces of this CountryForListViewsOutput.  # noqa: E501


        :return: The provinces of this CountryForListViewsOutput.  # noqa: E501
        :rtype: list[ProvinceForListViewsOutput]
        """
        return self._provinces

    @provinces.setter
    def provinces(self, provinces):
        """Sets the provinces of this CountryForListViewsOutput.


        :param provinces: The provinces of this CountryForListViewsOutput.  # noqa: E501
        :type: list[ProvinceForListViewsOutput]
        """

        self._provinces = provinces

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CountryForListViewsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CountryForListViewsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CountryForListViewsOutput):
            return True

        return self.to_dict() != other.to_dict()
