# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SetRenewalTypeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'instance_id': 'str',
        'product': 'str',
        'renew_type': 'str',
        'renewal_duration': 'int',
        'renewal_duration_unit': 'str',
        'renewal_times': 'int',
        'set_renewal_related_instance': 'bool'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'instance_id': 'InstanceID',
        'product': 'Product',
        'renew_type': 'RenewType',
        'renewal_duration': 'RenewalDuration',
        'renewal_duration_unit': 'RenewalDurationUnit',
        'renewal_times': 'RenewalTimes',
        'set_renewal_related_instance': 'SetRenewalRelatedInstance'
    }

    def __init__(self, client_token=None, instance_id=None, product=None, renew_type=None, renewal_duration=None, renewal_duration_unit=None, renewal_times=None, set_renewal_related_instance=None, _configuration=None):  # noqa: E501
        """SetRenewalTypeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._instance_id = None
        self._product = None
        self._renew_type = None
        self._renewal_duration = None
        self._renewal_duration_unit = None
        self._renewal_times = None
        self._set_renewal_related_instance = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        self.instance_id = instance_id
        self.product = product
        self.renew_type = renew_type
        if renewal_duration is not None:
            self.renewal_duration = renewal_duration
        if renewal_duration_unit is not None:
            self.renewal_duration_unit = renewal_duration_unit
        if renewal_times is not None:
            self.renewal_times = renewal_times
        if set_renewal_related_instance is not None:
            self.set_renewal_related_instance = set_renewal_related_instance

    @property
    def client_token(self):
        """Gets the client_token of this SetRenewalTypeRequest.  # noqa: E501


        :return: The client_token of this SetRenewalTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this SetRenewalTypeRequest.


        :param client_token: The client_token of this SetRenewalTypeRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def instance_id(self):
        """Gets the instance_id of this SetRenewalTypeRequest.  # noqa: E501


        :return: The instance_id of this SetRenewalTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this SetRenewalTypeRequest.


        :param instance_id: The instance_id of this SetRenewalTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def product(self):
        """Gets the product of this SetRenewalTypeRequest.  # noqa: E501


        :return: The product of this SetRenewalTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this SetRenewalTypeRequest.


        :param product: The product of this SetRenewalTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and product is None:
            raise ValueError("Invalid value for `product`, must not be `None`")  # noqa: E501

        self._product = product

    @property
    def renew_type(self):
        """Gets the renew_type of this SetRenewalTypeRequest.  # noqa: E501


        :return: The renew_type of this SetRenewalTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this SetRenewalTypeRequest.


        :param renew_type: The renew_type of this SetRenewalTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and renew_type is None:
            raise ValueError("Invalid value for `renew_type`, must not be `None`")  # noqa: E501

        self._renew_type = renew_type

    @property
    def renewal_duration(self):
        """Gets the renewal_duration of this SetRenewalTypeRequest.  # noqa: E501


        :return: The renewal_duration of this SetRenewalTypeRequest.  # noqa: E501
        :rtype: int
        """
        return self._renewal_duration

    @renewal_duration.setter
    def renewal_duration(self, renewal_duration):
        """Sets the renewal_duration of this SetRenewalTypeRequest.


        :param renewal_duration: The renewal_duration of this SetRenewalTypeRequest.  # noqa: E501
        :type: int
        """

        self._renewal_duration = renewal_duration

    @property
    def renewal_duration_unit(self):
        """Gets the renewal_duration_unit of this SetRenewalTypeRequest.  # noqa: E501


        :return: The renewal_duration_unit of this SetRenewalTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._renewal_duration_unit

    @renewal_duration_unit.setter
    def renewal_duration_unit(self, renewal_duration_unit):
        """Sets the renewal_duration_unit of this SetRenewalTypeRequest.


        :param renewal_duration_unit: The renewal_duration_unit of this SetRenewalTypeRequest.  # noqa: E501
        :type: str
        """

        self._renewal_duration_unit = renewal_duration_unit

    @property
    def renewal_times(self):
        """Gets the renewal_times of this SetRenewalTypeRequest.  # noqa: E501


        :return: The renewal_times of this SetRenewalTypeRequest.  # noqa: E501
        :rtype: int
        """
        return self._renewal_times

    @renewal_times.setter
    def renewal_times(self, renewal_times):
        """Sets the renewal_times of this SetRenewalTypeRequest.


        :param renewal_times: The renewal_times of this SetRenewalTypeRequest.  # noqa: E501
        :type: int
        """

        self._renewal_times = renewal_times

    @property
    def set_renewal_related_instance(self):
        """Gets the set_renewal_related_instance of this SetRenewalTypeRequest.  # noqa: E501


        :return: The set_renewal_related_instance of this SetRenewalTypeRequest.  # noqa: E501
        :rtype: bool
        """
        return self._set_renewal_related_instance

    @set_renewal_related_instance.setter
    def set_renewal_related_instance(self, set_renewal_related_instance):
        """Sets the set_renewal_related_instance of this SetRenewalTypeRequest.


        :param set_renewal_related_instance: The set_renewal_related_instance of this SetRenewalTypeRequest.  # noqa: E501
        :type: bool
        """

        self._set_renewal_related_instance = set_renewal_related_instance

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SetRenewalTypeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SetRenewalTypeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SetRenewalTypeRequest):
            return True

        return self.to_dict() != other.to_dict()
