# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlanningSurveyPathDetailForGetAlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'tool_name': 'str',
        'tool_paras': 'ToolParasForGetAlarmDetailOutput',
        'tool_res': 'str'
    }

    attribute_map = {
        'tool_name': 'ToolName',
        'tool_paras': 'ToolParas',
        'tool_res': 'ToolRes'
    }

    def __init__(self, tool_name=None, tool_paras=None, tool_res=None, _configuration=None):  # noqa: E501
        """PlanningSurveyPathDetailForGetAlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._tool_name = None
        self._tool_paras = None
        self._tool_res = None
        self.discriminator = None

        if tool_name is not None:
            self.tool_name = tool_name
        if tool_paras is not None:
            self.tool_paras = tool_paras
        if tool_res is not None:
            self.tool_res = tool_res

    @property
    def tool_name(self):
        """Gets the tool_name of this PlanningSurveyPathDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The tool_name of this PlanningSurveyPathDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._tool_name

    @tool_name.setter
    def tool_name(self, tool_name):
        """Sets the tool_name of this PlanningSurveyPathDetailForGetAlarmDetailOutput.


        :param tool_name: The tool_name of this PlanningSurveyPathDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._tool_name = tool_name

    @property
    def tool_paras(self):
        """Gets the tool_paras of this PlanningSurveyPathDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The tool_paras of this PlanningSurveyPathDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: ToolParasForGetAlarmDetailOutput
        """
        return self._tool_paras

    @tool_paras.setter
    def tool_paras(self, tool_paras):
        """Sets the tool_paras of this PlanningSurveyPathDetailForGetAlarmDetailOutput.


        :param tool_paras: The tool_paras of this PlanningSurveyPathDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: ToolParasForGetAlarmDetailOutput
        """

        self._tool_paras = tool_paras

    @property
    def tool_res(self):
        """Gets the tool_res of this PlanningSurveyPathDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The tool_res of this PlanningSurveyPathDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._tool_res

    @tool_res.setter
    def tool_res(self, tool_res):
        """Sets the tool_res of this PlanningSurveyPathDetailForGetAlarmDetailOutput.


        :param tool_res: The tool_res of this PlanningSurveyPathDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._tool_res = tool_res

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlanningSurveyPathDetailForGetAlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlanningSurveyPathDetailForGetAlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlanningSurveyPathDetailForGetAlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
