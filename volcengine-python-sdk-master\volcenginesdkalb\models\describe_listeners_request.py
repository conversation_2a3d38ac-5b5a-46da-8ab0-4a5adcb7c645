# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeListenersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'listener_ids': 'list[str]',
        'listener_name': 'str',
        'load_balancer_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'protocol': 'str',
        'tag_filters': 'list[TagFilterForDescribeListenersInput]'
    }

    attribute_map = {
        'listener_ids': 'ListenerIds',
        'listener_name': 'ListenerName',
        'load_balancer_id': 'LoadBalancerId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'protocol': 'Protocol',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, listener_ids=None, listener_name=None, load_balancer_id=None, page_number=None, page_size=None, project_name=None, protocol=None, tag_filters=None, _configuration=None):  # noqa: E501
        """DescribeListenersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._listener_ids = None
        self._listener_name = None
        self._load_balancer_id = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._protocol = None
        self._tag_filters = None
        self.discriminator = None

        if listener_ids is not None:
            self.listener_ids = listener_ids
        if listener_name is not None:
            self.listener_name = listener_name
        if load_balancer_id is not None:
            self.load_balancer_id = load_balancer_id
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if protocol is not None:
            self.protocol = protocol
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def listener_ids(self):
        """Gets the listener_ids of this DescribeListenersRequest.  # noqa: E501


        :return: The listener_ids of this DescribeListenersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._listener_ids

    @listener_ids.setter
    def listener_ids(self, listener_ids):
        """Sets the listener_ids of this DescribeListenersRequest.


        :param listener_ids: The listener_ids of this DescribeListenersRequest.  # noqa: E501
        :type: list[str]
        """

        self._listener_ids = listener_ids

    @property
    def listener_name(self):
        """Gets the listener_name of this DescribeListenersRequest.  # noqa: E501


        :return: The listener_name of this DescribeListenersRequest.  # noqa: E501
        :rtype: str
        """
        return self._listener_name

    @listener_name.setter
    def listener_name(self, listener_name):
        """Sets the listener_name of this DescribeListenersRequest.


        :param listener_name: The listener_name of this DescribeListenersRequest.  # noqa: E501
        :type: str
        """

        self._listener_name = listener_name

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this DescribeListenersRequest.  # noqa: E501


        :return: The load_balancer_id of this DescribeListenersRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this DescribeListenersRequest.


        :param load_balancer_id: The load_balancer_id of this DescribeListenersRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_id = load_balancer_id

    @property
    def page_number(self):
        """Gets the page_number of this DescribeListenersRequest.  # noqa: E501


        :return: The page_number of this DescribeListenersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeListenersRequest.


        :param page_number: The page_number of this DescribeListenersRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeListenersRequest.  # noqa: E501


        :return: The page_size of this DescribeListenersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeListenersRequest.


        :param page_size: The page_size of this DescribeListenersRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeListenersRequest.  # noqa: E501


        :return: The project_name of this DescribeListenersRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeListenersRequest.


        :param project_name: The project_name of this DescribeListenersRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def protocol(self):
        """Gets the protocol of this DescribeListenersRequest.  # noqa: E501


        :return: The protocol of this DescribeListenersRequest.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this DescribeListenersRequest.


        :param protocol: The protocol of this DescribeListenersRequest.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeListenersRequest.  # noqa: E501


        :return: The tag_filters of this DescribeListenersRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeListenersInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeListenersRequest.


        :param tag_filters: The tag_filters of this DescribeListenersRequest.  # noqa: E501
        :type: list[TagFilterForDescribeListenersInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeListenersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeListenersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeListenersRequest):
            return True

        return self.to_dict() != other.to_dict()
