# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateKafkaTriggerRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'batch_flush_duration_milliseconds': 'int',
        'batch_size': 'int',
        'description': 'str',
        'enabled': 'bool',
        'function_id': 'str',
        'id': 'str',
        'maximum_retry_attempts': 'int'
    }

    attribute_map = {
        'batch_flush_duration_milliseconds': 'BatchFlushDurationMilliseconds',
        'batch_size': 'BatchSize',
        'description': 'Description',
        'enabled': 'Enabled',
        'function_id': 'FunctionId',
        'id': 'Id',
        'maximum_retry_attempts': 'MaximumRetryAttempts'
    }

    def __init__(self, batch_flush_duration_milliseconds=None, batch_size=None, description=None, enabled=None, function_id=None, id=None, maximum_retry_attempts=None, _configuration=None):  # noqa: E501
        """UpdateKafkaTriggerRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._batch_flush_duration_milliseconds = None
        self._batch_size = None
        self._description = None
        self._enabled = None
        self._function_id = None
        self._id = None
        self._maximum_retry_attempts = None
        self.discriminator = None

        if batch_flush_duration_milliseconds is not None:
            self.batch_flush_duration_milliseconds = batch_flush_duration_milliseconds
        if batch_size is not None:
            self.batch_size = batch_size
        if description is not None:
            self.description = description
        if enabled is not None:
            self.enabled = enabled
        self.function_id = function_id
        self.id = id
        if maximum_retry_attempts is not None:
            self.maximum_retry_attempts = maximum_retry_attempts

    @property
    def batch_flush_duration_milliseconds(self):
        """Gets the batch_flush_duration_milliseconds of this UpdateKafkaTriggerRequest.  # noqa: E501


        :return: The batch_flush_duration_milliseconds of this UpdateKafkaTriggerRequest.  # noqa: E501
        :rtype: int
        """
        return self._batch_flush_duration_milliseconds

    @batch_flush_duration_milliseconds.setter
    def batch_flush_duration_milliseconds(self, batch_flush_duration_milliseconds):
        """Sets the batch_flush_duration_milliseconds of this UpdateKafkaTriggerRequest.


        :param batch_flush_duration_milliseconds: The batch_flush_duration_milliseconds of this UpdateKafkaTriggerRequest.  # noqa: E501
        :type: int
        """

        self._batch_flush_duration_milliseconds = batch_flush_duration_milliseconds

    @property
    def batch_size(self):
        """Gets the batch_size of this UpdateKafkaTriggerRequest.  # noqa: E501


        :return: The batch_size of this UpdateKafkaTriggerRequest.  # noqa: E501
        :rtype: int
        """
        return self._batch_size

    @batch_size.setter
    def batch_size(self, batch_size):
        """Sets the batch_size of this UpdateKafkaTriggerRequest.


        :param batch_size: The batch_size of this UpdateKafkaTriggerRequest.  # noqa: E501
        :type: int
        """

        self._batch_size = batch_size

    @property
    def description(self):
        """Gets the description of this UpdateKafkaTriggerRequest.  # noqa: E501


        :return: The description of this UpdateKafkaTriggerRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateKafkaTriggerRequest.


        :param description: The description of this UpdateKafkaTriggerRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enabled(self):
        """Gets the enabled of this UpdateKafkaTriggerRequest.  # noqa: E501


        :return: The enabled of this UpdateKafkaTriggerRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this UpdateKafkaTriggerRequest.


        :param enabled: The enabled of this UpdateKafkaTriggerRequest.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def function_id(self):
        """Gets the function_id of this UpdateKafkaTriggerRequest.  # noqa: E501


        :return: The function_id of this UpdateKafkaTriggerRequest.  # noqa: E501
        :rtype: str
        """
        return self._function_id

    @function_id.setter
    def function_id(self, function_id):
        """Sets the function_id of this UpdateKafkaTriggerRequest.


        :param function_id: The function_id of this UpdateKafkaTriggerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and function_id is None:
            raise ValueError("Invalid value for `function_id`, must not be `None`")  # noqa: E501

        self._function_id = function_id

    @property
    def id(self):
        """Gets the id of this UpdateKafkaTriggerRequest.  # noqa: E501


        :return: The id of this UpdateKafkaTriggerRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateKafkaTriggerRequest.


        :param id: The id of this UpdateKafkaTriggerRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def maximum_retry_attempts(self):
        """Gets the maximum_retry_attempts of this UpdateKafkaTriggerRequest.  # noqa: E501


        :return: The maximum_retry_attempts of this UpdateKafkaTriggerRequest.  # noqa: E501
        :rtype: int
        """
        return self._maximum_retry_attempts

    @maximum_retry_attempts.setter
    def maximum_retry_attempts(self, maximum_retry_attempts):
        """Sets the maximum_retry_attempts of this UpdateKafkaTriggerRequest.


        :param maximum_retry_attempts: The maximum_retry_attempts of this UpdateKafkaTriggerRequest.  # noqa: E501
        :type: int
        """

        self._maximum_retry_attempts = maximum_retry_attempts

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateKafkaTriggerRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateKafkaTriggerRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateKafkaTriggerRequest):
            return True

        return self.to_dict() != other.to_dict()
