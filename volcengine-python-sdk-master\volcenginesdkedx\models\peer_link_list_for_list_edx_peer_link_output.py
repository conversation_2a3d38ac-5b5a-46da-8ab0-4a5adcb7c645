# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PeerLinkListForListEDXPeerLinkOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth_pkg_id': 'str',
        'bandwidth_size': 'int',
        'edx_instance_id': 'str',
        'end_vgw_instance_id': 'str',
        'peer_link_id': 'str',
        'start_vgw_instance_id': 'str',
        'state': 'str'
    }

    attribute_map = {
        'bandwidth_pkg_id': 'BandwidthPkgId',
        'bandwidth_size': 'BandwidthSize',
        'edx_instance_id': 'EDXInstanceId',
        'end_vgw_instance_id': 'EndVGWInstanceId',
        'peer_link_id': 'PeerLinkId',
        'start_vgw_instance_id': 'StartVGWInstanceId',
        'state': 'State'
    }

    def __init__(self, bandwidth_pkg_id=None, bandwidth_size=None, edx_instance_id=None, end_vgw_instance_id=None, peer_link_id=None, start_vgw_instance_id=None, state=None, _configuration=None):  # noqa: E501
        """PeerLinkListForListEDXPeerLinkOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth_pkg_id = None
        self._bandwidth_size = None
        self._edx_instance_id = None
        self._end_vgw_instance_id = None
        self._peer_link_id = None
        self._start_vgw_instance_id = None
        self._state = None
        self.discriminator = None

        if bandwidth_pkg_id is not None:
            self.bandwidth_pkg_id = bandwidth_pkg_id
        if bandwidth_size is not None:
            self.bandwidth_size = bandwidth_size
        if edx_instance_id is not None:
            self.edx_instance_id = edx_instance_id
        if end_vgw_instance_id is not None:
            self.end_vgw_instance_id = end_vgw_instance_id
        if peer_link_id is not None:
            self.peer_link_id = peer_link_id
        if start_vgw_instance_id is not None:
            self.start_vgw_instance_id = start_vgw_instance_id
        if state is not None:
            self.state = state

    @property
    def bandwidth_pkg_id(self):
        """Gets the bandwidth_pkg_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501


        :return: The bandwidth_pkg_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_pkg_id

    @bandwidth_pkg_id.setter
    def bandwidth_pkg_id(self, bandwidth_pkg_id):
        """Sets the bandwidth_pkg_id of this PeerLinkListForListEDXPeerLinkOutput.


        :param bandwidth_pkg_id: The bandwidth_pkg_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :type: str
        """

        self._bandwidth_pkg_id = bandwidth_pkg_id

    @property
    def bandwidth_size(self):
        """Gets the bandwidth_size of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501


        :return: The bandwidth_size of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_size

    @bandwidth_size.setter
    def bandwidth_size(self, bandwidth_size):
        """Sets the bandwidth_size of this PeerLinkListForListEDXPeerLinkOutput.


        :param bandwidth_size: The bandwidth_size of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth_size = bandwidth_size

    @property
    def edx_instance_id(self):
        """Gets the edx_instance_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501


        :return: The edx_instance_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :rtype: str
        """
        return self._edx_instance_id

    @edx_instance_id.setter
    def edx_instance_id(self, edx_instance_id):
        """Sets the edx_instance_id of this PeerLinkListForListEDXPeerLinkOutput.


        :param edx_instance_id: The edx_instance_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :type: str
        """

        self._edx_instance_id = edx_instance_id

    @property
    def end_vgw_instance_id(self):
        """Gets the end_vgw_instance_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501


        :return: The end_vgw_instance_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_vgw_instance_id

    @end_vgw_instance_id.setter
    def end_vgw_instance_id(self, end_vgw_instance_id):
        """Sets the end_vgw_instance_id of this PeerLinkListForListEDXPeerLinkOutput.


        :param end_vgw_instance_id: The end_vgw_instance_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :type: str
        """

        self._end_vgw_instance_id = end_vgw_instance_id

    @property
    def peer_link_id(self):
        """Gets the peer_link_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501


        :return: The peer_link_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :rtype: str
        """
        return self._peer_link_id

    @peer_link_id.setter
    def peer_link_id(self, peer_link_id):
        """Sets the peer_link_id of this PeerLinkListForListEDXPeerLinkOutput.


        :param peer_link_id: The peer_link_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :type: str
        """

        self._peer_link_id = peer_link_id

    @property
    def start_vgw_instance_id(self):
        """Gets the start_vgw_instance_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501


        :return: The start_vgw_instance_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_vgw_instance_id

    @start_vgw_instance_id.setter
    def start_vgw_instance_id(self, start_vgw_instance_id):
        """Sets the start_vgw_instance_id of this PeerLinkListForListEDXPeerLinkOutput.


        :param start_vgw_instance_id: The start_vgw_instance_id of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :type: str
        """

        self._start_vgw_instance_id = start_vgw_instance_id

    @property
    def state(self):
        """Gets the state of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501


        :return: The state of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this PeerLinkListForListEDXPeerLinkOutput.


        :param state: The state of this PeerLinkListForListEDXPeerLinkOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PeerLinkListForListEDXPeerLinkOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PeerLinkListForListEDXPeerLinkOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PeerLinkListForListEDXPeerLinkOutput):
            return True

        return self.to_dict() != other.to_dict()
