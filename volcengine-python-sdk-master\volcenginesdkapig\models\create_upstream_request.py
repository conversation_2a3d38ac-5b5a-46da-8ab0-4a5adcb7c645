# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateUpstreamRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'circuit_breaking_settings': 'CircuitBreakingSettingsForCreateUpstreamInput',
        'comments': 'str',
        'gateway_id': 'str',
        'load_balancer_settings': 'LoadBalancerSettingsForCreateUpstreamInput',
        'name': 'str',
        'protocol': 'str',
        'resource_type': 'str',
        'source_type': 'str',
        'tags': 'list[TagForCreateUpstreamInput]',
        'tls_settings': 'TlsSettingsForCreateUpstreamInput',
        'upstream_spec': 'UpstreamSpecForCreateUpstreamInput'
    }

    attribute_map = {
        'circuit_breaking_settings': 'CircuitBreakingSettings',
        'comments': 'Comments',
        'gateway_id': 'GatewayId',
        'load_balancer_settings': 'LoadBalancerSettings',
        'name': 'Name',
        'protocol': 'Protocol',
        'resource_type': 'ResourceType',
        'source_type': 'SourceType',
        'tags': 'Tags',
        'tls_settings': 'TlsSettings',
        'upstream_spec': 'UpstreamSpec'
    }

    def __init__(self, circuit_breaking_settings=None, comments=None, gateway_id=None, load_balancer_settings=None, name=None, protocol=None, resource_type=None, source_type=None, tags=None, tls_settings=None, upstream_spec=None, _configuration=None):  # noqa: E501
        """CreateUpstreamRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._circuit_breaking_settings = None
        self._comments = None
        self._gateway_id = None
        self._load_balancer_settings = None
        self._name = None
        self._protocol = None
        self._resource_type = None
        self._source_type = None
        self._tags = None
        self._tls_settings = None
        self._upstream_spec = None
        self.discriminator = None

        if circuit_breaking_settings is not None:
            self.circuit_breaking_settings = circuit_breaking_settings
        if comments is not None:
            self.comments = comments
        self.gateway_id = gateway_id
        if load_balancer_settings is not None:
            self.load_balancer_settings = load_balancer_settings
        self.name = name
        if protocol is not None:
            self.protocol = protocol
        if resource_type is not None:
            self.resource_type = resource_type
        self.source_type = source_type
        if tags is not None:
            self.tags = tags
        if tls_settings is not None:
            self.tls_settings = tls_settings
        if upstream_spec is not None:
            self.upstream_spec = upstream_spec

    @property
    def circuit_breaking_settings(self):
        """Gets the circuit_breaking_settings of this CreateUpstreamRequest.  # noqa: E501


        :return: The circuit_breaking_settings of this CreateUpstreamRequest.  # noqa: E501
        :rtype: CircuitBreakingSettingsForCreateUpstreamInput
        """
        return self._circuit_breaking_settings

    @circuit_breaking_settings.setter
    def circuit_breaking_settings(self, circuit_breaking_settings):
        """Sets the circuit_breaking_settings of this CreateUpstreamRequest.


        :param circuit_breaking_settings: The circuit_breaking_settings of this CreateUpstreamRequest.  # noqa: E501
        :type: CircuitBreakingSettingsForCreateUpstreamInput
        """

        self._circuit_breaking_settings = circuit_breaking_settings

    @property
    def comments(self):
        """Gets the comments of this CreateUpstreamRequest.  # noqa: E501


        :return: The comments of this CreateUpstreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._comments

    @comments.setter
    def comments(self, comments):
        """Sets the comments of this CreateUpstreamRequest.


        :param comments: The comments of this CreateUpstreamRequest.  # noqa: E501
        :type: str
        """

        self._comments = comments

    @property
    def gateway_id(self):
        """Gets the gateway_id of this CreateUpstreamRequest.  # noqa: E501


        :return: The gateway_id of this CreateUpstreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._gateway_id

    @gateway_id.setter
    def gateway_id(self, gateway_id):
        """Sets the gateway_id of this CreateUpstreamRequest.


        :param gateway_id: The gateway_id of this CreateUpstreamRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and gateway_id is None:
            raise ValueError("Invalid value for `gateway_id`, must not be `None`")  # noqa: E501

        self._gateway_id = gateway_id

    @property
    def load_balancer_settings(self):
        """Gets the load_balancer_settings of this CreateUpstreamRequest.  # noqa: E501


        :return: The load_balancer_settings of this CreateUpstreamRequest.  # noqa: E501
        :rtype: LoadBalancerSettingsForCreateUpstreamInput
        """
        return self._load_balancer_settings

    @load_balancer_settings.setter
    def load_balancer_settings(self, load_balancer_settings):
        """Sets the load_balancer_settings of this CreateUpstreamRequest.


        :param load_balancer_settings: The load_balancer_settings of this CreateUpstreamRequest.  # noqa: E501
        :type: LoadBalancerSettingsForCreateUpstreamInput
        """

        self._load_balancer_settings = load_balancer_settings

    @property
    def name(self):
        """Gets the name of this CreateUpstreamRequest.  # noqa: E501


        :return: The name of this CreateUpstreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateUpstreamRequest.


        :param name: The name of this CreateUpstreamRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def protocol(self):
        """Gets the protocol of this CreateUpstreamRequest.  # noqa: E501


        :return: The protocol of this CreateUpstreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this CreateUpstreamRequest.


        :param protocol: The protocol of this CreateUpstreamRequest.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def resource_type(self):
        """Gets the resource_type of this CreateUpstreamRequest.  # noqa: E501


        :return: The resource_type of this CreateUpstreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this CreateUpstreamRequest.


        :param resource_type: The resource_type of this CreateUpstreamRequest.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def source_type(self):
        """Gets the source_type of this CreateUpstreamRequest.  # noqa: E501


        :return: The source_type of this CreateUpstreamRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this CreateUpstreamRequest.


        :param source_type: The source_type of this CreateUpstreamRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and source_type is None:
            raise ValueError("Invalid value for `source_type`, must not be `None`")  # noqa: E501

        self._source_type = source_type

    @property
    def tags(self):
        """Gets the tags of this CreateUpstreamRequest.  # noqa: E501


        :return: The tags of this CreateUpstreamRequest.  # noqa: E501
        :rtype: list[TagForCreateUpstreamInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateUpstreamRequest.


        :param tags: The tags of this CreateUpstreamRequest.  # noqa: E501
        :type: list[TagForCreateUpstreamInput]
        """

        self._tags = tags

    @property
    def tls_settings(self):
        """Gets the tls_settings of this CreateUpstreamRequest.  # noqa: E501


        :return: The tls_settings of this CreateUpstreamRequest.  # noqa: E501
        :rtype: TlsSettingsForCreateUpstreamInput
        """
        return self._tls_settings

    @tls_settings.setter
    def tls_settings(self, tls_settings):
        """Sets the tls_settings of this CreateUpstreamRequest.


        :param tls_settings: The tls_settings of this CreateUpstreamRequest.  # noqa: E501
        :type: TlsSettingsForCreateUpstreamInput
        """

        self._tls_settings = tls_settings

    @property
    def upstream_spec(self):
        """Gets the upstream_spec of this CreateUpstreamRequest.  # noqa: E501


        :return: The upstream_spec of this CreateUpstreamRequest.  # noqa: E501
        :rtype: UpstreamSpecForCreateUpstreamInput
        """
        return self._upstream_spec

    @upstream_spec.setter
    def upstream_spec(self, upstream_spec):
        """Sets the upstream_spec of this CreateUpstreamRequest.


        :param upstream_spec: The upstream_spec of this CreateUpstreamRequest.  # noqa: E501
        :type: UpstreamSpecForCreateUpstreamInput
        """

        self._upstream_spec = upstream_spec

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateUpstreamRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateUpstreamRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateUpstreamRequest):
            return True

        return self.to_dict() != other.to_dict()
