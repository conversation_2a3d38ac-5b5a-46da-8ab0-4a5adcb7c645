# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyQuotaTemplateServiceStatusResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'template_status': 'int'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'template_status': 'TemplateStatus'
    }

    def __init__(self, account_id=None, template_status=None, _configuration=None):  # noqa: E501
        """ModifyQuotaTemplateServiceStatusResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._template_status = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if template_status is not None:
            self.template_status = template_status

    @property
    def account_id(self):
        """Gets the account_id of this ModifyQuotaTemplateServiceStatusResponse.  # noqa: E501


        :return: The account_id of this ModifyQuotaTemplateServiceStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this ModifyQuotaTemplateServiceStatusResponse.


        :param account_id: The account_id of this ModifyQuotaTemplateServiceStatusResponse.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def template_status(self):
        """Gets the template_status of this ModifyQuotaTemplateServiceStatusResponse.  # noqa: E501


        :return: The template_status of this ModifyQuotaTemplateServiceStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._template_status

    @template_status.setter
    def template_status(self, template_status):
        """Sets the template_status of this ModifyQuotaTemplateServiceStatusResponse.


        :param template_status: The template_status of this ModifyQuotaTemplateServiceStatusResponse.  # noqa: E501
        :type: int
        """

        self._template_status = template_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyQuotaTemplateServiceStatusResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyQuotaTemplateServiceStatusResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyQuotaTemplateServiceStatusResponse):
            return True

        return self.to_dict() != other.to_dict()
