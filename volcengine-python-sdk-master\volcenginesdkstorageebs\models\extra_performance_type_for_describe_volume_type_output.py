# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ExtraPerformanceTypeForDescribeVolumeTypeOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'base_qos_type': 'str',
        'conversion_ratio': 'str',
        'id': 'str',
        'max_iops': 'str',
        'max_throughput': 'str'
    }

    attribute_map = {
        'base_qos_type': 'BaseQosType',
        'conversion_ratio': 'ConversionRatio',
        'id': 'Id',
        'max_iops': 'MaxIops',
        'max_throughput': 'MaxThroughput'
    }

    def __init__(self, base_qos_type=None, conversion_ratio=None, id=None, max_iops=None, max_throughput=None, _configuration=None):  # noqa: E501
        """ExtraPerformanceTypeForDescribeVolumeTypeOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._base_qos_type = None
        self._conversion_ratio = None
        self._id = None
        self._max_iops = None
        self._max_throughput = None
        self.discriminator = None

        if base_qos_type is not None:
            self.base_qos_type = base_qos_type
        if conversion_ratio is not None:
            self.conversion_ratio = conversion_ratio
        if id is not None:
            self.id = id
        if max_iops is not None:
            self.max_iops = max_iops
        if max_throughput is not None:
            self.max_throughput = max_throughput

    @property
    def base_qos_type(self):
        """Gets the base_qos_type of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501


        :return: The base_qos_type of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :rtype: str
        """
        return self._base_qos_type

    @base_qos_type.setter
    def base_qos_type(self, base_qos_type):
        """Sets the base_qos_type of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.


        :param base_qos_type: The base_qos_type of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :type: str
        """

        self._base_qos_type = base_qos_type

    @property
    def conversion_ratio(self):
        """Gets the conversion_ratio of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501


        :return: The conversion_ratio of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :rtype: str
        """
        return self._conversion_ratio

    @conversion_ratio.setter
    def conversion_ratio(self, conversion_ratio):
        """Sets the conversion_ratio of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.


        :param conversion_ratio: The conversion_ratio of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :type: str
        """

        self._conversion_ratio = conversion_ratio

    @property
    def id(self):
        """Gets the id of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501


        :return: The id of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.


        :param id: The id of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def max_iops(self):
        """Gets the max_iops of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501


        :return: The max_iops of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :rtype: str
        """
        return self._max_iops

    @max_iops.setter
    def max_iops(self, max_iops):
        """Sets the max_iops of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.


        :param max_iops: The max_iops of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :type: str
        """

        self._max_iops = max_iops

    @property
    def max_throughput(self):
        """Gets the max_throughput of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501


        :return: The max_throughput of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :rtype: str
        """
        return self._max_throughput

    @max_throughput.setter
    def max_throughput(self, max_throughput):
        """Sets the max_throughput of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.


        :param max_throughput: The max_throughput of this ExtraPerformanceTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :type: str
        """

        self._max_throughput = max_throughput

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExtraPerformanceTypeForDescribeVolumeTypeOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExtraPerformanceTypeForDescribeVolumeTypeOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExtraPerformanceTypeForDescribeVolumeTypeOutput):
            return True

        return self.to_dict() != other.to_dict()
