# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListDomainStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data': 'list[DataForListDomainStatisticsOutput]',
        'page': 'int',
        'size': 'int',
        'total': 'int'
    }

    attribute_map = {
        'data': 'Data',
        'page': 'Page',
        'size': 'Size',
        'total': 'Total'
    }

    def __init__(self, data=None, page=None, size=None, total=None, _configuration=None):  # noqa: E501
        """ListDomainStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data = None
        self._page = None
        self._size = None
        self._total = None
        self.discriminator = None

        if data is not None:
            self.data = data
        if page is not None:
            self.page = page
        if size is not None:
            self.size = size
        if total is not None:
            self.total = total

    @property
    def data(self):
        """Gets the data of this ListDomainStatisticsResponse.  # noqa: E501


        :return: The data of this ListDomainStatisticsResponse.  # noqa: E501
        :rtype: list[DataForListDomainStatisticsOutput]
        """
        return self._data

    @data.setter
    def data(self, data):
        """Sets the data of this ListDomainStatisticsResponse.


        :param data: The data of this ListDomainStatisticsResponse.  # noqa: E501
        :type: list[DataForListDomainStatisticsOutput]
        """

        self._data = data

    @property
    def page(self):
        """Gets the page of this ListDomainStatisticsResponse.  # noqa: E501


        :return: The page of this ListDomainStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._page

    @page.setter
    def page(self, page):
        """Sets the page of this ListDomainStatisticsResponse.


        :param page: The page of this ListDomainStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._page = page

    @property
    def size(self):
        """Gets the size of this ListDomainStatisticsResponse.  # noqa: E501


        :return: The size of this ListDomainStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this ListDomainStatisticsResponse.


        :param size: The size of this ListDomainStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def total(self):
        """Gets the total of this ListDomainStatisticsResponse.  # noqa: E501


        :return: The total of this ListDomainStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this ListDomainStatisticsResponse.


        :param total: The total of this ListDomainStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListDomainStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListDomainStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListDomainStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
