# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListUpstreamSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comments': 'str',
        'create_time': 'str',
        'gateway_id': 'str',
        'id': 'str',
        'ingress_settings': 'IngressSettingsForListUpstreamSourcesOutput',
        'source_spec': 'SourceSpecForListUpstreamSourcesOutput',
        'source_type': 'str',
        'status': 'str',
        'status_message': 'str',
        'update_time': 'str',
        'watch_namespaces': 'list[str]'
    }

    attribute_map = {
        'comments': 'Comments',
        'create_time': 'CreateTime',
        'gateway_id': 'GatewayId',
        'id': 'Id',
        'ingress_settings': 'IngressSettings',
        'source_spec': 'SourceSpec',
        'source_type': 'SourceType',
        'status': 'Status',
        'status_message': 'StatusMessage',
        'update_time': 'UpdateTime',
        'watch_namespaces': 'WatchNamespaces'
    }

    def __init__(self, comments=None, create_time=None, gateway_id=None, id=None, ingress_settings=None, source_spec=None, source_type=None, status=None, status_message=None, update_time=None, watch_namespaces=None, _configuration=None):  # noqa: E501
        """ItemForListUpstreamSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comments = None
        self._create_time = None
        self._gateway_id = None
        self._id = None
        self._ingress_settings = None
        self._source_spec = None
        self._source_type = None
        self._status = None
        self._status_message = None
        self._update_time = None
        self._watch_namespaces = None
        self.discriminator = None

        if comments is not None:
            self.comments = comments
        if create_time is not None:
            self.create_time = create_time
        if gateway_id is not None:
            self.gateway_id = gateway_id
        if id is not None:
            self.id = id
        if ingress_settings is not None:
            self.ingress_settings = ingress_settings
        if source_spec is not None:
            self.source_spec = source_spec
        if source_type is not None:
            self.source_type = source_type
        if status is not None:
            self.status = status
        if status_message is not None:
            self.status_message = status_message
        if update_time is not None:
            self.update_time = update_time
        if watch_namespaces is not None:
            self.watch_namespaces = watch_namespaces

    @property
    def comments(self):
        """Gets the comments of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The comments of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._comments

    @comments.setter
    def comments(self, comments):
        """Sets the comments of this ItemForListUpstreamSourcesOutput.


        :param comments: The comments of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: str
        """

        self._comments = comments

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The create_time of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListUpstreamSourcesOutput.


        :param create_time: The create_time of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def gateway_id(self):
        """Gets the gateway_id of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The gateway_id of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._gateway_id

    @gateway_id.setter
    def gateway_id(self, gateway_id):
        """Sets the gateway_id of this ItemForListUpstreamSourcesOutput.


        :param gateway_id: The gateway_id of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: str
        """

        self._gateway_id = gateway_id

    @property
    def id(self):
        """Gets the id of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The id of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListUpstreamSourcesOutput.


        :param id: The id of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def ingress_settings(self):
        """Gets the ingress_settings of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The ingress_settings of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: IngressSettingsForListUpstreamSourcesOutput
        """
        return self._ingress_settings

    @ingress_settings.setter
    def ingress_settings(self, ingress_settings):
        """Sets the ingress_settings of this ItemForListUpstreamSourcesOutput.


        :param ingress_settings: The ingress_settings of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: IngressSettingsForListUpstreamSourcesOutput
        """

        self._ingress_settings = ingress_settings

    @property
    def source_spec(self):
        """Gets the source_spec of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The source_spec of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: SourceSpecForListUpstreamSourcesOutput
        """
        return self._source_spec

    @source_spec.setter
    def source_spec(self, source_spec):
        """Sets the source_spec of this ItemForListUpstreamSourcesOutput.


        :param source_spec: The source_spec of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: SourceSpecForListUpstreamSourcesOutput
        """

        self._source_spec = source_spec

    @property
    def source_type(self):
        """Gets the source_type of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The source_type of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this ItemForListUpstreamSourcesOutput.


        :param source_type: The source_type of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: str
        """

        self._source_type = source_type

    @property
    def status(self):
        """Gets the status of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The status of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListUpstreamSourcesOutput.


        :param status: The status of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def status_message(self):
        """Gets the status_message of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The status_message of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status_message

    @status_message.setter
    def status_message(self, status_message):
        """Sets the status_message of this ItemForListUpstreamSourcesOutput.


        :param status_message: The status_message of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: str
        """

        self._status_message = status_message

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The update_time of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListUpstreamSourcesOutput.


        :param update_time: The update_time of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def watch_namespaces(self):
        """Gets the watch_namespaces of this ItemForListUpstreamSourcesOutput.  # noqa: E501


        :return: The watch_namespaces of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._watch_namespaces

    @watch_namespaces.setter
    def watch_namespaces(self, watch_namespaces):
        """Sets the watch_namespaces of this ItemForListUpstreamSourcesOutput.


        :param watch_namespaces: The watch_namespaces of this ItemForListUpstreamSourcesOutput.  # noqa: E501
        :type: list[str]
        """

        self._watch_namespaces = watch_namespaces

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListUpstreamSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListUpstreamSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListUpstreamSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
