# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateKubeconfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'type': 'str',
        'valid_duration': 'int'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'type': 'Type',
        'valid_duration': 'ValidDuration'
    }

    def __init__(self, cluster_id=None, type=None, valid_duration=None, _configuration=None):  # noqa: E501
        """CreateKubeconfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._type = None
        self._valid_duration = None
        self.discriminator = None

        self.cluster_id = cluster_id
        self.type = type
        if valid_duration is not None:
            self.valid_duration = valid_duration

    @property
    def cluster_id(self):
        """Gets the cluster_id of this CreateKubeconfigRequest.  # noqa: E501


        :return: The cluster_id of this CreateKubeconfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this CreateKubeconfigRequest.


        :param cluster_id: The cluster_id of this CreateKubeconfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def type(self):
        """Gets the type of this CreateKubeconfigRequest.  # noqa: E501


        :return: The type of this CreateKubeconfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateKubeconfigRequest.


        :param type: The type of this CreateKubeconfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501
        allowed_values = ["Public", "Private", "TargetCluster"]  # noqa: E501
        if (self._configuration.client_side_validation and
                type not in allowed_values):
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    @property
    def valid_duration(self):
        """Gets the valid_duration of this CreateKubeconfigRequest.  # noqa: E501


        :return: The valid_duration of this CreateKubeconfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._valid_duration

    @valid_duration.setter
    def valid_duration(self, valid_duration):
        """Sets the valid_duration of this CreateKubeconfigRequest.


        :param valid_duration: The valid_duration of this CreateKubeconfigRequest.  # noqa: E501
        :type: int
        """

        self._valid_duration = valid_duration

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateKubeconfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateKubeconfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateKubeconfigRequest):
            return True

        return self.to_dict() != other.to_dict()
