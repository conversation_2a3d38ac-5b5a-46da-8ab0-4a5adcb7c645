# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EstimationResultForModifyDBNodeSpecOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'effects': 'list[str]',
        'plans': 'list[str]'
    }

    attribute_map = {
        'effects': 'Effects',
        'plans': 'Plans'
    }

    def __init__(self, effects=None, plans=None, _configuration=None):  # noqa: E501
        """EstimationResultForModifyDBNodeSpecOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._effects = None
        self._plans = None
        self.discriminator = None

        if effects is not None:
            self.effects = effects
        if plans is not None:
            self.plans = plans

    @property
    def effects(self):
        """Gets the effects of this EstimationResultForModifyDBNodeSpecOutput.  # noqa: E501


        :return: The effects of this EstimationResultForModifyDBNodeSpecOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._effects

    @effects.setter
    def effects(self, effects):
        """Sets the effects of this EstimationResultForModifyDBNodeSpecOutput.


        :param effects: The effects of this EstimationResultForModifyDBNodeSpecOutput.  # noqa: E501
        :type: list[str]
        """

        self._effects = effects

    @property
    def plans(self):
        """Gets the plans of this EstimationResultForModifyDBNodeSpecOutput.  # noqa: E501


        :return: The plans of this EstimationResultForModifyDBNodeSpecOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._plans

    @plans.setter
    def plans(self, plans):
        """Sets the plans of this EstimationResultForModifyDBNodeSpecOutput.


        :param plans: The plans of this EstimationResultForModifyDBNodeSpecOutput.  # noqa: E501
        :type: list[str]
        """

        self._plans = plans

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EstimationResultForModifyDBNodeSpecOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EstimationResultForModifyDBNodeSpecOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EstimationResultForModifyDBNodeSpecOutput):
            return True

        return self.to_dict() != other.to_dict()
