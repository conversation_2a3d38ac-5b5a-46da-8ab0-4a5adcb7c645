# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BandwidthPkgListForListEDXBandwidthPkgOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allocated_bandwidth': 'int',
        'bandwidth_pkg_id': 'str',
        'billing_mode': 'str',
        'connect_area': 'str',
        'create_time': 'str',
        'edx_instance_id': 'str',
        'name': 'str',
        'paid_mode': 'str',
        'state': 'str',
        'total_bandwidth': 'int'
    }

    attribute_map = {
        'allocated_bandwidth': 'AllocatedBandwidth',
        'bandwidth_pkg_id': 'BandwidthPkgId',
        'billing_mode': 'BillingMode',
        'connect_area': 'ConnectArea',
        'create_time': 'CreateTime',
        'edx_instance_id': 'EDXInstanceId',
        'name': 'Name',
        'paid_mode': 'PaidMode',
        'state': 'State',
        'total_bandwidth': 'TotalBandwidth'
    }

    def __init__(self, allocated_bandwidth=None, bandwidth_pkg_id=None, billing_mode=None, connect_area=None, create_time=None, edx_instance_id=None, name=None, paid_mode=None, state=None, total_bandwidth=None, _configuration=None):  # noqa: E501
        """BandwidthPkgListForListEDXBandwidthPkgOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allocated_bandwidth = None
        self._bandwidth_pkg_id = None
        self._billing_mode = None
        self._connect_area = None
        self._create_time = None
        self._edx_instance_id = None
        self._name = None
        self._paid_mode = None
        self._state = None
        self._total_bandwidth = None
        self.discriminator = None

        if allocated_bandwidth is not None:
            self.allocated_bandwidth = allocated_bandwidth
        if bandwidth_pkg_id is not None:
            self.bandwidth_pkg_id = bandwidth_pkg_id
        if billing_mode is not None:
            self.billing_mode = billing_mode
        if connect_area is not None:
            self.connect_area = connect_area
        if create_time is not None:
            self.create_time = create_time
        if edx_instance_id is not None:
            self.edx_instance_id = edx_instance_id
        if name is not None:
            self.name = name
        if paid_mode is not None:
            self.paid_mode = paid_mode
        if state is not None:
            self.state = state
        if total_bandwidth is not None:
            self.total_bandwidth = total_bandwidth

    @property
    def allocated_bandwidth(self):
        """Gets the allocated_bandwidth of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The allocated_bandwidth of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: int
        """
        return self._allocated_bandwidth

    @allocated_bandwidth.setter
    def allocated_bandwidth(self, allocated_bandwidth):
        """Sets the allocated_bandwidth of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param allocated_bandwidth: The allocated_bandwidth of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: int
        """

        self._allocated_bandwidth = allocated_bandwidth

    @property
    def bandwidth_pkg_id(self):
        """Gets the bandwidth_pkg_id of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The bandwidth_pkg_id of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_pkg_id

    @bandwidth_pkg_id.setter
    def bandwidth_pkg_id(self, bandwidth_pkg_id):
        """Sets the bandwidth_pkg_id of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param bandwidth_pkg_id: The bandwidth_pkg_id of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._bandwidth_pkg_id = bandwidth_pkg_id

    @property
    def billing_mode(self):
        """Gets the billing_mode of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The billing_mode of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_mode

    @billing_mode.setter
    def billing_mode(self, billing_mode):
        """Sets the billing_mode of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param billing_mode: The billing_mode of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._billing_mode = billing_mode

    @property
    def connect_area(self):
        """Gets the connect_area of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The connect_area of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._connect_area

    @connect_area.setter
    def connect_area(self, connect_area):
        """Sets the connect_area of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param connect_area: The connect_area of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._connect_area = connect_area

    @property
    def create_time(self):
        """Gets the create_time of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The create_time of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param create_time: The create_time of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def edx_instance_id(self):
        """Gets the edx_instance_id of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The edx_instance_id of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._edx_instance_id

    @edx_instance_id.setter
    def edx_instance_id(self, edx_instance_id):
        """Sets the edx_instance_id of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param edx_instance_id: The edx_instance_id of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._edx_instance_id = edx_instance_id

    @property
    def name(self):
        """Gets the name of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The name of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param name: The name of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def paid_mode(self):
        """Gets the paid_mode of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The paid_mode of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._paid_mode

    @paid_mode.setter
    def paid_mode(self, paid_mode):
        """Sets the paid_mode of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param paid_mode: The paid_mode of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._paid_mode = paid_mode

    @property
    def state(self):
        """Gets the state of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The state of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param state: The state of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def total_bandwidth(self):
        """Gets the total_bandwidth of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501


        :return: The total_bandwidth of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_bandwidth

    @total_bandwidth.setter
    def total_bandwidth(self, total_bandwidth):
        """Sets the total_bandwidth of this BandwidthPkgListForListEDXBandwidthPkgOutput.


        :param total_bandwidth: The total_bandwidth of this BandwidthPkgListForListEDXBandwidthPkgOutput.  # noqa: E501
        :type: int
        """

        self._total_bandwidth = total_bandwidth

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BandwidthPkgListForListEDXBandwidthPkgOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BandwidthPkgListForListEDXBandwidthPkgOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BandwidthPkgListForListEDXBandwidthPkgOutput):
            return True

        return self.to_dict() != other.to_dict()
