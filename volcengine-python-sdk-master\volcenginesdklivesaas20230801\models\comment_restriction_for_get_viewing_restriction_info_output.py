# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CommentRestrictionForGetViewingRestrictionInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_allow_custom': 'bool',
        'is_allow_public': 'bool',
        'is_allow_wechat': 'bool'
    }

    attribute_map = {
        'is_allow_custom': 'IsAllowCustom',
        'is_allow_public': 'IsAllowPublic',
        'is_allow_wechat': 'IsAllowWechat'
    }

    def __init__(self, is_allow_custom=None, is_allow_public=None, is_allow_wechat=None, _configuration=None):  # noqa: E501
        """CommentRestrictionForGetViewingRestrictionInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_allow_custom = None
        self._is_allow_public = None
        self._is_allow_wechat = None
        self.discriminator = None

        if is_allow_custom is not None:
            self.is_allow_custom = is_allow_custom
        if is_allow_public is not None:
            self.is_allow_public = is_allow_public
        if is_allow_wechat is not None:
            self.is_allow_wechat = is_allow_wechat

    @property
    def is_allow_custom(self):
        """Gets the is_allow_custom of this CommentRestrictionForGetViewingRestrictionInfoOutput.  # noqa: E501


        :return: The is_allow_custom of this CommentRestrictionForGetViewingRestrictionInfoOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_allow_custom

    @is_allow_custom.setter
    def is_allow_custom(self, is_allow_custom):
        """Sets the is_allow_custom of this CommentRestrictionForGetViewingRestrictionInfoOutput.


        :param is_allow_custom: The is_allow_custom of this CommentRestrictionForGetViewingRestrictionInfoOutput.  # noqa: E501
        :type: bool
        """

        self._is_allow_custom = is_allow_custom

    @property
    def is_allow_public(self):
        """Gets the is_allow_public of this CommentRestrictionForGetViewingRestrictionInfoOutput.  # noqa: E501


        :return: The is_allow_public of this CommentRestrictionForGetViewingRestrictionInfoOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_allow_public

    @is_allow_public.setter
    def is_allow_public(self, is_allow_public):
        """Sets the is_allow_public of this CommentRestrictionForGetViewingRestrictionInfoOutput.


        :param is_allow_public: The is_allow_public of this CommentRestrictionForGetViewingRestrictionInfoOutput.  # noqa: E501
        :type: bool
        """

        self._is_allow_public = is_allow_public

    @property
    def is_allow_wechat(self):
        """Gets the is_allow_wechat of this CommentRestrictionForGetViewingRestrictionInfoOutput.  # noqa: E501


        :return: The is_allow_wechat of this CommentRestrictionForGetViewingRestrictionInfoOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_allow_wechat

    @is_allow_wechat.setter
    def is_allow_wechat(self, is_allow_wechat):
        """Sets the is_allow_wechat of this CommentRestrictionForGetViewingRestrictionInfoOutput.


        :param is_allow_wechat: The is_allow_wechat of this CommentRestrictionForGetViewingRestrictionInfoOutput.  # noqa: E501
        :type: bool
        """

        self._is_allow_wechat = is_allow_wechat

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CommentRestrictionForGetViewingRestrictionInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CommentRestrictionForGetViewingRestrictionInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CommentRestrictionForGetViewingRestrictionInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
