# coding: utf-8

"""
    rds_postgresql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceInfoForDescribeBackupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_detail': 'ChargeDetailForDescribeBackupsOutput',
        'db_engine_version': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_status': 'str',
        'instance_type': 'str',
        'memory': 'int',
        'nodes': 'list[NodeForDescribeBackupsOutput]',
        'project_name': 'str',
        'storage_space': 'int',
        'storage_type': 'str',
        'subnet_id': 'str',
        'vcpu': 'int',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'charge_detail': 'ChargeDetail',
        'db_engine_version': 'DBEngineVersion',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_status': 'InstanceStatus',
        'instance_type': 'InstanceType',
        'memory': 'Memory',
        'nodes': 'Nodes',
        'project_name': 'ProjectName',
        'storage_space': 'StorageSpace',
        'storage_type': 'StorageType',
        'subnet_id': 'SubnetId',
        'vcpu': 'VCPU',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, charge_detail=None, db_engine_version=None, instance_id=None, instance_name=None, instance_status=None, instance_type=None, memory=None, nodes=None, project_name=None, storage_space=None, storage_type=None, subnet_id=None, vcpu=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """InstanceInfoForDescribeBackupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_detail = None
        self._db_engine_version = None
        self._instance_id = None
        self._instance_name = None
        self._instance_status = None
        self._instance_type = None
        self._memory = None
        self._nodes = None
        self._project_name = None
        self._storage_space = None
        self._storage_type = None
        self._subnet_id = None
        self._vcpu = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if charge_detail is not None:
            self.charge_detail = charge_detail
        if db_engine_version is not None:
            self.db_engine_version = db_engine_version
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_status is not None:
            self.instance_status = instance_status
        if instance_type is not None:
            self.instance_type = instance_type
        if memory is not None:
            self.memory = memory
        if nodes is not None:
            self.nodes = nodes
        if project_name is not None:
            self.project_name = project_name
        if storage_space is not None:
            self.storage_space = storage_space
        if storage_type is not None:
            self.storage_type = storage_type
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if vcpu is not None:
            self.vcpu = vcpu
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def charge_detail(self):
        """Gets the charge_detail of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The charge_detail of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: ChargeDetailForDescribeBackupsOutput
        """
        return self._charge_detail

    @charge_detail.setter
    def charge_detail(self, charge_detail):
        """Sets the charge_detail of this InstanceInfoForDescribeBackupsOutput.


        :param charge_detail: The charge_detail of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: ChargeDetailForDescribeBackupsOutput
        """

        self._charge_detail = charge_detail

    @property
    def db_engine_version(self):
        """Gets the db_engine_version of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The db_engine_version of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine_version

    @db_engine_version.setter
    def db_engine_version(self, db_engine_version):
        """Sets the db_engine_version of this InstanceInfoForDescribeBackupsOutput.


        :param db_engine_version: The db_engine_version of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._db_engine_version = db_engine_version

    @property
    def instance_id(self):
        """Gets the instance_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The instance_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this InstanceInfoForDescribeBackupsOutput.


        :param instance_id: The instance_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The instance_name of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this InstanceInfoForDescribeBackupsOutput.


        :param instance_name: The instance_name of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_status(self):
        """Gets the instance_status of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The instance_status of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_status

    @instance_status.setter
    def instance_status(self, instance_status):
        """Sets the instance_status of this InstanceInfoForDescribeBackupsOutput.


        :param instance_status: The instance_status of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._instance_status = instance_status

    @property
    def instance_type(self):
        """Gets the instance_type of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The instance_type of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this InstanceInfoForDescribeBackupsOutput.


        :param instance_type: The instance_type of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def memory(self):
        """Gets the memory of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The memory of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._memory

    @memory.setter
    def memory(self, memory):
        """Sets the memory of this InstanceInfoForDescribeBackupsOutput.


        :param memory: The memory of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: int
        """

        self._memory = memory

    @property
    def nodes(self):
        """Gets the nodes of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The nodes of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: list[NodeForDescribeBackupsOutput]
        """
        return self._nodes

    @nodes.setter
    def nodes(self, nodes):
        """Sets the nodes of this InstanceInfoForDescribeBackupsOutput.


        :param nodes: The nodes of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: list[NodeForDescribeBackupsOutput]
        """

        self._nodes = nodes

    @property
    def project_name(self):
        """Gets the project_name of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The project_name of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this InstanceInfoForDescribeBackupsOutput.


        :param project_name: The project_name of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def storage_space(self):
        """Gets the storage_space of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The storage_space of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this InstanceInfoForDescribeBackupsOutput.


        :param storage_space: The storage_space of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: int
        """

        self._storage_space = storage_space

    @property
    def storage_type(self):
        """Gets the storage_type of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The storage_type of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this InstanceInfoForDescribeBackupsOutput.


        :param storage_type: The storage_type of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._storage_type = storage_type

    @property
    def subnet_id(self):
        """Gets the subnet_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The subnet_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this InstanceInfoForDescribeBackupsOutput.


        :param subnet_id: The subnet_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def vcpu(self):
        """Gets the vcpu of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The vcpu of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._vcpu

    @vcpu.setter
    def vcpu(self, vcpu):
        """Sets the vcpu of this InstanceInfoForDescribeBackupsOutput.


        :param vcpu: The vcpu of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: int
        """

        self._vcpu = vcpu

    @property
    def vpc_id(self):
        """Gets the vpc_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The vpc_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this InstanceInfoForDescribeBackupsOutput.


        :param vpc_id: The vpc_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501


        :return: The zone_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this InstanceInfoForDescribeBackupsOutput.


        :param zone_id: The zone_id of this InstanceInfoForDescribeBackupsOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceInfoForDescribeBackupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceInfoForDescribeBackupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceInfoForDescribeBackupsOutput):
            return True

        return self.to_dict() != other.to_dict()
