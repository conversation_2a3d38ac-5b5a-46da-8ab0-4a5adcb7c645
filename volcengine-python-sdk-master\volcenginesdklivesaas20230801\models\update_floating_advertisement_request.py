# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateFloatingAdvertisementRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'enable_floating': 'bool',
        'home_url': 'str',
        'image_url': 'str',
        'is_close_enable': 'int',
        'is_enable': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'enable_floating': 'EnableFloating',
        'home_url': 'HomeUrl',
        'image_url': 'ImageUrl',
        'is_close_enable': 'IsCloseEnable',
        'is_enable': 'IsEnable'
    }

    def __init__(self, activity_id=None, enable_floating=None, home_url=None, image_url=None, is_close_enable=None, is_enable=None, _configuration=None):  # noqa: E501
        """UpdateFloatingAdvertisementRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._enable_floating = None
        self._home_url = None
        self._image_url = None
        self._is_close_enable = None
        self._is_enable = None
        self.discriminator = None

        self.activity_id = activity_id
        if enable_floating is not None:
            self.enable_floating = enable_floating
        if home_url is not None:
            self.home_url = home_url
        if image_url is not None:
            self.image_url = image_url
        if is_close_enable is not None:
            self.is_close_enable = is_close_enable
        if is_enable is not None:
            self.is_enable = is_enable

    @property
    def activity_id(self):
        """Gets the activity_id of this UpdateFloatingAdvertisementRequest.  # noqa: E501


        :return: The activity_id of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this UpdateFloatingAdvertisementRequest.


        :param activity_id: The activity_id of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def enable_floating(self):
        """Gets the enable_floating of this UpdateFloatingAdvertisementRequest.  # noqa: E501


        :return: The enable_floating of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_floating

    @enable_floating.setter
    def enable_floating(self, enable_floating):
        """Sets the enable_floating of this UpdateFloatingAdvertisementRequest.


        :param enable_floating: The enable_floating of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :type: bool
        """

        self._enable_floating = enable_floating

    @property
    def home_url(self):
        """Gets the home_url of this UpdateFloatingAdvertisementRequest.  # noqa: E501


        :return: The home_url of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :rtype: str
        """
        return self._home_url

    @home_url.setter
    def home_url(self, home_url):
        """Sets the home_url of this UpdateFloatingAdvertisementRequest.


        :param home_url: The home_url of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :type: str
        """

        self._home_url = home_url

    @property
    def image_url(self):
        """Gets the image_url of this UpdateFloatingAdvertisementRequest.  # noqa: E501


        :return: The image_url of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_url

    @image_url.setter
    def image_url(self, image_url):
        """Sets the image_url of this UpdateFloatingAdvertisementRequest.


        :param image_url: The image_url of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :type: str
        """

        self._image_url = image_url

    @property
    def is_close_enable(self):
        """Gets the is_close_enable of this UpdateFloatingAdvertisementRequest.  # noqa: E501


        :return: The is_close_enable of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_close_enable

    @is_close_enable.setter
    def is_close_enable(self, is_close_enable):
        """Sets the is_close_enable of this UpdateFloatingAdvertisementRequest.


        :param is_close_enable: The is_close_enable of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :type: int
        """

        self._is_close_enable = is_close_enable

    @property
    def is_enable(self):
        """Gets the is_enable of this UpdateFloatingAdvertisementRequest.  # noqa: E501


        :return: The is_enable of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_enable

    @is_enable.setter
    def is_enable(self, is_enable):
        """Sets the is_enable of this UpdateFloatingAdvertisementRequest.


        :param is_enable: The is_enable of this UpdateFloatingAdvertisementRequest.  # noqa: E501
        :type: int
        """

        self._is_enable = is_enable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateFloatingAdvertisementRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateFloatingAdvertisementRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateFloatingAdvertisementRequest):
            return True

        return self.to_dict() != other.to_dict()
