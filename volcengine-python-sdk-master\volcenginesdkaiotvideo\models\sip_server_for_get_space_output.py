# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SipServerForGetSpaceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sip_domain': 'str',
        'sip_id': 'str',
        'sip_ip': 'str',
        'sip_port': 'SipPortForGetSpaceOutput'
    }

    attribute_map = {
        'sip_domain': 'SipDomain',
        'sip_id': 'SipId',
        'sip_ip': 'SipIp',
        'sip_port': 'SipPort'
    }

    def __init__(self, sip_domain=None, sip_id=None, sip_ip=None, sip_port=None, _configuration=None):  # noqa: E501
        """SipServerForGetSpaceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._sip_domain = None
        self._sip_id = None
        self._sip_ip = None
        self._sip_port = None
        self.discriminator = None

        if sip_domain is not None:
            self.sip_domain = sip_domain
        if sip_id is not None:
            self.sip_id = sip_id
        if sip_ip is not None:
            self.sip_ip = sip_ip
        if sip_port is not None:
            self.sip_port = sip_port

    @property
    def sip_domain(self):
        """Gets the sip_domain of this SipServerForGetSpaceOutput.  # noqa: E501


        :return: The sip_domain of this SipServerForGetSpaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._sip_domain

    @sip_domain.setter
    def sip_domain(self, sip_domain):
        """Sets the sip_domain of this SipServerForGetSpaceOutput.


        :param sip_domain: The sip_domain of this SipServerForGetSpaceOutput.  # noqa: E501
        :type: str
        """

        self._sip_domain = sip_domain

    @property
    def sip_id(self):
        """Gets the sip_id of this SipServerForGetSpaceOutput.  # noqa: E501


        :return: The sip_id of this SipServerForGetSpaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._sip_id

    @sip_id.setter
    def sip_id(self, sip_id):
        """Sets the sip_id of this SipServerForGetSpaceOutput.


        :param sip_id: The sip_id of this SipServerForGetSpaceOutput.  # noqa: E501
        :type: str
        """

        self._sip_id = sip_id

    @property
    def sip_ip(self):
        """Gets the sip_ip of this SipServerForGetSpaceOutput.  # noqa: E501


        :return: The sip_ip of this SipServerForGetSpaceOutput.  # noqa: E501
        :rtype: str
        """
        return self._sip_ip

    @sip_ip.setter
    def sip_ip(self, sip_ip):
        """Sets the sip_ip of this SipServerForGetSpaceOutput.


        :param sip_ip: The sip_ip of this SipServerForGetSpaceOutput.  # noqa: E501
        :type: str
        """

        self._sip_ip = sip_ip

    @property
    def sip_port(self):
        """Gets the sip_port of this SipServerForGetSpaceOutput.  # noqa: E501


        :return: The sip_port of this SipServerForGetSpaceOutput.  # noqa: E501
        :rtype: SipPortForGetSpaceOutput
        """
        return self._sip_port

    @sip_port.setter
    def sip_port(self, sip_port):
        """Sets the sip_port of this SipServerForGetSpaceOutput.


        :param sip_port: The sip_port of this SipServerForGetSpaceOutput.  # noqa: E501
        :type: SipPortForGetSpaceOutput
        """

        self._sip_port = sip_port

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SipServerForGetSpaceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SipServerForGetSpaceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SipServerForGetSpaceOutput):
            return True

        return self.to_dict() != other.to_dict()
