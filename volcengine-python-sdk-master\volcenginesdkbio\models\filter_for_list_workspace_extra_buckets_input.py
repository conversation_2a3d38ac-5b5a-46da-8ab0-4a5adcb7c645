# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListWorkspaceExtraBucketsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'buckets': 'list[str]',
        'type': 'str'
    }

    attribute_map = {
        'buckets': 'Buckets',
        'type': 'Type'
    }

    def __init__(self, buckets=None, type=None, _configuration=None):  # noqa: E501
        """FilterForListWorkspaceExtraBucketsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._buckets = None
        self._type = None
        self.discriminator = None

        if buckets is not None:
            self.buckets = buckets
        if type is not None:
            self.type = type

    @property
    def buckets(self):
        """Gets the buckets of this FilterForListWorkspaceExtraBucketsInput.  # noqa: E501


        :return: The buckets of this FilterForListWorkspaceExtraBucketsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._buckets

    @buckets.setter
    def buckets(self, buckets):
        """Sets the buckets of this FilterForListWorkspaceExtraBucketsInput.


        :param buckets: The buckets of this FilterForListWorkspaceExtraBucketsInput.  # noqa: E501
        :type: list[str]
        """

        self._buckets = buckets

    @property
    def type(self):
        """Gets the type of this FilterForListWorkspaceExtraBucketsInput.  # noqa: E501


        :return: The type of this FilterForListWorkspaceExtraBucketsInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this FilterForListWorkspaceExtraBucketsInput.


        :param type: The type of this FilterForListWorkspaceExtraBucketsInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["PublicCloud"]  # noqa: E501
        if (self._configuration.client_side_validation and
                type not in allowed_values):
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListWorkspaceExtraBucketsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListWorkspaceExtraBucketsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListWorkspaceExtraBucketsInput):
            return True

        return self.to_dict() != other.to_dict()
