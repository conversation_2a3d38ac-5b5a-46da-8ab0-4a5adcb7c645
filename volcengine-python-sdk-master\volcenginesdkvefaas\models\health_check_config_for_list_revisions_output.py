# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HealthCheckConfigForListRevisionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_health_check': 'bool',
        'failure_threshold': 'int',
        'initial_delay_seconds': 'int',
        'period_seconds': 'int',
        'probe_handler': 'ProbeHandlerForListRevisionsOutput',
        'success_threshold': 'int',
        'timeout_seconds': 'int'
    }

    attribute_map = {
        'enable_health_check': 'EnableHealthCheck',
        'failure_threshold': 'FailureThreshold',
        'initial_delay_seconds': 'InitialDelaySeconds',
        'period_seconds': 'PeriodSeconds',
        'probe_handler': 'ProbeHandler',
        'success_threshold': 'SuccessThreshold',
        'timeout_seconds': 'TimeoutSeconds'
    }

    def __init__(self, enable_health_check=None, failure_threshold=None, initial_delay_seconds=None, period_seconds=None, probe_handler=None, success_threshold=None, timeout_seconds=None, _configuration=None):  # noqa: E501
        """HealthCheckConfigForListRevisionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_health_check = None
        self._failure_threshold = None
        self._initial_delay_seconds = None
        self._period_seconds = None
        self._probe_handler = None
        self._success_threshold = None
        self._timeout_seconds = None
        self.discriminator = None

        if enable_health_check is not None:
            self.enable_health_check = enable_health_check
        if failure_threshold is not None:
            self.failure_threshold = failure_threshold
        if initial_delay_seconds is not None:
            self.initial_delay_seconds = initial_delay_seconds
        if period_seconds is not None:
            self.period_seconds = period_seconds
        if probe_handler is not None:
            self.probe_handler = probe_handler
        if success_threshold is not None:
            self.success_threshold = success_threshold
        if timeout_seconds is not None:
            self.timeout_seconds = timeout_seconds

    @property
    def enable_health_check(self):
        """Gets the enable_health_check of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501


        :return: The enable_health_check of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_health_check

    @enable_health_check.setter
    def enable_health_check(self, enable_health_check):
        """Sets the enable_health_check of this HealthCheckConfigForListRevisionsOutput.


        :param enable_health_check: The enable_health_check of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :type: bool
        """

        self._enable_health_check = enable_health_check

    @property
    def failure_threshold(self):
        """Gets the failure_threshold of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501


        :return: The failure_threshold of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._failure_threshold

    @failure_threshold.setter
    def failure_threshold(self, failure_threshold):
        """Sets the failure_threshold of this HealthCheckConfigForListRevisionsOutput.


        :param failure_threshold: The failure_threshold of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :type: int
        """

        self._failure_threshold = failure_threshold

    @property
    def initial_delay_seconds(self):
        """Gets the initial_delay_seconds of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501


        :return: The initial_delay_seconds of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._initial_delay_seconds

    @initial_delay_seconds.setter
    def initial_delay_seconds(self, initial_delay_seconds):
        """Sets the initial_delay_seconds of this HealthCheckConfigForListRevisionsOutput.


        :param initial_delay_seconds: The initial_delay_seconds of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :type: int
        """

        self._initial_delay_seconds = initial_delay_seconds

    @property
    def period_seconds(self):
        """Gets the period_seconds of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501


        :return: The period_seconds of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._period_seconds

    @period_seconds.setter
    def period_seconds(self, period_seconds):
        """Sets the period_seconds of this HealthCheckConfigForListRevisionsOutput.


        :param period_seconds: The period_seconds of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :type: int
        """

        self._period_seconds = period_seconds

    @property
    def probe_handler(self):
        """Gets the probe_handler of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501


        :return: The probe_handler of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :rtype: ProbeHandlerForListRevisionsOutput
        """
        return self._probe_handler

    @probe_handler.setter
    def probe_handler(self, probe_handler):
        """Sets the probe_handler of this HealthCheckConfigForListRevisionsOutput.


        :param probe_handler: The probe_handler of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :type: ProbeHandlerForListRevisionsOutput
        """

        self._probe_handler = probe_handler

    @property
    def success_threshold(self):
        """Gets the success_threshold of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501


        :return: The success_threshold of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._success_threshold

    @success_threshold.setter
    def success_threshold(self, success_threshold):
        """Sets the success_threshold of this HealthCheckConfigForListRevisionsOutput.


        :param success_threshold: The success_threshold of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :type: int
        """

        self._success_threshold = success_threshold

    @property
    def timeout_seconds(self):
        """Gets the timeout_seconds of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501


        :return: The timeout_seconds of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._timeout_seconds

    @timeout_seconds.setter
    def timeout_seconds(self, timeout_seconds):
        """Sets the timeout_seconds of this HealthCheckConfigForListRevisionsOutput.


        :param timeout_seconds: The timeout_seconds of this HealthCheckConfigForListRevisionsOutput.  # noqa: E501
        :type: int
        """

        self._timeout_seconds = timeout_seconds

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HealthCheckConfigForListRevisionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HealthCheckConfigForListRevisionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HealthCheckConfigForListRevisionsOutput):
            return True

        return self.to_dict() != other.to_dict()
