# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StartStreamResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'pull_urls': 'list[str]',
        'trans_pull_urls': 'list[str]'
    }

    attribute_map = {
        'id': 'ID',
        'pull_urls': 'PullUrls',
        'trans_pull_urls': 'TransPullUrls'
    }

    def __init__(self, id=None, pull_urls=None, trans_pull_urls=None, _configuration=None):  # noqa: E501
        """StartStreamResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._pull_urls = None
        self._trans_pull_urls = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if pull_urls is not None:
            self.pull_urls = pull_urls
        if trans_pull_urls is not None:
            self.trans_pull_urls = trans_pull_urls

    @property
    def id(self):
        """Gets the id of this StartStreamResponse.  # noqa: E501


        :return: The id of this StartStreamResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this StartStreamResponse.


        :param id: The id of this StartStreamResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def pull_urls(self):
        """Gets the pull_urls of this StartStreamResponse.  # noqa: E501


        :return: The pull_urls of this StartStreamResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._pull_urls

    @pull_urls.setter
    def pull_urls(self, pull_urls):
        """Sets the pull_urls of this StartStreamResponse.


        :param pull_urls: The pull_urls of this StartStreamResponse.  # noqa: E501
        :type: list[str]
        """

        self._pull_urls = pull_urls

    @property
    def trans_pull_urls(self):
        """Gets the trans_pull_urls of this StartStreamResponse.  # noqa: E501


        :return: The trans_pull_urls of this StartStreamResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._trans_pull_urls

    @trans_pull_urls.setter
    def trans_pull_urls(self, trans_pull_urls):
        """Sets the trans_pull_urls of this StartStreamResponse.


        :param trans_pull_urls: The trans_pull_urls of this StartStreamResponse.  # noqa: E501
        :type: list[str]
        """

        self._trans_pull_urls = trans_pull_urls

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StartStreamResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StartStreamResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StartStreamResponse):
            return True

        return self.to_dict() != other.to_dict()
