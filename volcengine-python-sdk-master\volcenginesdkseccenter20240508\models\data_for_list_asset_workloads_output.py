# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAssetWorkloadsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'cluster_name': 'str',
        'creation_time': 'int',
        'has_cve_risk': 'bool',
        'id': 'str',
        'k8s_id': 'str',
        'name': 'str',
        'namespace': 'str',
        'pod_count': 'int',
        'replicas': 'str',
        'type': 'str'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'cluster_name': 'ClusterName',
        'creation_time': 'CreationTime',
        'has_cve_risk': 'HasCveRisk',
        'id': 'Id',
        'k8s_id': 'K8sId',
        'name': 'Name',
        'namespace': 'Namespace',
        'pod_count': 'PodCount',
        'replicas': 'Replicas',
        'type': 'Type'
    }

    def __init__(self, cluster_id=None, cluster_name=None, creation_time=None, has_cve_risk=None, id=None, k8s_id=None, name=None, namespace=None, pod_count=None, replicas=None, type=None, _configuration=None):  # noqa: E501
        """DataForListAssetWorkloadsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._cluster_name = None
        self._creation_time = None
        self._has_cve_risk = None
        self._id = None
        self._k8s_id = None
        self._name = None
        self._namespace = None
        self._pod_count = None
        self._replicas = None
        self._type = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if creation_time is not None:
            self.creation_time = creation_time
        if has_cve_risk is not None:
            self.has_cve_risk = has_cve_risk
        if id is not None:
            self.id = id
        if k8s_id is not None:
            self.k8s_id = k8s_id
        if name is not None:
            self.name = name
        if namespace is not None:
            self.namespace = namespace
        if pod_count is not None:
            self.pod_count = pod_count
        if replicas is not None:
            self.replicas = replicas
        if type is not None:
            self.type = type

    @property
    def cluster_id(self):
        """Gets the cluster_id of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The cluster_id of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this DataForListAssetWorkloadsOutput.


        :param cluster_id: The cluster_id of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The cluster_name of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this DataForListAssetWorkloadsOutput.


        :param cluster_name: The cluster_name of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def creation_time(self):
        """Gets the creation_time of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The creation_time of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: int
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this DataForListAssetWorkloadsOutput.


        :param creation_time: The creation_time of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: int
        """

        self._creation_time = creation_time

    @property
    def has_cve_risk(self):
        """Gets the has_cve_risk of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The has_cve_risk of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._has_cve_risk

    @has_cve_risk.setter
    def has_cve_risk(self, has_cve_risk):
        """Sets the has_cve_risk of this DataForListAssetWorkloadsOutput.


        :param has_cve_risk: The has_cve_risk of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: bool
        """

        self._has_cve_risk = has_cve_risk

    @property
    def id(self):
        """Gets the id of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The id of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListAssetWorkloadsOutput.


        :param id: The id of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def k8s_id(self):
        """Gets the k8s_id of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The k8s_id of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: str
        """
        return self._k8s_id

    @k8s_id.setter
    def k8s_id(self, k8s_id):
        """Sets the k8s_id of this DataForListAssetWorkloadsOutput.


        :param k8s_id: The k8s_id of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: str
        """

        self._k8s_id = k8s_id

    @property
    def name(self):
        """Gets the name of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The name of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListAssetWorkloadsOutput.


        :param name: The name of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def namespace(self):
        """Gets the namespace of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The namespace of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this DataForListAssetWorkloadsOutput.


        :param namespace: The namespace of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def pod_count(self):
        """Gets the pod_count of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The pod_count of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: int
        """
        return self._pod_count

    @pod_count.setter
    def pod_count(self, pod_count):
        """Sets the pod_count of this DataForListAssetWorkloadsOutput.


        :param pod_count: The pod_count of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: int
        """

        self._pod_count = pod_count

    @property
    def replicas(self):
        """Gets the replicas of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The replicas of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: str
        """
        return self._replicas

    @replicas.setter
    def replicas(self, replicas):
        """Sets the replicas of this DataForListAssetWorkloadsOutput.


        :param replicas: The replicas of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: str
        """

        self._replicas = replicas

    @property
    def type(self):
        """Gets the type of this DataForListAssetWorkloadsOutput.  # noqa: E501


        :return: The type of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DataForListAssetWorkloadsOutput.


        :param type: The type of this DataForListAssetWorkloadsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAssetWorkloadsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAssetWorkloadsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAssetWorkloadsOutput):
            return True

        return self.to_dict() != other.to_dict()
