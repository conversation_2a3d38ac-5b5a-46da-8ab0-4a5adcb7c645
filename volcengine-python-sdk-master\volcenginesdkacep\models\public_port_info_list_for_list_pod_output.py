# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PublicPortInfoListForListPodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'isp': 'int',
        'public_ip': 'str',
        'public_port': 'int'
    }

    attribute_map = {
        'isp': 'Isp',
        'public_ip': 'PublicIp',
        'public_port': 'PublicPort'
    }

    def __init__(self, isp=None, public_ip=None, public_port=None, _configuration=None):  # noqa: E501
        """PublicPortInfoListForListPodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._isp = None
        self._public_ip = None
        self._public_port = None
        self.discriminator = None

        if isp is not None:
            self.isp = isp
        if public_ip is not None:
            self.public_ip = public_ip
        if public_port is not None:
            self.public_port = public_port

    @property
    def isp(self):
        """Gets the isp of this PublicPortInfoListForListPodOutput.  # noqa: E501


        :return: The isp of this PublicPortInfoListForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this PublicPortInfoListForListPodOutput.


        :param isp: The isp of this PublicPortInfoListForListPodOutput.  # noqa: E501
        :type: int
        """

        self._isp = isp

    @property
    def public_ip(self):
        """Gets the public_ip of this PublicPortInfoListForListPodOutput.  # noqa: E501


        :return: The public_ip of this PublicPortInfoListForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_ip

    @public_ip.setter
    def public_ip(self, public_ip):
        """Sets the public_ip of this PublicPortInfoListForListPodOutput.


        :param public_ip: The public_ip of this PublicPortInfoListForListPodOutput.  # noqa: E501
        :type: str
        """

        self._public_ip = public_ip

    @property
    def public_port(self):
        """Gets the public_port of this PublicPortInfoListForListPodOutput.  # noqa: E501


        :return: The public_port of this PublicPortInfoListForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._public_port

    @public_port.setter
    def public_port(self, public_port):
        """Sets the public_port of this PublicPortInfoListForListPodOutput.


        :param public_port: The public_port of this PublicPortInfoListForListPodOutput.  # noqa: E501
        :type: int
        """

        self._public_port = public_port

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PublicPortInfoListForListPodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PublicPortInfoListForListPodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PublicPortInfoListForListPodOutput):
            return True

        return self.to_dict() != other.to_dict()
