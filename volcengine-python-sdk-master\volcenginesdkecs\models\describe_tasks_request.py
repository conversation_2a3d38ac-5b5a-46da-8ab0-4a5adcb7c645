# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTasksRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_results': 'str',
        'next_token': 'str',
        'resource_id': 'str',
        'resource_ids': 'list[str]',
        'task_ids': 'list[str]'
    }

    attribute_map = {
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'resource_id': 'ResourceId',
        'resource_ids': 'ResourceIds',
        'task_ids': 'TaskIds'
    }

    def __init__(self, max_results=None, next_token=None, resource_id=None, resource_ids=None, task_ids=None, _configuration=None):  # noqa: E501
        """DescribeTasksRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_results = None
        self._next_token = None
        self._resource_id = None
        self._resource_ids = None
        self._task_ids = None
        self.discriminator = None

        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if resource_id is not None:
            self.resource_id = resource_id
        if resource_ids is not None:
            self.resource_ids = resource_ids
        if task_ids is not None:
            self.task_ids = task_ids

    @property
    def max_results(self):
        """Gets the max_results of this DescribeTasksRequest.  # noqa: E501


        :return: The max_results of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeTasksRequest.


        :param max_results: The max_results of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeTasksRequest.  # noqa: E501


        :return: The next_token of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeTasksRequest.


        :param next_token: The next_token of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def resource_id(self):
        """Gets the resource_id of this DescribeTasksRequest.  # noqa: E501


        :return: The resource_id of this DescribeTasksRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this DescribeTasksRequest.


        :param resource_id: The resource_id of this DescribeTasksRequest.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def resource_ids(self):
        """Gets the resource_ids of this DescribeTasksRequest.  # noqa: E501


        :return: The resource_ids of this DescribeTasksRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._resource_ids

    @resource_ids.setter
    def resource_ids(self, resource_ids):
        """Sets the resource_ids of this DescribeTasksRequest.


        :param resource_ids: The resource_ids of this DescribeTasksRequest.  # noqa: E501
        :type: list[str]
        """

        self._resource_ids = resource_ids

    @property
    def task_ids(self):
        """Gets the task_ids of this DescribeTasksRequest.  # noqa: E501


        :return: The task_ids of this DescribeTasksRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._task_ids

    @task_ids.setter
    def task_ids(self, task_ids):
        """Sets the task_ids of this DescribeTasksRequest.


        :param task_ids: The task_ids of this DescribeTasksRequest.  # noqa: E501
        :type: list[str]
        """

        self._task_ids = task_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTasksRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTasksRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTasksRequest):
            return True

        return self.to_dict() != other.to_dict()
