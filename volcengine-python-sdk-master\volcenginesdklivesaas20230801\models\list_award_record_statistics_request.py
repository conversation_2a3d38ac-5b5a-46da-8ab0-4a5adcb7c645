# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAwardRecordStatisticsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'award_id': 'int',
        'page_item_count': 'int',
        'page_no': 'int',
        'search_type': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'award_id': 'AwardId',
        'page_item_count': 'PageItemCount',
        'page_no': 'PageNo',
        'search_type': 'SearchType'
    }

    def __init__(self, activity_id=None, award_id=None, page_item_count=None, page_no=None, search_type=None, _configuration=None):  # noqa: E501
        """ListAwardRecordStatisticsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._award_id = None
        self._page_item_count = None
        self._page_no = None
        self._search_type = None
        self.discriminator = None

        self.activity_id = activity_id
        self.award_id = award_id
        if page_item_count is not None:
            self.page_item_count = page_item_count
        if page_no is not None:
            self.page_no = page_no
        if search_type is not None:
            self.search_type = search_type

    @property
    def activity_id(self):
        """Gets the activity_id of this ListAwardRecordStatisticsRequest.  # noqa: E501


        :return: The activity_id of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ListAwardRecordStatisticsRequest.


        :param activity_id: The activity_id of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def award_id(self):
        """Gets the award_id of this ListAwardRecordStatisticsRequest.  # noqa: E501


        :return: The award_id of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._award_id

    @award_id.setter
    def award_id(self, award_id):
        """Sets the award_id of this ListAwardRecordStatisticsRequest.


        :param award_id: The award_id of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and award_id is None:
            raise ValueError("Invalid value for `award_id`, must not be `None`")  # noqa: E501

        self._award_id = award_id

    @property
    def page_item_count(self):
        """Gets the page_item_count of this ListAwardRecordStatisticsRequest.  # noqa: E501


        :return: The page_item_count of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_item_count

    @page_item_count.setter
    def page_item_count(self, page_item_count):
        """Sets the page_item_count of this ListAwardRecordStatisticsRequest.


        :param page_item_count: The page_item_count of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._page_item_count = page_item_count

    @property
    def page_no(self):
        """Gets the page_no of this ListAwardRecordStatisticsRequest.  # noqa: E501


        :return: The page_no of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_no

    @page_no.setter
    def page_no(self, page_no):
        """Sets the page_no of this ListAwardRecordStatisticsRequest.


        :param page_no: The page_no of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._page_no = page_no

    @property
    def search_type(self):
        """Gets the search_type of this ListAwardRecordStatisticsRequest.  # noqa: E501


        :return: The search_type of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._search_type

    @search_type.setter
    def search_type(self, search_type):
        """Sets the search_type of this ListAwardRecordStatisticsRequest.


        :param search_type: The search_type of this ListAwardRecordStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._search_type = search_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAwardRecordStatisticsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAwardRecordStatisticsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAwardRecordStatisticsRequest):
            return True

        return self.to_dict() != other.to_dict()
