# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SecretForCreateSecretOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creation_date': 'int',
        'description': 'str',
        'encryption_key': 'str',
        'extended_config': 'str',
        'id': 'str',
        'last_rotation_time': 'str',
        'managed': 'bool',
        'project_name': 'str',
        'rotation_interval': 'int',
        'rotation_state': 'str',
        'schedule_delete_time': 'str',
        'schedule_rotation_time': 'str',
        'secret_name': 'str',
        'secret_state': 'str',
        'secret_type': 'str',
        'trn': 'str',
        'uid': 'str',
        'update_date': 'int'
    }

    attribute_map = {
        'creation_date': 'CreationDate',
        'description': 'Description',
        'encryption_key': 'EncryptionKey',
        'extended_config': 'ExtendedConfig',
        'id': 'ID',
        'last_rotation_time': 'LastRotationTime',
        'managed': 'Managed',
        'project_name': 'ProjectName',
        'rotation_interval': 'RotationInterval',
        'rotation_state': 'RotationState',
        'schedule_delete_time': 'ScheduleDeleteTime',
        'schedule_rotation_time': 'ScheduleRotationTime',
        'secret_name': 'SecretName',
        'secret_state': 'SecretState',
        'secret_type': 'SecretType',
        'trn': 'Trn',
        'uid': 'UID',
        'update_date': 'UpdateDate'
    }

    def __init__(self, creation_date=None, description=None, encryption_key=None, extended_config=None, id=None, last_rotation_time=None, managed=None, project_name=None, rotation_interval=None, rotation_state=None, schedule_delete_time=None, schedule_rotation_time=None, secret_name=None, secret_state=None, secret_type=None, trn=None, uid=None, update_date=None, _configuration=None):  # noqa: E501
        """SecretForCreateSecretOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creation_date = None
        self._description = None
        self._encryption_key = None
        self._extended_config = None
        self._id = None
        self._last_rotation_time = None
        self._managed = None
        self._project_name = None
        self._rotation_interval = None
        self._rotation_state = None
        self._schedule_delete_time = None
        self._schedule_rotation_time = None
        self._secret_name = None
        self._secret_state = None
        self._secret_type = None
        self._trn = None
        self._uid = None
        self._update_date = None
        self.discriminator = None

        if creation_date is not None:
            self.creation_date = creation_date
        if description is not None:
            self.description = description
        if encryption_key is not None:
            self.encryption_key = encryption_key
        if extended_config is not None:
            self.extended_config = extended_config
        if id is not None:
            self.id = id
        if last_rotation_time is not None:
            self.last_rotation_time = last_rotation_time
        if managed is not None:
            self.managed = managed
        if project_name is not None:
            self.project_name = project_name
        if rotation_interval is not None:
            self.rotation_interval = rotation_interval
        if rotation_state is not None:
            self.rotation_state = rotation_state
        if schedule_delete_time is not None:
            self.schedule_delete_time = schedule_delete_time
        if schedule_rotation_time is not None:
            self.schedule_rotation_time = schedule_rotation_time
        if secret_name is not None:
            self.secret_name = secret_name
        if secret_state is not None:
            self.secret_state = secret_state
        if secret_type is not None:
            self.secret_type = secret_type
        if trn is not None:
            self.trn = trn
        if uid is not None:
            self.uid = uid
        if update_date is not None:
            self.update_date = update_date

    @property
    def creation_date(self):
        """Gets the creation_date of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The creation_date of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: int
        """
        return self._creation_date

    @creation_date.setter
    def creation_date(self, creation_date):
        """Sets the creation_date of this SecretForCreateSecretOutput.


        :param creation_date: The creation_date of this SecretForCreateSecretOutput.  # noqa: E501
        :type: int
        """

        self._creation_date = creation_date

    @property
    def description(self):
        """Gets the description of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The description of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this SecretForCreateSecretOutput.


        :param description: The description of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def encryption_key(self):
        """Gets the encryption_key of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The encryption_key of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._encryption_key

    @encryption_key.setter
    def encryption_key(self, encryption_key):
        """Sets the encryption_key of this SecretForCreateSecretOutput.


        :param encryption_key: The encryption_key of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._encryption_key = encryption_key

    @property
    def extended_config(self):
        """Gets the extended_config of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The extended_config of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._extended_config

    @extended_config.setter
    def extended_config(self, extended_config):
        """Sets the extended_config of this SecretForCreateSecretOutput.


        :param extended_config: The extended_config of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._extended_config = extended_config

    @property
    def id(self):
        """Gets the id of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The id of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this SecretForCreateSecretOutput.


        :param id: The id of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def last_rotation_time(self):
        """Gets the last_rotation_time of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The last_rotation_time of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._last_rotation_time

    @last_rotation_time.setter
    def last_rotation_time(self, last_rotation_time):
        """Sets the last_rotation_time of this SecretForCreateSecretOutput.


        :param last_rotation_time: The last_rotation_time of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._last_rotation_time = last_rotation_time

    @property
    def managed(self):
        """Gets the managed of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The managed of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: bool
        """
        return self._managed

    @managed.setter
    def managed(self, managed):
        """Sets the managed of this SecretForCreateSecretOutput.


        :param managed: The managed of this SecretForCreateSecretOutput.  # noqa: E501
        :type: bool
        """

        self._managed = managed

    @property
    def project_name(self):
        """Gets the project_name of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The project_name of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this SecretForCreateSecretOutput.


        :param project_name: The project_name of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def rotation_interval(self):
        """Gets the rotation_interval of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The rotation_interval of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: int
        """
        return self._rotation_interval

    @rotation_interval.setter
    def rotation_interval(self, rotation_interval):
        """Sets the rotation_interval of this SecretForCreateSecretOutput.


        :param rotation_interval: The rotation_interval of this SecretForCreateSecretOutput.  # noqa: E501
        :type: int
        """

        self._rotation_interval = rotation_interval

    @property
    def rotation_state(self):
        """Gets the rotation_state of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The rotation_state of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._rotation_state

    @rotation_state.setter
    def rotation_state(self, rotation_state):
        """Sets the rotation_state of this SecretForCreateSecretOutput.


        :param rotation_state: The rotation_state of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._rotation_state = rotation_state

    @property
    def schedule_delete_time(self):
        """Gets the schedule_delete_time of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The schedule_delete_time of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._schedule_delete_time

    @schedule_delete_time.setter
    def schedule_delete_time(self, schedule_delete_time):
        """Sets the schedule_delete_time of this SecretForCreateSecretOutput.


        :param schedule_delete_time: The schedule_delete_time of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._schedule_delete_time = schedule_delete_time

    @property
    def schedule_rotation_time(self):
        """Gets the schedule_rotation_time of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The schedule_rotation_time of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._schedule_rotation_time

    @schedule_rotation_time.setter
    def schedule_rotation_time(self, schedule_rotation_time):
        """Sets the schedule_rotation_time of this SecretForCreateSecretOutput.


        :param schedule_rotation_time: The schedule_rotation_time of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._schedule_rotation_time = schedule_rotation_time

    @property
    def secret_name(self):
        """Gets the secret_name of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The secret_name of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._secret_name

    @secret_name.setter
    def secret_name(self, secret_name):
        """Sets the secret_name of this SecretForCreateSecretOutput.


        :param secret_name: The secret_name of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._secret_name = secret_name

    @property
    def secret_state(self):
        """Gets the secret_state of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The secret_state of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._secret_state

    @secret_state.setter
    def secret_state(self, secret_state):
        """Sets the secret_state of this SecretForCreateSecretOutput.


        :param secret_state: The secret_state of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._secret_state = secret_state

    @property
    def secret_type(self):
        """Gets the secret_type of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The secret_type of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._secret_type

    @secret_type.setter
    def secret_type(self, secret_type):
        """Sets the secret_type of this SecretForCreateSecretOutput.


        :param secret_type: The secret_type of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._secret_type = secret_type

    @property
    def trn(self):
        """Gets the trn of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The trn of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._trn

    @trn.setter
    def trn(self, trn):
        """Sets the trn of this SecretForCreateSecretOutput.


        :param trn: The trn of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._trn = trn

    @property
    def uid(self):
        """Gets the uid of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The uid of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this SecretForCreateSecretOutput.


        :param uid: The uid of this SecretForCreateSecretOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    @property
    def update_date(self):
        """Gets the update_date of this SecretForCreateSecretOutput.  # noqa: E501


        :return: The update_date of this SecretForCreateSecretOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_date

    @update_date.setter
    def update_date(self, update_date):
        """Sets the update_date of this SecretForCreateSecretOutput.


        :param update_date: The update_date of this SecretForCreateSecretOutput.  # noqa: E501
        :type: int
        """

        self._update_date = update_date

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SecretForCreateSecretOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SecretForCreateSecretOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SecretForCreateSecretOutput):
            return True

        return self.to_dict() != other.to_dict()
