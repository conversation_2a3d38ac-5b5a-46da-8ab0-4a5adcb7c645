# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetMediaStoragePayDataResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_used_doc_capacity': 'float',
        'account_used_vid_capacity': 'float',
        'all_used_doc_capacity': 'float',
        'all_used_vid_capacity': 'float',
        'post_pay_media_capacity_result': 'float',
        'pre_pay_media_capacity_result': 'float',
        'remain_capacity': 'float',
        'total_capacity': 'float'
    }

    attribute_map = {
        'account_used_doc_capacity': 'AccountUsedDocCapacity',
        'account_used_vid_capacity': 'AccountUsedVidCapacity',
        'all_used_doc_capacity': 'AllUsedDocCapacity',
        'all_used_vid_capacity': 'AllUsedVidCapacity',
        'post_pay_media_capacity_result': 'PostPayMediaCapacityResult',
        'pre_pay_media_capacity_result': 'PrePayMediaCapacityResult',
        'remain_capacity': 'RemainCapacity',
        'total_capacity': 'TotalCapacity'
    }

    def __init__(self, account_used_doc_capacity=None, account_used_vid_capacity=None, all_used_doc_capacity=None, all_used_vid_capacity=None, post_pay_media_capacity_result=None, pre_pay_media_capacity_result=None, remain_capacity=None, total_capacity=None, _configuration=None):  # noqa: E501
        """GetMediaStoragePayDataResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_used_doc_capacity = None
        self._account_used_vid_capacity = None
        self._all_used_doc_capacity = None
        self._all_used_vid_capacity = None
        self._post_pay_media_capacity_result = None
        self._pre_pay_media_capacity_result = None
        self._remain_capacity = None
        self._total_capacity = None
        self.discriminator = None

        if account_used_doc_capacity is not None:
            self.account_used_doc_capacity = account_used_doc_capacity
        if account_used_vid_capacity is not None:
            self.account_used_vid_capacity = account_used_vid_capacity
        if all_used_doc_capacity is not None:
            self.all_used_doc_capacity = all_used_doc_capacity
        if all_used_vid_capacity is not None:
            self.all_used_vid_capacity = all_used_vid_capacity
        if post_pay_media_capacity_result is not None:
            self.post_pay_media_capacity_result = post_pay_media_capacity_result
        if pre_pay_media_capacity_result is not None:
            self.pre_pay_media_capacity_result = pre_pay_media_capacity_result
        if remain_capacity is not None:
            self.remain_capacity = remain_capacity
        if total_capacity is not None:
            self.total_capacity = total_capacity

    @property
    def account_used_doc_capacity(self):
        """Gets the account_used_doc_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501


        :return: The account_used_doc_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._account_used_doc_capacity

    @account_used_doc_capacity.setter
    def account_used_doc_capacity(self, account_used_doc_capacity):
        """Sets the account_used_doc_capacity of this GetMediaStoragePayDataResponse.


        :param account_used_doc_capacity: The account_used_doc_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :type: float
        """

        self._account_used_doc_capacity = account_used_doc_capacity

    @property
    def account_used_vid_capacity(self):
        """Gets the account_used_vid_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501


        :return: The account_used_vid_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._account_used_vid_capacity

    @account_used_vid_capacity.setter
    def account_used_vid_capacity(self, account_used_vid_capacity):
        """Sets the account_used_vid_capacity of this GetMediaStoragePayDataResponse.


        :param account_used_vid_capacity: The account_used_vid_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :type: float
        """

        self._account_used_vid_capacity = account_used_vid_capacity

    @property
    def all_used_doc_capacity(self):
        """Gets the all_used_doc_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501


        :return: The all_used_doc_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._all_used_doc_capacity

    @all_used_doc_capacity.setter
    def all_used_doc_capacity(self, all_used_doc_capacity):
        """Sets the all_used_doc_capacity of this GetMediaStoragePayDataResponse.


        :param all_used_doc_capacity: The all_used_doc_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :type: float
        """

        self._all_used_doc_capacity = all_used_doc_capacity

    @property
    def all_used_vid_capacity(self):
        """Gets the all_used_vid_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501


        :return: The all_used_vid_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._all_used_vid_capacity

    @all_used_vid_capacity.setter
    def all_used_vid_capacity(self, all_used_vid_capacity):
        """Sets the all_used_vid_capacity of this GetMediaStoragePayDataResponse.


        :param all_used_vid_capacity: The all_used_vid_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :type: float
        """

        self._all_used_vid_capacity = all_used_vid_capacity

    @property
    def post_pay_media_capacity_result(self):
        """Gets the post_pay_media_capacity_result of this GetMediaStoragePayDataResponse.  # noqa: E501


        :return: The post_pay_media_capacity_result of this GetMediaStoragePayDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._post_pay_media_capacity_result

    @post_pay_media_capacity_result.setter
    def post_pay_media_capacity_result(self, post_pay_media_capacity_result):
        """Sets the post_pay_media_capacity_result of this GetMediaStoragePayDataResponse.


        :param post_pay_media_capacity_result: The post_pay_media_capacity_result of this GetMediaStoragePayDataResponse.  # noqa: E501
        :type: float
        """

        self._post_pay_media_capacity_result = post_pay_media_capacity_result

    @property
    def pre_pay_media_capacity_result(self):
        """Gets the pre_pay_media_capacity_result of this GetMediaStoragePayDataResponse.  # noqa: E501


        :return: The pre_pay_media_capacity_result of this GetMediaStoragePayDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._pre_pay_media_capacity_result

    @pre_pay_media_capacity_result.setter
    def pre_pay_media_capacity_result(self, pre_pay_media_capacity_result):
        """Sets the pre_pay_media_capacity_result of this GetMediaStoragePayDataResponse.


        :param pre_pay_media_capacity_result: The pre_pay_media_capacity_result of this GetMediaStoragePayDataResponse.  # noqa: E501
        :type: float
        """

        self._pre_pay_media_capacity_result = pre_pay_media_capacity_result

    @property
    def remain_capacity(self):
        """Gets the remain_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501


        :return: The remain_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._remain_capacity

    @remain_capacity.setter
    def remain_capacity(self, remain_capacity):
        """Sets the remain_capacity of this GetMediaStoragePayDataResponse.


        :param remain_capacity: The remain_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :type: float
        """

        self._remain_capacity = remain_capacity

    @property
    def total_capacity(self):
        """Gets the total_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501


        :return: The total_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :rtype: float
        """
        return self._total_capacity

    @total_capacity.setter
    def total_capacity(self, total_capacity):
        """Sets the total_capacity of this GetMediaStoragePayDataResponse.


        :param total_capacity: The total_capacity of this GetMediaStoragePayDataResponse.  # noqa: E501
        :type: float
        """

        self._total_capacity = total_capacity

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetMediaStoragePayDataResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetMediaStoragePayDataResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetMediaStoragePayDataResponse):
            return True

        return self.to_dict() != other.to_dict()
