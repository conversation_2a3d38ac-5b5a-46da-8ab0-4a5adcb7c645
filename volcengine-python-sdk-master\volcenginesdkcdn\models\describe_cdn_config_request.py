# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCdnConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'domain': 'str',
        'lock_info': 'bool'
    }

    attribute_map = {
        'domain': 'Domain',
        'lock_info': 'LockInfo'
    }

    def __init__(self, domain=None, lock_info=None, _configuration=None):  # noqa: E501
        """DescribeCdnConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain = None
        self._lock_info = None
        self.discriminator = None

        self.domain = domain
        if lock_info is not None:
            self.lock_info = lock_info

    @property
    def domain(self):
        """Gets the domain of this DescribeCdnConfigRequest.  # noqa: E501


        :return: The domain of this DescribeCdnConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this DescribeCdnConfigRequest.


        :param domain: The domain of this DescribeCdnConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and domain is None:
            raise ValueError("Invalid value for `domain`, must not be `None`")  # noqa: E501

        self._domain = domain

    @property
    def lock_info(self):
        """Gets the lock_info of this DescribeCdnConfigRequest.  # noqa: E501


        :return: The lock_info of this DescribeCdnConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._lock_info

    @lock_info.setter
    def lock_info(self, lock_info):
        """Sets the lock_info of this DescribeCdnConfigRequest.


        :param lock_info: The lock_info of this DescribeCdnConfigRequest.  # noqa: E501
        :type: bool
        """

        self._lock_info = lock_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCdnConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCdnConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCdnConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
