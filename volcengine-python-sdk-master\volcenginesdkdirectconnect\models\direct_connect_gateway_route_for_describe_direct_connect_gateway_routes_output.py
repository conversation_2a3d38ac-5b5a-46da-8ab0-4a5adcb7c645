# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'creation_time': 'str',
        'destination_cidr_block': 'str',
        'direct_connect_gateway_id': 'str',
        'direct_connect_gateway_route_id': 'str',
        'next_hop_id': 'str',
        'next_hop_type': 'str',
        'route_type': 'str',
        'status': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'creation_time': 'CreationTime',
        'destination_cidr_block': 'DestinationCidrBlock',
        'direct_connect_gateway_id': 'DirectConnectGatewayId',
        'direct_connect_gateway_route_id': 'DirectConnectGatewayRouteId',
        'next_hop_id': 'NextHopId',
        'next_hop_type': 'NextHopType',
        'route_type': 'RouteType',
        'status': 'Status'
    }

    def __init__(self, account_id=None, creation_time=None, destination_cidr_block=None, direct_connect_gateway_id=None, direct_connect_gateway_route_id=None, next_hop_id=None, next_hop_type=None, route_type=None, status=None, _configuration=None):  # noqa: E501
        """DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._creation_time = None
        self._destination_cidr_block = None
        self._direct_connect_gateway_id = None
        self._direct_connect_gateway_route_id = None
        self._next_hop_id = None
        self._next_hop_type = None
        self._route_type = None
        self._status = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if creation_time is not None:
            self.creation_time = creation_time
        if destination_cidr_block is not None:
            self.destination_cidr_block = destination_cidr_block
        if direct_connect_gateway_id is not None:
            self.direct_connect_gateway_id = direct_connect_gateway_id
        if direct_connect_gateway_route_id is not None:
            self.direct_connect_gateway_route_id = direct_connect_gateway_route_id
        if next_hop_id is not None:
            self.next_hop_id = next_hop_id
        if next_hop_type is not None:
            self.next_hop_type = next_hop_type
        if route_type is not None:
            self.route_type = route_type
        if status is not None:
            self.status = status

    @property
    def account_id(self):
        """Gets the account_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501


        :return: The account_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.


        :param account_id: The account_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def creation_time(self):
        """Gets the creation_time of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501


        :return: The creation_time of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.


        :param creation_time: The creation_time of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def destination_cidr_block(self):
        """Gets the destination_cidr_block of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501


        :return: The destination_cidr_block of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination_cidr_block

    @destination_cidr_block.setter
    def destination_cidr_block(self, destination_cidr_block):
        """Sets the destination_cidr_block of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.


        :param destination_cidr_block: The destination_cidr_block of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :type: str
        """

        self._destination_cidr_block = destination_cidr_block

    @property
    def direct_connect_gateway_id(self):
        """Gets the direct_connect_gateway_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501


        :return: The direct_connect_gateway_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_gateway_id

    @direct_connect_gateway_id.setter
    def direct_connect_gateway_id(self, direct_connect_gateway_id):
        """Sets the direct_connect_gateway_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.


        :param direct_connect_gateway_id: The direct_connect_gateway_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :type: str
        """

        self._direct_connect_gateway_id = direct_connect_gateway_id

    @property
    def direct_connect_gateway_route_id(self):
        """Gets the direct_connect_gateway_route_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501


        :return: The direct_connect_gateway_route_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_gateway_route_id

    @direct_connect_gateway_route_id.setter
    def direct_connect_gateway_route_id(self, direct_connect_gateway_route_id):
        """Sets the direct_connect_gateway_route_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.


        :param direct_connect_gateway_route_id: The direct_connect_gateway_route_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :type: str
        """

        self._direct_connect_gateway_route_id = direct_connect_gateway_route_id

    @property
    def next_hop_id(self):
        """Gets the next_hop_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501


        :return: The next_hop_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :rtype: str
        """
        return self._next_hop_id

    @next_hop_id.setter
    def next_hop_id(self, next_hop_id):
        """Sets the next_hop_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.


        :param next_hop_id: The next_hop_id of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :type: str
        """

        self._next_hop_id = next_hop_id

    @property
    def next_hop_type(self):
        """Gets the next_hop_type of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501


        :return: The next_hop_type of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :rtype: str
        """
        return self._next_hop_type

    @next_hop_type.setter
    def next_hop_type(self, next_hop_type):
        """Sets the next_hop_type of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.


        :param next_hop_type: The next_hop_type of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :type: str
        """

        self._next_hop_type = next_hop_type

    @property
    def route_type(self):
        """Gets the route_type of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501


        :return: The route_type of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :rtype: str
        """
        return self._route_type

    @route_type.setter
    def route_type(self, route_type):
        """Sets the route_type of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.


        :param route_type: The route_type of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :type: str
        """

        self._route_type = route_type

    @property
    def status(self):
        """Gets the status of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501


        :return: The status of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.


        :param status: The status of this DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DirectConnectGatewayRouteForDescribeDirectConnectGatewayRoutesOutput):
            return True

        return self.to_dict() != other.to_dict()
