# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListNodesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'fqdns': 'list[str]',
        'max_results': 'int',
        'next_token': 'str',
        'node_group_ids': 'list[str]',
        'node_ids': 'list[str]',
        'node_names': 'list[str]',
        'node_states': 'list[str]',
        'private_ips': 'list[str]',
        'public_ips': 'list[str]'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'fqdns': 'Fqdns',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'node_group_ids': 'NodeGroupIds',
        'node_ids': 'NodeIds',
        'node_names': 'NodeNames',
        'node_states': 'NodeStates',
        'private_ips': 'PrivateIps',
        'public_ips': 'PublicIps'
    }

    def __init__(self, cluster_id=None, fqdns=None, max_results=None, next_token=None, node_group_ids=None, node_ids=None, node_names=None, node_states=None, private_ips=None, public_ips=None, _configuration=None):  # noqa: E501
        """ListNodesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._fqdns = None
        self._max_results = None
        self._next_token = None
        self._node_group_ids = None
        self._node_ids = None
        self._node_names = None
        self._node_states = None
        self._private_ips = None
        self._public_ips = None
        self.discriminator = None

        self.cluster_id = cluster_id
        if fqdns is not None:
            self.fqdns = fqdns
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if node_group_ids is not None:
            self.node_group_ids = node_group_ids
        if node_ids is not None:
            self.node_ids = node_ids
        if node_names is not None:
            self.node_names = node_names
        if node_states is not None:
            self.node_states = node_states
        if private_ips is not None:
            self.private_ips = private_ips
        if public_ips is not None:
            self.public_ips = public_ips

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListNodesRequest.  # noqa: E501


        :return: The cluster_id of this ListNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListNodesRequest.


        :param cluster_id: The cluster_id of this ListNodesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def fqdns(self):
        """Gets the fqdns of this ListNodesRequest.  # noqa: E501


        :return: The fqdns of this ListNodesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._fqdns

    @fqdns.setter
    def fqdns(self, fqdns):
        """Sets the fqdns of this ListNodesRequest.


        :param fqdns: The fqdns of this ListNodesRequest.  # noqa: E501
        :type: list[str]
        """

        self._fqdns = fqdns

    @property
    def max_results(self):
        """Gets the max_results of this ListNodesRequest.  # noqa: E501


        :return: The max_results of this ListNodesRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListNodesRequest.


        :param max_results: The max_results of this ListNodesRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                max_results is not None and max_results > 100):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                max_results is not None and max_results < 1):  # noqa: E501
            raise ValueError("Invalid value for `max_results`, must be a value greater than or equal to `1`")  # noqa: E501

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListNodesRequest.  # noqa: E501


        :return: The next_token of this ListNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListNodesRequest.


        :param next_token: The next_token of this ListNodesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def node_group_ids(self):
        """Gets the node_group_ids of this ListNodesRequest.  # noqa: E501


        :return: The node_group_ids of this ListNodesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_group_ids

    @node_group_ids.setter
    def node_group_ids(self, node_group_ids):
        """Sets the node_group_ids of this ListNodesRequest.


        :param node_group_ids: The node_group_ids of this ListNodesRequest.  # noqa: E501
        :type: list[str]
        """

        self._node_group_ids = node_group_ids

    @property
    def node_ids(self):
        """Gets the node_ids of this ListNodesRequest.  # noqa: E501


        :return: The node_ids of this ListNodesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_ids

    @node_ids.setter
    def node_ids(self, node_ids):
        """Sets the node_ids of this ListNodesRequest.


        :param node_ids: The node_ids of this ListNodesRequest.  # noqa: E501
        :type: list[str]
        """

        self._node_ids = node_ids

    @property
    def node_names(self):
        """Gets the node_names of this ListNodesRequest.  # noqa: E501


        :return: The node_names of this ListNodesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_names

    @node_names.setter
    def node_names(self, node_names):
        """Sets the node_names of this ListNodesRequest.


        :param node_names: The node_names of this ListNodesRequest.  # noqa: E501
        :type: list[str]
        """

        self._node_names = node_names

    @property
    def node_states(self):
        """Gets the node_states of this ListNodesRequest.  # noqa: E501


        :return: The node_states of this ListNodesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_states

    @node_states.setter
    def node_states(self, node_states):
        """Sets the node_states of this ListNodesRequest.


        :param node_states: The node_states of this ListNodesRequest.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["UNKNOWN", "CREATING", "RUNNING", "STOPPING", "STOPPED", "REBOOTING", "REBUILDING", "DELETED"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(node_states).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `node_states` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(node_states) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._node_states = node_states

    @property
    def private_ips(self):
        """Gets the private_ips of this ListNodesRequest.  # noqa: E501


        :return: The private_ips of this ListNodesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._private_ips

    @private_ips.setter
    def private_ips(self, private_ips):
        """Sets the private_ips of this ListNodesRequest.


        :param private_ips: The private_ips of this ListNodesRequest.  # noqa: E501
        :type: list[str]
        """

        self._private_ips = private_ips

    @property
    def public_ips(self):
        """Gets the public_ips of this ListNodesRequest.  # noqa: E501


        :return: The public_ips of this ListNodesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._public_ips

    @public_ips.setter
    def public_ips(self, public_ips):
        """Sets the public_ips of this ListNodesRequest.


        :param public_ips: The public_ips of this ListNodesRequest.  # noqa: E501
        :type: list[str]
        """

        self._public_ips = public_ips

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListNodesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListNodesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListNodesRequest):
            return True

        return self.to_dict() != other.to_dict()
