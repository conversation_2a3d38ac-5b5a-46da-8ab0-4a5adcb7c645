# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataEntityForListSubmissionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'name': 'str',
        'row_ids': 'list[str]'
    }

    attribute_map = {
        'id': 'ID',
        'name': 'Name',
        'row_ids': 'RowIDs'
    }

    def __init__(self, id=None, name=None, row_ids=None, _configuration=None):  # noqa: E501
        """DataEntityForListSubmissionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._name = None
        self._row_ids = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if row_ids is not None:
            self.row_ids = row_ids

    @property
    def id(self):
        """Gets the id of this DataEntityForListSubmissionsOutput.  # noqa: E501


        :return: The id of this DataEntityForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataEntityForListSubmissionsOutput.


        :param id: The id of this DataEntityForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this DataEntityForListSubmissionsOutput.  # noqa: E501


        :return: The name of this DataEntityForListSubmissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataEntityForListSubmissionsOutput.


        :param name: The name of this DataEntityForListSubmissionsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def row_ids(self):
        """Gets the row_ids of this DataEntityForListSubmissionsOutput.  # noqa: E501


        :return: The row_ids of this DataEntityForListSubmissionsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._row_ids

    @row_ids.setter
    def row_ids(self, row_ids):
        """Sets the row_ids of this DataEntityForListSubmissionsOutput.


        :param row_ids: The row_ids of this DataEntityForListSubmissionsOutput.  # noqa: E501
        :type: list[str]
        """

        self._row_ids = row_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataEntityForListSubmissionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataEntityForListSubmissionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataEntityForListSubmissionsOutput):
            return True

        return self.to_dict() != other.to_dict()
