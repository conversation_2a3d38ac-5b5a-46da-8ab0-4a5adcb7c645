# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ParameterDefinitionForRunCommandInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'decimal_precision': 'str',
        'default_value': 'str',
        'max_length': 'str',
        'max_value': 'str',
        'min_length': 'str',
        'min_value': 'str',
        'name': 'str',
        'required': 'bool',
        'type': 'str'
    }

    attribute_map = {
        'decimal_precision': 'DecimalPrecision',
        'default_value': 'DefaultValue',
        'max_length': 'MaxLength',
        'max_value': 'MaxValue',
        'min_length': 'MinLength',
        'min_value': 'MinValue',
        'name': 'Name',
        'required': 'Required',
        'type': 'Type'
    }

    def __init__(self, decimal_precision=None, default_value=None, max_length=None, max_value=None, min_length=None, min_value=None, name=None, required=None, type=None, _configuration=None):  # noqa: E501
        """ParameterDefinitionForRunCommandInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._decimal_precision = None
        self._default_value = None
        self._max_length = None
        self._max_value = None
        self._min_length = None
        self._min_value = None
        self._name = None
        self._required = None
        self._type = None
        self.discriminator = None

        if decimal_precision is not None:
            self.decimal_precision = decimal_precision
        if default_value is not None:
            self.default_value = default_value
        if max_length is not None:
            self.max_length = max_length
        if max_value is not None:
            self.max_value = max_value
        if min_length is not None:
            self.min_length = min_length
        if min_value is not None:
            self.min_value = min_value
        if name is not None:
            self.name = name
        if required is not None:
            self.required = required
        if type is not None:
            self.type = type

    @property
    def decimal_precision(self):
        """Gets the decimal_precision of this ParameterDefinitionForRunCommandInput.  # noqa: E501


        :return: The decimal_precision of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :rtype: str
        """
        return self._decimal_precision

    @decimal_precision.setter
    def decimal_precision(self, decimal_precision):
        """Sets the decimal_precision of this ParameterDefinitionForRunCommandInput.


        :param decimal_precision: The decimal_precision of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :type: str
        """

        self._decimal_precision = decimal_precision

    @property
    def default_value(self):
        """Gets the default_value of this ParameterDefinitionForRunCommandInput.  # noqa: E501


        :return: The default_value of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :rtype: str
        """
        return self._default_value

    @default_value.setter
    def default_value(self, default_value):
        """Sets the default_value of this ParameterDefinitionForRunCommandInput.


        :param default_value: The default_value of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :type: str
        """

        self._default_value = default_value

    @property
    def max_length(self):
        """Gets the max_length of this ParameterDefinitionForRunCommandInput.  # noqa: E501


        :return: The max_length of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :rtype: str
        """
        return self._max_length

    @max_length.setter
    def max_length(self, max_length):
        """Sets the max_length of this ParameterDefinitionForRunCommandInput.


        :param max_length: The max_length of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :type: str
        """

        self._max_length = max_length

    @property
    def max_value(self):
        """Gets the max_value of this ParameterDefinitionForRunCommandInput.  # noqa: E501


        :return: The max_value of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :rtype: str
        """
        return self._max_value

    @max_value.setter
    def max_value(self, max_value):
        """Sets the max_value of this ParameterDefinitionForRunCommandInput.


        :param max_value: The max_value of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :type: str
        """

        self._max_value = max_value

    @property
    def min_length(self):
        """Gets the min_length of this ParameterDefinitionForRunCommandInput.  # noqa: E501


        :return: The min_length of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :rtype: str
        """
        return self._min_length

    @min_length.setter
    def min_length(self, min_length):
        """Sets the min_length of this ParameterDefinitionForRunCommandInput.


        :param min_length: The min_length of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :type: str
        """

        self._min_length = min_length

    @property
    def min_value(self):
        """Gets the min_value of this ParameterDefinitionForRunCommandInput.  # noqa: E501


        :return: The min_value of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :rtype: str
        """
        return self._min_value

    @min_value.setter
    def min_value(self, min_value):
        """Sets the min_value of this ParameterDefinitionForRunCommandInput.


        :param min_value: The min_value of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :type: str
        """

        self._min_value = min_value

    @property
    def name(self):
        """Gets the name of this ParameterDefinitionForRunCommandInput.  # noqa: E501


        :return: The name of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ParameterDefinitionForRunCommandInput.


        :param name: The name of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def required(self):
        """Gets the required of this ParameterDefinitionForRunCommandInput.  # noqa: E501


        :return: The required of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :rtype: bool
        """
        return self._required

    @required.setter
    def required(self, required):
        """Sets the required of this ParameterDefinitionForRunCommandInput.


        :param required: The required of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :type: bool
        """

        self._required = required

    @property
    def type(self):
        """Gets the type of this ParameterDefinitionForRunCommandInput.  # noqa: E501


        :return: The type of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ParameterDefinitionForRunCommandInput.


        :param type: The type of this ParameterDefinitionForRunCommandInput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ParameterDefinitionForRunCommandInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ParameterDefinitionForRunCommandInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ParameterDefinitionForRunCommandInput):
            return True

        return self.to_dict() != other.to_dict()
