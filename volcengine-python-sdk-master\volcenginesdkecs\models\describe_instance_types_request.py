# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeInstanceTypesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'image_id': 'str',
        'instance_type_ids': 'list[str]',
        'instance_types': 'list[str]',
        'max_results': 'int',
        'next_token': 'str'
    }

    attribute_map = {
        'image_id': 'ImageId',
        'instance_type_ids': 'InstanceTypeIds',
        'instance_types': 'InstanceTypes',
        'max_results': 'MaxResults',
        'next_token': 'NextToken'
    }

    def __init__(self, image_id=None, instance_type_ids=None, instance_types=None, max_results=None, next_token=None, _configuration=None):  # noqa: E501
        """DescribeInstanceTypesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._image_id = None
        self._instance_type_ids = None
        self._instance_types = None
        self._max_results = None
        self._next_token = None
        self.discriminator = None

        if image_id is not None:
            self.image_id = image_id
        if instance_type_ids is not None:
            self.instance_type_ids = instance_type_ids
        if instance_types is not None:
            self.instance_types = instance_types
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token

    @property
    def image_id(self):
        """Gets the image_id of this DescribeInstanceTypesRequest.  # noqa: E501


        :return: The image_id of this DescribeInstanceTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this DescribeInstanceTypesRequest.


        :param image_id: The image_id of this DescribeInstanceTypesRequest.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def instance_type_ids(self):
        """Gets the instance_type_ids of this DescribeInstanceTypesRequest.  # noqa: E501


        :return: The instance_type_ids of this DescribeInstanceTypesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_type_ids

    @instance_type_ids.setter
    def instance_type_ids(self, instance_type_ids):
        """Sets the instance_type_ids of this DescribeInstanceTypesRequest.


        :param instance_type_ids: The instance_type_ids of this DescribeInstanceTypesRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_type_ids = instance_type_ids

    @property
    def instance_types(self):
        """Gets the instance_types of this DescribeInstanceTypesRequest.  # noqa: E501


        :return: The instance_types of this DescribeInstanceTypesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_types

    @instance_types.setter
    def instance_types(self, instance_types):
        """Sets the instance_types of this DescribeInstanceTypesRequest.


        :param instance_types: The instance_types of this DescribeInstanceTypesRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_types = instance_types

    @property
    def max_results(self):
        """Gets the max_results of this DescribeInstanceTypesRequest.  # noqa: E501


        :return: The max_results of this DescribeInstanceTypesRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeInstanceTypesRequest.


        :param max_results: The max_results of this DescribeInstanceTypesRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeInstanceTypesRequest.  # noqa: E501


        :return: The next_token of this DescribeInstanceTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeInstanceTypesRequest.


        :param next_token: The next_token of this DescribeInstanceTypesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeInstanceTypesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeInstanceTypesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeInstanceTypesRequest):
            return True

        return self.to_dict() != other.to_dict()
