# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RollingUpdateStrategyForUpdateDeploymentInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_surge': 'int',
        'max_unavailable': 'int'
    }

    attribute_map = {
        'max_surge': 'MaxSurge',
        'max_unavailable': 'MaxUnavailable'
    }

    def __init__(self, max_surge=None, max_unavailable=None, _configuration=None):  # noqa: E501
        """RollingUpdateStrategyForUpdateDeploymentInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_surge = None
        self._max_unavailable = None
        self.discriminator = None

        if max_surge is not None:
            self.max_surge = max_surge
        if max_unavailable is not None:
            self.max_unavailable = max_unavailable

    @property
    def max_surge(self):
        """Gets the max_surge of this RollingUpdateStrategyForUpdateDeploymentInput.  # noqa: E501


        :return: The max_surge of this RollingUpdateStrategyForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._max_surge

    @max_surge.setter
    def max_surge(self, max_surge):
        """Sets the max_surge of this RollingUpdateStrategyForUpdateDeploymentInput.


        :param max_surge: The max_surge of this RollingUpdateStrategyForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._max_surge = max_surge

    @property
    def max_unavailable(self):
        """Gets the max_unavailable of this RollingUpdateStrategyForUpdateDeploymentInput.  # noqa: E501


        :return: The max_unavailable of this RollingUpdateStrategyForUpdateDeploymentInput.  # noqa: E501
        :rtype: int
        """
        return self._max_unavailable

    @max_unavailable.setter
    def max_unavailable(self, max_unavailable):
        """Sets the max_unavailable of this RollingUpdateStrategyForUpdateDeploymentInput.


        :param max_unavailable: The max_unavailable of this RollingUpdateStrategyForUpdateDeploymentInput.  # noqa: E501
        :type: int
        """

        self._max_unavailable = max_unavailable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RollingUpdateStrategyForUpdateDeploymentInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RollingUpdateStrategyForUpdateDeploymentInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RollingUpdateStrategyForUpdateDeploymentInput):
            return True

        return self.to_dict() != other.to_dict()
