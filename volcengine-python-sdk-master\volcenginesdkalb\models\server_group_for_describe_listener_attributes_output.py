# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ServerGroupForDescribeListenerAttributesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'server_group_id': 'str',
        'server_group_name': 'str'
    }

    attribute_map = {
        'server_group_id': 'ServerGroupId',
        'server_group_name': 'ServerGroupName'
    }

    def __init__(self, server_group_id=None, server_group_name=None, _configuration=None):  # noqa: E501
        """ServerGroupForDescribeListenerAttributesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._server_group_id = None
        self._server_group_name = None
        self.discriminator = None

        if server_group_id is not None:
            self.server_group_id = server_group_id
        if server_group_name is not None:
            self.server_group_name = server_group_name

    @property
    def server_group_id(self):
        """Gets the server_group_id of this ServerGroupForDescribeListenerAttributesOutput.  # noqa: E501


        :return: The server_group_id of this ServerGroupForDescribeListenerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this ServerGroupForDescribeListenerAttributesOutput.


        :param server_group_id: The server_group_id of this ServerGroupForDescribeListenerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    @property
    def server_group_name(self):
        """Gets the server_group_name of this ServerGroupForDescribeListenerAttributesOutput.  # noqa: E501


        :return: The server_group_name of this ServerGroupForDescribeListenerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_group_name

    @server_group_name.setter
    def server_group_name(self, server_group_name):
        """Sets the server_group_name of this ServerGroupForDescribeListenerAttributesOutput.


        :param server_group_name: The server_group_name of this ServerGroupForDescribeListenerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._server_group_name = server_group_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ServerGroupForDescribeListenerAttributesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ServerGroupForDescribeListenerAttributesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ServerGroupForDescribeListenerAttributesOutput):
            return True

        return self.to_dict() != other.to_dict()
