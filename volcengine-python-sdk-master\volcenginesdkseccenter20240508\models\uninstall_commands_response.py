# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UninstallCommandsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'not_volc_linux': 'str',
        'volc_linux': 'str',
        'windows': 'str'
    }

    attribute_map = {
        'not_volc_linux': 'NotVolcLinux',
        'volc_linux': 'VolcLinux',
        'windows': 'Windows'
    }

    def __init__(self, not_volc_linux=None, volc_linux=None, windows=None, _configuration=None):  # noqa: E501
        """UninstallCommandsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._not_volc_linux = None
        self._volc_linux = None
        self._windows = None
        self.discriminator = None

        if not_volc_linux is not None:
            self.not_volc_linux = not_volc_linux
        if volc_linux is not None:
            self.volc_linux = volc_linux
        if windows is not None:
            self.windows = windows

    @property
    def not_volc_linux(self):
        """Gets the not_volc_linux of this UninstallCommandsResponse.  # noqa: E501


        :return: The not_volc_linux of this UninstallCommandsResponse.  # noqa: E501
        :rtype: str
        """
        return self._not_volc_linux

    @not_volc_linux.setter
    def not_volc_linux(self, not_volc_linux):
        """Sets the not_volc_linux of this UninstallCommandsResponse.


        :param not_volc_linux: The not_volc_linux of this UninstallCommandsResponse.  # noqa: E501
        :type: str
        """

        self._not_volc_linux = not_volc_linux

    @property
    def volc_linux(self):
        """Gets the volc_linux of this UninstallCommandsResponse.  # noqa: E501


        :return: The volc_linux of this UninstallCommandsResponse.  # noqa: E501
        :rtype: str
        """
        return self._volc_linux

    @volc_linux.setter
    def volc_linux(self, volc_linux):
        """Sets the volc_linux of this UninstallCommandsResponse.


        :param volc_linux: The volc_linux of this UninstallCommandsResponse.  # noqa: E501
        :type: str
        """

        self._volc_linux = volc_linux

    @property
    def windows(self):
        """Gets the windows of this UninstallCommandsResponse.  # noqa: E501


        :return: The windows of this UninstallCommandsResponse.  # noqa: E501
        :rtype: str
        """
        return self._windows

    @windows.setter
    def windows(self, windows):
        """Sets the windows of this UninstallCommandsResponse.


        :param windows: The windows of this UninstallCommandsResponse.  # noqa: E501
        :type: str
        """

        self._windows = windows

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UninstallCommandsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UninstallCommandsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UninstallCommandsResponse):
            return True

        return self.to_dict() != other.to_dict()
