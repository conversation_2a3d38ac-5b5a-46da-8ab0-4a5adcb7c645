# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListEDXBandwidthPkgRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth_pkg_id': 'str',
        'edx_instance_id': 'str',
        'name': 'str',
        'page_number': 'str',
        'page_size': 'int',
        'state': 'str'
    }

    attribute_map = {
        'bandwidth_pkg_id': 'BandwidthPkgId',
        'edx_instance_id': 'EDXInstanceId',
        'name': 'Name',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'state': 'State'
    }

    def __init__(self, bandwidth_pkg_id=None, edx_instance_id=None, name=None, page_number=None, page_size=None, state=None, _configuration=None):  # noqa: E501
        """ListEDXBandwidthPkgRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth_pkg_id = None
        self._edx_instance_id = None
        self._name = None
        self._page_number = None
        self._page_size = None
        self._state = None
        self.discriminator = None

        if bandwidth_pkg_id is not None:
            self.bandwidth_pkg_id = bandwidth_pkg_id
        self.edx_instance_id = edx_instance_id
        if name is not None:
            self.name = name
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if state is not None:
            self.state = state

    @property
    def bandwidth_pkg_id(self):
        """Gets the bandwidth_pkg_id of this ListEDXBandwidthPkgRequest.  # noqa: E501


        :return: The bandwidth_pkg_id of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_pkg_id

    @bandwidth_pkg_id.setter
    def bandwidth_pkg_id(self, bandwidth_pkg_id):
        """Sets the bandwidth_pkg_id of this ListEDXBandwidthPkgRequest.


        :param bandwidth_pkg_id: The bandwidth_pkg_id of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """

        self._bandwidth_pkg_id = bandwidth_pkg_id

    @property
    def edx_instance_id(self):
        """Gets the edx_instance_id of this ListEDXBandwidthPkgRequest.  # noqa: E501


        :return: The edx_instance_id of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._edx_instance_id

    @edx_instance_id.setter
    def edx_instance_id(self, edx_instance_id):
        """Sets the edx_instance_id of this ListEDXBandwidthPkgRequest.


        :param edx_instance_id: The edx_instance_id of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and edx_instance_id is None:
            raise ValueError("Invalid value for `edx_instance_id`, must not be `None`")  # noqa: E501

        self._edx_instance_id = edx_instance_id

    @property
    def name(self):
        """Gets the name of this ListEDXBandwidthPkgRequest.  # noqa: E501


        :return: The name of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListEDXBandwidthPkgRequest.


        :param name: The name of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_number(self):
        """Gets the page_number of this ListEDXBandwidthPkgRequest.  # noqa: E501


        :return: The page_number of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListEDXBandwidthPkgRequest.


        :param page_number: The page_number of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListEDXBandwidthPkgRequest.  # noqa: E501


        :return: The page_size of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListEDXBandwidthPkgRequest.


        :param page_size: The page_size of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def state(self):
        """Gets the state of this ListEDXBandwidthPkgRequest.  # noqa: E501


        :return: The state of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ListEDXBandwidthPkgRequest.


        :param state: The state of this ListEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListEDXBandwidthPkgRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListEDXBandwidthPkgRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListEDXBandwidthPkgRequest):
            return True

        return self.to_dict() != other.to_dict()
