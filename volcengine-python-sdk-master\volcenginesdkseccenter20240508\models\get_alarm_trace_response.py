# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAlarmTraceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'c2_list': 'list[str]',
        'full_data': 'bool',
        'insert_time': 'int',
        'status': 'str',
        'trace_id': 'str',
        'trace_res': 'TraceResForGetAlarmTraceOutput',
        'update_time': 'int'
    }

    attribute_map = {
        'c2_list': 'C2List',
        'full_data': 'FullData',
        'insert_time': 'InsertTime',
        'status': 'Status',
        'trace_id': 'TraceID',
        'trace_res': 'TraceRes',
        'update_time': 'UpdateTime'
    }

    def __init__(self, c2_list=None, full_data=None, insert_time=None, status=None, trace_id=None, trace_res=None, update_time=None, _configuration=None):  # noqa: E501
        """GetAlarmTraceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._c2_list = None
        self._full_data = None
        self._insert_time = None
        self._status = None
        self._trace_id = None
        self._trace_res = None
        self._update_time = None
        self.discriminator = None

        if c2_list is not None:
            self.c2_list = c2_list
        if full_data is not None:
            self.full_data = full_data
        if insert_time is not None:
            self.insert_time = insert_time
        if status is not None:
            self.status = status
        if trace_id is not None:
            self.trace_id = trace_id
        if trace_res is not None:
            self.trace_res = trace_res
        if update_time is not None:
            self.update_time = update_time

    @property
    def c2_list(self):
        """Gets the c2_list of this GetAlarmTraceResponse.  # noqa: E501


        :return: The c2_list of this GetAlarmTraceResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._c2_list

    @c2_list.setter
    def c2_list(self, c2_list):
        """Sets the c2_list of this GetAlarmTraceResponse.


        :param c2_list: The c2_list of this GetAlarmTraceResponse.  # noqa: E501
        :type: list[str]
        """

        self._c2_list = c2_list

    @property
    def full_data(self):
        """Gets the full_data of this GetAlarmTraceResponse.  # noqa: E501


        :return: The full_data of this GetAlarmTraceResponse.  # noqa: E501
        :rtype: bool
        """
        return self._full_data

    @full_data.setter
    def full_data(self, full_data):
        """Sets the full_data of this GetAlarmTraceResponse.


        :param full_data: The full_data of this GetAlarmTraceResponse.  # noqa: E501
        :type: bool
        """

        self._full_data = full_data

    @property
    def insert_time(self):
        """Gets the insert_time of this GetAlarmTraceResponse.  # noqa: E501


        :return: The insert_time of this GetAlarmTraceResponse.  # noqa: E501
        :rtype: int
        """
        return self._insert_time

    @insert_time.setter
    def insert_time(self, insert_time):
        """Sets the insert_time of this GetAlarmTraceResponse.


        :param insert_time: The insert_time of this GetAlarmTraceResponse.  # noqa: E501
        :type: int
        """

        self._insert_time = insert_time

    @property
    def status(self):
        """Gets the status of this GetAlarmTraceResponse.  # noqa: E501


        :return: The status of this GetAlarmTraceResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetAlarmTraceResponse.


        :param status: The status of this GetAlarmTraceResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def trace_id(self):
        """Gets the trace_id of this GetAlarmTraceResponse.  # noqa: E501


        :return: The trace_id of this GetAlarmTraceResponse.  # noqa: E501
        :rtype: str
        """
        return self._trace_id

    @trace_id.setter
    def trace_id(self, trace_id):
        """Sets the trace_id of this GetAlarmTraceResponse.


        :param trace_id: The trace_id of this GetAlarmTraceResponse.  # noqa: E501
        :type: str
        """

        self._trace_id = trace_id

    @property
    def trace_res(self):
        """Gets the trace_res of this GetAlarmTraceResponse.  # noqa: E501


        :return: The trace_res of this GetAlarmTraceResponse.  # noqa: E501
        :rtype: TraceResForGetAlarmTraceOutput
        """
        return self._trace_res

    @trace_res.setter
    def trace_res(self, trace_res):
        """Sets the trace_res of this GetAlarmTraceResponse.


        :param trace_res: The trace_res of this GetAlarmTraceResponse.  # noqa: E501
        :type: TraceResForGetAlarmTraceOutput
        """

        self._trace_res = trace_res

    @property
    def update_time(self):
        """Gets the update_time of this GetAlarmTraceResponse.  # noqa: E501


        :return: The update_time of this GetAlarmTraceResponse.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetAlarmTraceResponse.


        :param update_time: The update_time of this GetAlarmTraceResponse.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAlarmTraceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAlarmTraceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAlarmTraceResponse):
            return True

        return self.to_dict() != other.to_dict()
