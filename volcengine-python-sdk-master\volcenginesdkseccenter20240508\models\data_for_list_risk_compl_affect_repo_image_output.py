# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRiskComplAffectRepoImageOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_id': 'str',
        'image_id': 'str',
        'namespace': 'str',
        'region': 'str',
        'registry_id': 'str',
        'registry_name': 'str',
        'repo': 'str',
        'tag': 'str'
    }

    attribute_map = {
        'asset_id': 'AssetID',
        'image_id': 'ImageID',
        'namespace': 'Namespace',
        'region': 'Region',
        'registry_id': 'RegistryID',
        'registry_name': 'RegistryName',
        'repo': 'Repo',
        'tag': 'Tag'
    }

    def __init__(self, asset_id=None, image_id=None, namespace=None, region=None, registry_id=None, registry_name=None, repo=None, tag=None, _configuration=None):  # noqa: E501
        """DataForListRiskComplAffectRepoImageOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_id = None
        self._image_id = None
        self._namespace = None
        self._region = None
        self._registry_id = None
        self._registry_name = None
        self._repo = None
        self._tag = None
        self.discriminator = None

        if asset_id is not None:
            self.asset_id = asset_id
        if image_id is not None:
            self.image_id = image_id
        if namespace is not None:
            self.namespace = namespace
        if region is not None:
            self.region = region
        if registry_id is not None:
            self.registry_id = registry_id
        if registry_name is not None:
            self.registry_name = registry_name
        if repo is not None:
            self.repo = repo
        if tag is not None:
            self.tag = tag

    @property
    def asset_id(self):
        """Gets the asset_id of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501


        :return: The asset_id of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this DataForListRiskComplAffectRepoImageOutput.


        :param asset_id: The asset_id of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def image_id(self):
        """Gets the image_id of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501


        :return: The image_id of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this DataForListRiskComplAffectRepoImageOutput.


        :param image_id: The image_id of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def namespace(self):
        """Gets the namespace of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501


        :return: The namespace of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this DataForListRiskComplAffectRepoImageOutput.


        :param namespace: The namespace of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def region(self):
        """Gets the region of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501


        :return: The region of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListRiskComplAffectRepoImageOutput.


        :param region: The region of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def registry_id(self):
        """Gets the registry_id of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501


        :return: The registry_id of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry_id

    @registry_id.setter
    def registry_id(self, registry_id):
        """Sets the registry_id of this DataForListRiskComplAffectRepoImageOutput.


        :param registry_id: The registry_id of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :type: str
        """

        self._registry_id = registry_id

    @property
    def registry_name(self):
        """Gets the registry_name of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501


        :return: The registry_name of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._registry_name

    @registry_name.setter
    def registry_name(self, registry_name):
        """Sets the registry_name of this DataForListRiskComplAffectRepoImageOutput.


        :param registry_name: The registry_name of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :type: str
        """

        self._registry_name = registry_name

    @property
    def repo(self):
        """Gets the repo of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501


        :return: The repo of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._repo

    @repo.setter
    def repo(self, repo):
        """Sets the repo of this DataForListRiskComplAffectRepoImageOutput.


        :param repo: The repo of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :type: str
        """

        self._repo = repo

    @property
    def tag(self):
        """Gets the tag of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501


        :return: The tag of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this DataForListRiskComplAffectRepoImageOutput.


        :param tag: The tag of this DataForListRiskComplAffectRepoImageOutput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRiskComplAffectRepoImageOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRiskComplAffectRepoImageOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRiskComplAffectRepoImageOutput):
            return True

        return self.to_dict() != other.to_dict()
