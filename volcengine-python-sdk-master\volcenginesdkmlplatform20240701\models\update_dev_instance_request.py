# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateDevInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'credential': 'CredentialForUpdateDevInstanceInput',
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'node_affinity_spec': 'NodeAffinitySpecForUpdateDevInstanceInput',
        'numa_affinity': 'str',
        'ports': 'list[PortForUpdateDevInstanceInput]',
        'resource_claim': 'ResourceClaimForUpdateDevInstanceInput',
        'resource_queue_id': 'str',
        'ssh_public_key': 'str',
        'storages': 'list[StorageForUpdateDevInstanceInput]',
        'volume_size': 'int'
    }

    attribute_map = {
        'credential': 'Credential',
        'description': 'Description',
        'id': 'Id',
        'name': 'Name',
        'node_affinity_spec': 'NodeAffinitySpec',
        'numa_affinity': 'NumaAffinity',
        'ports': 'Ports',
        'resource_claim': 'ResourceClaim',
        'resource_queue_id': 'ResourceQueueId',
        'ssh_public_key': 'SshPublicKey',
        'storages': 'Storages',
        'volume_size': 'VolumeSize'
    }

    def __init__(self, credential=None, description=None, id=None, name=None, node_affinity_spec=None, numa_affinity=None, ports=None, resource_claim=None, resource_queue_id=None, ssh_public_key=None, storages=None, volume_size=None, _configuration=None):  # noqa: E501
        """UpdateDevInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._credential = None
        self._description = None
        self._id = None
        self._name = None
        self._node_affinity_spec = None
        self._numa_affinity = None
        self._ports = None
        self._resource_claim = None
        self._resource_queue_id = None
        self._ssh_public_key = None
        self._storages = None
        self._volume_size = None
        self.discriminator = None

        if credential is not None:
            self.credential = credential
        if description is not None:
            self.description = description
        self.id = id
        if name is not None:
            self.name = name
        if node_affinity_spec is not None:
            self.node_affinity_spec = node_affinity_spec
        if numa_affinity is not None:
            self.numa_affinity = numa_affinity
        if ports is not None:
            self.ports = ports
        if resource_claim is not None:
            self.resource_claim = resource_claim
        if resource_queue_id is not None:
            self.resource_queue_id = resource_queue_id
        if ssh_public_key is not None:
            self.ssh_public_key = ssh_public_key
        if storages is not None:
            self.storages = storages
        if volume_size is not None:
            self.volume_size = volume_size

    @property
    def credential(self):
        """Gets the credential of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The credential of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: CredentialForUpdateDevInstanceInput
        """
        return self._credential

    @credential.setter
    def credential(self, credential):
        """Sets the credential of this UpdateDevInstanceRequest.


        :param credential: The credential of this UpdateDevInstanceRequest.  # noqa: E501
        :type: CredentialForUpdateDevInstanceInput
        """

        self._credential = credential

    @property
    def description(self):
        """Gets the description of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The description of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateDevInstanceRequest.


        :param description: The description of this UpdateDevInstanceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The id of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateDevInstanceRequest.


        :param id: The id of this UpdateDevInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The name of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateDevInstanceRequest.


        :param name: The name of this UpdateDevInstanceRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def node_affinity_spec(self):
        """Gets the node_affinity_spec of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The node_affinity_spec of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: NodeAffinitySpecForUpdateDevInstanceInput
        """
        return self._node_affinity_spec

    @node_affinity_spec.setter
    def node_affinity_spec(self, node_affinity_spec):
        """Sets the node_affinity_spec of this UpdateDevInstanceRequest.


        :param node_affinity_spec: The node_affinity_spec of this UpdateDevInstanceRequest.  # noqa: E501
        :type: NodeAffinitySpecForUpdateDevInstanceInput
        """

        self._node_affinity_spec = node_affinity_spec

    @property
    def numa_affinity(self):
        """Gets the numa_affinity of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The numa_affinity of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._numa_affinity

    @numa_affinity.setter
    def numa_affinity(self, numa_affinity):
        """Sets the numa_affinity of this UpdateDevInstanceRequest.


        :param numa_affinity: The numa_affinity of this UpdateDevInstanceRequest.  # noqa: E501
        :type: str
        """

        self._numa_affinity = numa_affinity

    @property
    def ports(self):
        """Gets the ports of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The ports of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: list[PortForUpdateDevInstanceInput]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this UpdateDevInstanceRequest.


        :param ports: The ports of this UpdateDevInstanceRequest.  # noqa: E501
        :type: list[PortForUpdateDevInstanceInput]
        """

        self._ports = ports

    @property
    def resource_claim(self):
        """Gets the resource_claim of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The resource_claim of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: ResourceClaimForUpdateDevInstanceInput
        """
        return self._resource_claim

    @resource_claim.setter
    def resource_claim(self, resource_claim):
        """Sets the resource_claim of this UpdateDevInstanceRequest.


        :param resource_claim: The resource_claim of this UpdateDevInstanceRequest.  # noqa: E501
        :type: ResourceClaimForUpdateDevInstanceInput
        """

        self._resource_claim = resource_claim

    @property
    def resource_queue_id(self):
        """Gets the resource_queue_id of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The resource_queue_id of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_queue_id

    @resource_queue_id.setter
    def resource_queue_id(self, resource_queue_id):
        """Sets the resource_queue_id of this UpdateDevInstanceRequest.


        :param resource_queue_id: The resource_queue_id of this UpdateDevInstanceRequest.  # noqa: E501
        :type: str
        """

        self._resource_queue_id = resource_queue_id

    @property
    def ssh_public_key(self):
        """Gets the ssh_public_key of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The ssh_public_key of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._ssh_public_key

    @ssh_public_key.setter
    def ssh_public_key(self, ssh_public_key):
        """Sets the ssh_public_key of this UpdateDevInstanceRequest.


        :param ssh_public_key: The ssh_public_key of this UpdateDevInstanceRequest.  # noqa: E501
        :type: str
        """

        self._ssh_public_key = ssh_public_key

    @property
    def storages(self):
        """Gets the storages of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The storages of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: list[StorageForUpdateDevInstanceInput]
        """
        return self._storages

    @storages.setter
    def storages(self, storages):
        """Sets the storages of this UpdateDevInstanceRequest.


        :param storages: The storages of this UpdateDevInstanceRequest.  # noqa: E501
        :type: list[StorageForUpdateDevInstanceInput]
        """

        self._storages = storages

    @property
    def volume_size(self):
        """Gets the volume_size of this UpdateDevInstanceRequest.  # noqa: E501


        :return: The volume_size of this UpdateDevInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._volume_size

    @volume_size.setter
    def volume_size(self, volume_size):
        """Sets the volume_size of this UpdateDevInstanceRequest.


        :param volume_size: The volume_size of this UpdateDevInstanceRequest.  # noqa: E501
        :type: int
        """

        self._volume_size = volume_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateDevInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateDevInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateDevInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
