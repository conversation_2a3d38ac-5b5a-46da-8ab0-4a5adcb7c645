# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResForGetStreamRecordOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'extra': 'ExtraForGetStreamRecordOutput',
        'remux_url': 'str',
        'url': 'str'
    }

    attribute_map = {
        'extra': 'Extra',
        'remux_url': 'RemuxUrl',
        'url': 'Url'
    }

    def __init__(self, extra=None, remux_url=None, url=None, _configuration=None):  # noqa: E501
        """ResForGetStreamRecordOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._extra = None
        self._remux_url = None
        self._url = None
        self.discriminator = None

        if extra is not None:
            self.extra = extra
        if remux_url is not None:
            self.remux_url = remux_url
        if url is not None:
            self.url = url

    @property
    def extra(self):
        """Gets the extra of this ResForGetStreamRecordOutput.  # noqa: E501


        :return: The extra of this ResForGetStreamRecordOutput.  # noqa: E501
        :rtype: ExtraForGetStreamRecordOutput
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this ResForGetStreamRecordOutput.


        :param extra: The extra of this ResForGetStreamRecordOutput.  # noqa: E501
        :type: ExtraForGetStreamRecordOutput
        """

        self._extra = extra

    @property
    def remux_url(self):
        """Gets the remux_url of this ResForGetStreamRecordOutput.  # noqa: E501


        :return: The remux_url of this ResForGetStreamRecordOutput.  # noqa: E501
        :rtype: str
        """
        return self._remux_url

    @remux_url.setter
    def remux_url(self, remux_url):
        """Sets the remux_url of this ResForGetStreamRecordOutput.


        :param remux_url: The remux_url of this ResForGetStreamRecordOutput.  # noqa: E501
        :type: str
        """

        self._remux_url = remux_url

    @property
    def url(self):
        """Gets the url of this ResForGetStreamRecordOutput.  # noqa: E501


        :return: The url of this ResForGetStreamRecordOutput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this ResForGetStreamRecordOutput.


        :param url: The url of this ResForGetStreamRecordOutput.  # noqa: E501
        :type: str
        """

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResForGetStreamRecordOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResForGetStreamRecordOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResForGetStreamRecordOutput):
            return True

        return self.to_dict() != other.to_dict()
