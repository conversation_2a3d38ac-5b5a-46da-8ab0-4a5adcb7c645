# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTransitRouterRouteEntriesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'destination_cidr_block': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'status': 'str',
        'transit_router_route_entry_ids': 'list[str]',
        'transit_router_route_entry_name': 'str',
        'transit_router_route_table_id': 'str'
    }

    attribute_map = {
        'destination_cidr_block': 'DestinationCidrBlock',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'status': 'Status',
        'transit_router_route_entry_ids': 'TransitRouterRouteEntryIds',
        'transit_router_route_entry_name': 'TransitRouterRouteEntryName',
        'transit_router_route_table_id': 'TransitRouterRouteTableId'
    }

    def __init__(self, destination_cidr_block=None, page_number=None, page_size=None, status=None, transit_router_route_entry_ids=None, transit_router_route_entry_name=None, transit_router_route_table_id=None, _configuration=None):  # noqa: E501
        """DescribeTransitRouterRouteEntriesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._destination_cidr_block = None
        self._page_number = None
        self._page_size = None
        self._status = None
        self._transit_router_route_entry_ids = None
        self._transit_router_route_entry_name = None
        self._transit_router_route_table_id = None
        self.discriminator = None

        if destination_cidr_block is not None:
            self.destination_cidr_block = destination_cidr_block
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if status is not None:
            self.status = status
        if transit_router_route_entry_ids is not None:
            self.transit_router_route_entry_ids = transit_router_route_entry_ids
        if transit_router_route_entry_name is not None:
            self.transit_router_route_entry_name = transit_router_route_entry_name
        self.transit_router_route_table_id = transit_router_route_table_id

    @property
    def destination_cidr_block(self):
        """Gets the destination_cidr_block of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501


        :return: The destination_cidr_block of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_cidr_block

    @destination_cidr_block.setter
    def destination_cidr_block(self, destination_cidr_block):
        """Sets the destination_cidr_block of this DescribeTransitRouterRouteEntriesRequest.


        :param destination_cidr_block: The destination_cidr_block of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :type: str
        """

        self._destination_cidr_block = destination_cidr_block

    @property
    def page_number(self):
        """Gets the page_number of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501


        :return: The page_number of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeTransitRouterRouteEntriesRequest.


        :param page_number: The page_number of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501


        :return: The page_size of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeTransitRouterRouteEntriesRequest.


        :param page_size: The page_size of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def status(self):
        """Gets the status of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501


        :return: The status of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeTransitRouterRouteEntriesRequest.


        :param status: The status of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_route_entry_ids(self):
        """Gets the transit_router_route_entry_ids of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501


        :return: The transit_router_route_entry_ids of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._transit_router_route_entry_ids

    @transit_router_route_entry_ids.setter
    def transit_router_route_entry_ids(self, transit_router_route_entry_ids):
        """Sets the transit_router_route_entry_ids of this DescribeTransitRouterRouteEntriesRequest.


        :param transit_router_route_entry_ids: The transit_router_route_entry_ids of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :type: list[str]
        """

        self._transit_router_route_entry_ids = transit_router_route_entry_ids

    @property
    def transit_router_route_entry_name(self):
        """Gets the transit_router_route_entry_name of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501


        :return: The transit_router_route_entry_name of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_entry_name

    @transit_router_route_entry_name.setter
    def transit_router_route_entry_name(self, transit_router_route_entry_name):
        """Sets the transit_router_route_entry_name of this DescribeTransitRouterRouteEntriesRequest.


        :param transit_router_route_entry_name: The transit_router_route_entry_name of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_route_entry_name = transit_router_route_entry_name

    @property
    def transit_router_route_table_id(self):
        """Gets the transit_router_route_table_id of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501


        :return: The transit_router_route_table_id of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_table_id

    @transit_router_route_table_id.setter
    def transit_router_route_table_id(self, transit_router_route_table_id):
        """Sets the transit_router_route_table_id of this DescribeTransitRouterRouteEntriesRequest.


        :param transit_router_route_table_id: The transit_router_route_table_id of this DescribeTransitRouterRouteEntriesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_route_table_id is None:
            raise ValueError("Invalid value for `transit_router_route_table_id`, must not be `None`")  # noqa: E501

        self._transit_router_route_table_id = transit_router_route_table_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTransitRouterRouteEntriesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTransitRouterRouteEntriesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTransitRouterRouteEntriesRequest):
            return True

        return self.to_dict() != other.to_dict()
