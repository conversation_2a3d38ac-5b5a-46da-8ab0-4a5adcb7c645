# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HotKeyForDescribeHotKeysOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'db_name': 'str',
        'key_info': 'str',
        'key_type': 'str',
        'node_id': 'str',
        'query_count': 'str',
        'shard_id': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'db_name': 'DBName',
        'key_info': 'KeyInfo',
        'key_type': 'KeyType',
        'node_id': 'NodeId',
        'query_count': 'QueryCount',
        'shard_id': 'ShardId'
    }

    def __init__(self, create_time=None, db_name=None, key_info=None, key_type=None, node_id=None, query_count=None, shard_id=None, _configuration=None):  # noqa: E501
        """HotKeyForDescribeHotKeysOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._db_name = None
        self._key_info = None
        self._key_type = None
        self._node_id = None
        self._query_count = None
        self._shard_id = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if db_name is not None:
            self.db_name = db_name
        if key_info is not None:
            self.key_info = key_info
        if key_type is not None:
            self.key_type = key_type
        if node_id is not None:
            self.node_id = node_id
        if query_count is not None:
            self.query_count = query_count
        if shard_id is not None:
            self.shard_id = shard_id

    @property
    def create_time(self):
        """Gets the create_time of this HotKeyForDescribeHotKeysOutput.  # noqa: E501


        :return: The create_time of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this HotKeyForDescribeHotKeysOutput.


        :param create_time: The create_time of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def db_name(self):
        """Gets the db_name of this HotKeyForDescribeHotKeysOutput.  # noqa: E501


        :return: The db_name of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_name

    @db_name.setter
    def db_name(self, db_name):
        """Sets the db_name of this HotKeyForDescribeHotKeysOutput.


        :param db_name: The db_name of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :type: str
        """

        self._db_name = db_name

    @property
    def key_info(self):
        """Gets the key_info of this HotKeyForDescribeHotKeysOutput.  # noqa: E501


        :return: The key_info of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._key_info

    @key_info.setter
    def key_info(self, key_info):
        """Sets the key_info of this HotKeyForDescribeHotKeysOutput.


        :param key_info: The key_info of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :type: str
        """

        self._key_info = key_info

    @property
    def key_type(self):
        """Gets the key_type of this HotKeyForDescribeHotKeysOutput.  # noqa: E501


        :return: The key_type of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._key_type

    @key_type.setter
    def key_type(self, key_type):
        """Sets the key_type of this HotKeyForDescribeHotKeysOutput.


        :param key_type: The key_type of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :type: str
        """

        self._key_type = key_type

    @property
    def node_id(self):
        """Gets the node_id of this HotKeyForDescribeHotKeysOutput.  # noqa: E501


        :return: The node_id of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this HotKeyForDescribeHotKeysOutput.


        :param node_id: The node_id of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def query_count(self):
        """Gets the query_count of this HotKeyForDescribeHotKeysOutput.  # noqa: E501


        :return: The query_count of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._query_count

    @query_count.setter
    def query_count(self, query_count):
        """Sets the query_count of this HotKeyForDescribeHotKeysOutput.


        :param query_count: The query_count of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :type: str
        """

        self._query_count = query_count

    @property
    def shard_id(self):
        """Gets the shard_id of this HotKeyForDescribeHotKeysOutput.  # noqa: E501


        :return: The shard_id of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :rtype: str
        """
        return self._shard_id

    @shard_id.setter
    def shard_id(self, shard_id):
        """Sets the shard_id of this HotKeyForDescribeHotKeysOutput.


        :param shard_id: The shard_id of this HotKeyForDescribeHotKeysOutput.  # noqa: E501
        :type: str
        """

        self._shard_id = shard_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HotKeyForDescribeHotKeysOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HotKeyForDescribeHotKeysOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HotKeyForDescribeHotKeysOutput):
            return True

        return self.to_dict() != other.to_dict()
