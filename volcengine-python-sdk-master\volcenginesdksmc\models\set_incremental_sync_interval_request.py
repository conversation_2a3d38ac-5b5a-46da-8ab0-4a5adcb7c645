# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SetIncrementalSyncIntervalRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'migration_job_id': 'str',
        'sync_interval': 'int'
    }

    attribute_map = {
        'migration_job_id': 'MigrationJobId',
        'sync_interval': 'SyncInterval'
    }

    def __init__(self, migration_job_id=None, sync_interval=None, _configuration=None):  # noqa: E501
        """SetIncrementalSyncIntervalRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._migration_job_id = None
        self._sync_interval = None
        self.discriminator = None

        self.migration_job_id = migration_job_id
        if sync_interval is not None:
            self.sync_interval = sync_interval

    @property
    def migration_job_id(self):
        """Gets the migration_job_id of this SetIncrementalSyncIntervalRequest.  # noqa: E501


        :return: The migration_job_id of this SetIncrementalSyncIntervalRequest.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_id

    @migration_job_id.setter
    def migration_job_id(self, migration_job_id):
        """Sets the migration_job_id of this SetIncrementalSyncIntervalRequest.


        :param migration_job_id: The migration_job_id of this SetIncrementalSyncIntervalRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and migration_job_id is None:
            raise ValueError("Invalid value for `migration_job_id`, must not be `None`")  # noqa: E501

        self._migration_job_id = migration_job_id

    @property
    def sync_interval(self):
        """Gets the sync_interval of this SetIncrementalSyncIntervalRequest.  # noqa: E501


        :return: The sync_interval of this SetIncrementalSyncIntervalRequest.  # noqa: E501
        :rtype: int
        """
        return self._sync_interval

    @sync_interval.setter
    def sync_interval(self, sync_interval):
        """Sets the sync_interval of this SetIncrementalSyncIntervalRequest.


        :param sync_interval: The sync_interval of this SetIncrementalSyncIntervalRequest.  # noqa: E501
        :type: int
        """

        self._sync_interval = sync_interval

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SetIncrementalSyncIntervalRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SetIncrementalSyncIntervalRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SetIncrementalSyncIntervalRequest):
            return True

        return self.to_dict() != other.to_dict()
