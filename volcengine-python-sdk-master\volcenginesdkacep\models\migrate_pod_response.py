# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MigratePodResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'details': 'list[DetailForMigratePodOutput]',
        'product_id': 'str'
    }

    attribute_map = {
        'details': 'Details',
        'product_id': 'ProductId'
    }

    def __init__(self, details=None, product_id=None, _configuration=None):  # noqa: E501
        """MigratePodResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._details = None
        self._product_id = None
        self.discriminator = None

        if details is not None:
            self.details = details
        if product_id is not None:
            self.product_id = product_id

    @property
    def details(self):
        """Gets the details of this MigratePodResponse.  # noqa: E501


        :return: The details of this MigratePodResponse.  # noqa: E501
        :rtype: list[DetailForMigratePodOutput]
        """
        return self._details

    @details.setter
    def details(self, details):
        """Sets the details of this MigratePodResponse.


        :param details: The details of this MigratePodResponse.  # noqa: E501
        :type: list[DetailForMigratePodOutput]
        """

        self._details = details

    @property
    def product_id(self):
        """Gets the product_id of this MigratePodResponse.  # noqa: E501


        :return: The product_id of this MigratePodResponse.  # noqa: E501
        :rtype: str
        """
        return self._product_id

    @product_id.setter
    def product_id(self, product_id):
        """Sets the product_id of this MigratePodResponse.


        :param product_id: The product_id of this MigratePodResponse.  # noqa: E501
        :type: str
        """

        self._product_id = product_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MigratePodResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MigratePodResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MigratePodResponse):
            return True

        return self.to_dict() != other.to_dict()
