# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceForDescribeInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affinity_group_id': 'str',
        'cpu_options': 'CpuOptionsForDescribeInstancesOutput',
        'cpus': 'int',
        'created_at': 'str',
        'deletion_protection': 'bool',
        'deployment_set_group_number': 'int',
        'deployment_set_id': 'str',
        'description': 'str',
        'eip_address': 'EipAddressForDescribeInstancesOutput',
        'elastic_scheduled_instance_type': 'str',
        'expired_at': 'str',
        'host_name': 'str',
        'hostname': 'str',
        'hpc_cluster_id': 'str',
        'image_id': 'str',
        'instance_charge_type': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_type_id': 'str',
        'key_pair_id': 'str',
        'key_pair_name': 'str',
        'local_volumes': 'list[LocalVolumeForDescribeInstancesOutput]',
        'memory_size': 'int',
        'network_interfaces': 'list[NetworkInterfaceForDescribeInstancesOutput]',
        'os_name': 'str',
        'os_type': 'str',
        'placement': 'PlacementForDescribeInstancesOutput',
        'project_name': 'str',
        'rdma_ip_addresses': 'list[str]',
        'scheduled_instance_id': 'str',
        'spot_price_limit': 'float',
        'spot_strategy': 'str',
        'status': 'str',
        'stopped_mode': 'str',
        'tags': 'list[TagForDescribeInstancesOutput]',
        'updated_at': 'str',
        'uuid': 'str',
        'volumes': 'list[VolumeForDescribeInstancesOutput]',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'affinity_group_id': 'AffinityGroupId',
        'cpu_options': 'CpuOptions',
        'cpus': 'Cpus',
        'created_at': 'CreatedAt',
        'deletion_protection': 'DeletionProtection',
        'deployment_set_group_number': 'DeploymentSetGroupNumber',
        'deployment_set_id': 'DeploymentSetId',
        'description': 'Description',
        'eip_address': 'EipAddress',
        'elastic_scheduled_instance_type': 'ElasticScheduledInstanceType',
        'expired_at': 'ExpiredAt',
        'host_name': 'HostName',
        'hostname': 'Hostname',
        'hpc_cluster_id': 'HpcClusterId',
        'image_id': 'ImageId',
        'instance_charge_type': 'InstanceChargeType',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_type_id': 'InstanceTypeId',
        'key_pair_id': 'KeyPairId',
        'key_pair_name': 'KeyPairName',
        'local_volumes': 'LocalVolumes',
        'memory_size': 'MemorySize',
        'network_interfaces': 'NetworkInterfaces',
        'os_name': 'OsName',
        'os_type': 'OsType',
        'placement': 'Placement',
        'project_name': 'ProjectName',
        'rdma_ip_addresses': 'RdmaIpAddresses',
        'scheduled_instance_id': 'ScheduledInstanceId',
        'spot_price_limit': 'SpotPriceLimit',
        'spot_strategy': 'SpotStrategy',
        'status': 'Status',
        'stopped_mode': 'StoppedMode',
        'tags': 'Tags',
        'updated_at': 'UpdatedAt',
        'uuid': 'Uuid',
        'volumes': 'Volumes',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, affinity_group_id=None, cpu_options=None, cpus=None, created_at=None, deletion_protection=None, deployment_set_group_number=None, deployment_set_id=None, description=None, eip_address=None, elastic_scheduled_instance_type=None, expired_at=None, host_name=None, hostname=None, hpc_cluster_id=None, image_id=None, instance_charge_type=None, instance_id=None, instance_name=None, instance_type_id=None, key_pair_id=None, key_pair_name=None, local_volumes=None, memory_size=None, network_interfaces=None, os_name=None, os_type=None, placement=None, project_name=None, rdma_ip_addresses=None, scheduled_instance_id=None, spot_price_limit=None, spot_strategy=None, status=None, stopped_mode=None, tags=None, updated_at=None, uuid=None, volumes=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """InstanceForDescribeInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affinity_group_id = None
        self._cpu_options = None
        self._cpus = None
        self._created_at = None
        self._deletion_protection = None
        self._deployment_set_group_number = None
        self._deployment_set_id = None
        self._description = None
        self._eip_address = None
        self._elastic_scheduled_instance_type = None
        self._expired_at = None
        self._host_name = None
        self._hostname = None
        self._hpc_cluster_id = None
        self._image_id = None
        self._instance_charge_type = None
        self._instance_id = None
        self._instance_name = None
        self._instance_type_id = None
        self._key_pair_id = None
        self._key_pair_name = None
        self._local_volumes = None
        self._memory_size = None
        self._network_interfaces = None
        self._os_name = None
        self._os_type = None
        self._placement = None
        self._project_name = None
        self._rdma_ip_addresses = None
        self._scheduled_instance_id = None
        self._spot_price_limit = None
        self._spot_strategy = None
        self._status = None
        self._stopped_mode = None
        self._tags = None
        self._updated_at = None
        self._uuid = None
        self._volumes = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if affinity_group_id is not None:
            self.affinity_group_id = affinity_group_id
        if cpu_options is not None:
            self.cpu_options = cpu_options
        if cpus is not None:
            self.cpus = cpus
        if created_at is not None:
            self.created_at = created_at
        if deletion_protection is not None:
            self.deletion_protection = deletion_protection
        if deployment_set_group_number is not None:
            self.deployment_set_group_number = deployment_set_group_number
        if deployment_set_id is not None:
            self.deployment_set_id = deployment_set_id
        if description is not None:
            self.description = description
        if eip_address is not None:
            self.eip_address = eip_address
        if elastic_scheduled_instance_type is not None:
            self.elastic_scheduled_instance_type = elastic_scheduled_instance_type
        if expired_at is not None:
            self.expired_at = expired_at
        if host_name is not None:
            self.host_name = host_name
        if hostname is not None:
            self.hostname = hostname
        if hpc_cluster_id is not None:
            self.hpc_cluster_id = hpc_cluster_id
        if image_id is not None:
            self.image_id = image_id
        if instance_charge_type is not None:
            self.instance_charge_type = instance_charge_type
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_type_id is not None:
            self.instance_type_id = instance_type_id
        if key_pair_id is not None:
            self.key_pair_id = key_pair_id
        if key_pair_name is not None:
            self.key_pair_name = key_pair_name
        if local_volumes is not None:
            self.local_volumes = local_volumes
        if memory_size is not None:
            self.memory_size = memory_size
        if network_interfaces is not None:
            self.network_interfaces = network_interfaces
        if os_name is not None:
            self.os_name = os_name
        if os_type is not None:
            self.os_type = os_type
        if placement is not None:
            self.placement = placement
        if project_name is not None:
            self.project_name = project_name
        if rdma_ip_addresses is not None:
            self.rdma_ip_addresses = rdma_ip_addresses
        if scheduled_instance_id is not None:
            self.scheduled_instance_id = scheduled_instance_id
        if spot_price_limit is not None:
            self.spot_price_limit = spot_price_limit
        if spot_strategy is not None:
            self.spot_strategy = spot_strategy
        if status is not None:
            self.status = status
        if stopped_mode is not None:
            self.stopped_mode = stopped_mode
        if tags is not None:
            self.tags = tags
        if updated_at is not None:
            self.updated_at = updated_at
        if uuid is not None:
            self.uuid = uuid
        if volumes is not None:
            self.volumes = volumes
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def affinity_group_id(self):
        """Gets the affinity_group_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The affinity_group_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._affinity_group_id

    @affinity_group_id.setter
    def affinity_group_id(self, affinity_group_id):
        """Sets the affinity_group_id of this InstanceForDescribeInstancesOutput.


        :param affinity_group_id: The affinity_group_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._affinity_group_id = affinity_group_id

    @property
    def cpu_options(self):
        """Gets the cpu_options of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The cpu_options of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: CpuOptionsForDescribeInstancesOutput
        """
        return self._cpu_options

    @cpu_options.setter
    def cpu_options(self, cpu_options):
        """Sets the cpu_options of this InstanceForDescribeInstancesOutput.


        :param cpu_options: The cpu_options of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: CpuOptionsForDescribeInstancesOutput
        """

        self._cpu_options = cpu_options

    @property
    def cpus(self):
        """Gets the cpus of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The cpus of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._cpus

    @cpus.setter
    def cpus(self, cpus):
        """Sets the cpus of this InstanceForDescribeInstancesOutput.


        :param cpus: The cpus of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: int
        """

        self._cpus = cpus

    @property
    def created_at(self):
        """Gets the created_at of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The created_at of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this InstanceForDescribeInstancesOutput.


        :param created_at: The created_at of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def deletion_protection(self):
        """Gets the deletion_protection of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The deletion_protection of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._deletion_protection

    @deletion_protection.setter
    def deletion_protection(self, deletion_protection):
        """Sets the deletion_protection of this InstanceForDescribeInstancesOutput.


        :param deletion_protection: The deletion_protection of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._deletion_protection = deletion_protection

    @property
    def deployment_set_group_number(self):
        """Gets the deployment_set_group_number of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The deployment_set_group_number of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._deployment_set_group_number

    @deployment_set_group_number.setter
    def deployment_set_group_number(self, deployment_set_group_number):
        """Sets the deployment_set_group_number of this InstanceForDescribeInstancesOutput.


        :param deployment_set_group_number: The deployment_set_group_number of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: int
        """

        self._deployment_set_group_number = deployment_set_group_number

    @property
    def deployment_set_id(self):
        """Gets the deployment_set_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The deployment_set_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._deployment_set_id

    @deployment_set_id.setter
    def deployment_set_id(self, deployment_set_id):
        """Sets the deployment_set_id of this InstanceForDescribeInstancesOutput.


        :param deployment_set_id: The deployment_set_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._deployment_set_id = deployment_set_id

    @property
    def description(self):
        """Gets the description of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The description of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this InstanceForDescribeInstancesOutput.


        :param description: The description of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def eip_address(self):
        """Gets the eip_address of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The eip_address of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: EipAddressForDescribeInstancesOutput
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this InstanceForDescribeInstancesOutput.


        :param eip_address: The eip_address of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: EipAddressForDescribeInstancesOutput
        """

        self._eip_address = eip_address

    @property
    def elastic_scheduled_instance_type(self):
        """Gets the elastic_scheduled_instance_type of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The elastic_scheduled_instance_type of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._elastic_scheduled_instance_type

    @elastic_scheduled_instance_type.setter
    def elastic_scheduled_instance_type(self, elastic_scheduled_instance_type):
        """Sets the elastic_scheduled_instance_type of this InstanceForDescribeInstancesOutput.


        :param elastic_scheduled_instance_type: The elastic_scheduled_instance_type of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._elastic_scheduled_instance_type = elastic_scheduled_instance_type

    @property
    def expired_at(self):
        """Gets the expired_at of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The expired_at of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_at

    @expired_at.setter
    def expired_at(self, expired_at):
        """Sets the expired_at of this InstanceForDescribeInstancesOutput.


        :param expired_at: The expired_at of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._expired_at = expired_at

    @property
    def host_name(self):
        """Gets the host_name of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The host_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._host_name

    @host_name.setter
    def host_name(self, host_name):
        """Sets the host_name of this InstanceForDescribeInstancesOutput.


        :param host_name: The host_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._host_name = host_name

    @property
    def hostname(self):
        """Gets the hostname of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The hostname of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this InstanceForDescribeInstancesOutput.


        :param hostname: The hostname of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def hpc_cluster_id(self):
        """Gets the hpc_cluster_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The hpc_cluster_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._hpc_cluster_id

    @hpc_cluster_id.setter
    def hpc_cluster_id(self, hpc_cluster_id):
        """Sets the hpc_cluster_id of this InstanceForDescribeInstancesOutput.


        :param hpc_cluster_id: The hpc_cluster_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._hpc_cluster_id = hpc_cluster_id

    @property
    def image_id(self):
        """Gets the image_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The image_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this InstanceForDescribeInstancesOutput.


        :param image_id: The image_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def instance_charge_type(self):
        """Gets the instance_charge_type of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_charge_type of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_charge_type

    @instance_charge_type.setter
    def instance_charge_type(self, instance_charge_type):
        """Sets the instance_charge_type of this InstanceForDescribeInstancesOutput.


        :param instance_charge_type: The instance_charge_type of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_charge_type = instance_charge_type

    @property
    def instance_id(self):
        """Gets the instance_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this InstanceForDescribeInstancesOutput.


        :param instance_id: The instance_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this InstanceForDescribeInstancesOutput.


        :param instance_name: The instance_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_type_id(self):
        """Gets the instance_type_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_type_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type_id

    @instance_type_id.setter
    def instance_type_id(self, instance_type_id):
        """Sets the instance_type_id of this InstanceForDescribeInstancesOutput.


        :param instance_type_id: The instance_type_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_type_id = instance_type_id

    @property
    def key_pair_id(self):
        """Gets the key_pair_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The key_pair_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._key_pair_id

    @key_pair_id.setter
    def key_pair_id(self, key_pair_id):
        """Sets the key_pair_id of this InstanceForDescribeInstancesOutput.


        :param key_pair_id: The key_pair_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._key_pair_id = key_pair_id

    @property
    def key_pair_name(self):
        """Gets the key_pair_name of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The key_pair_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._key_pair_name

    @key_pair_name.setter
    def key_pair_name(self, key_pair_name):
        """Sets the key_pair_name of this InstanceForDescribeInstancesOutput.


        :param key_pair_name: The key_pair_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._key_pair_name = key_pair_name

    @property
    def local_volumes(self):
        """Gets the local_volumes of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The local_volumes of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: list[LocalVolumeForDescribeInstancesOutput]
        """
        return self._local_volumes

    @local_volumes.setter
    def local_volumes(self, local_volumes):
        """Sets the local_volumes of this InstanceForDescribeInstancesOutput.


        :param local_volumes: The local_volumes of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: list[LocalVolumeForDescribeInstancesOutput]
        """

        self._local_volumes = local_volumes

    @property
    def memory_size(self):
        """Gets the memory_size of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The memory_size of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._memory_size

    @memory_size.setter
    def memory_size(self, memory_size):
        """Sets the memory_size of this InstanceForDescribeInstancesOutput.


        :param memory_size: The memory_size of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: int
        """

        self._memory_size = memory_size

    @property
    def network_interfaces(self):
        """Gets the network_interfaces of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The network_interfaces of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: list[NetworkInterfaceForDescribeInstancesOutput]
        """
        return self._network_interfaces

    @network_interfaces.setter
    def network_interfaces(self, network_interfaces):
        """Sets the network_interfaces of this InstanceForDescribeInstancesOutput.


        :param network_interfaces: The network_interfaces of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: list[NetworkInterfaceForDescribeInstancesOutput]
        """

        self._network_interfaces = network_interfaces

    @property
    def os_name(self):
        """Gets the os_name of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The os_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_name

    @os_name.setter
    def os_name(self, os_name):
        """Sets the os_name of this InstanceForDescribeInstancesOutput.


        :param os_name: The os_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._os_name = os_name

    @property
    def os_type(self):
        """Gets the os_type of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The os_type of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_type

    @os_type.setter
    def os_type(self, os_type):
        """Sets the os_type of this InstanceForDescribeInstancesOutput.


        :param os_type: The os_type of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._os_type = os_type

    @property
    def placement(self):
        """Gets the placement of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The placement of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: PlacementForDescribeInstancesOutput
        """
        return self._placement

    @placement.setter
    def placement(self, placement):
        """Sets the placement of this InstanceForDescribeInstancesOutput.


        :param placement: The placement of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: PlacementForDescribeInstancesOutput
        """

        self._placement = placement

    @property
    def project_name(self):
        """Gets the project_name of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The project_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this InstanceForDescribeInstancesOutput.


        :param project_name: The project_name of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def rdma_ip_addresses(self):
        """Gets the rdma_ip_addresses of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The rdma_ip_addresses of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._rdma_ip_addresses

    @rdma_ip_addresses.setter
    def rdma_ip_addresses(self, rdma_ip_addresses):
        """Sets the rdma_ip_addresses of this InstanceForDescribeInstancesOutput.


        :param rdma_ip_addresses: The rdma_ip_addresses of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: list[str]
        """

        self._rdma_ip_addresses = rdma_ip_addresses

    @property
    def scheduled_instance_id(self):
        """Gets the scheduled_instance_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The scheduled_instance_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._scheduled_instance_id

    @scheduled_instance_id.setter
    def scheduled_instance_id(self, scheduled_instance_id):
        """Sets the scheduled_instance_id of this InstanceForDescribeInstancesOutput.


        :param scheduled_instance_id: The scheduled_instance_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._scheduled_instance_id = scheduled_instance_id

    @property
    def spot_price_limit(self):
        """Gets the spot_price_limit of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The spot_price_limit of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: float
        """
        return self._spot_price_limit

    @spot_price_limit.setter
    def spot_price_limit(self, spot_price_limit):
        """Sets the spot_price_limit of this InstanceForDescribeInstancesOutput.


        :param spot_price_limit: The spot_price_limit of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: float
        """

        self._spot_price_limit = spot_price_limit

    @property
    def spot_strategy(self):
        """Gets the spot_strategy of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The spot_strategy of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._spot_strategy

    @spot_strategy.setter
    def spot_strategy(self, spot_strategy):
        """Sets the spot_strategy of this InstanceForDescribeInstancesOutput.


        :param spot_strategy: The spot_strategy of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._spot_strategy = spot_strategy

    @property
    def status(self):
        """Gets the status of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The status of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this InstanceForDescribeInstancesOutput.


        :param status: The status of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def stopped_mode(self):
        """Gets the stopped_mode of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The stopped_mode of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._stopped_mode

    @stopped_mode.setter
    def stopped_mode(self, stopped_mode):
        """Sets the stopped_mode of this InstanceForDescribeInstancesOutput.


        :param stopped_mode: The stopped_mode of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._stopped_mode = stopped_mode

    @property
    def tags(self):
        """Gets the tags of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The tags of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: list[TagForDescribeInstancesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this InstanceForDescribeInstancesOutput.


        :param tags: The tags of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: list[TagForDescribeInstancesOutput]
        """

        self._tags = tags

    @property
    def updated_at(self):
        """Gets the updated_at of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The updated_at of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this InstanceForDescribeInstancesOutput.


        :param updated_at: The updated_at of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def uuid(self):
        """Gets the uuid of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The uuid of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._uuid

    @uuid.setter
    def uuid(self, uuid):
        """Sets the uuid of this InstanceForDescribeInstancesOutput.


        :param uuid: The uuid of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._uuid = uuid

    @property
    def volumes(self):
        """Gets the volumes of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The volumes of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: list[VolumeForDescribeInstancesOutput]
        """
        return self._volumes

    @volumes.setter
    def volumes(self, volumes):
        """Sets the volumes of this InstanceForDescribeInstancesOutput.


        :param volumes: The volumes of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: list[VolumeForDescribeInstancesOutput]
        """

        self._volumes = volumes

    @property
    def vpc_id(self):
        """Gets the vpc_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The vpc_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this InstanceForDescribeInstancesOutput.


        :param vpc_id: The vpc_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The zone_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this InstanceForDescribeInstancesOutput.


        :param zone_id: The zone_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceForDescribeInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceForDescribeInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceForDescribeInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
