# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AuthConfigForExecContainerImageCommitmentInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'password': 'str',
        'plain_http': 'bool',
        'skip_tls_verify': 'bool',
        'username': 'str'
    }

    attribute_map = {
        'password': 'Password',
        'plain_http': 'PlainHTTP',
        'skip_tls_verify': 'SkipTLSVerify',
        'username': 'Username'
    }

    def __init__(self, password=None, plain_http=None, skip_tls_verify=None, username=None, _configuration=None):  # noqa: E501
        """AuthConfigForExecContainerImageCommitmentInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._password = None
        self._plain_http = None
        self._skip_tls_verify = None
        self._username = None
        self.discriminator = None

        if password is not None:
            self.password = password
        if plain_http is not None:
            self.plain_http = plain_http
        if skip_tls_verify is not None:
            self.skip_tls_verify = skip_tls_verify
        if username is not None:
            self.username = username

    @property
    def password(self):
        """Gets the password of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501


        :return: The password of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this AuthConfigForExecContainerImageCommitmentInput.


        :param password: The password of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501
        :type: str
        """

        self._password = password

    @property
    def plain_http(self):
        """Gets the plain_http of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501


        :return: The plain_http of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501
        :rtype: bool
        """
        return self._plain_http

    @plain_http.setter
    def plain_http(self, plain_http):
        """Sets the plain_http of this AuthConfigForExecContainerImageCommitmentInput.


        :param plain_http: The plain_http of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501
        :type: bool
        """

        self._plain_http = plain_http

    @property
    def skip_tls_verify(self):
        """Gets the skip_tls_verify of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501


        :return: The skip_tls_verify of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501
        :rtype: bool
        """
        return self._skip_tls_verify

    @skip_tls_verify.setter
    def skip_tls_verify(self, skip_tls_verify):
        """Sets the skip_tls_verify of this AuthConfigForExecContainerImageCommitmentInput.


        :param skip_tls_verify: The skip_tls_verify of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501
        :type: bool
        """

        self._skip_tls_verify = skip_tls_verify

    @property
    def username(self):
        """Gets the username of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501


        :return: The username of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this AuthConfigForExecContainerImageCommitmentInput.


        :param username: The username of this AuthConfigForExecContainerImageCommitmentInput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AuthConfigForExecContainerImageCommitmentInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AuthConfigForExecContainerImageCommitmentInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AuthConfigForExecContainerImageCommitmentInput):
            return True

        return self.to_dict() != other.to_dict()
