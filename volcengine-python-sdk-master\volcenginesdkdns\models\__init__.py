# coding: utf-8

# flake8: noqa
"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkdns.models.backup_info_for_list_user_zone_backups_output import BackupInfoForListUserZoneBackupsOutput
from volcenginesdkdns.models.batch_delete_custom_line_request import BatchDeleteCustomLineRequest
from volcenginesdkdns.models.batch_delete_custom_line_response import BatchDeleteCustomLineResponse
from volcenginesdkdns.models.check_retrieve_zone_request import CheckRetrieveZoneRequest
from volcenginesdkdns.models.check_retrieve_zone_response import CheckRetrieveZoneResponse
from volcenginesdkdns.models.check_zone_request import CheckZoneRequest
from volcenginesdkdns.models.check_zone_response import CheckZoneResponse
from volcenginesdkdns.models.create_custom_line_request import CreateCustomLineRequest
from volcenginesdkdns.models.create_custom_line_response import CreateCustomLineResponse
from volcenginesdkdns.models.create_record_request import CreateRecordRequest
from volcenginesdkdns.models.create_record_response import CreateRecordResponse
from volcenginesdkdns.models.create_user_zone_backup_request import CreateUserZoneBackupRequest
from volcenginesdkdns.models.create_user_zone_backup_response import CreateUserZoneBackupResponse
from volcenginesdkdns.models.create_zone_request import CreateZoneRequest
from volcenginesdkdns.models.create_zone_response import CreateZoneResponse
from volcenginesdkdns.models.customer_line_for_list_custom_lines_output import CustomerLineForListCustomLinesOutput
from volcenginesdkdns.models.data_for_list_domain_statistics_output import DataForListDomainStatisticsOutput
from volcenginesdkdns.models.data_for_list_zone_statistics_output import DataForListZoneStatisticsOutput
from volcenginesdkdns.models.delete_record_request import DeleteRecordRequest
from volcenginesdkdns.models.delete_record_response import DeleteRecordResponse
from volcenginesdkdns.models.delete_user_zone_backup_request import DeleteUserZoneBackupRequest
from volcenginesdkdns.models.delete_user_zone_backup_response import DeleteUserZoneBackupResponse
from volcenginesdkdns.models.delete_zone_request import DeleteZoneRequest
from volcenginesdkdns.models.delete_zone_response import DeleteZoneResponse
from volcenginesdkdns.models.line_for_list_lines_output import LineForListLinesOutput
from volcenginesdkdns.models.list_custom_lines_request import ListCustomLinesRequest
from volcenginesdkdns.models.list_custom_lines_response import ListCustomLinesResponse
from volcenginesdkdns.models.list_domain_statistics_request import ListDomainStatisticsRequest
from volcenginesdkdns.models.list_domain_statistics_response import ListDomainStatisticsResponse
from volcenginesdkdns.models.list_lines_request import ListLinesRequest
from volcenginesdkdns.models.list_lines_response import ListLinesResponse
from volcenginesdkdns.models.list_record_digest_by_line_request import ListRecordDigestByLineRequest
from volcenginesdkdns.models.list_record_digest_by_line_response import ListRecordDigestByLineResponse
from volcenginesdkdns.models.list_record_sets_request import ListRecordSetsRequest
from volcenginesdkdns.models.list_record_sets_response import ListRecordSetsResponse
from volcenginesdkdns.models.list_records_request import ListRecordsRequest
from volcenginesdkdns.models.list_records_response import ListRecordsResponse
from volcenginesdkdns.models.list_user_zone_backups_request import ListUserZoneBackupsRequest
from volcenginesdkdns.models.list_user_zone_backups_response import ListUserZoneBackupsResponse
from volcenginesdkdns.models.list_zone_statistics_request import ListZoneStatisticsRequest
from volcenginesdkdns.models.list_zone_statistics_response import ListZoneStatisticsResponse
from volcenginesdkdns.models.list_zones_request import ListZonesRequest
from volcenginesdkdns.models.list_zones_response import ListZonesResponse
from volcenginesdkdns.models.query_backup_schedule_request import QueryBackupScheduleRequest
from volcenginesdkdns.models.query_backup_schedule_response import QueryBackupScheduleResponse
from volcenginesdkdns.models.query_record_request import QueryRecordRequest
from volcenginesdkdns.models.query_record_response import QueryRecordResponse
from volcenginesdkdns.models.query_zone_request import QueryZoneRequest
from volcenginesdkdns.models.query_zone_response import QueryZoneResponse
from volcenginesdkdns.models.record_digest_for_list_record_digest_by_line_output import RecordDigestForListRecordDigestByLineOutput
from volcenginesdkdns.models.record_for_list_records_output import RecordForListRecordsOutput
from volcenginesdkdns.models.record_set_for_list_record_sets_output import RecordSetForListRecordSetsOutput
from volcenginesdkdns.models.restore_user_zone_backup_request import RestoreUserZoneBackupRequest
from volcenginesdkdns.models.restore_user_zone_backup_response import RestoreUserZoneBackupResponse
from volcenginesdkdns.models.retrieve_zone_request import RetrieveZoneRequest
from volcenginesdkdns.models.retrieve_zone_response import RetrieveZoneResponse
from volcenginesdkdns.models.tag_filter_for_list_zones_input import TagFilterForListZonesInput
from volcenginesdkdns.models.tag_for_create_zone_input import TagForCreateZoneInput
from volcenginesdkdns.models.tag_for_list_zones_output import TagForListZonesOutput
from volcenginesdkdns.models.update_backup_schedule_request import UpdateBackupScheduleRequest
from volcenginesdkdns.models.update_backup_schedule_response import UpdateBackupScheduleResponse
from volcenginesdkdns.models.update_custom_line_request import UpdateCustomLineRequest
from volcenginesdkdns.models.update_custom_line_response import UpdateCustomLineResponse
from volcenginesdkdns.models.update_record_request import UpdateRecordRequest
from volcenginesdkdns.models.update_record_response import UpdateRecordResponse
from volcenginesdkdns.models.update_record_set_request import UpdateRecordSetRequest
from volcenginesdkdns.models.update_record_set_response import UpdateRecordSetResponse
from volcenginesdkdns.models.update_record_status_request import UpdateRecordStatusRequest
from volcenginesdkdns.models.update_record_status_response import UpdateRecordStatusResponse
from volcenginesdkdns.models.update_zone_request import UpdateZoneRequest
from volcenginesdkdns.models.update_zone_response import UpdateZoneResponse
from volcenginesdkdns.models.zone_for_list_zones_output import ZoneForListZonesOutput
