# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MessageSendResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'msg_id': 'str',
        'offset_msg_id': 'str',
        'qeueue_offset': 'int',
        'send_status': 'str'
    }

    attribute_map = {
        'msg_id': 'MsgId',
        'offset_msg_id': 'OffsetMsgId',
        'qeueue_offset': 'QeueueOffset',
        'send_status': 'SendStatus'
    }

    def __init__(self, msg_id=None, offset_msg_id=None, qeueue_offset=None, send_status=None, _configuration=None):  # noqa: E501
        """MessageSendResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._msg_id = None
        self._offset_msg_id = None
        self._qeueue_offset = None
        self._send_status = None
        self.discriminator = None

        if msg_id is not None:
            self.msg_id = msg_id
        if offset_msg_id is not None:
            self.offset_msg_id = offset_msg_id
        if qeueue_offset is not None:
            self.qeueue_offset = qeueue_offset
        if send_status is not None:
            self.send_status = send_status

    @property
    def msg_id(self):
        """Gets the msg_id of this MessageSendResponse.  # noqa: E501


        :return: The msg_id of this MessageSendResponse.  # noqa: E501
        :rtype: str
        """
        return self._msg_id

    @msg_id.setter
    def msg_id(self, msg_id):
        """Sets the msg_id of this MessageSendResponse.


        :param msg_id: The msg_id of this MessageSendResponse.  # noqa: E501
        :type: str
        """

        self._msg_id = msg_id

    @property
    def offset_msg_id(self):
        """Gets the offset_msg_id of this MessageSendResponse.  # noqa: E501


        :return: The offset_msg_id of this MessageSendResponse.  # noqa: E501
        :rtype: str
        """
        return self._offset_msg_id

    @offset_msg_id.setter
    def offset_msg_id(self, offset_msg_id):
        """Sets the offset_msg_id of this MessageSendResponse.


        :param offset_msg_id: The offset_msg_id of this MessageSendResponse.  # noqa: E501
        :type: str
        """

        self._offset_msg_id = offset_msg_id

    @property
    def qeueue_offset(self):
        """Gets the qeueue_offset of this MessageSendResponse.  # noqa: E501


        :return: The qeueue_offset of this MessageSendResponse.  # noqa: E501
        :rtype: int
        """
        return self._qeueue_offset

    @qeueue_offset.setter
    def qeueue_offset(self, qeueue_offset):
        """Sets the qeueue_offset of this MessageSendResponse.


        :param qeueue_offset: The qeueue_offset of this MessageSendResponse.  # noqa: E501
        :type: int
        """

        self._qeueue_offset = qeueue_offset

    @property
    def send_status(self):
        """Gets the send_status of this MessageSendResponse.  # noqa: E501


        :return: The send_status of this MessageSendResponse.  # noqa: E501
        :rtype: str
        """
        return self._send_status

    @send_status.setter
    def send_status(self, send_status):
        """Sets the send_status of this MessageSendResponse.


        :param send_status: The send_status of this MessageSendResponse.  # noqa: E501
        :type: str
        """

        self._send_status = send_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MessageSendResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MessageSendResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MessageSendResponse):
            return True

        return self.to_dict() != other.to_dict()
