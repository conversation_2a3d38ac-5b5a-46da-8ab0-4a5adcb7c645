# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DedicatedHostClusterForDescribeDedicatedHostClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'dedicated_host_cluster_capacity': 'DedicatedHostClusterCapacityForDescribeDedicatedHostClustersOutput',
        'dedicated_host_cluster_id': 'str',
        'dedicated_host_cluster_name': 'str',
        'dedicated_host_ids': 'list[str]',
        'description': 'str',
        'updated_at': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'dedicated_host_cluster_capacity': 'DedicatedHostClusterCapacity',
        'dedicated_host_cluster_id': 'DedicatedHostClusterId',
        'dedicated_host_cluster_name': 'DedicatedHostClusterName',
        'dedicated_host_ids': 'DedicatedHostIds',
        'description': 'Description',
        'updated_at': 'UpdatedAt',
        'zone_id': 'ZoneId'
    }

    def __init__(self, created_at=None, dedicated_host_cluster_capacity=None, dedicated_host_cluster_id=None, dedicated_host_cluster_name=None, dedicated_host_ids=None, description=None, updated_at=None, zone_id=None, _configuration=None):  # noqa: E501
        """DedicatedHostClusterForDescribeDedicatedHostClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._dedicated_host_cluster_capacity = None
        self._dedicated_host_cluster_id = None
        self._dedicated_host_cluster_name = None
        self._dedicated_host_ids = None
        self._description = None
        self._updated_at = None
        self._zone_id = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if dedicated_host_cluster_capacity is not None:
            self.dedicated_host_cluster_capacity = dedicated_host_cluster_capacity
        if dedicated_host_cluster_id is not None:
            self.dedicated_host_cluster_id = dedicated_host_cluster_id
        if dedicated_host_cluster_name is not None:
            self.dedicated_host_cluster_name = dedicated_host_cluster_name
        if dedicated_host_ids is not None:
            self.dedicated_host_ids = dedicated_host_ids
        if description is not None:
            self.description = description
        if updated_at is not None:
            self.updated_at = updated_at
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def created_at(self):
        """Gets the created_at of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The created_at of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.


        :param created_at: The created_at of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def dedicated_host_cluster_capacity(self):
        """Gets the dedicated_host_cluster_capacity of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The dedicated_host_cluster_capacity of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: DedicatedHostClusterCapacityForDescribeDedicatedHostClustersOutput
        """
        return self._dedicated_host_cluster_capacity

    @dedicated_host_cluster_capacity.setter
    def dedicated_host_cluster_capacity(self, dedicated_host_cluster_capacity):
        """Sets the dedicated_host_cluster_capacity of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.


        :param dedicated_host_cluster_capacity: The dedicated_host_cluster_capacity of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: DedicatedHostClusterCapacityForDescribeDedicatedHostClustersOutput
        """

        self._dedicated_host_cluster_capacity = dedicated_host_cluster_capacity

    @property
    def dedicated_host_cluster_id(self):
        """Gets the dedicated_host_cluster_id of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The dedicated_host_cluster_id of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._dedicated_host_cluster_id

    @dedicated_host_cluster_id.setter
    def dedicated_host_cluster_id(self, dedicated_host_cluster_id):
        """Sets the dedicated_host_cluster_id of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.


        :param dedicated_host_cluster_id: The dedicated_host_cluster_id of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: str
        """

        self._dedicated_host_cluster_id = dedicated_host_cluster_id

    @property
    def dedicated_host_cluster_name(self):
        """Gets the dedicated_host_cluster_name of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The dedicated_host_cluster_name of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._dedicated_host_cluster_name

    @dedicated_host_cluster_name.setter
    def dedicated_host_cluster_name(self, dedicated_host_cluster_name):
        """Sets the dedicated_host_cluster_name of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.


        :param dedicated_host_cluster_name: The dedicated_host_cluster_name of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: str
        """

        self._dedicated_host_cluster_name = dedicated_host_cluster_name

    @property
    def dedicated_host_ids(self):
        """Gets the dedicated_host_ids of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The dedicated_host_ids of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._dedicated_host_ids

    @dedicated_host_ids.setter
    def dedicated_host_ids(self, dedicated_host_ids):
        """Sets the dedicated_host_ids of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.


        :param dedicated_host_ids: The dedicated_host_ids of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: list[str]
        """

        self._dedicated_host_ids = dedicated_host_ids

    @property
    def description(self):
        """Gets the description of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The description of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.


        :param description: The description of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def updated_at(self):
        """Gets the updated_at of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The updated_at of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.


        :param updated_at: The updated_at of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def zone_id(self):
        """Gets the zone_id of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The zone_id of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.


        :param zone_id: The zone_id of this DedicatedHostClusterForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DedicatedHostClusterForDescribeDedicatedHostClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DedicatedHostClusterForDescribeDedicatedHostClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DedicatedHostClusterForDescribeDedicatedHostClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
