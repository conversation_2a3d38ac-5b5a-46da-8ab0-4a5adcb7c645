# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DebugServerForListJobInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'private_url': 'str',
        'public_url': 'str',
        'state': 'str'
    }

    attribute_map = {
        'private_url': 'PrivateUrl',
        'public_url': 'PublicUrl',
        'state': 'State'
    }

    def __init__(self, private_url=None, public_url=None, state=None, _configuration=None):  # noqa: E501
        """DebugServerForListJobInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._private_url = None
        self._public_url = None
        self._state = None
        self.discriminator = None

        if private_url is not None:
            self.private_url = private_url
        if public_url is not None:
            self.public_url = public_url
        if state is not None:
            self.state = state

    @property
    def private_url(self):
        """Gets the private_url of this DebugServerForListJobInstancesOutput.  # noqa: E501


        :return: The private_url of this DebugServerForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_url

    @private_url.setter
    def private_url(self, private_url):
        """Sets the private_url of this DebugServerForListJobInstancesOutput.


        :param private_url: The private_url of this DebugServerForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._private_url = private_url

    @property
    def public_url(self):
        """Gets the public_url of this DebugServerForListJobInstancesOutput.  # noqa: E501


        :return: The public_url of this DebugServerForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_url

    @public_url.setter
    def public_url(self, public_url):
        """Sets the public_url of this DebugServerForListJobInstancesOutput.


        :param public_url: The public_url of this DebugServerForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._public_url = public_url

    @property
    def state(self):
        """Gets the state of this DebugServerForListJobInstancesOutput.  # noqa: E501


        :return: The state of this DebugServerForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this DebugServerForListJobInstancesOutput.


        :param state: The state of this DebugServerForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DebugServerForListJobInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DebugServerForListJobInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DebugServerForListJobInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
