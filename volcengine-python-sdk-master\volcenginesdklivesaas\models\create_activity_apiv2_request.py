# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateActivityAPIV2Request(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_type': 'int',
        'copy_stream': 'bool',
        'cover_image': 'str',
        'creator_name': 'str',
        'end_time': 'int',
        'is_answer_repetition_enable': 'int',
        'is_auto_end_enable': 'int',
        'is_auto_start_enable': 'int',
        'is_replay_auto_online_enable': 'int',
        'live_layout': 'int',
        'live_mode': 'int',
        'live_time': 'int',
        'loop_videos': 'list[LoopVideoForCreateActivityAPIV2Input]',
        'name': 'str',
        'old_id': 'int',
        'site_tags': 'list[SiteTagForCreateActivityAPIV2Input]',
        'template_id': 'int',
        'text_site_tags': 'list[TextSiteTagForCreateActivityAPIV2Input]',
        'vertical_cover_image': 'str',
        'vid': 'str',
        'view_url_path': 'str'
    }

    attribute_map = {
        'activity_type': 'ActivityType',
        'copy_stream': 'CopyStream',
        'cover_image': 'CoverImage',
        'creator_name': 'CreatorName',
        'end_time': 'EndTime',
        'is_answer_repetition_enable': 'IsAnswerRepetitionEnable',
        'is_auto_end_enable': 'IsAutoEndEnable',
        'is_auto_start_enable': 'IsAutoStartEnable',
        'is_replay_auto_online_enable': 'IsReplayAutoOnlineEnable',
        'live_layout': 'LiveLayout',
        'live_mode': 'LiveMode',
        'live_time': 'LiveTime',
        'loop_videos': 'LoopVideos',
        'name': 'Name',
        'old_id': 'OldId',
        'site_tags': 'SiteTags',
        'template_id': 'TemplateId',
        'text_site_tags': 'TextSiteTags',
        'vertical_cover_image': 'VerticalCoverImage',
        'vid': 'Vid',
        'view_url_path': 'ViewUrlPath'
    }

    def __init__(self, activity_type=None, copy_stream=None, cover_image=None, creator_name=None, end_time=None, is_answer_repetition_enable=None, is_auto_end_enable=None, is_auto_start_enable=None, is_replay_auto_online_enable=None, live_layout=None, live_mode=None, live_time=None, loop_videos=None, name=None, old_id=None, site_tags=None, template_id=None, text_site_tags=None, vertical_cover_image=None, vid=None, view_url_path=None, _configuration=None):  # noqa: E501
        """CreateActivityAPIV2Request - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_type = None
        self._copy_stream = None
        self._cover_image = None
        self._creator_name = None
        self._end_time = None
        self._is_answer_repetition_enable = None
        self._is_auto_end_enable = None
        self._is_auto_start_enable = None
        self._is_replay_auto_online_enable = None
        self._live_layout = None
        self._live_mode = None
        self._live_time = None
        self._loop_videos = None
        self._name = None
        self._old_id = None
        self._site_tags = None
        self._template_id = None
        self._text_site_tags = None
        self._vertical_cover_image = None
        self._vid = None
        self._view_url_path = None
        self.discriminator = None

        if activity_type is not None:
            self.activity_type = activity_type
        if copy_stream is not None:
            self.copy_stream = copy_stream
        if cover_image is not None:
            self.cover_image = cover_image
        if creator_name is not None:
            self.creator_name = creator_name
        if end_time is not None:
            self.end_time = end_time
        if is_answer_repetition_enable is not None:
            self.is_answer_repetition_enable = is_answer_repetition_enable
        if is_auto_end_enable is not None:
            self.is_auto_end_enable = is_auto_end_enable
        if is_auto_start_enable is not None:
            self.is_auto_start_enable = is_auto_start_enable
        if is_replay_auto_online_enable is not None:
            self.is_replay_auto_online_enable = is_replay_auto_online_enable
        if live_layout is not None:
            self.live_layout = live_layout
        if live_mode is not None:
            self.live_mode = live_mode
        if live_time is not None:
            self.live_time = live_time
        if loop_videos is not None:
            self.loop_videos = loop_videos
        self.name = name
        if old_id is not None:
            self.old_id = old_id
        if site_tags is not None:
            self.site_tags = site_tags
        if template_id is not None:
            self.template_id = template_id
        if text_site_tags is not None:
            self.text_site_tags = text_site_tags
        if vertical_cover_image is not None:
            self.vertical_cover_image = vertical_cover_image
        if vid is not None:
            self.vid = vid
        if view_url_path is not None:
            self.view_url_path = view_url_path

    @property
    def activity_type(self):
        """Gets the activity_type of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The activity_type of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._activity_type

    @activity_type.setter
    def activity_type(self, activity_type):
        """Sets the activity_type of this CreateActivityAPIV2Request.


        :param activity_type: The activity_type of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._activity_type = activity_type

    @property
    def copy_stream(self):
        """Gets the copy_stream of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The copy_stream of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: bool
        """
        return self._copy_stream

    @copy_stream.setter
    def copy_stream(self, copy_stream):
        """Sets the copy_stream of this CreateActivityAPIV2Request.


        :param copy_stream: The copy_stream of this CreateActivityAPIV2Request.  # noqa: E501
        :type: bool
        """

        self._copy_stream = copy_stream

    @property
    def cover_image(self):
        """Gets the cover_image of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The cover_image of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._cover_image

    @cover_image.setter
    def cover_image(self, cover_image):
        """Sets the cover_image of this CreateActivityAPIV2Request.


        :param cover_image: The cover_image of this CreateActivityAPIV2Request.  # noqa: E501
        :type: str
        """

        self._cover_image = cover_image

    @property
    def creator_name(self):
        """Gets the creator_name of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The creator_name of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._creator_name

    @creator_name.setter
    def creator_name(self, creator_name):
        """Sets the creator_name of this CreateActivityAPIV2Request.


        :param creator_name: The creator_name of this CreateActivityAPIV2Request.  # noqa: E501
        :type: str
        """

        self._creator_name = creator_name

    @property
    def end_time(self):
        """Gets the end_time of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The end_time of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this CreateActivityAPIV2Request.


        :param end_time: The end_time of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def is_answer_repetition_enable(self):
        """Gets the is_answer_repetition_enable of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The is_answer_repetition_enable of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._is_answer_repetition_enable

    @is_answer_repetition_enable.setter
    def is_answer_repetition_enable(self, is_answer_repetition_enable):
        """Sets the is_answer_repetition_enable of this CreateActivityAPIV2Request.


        :param is_answer_repetition_enable: The is_answer_repetition_enable of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._is_answer_repetition_enable = is_answer_repetition_enable

    @property
    def is_auto_end_enable(self):
        """Gets the is_auto_end_enable of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The is_auto_end_enable of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._is_auto_end_enable

    @is_auto_end_enable.setter
    def is_auto_end_enable(self, is_auto_end_enable):
        """Sets the is_auto_end_enable of this CreateActivityAPIV2Request.


        :param is_auto_end_enable: The is_auto_end_enable of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._is_auto_end_enable = is_auto_end_enable

    @property
    def is_auto_start_enable(self):
        """Gets the is_auto_start_enable of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The is_auto_start_enable of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._is_auto_start_enable

    @is_auto_start_enable.setter
    def is_auto_start_enable(self, is_auto_start_enable):
        """Sets the is_auto_start_enable of this CreateActivityAPIV2Request.


        :param is_auto_start_enable: The is_auto_start_enable of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._is_auto_start_enable = is_auto_start_enable

    @property
    def is_replay_auto_online_enable(self):
        """Gets the is_replay_auto_online_enable of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The is_replay_auto_online_enable of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._is_replay_auto_online_enable

    @is_replay_auto_online_enable.setter
    def is_replay_auto_online_enable(self, is_replay_auto_online_enable):
        """Sets the is_replay_auto_online_enable of this CreateActivityAPIV2Request.


        :param is_replay_auto_online_enable: The is_replay_auto_online_enable of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._is_replay_auto_online_enable = is_replay_auto_online_enable

    @property
    def live_layout(self):
        """Gets the live_layout of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The live_layout of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._live_layout

    @live_layout.setter
    def live_layout(self, live_layout):
        """Sets the live_layout of this CreateActivityAPIV2Request.


        :param live_layout: The live_layout of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._live_layout = live_layout

    @property
    def live_mode(self):
        """Gets the live_mode of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The live_mode of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._live_mode

    @live_mode.setter
    def live_mode(self, live_mode):
        """Sets the live_mode of this CreateActivityAPIV2Request.


        :param live_mode: The live_mode of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._live_mode = live_mode

    @property
    def live_time(self):
        """Gets the live_time of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The live_time of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._live_time

    @live_time.setter
    def live_time(self, live_time):
        """Sets the live_time of this CreateActivityAPIV2Request.


        :param live_time: The live_time of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._live_time = live_time

    @property
    def loop_videos(self):
        """Gets the loop_videos of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The loop_videos of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: list[LoopVideoForCreateActivityAPIV2Input]
        """
        return self._loop_videos

    @loop_videos.setter
    def loop_videos(self, loop_videos):
        """Sets the loop_videos of this CreateActivityAPIV2Request.


        :param loop_videos: The loop_videos of this CreateActivityAPIV2Request.  # noqa: E501
        :type: list[LoopVideoForCreateActivityAPIV2Input]
        """

        self._loop_videos = loop_videos

    @property
    def name(self):
        """Gets the name of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The name of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateActivityAPIV2Request.


        :param name: The name of this CreateActivityAPIV2Request.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def old_id(self):
        """Gets the old_id of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The old_id of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._old_id

    @old_id.setter
    def old_id(self, old_id):
        """Sets the old_id of this CreateActivityAPIV2Request.


        :param old_id: The old_id of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._old_id = old_id

    @property
    def site_tags(self):
        """Gets the site_tags of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The site_tags of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: list[SiteTagForCreateActivityAPIV2Input]
        """
        return self._site_tags

    @site_tags.setter
    def site_tags(self, site_tags):
        """Sets the site_tags of this CreateActivityAPIV2Request.


        :param site_tags: The site_tags of this CreateActivityAPIV2Request.  # noqa: E501
        :type: list[SiteTagForCreateActivityAPIV2Input]
        """

        self._site_tags = site_tags

    @property
    def template_id(self):
        """Gets the template_id of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The template_id of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: int
        """
        return self._template_id

    @template_id.setter
    def template_id(self, template_id):
        """Sets the template_id of this CreateActivityAPIV2Request.


        :param template_id: The template_id of this CreateActivityAPIV2Request.  # noqa: E501
        :type: int
        """

        self._template_id = template_id

    @property
    def text_site_tags(self):
        """Gets the text_site_tags of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The text_site_tags of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: list[TextSiteTagForCreateActivityAPIV2Input]
        """
        return self._text_site_tags

    @text_site_tags.setter
    def text_site_tags(self, text_site_tags):
        """Sets the text_site_tags of this CreateActivityAPIV2Request.


        :param text_site_tags: The text_site_tags of this CreateActivityAPIV2Request.  # noqa: E501
        :type: list[TextSiteTagForCreateActivityAPIV2Input]
        """

        self._text_site_tags = text_site_tags

    @property
    def vertical_cover_image(self):
        """Gets the vertical_cover_image of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The vertical_cover_image of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._vertical_cover_image

    @vertical_cover_image.setter
    def vertical_cover_image(self, vertical_cover_image):
        """Sets the vertical_cover_image of this CreateActivityAPIV2Request.


        :param vertical_cover_image: The vertical_cover_image of this CreateActivityAPIV2Request.  # noqa: E501
        :type: str
        """

        self._vertical_cover_image = vertical_cover_image

    @property
    def vid(self):
        """Gets the vid of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The vid of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._vid

    @vid.setter
    def vid(self, vid):
        """Sets the vid of this CreateActivityAPIV2Request.


        :param vid: The vid of this CreateActivityAPIV2Request.  # noqa: E501
        :type: str
        """

        self._vid = vid

    @property
    def view_url_path(self):
        """Gets the view_url_path of this CreateActivityAPIV2Request.  # noqa: E501


        :return: The view_url_path of this CreateActivityAPIV2Request.  # noqa: E501
        :rtype: str
        """
        return self._view_url_path

    @view_url_path.setter
    def view_url_path(self, view_url_path):
        """Sets the view_url_path of this CreateActivityAPIV2Request.


        :param view_url_path: The view_url_path of this CreateActivityAPIV2Request.  # noqa: E501
        :type: str
        """

        self._view_url_path = view_url_path

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateActivityAPIV2Request, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateActivityAPIV2Request):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateActivityAPIV2Request):
            return True

        return self.to_dict() != other.to_dict()
