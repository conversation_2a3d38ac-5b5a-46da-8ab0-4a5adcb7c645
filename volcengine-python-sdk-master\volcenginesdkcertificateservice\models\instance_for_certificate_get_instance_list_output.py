# coding: utf-8

"""
    certificate_service

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceForCertificateGetInstanceListOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'certificate_domain_type': 'str',
        'certificate_key_algorithm': 'str',
        'common_name': 'str',
        'created_time': 'str',
        'instance_id': 'str',
        'instance_level': 'str',
        'instance_type': 'str',
        'is_certificate_revoked': 'bool',
        'is_certificate_sm': 'bool',
        'issuer': 'str',
        'not_after': 'str',
        'not_before': 'str',
        'order_brand': 'str',
        'order_period': 'int',
        'project_name': 'str',
        'san': 'list[str]',
        'source_id': 'str',
        'status': 'str',
        'tag': 'str',
        'tags': 'list[TagForCertificateGetInstanceListOutput]'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'certificate_domain_type': 'CertificateDomainType',
        'certificate_key_algorithm': 'CertificateKeyAlgorithm',
        'common_name': 'CommonName',
        'created_time': 'CreatedTime',
        'instance_id': 'InstanceId',
        'instance_level': 'InstanceLevel',
        'instance_type': 'InstanceType',
        'is_certificate_revoked': 'IsCertificateRevoked',
        'is_certificate_sm': 'IsCertificateSm',
        'issuer': 'Issuer',
        'not_after': 'NotAfter',
        'not_before': 'NotBefore',
        'order_brand': 'OrderBrand',
        'order_period': 'OrderPeriod',
        'project_name': 'ProjectName',
        'san': 'San',
        'source_id': 'SourceId',
        'status': 'Status',
        'tag': 'Tag',
        'tags': 'Tags'
    }

    def __init__(self, account_id=None, certificate_domain_type=None, certificate_key_algorithm=None, common_name=None, created_time=None, instance_id=None, instance_level=None, instance_type=None, is_certificate_revoked=None, is_certificate_sm=None, issuer=None, not_after=None, not_before=None, order_brand=None, order_period=None, project_name=None, san=None, source_id=None, status=None, tag=None, tags=None, _configuration=None):  # noqa: E501
        """InstanceForCertificateGetInstanceListOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._certificate_domain_type = None
        self._certificate_key_algorithm = None
        self._common_name = None
        self._created_time = None
        self._instance_id = None
        self._instance_level = None
        self._instance_type = None
        self._is_certificate_revoked = None
        self._is_certificate_sm = None
        self._issuer = None
        self._not_after = None
        self._not_before = None
        self._order_brand = None
        self._order_period = None
        self._project_name = None
        self._san = None
        self._source_id = None
        self._status = None
        self._tag = None
        self._tags = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if certificate_domain_type is not None:
            self.certificate_domain_type = certificate_domain_type
        if certificate_key_algorithm is not None:
            self.certificate_key_algorithm = certificate_key_algorithm
        if common_name is not None:
            self.common_name = common_name
        if created_time is not None:
            self.created_time = created_time
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_level is not None:
            self.instance_level = instance_level
        if instance_type is not None:
            self.instance_type = instance_type
        if is_certificate_revoked is not None:
            self.is_certificate_revoked = is_certificate_revoked
        if is_certificate_sm is not None:
            self.is_certificate_sm = is_certificate_sm
        if issuer is not None:
            self.issuer = issuer
        if not_after is not None:
            self.not_after = not_after
        if not_before is not None:
            self.not_before = not_before
        if order_brand is not None:
            self.order_brand = order_brand
        if order_period is not None:
            self.order_period = order_period
        if project_name is not None:
            self.project_name = project_name
        if san is not None:
            self.san = san
        if source_id is not None:
            self.source_id = source_id
        if status is not None:
            self.status = status
        if tag is not None:
            self.tag = tag
        if tags is not None:
            self.tags = tags

    @property
    def account_id(self):
        """Gets the account_id of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The account_id of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this InstanceForCertificateGetInstanceListOutput.


        :param account_id: The account_id of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def certificate_domain_type(self):
        """Gets the certificate_domain_type of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The certificate_domain_type of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._certificate_domain_type

    @certificate_domain_type.setter
    def certificate_domain_type(self, certificate_domain_type):
        """Sets the certificate_domain_type of this InstanceForCertificateGetInstanceListOutput.


        :param certificate_domain_type: The certificate_domain_type of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._certificate_domain_type = certificate_domain_type

    @property
    def certificate_key_algorithm(self):
        """Gets the certificate_key_algorithm of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The certificate_key_algorithm of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._certificate_key_algorithm

    @certificate_key_algorithm.setter
    def certificate_key_algorithm(self, certificate_key_algorithm):
        """Sets the certificate_key_algorithm of this InstanceForCertificateGetInstanceListOutput.


        :param certificate_key_algorithm: The certificate_key_algorithm of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._certificate_key_algorithm = certificate_key_algorithm

    @property
    def common_name(self):
        """Gets the common_name of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The common_name of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._common_name

    @common_name.setter
    def common_name(self, common_name):
        """Sets the common_name of this InstanceForCertificateGetInstanceListOutput.


        :param common_name: The common_name of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._common_name = common_name

    @property
    def created_time(self):
        """Gets the created_time of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The created_time of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this InstanceForCertificateGetInstanceListOutput.


        :param created_time: The created_time of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def instance_id(self):
        """Gets the instance_id of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The instance_id of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this InstanceForCertificateGetInstanceListOutput.


        :param instance_id: The instance_id of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_level(self):
        """Gets the instance_level of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The instance_level of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_level

    @instance_level.setter
    def instance_level(self, instance_level):
        """Sets the instance_level of this InstanceForCertificateGetInstanceListOutput.


        :param instance_level: The instance_level of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._instance_level = instance_level

    @property
    def instance_type(self):
        """Gets the instance_type of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The instance_type of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this InstanceForCertificateGetInstanceListOutput.


        :param instance_type: The instance_type of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    @property
    def is_certificate_revoked(self):
        """Gets the is_certificate_revoked of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The is_certificate_revoked of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_certificate_revoked

    @is_certificate_revoked.setter
    def is_certificate_revoked(self, is_certificate_revoked):
        """Sets the is_certificate_revoked of this InstanceForCertificateGetInstanceListOutput.


        :param is_certificate_revoked: The is_certificate_revoked of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: bool
        """

        self._is_certificate_revoked = is_certificate_revoked

    @property
    def is_certificate_sm(self):
        """Gets the is_certificate_sm of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The is_certificate_sm of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_certificate_sm

    @is_certificate_sm.setter
    def is_certificate_sm(self, is_certificate_sm):
        """Sets the is_certificate_sm of this InstanceForCertificateGetInstanceListOutput.


        :param is_certificate_sm: The is_certificate_sm of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: bool
        """

        self._is_certificate_sm = is_certificate_sm

    @property
    def issuer(self):
        """Gets the issuer of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The issuer of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._issuer

    @issuer.setter
    def issuer(self, issuer):
        """Sets the issuer of this InstanceForCertificateGetInstanceListOutput.


        :param issuer: The issuer of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._issuer = issuer

    @property
    def not_after(self):
        """Gets the not_after of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The not_after of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._not_after

    @not_after.setter
    def not_after(self, not_after):
        """Sets the not_after of this InstanceForCertificateGetInstanceListOutput.


        :param not_after: The not_after of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._not_after = not_after

    @property
    def not_before(self):
        """Gets the not_before of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The not_before of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._not_before

    @not_before.setter
    def not_before(self, not_before):
        """Sets the not_before of this InstanceForCertificateGetInstanceListOutput.


        :param not_before: The not_before of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._not_before = not_before

    @property
    def order_brand(self):
        """Gets the order_brand of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The order_brand of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._order_brand

    @order_brand.setter
    def order_brand(self, order_brand):
        """Sets the order_brand of this InstanceForCertificateGetInstanceListOutput.


        :param order_brand: The order_brand of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._order_brand = order_brand

    @property
    def order_period(self):
        """Gets the order_period of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The order_period of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: int
        """
        return self._order_period

    @order_period.setter
    def order_period(self, order_period):
        """Sets the order_period of this InstanceForCertificateGetInstanceListOutput.


        :param order_period: The order_period of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: int
        """

        self._order_period = order_period

    @property
    def project_name(self):
        """Gets the project_name of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The project_name of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this InstanceForCertificateGetInstanceListOutput.


        :param project_name: The project_name of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def san(self):
        """Gets the san of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The san of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._san

    @san.setter
    def san(self, san):
        """Sets the san of this InstanceForCertificateGetInstanceListOutput.


        :param san: The san of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: list[str]
        """

        self._san = san

    @property
    def source_id(self):
        """Gets the source_id of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The source_id of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_id

    @source_id.setter
    def source_id(self, source_id):
        """Sets the source_id of this InstanceForCertificateGetInstanceListOutput.


        :param source_id: The source_id of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._source_id = source_id

    @property
    def status(self):
        """Gets the status of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The status of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this InstanceForCertificateGetInstanceListOutput.


        :param status: The status of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tag(self):
        """Gets the tag of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The tag of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this InstanceForCertificateGetInstanceListOutput.


        :param tag: The tag of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def tags(self):
        """Gets the tags of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501


        :return: The tags of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :rtype: list[TagForCertificateGetInstanceListOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this InstanceForCertificateGetInstanceListOutput.


        :param tags: The tags of this InstanceForCertificateGetInstanceListOutput.  # noqa: E501
        :type: list[TagForCertificateGetInstanceListOutput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceForCertificateGetInstanceListOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceForCertificateGetInstanceListOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceForCertificateGetInstanceListOutput):
            return True

        return self.to_dict() != other.to_dict()
