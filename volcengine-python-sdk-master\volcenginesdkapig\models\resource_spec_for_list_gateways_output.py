# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceSpecForListGatewaysOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'clb_spec_code': 'str',
        'instance_spec_code': 'str',
        'network_type': 'NetworkTypeForListGatewaysOutput',
        'public_network_bandwidth': 'int',
        'public_network_billing_type': 'str',
        'replicas': 'int'
    }

    attribute_map = {
        'clb_spec_code': 'CLBSpecCode',
        'instance_spec_code': 'InstanceSpecCode',
        'network_type': 'NetworkType',
        'public_network_bandwidth': 'PublicNetworkBandwidth',
        'public_network_billing_type': 'PublicNetworkBillingType',
        'replicas': 'Replicas'
    }

    def __init__(self, clb_spec_code=None, instance_spec_code=None, network_type=None, public_network_bandwidth=None, public_network_billing_type=None, replicas=None, _configuration=None):  # noqa: E501
        """ResourceSpecForListGatewaysOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._clb_spec_code = None
        self._instance_spec_code = None
        self._network_type = None
        self._public_network_bandwidth = None
        self._public_network_billing_type = None
        self._replicas = None
        self.discriminator = None

        if clb_spec_code is not None:
            self.clb_spec_code = clb_spec_code
        if instance_spec_code is not None:
            self.instance_spec_code = instance_spec_code
        if network_type is not None:
            self.network_type = network_type
        if public_network_bandwidth is not None:
            self.public_network_bandwidth = public_network_bandwidth
        if public_network_billing_type is not None:
            self.public_network_billing_type = public_network_billing_type
        if replicas is not None:
            self.replicas = replicas

    @property
    def clb_spec_code(self):
        """Gets the clb_spec_code of this ResourceSpecForListGatewaysOutput.  # noqa: E501


        :return: The clb_spec_code of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._clb_spec_code

    @clb_spec_code.setter
    def clb_spec_code(self, clb_spec_code):
        """Sets the clb_spec_code of this ResourceSpecForListGatewaysOutput.


        :param clb_spec_code: The clb_spec_code of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._clb_spec_code = clb_spec_code

    @property
    def instance_spec_code(self):
        """Gets the instance_spec_code of this ResourceSpecForListGatewaysOutput.  # noqa: E501


        :return: The instance_spec_code of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_spec_code

    @instance_spec_code.setter
    def instance_spec_code(self, instance_spec_code):
        """Sets the instance_spec_code of this ResourceSpecForListGatewaysOutput.


        :param instance_spec_code: The instance_spec_code of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._instance_spec_code = instance_spec_code

    @property
    def network_type(self):
        """Gets the network_type of this ResourceSpecForListGatewaysOutput.  # noqa: E501


        :return: The network_type of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :rtype: NetworkTypeForListGatewaysOutput
        """
        return self._network_type

    @network_type.setter
    def network_type(self, network_type):
        """Sets the network_type of this ResourceSpecForListGatewaysOutput.


        :param network_type: The network_type of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :type: NetworkTypeForListGatewaysOutput
        """

        self._network_type = network_type

    @property
    def public_network_bandwidth(self):
        """Gets the public_network_bandwidth of this ResourceSpecForListGatewaysOutput.  # noqa: E501


        :return: The public_network_bandwidth of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :rtype: int
        """
        return self._public_network_bandwidth

    @public_network_bandwidth.setter
    def public_network_bandwidth(self, public_network_bandwidth):
        """Sets the public_network_bandwidth of this ResourceSpecForListGatewaysOutput.


        :param public_network_bandwidth: The public_network_bandwidth of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :type: int
        """

        self._public_network_bandwidth = public_network_bandwidth

    @property
    def public_network_billing_type(self):
        """Gets the public_network_billing_type of this ResourceSpecForListGatewaysOutput.  # noqa: E501


        :return: The public_network_billing_type of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_network_billing_type

    @public_network_billing_type.setter
    def public_network_billing_type(self, public_network_billing_type):
        """Sets the public_network_billing_type of this ResourceSpecForListGatewaysOutput.


        :param public_network_billing_type: The public_network_billing_type of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :type: str
        """

        self._public_network_billing_type = public_network_billing_type

    @property
    def replicas(self):
        """Gets the replicas of this ResourceSpecForListGatewaysOutput.  # noqa: E501


        :return: The replicas of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :rtype: int
        """
        return self._replicas

    @replicas.setter
    def replicas(self, replicas):
        """Sets the replicas of this ResourceSpecForListGatewaysOutput.


        :param replicas: The replicas of this ResourceSpecForListGatewaysOutput.  # noqa: E501
        :type: int
        """

        self._replicas = replicas

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceSpecForListGatewaysOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceSpecForListGatewaysOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceSpecForListGatewaysOutput):
            return True

        return self.to_dict() != other.to_dict()
