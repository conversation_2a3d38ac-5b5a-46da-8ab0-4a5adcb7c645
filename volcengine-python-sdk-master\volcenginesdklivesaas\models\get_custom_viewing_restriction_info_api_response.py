# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetCustomViewingRestrictionInfoAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'custom_viewing_restriction': 'list[CustomViewingRestrictionForGetCustomViewingRestrictionInfoAPIOutput]',
        'main_viewing_restriction_button_title': 'str',
        'sub_viewing_restriction_button_title': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'custom_viewing_restriction': 'CustomViewingRestriction',
        'main_viewing_restriction_button_title': 'MainViewingRestrictionButtonTitle',
        'sub_viewing_restriction_button_title': 'SubViewingRestrictionButtonTitle'
    }

    def __init__(self, activity_id=None, custom_viewing_restriction=None, main_viewing_restriction_button_title=None, sub_viewing_restriction_button_title=None, _configuration=None):  # noqa: E501
        """GetCustomViewingRestrictionInfoAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._custom_viewing_restriction = None
        self._main_viewing_restriction_button_title = None
        self._sub_viewing_restriction_button_title = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if custom_viewing_restriction is not None:
            self.custom_viewing_restriction = custom_viewing_restriction
        if main_viewing_restriction_button_title is not None:
            self.main_viewing_restriction_button_title = main_viewing_restriction_button_title
        if sub_viewing_restriction_button_title is not None:
            self.sub_viewing_restriction_button_title = sub_viewing_restriction_button_title

    @property
    def activity_id(self):
        """Gets the activity_id of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501


        :return: The activity_id of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetCustomViewingRestrictionInfoAPIResponse.


        :param activity_id: The activity_id of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def custom_viewing_restriction(self):
        """Gets the custom_viewing_restriction of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501


        :return: The custom_viewing_restriction of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501
        :rtype: list[CustomViewingRestrictionForGetCustomViewingRestrictionInfoAPIOutput]
        """
        return self._custom_viewing_restriction

    @custom_viewing_restriction.setter
    def custom_viewing_restriction(self, custom_viewing_restriction):
        """Sets the custom_viewing_restriction of this GetCustomViewingRestrictionInfoAPIResponse.


        :param custom_viewing_restriction: The custom_viewing_restriction of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501
        :type: list[CustomViewingRestrictionForGetCustomViewingRestrictionInfoAPIOutput]
        """

        self._custom_viewing_restriction = custom_viewing_restriction

    @property
    def main_viewing_restriction_button_title(self):
        """Gets the main_viewing_restriction_button_title of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501


        :return: The main_viewing_restriction_button_title of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._main_viewing_restriction_button_title

    @main_viewing_restriction_button_title.setter
    def main_viewing_restriction_button_title(self, main_viewing_restriction_button_title):
        """Sets the main_viewing_restriction_button_title of this GetCustomViewingRestrictionInfoAPIResponse.


        :param main_viewing_restriction_button_title: The main_viewing_restriction_button_title of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501
        :type: str
        """

        self._main_viewing_restriction_button_title = main_viewing_restriction_button_title

    @property
    def sub_viewing_restriction_button_title(self):
        """Gets the sub_viewing_restriction_button_title of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501


        :return: The sub_viewing_restriction_button_title of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501
        :rtype: str
        """
        return self._sub_viewing_restriction_button_title

    @sub_viewing_restriction_button_title.setter
    def sub_viewing_restriction_button_title(self, sub_viewing_restriction_button_title):
        """Sets the sub_viewing_restriction_button_title of this GetCustomViewingRestrictionInfoAPIResponse.


        :param sub_viewing_restriction_button_title: The sub_viewing_restriction_button_title of this GetCustomViewingRestrictionInfoAPIResponse.  # noqa: E501
        :type: str
        """

        self._sub_viewing_restriction_button_title = sub_viewing_restriction_button_title

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetCustomViewingRestrictionInfoAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetCustomViewingRestrictionInfoAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetCustomViewingRestrictionInfoAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
