# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TextSiteTagForListActivityByCacheAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'db_index': 'int',
        'id': 'int',
        'index': 'int',
        'name': 'str',
        'show': 'int',
        'value': 'str'
    }

    attribute_map = {
        'db_index': 'DbIndex',
        'id': 'Id',
        'index': 'Index',
        'name': 'Name',
        'show': 'Show',
        'value': 'Value'
    }

    def __init__(self, db_index=None, id=None, index=None, name=None, show=None, value=None, _configuration=None):  # noqa: E501
        """TextSiteTagForListActivityByCacheAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._db_index = None
        self._id = None
        self._index = None
        self._name = None
        self._show = None
        self._value = None
        self.discriminator = None

        if db_index is not None:
            self.db_index = db_index
        if id is not None:
            self.id = id
        if index is not None:
            self.index = index
        if name is not None:
            self.name = name
        if show is not None:
            self.show = show
        if value is not None:
            self.value = value

    @property
    def db_index(self):
        """Gets the db_index of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501


        :return: The db_index of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._db_index

    @db_index.setter
    def db_index(self, db_index):
        """Sets the db_index of this TextSiteTagForListActivityByCacheAPIOutput.


        :param db_index: The db_index of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :type: int
        """

        self._db_index = db_index

    @property
    def id(self):
        """Gets the id of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501


        :return: The id of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this TextSiteTagForListActivityByCacheAPIOutput.


        :param id: The id of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def index(self):
        """Gets the index of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501


        :return: The index of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._index

    @index.setter
    def index(self, index):
        """Sets the index of this TextSiteTagForListActivityByCacheAPIOutput.


        :param index: The index of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :type: int
        """

        self._index = index

    @property
    def name(self):
        """Gets the name of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501


        :return: The name of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this TextSiteTagForListActivityByCacheAPIOutput.


        :param name: The name of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def show(self):
        """Gets the show of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501


        :return: The show of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._show

    @show.setter
    def show(self, show):
        """Sets the show of this TextSiteTagForListActivityByCacheAPIOutput.


        :param show: The show of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :type: int
        """

        self._show = show

    @property
    def value(self):
        """Gets the value of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501


        :return: The value of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this TextSiteTagForListActivityByCacheAPIOutput.


        :param value: The value of this TextSiteTagForListActivityByCacheAPIOutput.  # noqa: E501
        :type: str
        """

        self._value = value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TextSiteTagForListActivityByCacheAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TextSiteTagForListActivityByCacheAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TextSiteTagForListActivityByCacheAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
