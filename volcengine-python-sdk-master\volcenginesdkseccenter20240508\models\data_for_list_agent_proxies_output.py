# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAgentProxiesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address': 'str',
        'client_number': 'int',
        'name': 'str',
        'server_number': 'int',
        'status': 'str'
    }

    attribute_map = {
        'address': 'Address',
        'client_number': 'ClientNumber',
        'name': 'Name',
        'server_number': 'ServerNumber',
        'status': 'Status'
    }

    def __init__(self, address=None, client_number=None, name=None, server_number=None, status=None, _configuration=None):  # noqa: E501
        """DataForListAgentProxiesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address = None
        self._client_number = None
        self._name = None
        self._server_number = None
        self._status = None
        self.discriminator = None

        if address is not None:
            self.address = address
        if client_number is not None:
            self.client_number = client_number
        if name is not None:
            self.name = name
        if server_number is not None:
            self.server_number = server_number
        if status is not None:
            self.status = status

    @property
    def address(self):
        """Gets the address of this DataForListAgentProxiesOutput.  # noqa: E501


        :return: The address of this DataForListAgentProxiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this DataForListAgentProxiesOutput.


        :param address: The address of this DataForListAgentProxiesOutput.  # noqa: E501
        :type: str
        """

        self._address = address

    @property
    def client_number(self):
        """Gets the client_number of this DataForListAgentProxiesOutput.  # noqa: E501


        :return: The client_number of this DataForListAgentProxiesOutput.  # noqa: E501
        :rtype: int
        """
        return self._client_number

    @client_number.setter
    def client_number(self, client_number):
        """Sets the client_number of this DataForListAgentProxiesOutput.


        :param client_number: The client_number of this DataForListAgentProxiesOutput.  # noqa: E501
        :type: int
        """

        self._client_number = client_number

    @property
    def name(self):
        """Gets the name of this DataForListAgentProxiesOutput.  # noqa: E501


        :return: The name of this DataForListAgentProxiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListAgentProxiesOutput.


        :param name: The name of this DataForListAgentProxiesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def server_number(self):
        """Gets the server_number of this DataForListAgentProxiesOutput.  # noqa: E501


        :return: The server_number of this DataForListAgentProxiesOutput.  # noqa: E501
        :rtype: int
        """
        return self._server_number

    @server_number.setter
    def server_number(self, server_number):
        """Sets the server_number of this DataForListAgentProxiesOutput.


        :param server_number: The server_number of this DataForListAgentProxiesOutput.  # noqa: E501
        :type: int
        """

        self._server_number = server_number

    @property
    def status(self):
        """Gets the status of this DataForListAgentProxiesOutput.  # noqa: E501


        :return: The status of this DataForListAgentProxiesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListAgentProxiesOutput.


        :param status: The status of this DataForListAgentProxiesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAgentProxiesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAgentProxiesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAgentProxiesOutput):
            return True

        return self.to_dict() != other.to_dict()
