# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListDevicesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'device_id': 'str',
        'device_nsid': 'str',
        'device_name': 'str',
        'order': 'str',
        'page_number': 'str',
        'page_size': 'str',
        'space_id': 'str',
        'status': 'str'
    }

    attribute_map = {
        'device_id': 'DeviceID',
        'device_nsid': 'DeviceNSID',
        'device_name': 'DeviceName',
        'order': 'Order',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'space_id': 'SpaceID',
        'status': 'Status'
    }

    def __init__(self, device_id=None, device_nsid=None, device_name=None, order=None, page_number=None, page_size=None, space_id=None, status=None, _configuration=None):  # noqa: E501
        """ListDevicesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._device_id = None
        self._device_nsid = None
        self._device_name = None
        self._order = None
        self._page_number = None
        self._page_size = None
        self._space_id = None
        self._status = None
        self.discriminator = None

        if device_id is not None:
            self.device_id = device_id
        if device_nsid is not None:
            self.device_nsid = device_nsid
        if device_name is not None:
            self.device_name = device_name
        if order is not None:
            self.order = order
        if page_number is not None:
            self.page_number = page_number
        self.page_size = page_size
        self.space_id = space_id
        if status is not None:
            self.status = status

    @property
    def device_id(self):
        """Gets the device_id of this ListDevicesRequest.  # noqa: E501


        :return: The device_id of this ListDevicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_id

    @device_id.setter
    def device_id(self, device_id):
        """Sets the device_id of this ListDevicesRequest.


        :param device_id: The device_id of this ListDevicesRequest.  # noqa: E501
        :type: str
        """

        self._device_id = device_id

    @property
    def device_nsid(self):
        """Gets the device_nsid of this ListDevicesRequest.  # noqa: E501


        :return: The device_nsid of this ListDevicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_nsid

    @device_nsid.setter
    def device_nsid(self, device_nsid):
        """Sets the device_nsid of this ListDevicesRequest.


        :param device_nsid: The device_nsid of this ListDevicesRequest.  # noqa: E501
        :type: str
        """

        self._device_nsid = device_nsid

    @property
    def device_name(self):
        """Gets the device_name of this ListDevicesRequest.  # noqa: E501


        :return: The device_name of this ListDevicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_name

    @device_name.setter
    def device_name(self, device_name):
        """Sets the device_name of this ListDevicesRequest.


        :param device_name: The device_name of this ListDevicesRequest.  # noqa: E501
        :type: str
        """

        self._device_name = device_name

    @property
    def order(self):
        """Gets the order of this ListDevicesRequest.  # noqa: E501


        :return: The order of this ListDevicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._order

    @order.setter
    def order(self, order):
        """Sets the order of this ListDevicesRequest.


        :param order: The order of this ListDevicesRequest.  # noqa: E501
        :type: str
        """

        self._order = order

    @property
    def page_number(self):
        """Gets the page_number of this ListDevicesRequest.  # noqa: E501


        :return: The page_number of this ListDevicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListDevicesRequest.


        :param page_number: The page_number of this ListDevicesRequest.  # noqa: E501
        :type: str
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListDevicesRequest.  # noqa: E501


        :return: The page_size of this ListDevicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListDevicesRequest.


        :param page_size: The page_size of this ListDevicesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def space_id(self):
        """Gets the space_id of this ListDevicesRequest.  # noqa: E501


        :return: The space_id of this ListDevicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this ListDevicesRequest.


        :param space_id: The space_id of this ListDevicesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and space_id is None:
            raise ValueError("Invalid value for `space_id`, must not be `None`")  # noqa: E501

        self._space_id = space_id

    @property
    def status(self):
        """Gets the status of this ListDevicesRequest.  # noqa: E501


        :return: The status of this ListDevicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListDevicesRequest.


        :param status: The status of this ListDevicesRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListDevicesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListDevicesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListDevicesRequest):
            return True

        return self.to_dict() != other.to_dict()
