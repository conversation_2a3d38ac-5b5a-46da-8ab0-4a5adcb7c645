# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDirectConnectConnectionAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'customer_contact_email': 'str',
        'customer_contact_phone': 'str',
        'customer_name': 'str',
        'description': 'str',
        'direct_connect_connection_id': 'str',
        'direct_connect_connection_name': 'str',
        'project_name': 'str'
    }

    attribute_map = {
        'customer_contact_email': 'CustomerContactEmail',
        'customer_contact_phone': 'CustomerContactPhone',
        'customer_name': 'CustomerName',
        'description': 'Description',
        'direct_connect_connection_id': 'DirectConnectConnectionId',
        'direct_connect_connection_name': 'DirectConnectConnectionName',
        'project_name': 'ProjectName'
    }

    def __init__(self, customer_contact_email=None, customer_contact_phone=None, customer_name=None, description=None, direct_connect_connection_id=None, direct_connect_connection_name=None, project_name=None, _configuration=None):  # noqa: E501
        """ModifyDirectConnectConnectionAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._customer_contact_email = None
        self._customer_contact_phone = None
        self._customer_name = None
        self._description = None
        self._direct_connect_connection_id = None
        self._direct_connect_connection_name = None
        self._project_name = None
        self.discriminator = None

        if customer_contact_email is not None:
            self.customer_contact_email = customer_contact_email
        if customer_contact_phone is not None:
            self.customer_contact_phone = customer_contact_phone
        if customer_name is not None:
            self.customer_name = customer_name
        if description is not None:
            self.description = description
        self.direct_connect_connection_id = direct_connect_connection_id
        if direct_connect_connection_name is not None:
            self.direct_connect_connection_name = direct_connect_connection_name
        if project_name is not None:
            self.project_name = project_name

    @property
    def customer_contact_email(self):
        """Gets the customer_contact_email of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501


        :return: The customer_contact_email of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._customer_contact_email

    @customer_contact_email.setter
    def customer_contact_email(self, customer_contact_email):
        """Sets the customer_contact_email of this ModifyDirectConnectConnectionAttributesRequest.


        :param customer_contact_email: The customer_contact_email of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._customer_contact_email = customer_contact_email

    @property
    def customer_contact_phone(self):
        """Gets the customer_contact_phone of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501


        :return: The customer_contact_phone of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._customer_contact_phone

    @customer_contact_phone.setter
    def customer_contact_phone(self, customer_contact_phone):
        """Sets the customer_contact_phone of this ModifyDirectConnectConnectionAttributesRequest.


        :param customer_contact_phone: The customer_contact_phone of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._customer_contact_phone = customer_contact_phone

    @property
    def customer_name(self):
        """Gets the customer_name of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501


        :return: The customer_name of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._customer_name

    @customer_name.setter
    def customer_name(self, customer_name):
        """Sets the customer_name of this ModifyDirectConnectConnectionAttributesRequest.


        :param customer_name: The customer_name of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._customer_name = customer_name

    @property
    def description(self):
        """Gets the description of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501


        :return: The description of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyDirectConnectConnectionAttributesRequest.


        :param description: The description of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def direct_connect_connection_id(self):
        """Gets the direct_connect_connection_id of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501


        :return: The direct_connect_connection_id of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_connection_id

    @direct_connect_connection_id.setter
    def direct_connect_connection_id(self, direct_connect_connection_id):
        """Sets the direct_connect_connection_id of this ModifyDirectConnectConnectionAttributesRequest.


        :param direct_connect_connection_id: The direct_connect_connection_id of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and direct_connect_connection_id is None:
            raise ValueError("Invalid value for `direct_connect_connection_id`, must not be `None`")  # noqa: E501

        self._direct_connect_connection_id = direct_connect_connection_id

    @property
    def direct_connect_connection_name(self):
        """Gets the direct_connect_connection_name of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501


        :return: The direct_connect_connection_name of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_connection_name

    @direct_connect_connection_name.setter
    def direct_connect_connection_name(self, direct_connect_connection_name):
        """Sets the direct_connect_connection_name of this ModifyDirectConnectConnectionAttributesRequest.


        :param direct_connect_connection_name: The direct_connect_connection_name of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._direct_connect_connection_name = direct_connect_connection_name

    @property
    def project_name(self):
        """Gets the project_name of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501


        :return: The project_name of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ModifyDirectConnectConnectionAttributesRequest.


        :param project_name: The project_name of this ModifyDirectConnectConnectionAttributesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDirectConnectConnectionAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDirectConnectConnectionAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDirectConnectConnectionAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
