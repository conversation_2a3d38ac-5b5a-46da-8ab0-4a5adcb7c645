# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListZoneStatisticsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end': 'int',
        'name': 'str',
        'order_key': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'search_mode': 'str',
        'search_order': 'str',
        'start': 'int',
        'threshold': 'int'
    }

    attribute_map = {
        'end': 'End',
        'name': 'Name',
        'order_key': 'OrderKey',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'search_mode': 'SearchMode',
        'search_order': 'SearchOrder',
        'start': 'Start',
        'threshold': 'Threshold'
    }

    def __init__(self, end=None, name=None, order_key=None, page_number=None, page_size=None, search_mode=None, search_order=None, start=None, threshold=None, _configuration=None):  # noqa: E501
        """ListZoneStatisticsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end = None
        self._name = None
        self._order_key = None
        self._page_number = None
        self._page_size = None
        self._search_mode = None
        self._search_order = None
        self._start = None
        self._threshold = None
        self.discriminator = None

        if end is not None:
            self.end = end
        if name is not None:
            self.name = name
        if order_key is not None:
            self.order_key = order_key
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if search_mode is not None:
            self.search_mode = search_mode
        if search_order is not None:
            self.search_order = search_order
        if start is not None:
            self.start = start
        if threshold is not None:
            self.threshold = threshold

    @property
    def end(self):
        """Gets the end of this ListZoneStatisticsRequest.  # noqa: E501


        :return: The end of this ListZoneStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._end

    @end.setter
    def end(self, end):
        """Sets the end of this ListZoneStatisticsRequest.


        :param end: The end of this ListZoneStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._end = end

    @property
    def name(self):
        """Gets the name of this ListZoneStatisticsRequest.  # noqa: E501


        :return: The name of this ListZoneStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListZoneStatisticsRequest.


        :param name: The name of this ListZoneStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def order_key(self):
        """Gets the order_key of this ListZoneStatisticsRequest.  # noqa: E501


        :return: The order_key of this ListZoneStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_key

    @order_key.setter
    def order_key(self, order_key):
        """Sets the order_key of this ListZoneStatisticsRequest.


        :param order_key: The order_key of this ListZoneStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._order_key = order_key

    @property
    def page_number(self):
        """Gets the page_number of this ListZoneStatisticsRequest.  # noqa: E501


        :return: The page_number of this ListZoneStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListZoneStatisticsRequest.


        :param page_number: The page_number of this ListZoneStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListZoneStatisticsRequest.  # noqa: E501


        :return: The page_size of this ListZoneStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListZoneStatisticsRequest.


        :param page_size: The page_size of this ListZoneStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def search_mode(self):
        """Gets the search_mode of this ListZoneStatisticsRequest.  # noqa: E501


        :return: The search_mode of this ListZoneStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._search_mode

    @search_mode.setter
    def search_mode(self, search_mode):
        """Sets the search_mode of this ListZoneStatisticsRequest.


        :param search_mode: The search_mode of this ListZoneStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._search_mode = search_mode

    @property
    def search_order(self):
        """Gets the search_order of this ListZoneStatisticsRequest.  # noqa: E501


        :return: The search_order of this ListZoneStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._search_order

    @search_order.setter
    def search_order(self, search_order):
        """Sets the search_order of this ListZoneStatisticsRequest.


        :param search_order: The search_order of this ListZoneStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._search_order = search_order

    @property
    def start(self):
        """Gets the start of this ListZoneStatisticsRequest.  # noqa: E501


        :return: The start of this ListZoneStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._start

    @start.setter
    def start(self, start):
        """Sets the start of this ListZoneStatisticsRequest.


        :param start: The start of this ListZoneStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._start = start

    @property
    def threshold(self):
        """Gets the threshold of this ListZoneStatisticsRequest.  # noqa: E501


        :return: The threshold of this ListZoneStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this ListZoneStatisticsRequest.


        :param threshold: The threshold of this ListZoneStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._threshold = threshold

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListZoneStatisticsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListZoneStatisticsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListZoneStatisticsRequest):
            return True

        return self.to_dict() != other.to_dict()
