# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCdnCertInfoRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cert_id': 'str',
        'cert_type': 'str',
        'configured': 'bool',
        'configured_domain': 'str',
        'dns_name': 'str',
        'encry_type': 'str',
        'page_num': 'int',
        'page_size': 'int',
        'source': 'str',
        'status': 'str'
    }

    attribute_map = {
        'cert_id': 'CertId',
        'cert_type': 'CertType',
        'configured': 'Configured',
        'configured_domain': 'ConfiguredDomain',
        'dns_name': 'DnsName',
        'encry_type': 'EncryType',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'source': 'Source',
        'status': 'Status'
    }

    def __init__(self, cert_id=None, cert_type=None, configured=None, configured_domain=None, dns_name=None, encry_type=None, page_num=None, page_size=None, source=None, status=None, _configuration=None):  # noqa: E501
        """ListCdnCertInfoRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cert_id = None
        self._cert_type = None
        self._configured = None
        self._configured_domain = None
        self._dns_name = None
        self._encry_type = None
        self._page_num = None
        self._page_size = None
        self._source = None
        self._status = None
        self.discriminator = None

        if cert_id is not None:
            self.cert_id = cert_id
        if cert_type is not None:
            self.cert_type = cert_type
        if configured is not None:
            self.configured = configured
        if configured_domain is not None:
            self.configured_domain = configured_domain
        if dns_name is not None:
            self.dns_name = dns_name
        if encry_type is not None:
            self.encry_type = encry_type
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if source is not None:
            self.source = source
        if status is not None:
            self.status = status

    @property
    def cert_id(self):
        """Gets the cert_id of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The cert_id of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._cert_id

    @cert_id.setter
    def cert_id(self, cert_id):
        """Sets the cert_id of this ListCdnCertInfoRequest.


        :param cert_id: The cert_id of this ListCdnCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._cert_id = cert_id

    @property
    def cert_type(self):
        """Gets the cert_type of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The cert_type of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._cert_type

    @cert_type.setter
    def cert_type(self, cert_type):
        """Sets the cert_type of this ListCdnCertInfoRequest.


        :param cert_type: The cert_type of this ListCdnCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._cert_type = cert_type

    @property
    def configured(self):
        """Gets the configured of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The configured of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: bool
        """
        return self._configured

    @configured.setter
    def configured(self, configured):
        """Sets the configured of this ListCdnCertInfoRequest.


        :param configured: The configured of this ListCdnCertInfoRequest.  # noqa: E501
        :type: bool
        """

        self._configured = configured

    @property
    def configured_domain(self):
        """Gets the configured_domain of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The configured_domain of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._configured_domain

    @configured_domain.setter
    def configured_domain(self, configured_domain):
        """Sets the configured_domain of this ListCdnCertInfoRequest.


        :param configured_domain: The configured_domain of this ListCdnCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._configured_domain = configured_domain

    @property
    def dns_name(self):
        """Gets the dns_name of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The dns_name of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._dns_name

    @dns_name.setter
    def dns_name(self, dns_name):
        """Sets the dns_name of this ListCdnCertInfoRequest.


        :param dns_name: The dns_name of this ListCdnCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._dns_name = dns_name

    @property
    def encry_type(self):
        """Gets the encry_type of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The encry_type of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._encry_type

    @encry_type.setter
    def encry_type(self, encry_type):
        """Sets the encry_type of this ListCdnCertInfoRequest.


        :param encry_type: The encry_type of this ListCdnCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._encry_type = encry_type

    @property
    def page_num(self):
        """Gets the page_num of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The page_num of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListCdnCertInfoRequest.


        :param page_num: The page_num of this ListCdnCertInfoRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The page_size of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListCdnCertInfoRequest.


        :param page_size: The page_size of this ListCdnCertInfoRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def source(self):
        """Gets the source of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The source of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this ListCdnCertInfoRequest.


        :param source: The source of this ListCdnCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._source = source

    @property
    def status(self):
        """Gets the status of this ListCdnCertInfoRequest.  # noqa: E501


        :return: The status of this ListCdnCertInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListCdnCertInfoRequest.


        :param status: The status of this ListCdnCertInfoRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCdnCertInfoRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCdnCertInfoRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCdnCertInfoRequest):
            return True

        return self.to_dict() != other.to_dict()
