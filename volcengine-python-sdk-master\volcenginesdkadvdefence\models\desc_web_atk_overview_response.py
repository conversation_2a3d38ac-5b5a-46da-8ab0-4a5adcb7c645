# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescWebAtkOverviewResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attack_count': 'int',
        'attack_ip_count': 'int',
        'peak_attack_qps': 'int'
    }

    attribute_map = {
        'attack_count': 'AttackCount',
        'attack_ip_count': 'AttackIPCount',
        'peak_attack_qps': 'PeakAttackQps'
    }

    def __init__(self, attack_count=None, attack_ip_count=None, peak_attack_qps=None, _configuration=None):  # noqa: E501
        """DescWebAtkOverviewResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attack_count = None
        self._attack_ip_count = None
        self._peak_attack_qps = None
        self.discriminator = None

        if attack_count is not None:
            self.attack_count = attack_count
        if attack_ip_count is not None:
            self.attack_ip_count = attack_ip_count
        if peak_attack_qps is not None:
            self.peak_attack_qps = peak_attack_qps

    @property
    def attack_count(self):
        """Gets the attack_count of this DescWebAtkOverviewResponse.  # noqa: E501


        :return: The attack_count of this DescWebAtkOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._attack_count

    @attack_count.setter
    def attack_count(self, attack_count):
        """Sets the attack_count of this DescWebAtkOverviewResponse.


        :param attack_count: The attack_count of this DescWebAtkOverviewResponse.  # noqa: E501
        :type: int
        """

        self._attack_count = attack_count

    @property
    def attack_ip_count(self):
        """Gets the attack_ip_count of this DescWebAtkOverviewResponse.  # noqa: E501


        :return: The attack_ip_count of this DescWebAtkOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._attack_ip_count

    @attack_ip_count.setter
    def attack_ip_count(self, attack_ip_count):
        """Sets the attack_ip_count of this DescWebAtkOverviewResponse.


        :param attack_ip_count: The attack_ip_count of this DescWebAtkOverviewResponse.  # noqa: E501
        :type: int
        """

        self._attack_ip_count = attack_ip_count

    @property
    def peak_attack_qps(self):
        """Gets the peak_attack_qps of this DescWebAtkOverviewResponse.  # noqa: E501


        :return: The peak_attack_qps of this DescWebAtkOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._peak_attack_qps

    @peak_attack_qps.setter
    def peak_attack_qps(self, peak_attack_qps):
        """Sets the peak_attack_qps of this DescWebAtkOverviewResponse.


        :param peak_attack_qps: The peak_attack_qps of this DescWebAtkOverviewResponse.  # noqa: E501
        :type: int
        """

        self._peak_attack_qps = peak_attack_qps

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescWebAtkOverviewResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescWebAtkOverviewResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescWebAtkOverviewResponse):
            return True

        return self.to_dict() != other.to_dict()
