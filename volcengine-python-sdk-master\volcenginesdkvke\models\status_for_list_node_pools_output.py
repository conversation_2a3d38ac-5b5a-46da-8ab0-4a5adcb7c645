# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatusForListNodePoolsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'conditions': 'list[ConditionForListNodePoolsOutput]',
        'phase': 'str'
    }

    attribute_map = {
        'conditions': 'Conditions',
        'phase': 'Phase'
    }

    def __init__(self, conditions=None, phase=None, _configuration=None):  # noqa: E501
        """StatusForListNodePoolsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._conditions = None
        self._phase = None
        self.discriminator = None

        if conditions is not None:
            self.conditions = conditions
        if phase is not None:
            self.phase = phase

    @property
    def conditions(self):
        """Gets the conditions of this StatusForListNodePoolsOutput.  # noqa: E501


        :return: The conditions of this StatusForListNodePoolsOutput.  # noqa: E501
        :rtype: list[ConditionForListNodePoolsOutput]
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this StatusForListNodePoolsOutput.


        :param conditions: The conditions of this StatusForListNodePoolsOutput.  # noqa: E501
        :type: list[ConditionForListNodePoolsOutput]
        """

        self._conditions = conditions

    @property
    def phase(self):
        """Gets the phase of this StatusForListNodePoolsOutput.  # noqa: E501


        :return: The phase of this StatusForListNodePoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._phase

    @phase.setter
    def phase(self, phase):
        """Sets the phase of this StatusForListNodePoolsOutput.


        :param phase: The phase of this StatusForListNodePoolsOutput.  # noqa: E501
        :type: str
        """

        self._phase = phase

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatusForListNodePoolsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatusForListNodePoolsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatusForListNodePoolsOutput):
            return True

        return self.to_dict() != other.to_dict()
