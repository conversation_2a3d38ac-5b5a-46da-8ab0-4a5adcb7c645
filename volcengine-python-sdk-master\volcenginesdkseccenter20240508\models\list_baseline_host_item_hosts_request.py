# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListBaselineHostItemHostsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'asset_id': 'str',
        'asset_name': 'str',
        'asset_type': 'str',
        'baseline_id': 'int',
        'cloud_providers': 'list[str]',
        'cluster_id': 'str',
        'cluster_name': 'str',
        'hostname': 'str',
        'ip': 'str',
        'leaf_group_ids': 'list[str]',
        'node_ip': 'str',
        'node_name': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'region': 'str',
        'sort_by': 'str',
        'sort_order': 'str',
        'tag': 'list[str]',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'asset_id': 'AssetID',
        'asset_name': 'AssetName',
        'asset_type': 'AssetType',
        'baseline_id': 'BaselineID',
        'cloud_providers': 'CloudProviders',
        'cluster_id': 'ClusterID',
        'cluster_name': 'ClusterName',
        'hostname': 'Hostname',
        'ip': 'IP',
        'leaf_group_ids': 'LeafGroupIDs',
        'node_ip': 'NodeIP',
        'node_name': 'NodeName',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'region': 'Region',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'tag': 'Tag',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id=None, asset_id=None, asset_name=None, asset_type=None, baseline_id=None, cloud_providers=None, cluster_id=None, cluster_name=None, hostname=None, ip=None, leaf_group_ids=None, node_ip=None, node_name=None, page_number=None, page_size=None, region=None, sort_by=None, sort_order=None, tag=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ListBaselineHostItemHostsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._asset_id = None
        self._asset_name = None
        self._asset_type = None
        self._baseline_id = None
        self._cloud_providers = None
        self._cluster_id = None
        self._cluster_name = None
        self._hostname = None
        self._ip = None
        self._leaf_group_ids = None
        self._node_ip = None
        self._node_name = None
        self._page_number = None
        self._page_size = None
        self._region = None
        self._sort_by = None
        self._sort_order = None
        self._tag = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_name is not None:
            self.asset_name = asset_name
        if asset_type is not None:
            self.asset_type = asset_type
        if baseline_id is not None:
            self.baseline_id = baseline_id
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if hostname is not None:
            self.hostname = hostname
        if ip is not None:
            self.ip = ip
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if node_ip is not None:
            self.node_ip = node_ip
        if node_name is not None:
            self.node_name = node_name
        self.page_number = page_number
        self.page_size = page_size
        if region is not None:
            self.region = region
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if tag is not None:
            self.tag = tag
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id(self):
        """Gets the agent_id of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The agent_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ListBaselineHostItemHostsRequest.


        :param agent_id: The agent_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def asset_id(self):
        """Gets the asset_id of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The asset_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this ListBaselineHostItemHostsRequest.


        :param asset_id: The asset_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_name(self):
        """Gets the asset_name of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The asset_name of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this ListBaselineHostItemHostsRequest.


        :param asset_name: The asset_name of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def asset_type(self):
        """Gets the asset_type of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The asset_type of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this ListBaselineHostItemHostsRequest.


        :param asset_type: The asset_type of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def baseline_id(self):
        """Gets the baseline_id of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The baseline_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: int
        """
        return self._baseline_id

    @baseline_id.setter
    def baseline_id(self, baseline_id):
        """Sets the baseline_id of this ListBaselineHostItemHostsRequest.


        :param baseline_id: The baseline_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: int
        """

        self._baseline_id = baseline_id

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The cloud_providers of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ListBaselineHostItemHostsRequest.


        :param cloud_providers: The cloud_providers of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The cluster_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListBaselineHostItemHostsRequest.


        :param cluster_id: The cluster_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The cluster_name of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this ListBaselineHostItemHostsRequest.


        :param cluster_name: The cluster_name of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def hostname(self):
        """Gets the hostname of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The hostname of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this ListBaselineHostItemHostsRequest.


        :param hostname: The hostname of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def ip(self):
        """Gets the ip of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The ip of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ListBaselineHostItemHostsRequest.


        :param ip: The ip of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The leaf_group_ids of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ListBaselineHostItemHostsRequest.


        :param leaf_group_ids: The leaf_group_ids of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def node_ip(self):
        """Gets the node_ip of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The node_ip of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_ip

    @node_ip.setter
    def node_ip(self, node_ip):
        """Sets the node_ip of this ListBaselineHostItemHostsRequest.


        :param node_ip: The node_ip of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._node_ip = node_ip

    @property
    def node_name(self):
        """Gets the node_name of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The node_name of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this ListBaselineHostItemHostsRequest.


        :param node_name: The node_name of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def page_number(self):
        """Gets the page_number of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The page_number of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListBaselineHostItemHostsRequest.


        :param page_number: The page_number of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The page_size of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListBaselineHostItemHostsRequest.


        :param page_size: The page_size of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def region(self):
        """Gets the region of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The region of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ListBaselineHostItemHostsRequest.


        :param region: The region of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def sort_by(self):
        """Gets the sort_by of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The sort_by of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListBaselineHostItemHostsRequest.


        :param sort_by: The sort_by of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The sort_order of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListBaselineHostItemHostsRequest.


        :param sort_order: The sort_order of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def tag(self):
        """Gets the tag of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The tag of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this ListBaselineHostItemHostsRequest.


        :param tag: The tag of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: list[str]
        """

        self._tag = tag

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListBaselineHostItemHostsRequest.  # noqa: E501


        :return: The top_group_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListBaselineHostItemHostsRequest.


        :param top_group_id: The top_group_id of this ListBaselineHostItemHostsRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListBaselineHostItemHostsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListBaselineHostItemHostsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListBaselineHostItemHostsRequest):
            return True

        return self.to_dict() != other.to_dict()
