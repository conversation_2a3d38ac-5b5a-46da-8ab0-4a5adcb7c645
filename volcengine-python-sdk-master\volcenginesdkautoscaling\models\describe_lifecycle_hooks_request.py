# coding: utf-8

"""
    auto_scaling

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeLifecycleHooksRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'lifecycle_hook_ids': 'list[str]',
        'lifecycle_hook_name': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'scaling_group_id': 'str'
    }

    attribute_map = {
        'lifecycle_hook_ids': 'LifecycleHookIds',
        'lifecycle_hook_name': 'LifecycleHookName',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'scaling_group_id': 'ScalingGroupId'
    }

    def __init__(self, lifecycle_hook_ids=None, lifecycle_hook_name=None, page_number=None, page_size=None, scaling_group_id=None, _configuration=None):  # noqa: E501
        """DescribeLifecycleHooksRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._lifecycle_hook_ids = None
        self._lifecycle_hook_name = None
        self._page_number = None
        self._page_size = None
        self._scaling_group_id = None
        self.discriminator = None

        if lifecycle_hook_ids is not None:
            self.lifecycle_hook_ids = lifecycle_hook_ids
        if lifecycle_hook_name is not None:
            self.lifecycle_hook_name = lifecycle_hook_name
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        self.scaling_group_id = scaling_group_id

    @property
    def lifecycle_hook_ids(self):
        """Gets the lifecycle_hook_ids of this DescribeLifecycleHooksRequest.  # noqa: E501


        :return: The lifecycle_hook_ids of this DescribeLifecycleHooksRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._lifecycle_hook_ids

    @lifecycle_hook_ids.setter
    def lifecycle_hook_ids(self, lifecycle_hook_ids):
        """Sets the lifecycle_hook_ids of this DescribeLifecycleHooksRequest.


        :param lifecycle_hook_ids: The lifecycle_hook_ids of this DescribeLifecycleHooksRequest.  # noqa: E501
        :type: list[str]
        """

        self._lifecycle_hook_ids = lifecycle_hook_ids

    @property
    def lifecycle_hook_name(self):
        """Gets the lifecycle_hook_name of this DescribeLifecycleHooksRequest.  # noqa: E501


        :return: The lifecycle_hook_name of this DescribeLifecycleHooksRequest.  # noqa: E501
        :rtype: str
        """
        return self._lifecycle_hook_name

    @lifecycle_hook_name.setter
    def lifecycle_hook_name(self, lifecycle_hook_name):
        """Sets the lifecycle_hook_name of this DescribeLifecycleHooksRequest.


        :param lifecycle_hook_name: The lifecycle_hook_name of this DescribeLifecycleHooksRequest.  # noqa: E501
        :type: str
        """

        self._lifecycle_hook_name = lifecycle_hook_name

    @property
    def page_number(self):
        """Gets the page_number of this DescribeLifecycleHooksRequest.  # noqa: E501


        :return: The page_number of this DescribeLifecycleHooksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeLifecycleHooksRequest.


        :param page_number: The page_number of this DescribeLifecycleHooksRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeLifecycleHooksRequest.  # noqa: E501


        :return: The page_size of this DescribeLifecycleHooksRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeLifecycleHooksRequest.


        :param page_size: The page_size of this DescribeLifecycleHooksRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def scaling_group_id(self):
        """Gets the scaling_group_id of this DescribeLifecycleHooksRequest.  # noqa: E501


        :return: The scaling_group_id of this DescribeLifecycleHooksRequest.  # noqa: E501
        :rtype: str
        """
        return self._scaling_group_id

    @scaling_group_id.setter
    def scaling_group_id(self, scaling_group_id):
        """Sets the scaling_group_id of this DescribeLifecycleHooksRequest.


        :param scaling_group_id: The scaling_group_id of this DescribeLifecycleHooksRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and scaling_group_id is None:
            raise ValueError("Invalid value for `scaling_group_id`, must not be `None`")  # noqa: E501

        self._scaling_group_id = scaling_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeLifecycleHooksRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeLifecycleHooksRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeLifecycleHooksRequest):
            return True

        return self.to_dict() != other.to_dict()
