# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCloudEnvsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_key': 'str',
        'cloud_platform': 'str',
        'comment': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'int',
        'sync_status': 'int'
    }

    attribute_map = {
        'access_key': 'AccessKey',
        'cloud_platform': 'CloudPlatform',
        'comment': 'Comment',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status',
        'sync_status': 'SyncStatus'
    }

    def __init__(self, access_key=None, cloud_platform=None, comment=None, page_number=None, page_size=None, sort_by=None, sort_order=None, status=None, sync_status=None, _configuration=None):  # noqa: E501
        """ListCloudEnvsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_key = None
        self._cloud_platform = None
        self._comment = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self._sync_status = None
        self.discriminator = None

        if access_key is not None:
            self.access_key = access_key
        if cloud_platform is not None:
            self.cloud_platform = cloud_platform
        if comment is not None:
            self.comment = comment
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status
        if sync_status is not None:
            self.sync_status = sync_status

    @property
    def access_key(self):
        """Gets the access_key of this ListCloudEnvsRequest.  # noqa: E501


        :return: The access_key of this ListCloudEnvsRequest.  # noqa: E501
        :rtype: str
        """
        return self._access_key

    @access_key.setter
    def access_key(self, access_key):
        """Sets the access_key of this ListCloudEnvsRequest.


        :param access_key: The access_key of this ListCloudEnvsRequest.  # noqa: E501
        :type: str
        """

        self._access_key = access_key

    @property
    def cloud_platform(self):
        """Gets the cloud_platform of this ListCloudEnvsRequest.  # noqa: E501


        :return: The cloud_platform of this ListCloudEnvsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cloud_platform

    @cloud_platform.setter
    def cloud_platform(self, cloud_platform):
        """Sets the cloud_platform of this ListCloudEnvsRequest.


        :param cloud_platform: The cloud_platform of this ListCloudEnvsRequest.  # noqa: E501
        :type: str
        """

        self._cloud_platform = cloud_platform

    @property
    def comment(self):
        """Gets the comment of this ListCloudEnvsRequest.  # noqa: E501


        :return: The comment of this ListCloudEnvsRequest.  # noqa: E501
        :rtype: str
        """
        return self._comment

    @comment.setter
    def comment(self, comment):
        """Sets the comment of this ListCloudEnvsRequest.


        :param comment: The comment of this ListCloudEnvsRequest.  # noqa: E501
        :type: str
        """

        self._comment = comment

    @property
    def page_number(self):
        """Gets the page_number of this ListCloudEnvsRequest.  # noqa: E501


        :return: The page_number of this ListCloudEnvsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListCloudEnvsRequest.


        :param page_number: The page_number of this ListCloudEnvsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                page_number is not None and page_number < 1):  # noqa: E501
            raise ValueError("Invalid value for `page_number`, must be a value greater than or equal to `1`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListCloudEnvsRequest.  # noqa: E501


        :return: The page_size of this ListCloudEnvsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListCloudEnvsRequest.


        :param page_size: The page_size of this ListCloudEnvsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 5000):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `5000`")  # noqa: E501
        if (self._configuration.client_side_validation and
                page_size is not None and page_size < 1):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value greater than or equal to `1`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListCloudEnvsRequest.  # noqa: E501


        :return: The sort_by of this ListCloudEnvsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListCloudEnvsRequest.


        :param sort_by: The sort_by of this ListCloudEnvsRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListCloudEnvsRequest.  # noqa: E501


        :return: The sort_order of this ListCloudEnvsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListCloudEnvsRequest.


        :param sort_order: The sort_order of this ListCloudEnvsRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListCloudEnvsRequest.  # noqa: E501


        :return: The status of this ListCloudEnvsRequest.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListCloudEnvsRequest.


        :param status: The status of this ListCloudEnvsRequest.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def sync_status(self):
        """Gets the sync_status of this ListCloudEnvsRequest.  # noqa: E501


        :return: The sync_status of this ListCloudEnvsRequest.  # noqa: E501
        :rtype: int
        """
        return self._sync_status

    @sync_status.setter
    def sync_status(self, sync_status):
        """Sets the sync_status of this ListCloudEnvsRequest.


        :param sync_status: The sync_status of this ListCloudEnvsRequest.  # noqa: E501
        :type: int
        """

        self._sync_status = sync_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCloudEnvsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCloudEnvsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCloudEnvsRequest):
            return True

        return self.to_dict() != other.to_dict()
