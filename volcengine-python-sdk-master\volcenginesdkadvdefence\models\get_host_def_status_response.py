# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetHostDefStatusResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_enable': 'int',
        'black_lct_enable': 'int',
        'block_enable': 'int',
        'cc_enable': 'int'
    }

    attribute_map = {
        'allow_enable': 'AllowEnable',
        'black_lct_enable': 'BlackLctEnable',
        'block_enable': 'BlockEnable',
        'cc_enable': 'CcEnable'
    }

    def __init__(self, allow_enable=None, black_lct_enable=None, block_enable=None, cc_enable=None, _configuration=None):  # noqa: E501
        """GetHostDefStatusResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_enable = None
        self._black_lct_enable = None
        self._block_enable = None
        self._cc_enable = None
        self.discriminator = None

        if allow_enable is not None:
            self.allow_enable = allow_enable
        if black_lct_enable is not None:
            self.black_lct_enable = black_lct_enable
        if block_enable is not None:
            self.block_enable = block_enable
        if cc_enable is not None:
            self.cc_enable = cc_enable

    @property
    def allow_enable(self):
        """Gets the allow_enable of this GetHostDefStatusResponse.  # noqa: E501


        :return: The allow_enable of this GetHostDefStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._allow_enable

    @allow_enable.setter
    def allow_enable(self, allow_enable):
        """Sets the allow_enable of this GetHostDefStatusResponse.


        :param allow_enable: The allow_enable of this GetHostDefStatusResponse.  # noqa: E501
        :type: int
        """

        self._allow_enable = allow_enable

    @property
    def black_lct_enable(self):
        """Gets the black_lct_enable of this GetHostDefStatusResponse.  # noqa: E501


        :return: The black_lct_enable of this GetHostDefStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._black_lct_enable

    @black_lct_enable.setter
    def black_lct_enable(self, black_lct_enable):
        """Sets the black_lct_enable of this GetHostDefStatusResponse.


        :param black_lct_enable: The black_lct_enable of this GetHostDefStatusResponse.  # noqa: E501
        :type: int
        """

        self._black_lct_enable = black_lct_enable

    @property
    def block_enable(self):
        """Gets the block_enable of this GetHostDefStatusResponse.  # noqa: E501


        :return: The block_enable of this GetHostDefStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._block_enable

    @block_enable.setter
    def block_enable(self, block_enable):
        """Sets the block_enable of this GetHostDefStatusResponse.


        :param block_enable: The block_enable of this GetHostDefStatusResponse.  # noqa: E501
        :type: int
        """

        self._block_enable = block_enable

    @property
    def cc_enable(self):
        """Gets the cc_enable of this GetHostDefStatusResponse.  # noqa: E501


        :return: The cc_enable of this GetHostDefStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._cc_enable

    @cc_enable.setter
    def cc_enable(self, cc_enable):
        """Sets the cc_enable of this GetHostDefStatusResponse.


        :param cc_enable: The cc_enable of this GetHostDefStatusResponse.  # noqa: E501
        :type: int
        """

        self._cc_enable = cc_enable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetHostDefStatusResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetHostDefStatusResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetHostDefStatusResponse):
            return True

        return self.to_dict() != other.to_dict()
