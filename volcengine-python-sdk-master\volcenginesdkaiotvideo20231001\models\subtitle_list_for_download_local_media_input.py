# coding: utf-8

"""
    aiotvideo20231001

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubtitleListForDownloadLocalMediaInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'content': 'str',
        'end': 'int',
        'font': 'FontForDownloadLocalMediaInput',
        'start': 'int'
    }

    attribute_map = {
        'content': 'Content',
        'end': 'End',
        'font': 'Font',
        'start': 'Start'
    }

    def __init__(self, content=None, end=None, font=None, start=None, _configuration=None):  # noqa: E501
        """SubtitleListForDownloadLocalMediaInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._content = None
        self._end = None
        self._font = None
        self._start = None
        self.discriminator = None

        if content is not None:
            self.content = content
        if end is not None:
            self.end = end
        if font is not None:
            self.font = font
        if start is not None:
            self.start = start

    @property
    def content(self):
        """Gets the content of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501


        :return: The content of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501
        :rtype: str
        """
        return self._content

    @content.setter
    def content(self, content):
        """Sets the content of this SubtitleListForDownloadLocalMediaInput.


        :param content: The content of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501
        :type: str
        """

        self._content = content

    @property
    def end(self):
        """Gets the end of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501


        :return: The end of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501
        :rtype: int
        """
        return self._end

    @end.setter
    def end(self, end):
        """Sets the end of this SubtitleListForDownloadLocalMediaInput.


        :param end: The end of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501
        :type: int
        """

        self._end = end

    @property
    def font(self):
        """Gets the font of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501


        :return: The font of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501
        :rtype: FontForDownloadLocalMediaInput
        """
        return self._font

    @font.setter
    def font(self, font):
        """Sets the font of this SubtitleListForDownloadLocalMediaInput.


        :param font: The font of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501
        :type: FontForDownloadLocalMediaInput
        """

        self._font = font

    @property
    def start(self):
        """Gets the start of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501


        :return: The start of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501
        :rtype: int
        """
        return self._start

    @start.setter
    def start(self, start):
        """Sets the start of this SubtitleListForDownloadLocalMediaInput.


        :param start: The start of this SubtitleListForDownloadLocalMediaInput.  # noqa: E501
        :type: int
        """

        self._start = start

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubtitleListForDownloadLocalMediaInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubtitleListForDownloadLocalMediaInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubtitleListForDownloadLocalMediaInput):
            return True

        return self.to_dict() != other.to_dict()
