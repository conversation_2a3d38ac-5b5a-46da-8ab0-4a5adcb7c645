# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForDescribeNormalLogsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'connection': 'str',
        'create_time': 'str',
        'log_type': 'str',
        'message': 'str'
    }

    attribute_map = {
        'connection': 'Connection',
        'create_time': 'CreateTime',
        'log_type': 'LogType',
        'message': 'Message'
    }

    def __init__(self, connection=None, create_time=None, log_type=None, message=None, _configuration=None):  # noqa: E501
        """DataForDescribeNormalLogsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._connection = None
        self._create_time = None
        self._log_type = None
        self._message = None
        self.discriminator = None

        if connection is not None:
            self.connection = connection
        if create_time is not None:
            self.create_time = create_time
        if log_type is not None:
            self.log_type = log_type
        if message is not None:
            self.message = message

    @property
    def connection(self):
        """Gets the connection of this DataForDescribeNormalLogsOutput.  # noqa: E501


        :return: The connection of this DataForDescribeNormalLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._connection

    @connection.setter
    def connection(self, connection):
        """Sets the connection of this DataForDescribeNormalLogsOutput.


        :param connection: The connection of this DataForDescribeNormalLogsOutput.  # noqa: E501
        :type: str
        """

        self._connection = connection

    @property
    def create_time(self):
        """Gets the create_time of this DataForDescribeNormalLogsOutput.  # noqa: E501


        :return: The create_time of this DataForDescribeNormalLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForDescribeNormalLogsOutput.


        :param create_time: The create_time of this DataForDescribeNormalLogsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def log_type(self):
        """Gets the log_type of this DataForDescribeNormalLogsOutput.  # noqa: E501


        :return: The log_type of this DataForDescribeNormalLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_type

    @log_type.setter
    def log_type(self, log_type):
        """Sets the log_type of this DataForDescribeNormalLogsOutput.


        :param log_type: The log_type of this DataForDescribeNormalLogsOutput.  # noqa: E501
        :type: str
        """

        self._log_type = log_type

    @property
    def message(self):
        """Gets the message of this DataForDescribeNormalLogsOutput.  # noqa: E501


        :return: The message of this DataForDescribeNormalLogsOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this DataForDescribeNormalLogsOutput.


        :param message: The message of this DataForDescribeNormalLogsOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForDescribeNormalLogsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForDescribeNormalLogsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForDescribeNormalLogsOutput):
            return True

        return self.to_dict() != other.to_dict()
