# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetOneRaspAlarmResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'base_alarm_info': 'BaseAlarmInfoForGetOneRaspAlarmOutput',
        'base_info': 'BaseInfoForGetOneRaspAlarmOutput',
        'container_info': 'ContainerInfoForGetOneRaspAlarmOutput',
        'data_type': 'str',
        'plus_alarm_info2439': 'PlusAlarmInfo2439ForGetOneRaspAlarmOutput'
    }

    attribute_map = {
        'base_alarm_info': 'BaseAlarmInfo',
        'base_info': 'BaseInfo',
        'container_info': 'ContainerInfo',
        'data_type': 'DataType',
        'plus_alarm_info2439': 'PlusAlarmInfo2439'
    }

    def __init__(self, base_alarm_info=None, base_info=None, container_info=None, data_type=None, plus_alarm_info2439=None, _configuration=None):  # noqa: E501
        """GetOneRaspAlarmResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._base_alarm_info = None
        self._base_info = None
        self._container_info = None
        self._data_type = None
        self._plus_alarm_info2439 = None
        self.discriminator = None

        if base_alarm_info is not None:
            self.base_alarm_info = base_alarm_info
        if base_info is not None:
            self.base_info = base_info
        if container_info is not None:
            self.container_info = container_info
        if data_type is not None:
            self.data_type = data_type
        if plus_alarm_info2439 is not None:
            self.plus_alarm_info2439 = plus_alarm_info2439

    @property
    def base_alarm_info(self):
        """Gets the base_alarm_info of this GetOneRaspAlarmResponse.  # noqa: E501


        :return: The base_alarm_info of this GetOneRaspAlarmResponse.  # noqa: E501
        :rtype: BaseAlarmInfoForGetOneRaspAlarmOutput
        """
        return self._base_alarm_info

    @base_alarm_info.setter
    def base_alarm_info(self, base_alarm_info):
        """Sets the base_alarm_info of this GetOneRaspAlarmResponse.


        :param base_alarm_info: The base_alarm_info of this GetOneRaspAlarmResponse.  # noqa: E501
        :type: BaseAlarmInfoForGetOneRaspAlarmOutput
        """

        self._base_alarm_info = base_alarm_info

    @property
    def base_info(self):
        """Gets the base_info of this GetOneRaspAlarmResponse.  # noqa: E501


        :return: The base_info of this GetOneRaspAlarmResponse.  # noqa: E501
        :rtype: BaseInfoForGetOneRaspAlarmOutput
        """
        return self._base_info

    @base_info.setter
    def base_info(self, base_info):
        """Sets the base_info of this GetOneRaspAlarmResponse.


        :param base_info: The base_info of this GetOneRaspAlarmResponse.  # noqa: E501
        :type: BaseInfoForGetOneRaspAlarmOutput
        """

        self._base_info = base_info

    @property
    def container_info(self):
        """Gets the container_info of this GetOneRaspAlarmResponse.  # noqa: E501


        :return: The container_info of this GetOneRaspAlarmResponse.  # noqa: E501
        :rtype: ContainerInfoForGetOneRaspAlarmOutput
        """
        return self._container_info

    @container_info.setter
    def container_info(self, container_info):
        """Sets the container_info of this GetOneRaspAlarmResponse.


        :param container_info: The container_info of this GetOneRaspAlarmResponse.  # noqa: E501
        :type: ContainerInfoForGetOneRaspAlarmOutput
        """

        self._container_info = container_info

    @property
    def data_type(self):
        """Gets the data_type of this GetOneRaspAlarmResponse.  # noqa: E501


        :return: The data_type of this GetOneRaspAlarmResponse.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this GetOneRaspAlarmResponse.


        :param data_type: The data_type of this GetOneRaspAlarmResponse.  # noqa: E501
        :type: str
        """

        self._data_type = data_type

    @property
    def plus_alarm_info2439(self):
        """Gets the plus_alarm_info2439 of this GetOneRaspAlarmResponse.  # noqa: E501


        :return: The plus_alarm_info2439 of this GetOneRaspAlarmResponse.  # noqa: E501
        :rtype: PlusAlarmInfo2439ForGetOneRaspAlarmOutput
        """
        return self._plus_alarm_info2439

    @plus_alarm_info2439.setter
    def plus_alarm_info2439(self, plus_alarm_info2439):
        """Sets the plus_alarm_info2439 of this GetOneRaspAlarmResponse.


        :param plus_alarm_info2439: The plus_alarm_info2439 of this GetOneRaspAlarmResponse.  # noqa: E501
        :type: PlusAlarmInfo2439ForGetOneRaspAlarmOutput
        """

        self._plus_alarm_info2439 = plus_alarm_info2439

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetOneRaspAlarmResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetOneRaspAlarmResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetOneRaspAlarmResponse):
            return True

        return self.to_dict() != other.to_dict()
