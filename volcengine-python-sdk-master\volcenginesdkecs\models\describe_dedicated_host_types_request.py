# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDedicatedHostTypesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dedicated_host_type_ids': 'list[str]',
        'max_results': 'int',
        'next_token': 'str',
        'supported_instance_type_family': 'str'
    }

    attribute_map = {
        'dedicated_host_type_ids': 'DedicatedHostTypeIds',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'supported_instance_type_family': 'SupportedInstanceTypeFamily'
    }

    def __init__(self, dedicated_host_type_ids=None, max_results=None, next_token=None, supported_instance_type_family=None, _configuration=None):  # noqa: E501
        """DescribeDedicatedHostTypesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dedicated_host_type_ids = None
        self._max_results = None
        self._next_token = None
        self._supported_instance_type_family = None
        self.discriminator = None

        if dedicated_host_type_ids is not None:
            self.dedicated_host_type_ids = dedicated_host_type_ids
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if supported_instance_type_family is not None:
            self.supported_instance_type_family = supported_instance_type_family

    @property
    def dedicated_host_type_ids(self):
        """Gets the dedicated_host_type_ids of this DescribeDedicatedHostTypesRequest.  # noqa: E501


        :return: The dedicated_host_type_ids of this DescribeDedicatedHostTypesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._dedicated_host_type_ids

    @dedicated_host_type_ids.setter
    def dedicated_host_type_ids(self, dedicated_host_type_ids):
        """Sets the dedicated_host_type_ids of this DescribeDedicatedHostTypesRequest.


        :param dedicated_host_type_ids: The dedicated_host_type_ids of this DescribeDedicatedHostTypesRequest.  # noqa: E501
        :type: list[str]
        """

        self._dedicated_host_type_ids = dedicated_host_type_ids

    @property
    def max_results(self):
        """Gets the max_results of this DescribeDedicatedHostTypesRequest.  # noqa: E501


        :return: The max_results of this DescribeDedicatedHostTypesRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeDedicatedHostTypesRequest.


        :param max_results: The max_results of this DescribeDedicatedHostTypesRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeDedicatedHostTypesRequest.  # noqa: E501


        :return: The next_token of this DescribeDedicatedHostTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeDedicatedHostTypesRequest.


        :param next_token: The next_token of this DescribeDedicatedHostTypesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def supported_instance_type_family(self):
        """Gets the supported_instance_type_family of this DescribeDedicatedHostTypesRequest.  # noqa: E501


        :return: The supported_instance_type_family of this DescribeDedicatedHostTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._supported_instance_type_family

    @supported_instance_type_family.setter
    def supported_instance_type_family(self, supported_instance_type_family):
        """Sets the supported_instance_type_family of this DescribeDedicatedHostTypesRequest.


        :param supported_instance_type_family: The supported_instance_type_family of this DescribeDedicatedHostTypesRequest.  # noqa: E501
        :type: str
        """

        self._supported_instance_type_family = supported_instance_type_family

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDedicatedHostTypesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDedicatedHostTypesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDedicatedHostTypesRequest):
            return True

        return self.to_dict() != other.to_dict()
