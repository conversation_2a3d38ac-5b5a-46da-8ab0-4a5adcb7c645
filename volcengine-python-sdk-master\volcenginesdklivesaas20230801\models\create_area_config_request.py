# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateAreaConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'area_ip': 'AreaIpForCreateAreaConfigInput',
        'area_name': 'str',
        'enable_extranet_url': 'bool',
        'lb_strategy': 'str'
    }

    attribute_map = {
        'area_ip': 'AreaIp',
        'area_name': 'AreaName',
        'enable_extranet_url': 'EnableExtranetUrl',
        'lb_strategy': 'LBStrategy'
    }

    def __init__(self, area_ip=None, area_name=None, enable_extranet_url=None, lb_strategy=None, _configuration=None):  # noqa: E501
        """CreateAreaConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._area_ip = None
        self._area_name = None
        self._enable_extranet_url = None
        self._lb_strategy = None
        self.discriminator = None

        if area_ip is not None:
            self.area_ip = area_ip
        self.area_name = area_name
        self.enable_extranet_url = enable_extranet_url
        self.lb_strategy = lb_strategy

    @property
    def area_ip(self):
        """Gets the area_ip of this CreateAreaConfigRequest.  # noqa: E501


        :return: The area_ip of this CreateAreaConfigRequest.  # noqa: E501
        :rtype: AreaIpForCreateAreaConfigInput
        """
        return self._area_ip

    @area_ip.setter
    def area_ip(self, area_ip):
        """Sets the area_ip of this CreateAreaConfigRequest.


        :param area_ip: The area_ip of this CreateAreaConfigRequest.  # noqa: E501
        :type: AreaIpForCreateAreaConfigInput
        """

        self._area_ip = area_ip

    @property
    def area_name(self):
        """Gets the area_name of this CreateAreaConfigRequest.  # noqa: E501


        :return: The area_name of this CreateAreaConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._area_name

    @area_name.setter
    def area_name(self, area_name):
        """Sets the area_name of this CreateAreaConfigRequest.


        :param area_name: The area_name of this CreateAreaConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and area_name is None:
            raise ValueError("Invalid value for `area_name`, must not be `None`")  # noqa: E501

        self._area_name = area_name

    @property
    def enable_extranet_url(self):
        """Gets the enable_extranet_url of this CreateAreaConfigRequest.  # noqa: E501


        :return: The enable_extranet_url of this CreateAreaConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_extranet_url

    @enable_extranet_url.setter
    def enable_extranet_url(self, enable_extranet_url):
        """Sets the enable_extranet_url of this CreateAreaConfigRequest.


        :param enable_extranet_url: The enable_extranet_url of this CreateAreaConfigRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and enable_extranet_url is None:
            raise ValueError("Invalid value for `enable_extranet_url`, must not be `None`")  # noqa: E501

        self._enable_extranet_url = enable_extranet_url

    @property
    def lb_strategy(self):
        """Gets the lb_strategy of this CreateAreaConfigRequest.  # noqa: E501


        :return: The lb_strategy of this CreateAreaConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._lb_strategy

    @lb_strategy.setter
    def lb_strategy(self, lb_strategy):
        """Sets the lb_strategy of this CreateAreaConfigRequest.


        :param lb_strategy: The lb_strategy of this CreateAreaConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and lb_strategy is None:
            raise ValueError("Invalid value for `lb_strategy`, must not be `None`")  # noqa: E501

        self._lb_strategy = lb_strategy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateAreaConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateAreaConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateAreaConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
