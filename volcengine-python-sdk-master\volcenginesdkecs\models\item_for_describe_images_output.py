# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForDescribeImagesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'result': 'str',
        'risk_code': 'str',
        'risk_level': 'str'
    }

    attribute_map = {
        'name': 'Name',
        'result': 'Result',
        'risk_code': 'RiskCode',
        'risk_level': 'RiskLevel'
    }

    def __init__(self, name=None, result=None, risk_code=None, risk_level=None, _configuration=None):  # noqa: E501
        """ItemForDescribeImagesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._result = None
        self._risk_code = None
        self._risk_level = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if result is not None:
            self.result = result
        if risk_code is not None:
            self.risk_code = risk_code
        if risk_level is not None:
            self.risk_level = risk_level

    @property
    def name(self):
        """Gets the name of this ItemForDescribeImagesOutput.  # noqa: E501


        :return: The name of this ItemForDescribeImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForDescribeImagesOutput.


        :param name: The name of this ItemForDescribeImagesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def result(self):
        """Gets the result of this ItemForDescribeImagesOutput.  # noqa: E501


        :return: The result of this ItemForDescribeImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._result

    @result.setter
    def result(self, result):
        """Sets the result of this ItemForDescribeImagesOutput.


        :param result: The result of this ItemForDescribeImagesOutput.  # noqa: E501
        :type: str
        """

        self._result = result

    @property
    def risk_code(self):
        """Gets the risk_code of this ItemForDescribeImagesOutput.  # noqa: E501


        :return: The risk_code of this ItemForDescribeImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._risk_code

    @risk_code.setter
    def risk_code(self, risk_code):
        """Sets the risk_code of this ItemForDescribeImagesOutput.


        :param risk_code: The risk_code of this ItemForDescribeImagesOutput.  # noqa: E501
        :type: str
        """

        self._risk_code = risk_code

    @property
    def risk_level(self):
        """Gets the risk_level of this ItemForDescribeImagesOutput.  # noqa: E501


        :return: The risk_level of this ItemForDescribeImagesOutput.  # noqa: E501
        :rtype: str
        """
        return self._risk_level

    @risk_level.setter
    def risk_level(self, risk_level):
        """Sets the risk_level of this ItemForDescribeImagesOutput.


        :param risk_level: The risk_level of this ItemForDescribeImagesOutput.  # noqa: E501
        :type: str
        """

        self._risk_level = risk_level

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForDescribeImagesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForDescribeImagesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForDescribeImagesOutput):
            return True

        return self.to_dict() != other.to_dict()
