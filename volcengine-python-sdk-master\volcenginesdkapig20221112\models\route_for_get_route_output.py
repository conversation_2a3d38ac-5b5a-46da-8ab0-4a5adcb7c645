# coding: utf-8

"""
    apig20221112

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RouteForGetRouteOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'advanced_setting': 'AdvancedSettingForGetRouteOutput',
        'create_time': 'str',
        'custom_domains': 'list[CustomDomainForGetRouteOutput]',
        'domains': 'list[DomainForGetRouteOutput]',
        'enable': 'bool',
        'id': 'str',
        'match_rule': 'MatchRuleForGetRouteOutput',
        'name': 'str',
        'priority': 'int',
        'reason': 'str',
        'resource_type': 'str',
        'service_id': 'str',
        'service_name': 'str',
        'status': 'str',
        'tags': 'list[TagForGetRouteOutput]',
        'update_time': 'str',
        'upstream_list': 'list[UpstreamListForGetRouteOutput]'
    }

    attribute_map = {
        'advanced_setting': 'AdvancedSetting',
        'create_time': 'CreateTime',
        'custom_domains': 'CustomDomains',
        'domains': 'Domains',
        'enable': 'Enable',
        'id': 'Id',
        'match_rule': 'MatchRule',
        'name': 'Name',
        'priority': 'Priority',
        'reason': 'Reason',
        'resource_type': 'ResourceType',
        'service_id': 'ServiceId',
        'service_name': 'ServiceName',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime',
        'upstream_list': 'UpstreamList'
    }

    def __init__(self, advanced_setting=None, create_time=None, custom_domains=None, domains=None, enable=None, id=None, match_rule=None, name=None, priority=None, reason=None, resource_type=None, service_id=None, service_name=None, status=None, tags=None, update_time=None, upstream_list=None, _configuration=None):  # noqa: E501
        """RouteForGetRouteOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._advanced_setting = None
        self._create_time = None
        self._custom_domains = None
        self._domains = None
        self._enable = None
        self._id = None
        self._match_rule = None
        self._name = None
        self._priority = None
        self._reason = None
        self._resource_type = None
        self._service_id = None
        self._service_name = None
        self._status = None
        self._tags = None
        self._update_time = None
        self._upstream_list = None
        self.discriminator = None

        if advanced_setting is not None:
            self.advanced_setting = advanced_setting
        if create_time is not None:
            self.create_time = create_time
        if custom_domains is not None:
            self.custom_domains = custom_domains
        if domains is not None:
            self.domains = domains
        if enable is not None:
            self.enable = enable
        if id is not None:
            self.id = id
        if match_rule is not None:
            self.match_rule = match_rule
        if name is not None:
            self.name = name
        if priority is not None:
            self.priority = priority
        if reason is not None:
            self.reason = reason
        if resource_type is not None:
            self.resource_type = resource_type
        if service_id is not None:
            self.service_id = service_id
        if service_name is not None:
            self.service_name = service_name
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time
        if upstream_list is not None:
            self.upstream_list = upstream_list

    @property
    def advanced_setting(self):
        """Gets the advanced_setting of this RouteForGetRouteOutput.  # noqa: E501


        :return: The advanced_setting of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: AdvancedSettingForGetRouteOutput
        """
        return self._advanced_setting

    @advanced_setting.setter
    def advanced_setting(self, advanced_setting):
        """Sets the advanced_setting of this RouteForGetRouteOutput.


        :param advanced_setting: The advanced_setting of this RouteForGetRouteOutput.  # noqa: E501
        :type: AdvancedSettingForGetRouteOutput
        """

        self._advanced_setting = advanced_setting

    @property
    def create_time(self):
        """Gets the create_time of this RouteForGetRouteOutput.  # noqa: E501


        :return: The create_time of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this RouteForGetRouteOutput.


        :param create_time: The create_time of this RouteForGetRouteOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def custom_domains(self):
        """Gets the custom_domains of this RouteForGetRouteOutput.  # noqa: E501


        :return: The custom_domains of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: list[CustomDomainForGetRouteOutput]
        """
        return self._custom_domains

    @custom_domains.setter
    def custom_domains(self, custom_domains):
        """Sets the custom_domains of this RouteForGetRouteOutput.


        :param custom_domains: The custom_domains of this RouteForGetRouteOutput.  # noqa: E501
        :type: list[CustomDomainForGetRouteOutput]
        """

        self._custom_domains = custom_domains

    @property
    def domains(self):
        """Gets the domains of this RouteForGetRouteOutput.  # noqa: E501


        :return: The domains of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: list[DomainForGetRouteOutput]
        """
        return self._domains

    @domains.setter
    def domains(self, domains):
        """Sets the domains of this RouteForGetRouteOutput.


        :param domains: The domains of this RouteForGetRouteOutput.  # noqa: E501
        :type: list[DomainForGetRouteOutput]
        """

        self._domains = domains

    @property
    def enable(self):
        """Gets the enable of this RouteForGetRouteOutput.  # noqa: E501


        :return: The enable of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this RouteForGetRouteOutput.


        :param enable: The enable of this RouteForGetRouteOutput.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def id(self):
        """Gets the id of this RouteForGetRouteOutput.  # noqa: E501


        :return: The id of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this RouteForGetRouteOutput.


        :param id: The id of this RouteForGetRouteOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def match_rule(self):
        """Gets the match_rule of this RouteForGetRouteOutput.  # noqa: E501


        :return: The match_rule of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: MatchRuleForGetRouteOutput
        """
        return self._match_rule

    @match_rule.setter
    def match_rule(self, match_rule):
        """Sets the match_rule of this RouteForGetRouteOutput.


        :param match_rule: The match_rule of this RouteForGetRouteOutput.  # noqa: E501
        :type: MatchRuleForGetRouteOutput
        """

        self._match_rule = match_rule

    @property
    def name(self):
        """Gets the name of this RouteForGetRouteOutput.  # noqa: E501


        :return: The name of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this RouteForGetRouteOutput.


        :param name: The name of this RouteForGetRouteOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def priority(self):
        """Gets the priority of this RouteForGetRouteOutput.  # noqa: E501


        :return: The priority of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this RouteForGetRouteOutput.


        :param priority: The priority of this RouteForGetRouteOutput.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def reason(self):
        """Gets the reason of this RouteForGetRouteOutput.  # noqa: E501


        :return: The reason of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this RouteForGetRouteOutput.


        :param reason: The reason of this RouteForGetRouteOutput.  # noqa: E501
        :type: str
        """

        self._reason = reason

    @property
    def resource_type(self):
        """Gets the resource_type of this RouteForGetRouteOutput.  # noqa: E501


        :return: The resource_type of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this RouteForGetRouteOutput.


        :param resource_type: The resource_type of this RouteForGetRouteOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def service_id(self):
        """Gets the service_id of this RouteForGetRouteOutput.  # noqa: E501


        :return: The service_id of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this RouteForGetRouteOutput.


        :param service_id: The service_id of this RouteForGetRouteOutput.  # noqa: E501
        :type: str
        """

        self._service_id = service_id

    @property
    def service_name(self):
        """Gets the service_name of this RouteForGetRouteOutput.  # noqa: E501


        :return: The service_name of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this RouteForGetRouteOutput.


        :param service_name: The service_name of this RouteForGetRouteOutput.  # noqa: E501
        :type: str
        """

        self._service_name = service_name

    @property
    def status(self):
        """Gets the status of this RouteForGetRouteOutput.  # noqa: E501


        :return: The status of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this RouteForGetRouteOutput.


        :param status: The status of this RouteForGetRouteOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this RouteForGetRouteOutput.  # noqa: E501


        :return: The tags of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: list[TagForGetRouteOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this RouteForGetRouteOutput.


        :param tags: The tags of this RouteForGetRouteOutput.  # noqa: E501
        :type: list[TagForGetRouteOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this RouteForGetRouteOutput.  # noqa: E501


        :return: The update_time of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this RouteForGetRouteOutput.


        :param update_time: The update_time of this RouteForGetRouteOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def upstream_list(self):
        """Gets the upstream_list of this RouteForGetRouteOutput.  # noqa: E501


        :return: The upstream_list of this RouteForGetRouteOutput.  # noqa: E501
        :rtype: list[UpstreamListForGetRouteOutput]
        """
        return self._upstream_list

    @upstream_list.setter
    def upstream_list(self, upstream_list):
        """Sets the upstream_list of this RouteForGetRouteOutput.


        :param upstream_list: The upstream_list of this RouteForGetRouteOutput.  # noqa: E501
        :type: list[UpstreamListForGetRouteOutput]
        """

        self._upstream_list = upstream_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RouteForGetRouteOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RouteForGetRouteOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RouteForGetRouteOutput):
            return True

        return self.to_dict() != other.to_dict()
