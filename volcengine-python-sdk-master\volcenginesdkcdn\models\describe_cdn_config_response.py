# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCdnConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'domain_config': 'DomainConfigForDescribeCdnConfigOutput',
        'feature_config': 'FeatureConfigForDescribeCdnConfigOutput',
        'module_lock_config': 'ModuleLockConfigForDescribeCdnConfigOutput'
    }

    attribute_map = {
        'domain_config': 'DomainConfig',
        'feature_config': 'FeatureConfig',
        'module_lock_config': 'ModuleLockConfig'
    }

    def __init__(self, domain_config=None, feature_config=None, module_lock_config=None, _configuration=None):  # noqa: E501
        """DescribeCdnConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._domain_config = None
        self._feature_config = None
        self._module_lock_config = None
        self.discriminator = None

        if domain_config is not None:
            self.domain_config = domain_config
        if feature_config is not None:
            self.feature_config = feature_config
        if module_lock_config is not None:
            self.module_lock_config = module_lock_config

    @property
    def domain_config(self):
        """Gets the domain_config of this DescribeCdnConfigResponse.  # noqa: E501


        :return: The domain_config of this DescribeCdnConfigResponse.  # noqa: E501
        :rtype: DomainConfigForDescribeCdnConfigOutput
        """
        return self._domain_config

    @domain_config.setter
    def domain_config(self, domain_config):
        """Sets the domain_config of this DescribeCdnConfigResponse.


        :param domain_config: The domain_config of this DescribeCdnConfigResponse.  # noqa: E501
        :type: DomainConfigForDescribeCdnConfigOutput
        """

        self._domain_config = domain_config

    @property
    def feature_config(self):
        """Gets the feature_config of this DescribeCdnConfigResponse.  # noqa: E501


        :return: The feature_config of this DescribeCdnConfigResponse.  # noqa: E501
        :rtype: FeatureConfigForDescribeCdnConfigOutput
        """
        return self._feature_config

    @feature_config.setter
    def feature_config(self, feature_config):
        """Sets the feature_config of this DescribeCdnConfigResponse.


        :param feature_config: The feature_config of this DescribeCdnConfigResponse.  # noqa: E501
        :type: FeatureConfigForDescribeCdnConfigOutput
        """

        self._feature_config = feature_config

    @property
    def module_lock_config(self):
        """Gets the module_lock_config of this DescribeCdnConfigResponse.  # noqa: E501


        :return: The module_lock_config of this DescribeCdnConfigResponse.  # noqa: E501
        :rtype: ModuleLockConfigForDescribeCdnConfigOutput
        """
        return self._module_lock_config

    @module_lock_config.setter
    def module_lock_config(self, module_lock_config):
        """Sets the module_lock_config of this DescribeCdnConfigResponse.


        :param module_lock_config: The module_lock_config of this DescribeCdnConfigResponse.  # noqa: E501
        :type: ModuleLockConfigForDescribeCdnConfigOutput
        """

        self._module_lock_config = module_lock_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCdnConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCdnConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCdnConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
