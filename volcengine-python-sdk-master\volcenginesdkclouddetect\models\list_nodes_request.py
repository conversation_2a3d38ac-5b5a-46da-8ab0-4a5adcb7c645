# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListNodesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_mainland': 'bool',
        'line_type': 'list[int]',
        'project_name': 'str'
    }

    attribute_map = {
        'is_mainland': 'IsMainland',
        'line_type': 'LineType',
        'project_name': 'ProjectName'
    }

    def __init__(self, is_mainland=None, line_type=None, project_name=None, _configuration=None):  # noqa: E501
        """ListNodesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_mainland = None
        self._line_type = None
        self._project_name = None
        self.discriminator = None

        self.is_mainland = is_mainland
        if line_type is not None:
            self.line_type = line_type
        if project_name is not None:
            self.project_name = project_name

    @property
    def is_mainland(self):
        """Gets the is_mainland of this ListNodesRequest.  # noqa: E501


        :return: The is_mainland of this ListNodesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_mainland

    @is_mainland.setter
    def is_mainland(self, is_mainland):
        """Sets the is_mainland of this ListNodesRequest.


        :param is_mainland: The is_mainland of this ListNodesRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and is_mainland is None:
            raise ValueError("Invalid value for `is_mainland`, must not be `None`")  # noqa: E501

        self._is_mainland = is_mainland

    @property
    def line_type(self):
        """Gets the line_type of this ListNodesRequest.  # noqa: E501


        :return: The line_type of this ListNodesRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._line_type

    @line_type.setter
    def line_type(self, line_type):
        """Sets the line_type of this ListNodesRequest.


        :param line_type: The line_type of this ListNodesRequest.  # noqa: E501
        :type: list[int]
        """

        self._line_type = line_type

    @property
    def project_name(self):
        """Gets the project_name of this ListNodesRequest.  # noqa: E501


        :return: The project_name of this ListNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListNodesRequest.


        :param project_name: The project_name of this ListNodesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListNodesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListNodesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListNodesRequest):
            return True

        return self.to_dict() != other.to_dict()
