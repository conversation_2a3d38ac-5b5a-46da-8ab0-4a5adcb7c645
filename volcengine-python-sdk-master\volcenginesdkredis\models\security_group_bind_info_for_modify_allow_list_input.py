# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SecurityGroupBindInfoForModifyAllowListInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bind_mode': 'str',
        'security_group_id': 'str'
    }

    attribute_map = {
        'bind_mode': 'BindMode',
        'security_group_id': 'SecurityGroupId'
    }

    def __init__(self, bind_mode=None, security_group_id=None, _configuration=None):  # noqa: E501
        """SecurityGroupBindInfoForModifyAllowListInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bind_mode = None
        self._security_group_id = None
        self.discriminator = None

        if bind_mode is not None:
            self.bind_mode = bind_mode
        if security_group_id is not None:
            self.security_group_id = security_group_id

    @property
    def bind_mode(self):
        """Gets the bind_mode of this SecurityGroupBindInfoForModifyAllowListInput.  # noqa: E501


        :return: The bind_mode of this SecurityGroupBindInfoForModifyAllowListInput.  # noqa: E501
        :rtype: str
        """
        return self._bind_mode

    @bind_mode.setter
    def bind_mode(self, bind_mode):
        """Sets the bind_mode of this SecurityGroupBindInfoForModifyAllowListInput.


        :param bind_mode: The bind_mode of this SecurityGroupBindInfoForModifyAllowListInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["AssociateEcsIp", "IngressDirectionIp"]  # noqa: E501
        if (self._configuration.client_side_validation and
                bind_mode not in allowed_values):
            raise ValueError(
                "Invalid value for `bind_mode` ({0}), must be one of {1}"  # noqa: E501
                .format(bind_mode, allowed_values)
            )

        self._bind_mode = bind_mode

    @property
    def security_group_id(self):
        """Gets the security_group_id of this SecurityGroupBindInfoForModifyAllowListInput.  # noqa: E501


        :return: The security_group_id of this SecurityGroupBindInfoForModifyAllowListInput.  # noqa: E501
        :rtype: str
        """
        return self._security_group_id

    @security_group_id.setter
    def security_group_id(self, security_group_id):
        """Sets the security_group_id of this SecurityGroupBindInfoForModifyAllowListInput.


        :param security_group_id: The security_group_id of this SecurityGroupBindInfoForModifyAllowListInput.  # noqa: E501
        :type: str
        """

        self._security_group_id = security_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SecurityGroupBindInfoForModifyAllowListInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SecurityGroupBindInfoForModifyAllowListInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SecurityGroupBindInfoForModifyAllowListInput):
            return True

        return self.to_dict() != other.to_dict()
