# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeAffinitySpecForGetDeploymentOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'gpucpu_node_preference': 'str',
        'strategy_type': 'str'
    }

    attribute_map = {
        'gpucpu_node_preference': 'GPUCPUNodePreference',
        'strategy_type': 'StrategyType'
    }

    def __init__(self, gpucpu_node_preference=None, strategy_type=None, _configuration=None):  # noqa: E501
        """NodeAffinitySpecForGetDeploymentOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._gpucpu_node_preference = None
        self._strategy_type = None
        self.discriminator = None

        if gpucpu_node_preference is not None:
            self.gpucpu_node_preference = gpucpu_node_preference
        if strategy_type is not None:
            self.strategy_type = strategy_type

    @property
    def gpucpu_node_preference(self):
        """Gets the gpucpu_node_preference of this NodeAffinitySpecForGetDeploymentOutput.  # noqa: E501


        :return: The gpucpu_node_preference of this NodeAffinitySpecForGetDeploymentOutput.  # noqa: E501
        :rtype: str
        """
        return self._gpucpu_node_preference

    @gpucpu_node_preference.setter
    def gpucpu_node_preference(self, gpucpu_node_preference):
        """Sets the gpucpu_node_preference of this NodeAffinitySpecForGetDeploymentOutput.


        :param gpucpu_node_preference: The gpucpu_node_preference of this NodeAffinitySpecForGetDeploymentOutput.  # noqa: E501
        :type: str
        """

        self._gpucpu_node_preference = gpucpu_node_preference

    @property
    def strategy_type(self):
        """Gets the strategy_type of this NodeAffinitySpecForGetDeploymentOutput.  # noqa: E501


        :return: The strategy_type of this NodeAffinitySpecForGetDeploymentOutput.  # noqa: E501
        :rtype: str
        """
        return self._strategy_type

    @strategy_type.setter
    def strategy_type(self, strategy_type):
        """Sets the strategy_type of this NodeAffinitySpecForGetDeploymentOutput.


        :param strategy_type: The strategy_type of this NodeAffinitySpecForGetDeploymentOutput.  # noqa: E501
        :type: str
        """

        self._strategy_type = strategy_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeAffinitySpecForGetDeploymentOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeAffinitySpecForGetDeploymentOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeAffinitySpecForGetDeploymentOutput):
            return True

        return self.to_dict() != other.to_dict()
