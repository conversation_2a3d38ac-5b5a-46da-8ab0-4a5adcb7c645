# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeBasicAcceleratorResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'bandwidth_package_ids': 'list[str]',
        'bandwidth_package_volume': 'int',
        'begin_time': 'int',
        'billing_type': 'str',
        'charge_type': 'str',
        'create_time': 'int',
        'create_time_str': 'str',
        'cross_domain_bandwidth_ids': 'list[str]',
        'end_point_groups': 'list[EndPointGroupForDescribeBasicAcceleratorOutput]',
        'expired_time': 'int',
        'ip_sets': 'list[IPSetForDescribeBasicAcceleratorOutput]',
        'mode': 'str',
        'name': 'str',
        'project_name': 'str',
        'renew_type': 'int',
        'state': 'str'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'bandwidth_package_ids': 'BandwidthPackageIds',
        'bandwidth_package_volume': 'BandwidthPackageVolume',
        'begin_time': 'BeginTime',
        'billing_type': 'BillingType',
        'charge_type': 'ChargeType',
        'create_time': 'CreateTime',
        'create_time_str': 'CreateTimeStr',
        'cross_domain_bandwidth_ids': 'CrossDomainBandwidthIds',
        'end_point_groups': 'EndPointGroups',
        'expired_time': 'ExpiredTime',
        'ip_sets': 'IPSets',
        'mode': 'Mode',
        'name': 'Name',
        'project_name': 'ProjectName',
        'renew_type': 'RenewType',
        'state': 'State'
    }

    def __init__(self, accelerator_id=None, bandwidth_package_ids=None, bandwidth_package_volume=None, begin_time=None, billing_type=None, charge_type=None, create_time=None, create_time_str=None, cross_domain_bandwidth_ids=None, end_point_groups=None, expired_time=None, ip_sets=None, mode=None, name=None, project_name=None, renew_type=None, state=None, _configuration=None):  # noqa: E501
        """DescribeBasicAcceleratorResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._bandwidth_package_ids = None
        self._bandwidth_package_volume = None
        self._begin_time = None
        self._billing_type = None
        self._charge_type = None
        self._create_time = None
        self._create_time_str = None
        self._cross_domain_bandwidth_ids = None
        self._end_point_groups = None
        self._expired_time = None
        self._ip_sets = None
        self._mode = None
        self._name = None
        self._project_name = None
        self._renew_type = None
        self._state = None
        self.discriminator = None

        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if bandwidth_package_ids is not None:
            self.bandwidth_package_ids = bandwidth_package_ids
        if bandwidth_package_volume is not None:
            self.bandwidth_package_volume = bandwidth_package_volume
        if begin_time is not None:
            self.begin_time = begin_time
        if billing_type is not None:
            self.billing_type = billing_type
        if charge_type is not None:
            self.charge_type = charge_type
        if create_time is not None:
            self.create_time = create_time
        if create_time_str is not None:
            self.create_time_str = create_time_str
        if cross_domain_bandwidth_ids is not None:
            self.cross_domain_bandwidth_ids = cross_domain_bandwidth_ids
        if end_point_groups is not None:
            self.end_point_groups = end_point_groups
        if expired_time is not None:
            self.expired_time = expired_time
        if ip_sets is not None:
            self.ip_sets = ip_sets
        if mode is not None:
            self.mode = mode
        if name is not None:
            self.name = name
        if project_name is not None:
            self.project_name = project_name
        if renew_type is not None:
            self.renew_type = renew_type
        if state is not None:
            self.state = state

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The accelerator_id of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this DescribeBasicAcceleratorResponse.


        :param accelerator_id: The accelerator_id of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def bandwidth_package_ids(self):
        """Gets the bandwidth_package_ids of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The bandwidth_package_ids of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._bandwidth_package_ids

    @bandwidth_package_ids.setter
    def bandwidth_package_ids(self, bandwidth_package_ids):
        """Sets the bandwidth_package_ids of this DescribeBasicAcceleratorResponse.


        :param bandwidth_package_ids: The bandwidth_package_ids of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: list[str]
        """

        self._bandwidth_package_ids = bandwidth_package_ids

    @property
    def bandwidth_package_volume(self):
        """Gets the bandwidth_package_volume of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The bandwidth_package_volume of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_package_volume

    @bandwidth_package_volume.setter
    def bandwidth_package_volume(self, bandwidth_package_volume):
        """Sets the bandwidth_package_volume of this DescribeBasicAcceleratorResponse.


        :param bandwidth_package_volume: The bandwidth_package_volume of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._bandwidth_package_volume = bandwidth_package_volume

    @property
    def begin_time(self):
        """Gets the begin_time of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The begin_time of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._begin_time

    @begin_time.setter
    def begin_time(self, begin_time):
        """Sets the begin_time of this DescribeBasicAcceleratorResponse.


        :param begin_time: The begin_time of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._begin_time = begin_time

    @property
    def billing_type(self):
        """Gets the billing_type of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The billing_type of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this DescribeBasicAcceleratorResponse.


        :param billing_type: The billing_type of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._billing_type = billing_type

    @property
    def charge_type(self):
        """Gets the charge_type of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The charge_type of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this DescribeBasicAcceleratorResponse.


        :param charge_type: The charge_type of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def create_time(self):
        """Gets the create_time of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The create_time of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeBasicAcceleratorResponse.


        :param create_time: The create_time of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def create_time_str(self):
        """Gets the create_time_str of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The create_time_str of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time_str

    @create_time_str.setter
    def create_time_str(self, create_time_str):
        """Sets the create_time_str of this DescribeBasicAcceleratorResponse.


        :param create_time_str: The create_time_str of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._create_time_str = create_time_str

    @property
    def cross_domain_bandwidth_ids(self):
        """Gets the cross_domain_bandwidth_ids of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The cross_domain_bandwidth_ids of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._cross_domain_bandwidth_ids

    @cross_domain_bandwidth_ids.setter
    def cross_domain_bandwidth_ids(self, cross_domain_bandwidth_ids):
        """Sets the cross_domain_bandwidth_ids of this DescribeBasicAcceleratorResponse.


        :param cross_domain_bandwidth_ids: The cross_domain_bandwidth_ids of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: list[str]
        """

        self._cross_domain_bandwidth_ids = cross_domain_bandwidth_ids

    @property
    def end_point_groups(self):
        """Gets the end_point_groups of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The end_point_groups of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: list[EndPointGroupForDescribeBasicAcceleratorOutput]
        """
        return self._end_point_groups

    @end_point_groups.setter
    def end_point_groups(self, end_point_groups):
        """Sets the end_point_groups of this DescribeBasicAcceleratorResponse.


        :param end_point_groups: The end_point_groups of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: list[EndPointGroupForDescribeBasicAcceleratorOutput]
        """

        self._end_point_groups = end_point_groups

    @property
    def expired_time(self):
        """Gets the expired_time of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The expired_time of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this DescribeBasicAcceleratorResponse.


        :param expired_time: The expired_time of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._expired_time = expired_time

    @property
    def ip_sets(self):
        """Gets the ip_sets of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The ip_sets of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: list[IPSetForDescribeBasicAcceleratorOutput]
        """
        return self._ip_sets

    @ip_sets.setter
    def ip_sets(self, ip_sets):
        """Sets the ip_sets of this DescribeBasicAcceleratorResponse.


        :param ip_sets: The ip_sets of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: list[IPSetForDescribeBasicAcceleratorOutput]
        """

        self._ip_sets = ip_sets

    @property
    def mode(self):
        """Gets the mode of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The mode of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._mode

    @mode.setter
    def mode(self, mode):
        """Sets the mode of this DescribeBasicAcceleratorResponse.


        :param mode: The mode of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._mode = mode

    @property
    def name(self):
        """Gets the name of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The name of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DescribeBasicAcceleratorResponse.


        :param name: The name of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The project_name of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeBasicAcceleratorResponse.


        :param project_name: The project_name of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def renew_type(self):
        """Gets the renew_type of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The renew_type of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this DescribeBasicAcceleratorResponse.


        :param renew_type: The renew_type of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._renew_type = renew_type

    @property
    def state(self):
        """Gets the state of this DescribeBasicAcceleratorResponse.  # noqa: E501


        :return: The state of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this DescribeBasicAcceleratorResponse.


        :param state: The state of this DescribeBasicAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeBasicAcceleratorResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeBasicAcceleratorResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeBasicAcceleratorResponse):
            return True

        return self.to_dict() != other.to_dict()
