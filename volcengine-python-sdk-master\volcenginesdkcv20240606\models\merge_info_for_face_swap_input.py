# coding: utf-8

"""
    cv20240606

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MergeInfoForFaceSwapInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'location': 'int',
        'template_location': 'int'
    }

    attribute_map = {
        'location': 'location',
        'template_location': 'template_location'
    }

    def __init__(self, location=None, template_location=None, _configuration=None):  # noqa: E501
        """MergeInfoForFaceSwapInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._location = None
        self._template_location = None
        self.discriminator = None

        if location is not None:
            self.location = location
        if template_location is not None:
            self.template_location = template_location

    @property
    def location(self):
        """Gets the location of this MergeInfoForFaceSwapInput.  # noqa: E501


        :return: The location of this MergeInfoForFaceSwapInput.  # noqa: E501
        :rtype: int
        """
        return self._location

    @location.setter
    def location(self, location):
        """Sets the location of this MergeInfoForFaceSwapInput.


        :param location: The location of this MergeInfoForFaceSwapInput.  # noqa: E501
        :type: int
        """

        self._location = location

    @property
    def template_location(self):
        """Gets the template_location of this MergeInfoForFaceSwapInput.  # noqa: E501


        :return: The template_location of this MergeInfoForFaceSwapInput.  # noqa: E501
        :rtype: int
        """
        return self._template_location

    @template_location.setter
    def template_location(self, template_location):
        """Sets the template_location of this MergeInfoForFaceSwapInput.


        :param template_location: The template_location of this MergeInfoForFaceSwapInput.  # noqa: E501
        :type: int
        """

        self._template_location = template_location

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MergeInfoForFaceSwapInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MergeInfoForFaceSwapInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MergeInfoForFaceSwapInput):
            return True

        return self.to_dict() != other.to_dict()
