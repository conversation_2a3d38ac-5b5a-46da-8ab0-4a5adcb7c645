# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateEDXLinkBandwidthRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth_size': 'int',
        'link_id': 'str'
    }

    attribute_map = {
        'bandwidth_size': 'BandwidthSize',
        'link_id': 'LinkID'
    }

    def __init__(self, bandwidth_size=None, link_id=None, _configuration=None):  # noqa: E501
        """UpdateEDXLinkBandwidthRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth_size = None
        self._link_id = None
        self.discriminator = None

        self.bandwidth_size = bandwidth_size
        self.link_id = link_id

    @property
    def bandwidth_size(self):
        """Gets the bandwidth_size of this UpdateEDXLinkBandwidthRequest.  # noqa: E501


        :return: The bandwidth_size of this UpdateEDXLinkBandwidthRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth_size

    @bandwidth_size.setter
    def bandwidth_size(self, bandwidth_size):
        """Sets the bandwidth_size of this UpdateEDXLinkBandwidthRequest.


        :param bandwidth_size: The bandwidth_size of this UpdateEDXLinkBandwidthRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and bandwidth_size is None:
            raise ValueError("Invalid value for `bandwidth_size`, must not be `None`")  # noqa: E501

        self._bandwidth_size = bandwidth_size

    @property
    def link_id(self):
        """Gets the link_id of this UpdateEDXLinkBandwidthRequest.  # noqa: E501


        :return: The link_id of this UpdateEDXLinkBandwidthRequest.  # noqa: E501
        :rtype: str
        """
        return self._link_id

    @link_id.setter
    def link_id(self, link_id):
        """Sets the link_id of this UpdateEDXLinkBandwidthRequest.


        :param link_id: The link_id of this UpdateEDXLinkBandwidthRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and link_id is None:
            raise ValueError("Invalid value for `link_id`, must not be `None`")  # noqa: E501

        self._link_id = link_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateEDXLinkBandwidthRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateEDXLinkBandwidthRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateEDXLinkBandwidthRequest):
            return True

        return self.to_dict() != other.to_dict()
