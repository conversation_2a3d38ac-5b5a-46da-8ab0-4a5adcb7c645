# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InteractDataForListAccountUserDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comment_count': 'int',
        'coupon_pickup_count': 'int',
        'invite_count': 'int',
        'lottery_count': 'int',
        'lottery_hit_count': 'int',
        'mute_number': 'int',
        'no_interact_number': 'int',
        'questionnaire_count': 'int',
        'reservation_count': 'int',
        'shift_screen_number': 'int',
        'thumb_up_number': 'int',
        'vote_count': 'int'
    }

    attribute_map = {
        'comment_count': 'CommentCount',
        'coupon_pickup_count': 'CouponPickupCount',
        'invite_count': 'InviteCount',
        'lottery_count': 'LotteryCount',
        'lottery_hit_count': 'LotteryHitCount',
        'mute_number': 'MuteNumber',
        'no_interact_number': 'NoInteractNumber',
        'questionnaire_count': 'QuestionnaireCount',
        'reservation_count': 'ReservationCount',
        'shift_screen_number': 'ShiftScreenNumber',
        'thumb_up_number': 'ThumbUpNumber',
        'vote_count': 'VoteCount'
    }

    def __init__(self, comment_count=None, coupon_pickup_count=None, invite_count=None, lottery_count=None, lottery_hit_count=None, mute_number=None, no_interact_number=None, questionnaire_count=None, reservation_count=None, shift_screen_number=None, thumb_up_number=None, vote_count=None, _configuration=None):  # noqa: E501
        """InteractDataForListAccountUserDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comment_count = None
        self._coupon_pickup_count = None
        self._invite_count = None
        self._lottery_count = None
        self._lottery_hit_count = None
        self._mute_number = None
        self._no_interact_number = None
        self._questionnaire_count = None
        self._reservation_count = None
        self._shift_screen_number = None
        self._thumb_up_number = None
        self._vote_count = None
        self.discriminator = None

        if comment_count is not None:
            self.comment_count = comment_count
        if coupon_pickup_count is not None:
            self.coupon_pickup_count = coupon_pickup_count
        if invite_count is not None:
            self.invite_count = invite_count
        if lottery_count is not None:
            self.lottery_count = lottery_count
        if lottery_hit_count is not None:
            self.lottery_hit_count = lottery_hit_count
        if mute_number is not None:
            self.mute_number = mute_number
        if no_interact_number is not None:
            self.no_interact_number = no_interact_number
        if questionnaire_count is not None:
            self.questionnaire_count = questionnaire_count
        if reservation_count is not None:
            self.reservation_count = reservation_count
        if shift_screen_number is not None:
            self.shift_screen_number = shift_screen_number
        if thumb_up_number is not None:
            self.thumb_up_number = thumb_up_number
        if vote_count is not None:
            self.vote_count = vote_count

    @property
    def comment_count(self):
        """Gets the comment_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The comment_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._comment_count

    @comment_count.setter
    def comment_count(self, comment_count):
        """Sets the comment_count of this InteractDataForListAccountUserDataOutput.


        :param comment_count: The comment_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._comment_count = comment_count

    @property
    def coupon_pickup_count(self):
        """Gets the coupon_pickup_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The coupon_pickup_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._coupon_pickup_count

    @coupon_pickup_count.setter
    def coupon_pickup_count(self, coupon_pickup_count):
        """Sets the coupon_pickup_count of this InteractDataForListAccountUserDataOutput.


        :param coupon_pickup_count: The coupon_pickup_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._coupon_pickup_count = coupon_pickup_count

    @property
    def invite_count(self):
        """Gets the invite_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The invite_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._invite_count

    @invite_count.setter
    def invite_count(self, invite_count):
        """Sets the invite_count of this InteractDataForListAccountUserDataOutput.


        :param invite_count: The invite_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._invite_count = invite_count

    @property
    def lottery_count(self):
        """Gets the lottery_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The lottery_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._lottery_count

    @lottery_count.setter
    def lottery_count(self, lottery_count):
        """Sets the lottery_count of this InteractDataForListAccountUserDataOutput.


        :param lottery_count: The lottery_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._lottery_count = lottery_count

    @property
    def lottery_hit_count(self):
        """Gets the lottery_hit_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The lottery_hit_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._lottery_hit_count

    @lottery_hit_count.setter
    def lottery_hit_count(self, lottery_hit_count):
        """Sets the lottery_hit_count of this InteractDataForListAccountUserDataOutput.


        :param lottery_hit_count: The lottery_hit_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._lottery_hit_count = lottery_hit_count

    @property
    def mute_number(self):
        """Gets the mute_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The mute_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._mute_number

    @mute_number.setter
    def mute_number(self, mute_number):
        """Sets the mute_number of this InteractDataForListAccountUserDataOutput.


        :param mute_number: The mute_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._mute_number = mute_number

    @property
    def no_interact_number(self):
        """Gets the no_interact_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The no_interact_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._no_interact_number

    @no_interact_number.setter
    def no_interact_number(self, no_interact_number):
        """Sets the no_interact_number of this InteractDataForListAccountUserDataOutput.


        :param no_interact_number: The no_interact_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._no_interact_number = no_interact_number

    @property
    def questionnaire_count(self):
        """Gets the questionnaire_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The questionnaire_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._questionnaire_count

    @questionnaire_count.setter
    def questionnaire_count(self, questionnaire_count):
        """Sets the questionnaire_count of this InteractDataForListAccountUserDataOutput.


        :param questionnaire_count: The questionnaire_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._questionnaire_count = questionnaire_count

    @property
    def reservation_count(self):
        """Gets the reservation_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The reservation_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._reservation_count

    @reservation_count.setter
    def reservation_count(self, reservation_count):
        """Sets the reservation_count of this InteractDataForListAccountUserDataOutput.


        :param reservation_count: The reservation_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._reservation_count = reservation_count

    @property
    def shift_screen_number(self):
        """Gets the shift_screen_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The shift_screen_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._shift_screen_number

    @shift_screen_number.setter
    def shift_screen_number(self, shift_screen_number):
        """Sets the shift_screen_number of this InteractDataForListAccountUserDataOutput.


        :param shift_screen_number: The shift_screen_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._shift_screen_number = shift_screen_number

    @property
    def thumb_up_number(self):
        """Gets the thumb_up_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The thumb_up_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._thumb_up_number

    @thumb_up_number.setter
    def thumb_up_number(self, thumb_up_number):
        """Sets the thumb_up_number of this InteractDataForListAccountUserDataOutput.


        :param thumb_up_number: The thumb_up_number of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._thumb_up_number = thumb_up_number

    @property
    def vote_count(self):
        """Gets the vote_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501


        :return: The vote_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :rtype: int
        """
        return self._vote_count

    @vote_count.setter
    def vote_count(self, vote_count):
        """Sets the vote_count of this InteractDataForListAccountUserDataOutput.


        :param vote_count: The vote_count of this InteractDataForListAccountUserDataOutput.  # noqa: E501
        :type: int
        """

        self._vote_count = vote_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InteractDataForListAccountUserDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InteractDataForListAccountUserDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InteractDataForListAccountUserDataOutput):
            return True

        return self.to_dict() != other.to_dict()
