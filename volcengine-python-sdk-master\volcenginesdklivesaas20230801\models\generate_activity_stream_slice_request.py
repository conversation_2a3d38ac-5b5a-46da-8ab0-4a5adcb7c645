# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GenerateActivityStreamSliceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'end_time': 'int',
        'is_backup': 'bool',
        'line_id': 'int',
        'start_time': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'end_time': 'EndTime',
        'is_backup': 'IsBackup',
        'line_id': 'LineId',
        'start_time': 'StartTime'
    }

    def __init__(self, activity_id=None, end_time=None, is_backup=None, line_id=None, start_time=None, _configuration=None):  # noqa: E501
        """GenerateActivityStreamSliceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._end_time = None
        self._is_backup = None
        self._line_id = None
        self._start_time = None
        self.discriminator = None

        self.activity_id = activity_id
        self.end_time = end_time
        if is_backup is not None:
            self.is_backup = is_backup
        self.line_id = line_id
        self.start_time = start_time

    @property
    def activity_id(self):
        """Gets the activity_id of this GenerateActivityStreamSliceRequest.  # noqa: E501


        :return: The activity_id of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GenerateActivityStreamSliceRequest.


        :param activity_id: The activity_id of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def end_time(self):
        """Gets the end_time of this GenerateActivityStreamSliceRequest.  # noqa: E501


        :return: The end_time of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this GenerateActivityStreamSliceRequest.


        :param end_time: The end_time of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def is_backup(self):
        """Gets the is_backup of this GenerateActivityStreamSliceRequest.  # noqa: E501


        :return: The is_backup of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_backup

    @is_backup.setter
    def is_backup(self, is_backup):
        """Sets the is_backup of this GenerateActivityStreamSliceRequest.


        :param is_backup: The is_backup of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :type: bool
        """

        self._is_backup = is_backup

    @property
    def line_id(self):
        """Gets the line_id of this GenerateActivityStreamSliceRequest.  # noqa: E501


        :return: The line_id of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :rtype: int
        """
        return self._line_id

    @line_id.setter
    def line_id(self, line_id):
        """Sets the line_id of this GenerateActivityStreamSliceRequest.


        :param line_id: The line_id of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and line_id is None:
            raise ValueError("Invalid value for `line_id`, must not be `None`")  # noqa: E501

        self._line_id = line_id

    @property
    def start_time(self):
        """Gets the start_time of this GenerateActivityStreamSliceRequest.  # noqa: E501


        :return: The start_time of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this GenerateActivityStreamSliceRequest.


        :param start_time: The start_time of this GenerateActivityStreamSliceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GenerateActivityStreamSliceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GenerateActivityStreamSliceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GenerateActivityStreamSliceRequest):
            return True

        return self.to_dict() != other.to_dict()
