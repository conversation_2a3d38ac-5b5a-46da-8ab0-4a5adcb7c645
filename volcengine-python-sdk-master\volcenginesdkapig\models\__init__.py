# coding: utf-8

# flake8: noqa
"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkapig.models.ai_provider_for_create_upstream_input import AIProviderForCreateUpstreamInput
from volcenginesdkapig.models.ai_provider_for_get_upstream_output import AIProviderForGetUpstreamOutput
from volcenginesdkapig.models.ai_provider_for_list_upstreams_output import AIProviderForListUpstreamsOutput
from volcenginesdkapig.models.ai_provider_for_update_upstream_input import AIProviderForUpdateUpstreamInput
from volcenginesdkapig.models.auth_config_for_create_upstream_source_input import AuthConfigForCreateUpstreamSourceInput
from volcenginesdkapig.models.auth_config_for_get_upstream_source_output import AuthConfigForGetUpstreamSourceOutput
from volcenginesdkapig.models.auth_config_for_list_upstream_sources_output import AuthConfigForListUpstreamSourcesOutput
from volcenginesdkapig.models.auth_spec_for_create_gateway_service_input import AuthSpecForCreateGatewayServiceInput
from volcenginesdkapig.models.auth_spec_for_get_gateway_service_output import AuthSpecForGetGatewayServiceOutput
from volcenginesdkapig.models.auth_spec_for_list_gateway_services_output import AuthSpecForListGatewayServicesOutput
from volcenginesdkapig.models.auth_spec_for_update_gateway_service_input import AuthSpecForUpdateGatewayServiceInput
from volcenginesdkapig.models.backend_spec_for_create_gateway_input import BackendSpecForCreateGatewayInput
from volcenginesdkapig.models.backend_spec_for_get_gateway_output import BackendSpecForGetGatewayOutput
from volcenginesdkapig.models.backend_spec_for_list_gateways_output import BackendSpecForListGatewaysOutput
from volcenginesdkapig.models.backend_target_list_for_get_upstream_output import BackendTargetListForGetUpstreamOutput
from volcenginesdkapig.models.backend_target_list_for_list_upstreams_output import BackendTargetListForListUpstreamsOutput
from volcenginesdkapig.models.basic_for_create_upstream_source_input import BasicForCreateUpstreamSourceInput
from volcenginesdkapig.models.basic_for_get_upstream_source_output import BasicForGetUpstreamSourceOutput
from volcenginesdkapig.models.basic_for_list_upstream_sources_output import BasicForListUpstreamSourcesOutput
from volcenginesdkapig.models.circuit_breaking_settings_for_create_upstream_input import CircuitBreakingSettingsForCreateUpstreamInput
from volcenginesdkapig.models.circuit_breaking_settings_for_get_upstream_output import CircuitBreakingSettingsForGetUpstreamOutput
from volcenginesdkapig.models.circuit_breaking_settings_for_list_upstreams_output import CircuitBreakingSettingsForListUpstreamsOutput
from volcenginesdkapig.models.circuit_breaking_settings_for_update_upstream_input import CircuitBreakingSettingsForUpdateUpstreamInput
from volcenginesdkapig.models.consistent_hash_lb_for_create_upstream_input import ConsistentHashLBForCreateUpstreamInput
from volcenginesdkapig.models.consistent_hash_lb_for_get_upstream_output import ConsistentHashLBForGetUpstreamOutput
from volcenginesdkapig.models.consistent_hash_lb_for_list_upstreams_output import ConsistentHashLBForListUpstreamsOutput
from volcenginesdkapig.models.consistent_hash_lb_for_update_upstream_input import ConsistentHashLBForUpdateUpstreamInput
from volcenginesdkapig.models.consumer_for_get_consumer_output import ConsumerForGetConsumerOutput
from volcenginesdkapig.models.create_consumer_credential_request import CreateConsumerCredentialRequest
from volcenginesdkapig.models.create_consumer_credential_response import CreateConsumerCredentialResponse
from volcenginesdkapig.models.create_consumer_request import CreateConsumerRequest
from volcenginesdkapig.models.create_consumer_response import CreateConsumerResponse
from volcenginesdkapig.models.create_gateway_request import CreateGatewayRequest
from volcenginesdkapig.models.create_gateway_response import CreateGatewayResponse
from volcenginesdkapig.models.create_gateway_service_request import CreateGatewayServiceRequest
from volcenginesdkapig.models.create_gateway_service_response import CreateGatewayServiceResponse
from volcenginesdkapig.models.create_plugin_binding_request import CreatePluginBindingRequest
from volcenginesdkapig.models.create_plugin_binding_response import CreatePluginBindingResponse
from volcenginesdkapig.models.create_upstream_request import CreateUpstreamRequest
from volcenginesdkapig.models.create_upstream_response import CreateUpstreamResponse
from volcenginesdkapig.models.create_upstream_source_request import CreateUpstreamSourceRequest
from volcenginesdkapig.models.create_upstream_source_response import CreateUpstreamSourceResponse
from volcenginesdkapig.models.custom_body_params_for_create_upstream_input import CustomBodyParamsForCreateUpstreamInput
from volcenginesdkapig.models.custom_body_params_for_get_upstream_output import CustomBodyParamsForGetUpstreamOutput
from volcenginesdkapig.models.custom_body_params_for_list_upstreams_output import CustomBodyParamsForListUpstreamsOutput
from volcenginesdkapig.models.custom_body_params_for_update_upstream_input import CustomBodyParamsForUpdateUpstreamInput
from volcenginesdkapig.models.custom_domain_for_get_gateway_service_output import CustomDomainForGetGatewayServiceOutput
from volcenginesdkapig.models.custom_domain_for_list_gateway_services_output import CustomDomainForListGatewayServicesOutput
from volcenginesdkapig.models.custom_header_params_for_create_upstream_input import CustomHeaderParamsForCreateUpstreamInput
from volcenginesdkapig.models.custom_header_params_for_get_upstream_output import CustomHeaderParamsForGetUpstreamOutput
from volcenginesdkapig.models.custom_header_params_for_list_upstreams_output import CustomHeaderParamsForListUpstreamsOutput
from volcenginesdkapig.models.custom_header_params_for_update_upstream_input import CustomHeaderParamsForUpdateUpstreamInput
from volcenginesdkapig.models.custom_log_for_get_gateway_output import CustomLogForGetGatewayOutput
from volcenginesdkapig.models.custom_log_for_list_gateways_output import CustomLogForListGatewaysOutput
from volcenginesdkapig.models.custom_log_for_update_gateway_custom_log_input import CustomLogForUpdateGatewayCustomLogInput
from volcenginesdkapig.models.custom_model_service_for_create_upstream_input import CustomModelServiceForCreateUpstreamInput
from volcenginesdkapig.models.custom_model_service_for_get_upstream_output import CustomModelServiceForGetUpstreamOutput
from volcenginesdkapig.models.custom_model_service_for_list_upstreams_output import CustomModelServiceForListUpstreamsOutput
from volcenginesdkapig.models.custom_model_service_for_update_upstream_input import CustomModelServiceForUpdateUpstreamInput
from volcenginesdkapig.models.custom_variable_for_get_gateway_output import CustomVariableForGetGatewayOutput
from volcenginesdkapig.models.custom_variable_for_list_gateways_output import CustomVariableForListGatewaysOutput
from volcenginesdkapig.models.custom_variable_for_update_gateway_custom_log_input import CustomVariableForUpdateGatewayCustomLogInput
from volcenginesdkapig.models.data_for_get_gateway_output import DataForGetGatewayOutput
from volcenginesdkapig.models.data_for_list_gateways_output import DataForListGatewaysOutput
from volcenginesdkapig.models.delete_consumer_credential_request import DeleteConsumerCredentialRequest
from volcenginesdkapig.models.delete_consumer_credential_response import DeleteConsumerCredentialResponse
from volcenginesdkapig.models.delete_consumer_request import DeleteConsumerRequest
from volcenginesdkapig.models.delete_consumer_response import DeleteConsumerResponse
from volcenginesdkapig.models.delete_gateway_request import DeleteGatewayRequest
from volcenginesdkapig.models.delete_gateway_response import DeleteGatewayResponse
from volcenginesdkapig.models.delete_gateway_service_request import DeleteGatewayServiceRequest
from volcenginesdkapig.models.delete_gateway_service_response import DeleteGatewayServiceResponse
from volcenginesdkapig.models.delete_plugin_binding_request import DeletePluginBindingRequest
from volcenginesdkapig.models.delete_plugin_binding_response import DeletePluginBindingResponse
from volcenginesdkapig.models.delete_upstream_request import DeleteUpstreamRequest
from volcenginesdkapig.models.delete_upstream_response import DeleteUpstreamResponse
from volcenginesdkapig.models.delete_upstream_source_request import DeleteUpstreamSourceRequest
from volcenginesdkapig.models.delete_upstream_source_response import DeleteUpstreamSourceResponse
from volcenginesdkapig.models.domain_for_get_gateway_service_output import DomainForGetGatewayServiceOutput
from volcenginesdkapig.models.domain_for_list_gateway_services_output import DomainForListGatewayServicesOutput
from volcenginesdkapig.models.ecs_list_for_create_upstream_input import EcsListForCreateUpstreamInput
from volcenginesdkapig.models.ecs_list_for_get_upstream_output import EcsListForGetUpstreamOutput
from volcenginesdkapig.models.ecs_list_for_list_upstreams_output import EcsListForListUpstreamsOutput
from volcenginesdkapig.models.ecs_list_for_update_upstream_input import EcsListForUpdateUpstreamInput
from volcenginesdkapig.models.event_for_get_gateway_output import EventForGetGatewayOutput
from volcenginesdkapig.models.event_for_list_gateways_output import EventForListGatewaysOutput
from volcenginesdkapig.models.filter_for_list_consumers_input import FilterForListConsumersInput
from volcenginesdkapig.models.filter_for_list_gateway_services_input import FilterForListGatewayServicesInput
from volcenginesdkapig.models.filter_for_list_gateways_input import FilterForListGatewaysInput
from volcenginesdkapig.models.filter_for_list_plugin_bindings_input import FilterForListPluginBindingsInput
from volcenginesdkapig.models.filter_for_list_upstream_sources_input import FilterForListUpstreamSourcesInput
from volcenginesdkapig.models.filter_for_list_upstreams_input import FilterForListUpstreamsInput
from volcenginesdkapig.models.gateway_for_get_gateway_output import GatewayForGetGatewayOutput
from volcenginesdkapig.models.gateway_service_for_get_gateway_service_output import GatewayServiceForGetGatewayServiceOutput
from volcenginesdkapig.models.get_consumer_request import GetConsumerRequest
from volcenginesdkapig.models.get_consumer_response import GetConsumerResponse
from volcenginesdkapig.models.get_gateway_request import GetGatewayRequest
from volcenginesdkapig.models.get_gateway_response import GetGatewayResponse
from volcenginesdkapig.models.get_gateway_service_request import GetGatewayServiceRequest
from volcenginesdkapig.models.get_gateway_service_response import GetGatewayServiceResponse
from volcenginesdkapig.models.get_plugin_binding_request import GetPluginBindingRequest
from volcenginesdkapig.models.get_plugin_binding_response import GetPluginBindingResponse
from volcenginesdkapig.models.get_upstream_request import GetUpstreamRequest
from volcenginesdkapig.models.get_upstream_response import GetUpstreamResponse
from volcenginesdkapig.models.get_upstream_source_request import GetUpstreamSourceRequest
from volcenginesdkapig.models.get_upstream_source_response import GetUpstreamSourceResponse
from volcenginesdkapig.models.http_cookie_for_create_upstream_input import HTTPCookieForCreateUpstreamInput
from volcenginesdkapig.models.http_cookie_for_get_upstream_output import HTTPCookieForGetUpstreamOutput
from volcenginesdkapig.models.http_cookie_for_list_upstreams_output import HTTPCookieForListUpstreamsOutput
from volcenginesdkapig.models.http_cookie_for_update_upstream_input import HTTPCookieForUpdateUpstreamInput
from volcenginesdkapig.models.hmac_auth_credential_for_create_consumer_credential_input import HmacAuthCredentialForCreateConsumerCredentialInput
from volcenginesdkapig.models.hmac_auth_credential_for_list_consumer_credentials_output import HmacAuthCredentialForListConsumerCredentialsOutput
from volcenginesdkapig.models.ingress_settings_filter_for_list_upstream_sources_input import IngressSettingsFilterForListUpstreamSourcesInput
from volcenginesdkapig.models.ingress_settings_for_create_upstream_source_input import IngressSettingsForCreateUpstreamSourceInput
from volcenginesdkapig.models.ingress_settings_for_get_upstream_source_output import IngressSettingsForGetUpstreamSourceOutput
from volcenginesdkapig.models.ingress_settings_for_list_upstream_sources_output import IngressSettingsForListUpstreamSourcesOutput
from volcenginesdkapig.models.ingress_settings_for_update_upstream_source_input import IngressSettingsForUpdateUpstreamSourceInput
from volcenginesdkapig.models.item_for_list_consumer_credentials_output import ItemForListConsumerCredentialsOutput
from volcenginesdkapig.models.item_for_list_consumers_output import ItemForListConsumersOutput
from volcenginesdkapig.models.item_for_list_gateway_services_output import ItemForListGatewayServicesOutput
from volcenginesdkapig.models.item_for_list_gateways_output import ItemForListGatewaysOutput
from volcenginesdkapig.models.item_for_list_plugin_bindings_output import ItemForListPluginBindingsOutput
from volcenginesdkapig.models.item_for_list_upstream_sources_output import ItemForListUpstreamSourcesOutput
from volcenginesdkapig.models.item_for_list_upstreams_output import ItemForListUpstreamsOutput
from volcenginesdkapig.models.k8_s_service_for_create_upstream_input import K8SServiceForCreateUpstreamInput
from volcenginesdkapig.models.k8_s_service_for_get_upstream_output import K8SServiceForGetUpstreamOutput
from volcenginesdkapig.models.k8_s_service_for_list_upstreams_output import K8SServiceForListUpstreamsOutput
from volcenginesdkapig.models.k8_s_service_for_update_upstream_input import K8SServiceForUpdateUpstreamInput
from volcenginesdkapig.models.k8_s_source_for_create_upstream_source_input import K8SSourceForCreateUpstreamSourceInput
from volcenginesdkapig.models.k8_s_source_for_get_upstream_source_output import K8SSourceForGetUpstreamSourceOutput
from volcenginesdkapig.models.k8_s_source_for_list_upstream_sources_output import K8SSourceForListUpstreamSourcesOutput
from volcenginesdkapig.models.key_auth_credential_for_create_consumer_credential_input import KeyAuthCredentialForCreateConsumerCredentialInput
from volcenginesdkapig.models.key_auth_credential_for_list_consumer_credentials_output import KeyAuthCredentialForListConsumerCredentialsOutput
from volcenginesdkapig.models.label_for_get_upstream_output import LabelForGetUpstreamOutput
from volcenginesdkapig.models.label_for_list_upstreams_output import LabelForListUpstreamsOutput
from volcenginesdkapig.models.list_consumer_credentials_request import ListConsumerCredentialsRequest
from volcenginesdkapig.models.list_consumer_credentials_response import ListConsumerCredentialsResponse
from volcenginesdkapig.models.list_consumers_request import ListConsumersRequest
from volcenginesdkapig.models.list_consumers_response import ListConsumersResponse
from volcenginesdkapig.models.list_gateway_services_request import ListGatewayServicesRequest
from volcenginesdkapig.models.list_gateway_services_response import ListGatewayServicesResponse
from volcenginesdkapig.models.list_gateways_request import ListGatewaysRequest
from volcenginesdkapig.models.list_gateways_response import ListGatewaysResponse
from volcenginesdkapig.models.list_plugin_bindings_request import ListPluginBindingsRequest
from volcenginesdkapig.models.list_plugin_bindings_response import ListPluginBindingsResponse
from volcenginesdkapig.models.list_upstream_sources_request import ListUpstreamSourcesRequest
from volcenginesdkapig.models.list_upstream_sources_response import ListUpstreamSourcesResponse
from volcenginesdkapig.models.list_upstreams_request import ListUpstreamsRequest
from volcenginesdkapig.models.list_upstreams_response import ListUpstreamsResponse
from volcenginesdkapig.models.load_balancer_settings_for_create_upstream_input import LoadBalancerSettingsForCreateUpstreamInput
from volcenginesdkapig.models.load_balancer_settings_for_get_upstream_output import LoadBalancerSettingsForGetUpstreamOutput
from volcenginesdkapig.models.load_balancer_settings_for_list_upstreams_output import LoadBalancerSettingsForListUpstreamsOutput
from volcenginesdkapig.models.load_balancer_settings_for_update_upstream_input import LoadBalancerSettingsForUpdateUpstreamInput
from volcenginesdkapig.models.log_spec_for_create_gateway_input import LogSpecForCreateGatewayInput
from volcenginesdkapig.models.log_spec_for_get_gateway_output import LogSpecForGetGatewayOutput
from volcenginesdkapig.models.log_spec_for_list_gateways_output import LogSpecForListGatewaysOutput
from volcenginesdkapig.models.log_spec_for_update_gateway_log_input import LogSpecForUpdateGatewayLogInput
from volcenginesdkapig.models.monitor_spec_for_create_gateway_input import MonitorSpecForCreateGatewayInput
from volcenginesdkapig.models.monitor_spec_for_get_gateway_output import MonitorSpecForGetGatewayOutput
from volcenginesdkapig.models.monitor_spec_for_list_gateways_output import MonitorSpecForListGatewaysOutput
from volcenginesdkapig.models.monitor_spec_for_update_gateway_input import MonitorSpecForUpdateGatewayInput
from volcenginesdkapig.models.nacos_service_for_create_upstream_input import NacosServiceForCreateUpstreamInput
from volcenginesdkapig.models.nacos_service_for_get_upstream_output import NacosServiceForGetUpstreamOutput
from volcenginesdkapig.models.nacos_service_for_list_upstreams_output import NacosServiceForListUpstreamsOutput
from volcenginesdkapig.models.nacos_service_for_update_upstream_input import NacosServiceForUpdateUpstreamInput
from volcenginesdkapig.models.nacos_source_for_create_upstream_source_input import NacosSourceForCreateUpstreamSourceInput
from volcenginesdkapig.models.nacos_source_for_get_upstream_source_output import NacosSourceForGetUpstreamSourceOutput
from volcenginesdkapig.models.nacos_source_for_list_upstream_sources_output import NacosSourceForListUpstreamSourcesOutput
from volcenginesdkapig.models.network_spec_for_create_gateway_input import NetworkSpecForCreateGatewayInput
from volcenginesdkapig.models.network_spec_for_get_gateway_output import NetworkSpecForGetGatewayOutput
from volcenginesdkapig.models.network_spec_for_list_gateways_output import NetworkSpecForListGatewaysOutput
from volcenginesdkapig.models.network_type_for_create_gateway_input import NetworkTypeForCreateGatewayInput
from volcenginesdkapig.models.network_type_for_get_gateway_output import NetworkTypeForGetGatewayOutput
from volcenginesdkapig.models.network_type_for_list_gateways_output import NetworkTypeForListGatewaysOutput
from volcenginesdkapig.models.request_header_for_get_gateway_output import RequestHeaderForGetGatewayOutput
from volcenginesdkapig.models.request_header_for_list_gateways_output import RequestHeaderForListGatewaysOutput
from volcenginesdkapig.models.request_header_for_update_gateway_custom_log_input import RequestHeaderForUpdateGatewayCustomLogInput
from volcenginesdkapig.models.resource_spec_for_create_gateway_input import ResourceSpecForCreateGatewayInput
from volcenginesdkapig.models.resource_spec_for_get_gateway_output import ResourceSpecForGetGatewayOutput
from volcenginesdkapig.models.resource_spec_for_list_gateways_output import ResourceSpecForListGatewaysOutput
from volcenginesdkapig.models.resource_spec_for_update_gateway_spec_input import ResourceSpecForUpdateGatewaySpecInput
from volcenginesdkapig.models.response_header_for_get_gateway_output import ResponseHeaderForGetGatewayOutput
from volcenginesdkapig.models.response_header_for_list_gateways_output import ResponseHeaderForListGatewaysOutput
from volcenginesdkapig.models.response_header_for_update_gateway_custom_log_input import ResponseHeaderForUpdateGatewayCustomLogInput
from volcenginesdkapig.models.source_spec_for_create_upstream_source_input import SourceSpecForCreateUpstreamSourceInput
from volcenginesdkapig.models.source_spec_for_get_upstream_source_output import SourceSpecForGetUpstreamSourceOutput
from volcenginesdkapig.models.source_spec_for_list_upstream_sources_output import SourceSpecForListUpstreamSourcesOutput
from volcenginesdkapig.models.subnet_for_get_gateway_output import SubnetForGetGatewayOutput
from volcenginesdkapig.models.tag_for_create_gateway_input import TagForCreateGatewayInput
from volcenginesdkapig.models.tag_for_create_upstream_input import TagForCreateUpstreamInput
from volcenginesdkapig.models.tag_for_get_gateway_output import TagForGetGatewayOutput
from volcenginesdkapig.models.tag_for_get_gateway_service_output import TagForGetGatewayServiceOutput
from volcenginesdkapig.models.tag_for_get_upstream_output import TagForGetUpstreamOutput
from volcenginesdkapig.models.tag_for_list_gateway_services_output import TagForListGatewayServicesOutput
from volcenginesdkapig.models.tag_for_list_gateways_input import TagForListGatewaysInput
from volcenginesdkapig.models.tag_for_list_gateways_output import TagForListGatewaysOutput
from volcenginesdkapig.models.tag_for_list_upstreams_output import TagForListUpstreamsOutput
from volcenginesdkapig.models.tls_settings_for_create_upstream_input import TlsSettingsForCreateUpstreamInput
from volcenginesdkapig.models.tls_settings_for_get_upstream_output import TlsSettingsForGetUpstreamOutput
from volcenginesdkapig.models.tls_settings_for_list_upstreams_output import TlsSettingsForListUpstreamsOutput
from volcenginesdkapig.models.tls_settings_for_update_upstream_input import TlsSettingsForUpdateUpstreamInput
from volcenginesdkapig.models.update_consumer_request import UpdateConsumerRequest
from volcenginesdkapig.models.update_consumer_response import UpdateConsumerResponse
from volcenginesdkapig.models.update_gateway_custom_log_request import UpdateGatewayCustomLogRequest
from volcenginesdkapig.models.update_gateway_custom_log_response import UpdateGatewayCustomLogResponse
from volcenginesdkapig.models.update_gateway_log_request import UpdateGatewayLogRequest
from volcenginesdkapig.models.update_gateway_log_response import UpdateGatewayLogResponse
from volcenginesdkapig.models.update_gateway_request import UpdateGatewayRequest
from volcenginesdkapig.models.update_gateway_response import UpdateGatewayResponse
from volcenginesdkapig.models.update_gateway_service_request import UpdateGatewayServiceRequest
from volcenginesdkapig.models.update_gateway_service_response import UpdateGatewayServiceResponse
from volcenginesdkapig.models.update_gateway_spec_request import UpdateGatewaySpecRequest
from volcenginesdkapig.models.update_gateway_spec_response import UpdateGatewaySpecResponse
from volcenginesdkapig.models.update_plugin_binding_request import UpdatePluginBindingRequest
from volcenginesdkapig.models.update_plugin_binding_response import UpdatePluginBindingResponse
from volcenginesdkapig.models.update_upstream_request import UpdateUpstreamRequest
from volcenginesdkapig.models.update_upstream_response import UpdateUpstreamResponse
from volcenginesdkapig.models.update_upstream_source_request import UpdateUpstreamSourceRequest
from volcenginesdkapig.models.update_upstream_source_response import UpdateUpstreamSourceResponse
from volcenginesdkapig.models.upstream_for_get_upstream_output import UpstreamForGetUpstreamOutput
from volcenginesdkapig.models.upstream_source_for_get_upstream_source_output import UpstreamSourceForGetUpstreamSourceOutput
from volcenginesdkapig.models.upstream_spec_for_create_upstream_input import UpstreamSpecForCreateUpstreamInput
from volcenginesdkapig.models.upstream_spec_for_get_upstream_output import UpstreamSpecForGetUpstreamOutput
from volcenginesdkapig.models.upstream_spec_for_list_upstreams_output import UpstreamSpecForListUpstreamsOutput
from volcenginesdkapig.models.upstream_spec_for_update_upstream_input import UpstreamSpecForUpdateUpstreamInput
from volcenginesdkapig.models.ve_faas_for_create_upstream_input import VeFaasForCreateUpstreamInput
from volcenginesdkapig.models.ve_faas_for_get_upstream_output import VeFaasForGetUpstreamOutput
from volcenginesdkapig.models.ve_faas_for_list_upstreams_output import VeFaasForListUpstreamsOutput
from volcenginesdkapig.models.ve_faas_for_update_upstream_input import VeFaasForUpdateUpstreamInput
from volcenginesdkapig.models.version_detail_for_get_upstream_output import VersionDetailForGetUpstreamOutput
from volcenginesdkapig.models.version_detail_for_list_upstreams_output import VersionDetailForListUpstreamsOutput
