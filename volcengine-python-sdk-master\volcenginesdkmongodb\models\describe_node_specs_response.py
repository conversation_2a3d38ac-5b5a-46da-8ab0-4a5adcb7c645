# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNodeSpecsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'config_server_node_specs': 'list[ConfigServerNodeSpecForDescribeNodeSpecsOutput]',
        'mongos_node_specs': 'list[MongosNodeSpecForDescribeNodeSpecsOutput]',
        'node_specs': 'list[NodeSpecForDescribeNodeSpecsOutput]',
        'shard_node_specs': 'list[ShardNodeSpecForDescribeNodeSpecsOutput]'
    }

    attribute_map = {
        'config_server_node_specs': 'ConfigServerNodeSpecs',
        'mongos_node_specs': 'MongosNodeSpecs',
        'node_specs': 'NodeSpecs',
        'shard_node_specs': 'ShardNodeSpecs'
    }

    def __init__(self, config_server_node_specs=None, mongos_node_specs=None, node_specs=None, shard_node_specs=None, _configuration=None):  # noqa: E501
        """DescribeNodeSpecsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._config_server_node_specs = None
        self._mongos_node_specs = None
        self._node_specs = None
        self._shard_node_specs = None
        self.discriminator = None

        if config_server_node_specs is not None:
            self.config_server_node_specs = config_server_node_specs
        if mongos_node_specs is not None:
            self.mongos_node_specs = mongos_node_specs
        if node_specs is not None:
            self.node_specs = node_specs
        if shard_node_specs is not None:
            self.shard_node_specs = shard_node_specs

    @property
    def config_server_node_specs(self):
        """Gets the config_server_node_specs of this DescribeNodeSpecsResponse.  # noqa: E501


        :return: The config_server_node_specs of this DescribeNodeSpecsResponse.  # noqa: E501
        :rtype: list[ConfigServerNodeSpecForDescribeNodeSpecsOutput]
        """
        return self._config_server_node_specs

    @config_server_node_specs.setter
    def config_server_node_specs(self, config_server_node_specs):
        """Sets the config_server_node_specs of this DescribeNodeSpecsResponse.


        :param config_server_node_specs: The config_server_node_specs of this DescribeNodeSpecsResponse.  # noqa: E501
        :type: list[ConfigServerNodeSpecForDescribeNodeSpecsOutput]
        """

        self._config_server_node_specs = config_server_node_specs

    @property
    def mongos_node_specs(self):
        """Gets the mongos_node_specs of this DescribeNodeSpecsResponse.  # noqa: E501


        :return: The mongos_node_specs of this DescribeNodeSpecsResponse.  # noqa: E501
        :rtype: list[MongosNodeSpecForDescribeNodeSpecsOutput]
        """
        return self._mongos_node_specs

    @mongos_node_specs.setter
    def mongos_node_specs(self, mongos_node_specs):
        """Sets the mongos_node_specs of this DescribeNodeSpecsResponse.


        :param mongos_node_specs: The mongos_node_specs of this DescribeNodeSpecsResponse.  # noqa: E501
        :type: list[MongosNodeSpecForDescribeNodeSpecsOutput]
        """

        self._mongos_node_specs = mongos_node_specs

    @property
    def node_specs(self):
        """Gets the node_specs of this DescribeNodeSpecsResponse.  # noqa: E501


        :return: The node_specs of this DescribeNodeSpecsResponse.  # noqa: E501
        :rtype: list[NodeSpecForDescribeNodeSpecsOutput]
        """
        return self._node_specs

    @node_specs.setter
    def node_specs(self, node_specs):
        """Sets the node_specs of this DescribeNodeSpecsResponse.


        :param node_specs: The node_specs of this DescribeNodeSpecsResponse.  # noqa: E501
        :type: list[NodeSpecForDescribeNodeSpecsOutput]
        """

        self._node_specs = node_specs

    @property
    def shard_node_specs(self):
        """Gets the shard_node_specs of this DescribeNodeSpecsResponse.  # noqa: E501


        :return: The shard_node_specs of this DescribeNodeSpecsResponse.  # noqa: E501
        :rtype: list[ShardNodeSpecForDescribeNodeSpecsOutput]
        """
        return self._shard_node_specs

    @shard_node_specs.setter
    def shard_node_specs(self, shard_node_specs):
        """Sets the shard_node_specs of this DescribeNodeSpecsResponse.


        :param shard_node_specs: The shard_node_specs of this DescribeNodeSpecsResponse.  # noqa: E501
        :type: list[ShardNodeSpecForDescribeNodeSpecsOutput]
        """

        self._shard_node_specs = shard_node_specs

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNodeSpecsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNodeSpecsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNodeSpecsResponse):
            return True

        return self.to_dict() != other.to_dict()
