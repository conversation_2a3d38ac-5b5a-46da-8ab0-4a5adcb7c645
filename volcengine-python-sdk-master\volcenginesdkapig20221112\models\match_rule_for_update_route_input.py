# coding: utf-8

"""
    apig20221112

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MatchRuleForUpdateRouteInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'header': 'list[HeaderForUpdateRouteInput]',
        'method': 'list[str]',
        'path': 'PathForUpdateRouteInput',
        'query_string': 'list[QueryStringForUpdateRouteInput]'
    }

    attribute_map = {
        'header': 'Header',
        'method': 'Method',
        'path': 'Path',
        'query_string': 'QueryString'
    }

    def __init__(self, header=None, method=None, path=None, query_string=None, _configuration=None):  # noqa: E501
        """MatchRuleForUpdateRouteInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._header = None
        self._method = None
        self._path = None
        self._query_string = None
        self.discriminator = None

        if header is not None:
            self.header = header
        if method is not None:
            self.method = method
        if path is not None:
            self.path = path
        if query_string is not None:
            self.query_string = query_string

    @property
    def header(self):
        """Gets the header of this MatchRuleForUpdateRouteInput.  # noqa: E501


        :return: The header of this MatchRuleForUpdateRouteInput.  # noqa: E501
        :rtype: list[HeaderForUpdateRouteInput]
        """
        return self._header

    @header.setter
    def header(self, header):
        """Sets the header of this MatchRuleForUpdateRouteInput.


        :param header: The header of this MatchRuleForUpdateRouteInput.  # noqa: E501
        :type: list[HeaderForUpdateRouteInput]
        """

        self._header = header

    @property
    def method(self):
        """Gets the method of this MatchRuleForUpdateRouteInput.  # noqa: E501


        :return: The method of this MatchRuleForUpdateRouteInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._method

    @method.setter
    def method(self, method):
        """Sets the method of this MatchRuleForUpdateRouteInput.


        :param method: The method of this MatchRuleForUpdateRouteInput.  # noqa: E501
        :type: list[str]
        """

        self._method = method

    @property
    def path(self):
        """Gets the path of this MatchRuleForUpdateRouteInput.  # noqa: E501


        :return: The path of this MatchRuleForUpdateRouteInput.  # noqa: E501
        :rtype: PathForUpdateRouteInput
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this MatchRuleForUpdateRouteInput.


        :param path: The path of this MatchRuleForUpdateRouteInput.  # noqa: E501
        :type: PathForUpdateRouteInput
        """

        self._path = path

    @property
    def query_string(self):
        """Gets the query_string of this MatchRuleForUpdateRouteInput.  # noqa: E501


        :return: The query_string of this MatchRuleForUpdateRouteInput.  # noqa: E501
        :rtype: list[QueryStringForUpdateRouteInput]
        """
        return self._query_string

    @query_string.setter
    def query_string(self, query_string):
        """Sets the query_string of this MatchRuleForUpdateRouteInput.


        :param query_string: The query_string of this MatchRuleForUpdateRouteInput.  # noqa: E501
        :type: list[QueryStringForUpdateRouteInput]
        """

        self._query_string = query_string

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MatchRuleForUpdateRouteInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MatchRuleForUpdateRouteInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MatchRuleForUpdateRouteInput):
            return True

        return self.to_dict() != other.to_dict()
