# coding: utf-8

"""
    auto_scaling

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CompleteLifecycleActivityRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'lifecycle_activity_id': 'str',
        'lifecycle_activity_policy': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'lifecycle_activity_id': 'LifecycleActivityId',
        'lifecycle_activity_policy': 'LifecycleActivityPolicy'
    }

    def __init__(self, client_token=None, lifecycle_activity_id=None, lifecycle_activity_policy=None, _configuration=None):  # noqa: E501
        """CompleteLifecycleActivityRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._lifecycle_activity_id = None
        self._lifecycle_activity_policy = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        self.lifecycle_activity_id = lifecycle_activity_id
        if lifecycle_activity_policy is not None:
            self.lifecycle_activity_policy = lifecycle_activity_policy

    @property
    def client_token(self):
        """Gets the client_token of this CompleteLifecycleActivityRequest.  # noqa: E501


        :return: The client_token of this CompleteLifecycleActivityRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CompleteLifecycleActivityRequest.


        :param client_token: The client_token of this CompleteLifecycleActivityRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def lifecycle_activity_id(self):
        """Gets the lifecycle_activity_id of this CompleteLifecycleActivityRequest.  # noqa: E501


        :return: The lifecycle_activity_id of this CompleteLifecycleActivityRequest.  # noqa: E501
        :rtype: str
        """
        return self._lifecycle_activity_id

    @lifecycle_activity_id.setter
    def lifecycle_activity_id(self, lifecycle_activity_id):
        """Sets the lifecycle_activity_id of this CompleteLifecycleActivityRequest.


        :param lifecycle_activity_id: The lifecycle_activity_id of this CompleteLifecycleActivityRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and lifecycle_activity_id is None:
            raise ValueError("Invalid value for `lifecycle_activity_id`, must not be `None`")  # noqa: E501

        self._lifecycle_activity_id = lifecycle_activity_id

    @property
    def lifecycle_activity_policy(self):
        """Gets the lifecycle_activity_policy of this CompleteLifecycleActivityRequest.  # noqa: E501


        :return: The lifecycle_activity_policy of this CompleteLifecycleActivityRequest.  # noqa: E501
        :rtype: str
        """
        return self._lifecycle_activity_policy

    @lifecycle_activity_policy.setter
    def lifecycle_activity_policy(self, lifecycle_activity_policy):
        """Sets the lifecycle_activity_policy of this CompleteLifecycleActivityRequest.


        :param lifecycle_activity_policy: The lifecycle_activity_policy of this CompleteLifecycleActivityRequest.  # noqa: E501
        :type: str
        """

        self._lifecycle_activity_policy = lifecycle_activity_policy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CompleteLifecycleActivityRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CompleteLifecycleActivityRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CompleteLifecycleActivityRequest):
            return True

        return self.to_dict() != other.to_dict()
