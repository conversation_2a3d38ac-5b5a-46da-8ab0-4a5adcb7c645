# coding: utf-8

"""
    fwcenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyControlPolicyPositionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'direction': 'str',
        'new_prio': 'int',
        'rule_id': 'str'
    }

    attribute_map = {
        'direction': 'Direction',
        'new_prio': 'NewPrio',
        'rule_id': 'RuleId'
    }

    def __init__(self, direction=None, new_prio=None, rule_id=None, _configuration=None):  # noqa: E501
        """ModifyControlPolicyPositionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._direction = None
        self._new_prio = None
        self._rule_id = None
        self.discriminator = None

        self.direction = direction
        self.new_prio = new_prio
        self.rule_id = rule_id

    @property
    def direction(self):
        """Gets the direction of this ModifyControlPolicyPositionRequest.  # noqa: E501


        :return: The direction of this ModifyControlPolicyPositionRequest.  # noqa: E501
        :rtype: str
        """
        return self._direction

    @direction.setter
    def direction(self, direction):
        """Sets the direction of this ModifyControlPolicyPositionRequest.


        :param direction: The direction of this ModifyControlPolicyPositionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and direction is None:
            raise ValueError("Invalid value for `direction`, must not be `None`")  # noqa: E501
        allowed_values = ["in", "out"]  # noqa: E501
        if (self._configuration.client_side_validation and
                direction not in allowed_values):
            raise ValueError(
                "Invalid value for `direction` ({0}), must be one of {1}"  # noqa: E501
                .format(direction, allowed_values)
            )

        self._direction = direction

    @property
    def new_prio(self):
        """Gets the new_prio of this ModifyControlPolicyPositionRequest.  # noqa: E501


        :return: The new_prio of this ModifyControlPolicyPositionRequest.  # noqa: E501
        :rtype: int
        """
        return self._new_prio

    @new_prio.setter
    def new_prio(self, new_prio):
        """Sets the new_prio of this ModifyControlPolicyPositionRequest.


        :param new_prio: The new_prio of this ModifyControlPolicyPositionRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and new_prio is None:
            raise ValueError("Invalid value for `new_prio`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                new_prio is not None and new_prio < 1):  # noqa: E501
            raise ValueError("Invalid value for `new_prio`, must be a value greater than or equal to `1`")  # noqa: E501

        self._new_prio = new_prio

    @property
    def rule_id(self):
        """Gets the rule_id of this ModifyControlPolicyPositionRequest.  # noqa: E501


        :return: The rule_id of this ModifyControlPolicyPositionRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this ModifyControlPolicyPositionRequest.


        :param rule_id: The rule_id of this ModifyControlPolicyPositionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_id is None:
            raise ValueError("Invalid value for `rule_id`, must not be `None`")  # noqa: E501

        self._rule_id = rule_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyControlPolicyPositionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyControlPolicyPositionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyControlPolicyPositionRequest):
            return True

        return self.to_dict() != other.to_dict()
