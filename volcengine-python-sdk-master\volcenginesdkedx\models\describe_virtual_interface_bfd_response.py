# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeVirtualInterfaceBFDResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'detect_multiplier': 'int',
        'enabled': 'bool',
        'receive_interval': 'int',
        'state': 'str',
        'transmit_interval': 'int',
        'vif_instance_id': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'detect_multiplier': 'DetectMultiplier',
        'enabled': 'Enabled',
        'receive_interval': 'ReceiveInterval',
        'state': 'State',
        'transmit_interval': 'TransmitInterval',
        'vif_instance_id': 'VIFInstanceId'
    }

    def __init__(self, create_time=None, detect_multiplier=None, enabled=None, receive_interval=None, state=None, transmit_interval=None, vif_instance_id=None, _configuration=None):  # noqa: E501
        """DescribeVirtualInterfaceBFDResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._detect_multiplier = None
        self._enabled = None
        self._receive_interval = None
        self._state = None
        self._transmit_interval = None
        self._vif_instance_id = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if detect_multiplier is not None:
            self.detect_multiplier = detect_multiplier
        if enabled is not None:
            self.enabled = enabled
        if receive_interval is not None:
            self.receive_interval = receive_interval
        if state is not None:
            self.state = state
        if transmit_interval is not None:
            self.transmit_interval = transmit_interval
        if vif_instance_id is not None:
            self.vif_instance_id = vif_instance_id

    @property
    def create_time(self):
        """Gets the create_time of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501


        :return: The create_time of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeVirtualInterfaceBFDResponse.


        :param create_time: The create_time of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def detect_multiplier(self):
        """Gets the detect_multiplier of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501


        :return: The detect_multiplier of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :rtype: int
        """
        return self._detect_multiplier

    @detect_multiplier.setter
    def detect_multiplier(self, detect_multiplier):
        """Sets the detect_multiplier of this DescribeVirtualInterfaceBFDResponse.


        :param detect_multiplier: The detect_multiplier of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :type: int
        """

        self._detect_multiplier = detect_multiplier

    @property
    def enabled(self):
        """Gets the enabled of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501


        :return: The enabled of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this DescribeVirtualInterfaceBFDResponse.


        :param enabled: The enabled of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def receive_interval(self):
        """Gets the receive_interval of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501


        :return: The receive_interval of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :rtype: int
        """
        return self._receive_interval

    @receive_interval.setter
    def receive_interval(self, receive_interval):
        """Sets the receive_interval of this DescribeVirtualInterfaceBFDResponse.


        :param receive_interval: The receive_interval of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :type: int
        """

        self._receive_interval = receive_interval

    @property
    def state(self):
        """Gets the state of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501


        :return: The state of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this DescribeVirtualInterfaceBFDResponse.


        :param state: The state of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def transmit_interval(self):
        """Gets the transmit_interval of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501


        :return: The transmit_interval of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :rtype: int
        """
        return self._transmit_interval

    @transmit_interval.setter
    def transmit_interval(self, transmit_interval):
        """Sets the transmit_interval of this DescribeVirtualInterfaceBFDResponse.


        :param transmit_interval: The transmit_interval of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :type: int
        """

        self._transmit_interval = transmit_interval

    @property
    def vif_instance_id(self):
        """Gets the vif_instance_id of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501


        :return: The vif_instance_id of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :rtype: str
        """
        return self._vif_instance_id

    @vif_instance_id.setter
    def vif_instance_id(self, vif_instance_id):
        """Sets the vif_instance_id of this DescribeVirtualInterfaceBFDResponse.


        :param vif_instance_id: The vif_instance_id of this DescribeVirtualInterfaceBFDResponse.  # noqa: E501
        :type: str
        """

        self._vif_instance_id = vif_instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeVirtualInterfaceBFDResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeVirtualInterfaceBFDResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeVirtualInterfaceBFDResponse):
            return True

        return self.to_dict() != other.to_dict()
