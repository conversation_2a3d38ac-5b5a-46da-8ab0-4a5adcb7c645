# coding: utf-8

"""
    tis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeviceInfoListForGetQuotaInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'field_name': 'str',
        'field_value': 'str'
    }

    attribute_map = {
        'field_name': 'fieldName',
        'field_value': 'fieldValue'
    }

    def __init__(self, field_name=None, field_value=None, _configuration=None):  # noqa: E501
        """DeviceInfoListForGetQuotaInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._field_name = None
        self._field_value = None
        self.discriminator = None

        if field_name is not None:
            self.field_name = field_name
        if field_value is not None:
            self.field_value = field_value

    @property
    def field_name(self):
        """Gets the field_name of this DeviceInfoListForGetQuotaInfoOutput.  # noqa: E501


        :return: The field_name of this DeviceInfoListForGetQuotaInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._field_name

    @field_name.setter
    def field_name(self, field_name):
        """Sets the field_name of this DeviceInfoListForGetQuotaInfoOutput.


        :param field_name: The field_name of this DeviceInfoListForGetQuotaInfoOutput.  # noqa: E501
        :type: str
        """

        self._field_name = field_name

    @property
    def field_value(self):
        """Gets the field_value of this DeviceInfoListForGetQuotaInfoOutput.  # noqa: E501


        :return: The field_value of this DeviceInfoListForGetQuotaInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._field_value

    @field_value.setter
    def field_value(self, field_value):
        """Sets the field_value of this DeviceInfoListForGetQuotaInfoOutput.


        :param field_value: The field_value of this DeviceInfoListForGetQuotaInfoOutput.  # noqa: E501
        :type: str
        """

        self._field_value = field_value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeviceInfoListForGetQuotaInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeviceInfoListForGetQuotaInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeviceInfoListForGetQuotaInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
