# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VoteListForGetVoteListAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'choice_type': 'str',
        'deadline': 'int',
        'status': 'str',
        'title': 'str',
        'total_count': 'int',
        'vote_id': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'choice_type': 'ChoiceType',
        'deadline': 'Deadline',
        'status': 'Status',
        'title': 'Title',
        'total_count': 'TotalCount',
        'vote_id': 'VoteId'
    }

    def __init__(self, activity_id=None, choice_type=None, deadline=None, status=None, title=None, total_count=None, vote_id=None, _configuration=None):  # noqa: E501
        """VoteListForGetVoteListAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._choice_type = None
        self._deadline = None
        self._status = None
        self._title = None
        self._total_count = None
        self._vote_id = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if choice_type is not None:
            self.choice_type = choice_type
        if deadline is not None:
            self.deadline = deadline
        if status is not None:
            self.status = status
        if title is not None:
            self.title = title
        if total_count is not None:
            self.total_count = total_count
        if vote_id is not None:
            self.vote_id = vote_id

    @property
    def activity_id(self):
        """Gets the activity_id of this VoteListForGetVoteListAPIOutput.  # noqa: E501


        :return: The activity_id of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this VoteListForGetVoteListAPIOutput.


        :param activity_id: The activity_id of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def choice_type(self):
        """Gets the choice_type of this VoteListForGetVoteListAPIOutput.  # noqa: E501


        :return: The choice_type of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._choice_type

    @choice_type.setter
    def choice_type(self, choice_type):
        """Sets the choice_type of this VoteListForGetVoteListAPIOutput.


        :param choice_type: The choice_type of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :type: str
        """

        self._choice_type = choice_type

    @property
    def deadline(self):
        """Gets the deadline of this VoteListForGetVoteListAPIOutput.  # noqa: E501


        :return: The deadline of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._deadline

    @deadline.setter
    def deadline(self, deadline):
        """Sets the deadline of this VoteListForGetVoteListAPIOutput.


        :param deadline: The deadline of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :type: int
        """

        self._deadline = deadline

    @property
    def status(self):
        """Gets the status of this VoteListForGetVoteListAPIOutput.  # noqa: E501


        :return: The status of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VoteListForGetVoteListAPIOutput.


        :param status: The status of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def title(self):
        """Gets the title of this VoteListForGetVoteListAPIOutput.  # noqa: E501


        :return: The title of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this VoteListForGetVoteListAPIOutput.


        :param title: The title of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def total_count(self):
        """Gets the total_count of this VoteListForGetVoteListAPIOutput.  # noqa: E501


        :return: The total_count of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this VoteListForGetVoteListAPIOutput.


        :param total_count: The total_count of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    @property
    def vote_id(self):
        """Gets the vote_id of this VoteListForGetVoteListAPIOutput.  # noqa: E501


        :return: The vote_id of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._vote_id

    @vote_id.setter
    def vote_id(self, vote_id):
        """Sets the vote_id of this VoteListForGetVoteListAPIOutput.


        :param vote_id: The vote_id of this VoteListForGetVoteListAPIOutput.  # noqa: E501
        :type: str
        """

        self._vote_id = vote_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VoteListForGetVoteListAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VoteListForGetVoteListAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VoteListForGetVoteListAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
