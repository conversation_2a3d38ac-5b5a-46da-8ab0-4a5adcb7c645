# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeviceItemForGetDeviceChannelsV2Output(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'channel_id': 'str',
        'civil_code': 'str',
        'download_speed': 'str',
        'ip_address': 'str',
        'latitude': 'float',
        'longitude': 'float',
        'manufacturer': 'str',
        'model': 'str',
        'name': 'str',
        'ptz_type': 'int',
        'parent_id': 'str',
        'parental': 'str',
        'port': 'int',
        'register_way': 'str',
        'resolution': 'str',
        'secrecy': 'str',
        'status': 'str'
    }

    attribute_map = {
        'channel_id': 'ChannelID',
        'civil_code': 'CivilCode',
        'download_speed': 'DownloadSpeed',
        'ip_address': 'IPAddress',
        'latitude': 'Latitude',
        'longitude': 'Longitude',
        'manufacturer': 'Manufacturer',
        'model': 'Model',
        'name': 'Name',
        'ptz_type': 'PTZType',
        'parent_id': 'ParentID',
        'parental': 'Parental',
        'port': 'Port',
        'register_way': 'RegisterWay',
        'resolution': 'Resolution',
        'secrecy': 'Secrecy',
        'status': 'Status'
    }

    def __init__(self, channel_id=None, civil_code=None, download_speed=None, ip_address=None, latitude=None, longitude=None, manufacturer=None, model=None, name=None, ptz_type=None, parent_id=None, parental=None, port=None, register_way=None, resolution=None, secrecy=None, status=None, _configuration=None):  # noqa: E501
        """DeviceItemForGetDeviceChannelsV2Output - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._channel_id = None
        self._civil_code = None
        self._download_speed = None
        self._ip_address = None
        self._latitude = None
        self._longitude = None
        self._manufacturer = None
        self._model = None
        self._name = None
        self._ptz_type = None
        self._parent_id = None
        self._parental = None
        self._port = None
        self._register_way = None
        self._resolution = None
        self._secrecy = None
        self._status = None
        self.discriminator = None

        if channel_id is not None:
            self.channel_id = channel_id
        if civil_code is not None:
            self.civil_code = civil_code
        if download_speed is not None:
            self.download_speed = download_speed
        if ip_address is not None:
            self.ip_address = ip_address
        if latitude is not None:
            self.latitude = latitude
        if longitude is not None:
            self.longitude = longitude
        if manufacturer is not None:
            self.manufacturer = manufacturer
        if model is not None:
            self.model = model
        if name is not None:
            self.name = name
        if ptz_type is not None:
            self.ptz_type = ptz_type
        if parent_id is not None:
            self.parent_id = parent_id
        if parental is not None:
            self.parental = parental
        if port is not None:
            self.port = port
        if register_way is not None:
            self.register_way = register_way
        if resolution is not None:
            self.resolution = resolution
        if secrecy is not None:
            self.secrecy = secrecy
        if status is not None:
            self.status = status

    @property
    def channel_id(self):
        """Gets the channel_id of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The channel_id of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._channel_id

    @channel_id.setter
    def channel_id(self, channel_id):
        """Sets the channel_id of this DeviceItemForGetDeviceChannelsV2Output.


        :param channel_id: The channel_id of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._channel_id = channel_id

    @property
    def civil_code(self):
        """Gets the civil_code of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The civil_code of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._civil_code

    @civil_code.setter
    def civil_code(self, civil_code):
        """Sets the civil_code of this DeviceItemForGetDeviceChannelsV2Output.


        :param civil_code: The civil_code of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._civil_code = civil_code

    @property
    def download_speed(self):
        """Gets the download_speed of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The download_speed of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._download_speed

    @download_speed.setter
    def download_speed(self, download_speed):
        """Sets the download_speed of this DeviceItemForGetDeviceChannelsV2Output.


        :param download_speed: The download_speed of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._download_speed = download_speed

    @property
    def ip_address(self):
        """Gets the ip_address of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The ip_address of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._ip_address

    @ip_address.setter
    def ip_address(self, ip_address):
        """Sets the ip_address of this DeviceItemForGetDeviceChannelsV2Output.


        :param ip_address: The ip_address of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._ip_address = ip_address

    @property
    def latitude(self):
        """Gets the latitude of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The latitude of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: float
        """
        return self._latitude

    @latitude.setter
    def latitude(self, latitude):
        """Sets the latitude of this DeviceItemForGetDeviceChannelsV2Output.


        :param latitude: The latitude of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: float
        """

        self._latitude = latitude

    @property
    def longitude(self):
        """Gets the longitude of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The longitude of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: float
        """
        return self._longitude

    @longitude.setter
    def longitude(self, longitude):
        """Sets the longitude of this DeviceItemForGetDeviceChannelsV2Output.


        :param longitude: The longitude of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: float
        """

        self._longitude = longitude

    @property
    def manufacturer(self):
        """Gets the manufacturer of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The manufacturer of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._manufacturer

    @manufacturer.setter
    def manufacturer(self, manufacturer):
        """Sets the manufacturer of this DeviceItemForGetDeviceChannelsV2Output.


        :param manufacturer: The manufacturer of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._manufacturer = manufacturer

    @property
    def model(self):
        """Gets the model of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The model of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._model

    @model.setter
    def model(self, model):
        """Sets the model of this DeviceItemForGetDeviceChannelsV2Output.


        :param model: The model of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._model = model

    @property
    def name(self):
        """Gets the name of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The name of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DeviceItemForGetDeviceChannelsV2Output.


        :param name: The name of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def ptz_type(self):
        """Gets the ptz_type of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The ptz_type of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: int
        """
        return self._ptz_type

    @ptz_type.setter
    def ptz_type(self, ptz_type):
        """Sets the ptz_type of this DeviceItemForGetDeviceChannelsV2Output.


        :param ptz_type: The ptz_type of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: int
        """

        self._ptz_type = ptz_type

    @property
    def parent_id(self):
        """Gets the parent_id of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The parent_id of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._parent_id

    @parent_id.setter
    def parent_id(self, parent_id):
        """Sets the parent_id of this DeviceItemForGetDeviceChannelsV2Output.


        :param parent_id: The parent_id of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._parent_id = parent_id

    @property
    def parental(self):
        """Gets the parental of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The parental of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._parental

    @parental.setter
    def parental(self, parental):
        """Sets the parental of this DeviceItemForGetDeviceChannelsV2Output.


        :param parental: The parental of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._parental = parental

    @property
    def port(self):
        """Gets the port of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The port of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this DeviceItemForGetDeviceChannelsV2Output.


        :param port: The port of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def register_way(self):
        """Gets the register_way of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The register_way of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._register_way

    @register_way.setter
    def register_way(self, register_way):
        """Sets the register_way of this DeviceItemForGetDeviceChannelsV2Output.


        :param register_way: The register_way of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._register_way = register_way

    @property
    def resolution(self):
        """Gets the resolution of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The resolution of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._resolution

    @resolution.setter
    def resolution(self, resolution):
        """Sets the resolution of this DeviceItemForGetDeviceChannelsV2Output.


        :param resolution: The resolution of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._resolution = resolution

    @property
    def secrecy(self):
        """Gets the secrecy of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The secrecy of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._secrecy

    @secrecy.setter
    def secrecy(self, secrecy):
        """Sets the secrecy of this DeviceItemForGetDeviceChannelsV2Output.


        :param secrecy: The secrecy of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._secrecy = secrecy

    @property
    def status(self):
        """Gets the status of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501


        :return: The status of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DeviceItemForGetDeviceChannelsV2Output.


        :param status: The status of this DeviceItemForGetDeviceChannelsV2Output.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeviceItemForGetDeviceChannelsV2Output, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeviceItemForGetDeviceChannelsV2Output):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeviceItemForGetDeviceChannelsV2Output):
            return True

        return self.to_dict() != other.to_dict()
