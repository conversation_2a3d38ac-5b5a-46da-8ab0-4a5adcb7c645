# coding: utf-8

"""
    rds_postgresql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ParameterForModifyDBInstanceParametersInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'checking_code': 'str',
        'default_value': 'str',
        'description': 'str',
        'description_zh': 'str',
        'force_restart': 'bool',
        'name': 'str',
        'type': 'str',
        'value': 'str'
    }

    attribute_map = {
        'checking_code': 'CheckingCode',
        'default_value': 'DefaultValue',
        'description': 'Description',
        'description_zh': 'DescriptionZH',
        'force_restart': 'ForceRestart',
        'name': 'Name',
        'type': 'Type',
        'value': 'Value'
    }

    def __init__(self, checking_code=None, default_value=None, description=None, description_zh=None, force_restart=None, name=None, type=None, value=None, _configuration=None):  # noqa: E501
        """ParameterForModifyDBInstanceParametersInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._checking_code = None
        self._default_value = None
        self._description = None
        self._description_zh = None
        self._force_restart = None
        self._name = None
        self._type = None
        self._value = None
        self.discriminator = None

        if checking_code is not None:
            self.checking_code = checking_code
        if default_value is not None:
            self.default_value = default_value
        if description is not None:
            self.description = description
        if description_zh is not None:
            self.description_zh = description_zh
        if force_restart is not None:
            self.force_restart = force_restart
        if name is not None:
            self.name = name
        if type is not None:
            self.type = type
        if value is not None:
            self.value = value

    @property
    def checking_code(self):
        """Gets the checking_code of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501


        :return: The checking_code of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :rtype: str
        """
        return self._checking_code

    @checking_code.setter
    def checking_code(self, checking_code):
        """Sets the checking_code of this ParameterForModifyDBInstanceParametersInput.


        :param checking_code: The checking_code of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :type: str
        """

        self._checking_code = checking_code

    @property
    def default_value(self):
        """Gets the default_value of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501


        :return: The default_value of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :rtype: str
        """
        return self._default_value

    @default_value.setter
    def default_value(self, default_value):
        """Sets the default_value of this ParameterForModifyDBInstanceParametersInput.


        :param default_value: The default_value of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :type: str
        """

        self._default_value = default_value

    @property
    def description(self):
        """Gets the description of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501


        :return: The description of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ParameterForModifyDBInstanceParametersInput.


        :param description: The description of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def description_zh(self):
        """Gets the description_zh of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501


        :return: The description_zh of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :rtype: str
        """
        return self._description_zh

    @description_zh.setter
    def description_zh(self, description_zh):
        """Sets the description_zh of this ParameterForModifyDBInstanceParametersInput.


        :param description_zh: The description_zh of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :type: str
        """

        self._description_zh = description_zh

    @property
    def force_restart(self):
        """Gets the force_restart of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501


        :return: The force_restart of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :rtype: bool
        """
        return self._force_restart

    @force_restart.setter
    def force_restart(self, force_restart):
        """Sets the force_restart of this ParameterForModifyDBInstanceParametersInput.


        :param force_restart: The force_restart of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :type: bool
        """

        self._force_restart = force_restart

    @property
    def name(self):
        """Gets the name of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501


        :return: The name of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ParameterForModifyDBInstanceParametersInput.


        :param name: The name of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def type(self):
        """Gets the type of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501


        :return: The type of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ParameterForModifyDBInstanceParametersInput.


        :param type: The type of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def value(self):
        """Gets the value of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501


        :return: The value of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :rtype: str
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this ParameterForModifyDBInstanceParametersInput.


        :param value: The value of this ParameterForModifyDBInstanceParametersInput.  # noqa: E501
        :type: str
        """

        self._value = value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ParameterForModifyDBInstanceParametersInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ParameterForModifyDBInstanceParametersInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ParameterForModifyDBInstanceParametersInput):
            return True

        return self.to_dict() != other.to_dict()
