# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetRepoRegistrySummaryOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'total_count': 'int',
        'unready_count': 'int'
    }

    attribute_map = {
        'total_count': 'TotalCount',
        'unready_count': 'UnreadyCount'
    }

    def __init__(self, total_count=None, unready_count=None, _configuration=None):  # noqa: E501
        """DataForGetRepoRegistrySummaryOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._total_count = None
        self._unready_count = None
        self.discriminator = None

        if total_count is not None:
            self.total_count = total_count
        if unready_count is not None:
            self.unready_count = unready_count

    @property
    def total_count(self):
        """Gets the total_count of this DataForGetRepoRegistrySummaryOutput.  # noqa: E501


        :return: The total_count of this DataForGetRepoRegistrySummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this DataForGetRepoRegistrySummaryOutput.


        :param total_count: The total_count of this DataForGetRepoRegistrySummaryOutput.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    @property
    def unready_count(self):
        """Gets the unready_count of this DataForGetRepoRegistrySummaryOutput.  # noqa: E501


        :return: The unready_count of this DataForGetRepoRegistrySummaryOutput.  # noqa: E501
        :rtype: int
        """
        return self._unready_count

    @unready_count.setter
    def unready_count(self, unready_count):
        """Sets the unready_count of this DataForGetRepoRegistrySummaryOutput.


        :param unready_count: The unready_count of this DataForGetRepoRegistrySummaryOutput.  # noqa: E501
        :type: int
        """

        self._unready_count = unready_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetRepoRegistrySummaryOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetRepoRegistrySummaryOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetRepoRegistrySummaryOutput):
            return True

        return self.to_dict() != other.to_dict()
