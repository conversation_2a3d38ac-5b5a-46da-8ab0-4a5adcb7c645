# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EmojiForGetActivityCustomEmojiSetDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'emoji_id': 'int',
        'emoji_image': 'str',
        'emoji_location': 'int',
        'emoji_name': 'str',
        'emoji_type': 'int'
    }

    attribute_map = {
        'emoji_id': 'EmojiId',
        'emoji_image': 'EmojiImage',
        'emoji_location': 'EmojiLocation',
        'emoji_name': 'EmojiName',
        'emoji_type': 'EmojiType'
    }

    def __init__(self, emoji_id=None, emoji_image=None, emoji_location=None, emoji_name=None, emoji_type=None, _configuration=None):  # noqa: E501
        """EmojiForGetActivityCustomEmojiSetDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._emoji_id = None
        self._emoji_image = None
        self._emoji_location = None
        self._emoji_name = None
        self._emoji_type = None
        self.discriminator = None

        if emoji_id is not None:
            self.emoji_id = emoji_id
        if emoji_image is not None:
            self.emoji_image = emoji_image
        if emoji_location is not None:
            self.emoji_location = emoji_location
        if emoji_name is not None:
            self.emoji_name = emoji_name
        if emoji_type is not None:
            self.emoji_type = emoji_type

    @property
    def emoji_id(self):
        """Gets the emoji_id of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_id of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._emoji_id

    @emoji_id.setter
    def emoji_id(self, emoji_id):
        """Sets the emoji_id of this EmojiForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_id: The emoji_id of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: int
        """

        self._emoji_id = emoji_id

    @property
    def emoji_image(self):
        """Gets the emoji_image of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_image of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._emoji_image

    @emoji_image.setter
    def emoji_image(self, emoji_image):
        """Sets the emoji_image of this EmojiForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_image: The emoji_image of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: str
        """

        self._emoji_image = emoji_image

    @property
    def emoji_location(self):
        """Gets the emoji_location of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_location of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._emoji_location

    @emoji_location.setter
    def emoji_location(self, emoji_location):
        """Sets the emoji_location of this EmojiForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_location: The emoji_location of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: int
        """

        self._emoji_location = emoji_location

    @property
    def emoji_name(self):
        """Gets the emoji_name of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_name of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._emoji_name

    @emoji_name.setter
    def emoji_name(self, emoji_name):
        """Sets the emoji_name of this EmojiForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_name: The emoji_name of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: str
        """

        self._emoji_name = emoji_name

    @property
    def emoji_type(self):
        """Gets the emoji_type of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501


        :return: The emoji_type of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._emoji_type

    @emoji_type.setter
    def emoji_type(self, emoji_type):
        """Sets the emoji_type of this EmojiForGetActivityCustomEmojiSetDetailOutput.


        :param emoji_type: The emoji_type of this EmojiForGetActivityCustomEmojiSetDetailOutput.  # noqa: E501
        :type: int
        """

        self._emoji_type = emoji_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EmojiForGetActivityCustomEmojiSetDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EmojiForGetActivityCustomEmojiSetDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EmojiForGetActivityCustomEmojiSetDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
