# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RowForListCustomRouteOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_at': 'int',
        'custom_route_id': 'str',
        'custom_route_name': 'str',
        'dst_ip': 'str',
        'proxy_cipher': 'str',
        'proxy_port': 'int',
        'proxy_protocol': 'str',
        'update_at': 'int',
        'zone': 'str'
    }

    attribute_map = {
        'create_at': 'CreateAt',
        'custom_route_id': 'CustomRouteId',
        'custom_route_name': 'CustomRouteName',
        'dst_ip': 'DstIP',
        'proxy_cipher': 'ProxyCipher',
        'proxy_port': 'ProxyPort',
        'proxy_protocol': 'ProxyProtocol',
        'update_at': 'UpdateAt',
        'zone': 'Zone'
    }

    def __init__(self, create_at=None, custom_route_id=None, custom_route_name=None, dst_ip=None, proxy_cipher=None, proxy_port=None, proxy_protocol=None, update_at=None, zone=None, _configuration=None):  # noqa: E501
        """RowForListCustomRouteOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_at = None
        self._custom_route_id = None
        self._custom_route_name = None
        self._dst_ip = None
        self._proxy_cipher = None
        self._proxy_port = None
        self._proxy_protocol = None
        self._update_at = None
        self._zone = None
        self.discriminator = None

        if create_at is not None:
            self.create_at = create_at
        if custom_route_id is not None:
            self.custom_route_id = custom_route_id
        if custom_route_name is not None:
            self.custom_route_name = custom_route_name
        if dst_ip is not None:
            self.dst_ip = dst_ip
        if proxy_cipher is not None:
            self.proxy_cipher = proxy_cipher
        if proxy_port is not None:
            self.proxy_port = proxy_port
        if proxy_protocol is not None:
            self.proxy_protocol = proxy_protocol
        if update_at is not None:
            self.update_at = update_at
        if zone is not None:
            self.zone = zone

    @property
    def create_at(self):
        """Gets the create_at of this RowForListCustomRouteOutput.  # noqa: E501


        :return: The create_at of this RowForListCustomRouteOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_at

    @create_at.setter
    def create_at(self, create_at):
        """Sets the create_at of this RowForListCustomRouteOutput.


        :param create_at: The create_at of this RowForListCustomRouteOutput.  # noqa: E501
        :type: int
        """

        self._create_at = create_at

    @property
    def custom_route_id(self):
        """Gets the custom_route_id of this RowForListCustomRouteOutput.  # noqa: E501


        :return: The custom_route_id of this RowForListCustomRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._custom_route_id

    @custom_route_id.setter
    def custom_route_id(self, custom_route_id):
        """Sets the custom_route_id of this RowForListCustomRouteOutput.


        :param custom_route_id: The custom_route_id of this RowForListCustomRouteOutput.  # noqa: E501
        :type: str
        """

        self._custom_route_id = custom_route_id

    @property
    def custom_route_name(self):
        """Gets the custom_route_name of this RowForListCustomRouteOutput.  # noqa: E501


        :return: The custom_route_name of this RowForListCustomRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._custom_route_name

    @custom_route_name.setter
    def custom_route_name(self, custom_route_name):
        """Sets the custom_route_name of this RowForListCustomRouteOutput.


        :param custom_route_name: The custom_route_name of this RowForListCustomRouteOutput.  # noqa: E501
        :type: str
        """

        self._custom_route_name = custom_route_name

    @property
    def dst_ip(self):
        """Gets the dst_ip of this RowForListCustomRouteOutput.  # noqa: E501


        :return: The dst_ip of this RowForListCustomRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._dst_ip

    @dst_ip.setter
    def dst_ip(self, dst_ip):
        """Sets the dst_ip of this RowForListCustomRouteOutput.


        :param dst_ip: The dst_ip of this RowForListCustomRouteOutput.  # noqa: E501
        :type: str
        """

        self._dst_ip = dst_ip

    @property
    def proxy_cipher(self):
        """Gets the proxy_cipher of this RowForListCustomRouteOutput.  # noqa: E501


        :return: The proxy_cipher of this RowForListCustomRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._proxy_cipher

    @proxy_cipher.setter
    def proxy_cipher(self, proxy_cipher):
        """Sets the proxy_cipher of this RowForListCustomRouteOutput.


        :param proxy_cipher: The proxy_cipher of this RowForListCustomRouteOutput.  # noqa: E501
        :type: str
        """

        self._proxy_cipher = proxy_cipher

    @property
    def proxy_port(self):
        """Gets the proxy_port of this RowForListCustomRouteOutput.  # noqa: E501


        :return: The proxy_port of this RowForListCustomRouteOutput.  # noqa: E501
        :rtype: int
        """
        return self._proxy_port

    @proxy_port.setter
    def proxy_port(self, proxy_port):
        """Sets the proxy_port of this RowForListCustomRouteOutput.


        :param proxy_port: The proxy_port of this RowForListCustomRouteOutput.  # noqa: E501
        :type: int
        """

        self._proxy_port = proxy_port

    @property
    def proxy_protocol(self):
        """Gets the proxy_protocol of this RowForListCustomRouteOutput.  # noqa: E501


        :return: The proxy_protocol of this RowForListCustomRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._proxy_protocol

    @proxy_protocol.setter
    def proxy_protocol(self, proxy_protocol):
        """Sets the proxy_protocol of this RowForListCustomRouteOutput.


        :param proxy_protocol: The proxy_protocol of this RowForListCustomRouteOutput.  # noqa: E501
        :type: str
        """

        self._proxy_protocol = proxy_protocol

    @property
    def update_at(self):
        """Gets the update_at of this RowForListCustomRouteOutput.  # noqa: E501


        :return: The update_at of this RowForListCustomRouteOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_at

    @update_at.setter
    def update_at(self, update_at):
        """Sets the update_at of this RowForListCustomRouteOutput.


        :param update_at: The update_at of this RowForListCustomRouteOutput.  # noqa: E501
        :type: int
        """

        self._update_at = update_at

    @property
    def zone(self):
        """Gets the zone of this RowForListCustomRouteOutput.  # noqa: E501


        :return: The zone of this RowForListCustomRouteOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone

    @zone.setter
    def zone(self, zone):
        """Sets the zone of this RowForListCustomRouteOutput.


        :param zone: The zone of this RowForListCustomRouteOutput.  # noqa: E501
        :type: str
        """

        self._zone = zone

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RowForListCustomRouteOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RowForListCustomRouteOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RowForListCustomRouteOutput):
            return True

        return self.to_dict() != other.to_dict()
