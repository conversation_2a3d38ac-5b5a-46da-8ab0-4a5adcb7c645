# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DomainLogDetailForDescribeCdnAccessLogOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'int',
        'log_name': 'str',
        'log_path': 'str',
        'log_size': 'int',
        'start_time': 'int'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'log_name': 'LogName',
        'log_path': 'LogPath',
        'log_size': 'LogSize',
        'start_time': 'StartTime'
    }

    def __init__(self, end_time=None, log_name=None, log_path=None, log_size=None, start_time=None, _configuration=None):  # noqa: E501
        """DomainLogDetailForDescribeCdnAccessLogOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._log_name = None
        self._log_path = None
        self._log_size = None
        self._start_time = None
        self.discriminator = None

        if end_time is not None:
            self.end_time = end_time
        if log_name is not None:
            self.log_name = log_name
        if log_path is not None:
            self.log_path = log_path
        if log_size is not None:
            self.log_size = log_size
        if start_time is not None:
            self.start_time = start_time

    @property
    def end_time(self):
        """Gets the end_time of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The end_time of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DomainLogDetailForDescribeCdnAccessLogOutput.


        :param end_time: The end_time of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def log_name(self):
        """Gets the log_name of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The log_name of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_name

    @log_name.setter
    def log_name(self, log_name):
        """Sets the log_name of this DomainLogDetailForDescribeCdnAccessLogOutput.


        :param log_name: The log_name of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: str
        """

        self._log_name = log_name

    @property
    def log_path(self):
        """Gets the log_path of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The log_path of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_path

    @log_path.setter
    def log_path(self, log_path):
        """Sets the log_path of this DomainLogDetailForDescribeCdnAccessLogOutput.


        :param log_path: The log_path of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: str
        """

        self._log_path = log_path

    @property
    def log_size(self):
        """Gets the log_size of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The log_size of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: int
        """
        return self._log_size

    @log_size.setter
    def log_size(self, log_size):
        """Sets the log_size of this DomainLogDetailForDescribeCdnAccessLogOutput.


        :param log_size: The log_size of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: int
        """

        self._log_size = log_size

    @property
    def start_time(self):
        """Gets the start_time of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501


        :return: The start_time of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DomainLogDetailForDescribeCdnAccessLogOutput.


        :param start_time: The start_time of this DomainLogDetailForDescribeCdnAccessLogOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DomainLogDetailForDescribeCdnAccessLogOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DomainLogDetailForDescribeCdnAccessLogOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DomainLogDetailForDescribeCdnAccessLogOutput):
            return True

        return self.to_dict() != other.to_dict()
