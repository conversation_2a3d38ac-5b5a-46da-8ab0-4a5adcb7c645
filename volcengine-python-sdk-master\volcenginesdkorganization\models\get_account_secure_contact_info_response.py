# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAccountSecureContactInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'email': 'str',
        'email_verified': 'int',
        'new_email': 'str',
        'new_phone': 'str',
        'phone': 'str',
        'phone_verified': 'int'
    }

    attribute_map = {
        'email': 'Email',
        'email_verified': 'EmailVerified',
        'new_email': 'NewEmail',
        'new_phone': 'NewPhone',
        'phone': 'Phone',
        'phone_verified': 'PhoneVerified'
    }

    def __init__(self, email=None, email_verified=None, new_email=None, new_phone=None, phone=None, phone_verified=None, _configuration=None):  # noqa: E501
        """GetAccountSecureContactInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._email = None
        self._email_verified = None
        self._new_email = None
        self._new_phone = None
        self._phone = None
        self._phone_verified = None
        self.discriminator = None

        if email is not None:
            self.email = email
        if email_verified is not None:
            self.email_verified = email_verified
        if new_email is not None:
            self.new_email = new_email
        if new_phone is not None:
            self.new_phone = new_phone
        if phone is not None:
            self.phone = phone
        if phone_verified is not None:
            self.phone_verified = phone_verified

    @property
    def email(self):
        """Gets the email of this GetAccountSecureContactInfoResponse.  # noqa: E501


        :return: The email of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this GetAccountSecureContactInfoResponse.


        :param email: The email of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def email_verified(self):
        """Gets the email_verified of this GetAccountSecureContactInfoResponse.  # noqa: E501


        :return: The email_verified of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._email_verified

    @email_verified.setter
    def email_verified(self, email_verified):
        """Sets the email_verified of this GetAccountSecureContactInfoResponse.


        :param email_verified: The email_verified of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :type: int
        """

        self._email_verified = email_verified

    @property
    def new_email(self):
        """Gets the new_email of this GetAccountSecureContactInfoResponse.  # noqa: E501


        :return: The new_email of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._new_email

    @new_email.setter
    def new_email(self, new_email):
        """Sets the new_email of this GetAccountSecureContactInfoResponse.


        :param new_email: The new_email of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :type: str
        """

        self._new_email = new_email

    @property
    def new_phone(self):
        """Gets the new_phone of this GetAccountSecureContactInfoResponse.  # noqa: E501


        :return: The new_phone of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._new_phone

    @new_phone.setter
    def new_phone(self, new_phone):
        """Sets the new_phone of this GetAccountSecureContactInfoResponse.


        :param new_phone: The new_phone of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :type: str
        """

        self._new_phone = new_phone

    @property
    def phone(self):
        """Gets the phone of this GetAccountSecureContactInfoResponse.  # noqa: E501


        :return: The phone of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :rtype: str
        """
        return self._phone

    @phone.setter
    def phone(self, phone):
        """Sets the phone of this GetAccountSecureContactInfoResponse.


        :param phone: The phone of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :type: str
        """

        self._phone = phone

    @property
    def phone_verified(self):
        """Gets the phone_verified of this GetAccountSecureContactInfoResponse.  # noqa: E501


        :return: The phone_verified of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :rtype: int
        """
        return self._phone_verified

    @phone_verified.setter
    def phone_verified(self, phone_verified):
        """Sets the phone_verified of this GetAccountSecureContactInfoResponse.


        :param phone_verified: The phone_verified of this GetAccountSecureContactInfoResponse.  # noqa: E501
        :type: int
        """

        self._phone_verified = phone_verified

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAccountSecureContactInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAccountSecureContactInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAccountSecureContactInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
