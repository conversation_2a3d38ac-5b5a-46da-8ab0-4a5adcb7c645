# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EndpointConfigurationForListEndpointGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'endpoint': 'str',
        'private_instance_id': 'str',
        'type': 'str',
        'weight': 'int'
    }

    attribute_map = {
        'endpoint': 'Endpoint',
        'private_instance_id': 'PrivateInstanceID',
        'type': 'Type',
        'weight': 'Weight'
    }

    def __init__(self, endpoint=None, private_instance_id=None, type=None, weight=None, _configuration=None):  # noqa: E501
        """EndpointConfigurationForListEndpointGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._endpoint = None
        self._private_instance_id = None
        self._type = None
        self._weight = None
        self.discriminator = None

        if endpoint is not None:
            self.endpoint = endpoint
        if private_instance_id is not None:
            self.private_instance_id = private_instance_id
        if type is not None:
            self.type = type
        if weight is not None:
            self.weight = weight

    @property
    def endpoint(self):
        """Gets the endpoint of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501


        :return: The endpoint of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint

    @endpoint.setter
    def endpoint(self, endpoint):
        """Sets the endpoint of this EndpointConfigurationForListEndpointGroupsOutput.


        :param endpoint: The endpoint of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501
        :type: str
        """

        self._endpoint = endpoint

    @property
    def private_instance_id(self):
        """Gets the private_instance_id of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501


        :return: The private_instance_id of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_instance_id

    @private_instance_id.setter
    def private_instance_id(self, private_instance_id):
        """Sets the private_instance_id of this EndpointConfigurationForListEndpointGroupsOutput.


        :param private_instance_id: The private_instance_id of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501
        :type: str
        """

        self._private_instance_id = private_instance_id

    @property
    def type(self):
        """Gets the type of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501


        :return: The type of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this EndpointConfigurationForListEndpointGroupsOutput.


        :param type: The type of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def weight(self):
        """Gets the weight of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501


        :return: The weight of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._weight

    @weight.setter
    def weight(self, weight):
        """Sets the weight of this EndpointConfigurationForListEndpointGroupsOutput.


        :param weight: The weight of this EndpointConfigurationForListEndpointGroupsOutput.  # noqa: E501
        :type: int
        """

        self._weight = weight

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EndpointConfigurationForListEndpointGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EndpointConfigurationForListEndpointGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EndpointConfigurationForListEndpointGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
