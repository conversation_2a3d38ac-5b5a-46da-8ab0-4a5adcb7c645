# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResultForDescribeStatisticsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'statistics_results': 'list[StatisticsResultForDescribeStatisticsOutput]',
        'time_stamp': 'str',
        'time_stamp_int': 'int'
    }

    attribute_map = {
        'statistics_results': 'StatisticsResults',
        'time_stamp': 'TimeStamp',
        'time_stamp_int': 'TimeStampInt'
    }

    def __init__(self, statistics_results=None, time_stamp=None, time_stamp_int=None, _configuration=None):  # noqa: E501
        """ResultForDescribeStatisticsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._statistics_results = None
        self._time_stamp = None
        self._time_stamp_int = None
        self.discriminator = None

        if statistics_results is not None:
            self.statistics_results = statistics_results
        if time_stamp is not None:
            self.time_stamp = time_stamp
        if time_stamp_int is not None:
            self.time_stamp_int = time_stamp_int

    @property
    def statistics_results(self):
        """Gets the statistics_results of this ResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The statistics_results of this ResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: list[StatisticsResultForDescribeStatisticsOutput]
        """
        return self._statistics_results

    @statistics_results.setter
    def statistics_results(self, statistics_results):
        """Sets the statistics_results of this ResultForDescribeStatisticsOutput.


        :param statistics_results: The statistics_results of this ResultForDescribeStatisticsOutput.  # noqa: E501
        :type: list[StatisticsResultForDescribeStatisticsOutput]
        """

        self._statistics_results = statistics_results

    @property
    def time_stamp(self):
        """Gets the time_stamp of this ResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The time_stamp of this ResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._time_stamp

    @time_stamp.setter
    def time_stamp(self, time_stamp):
        """Sets the time_stamp of this ResultForDescribeStatisticsOutput.


        :param time_stamp: The time_stamp of this ResultForDescribeStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._time_stamp = time_stamp

    @property
    def time_stamp_int(self):
        """Gets the time_stamp_int of this ResultForDescribeStatisticsOutput.  # noqa: E501


        :return: The time_stamp_int of this ResultForDescribeStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._time_stamp_int

    @time_stamp_int.setter
    def time_stamp_int(self, time_stamp_int):
        """Sets the time_stamp_int of this ResultForDescribeStatisticsOutput.


        :param time_stamp_int: The time_stamp_int of this ResultForDescribeStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._time_stamp_int = time_stamp_int

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResultForDescribeStatisticsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResultForDescribeStatisticsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResultForDescribeStatisticsOutput):
            return True

        return self.to_dict() != other.to_dict()
