# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TlsInfoForDescribeDataFlowsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable': 'bool',
        'project_id': 'str',
        'project_name': 'str',
        'topic_embedded_url': 'str',
        'topic_id': 'str',
        'topic_name': 'str'
    }

    attribute_map = {
        'enable': 'Enable',
        'project_id': 'ProjectId',
        'project_name': 'ProjectName',
        'topic_embedded_url': 'TopicEmbeddedUrl',
        'topic_id': 'TopicId',
        'topic_name': 'TopicName'
    }

    def __init__(self, enable=None, project_id=None, project_name=None, topic_embedded_url=None, topic_id=None, topic_name=None, _configuration=None):  # noqa: E501
        """TlsInfoForDescribeDataFlowsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable = None
        self._project_id = None
        self._project_name = None
        self._topic_embedded_url = None
        self._topic_id = None
        self._topic_name = None
        self.discriminator = None

        if enable is not None:
            self.enable = enable
        if project_id is not None:
            self.project_id = project_id
        if project_name is not None:
            self.project_name = project_name
        if topic_embedded_url is not None:
            self.topic_embedded_url = topic_embedded_url
        if topic_id is not None:
            self.topic_id = topic_id
        if topic_name is not None:
            self.topic_name = topic_name

    @property
    def enable(self):
        """Gets the enable of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501


        :return: The enable of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this TlsInfoForDescribeDataFlowsOutput.


        :param enable: The enable of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def project_id(self):
        """Gets the project_id of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501


        :return: The project_id of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this TlsInfoForDescribeDataFlowsOutput.


        :param project_id: The project_id of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def project_name(self):
        """Gets the project_name of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501


        :return: The project_name of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this TlsInfoForDescribeDataFlowsOutput.


        :param project_name: The project_name of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def topic_embedded_url(self):
        """Gets the topic_embedded_url of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501


        :return: The topic_embedded_url of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._topic_embedded_url

    @topic_embedded_url.setter
    def topic_embedded_url(self, topic_embedded_url):
        """Sets the topic_embedded_url of this TlsInfoForDescribeDataFlowsOutput.


        :param topic_embedded_url: The topic_embedded_url of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :type: str
        """

        self._topic_embedded_url = topic_embedded_url

    @property
    def topic_id(self):
        """Gets the topic_id of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501


        :return: The topic_id of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._topic_id

    @topic_id.setter
    def topic_id(self, topic_id):
        """Sets the topic_id of this TlsInfoForDescribeDataFlowsOutput.


        :param topic_id: The topic_id of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :type: str
        """

        self._topic_id = topic_id

    @property
    def topic_name(self):
        """Gets the topic_name of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501


        :return: The topic_name of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._topic_name

    @topic_name.setter
    def topic_name(self, topic_name):
        """Sets the topic_name of this TlsInfoForDescribeDataFlowsOutput.


        :param topic_name: The topic_name of this TlsInfoForDescribeDataFlowsOutput.  # noqa: E501
        :type: str
        """

        self._topic_name = topic_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TlsInfoForDescribeDataFlowsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TlsInfoForDescribeDataFlowsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TlsInfoForDescribeDataFlowsOutput):
            return True

        return self.to_dict() != other.to_dict()
