# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetNeighboringAlarmResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'next': 'NextForGetNeighboringAlarmOutput',
        'prev': 'PrevForGetNeighboringAlarmOutput'
    }

    attribute_map = {
        'next': 'Next',
        'prev': 'Prev'
    }

    def __init__(self, next=None, prev=None, _configuration=None):  # noqa: E501
        """GetNeighboringAlarmResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._next = None
        self._prev = None
        self.discriminator = None

        if next is not None:
            self.next = next
        if prev is not None:
            self.prev = prev

    @property
    def next(self):
        """Gets the next of this GetNeighboringAlarmResponse.  # noqa: E501


        :return: The next of this GetNeighboringAlarmResponse.  # noqa: E501
        :rtype: NextForGetNeighboringAlarmOutput
        """
        return self._next

    @next.setter
    def next(self, next):
        """Sets the next of this GetNeighboringAlarmResponse.


        :param next: The next of this GetNeighboringAlarmResponse.  # noqa: E501
        :type: NextForGetNeighboringAlarmOutput
        """

        self._next = next

    @property
    def prev(self):
        """Gets the prev of this GetNeighboringAlarmResponse.  # noqa: E501


        :return: The prev of this GetNeighboringAlarmResponse.  # noqa: E501
        :rtype: PrevForGetNeighboringAlarmOutput
        """
        return self._prev

    @prev.setter
    def prev(self, prev):
        """Sets the prev of this GetNeighboringAlarmResponse.


        :param prev: The prev of this GetNeighboringAlarmResponse.  # noqa: E501
        :type: PrevForGetNeighboringAlarmOutput
        """

        self._prev = prev

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetNeighboringAlarmResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetNeighboringAlarmResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetNeighboringAlarmResponse):
            return True

        return self.to_dict() != other.to_dict()
