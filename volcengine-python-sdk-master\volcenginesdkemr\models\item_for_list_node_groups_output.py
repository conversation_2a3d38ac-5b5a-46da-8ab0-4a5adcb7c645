# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListNodeGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_type': 'str',
        'create_time': 'int',
        'data_disks': 'list[DataDiskForListNodeGroupsOutput]',
        'ecs_instance_types': 'list[str]',
        'layout_component_names': 'list[str]',
        'node_group_id': 'str',
        'node_group_name': 'str',
        'node_group_state': 'str',
        'node_group_type': 'str',
        'subnet_ids': 'list[str]',
        'system_disk': 'SystemDiskForListNodeGroupsOutput',
        'terminate_time': 'str',
        'with_public_ip': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'charge_type': 'ChargeType',
        'create_time': 'CreateTime',
        'data_disks': 'DataDisks',
        'ecs_instance_types': 'EcsInstanceTypes',
        'layout_component_names': 'LayoutComponentNames',
        'node_group_id': 'NodeGroupId',
        'node_group_name': 'NodeGroupName',
        'node_group_state': 'NodeGroupState',
        'node_group_type': 'NodeGroupType',
        'subnet_ids': 'SubnetIds',
        'system_disk': 'SystemDisk',
        'terminate_time': 'TerminateTime',
        'with_public_ip': 'WithPublicIp',
        'zone_id': 'ZoneId'
    }

    def __init__(self, charge_type=None, create_time=None, data_disks=None, ecs_instance_types=None, layout_component_names=None, node_group_id=None, node_group_name=None, node_group_state=None, node_group_type=None, subnet_ids=None, system_disk=None, terminate_time=None, with_public_ip=None, zone_id=None, _configuration=None):  # noqa: E501
        """ItemForListNodeGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_type = None
        self._create_time = None
        self._data_disks = None
        self._ecs_instance_types = None
        self._layout_component_names = None
        self._node_group_id = None
        self._node_group_name = None
        self._node_group_state = None
        self._node_group_type = None
        self._subnet_ids = None
        self._system_disk = None
        self._terminate_time = None
        self._with_public_ip = None
        self._zone_id = None
        self.discriminator = None

        if charge_type is not None:
            self.charge_type = charge_type
        if create_time is not None:
            self.create_time = create_time
        if data_disks is not None:
            self.data_disks = data_disks
        if ecs_instance_types is not None:
            self.ecs_instance_types = ecs_instance_types
        if layout_component_names is not None:
            self.layout_component_names = layout_component_names
        if node_group_id is not None:
            self.node_group_id = node_group_id
        if node_group_name is not None:
            self.node_group_name = node_group_name
        if node_group_state is not None:
            self.node_group_state = node_group_state
        if node_group_type is not None:
            self.node_group_type = node_group_type
        if subnet_ids is not None:
            self.subnet_ids = subnet_ids
        if system_disk is not None:
            self.system_disk = system_disk
        if terminate_time is not None:
            self.terminate_time = terminate_time
        if with_public_ip is not None:
            self.with_public_ip = with_public_ip
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def charge_type(self):
        """Gets the charge_type of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The charge_type of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ItemForListNodeGroupsOutput.


        :param charge_type: The charge_type of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The create_time of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListNodeGroupsOutput.


        :param create_time: The create_time of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def data_disks(self):
        """Gets the data_disks of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The data_disks of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: list[DataDiskForListNodeGroupsOutput]
        """
        return self._data_disks

    @data_disks.setter
    def data_disks(self, data_disks):
        """Sets the data_disks of this ItemForListNodeGroupsOutput.


        :param data_disks: The data_disks of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: list[DataDiskForListNodeGroupsOutput]
        """

        self._data_disks = data_disks

    @property
    def ecs_instance_types(self):
        """Gets the ecs_instance_types of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The ecs_instance_types of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ecs_instance_types

    @ecs_instance_types.setter
    def ecs_instance_types(self, ecs_instance_types):
        """Sets the ecs_instance_types of this ItemForListNodeGroupsOutput.


        :param ecs_instance_types: The ecs_instance_types of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: list[str]
        """

        self._ecs_instance_types = ecs_instance_types

    @property
    def layout_component_names(self):
        """Gets the layout_component_names of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The layout_component_names of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._layout_component_names

    @layout_component_names.setter
    def layout_component_names(self, layout_component_names):
        """Sets the layout_component_names of this ItemForListNodeGroupsOutput.


        :param layout_component_names: The layout_component_names of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: list[str]
        """

        self._layout_component_names = layout_component_names

    @property
    def node_group_id(self):
        """Gets the node_group_id of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The node_group_id of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_group_id

    @node_group_id.setter
    def node_group_id(self, node_group_id):
        """Sets the node_group_id of this ItemForListNodeGroupsOutput.


        :param node_group_id: The node_group_id of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._node_group_id = node_group_id

    @property
    def node_group_name(self):
        """Gets the node_group_name of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The node_group_name of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_group_name

    @node_group_name.setter
    def node_group_name(self, node_group_name):
        """Sets the node_group_name of this ItemForListNodeGroupsOutput.


        :param node_group_name: The node_group_name of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._node_group_name = node_group_name

    @property
    def node_group_state(self):
        """Gets the node_group_state of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The node_group_state of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_group_state

    @node_group_state.setter
    def node_group_state(self, node_group_state):
        """Sets the node_group_state of this ItemForListNodeGroupsOutput.


        :param node_group_state: The node_group_state of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._node_group_state = node_group_state

    @property
    def node_group_type(self):
        """Gets the node_group_type of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The node_group_type of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_group_type

    @node_group_type.setter
    def node_group_type(self, node_group_type):
        """Sets the node_group_type of this ItemForListNodeGroupsOutput.


        :param node_group_type: The node_group_type of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._node_group_type = node_group_type

    @property
    def subnet_ids(self):
        """Gets the subnet_ids of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The subnet_ids of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._subnet_ids

    @subnet_ids.setter
    def subnet_ids(self, subnet_ids):
        """Sets the subnet_ids of this ItemForListNodeGroupsOutput.


        :param subnet_ids: The subnet_ids of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: list[str]
        """

        self._subnet_ids = subnet_ids

    @property
    def system_disk(self):
        """Gets the system_disk of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The system_disk of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: SystemDiskForListNodeGroupsOutput
        """
        return self._system_disk

    @system_disk.setter
    def system_disk(self, system_disk):
        """Sets the system_disk of this ItemForListNodeGroupsOutput.


        :param system_disk: The system_disk of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: SystemDiskForListNodeGroupsOutput
        """

        self._system_disk = system_disk

    @property
    def terminate_time(self):
        """Gets the terminate_time of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The terminate_time of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._terminate_time

    @terminate_time.setter
    def terminate_time(self, terminate_time):
        """Sets the terminate_time of this ItemForListNodeGroupsOutput.


        :param terminate_time: The terminate_time of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._terminate_time = terminate_time

    @property
    def with_public_ip(self):
        """Gets the with_public_ip of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The with_public_ip of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._with_public_ip

    @with_public_ip.setter
    def with_public_ip(self, with_public_ip):
        """Sets the with_public_ip of this ItemForListNodeGroupsOutput.


        :param with_public_ip: The with_public_ip of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._with_public_ip = with_public_ip

    @property
    def zone_id(self):
        """Gets the zone_id of this ItemForListNodeGroupsOutput.  # noqa: E501


        :return: The zone_id of this ItemForListNodeGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ItemForListNodeGroupsOutput.


        :param zone_id: The zone_id of this ItemForListNodeGroupsOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListNodeGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListNodeGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListNodeGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
