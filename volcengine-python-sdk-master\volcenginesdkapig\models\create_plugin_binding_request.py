# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreatePluginBindingRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'enable': 'bool',
        'plugin_config': 'str',
        'plugin_name': 'str',
        'scope': 'str',
        'target': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'enable': 'Enable',
        'plugin_config': 'PluginConfig',
        'plugin_name': 'PluginName',
        'scope': 'Scope',
        'target': 'Target'
    }

    def __init__(self, description=None, enable=None, plugin_config=None, plugin_name=None, scope=None, target=None, _configuration=None):  # noqa: E501
        """CreatePluginBindingRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._enable = None
        self._plugin_config = None
        self._plugin_name = None
        self._scope = None
        self._target = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.enable = enable
        self.plugin_config = plugin_config
        self.plugin_name = plugin_name
        self.scope = scope
        self.target = target

    @property
    def description(self):
        """Gets the description of this CreatePluginBindingRequest.  # noqa: E501


        :return: The description of this CreatePluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreatePluginBindingRequest.


        :param description: The description of this CreatePluginBindingRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enable(self):
        """Gets the enable of this CreatePluginBindingRequest.  # noqa: E501


        :return: The enable of this CreatePluginBindingRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this CreatePluginBindingRequest.


        :param enable: The enable of this CreatePluginBindingRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and enable is None:
            raise ValueError("Invalid value for `enable`, must not be `None`")  # noqa: E501

        self._enable = enable

    @property
    def plugin_config(self):
        """Gets the plugin_config of this CreatePluginBindingRequest.  # noqa: E501


        :return: The plugin_config of this CreatePluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._plugin_config

    @plugin_config.setter
    def plugin_config(self, plugin_config):
        """Sets the plugin_config of this CreatePluginBindingRequest.


        :param plugin_config: The plugin_config of this CreatePluginBindingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and plugin_config is None:
            raise ValueError("Invalid value for `plugin_config`, must not be `None`")  # noqa: E501

        self._plugin_config = plugin_config

    @property
    def plugin_name(self):
        """Gets the plugin_name of this CreatePluginBindingRequest.  # noqa: E501


        :return: The plugin_name of this CreatePluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._plugin_name

    @plugin_name.setter
    def plugin_name(self, plugin_name):
        """Sets the plugin_name of this CreatePluginBindingRequest.


        :param plugin_name: The plugin_name of this CreatePluginBindingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and plugin_name is None:
            raise ValueError("Invalid value for `plugin_name`, must not be `None`")  # noqa: E501

        self._plugin_name = plugin_name

    @property
    def scope(self):
        """Gets the scope of this CreatePluginBindingRequest.  # noqa: E501


        :return: The scope of this CreatePluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._scope

    @scope.setter
    def scope(self, scope):
        """Sets the scope of this CreatePluginBindingRequest.


        :param scope: The scope of this CreatePluginBindingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and scope is None:
            raise ValueError("Invalid value for `scope`, must not be `None`")  # noqa: E501

        self._scope = scope

    @property
    def target(self):
        """Gets the target of this CreatePluginBindingRequest.  # noqa: E501


        :return: The target of this CreatePluginBindingRequest.  # noqa: E501
        :rtype: str
        """
        return self._target

    @target.setter
    def target(self, target):
        """Sets the target of this CreatePluginBindingRequest.


        :param target: The target of this CreatePluginBindingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and target is None:
            raise ValueError("Invalid value for `target`, must not be `None`")  # noqa: E501

        self._target = target

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreatePluginBindingRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreatePluginBindingRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreatePluginBindingRequest):
            return True

        return self.to_dict() != other.to_dict()
