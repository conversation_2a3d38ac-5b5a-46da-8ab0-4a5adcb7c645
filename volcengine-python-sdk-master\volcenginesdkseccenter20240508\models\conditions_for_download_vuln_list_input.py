# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConditionsForDownloadVulnListInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cloud_providers': 'list[str]',
        'cve_id': 'str',
        'if_high_availability': 'bool',
        'leaf_group_ids': 'list[str]',
        'level': 'list[str]',
        'tag': 'list[str]',
        'top_group_id': 'str',
        'vuln_name': 'str'
    }

    attribute_map = {
        'cloud_providers': 'CloudProviders',
        'cve_id': 'CveID',
        'if_high_availability': 'IfHighAvailability',
        'leaf_group_ids': 'LeafGroupIDs',
        'level': 'Level',
        'tag': 'Tag',
        'top_group_id': 'TopGroupID',
        'vuln_name': 'VulnName'
    }

    def __init__(self, cloud_providers=None, cve_id=None, if_high_availability=None, leaf_group_ids=None, level=None, tag=None, top_group_id=None, vuln_name=None, _configuration=None):  # noqa: E501
        """ConditionsForDownloadVulnListInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cloud_providers = None
        self._cve_id = None
        self._if_high_availability = None
        self._leaf_group_ids = None
        self._level = None
        self._tag = None
        self._top_group_id = None
        self._vuln_name = None
        self.discriminator = None

        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if cve_id is not None:
            self.cve_id = cve_id
        if if_high_availability is not None:
            self.if_high_availability = if_high_availability
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if level is not None:
            self.level = level
        if tag is not None:
            self.tag = tag
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if vuln_name is not None:
            self.vuln_name = vuln_name

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ConditionsForDownloadVulnListInput.  # noqa: E501


        :return: The cloud_providers of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ConditionsForDownloadVulnListInput.


        :param cloud_providers: The cloud_providers of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def cve_id(self):
        """Gets the cve_id of this ConditionsForDownloadVulnListInput.  # noqa: E501


        :return: The cve_id of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :rtype: str
        """
        return self._cve_id

    @cve_id.setter
    def cve_id(self, cve_id):
        """Sets the cve_id of this ConditionsForDownloadVulnListInput.


        :param cve_id: The cve_id of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :type: str
        """

        self._cve_id = cve_id

    @property
    def if_high_availability(self):
        """Gets the if_high_availability of this ConditionsForDownloadVulnListInput.  # noqa: E501


        :return: The if_high_availability of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :rtype: bool
        """
        return self._if_high_availability

    @if_high_availability.setter
    def if_high_availability(self, if_high_availability):
        """Sets the if_high_availability of this ConditionsForDownloadVulnListInput.


        :param if_high_availability: The if_high_availability of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :type: bool
        """

        self._if_high_availability = if_high_availability

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ConditionsForDownloadVulnListInput.  # noqa: E501


        :return: The leaf_group_ids of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ConditionsForDownloadVulnListInput.


        :param leaf_group_ids: The leaf_group_ids of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def level(self):
        """Gets the level of this ConditionsForDownloadVulnListInput.  # noqa: E501


        :return: The level of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this ConditionsForDownloadVulnListInput.


        :param level: The level of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :type: list[str]
        """

        self._level = level

    @property
    def tag(self):
        """Gets the tag of this ConditionsForDownloadVulnListInput.  # noqa: E501


        :return: The tag of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this ConditionsForDownloadVulnListInput.


        :param tag: The tag of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :type: list[str]
        """

        self._tag = tag

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ConditionsForDownloadVulnListInput.  # noqa: E501


        :return: The top_group_id of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ConditionsForDownloadVulnListInput.


        :param top_group_id: The top_group_id of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def vuln_name(self):
        """Gets the vuln_name of this ConditionsForDownloadVulnListInput.  # noqa: E501


        :return: The vuln_name of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_name

    @vuln_name.setter
    def vuln_name(self, vuln_name):
        """Sets the vuln_name of this ConditionsForDownloadVulnListInput.


        :param vuln_name: The vuln_name of this ConditionsForDownloadVulnListInput.  # noqa: E501
        :type: str
        """

        self._vuln_name = vuln_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConditionsForDownloadVulnListInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConditionsForDownloadVulnListInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConditionsForDownloadVulnListInput):
            return True

        return self.to_dict() != other.to_dict()
