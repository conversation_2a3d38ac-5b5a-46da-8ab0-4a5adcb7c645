# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StorageConfigForCreateJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'credential': 'ConvertCredentialForCreateJobInput',
        'sidecar_memory_ratio': 'float',
        'storages': 'list[StorageForCreateJobInput]'
    }

    attribute_map = {
        'credential': 'Credential',
        'sidecar_memory_ratio': 'SidecarMemoryRatio',
        'storages': 'Storages'
    }

    def __init__(self, credential=None, sidecar_memory_ratio=None, storages=None, _configuration=None):  # noqa: E501
        """StorageConfigForCreateJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._credential = None
        self._sidecar_memory_ratio = None
        self._storages = None
        self.discriminator = None

        if credential is not None:
            self.credential = credential
        if sidecar_memory_ratio is not None:
            self.sidecar_memory_ratio = sidecar_memory_ratio
        if storages is not None:
            self.storages = storages

    @property
    def credential(self):
        """Gets the credential of this StorageConfigForCreateJobInput.  # noqa: E501


        :return: The credential of this StorageConfigForCreateJobInput.  # noqa: E501
        :rtype: ConvertCredentialForCreateJobInput
        """
        return self._credential

    @credential.setter
    def credential(self, credential):
        """Sets the credential of this StorageConfigForCreateJobInput.


        :param credential: The credential of this StorageConfigForCreateJobInput.  # noqa: E501
        :type: ConvertCredentialForCreateJobInput
        """

        self._credential = credential

    @property
    def sidecar_memory_ratio(self):
        """Gets the sidecar_memory_ratio of this StorageConfigForCreateJobInput.  # noqa: E501


        :return: The sidecar_memory_ratio of this StorageConfigForCreateJobInput.  # noqa: E501
        :rtype: float
        """
        return self._sidecar_memory_ratio

    @sidecar_memory_ratio.setter
    def sidecar_memory_ratio(self, sidecar_memory_ratio):
        """Sets the sidecar_memory_ratio of this StorageConfigForCreateJobInput.


        :param sidecar_memory_ratio: The sidecar_memory_ratio of this StorageConfigForCreateJobInput.  # noqa: E501
        :type: float
        """

        self._sidecar_memory_ratio = sidecar_memory_ratio

    @property
    def storages(self):
        """Gets the storages of this StorageConfigForCreateJobInput.  # noqa: E501


        :return: The storages of this StorageConfigForCreateJobInput.  # noqa: E501
        :rtype: list[StorageForCreateJobInput]
        """
        return self._storages

    @storages.setter
    def storages(self, storages):
        """Sets the storages of this StorageConfigForCreateJobInput.


        :param storages: The storages of this StorageConfigForCreateJobInput.  # noqa: E501
        :type: list[StorageForCreateJobInput]
        """

        self._storages = storages

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StorageConfigForCreateJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StorageConfigForCreateJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StorageConfigForCreateJobInput):
            return True

        return self.to_dict() != other.to_dict()
