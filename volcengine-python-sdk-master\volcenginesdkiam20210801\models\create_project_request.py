# coding: utf-8

"""
    iam20210801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateProjectRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'display_name': 'str',
        'parent_project_name': 'str',
        'project_name': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'display_name': 'DisplayName',
        'parent_project_name': 'ParentProjectName',
        'project_name': 'ProjectName'
    }

    def __init__(self, description=None, display_name=None, parent_project_name=None, project_name=None, _configuration=None):  # noqa: E501
        """CreateProjectRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._display_name = None
        self._parent_project_name = None
        self._project_name = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if display_name is not None:
            self.display_name = display_name
        if parent_project_name is not None:
            self.parent_project_name = parent_project_name
        self.project_name = project_name

    @property
    def description(self):
        """Gets the description of this CreateProjectRequest.  # noqa: E501


        :return: The description of this CreateProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateProjectRequest.


        :param description: The description of this CreateProjectRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def display_name(self):
        """Gets the display_name of this CreateProjectRequest.  # noqa: E501


        :return: The display_name of this CreateProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._display_name

    @display_name.setter
    def display_name(self, display_name):
        """Sets the display_name of this CreateProjectRequest.


        :param display_name: The display_name of this CreateProjectRequest.  # noqa: E501
        :type: str
        """

        self._display_name = display_name

    @property
    def parent_project_name(self):
        """Gets the parent_project_name of this CreateProjectRequest.  # noqa: E501


        :return: The parent_project_name of this CreateProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._parent_project_name

    @parent_project_name.setter
    def parent_project_name(self, parent_project_name):
        """Sets the parent_project_name of this CreateProjectRequest.


        :param parent_project_name: The parent_project_name of this CreateProjectRequest.  # noqa: E501
        :type: str
        """

        self._parent_project_name = parent_project_name

    @property
    def project_name(self):
        """Gets the project_name of this CreateProjectRequest.  # noqa: E501


        :return: The project_name of this CreateProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateProjectRequest.


        :param project_name: The project_name of this CreateProjectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and project_name is None:
            raise ValueError("Invalid value for `project_name`, must not be `None`")  # noqa: E501

        self._project_name = project_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateProjectRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateProjectRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateProjectRequest):
            return True

        return self.to_dict() != other.to_dict()
