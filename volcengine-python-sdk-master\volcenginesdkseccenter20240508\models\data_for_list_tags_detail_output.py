# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListTagsDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'tag_host_num': 'int',
        'tag_name': 'str'
    }

    attribute_map = {
        'tag_host_num': 'TagHostNum',
        'tag_name': 'TagName'
    }

    def __init__(self, tag_host_num=None, tag_name=None, _configuration=None):  # noqa: E501
        """DataForListTagsDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._tag_host_num = None
        self._tag_name = None
        self.discriminator = None

        if tag_host_num is not None:
            self.tag_host_num = tag_host_num
        if tag_name is not None:
            self.tag_name = tag_name

    @property
    def tag_host_num(self):
        """Gets the tag_host_num of this DataForListTagsDetailOutput.  # noqa: E501


        :return: The tag_host_num of this DataForListTagsDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._tag_host_num

    @tag_host_num.setter
    def tag_host_num(self, tag_host_num):
        """Sets the tag_host_num of this DataForListTagsDetailOutput.


        :param tag_host_num: The tag_host_num of this DataForListTagsDetailOutput.  # noqa: E501
        :type: int
        """

        self._tag_host_num = tag_host_num

    @property
    def tag_name(self):
        """Gets the tag_name of this DataForListTagsDetailOutput.  # noqa: E501


        :return: The tag_name of this DataForListTagsDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag_name

    @tag_name.setter
    def tag_name(self, tag_name):
        """Sets the tag_name of this DataForListTagsDetailOutput.


        :param tag_name: The tag_name of this DataForListTagsDetailOutput.  # noqa: E501
        :type: str
        """

        self._tag_name = tag_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListTagsDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListTagsDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListTagsDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
