# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateBatchInferenceJobRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'completion_window': 'str',
        'description': 'str',
        'dry_run': 'bool',
        'input_file_tos_location': 'InputFileTosLocationForCreateBatchInferenceJobInput',
        'model_reference': 'ModelReferenceForCreateBatchInferenceJobInput',
        'name': 'str',
        'output_dir_tos_location': 'OutputDirTosLocationForCreateBatchInferenceJobInput',
        'project_name': 'str',
        'tags': 'list[TagForCreateBatchInferenceJobInput]'
    }

    attribute_map = {
        'completion_window': 'CompletionWindow',
        'description': 'Description',
        'dry_run': 'DryRun',
        'input_file_tos_location': 'InputFileTosLocation',
        'model_reference': 'ModelReference',
        'name': 'Name',
        'output_dir_tos_location': 'OutputDirTosLocation',
        'project_name': 'ProjectName',
        'tags': 'Tags'
    }

    def __init__(self, completion_window=None, description=None, dry_run=None, input_file_tos_location=None, model_reference=None, name=None, output_dir_tos_location=None, project_name=None, tags=None, _configuration=None):  # noqa: E501
        """CreateBatchInferenceJobRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._completion_window = None
        self._description = None
        self._dry_run = None
        self._input_file_tos_location = None
        self._model_reference = None
        self._name = None
        self._output_dir_tos_location = None
        self._project_name = None
        self._tags = None
        self.discriminator = None

        if completion_window is not None:
            self.completion_window = completion_window
        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        if input_file_tos_location is not None:
            self.input_file_tos_location = input_file_tos_location
        if model_reference is not None:
            self.model_reference = model_reference
        self.name = name
        if output_dir_tos_location is not None:
            self.output_dir_tos_location = output_dir_tos_location
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags

    @property
    def completion_window(self):
        """Gets the completion_window of this CreateBatchInferenceJobRequest.  # noqa: E501


        :return: The completion_window of this CreateBatchInferenceJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._completion_window

    @completion_window.setter
    def completion_window(self, completion_window):
        """Sets the completion_window of this CreateBatchInferenceJobRequest.


        :param completion_window: The completion_window of this CreateBatchInferenceJobRequest.  # noqa: E501
        :type: str
        """

        self._completion_window = completion_window

    @property
    def description(self):
        """Gets the description of this CreateBatchInferenceJobRequest.  # noqa: E501


        :return: The description of this CreateBatchInferenceJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateBatchInferenceJobRequest.


        :param description: The description of this CreateBatchInferenceJobRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this CreateBatchInferenceJobRequest.  # noqa: E501


        :return: The dry_run of this CreateBatchInferenceJobRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this CreateBatchInferenceJobRequest.


        :param dry_run: The dry_run of this CreateBatchInferenceJobRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def input_file_tos_location(self):
        """Gets the input_file_tos_location of this CreateBatchInferenceJobRequest.  # noqa: E501


        :return: The input_file_tos_location of this CreateBatchInferenceJobRequest.  # noqa: E501
        :rtype: InputFileTosLocationForCreateBatchInferenceJobInput
        """
        return self._input_file_tos_location

    @input_file_tos_location.setter
    def input_file_tos_location(self, input_file_tos_location):
        """Sets the input_file_tos_location of this CreateBatchInferenceJobRequest.


        :param input_file_tos_location: The input_file_tos_location of this CreateBatchInferenceJobRequest.  # noqa: E501
        :type: InputFileTosLocationForCreateBatchInferenceJobInput
        """

        self._input_file_tos_location = input_file_tos_location

    @property
    def model_reference(self):
        """Gets the model_reference of this CreateBatchInferenceJobRequest.  # noqa: E501


        :return: The model_reference of this CreateBatchInferenceJobRequest.  # noqa: E501
        :rtype: ModelReferenceForCreateBatchInferenceJobInput
        """
        return self._model_reference

    @model_reference.setter
    def model_reference(self, model_reference):
        """Sets the model_reference of this CreateBatchInferenceJobRequest.


        :param model_reference: The model_reference of this CreateBatchInferenceJobRequest.  # noqa: E501
        :type: ModelReferenceForCreateBatchInferenceJobInput
        """

        self._model_reference = model_reference

    @property
    def name(self):
        """Gets the name of this CreateBatchInferenceJobRequest.  # noqa: E501


        :return: The name of this CreateBatchInferenceJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateBatchInferenceJobRequest.


        :param name: The name of this CreateBatchInferenceJobRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def output_dir_tos_location(self):
        """Gets the output_dir_tos_location of this CreateBatchInferenceJobRequest.  # noqa: E501


        :return: The output_dir_tos_location of this CreateBatchInferenceJobRequest.  # noqa: E501
        :rtype: OutputDirTosLocationForCreateBatchInferenceJobInput
        """
        return self._output_dir_tos_location

    @output_dir_tos_location.setter
    def output_dir_tos_location(self, output_dir_tos_location):
        """Sets the output_dir_tos_location of this CreateBatchInferenceJobRequest.


        :param output_dir_tos_location: The output_dir_tos_location of this CreateBatchInferenceJobRequest.  # noqa: E501
        :type: OutputDirTosLocationForCreateBatchInferenceJobInput
        """

        self._output_dir_tos_location = output_dir_tos_location

    @property
    def project_name(self):
        """Gets the project_name of this CreateBatchInferenceJobRequest.  # noqa: E501


        :return: The project_name of this CreateBatchInferenceJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateBatchInferenceJobRequest.


        :param project_name: The project_name of this CreateBatchInferenceJobRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateBatchInferenceJobRequest.  # noqa: E501


        :return: The tags of this CreateBatchInferenceJobRequest.  # noqa: E501
        :rtype: list[TagForCreateBatchInferenceJobInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateBatchInferenceJobRequest.


        :param tags: The tags of this CreateBatchInferenceJobRequest.  # noqa: E501
        :type: list[TagForCreateBatchInferenceJobInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateBatchInferenceJobRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateBatchInferenceJobRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateBatchInferenceJobRequest):
            return True

        return self.to_dict() != other.to_dict()
