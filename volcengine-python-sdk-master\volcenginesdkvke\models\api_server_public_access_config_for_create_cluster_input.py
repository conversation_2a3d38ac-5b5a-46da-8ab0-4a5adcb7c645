# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ApiServerPublicAccessConfigForCreateClusterInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'public_access_network_config': 'PublicAccessNetworkConfigForCreateClusterInput'
    }

    attribute_map = {
        'public_access_network_config': 'PublicAccessNetworkConfig'
    }

    def __init__(self, public_access_network_config=None, _configuration=None):  # noqa: E501
        """ApiServerPublicAccessConfigForCreateClusterInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._public_access_network_config = None
        self.discriminator = None

        if public_access_network_config is not None:
            self.public_access_network_config = public_access_network_config

    @property
    def public_access_network_config(self):
        """Gets the public_access_network_config of this ApiServerPublicAccessConfigForCreateClusterInput.  # noqa: E501


        :return: The public_access_network_config of this ApiServerPublicAccessConfigForCreateClusterInput.  # noqa: E501
        :rtype: PublicAccessNetworkConfigForCreateClusterInput
        """
        return self._public_access_network_config

    @public_access_network_config.setter
    def public_access_network_config(self, public_access_network_config):
        """Sets the public_access_network_config of this ApiServerPublicAccessConfigForCreateClusterInput.


        :param public_access_network_config: The public_access_network_config of this ApiServerPublicAccessConfigForCreateClusterInput.  # noqa: E501
        :type: PublicAccessNetworkConfigForCreateClusterInput
        """

        self._public_access_network_config = public_access_network_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ApiServerPublicAccessConfigForCreateClusterInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ApiServerPublicAccessConfigForCreateClusterInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ApiServerPublicAccessConfigForCreateClusterInput):
            return True

        return self.to_dict() != other.to_dict()
