# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MountServiceForDescribeMountServicesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'attach_file_systems': 'list[AttachFileSystemForDescribeMountServicesOutput]',
        'create_time': 'str',
        'mount_service_id': 'str',
        'mount_service_name': 'str',
        'nodes': 'list[NodeForDescribeMountServicesOutput]',
        'project': 'str',
        'region_id': 'str',
        'security_group_id': 'str',
        'status': 'str',
        'subnet_id': 'str',
        'version_number': 'str',
        'vpc_id': 'str',
        'zone_id': 'str',
        'zone_name': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'attach_file_systems': 'AttachFileSystems',
        'create_time': 'CreateTime',
        'mount_service_id': 'MountServiceId',
        'mount_service_name': 'MountServiceName',
        'nodes': 'Nodes',
        'project': 'Project',
        'region_id': 'RegionId',
        'security_group_id': 'SecurityGroupId',
        'status': 'Status',
        'subnet_id': 'SubnetId',
        'version_number': 'VersionNumber',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId',
        'zone_name': 'ZoneName'
    }

    def __init__(self, account_id=None, attach_file_systems=None, create_time=None, mount_service_id=None, mount_service_name=None, nodes=None, project=None, region_id=None, security_group_id=None, status=None, subnet_id=None, version_number=None, vpc_id=None, zone_id=None, zone_name=None, _configuration=None):  # noqa: E501
        """MountServiceForDescribeMountServicesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._attach_file_systems = None
        self._create_time = None
        self._mount_service_id = None
        self._mount_service_name = None
        self._nodes = None
        self._project = None
        self._region_id = None
        self._security_group_id = None
        self._status = None
        self._subnet_id = None
        self._version_number = None
        self._vpc_id = None
        self._zone_id = None
        self._zone_name = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if attach_file_systems is not None:
            self.attach_file_systems = attach_file_systems
        if create_time is not None:
            self.create_time = create_time
        if mount_service_id is not None:
            self.mount_service_id = mount_service_id
        if mount_service_name is not None:
            self.mount_service_name = mount_service_name
        if nodes is not None:
            self.nodes = nodes
        if project is not None:
            self.project = project
        if region_id is not None:
            self.region_id = region_id
        if security_group_id is not None:
            self.security_group_id = security_group_id
        if status is not None:
            self.status = status
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if version_number is not None:
            self.version_number = version_number
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id
        if zone_name is not None:
            self.zone_name = zone_name

    @property
    def account_id(self):
        """Gets the account_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The account_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this MountServiceForDescribeMountServicesOutput.


        :param account_id: The account_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def attach_file_systems(self):
        """Gets the attach_file_systems of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The attach_file_systems of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: list[AttachFileSystemForDescribeMountServicesOutput]
        """
        return self._attach_file_systems

    @attach_file_systems.setter
    def attach_file_systems(self, attach_file_systems):
        """Sets the attach_file_systems of this MountServiceForDescribeMountServicesOutput.


        :param attach_file_systems: The attach_file_systems of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: list[AttachFileSystemForDescribeMountServicesOutput]
        """

        self._attach_file_systems = attach_file_systems

    @property
    def create_time(self):
        """Gets the create_time of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The create_time of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this MountServiceForDescribeMountServicesOutput.


        :param create_time: The create_time of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def mount_service_id(self):
        """Gets the mount_service_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The mount_service_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._mount_service_id

    @mount_service_id.setter
    def mount_service_id(self, mount_service_id):
        """Sets the mount_service_id of this MountServiceForDescribeMountServicesOutput.


        :param mount_service_id: The mount_service_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._mount_service_id = mount_service_id

    @property
    def mount_service_name(self):
        """Gets the mount_service_name of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The mount_service_name of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._mount_service_name

    @mount_service_name.setter
    def mount_service_name(self, mount_service_name):
        """Sets the mount_service_name of this MountServiceForDescribeMountServicesOutput.


        :param mount_service_name: The mount_service_name of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._mount_service_name = mount_service_name

    @property
    def nodes(self):
        """Gets the nodes of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The nodes of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: list[NodeForDescribeMountServicesOutput]
        """
        return self._nodes

    @nodes.setter
    def nodes(self, nodes):
        """Sets the nodes of this MountServiceForDescribeMountServicesOutput.


        :param nodes: The nodes of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: list[NodeForDescribeMountServicesOutput]
        """

        self._nodes = nodes

    @property
    def project(self):
        """Gets the project of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The project of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this MountServiceForDescribeMountServicesOutput.


        :param project: The project of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def region_id(self):
        """Gets the region_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The region_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this MountServiceForDescribeMountServicesOutput.


        :param region_id: The region_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def security_group_id(self):
        """Gets the security_group_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The security_group_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._security_group_id

    @security_group_id.setter
    def security_group_id(self, security_group_id):
        """Sets the security_group_id of this MountServiceForDescribeMountServicesOutput.


        :param security_group_id: The security_group_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._security_group_id = security_group_id

    @property
    def status(self):
        """Gets the status of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The status of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this MountServiceForDescribeMountServicesOutput.


        :param status: The status of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def subnet_id(self):
        """Gets the subnet_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The subnet_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this MountServiceForDescribeMountServicesOutput.


        :param subnet_id: The subnet_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def version_number(self):
        """Gets the version_number of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The version_number of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._version_number

    @version_number.setter
    def version_number(self, version_number):
        """Sets the version_number of this MountServiceForDescribeMountServicesOutput.


        :param version_number: The version_number of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._version_number = version_number

    @property
    def vpc_id(self):
        """Gets the vpc_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The vpc_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this MountServiceForDescribeMountServicesOutput.


        :param vpc_id: The vpc_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The zone_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this MountServiceForDescribeMountServicesOutput.


        :param zone_id: The zone_id of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    @property
    def zone_name(self):
        """Gets the zone_name of this MountServiceForDescribeMountServicesOutput.  # noqa: E501


        :return: The zone_name of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this MountServiceForDescribeMountServicesOutput.


        :param zone_name: The zone_name of this MountServiceForDescribeMountServicesOutput.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MountServiceForDescribeMountServicesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MountServiceForDescribeMountServicesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MountServiceForDescribeMountServicesOutput):
            return True

        return self.to_dict() != other.to_dict()
