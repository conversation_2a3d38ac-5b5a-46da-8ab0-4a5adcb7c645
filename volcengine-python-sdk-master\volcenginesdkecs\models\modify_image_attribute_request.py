# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyImageAttributeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'boot_mode': 'str',
        'description': 'str',
        'image_id': 'str',
        'image_name': 'str'
    }

    attribute_map = {
        'boot_mode': 'BootMode',
        'description': 'Description',
        'image_id': 'ImageId',
        'image_name': 'ImageName'
    }

    def __init__(self, boot_mode=None, description=None, image_id=None, image_name=None, _configuration=None):  # noqa: E501
        """ModifyImageAttributeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._boot_mode = None
        self._description = None
        self._image_id = None
        self._image_name = None
        self.discriminator = None

        if boot_mode is not None:
            self.boot_mode = boot_mode
        if description is not None:
            self.description = description
        self.image_id = image_id
        if image_name is not None:
            self.image_name = image_name

    @property
    def boot_mode(self):
        """Gets the boot_mode of this ModifyImageAttributeRequest.  # noqa: E501


        :return: The boot_mode of this ModifyImageAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._boot_mode

    @boot_mode.setter
    def boot_mode(self, boot_mode):
        """Sets the boot_mode of this ModifyImageAttributeRequest.


        :param boot_mode: The boot_mode of this ModifyImageAttributeRequest.  # noqa: E501
        :type: str
        """

        self._boot_mode = boot_mode

    @property
    def description(self):
        """Gets the description of this ModifyImageAttributeRequest.  # noqa: E501


        :return: The description of this ModifyImageAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyImageAttributeRequest.


        :param description: The description of this ModifyImageAttributeRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def image_id(self):
        """Gets the image_id of this ModifyImageAttributeRequest.  # noqa: E501


        :return: The image_id of this ModifyImageAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this ModifyImageAttributeRequest.


        :param image_id: The image_id of this ModifyImageAttributeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and image_id is None:
            raise ValueError("Invalid value for `image_id`, must not be `None`")  # noqa: E501

        self._image_id = image_id

    @property
    def image_name(self):
        """Gets the image_name of this ModifyImageAttributeRequest.  # noqa: E501


        :return: The image_name of this ModifyImageAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this ModifyImageAttributeRequest.


        :param image_name: The image_name of this ModifyImageAttributeRequest.  # noqa: E501
        :type: str
        """

        self._image_name = image_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyImageAttributeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyImageAttributeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyImageAttributeRequest):
            return True

        return self.to_dict() != other.to_dict()
