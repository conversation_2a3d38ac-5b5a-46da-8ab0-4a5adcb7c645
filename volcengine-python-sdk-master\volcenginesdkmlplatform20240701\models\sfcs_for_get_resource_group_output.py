# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SfcsForGetResourceGroupOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'replicas': 'int',
        'status': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'id': 'Id',
        'name': 'Name',
        'replicas': 'Replicas',
        'status': 'Status',
        'zone_id': 'ZoneId'
    }

    def __init__(self, description=None, id=None, name=None, replicas=None, status=None, zone_id=None, _configuration=None):  # noqa: E501
        """SfcsForGetResourceGroupOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._id = None
        self._name = None
        self._replicas = None
        self._status = None
        self._zone_id = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if replicas is not None:
            self.replicas = replicas
        if status is not None:
            self.status = status
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def description(self):
        """Gets the description of this SfcsForGetResourceGroupOutput.  # noqa: E501


        :return: The description of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this SfcsForGetResourceGroupOutput.


        :param description: The description of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this SfcsForGetResourceGroupOutput.  # noqa: E501


        :return: The id of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this SfcsForGetResourceGroupOutput.


        :param id: The id of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this SfcsForGetResourceGroupOutput.  # noqa: E501


        :return: The name of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this SfcsForGetResourceGroupOutput.


        :param name: The name of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def replicas(self):
        """Gets the replicas of this SfcsForGetResourceGroupOutput.  # noqa: E501


        :return: The replicas of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :rtype: int
        """
        return self._replicas

    @replicas.setter
    def replicas(self, replicas):
        """Sets the replicas of this SfcsForGetResourceGroupOutput.


        :param replicas: The replicas of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :type: int
        """

        self._replicas = replicas

    @property
    def status(self):
        """Gets the status of this SfcsForGetResourceGroupOutput.  # noqa: E501


        :return: The status of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SfcsForGetResourceGroupOutput.


        :param status: The status of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def zone_id(self):
        """Gets the zone_id of this SfcsForGetResourceGroupOutput.  # noqa: E501


        :return: The zone_id of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this SfcsForGetResourceGroupOutput.


        :param zone_id: The zone_id of this SfcsForGetResourceGroupOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SfcsForGetResourceGroupOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SfcsForGetResourceGroupOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SfcsForGetResourceGroupOutput):
            return True

        return self.to_dict() != other.to_dict()
