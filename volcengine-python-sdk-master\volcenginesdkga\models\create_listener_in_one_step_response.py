# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateListenerInOneStepResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_endpoint_group_ids': 'list[str]',
        'endpoint_group_ids': 'list[str]',
        'listener_id': 'str'
    }

    attribute_map = {
        'backup_endpoint_group_ids': 'BackupEndpointGroupIds',
        'endpoint_group_ids': 'EndpointGroupIds',
        'listener_id': 'ListenerId'
    }

    def __init__(self, backup_endpoint_group_ids=None, endpoint_group_ids=None, listener_id=None, _configuration=None):  # noqa: E501
        """CreateListenerInOneStepResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_endpoint_group_ids = None
        self._endpoint_group_ids = None
        self._listener_id = None
        self.discriminator = None

        if backup_endpoint_group_ids is not None:
            self.backup_endpoint_group_ids = backup_endpoint_group_ids
        if endpoint_group_ids is not None:
            self.endpoint_group_ids = endpoint_group_ids
        if listener_id is not None:
            self.listener_id = listener_id

    @property
    def backup_endpoint_group_ids(self):
        """Gets the backup_endpoint_group_ids of this CreateListenerInOneStepResponse.  # noqa: E501


        :return: The backup_endpoint_group_ids of this CreateListenerInOneStepResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._backup_endpoint_group_ids

    @backup_endpoint_group_ids.setter
    def backup_endpoint_group_ids(self, backup_endpoint_group_ids):
        """Sets the backup_endpoint_group_ids of this CreateListenerInOneStepResponse.


        :param backup_endpoint_group_ids: The backup_endpoint_group_ids of this CreateListenerInOneStepResponse.  # noqa: E501
        :type: list[str]
        """

        self._backup_endpoint_group_ids = backup_endpoint_group_ids

    @property
    def endpoint_group_ids(self):
        """Gets the endpoint_group_ids of this CreateListenerInOneStepResponse.  # noqa: E501


        :return: The endpoint_group_ids of this CreateListenerInOneStepResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._endpoint_group_ids

    @endpoint_group_ids.setter
    def endpoint_group_ids(self, endpoint_group_ids):
        """Sets the endpoint_group_ids of this CreateListenerInOneStepResponse.


        :param endpoint_group_ids: The endpoint_group_ids of this CreateListenerInOneStepResponse.  # noqa: E501
        :type: list[str]
        """

        self._endpoint_group_ids = endpoint_group_ids

    @property
    def listener_id(self):
        """Gets the listener_id of this CreateListenerInOneStepResponse.  # noqa: E501


        :return: The listener_id of this CreateListenerInOneStepResponse.  # noqa: E501
        :rtype: str
        """
        return self._listener_id

    @listener_id.setter
    def listener_id(self, listener_id):
        """Sets the listener_id of this CreateListenerInOneStepResponse.


        :param listener_id: The listener_id of this CreateListenerInOneStepResponse.  # noqa: E501
        :type: str
        """

        self._listener_id = listener_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateListenerInOneStepResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateListenerInOneStepResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateListenerInOneStepResponse):
            return True

        return self.to_dict() != other.to_dict()
