# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateClusterConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'cluster_config': 'ClusterConfigForUpdateClusterConfigInput',
        'delete_protection_enabled': 'bool',
        'description': 'str',
        'id': 'str',
        'logging_config': 'LoggingConfigForUpdateClusterConfigInput',
        'monitoring_config': 'MonitoringConfigForUpdateClusterConfigInput',
        'name': 'str',
        'pods_config': 'PodsConfigForUpdateClusterConfigInput',
        'register_monitoring_config': 'RegisterMonitoringConfigForUpdateClusterConfigInput',
        'source_region': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'cluster_config': 'ClusterConfig',
        'delete_protection_enabled': 'DeleteProtectionEnabled',
        'description': 'Description',
        'id': 'Id',
        'logging_config': 'LoggingConfig',
        'monitoring_config': 'MonitoringConfig',
        'name': 'Name',
        'pods_config': 'PodsConfig',
        'register_monitoring_config': 'RegisterMonitoringConfig',
        'source_region': 'SourceRegion'
    }

    def __init__(self, client_token=None, cluster_config=None, delete_protection_enabled=None, description=None, id=None, logging_config=None, monitoring_config=None, name=None, pods_config=None, register_monitoring_config=None, source_region=None, _configuration=None):  # noqa: E501
        """UpdateClusterConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._cluster_config = None
        self._delete_protection_enabled = None
        self._description = None
        self._id = None
        self._logging_config = None
        self._monitoring_config = None
        self._name = None
        self._pods_config = None
        self._register_monitoring_config = None
        self._source_region = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if cluster_config is not None:
            self.cluster_config = cluster_config
        if delete_protection_enabled is not None:
            self.delete_protection_enabled = delete_protection_enabled
        if description is not None:
            self.description = description
        self.id = id
        if logging_config is not None:
            self.logging_config = logging_config
        if monitoring_config is not None:
            self.monitoring_config = monitoring_config
        if name is not None:
            self.name = name
        if pods_config is not None:
            self.pods_config = pods_config
        if register_monitoring_config is not None:
            self.register_monitoring_config = register_monitoring_config
        if source_region is not None:
            self.source_region = source_region

    @property
    def client_token(self):
        """Gets the client_token of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The client_token of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this UpdateClusterConfigRequest.


        :param client_token: The client_token of this UpdateClusterConfigRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def cluster_config(self):
        """Gets the cluster_config of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The cluster_config of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: ClusterConfigForUpdateClusterConfigInput
        """
        return self._cluster_config

    @cluster_config.setter
    def cluster_config(self, cluster_config):
        """Sets the cluster_config of this UpdateClusterConfigRequest.


        :param cluster_config: The cluster_config of this UpdateClusterConfigRequest.  # noqa: E501
        :type: ClusterConfigForUpdateClusterConfigInput
        """

        self._cluster_config = cluster_config

    @property
    def delete_protection_enabled(self):
        """Gets the delete_protection_enabled of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The delete_protection_enabled of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._delete_protection_enabled

    @delete_protection_enabled.setter
    def delete_protection_enabled(self, delete_protection_enabled):
        """Sets the delete_protection_enabled of this UpdateClusterConfigRequest.


        :param delete_protection_enabled: The delete_protection_enabled of this UpdateClusterConfigRequest.  # noqa: E501
        :type: bool
        """

        self._delete_protection_enabled = delete_protection_enabled

    @property
    def description(self):
        """Gets the description of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The description of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateClusterConfigRequest.


        :param description: The description of this UpdateClusterConfigRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The id of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateClusterConfigRequest.


        :param id: The id of this UpdateClusterConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def logging_config(self):
        """Gets the logging_config of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The logging_config of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: LoggingConfigForUpdateClusterConfigInput
        """
        return self._logging_config

    @logging_config.setter
    def logging_config(self, logging_config):
        """Sets the logging_config of this UpdateClusterConfigRequest.


        :param logging_config: The logging_config of this UpdateClusterConfigRequest.  # noqa: E501
        :type: LoggingConfigForUpdateClusterConfigInput
        """

        self._logging_config = logging_config

    @property
    def monitoring_config(self):
        """Gets the monitoring_config of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The monitoring_config of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: MonitoringConfigForUpdateClusterConfigInput
        """
        return self._monitoring_config

    @monitoring_config.setter
    def monitoring_config(self, monitoring_config):
        """Sets the monitoring_config of this UpdateClusterConfigRequest.


        :param monitoring_config: The monitoring_config of this UpdateClusterConfigRequest.  # noqa: E501
        :type: MonitoringConfigForUpdateClusterConfigInput
        """

        self._monitoring_config = monitoring_config

    @property
    def name(self):
        """Gets the name of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The name of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateClusterConfigRequest.


        :param name: The name of this UpdateClusterConfigRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def pods_config(self):
        """Gets the pods_config of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The pods_config of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: PodsConfigForUpdateClusterConfigInput
        """
        return self._pods_config

    @pods_config.setter
    def pods_config(self, pods_config):
        """Sets the pods_config of this UpdateClusterConfigRequest.


        :param pods_config: The pods_config of this UpdateClusterConfigRequest.  # noqa: E501
        :type: PodsConfigForUpdateClusterConfigInput
        """

        self._pods_config = pods_config

    @property
    def register_monitoring_config(self):
        """Gets the register_monitoring_config of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The register_monitoring_config of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: RegisterMonitoringConfigForUpdateClusterConfigInput
        """
        return self._register_monitoring_config

    @register_monitoring_config.setter
    def register_monitoring_config(self, register_monitoring_config):
        """Sets the register_monitoring_config of this UpdateClusterConfigRequest.


        :param register_monitoring_config: The register_monitoring_config of this UpdateClusterConfigRequest.  # noqa: E501
        :type: RegisterMonitoringConfigForUpdateClusterConfigInput
        """

        self._register_monitoring_config = register_monitoring_config

    @property
    def source_region(self):
        """Gets the source_region of this UpdateClusterConfigRequest.  # noqa: E501


        :return: The source_region of this UpdateClusterConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_region

    @source_region.setter
    def source_region(self, source_region):
        """Sets the source_region of this UpdateClusterConfigRequest.


        :param source_region: The source_region of this UpdateClusterConfigRequest.  # noqa: E501
        :type: str
        """

        self._source_region = source_region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateClusterConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateClusterConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateClusterConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
