# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVQUserDataAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'date_time': 'str',
        'users': 'list[UserForGetVQUserDataAPIInput]'
    }

    attribute_map = {
        'date_time': 'DateTime',
        'users': 'Users'
    }

    def __init__(self, date_time=None, users=None, _configuration=None):  # noqa: E501
        """GetVQUserDataAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._date_time = None
        self._users = None
        self.discriminator = None

        self.date_time = date_time
        if users is not None:
            self.users = users

    @property
    def date_time(self):
        """Gets the date_time of this GetVQUserDataAPIRequest.  # noqa: E501


        :return: The date_time of this GetVQUserDataAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._date_time

    @date_time.setter
    def date_time(self, date_time):
        """Sets the date_time of this GetVQUserDataAPIRequest.


        :param date_time: The date_time of this GetVQUserDataAPIRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and date_time is None:
            raise ValueError("Invalid value for `date_time`, must not be `None`")  # noqa: E501

        self._date_time = date_time

    @property
    def users(self):
        """Gets the users of this GetVQUserDataAPIRequest.  # noqa: E501


        :return: The users of this GetVQUserDataAPIRequest.  # noqa: E501
        :rtype: list[UserForGetVQUserDataAPIInput]
        """
        return self._users

    @users.setter
    def users(self, users):
        """Sets the users of this GetVQUserDataAPIRequest.


        :param users: The users of this GetVQUserDataAPIRequest.  # noqa: E501
        :type: list[UserForGetVQUserDataAPIInput]
        """

        self._users = users

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVQUserDataAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVQUserDataAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVQUserDataAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
