# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PublicBandwidthPackageForDescribePublicBandwidthPackageOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_ids': 'list[str]',
        'account_id': 'str',
        'bandwidth': 'int',
        'bandwidth_type': 'str',
        'begin_time': 'int',
        'billing_type': 'str',
        'charge_type': 'str',
        'create_time': 'int',
        'create_time_str': 'str',
        'expired_time': 'int',
        'id': 'str',
        'name': 'str',
        'project_name': 'str',
        'renew_type': 'int',
        'resource_tags': 'list[ResourceTagForDescribePublicBandwidthPackageOutput]',
        'state': 'str'
    }

    attribute_map = {
        'accelerator_ids': 'AcceleratorIDs',
        'account_id': 'AccountID',
        'bandwidth': 'Bandwidth',
        'bandwidth_type': 'BandwidthType',
        'begin_time': 'BeginTime',
        'billing_type': 'BillingType',
        'charge_type': 'ChargeType',
        'create_time': 'CreateTime',
        'create_time_str': 'CreateTimeStr',
        'expired_time': 'ExpiredTime',
        'id': 'ID',
        'name': 'Name',
        'project_name': 'ProjectName',
        'renew_type': 'RenewType',
        'resource_tags': 'ResourceTags',
        'state': 'State'
    }

    def __init__(self, accelerator_ids=None, account_id=None, bandwidth=None, bandwidth_type=None, begin_time=None, billing_type=None, charge_type=None, create_time=None, create_time_str=None, expired_time=None, id=None, name=None, project_name=None, renew_type=None, resource_tags=None, state=None, _configuration=None):  # noqa: E501
        """PublicBandwidthPackageForDescribePublicBandwidthPackageOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_ids = None
        self._account_id = None
        self._bandwidth = None
        self._bandwidth_type = None
        self._begin_time = None
        self._billing_type = None
        self._charge_type = None
        self._create_time = None
        self._create_time_str = None
        self._expired_time = None
        self._id = None
        self._name = None
        self._project_name = None
        self._renew_type = None
        self._resource_tags = None
        self._state = None
        self.discriminator = None

        if accelerator_ids is not None:
            self.accelerator_ids = accelerator_ids
        if account_id is not None:
            self.account_id = account_id
        if bandwidth is not None:
            self.bandwidth = bandwidth
        if bandwidth_type is not None:
            self.bandwidth_type = bandwidth_type
        if begin_time is not None:
            self.begin_time = begin_time
        if billing_type is not None:
            self.billing_type = billing_type
        if charge_type is not None:
            self.charge_type = charge_type
        if create_time is not None:
            self.create_time = create_time
        if create_time_str is not None:
            self.create_time_str = create_time_str
        if expired_time is not None:
            self.expired_time = expired_time
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if project_name is not None:
            self.project_name = project_name
        if renew_type is not None:
            self.renew_type = renew_type
        if resource_tags is not None:
            self.resource_tags = resource_tags
        if state is not None:
            self.state = state

    @property
    def accelerator_ids(self):
        """Gets the accelerator_ids of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The accelerator_ids of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._accelerator_ids

    @accelerator_ids.setter
    def accelerator_ids(self, accelerator_ids):
        """Sets the accelerator_ids of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param accelerator_ids: The accelerator_ids of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: list[str]
        """

        self._accelerator_ids = accelerator_ids

    @property
    def account_id(self):
        """Gets the account_id of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The account_id of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param account_id: The account_id of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def bandwidth(self):
        """Gets the bandwidth of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The bandwidth of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param bandwidth: The bandwidth of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def bandwidth_type(self):
        """Gets the bandwidth_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The bandwidth_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth_type

    @bandwidth_type.setter
    def bandwidth_type(self, bandwidth_type):
        """Sets the bandwidth_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param bandwidth_type: The bandwidth_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: str
        """

        self._bandwidth_type = bandwidth_type

    @property
    def begin_time(self):
        """Gets the begin_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The begin_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: int
        """
        return self._begin_time

    @begin_time.setter
    def begin_time(self, begin_time):
        """Sets the begin_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param begin_time: The begin_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: int
        """

        self._begin_time = begin_time

    @property
    def billing_type(self):
        """Gets the billing_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The billing_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param billing_type: The billing_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: str
        """

        self._billing_type = billing_type

    @property
    def charge_type(self):
        """Gets the charge_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The charge_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param charge_type: The charge_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def create_time(self):
        """Gets the create_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The create_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param create_time: The create_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def create_time_str(self):
        """Gets the create_time_str of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The create_time_str of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time_str

    @create_time_str.setter
    def create_time_str(self, create_time_str):
        """Sets the create_time_str of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param create_time_str: The create_time_str of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: str
        """

        self._create_time_str = create_time_str

    @property
    def expired_time(self):
        """Gets the expired_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The expired_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: int
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param expired_time: The expired_time of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: int
        """

        self._expired_time = expired_time

    @property
    def id(self):
        """Gets the id of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The id of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param id: The id of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The name of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param name: The name of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The project_name of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param project_name: The project_name of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def renew_type(self):
        """Gets the renew_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The renew_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: int
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param renew_type: The renew_type of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: int
        """

        self._renew_type = renew_type

    @property
    def resource_tags(self):
        """Gets the resource_tags of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The resource_tags of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: list[ResourceTagForDescribePublicBandwidthPackageOutput]
        """
        return self._resource_tags

    @resource_tags.setter
    def resource_tags(self, resource_tags):
        """Sets the resource_tags of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param resource_tags: The resource_tags of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: list[ResourceTagForDescribePublicBandwidthPackageOutput]
        """

        self._resource_tags = resource_tags

    @property
    def state(self):
        """Gets the state of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501


        :return: The state of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.


        :param state: The state of this PublicBandwidthPackageForDescribePublicBandwidthPackageOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PublicBandwidthPackageForDescribePublicBandwidthPackageOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PublicBandwidthPackageForDescribePublicBandwidthPackageOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PublicBandwidthPackageForDescribePublicBandwidthPackageOutput):
            return True

        return self.to_dict() != other.to_dict()
