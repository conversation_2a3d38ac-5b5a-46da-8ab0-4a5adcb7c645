# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeServerGroupBackendServersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_ids': 'list[str]',
        'ips': 'list[str]',
        'page_number': 'str',
        'page_size': 'str',
        'server_group_id': 'str'
    }

    attribute_map = {
        'instance_ids': 'InstanceIds',
        'ips': 'Ips',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'server_group_id': 'ServerGroupId'
    }

    def __init__(self, instance_ids=None, ips=None, page_number=None, page_size=None, server_group_id=None, _configuration=None):  # noqa: E501
        """DescribeServerGroupBackendServersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_ids = None
        self._ips = None
        self._page_number = None
        self._page_size = None
        self._server_group_id = None
        self.discriminator = None

        if instance_ids is not None:
            self.instance_ids = instance_ids
        if ips is not None:
            self.ips = ips
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        self.server_group_id = server_group_id

    @property
    def instance_ids(self):
        """Gets the instance_ids of this DescribeServerGroupBackendServersRequest.  # noqa: E501


        :return: The instance_ids of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ids

    @instance_ids.setter
    def instance_ids(self, instance_ids):
        """Sets the instance_ids of this DescribeServerGroupBackendServersRequest.


        :param instance_ids: The instance_ids of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_ids = instance_ids

    @property
    def ips(self):
        """Gets the ips of this DescribeServerGroupBackendServersRequest.  # noqa: E501


        :return: The ips of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ips

    @ips.setter
    def ips(self, ips):
        """Sets the ips of this DescribeServerGroupBackendServersRequest.


        :param ips: The ips of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :type: list[str]
        """

        self._ips = ips

    @property
    def page_number(self):
        """Gets the page_number of this DescribeServerGroupBackendServersRequest.  # noqa: E501


        :return: The page_number of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeServerGroupBackendServersRequest.


        :param page_number: The page_number of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :type: str
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeServerGroupBackendServersRequest.  # noqa: E501


        :return: The page_size of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeServerGroupBackendServersRequest.


        :param page_size: The page_size of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :type: str
        """

        self._page_size = page_size

    @property
    def server_group_id(self):
        """Gets the server_group_id of this DescribeServerGroupBackendServersRequest.  # noqa: E501


        :return: The server_group_id of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this DescribeServerGroupBackendServersRequest.


        :param server_group_id: The server_group_id of this DescribeServerGroupBackendServersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and server_group_id is None:
            raise ValueError("Invalid value for `server_group_id`, must not be `None`")  # noqa: E501

        self._server_group_id = server_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeServerGroupBackendServersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeServerGroupBackendServersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeServerGroupBackendServersRequest):
            return True

        return self.to_dict() != other.to_dict()
