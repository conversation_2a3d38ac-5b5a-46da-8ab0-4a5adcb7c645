# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AlarmNodeForGetMlpAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_expires': 'str',
        'additional_actions_string': 'str',
        'args_array': 'list[str]',
        'argv': 'str',
        'bruteforce_sip': 'str',
        'category_name': 'str',
        '_class': 'str',
        'cmd': 'str',
        'comm': 'str',
        'command_line': 'str',
        'configuration': 'str',
        'configuration_file_hash': 'str',
        'connect_info': 'str',
        'contents': 'str',
        'create_at': 'str',
        'current_directory': 'str',
        'data_type': 'str',
        'description': 'str',
        'destination_hostname': 'str',
        'destination_ip': 'str',
        'destination_port': 'str',
        'destination_port_name': 'str',
        'details': 'str',
        'detection_user': 'str',
        'dip': 'str',
        'dport': 'str',
        'dst_list': 'str',
        'event_type': 'str',
        'exe': 'str',
        'exe_hash': 'str',
        'external_conns': 'str',
        'failure_reason': 'str',
        'fd_name': 'str',
        'file_downloadable': 'bool',
        'file_path': 'str',
        'flags': 'str',
        'hash': 'str',
        'hashes': 'str',
        'highlight_fields': 'str',
        'hit_argv_list': 'list[str]',
        'hit_data': 'str',
        'home_directory': 'str',
        'image': 'str',
        'image_loaded': 'str',
        'interrupt_number': 'str',
        'ioc_detail': 'str',
        'ioc_meta': 'str',
        'ioc_severity': 'str',
        'ioc_source': 'str',
        'ip_address': 'str',
        'ko_file': 'str',
        'ld_preload': 'str',
        'logon_type': 'str',
        'md5_hash': 'str',
        'mod_info': 'str',
        'modify_at': 'str',
        'module_name': 'str',
        'name': 'str',
        'new_name': 'str',
        'new_thread_id': 'str',
        'nspid': 'str',
        'old_name': 'str',
        'old_uid': 'str',
        'old_username': 'str',
        'original_file_name': 'str',
        'parent_command_line': 'str',
        'parent_image': 'str',
        'path': 'str',
        'pgid': 'str',
        'pgid_argv': 'str',
        'pid': 'str',
        'pid_set': 'str',
        'pid_tree': 'str',
        'ppid': 'str',
        'ppid_argv': 'str',
        'privilege_list': 'str',
        'probe_hook': 'str',
        'process_guid': 'str',
        'process_id': 'str',
        'process_name': 'str',
        'protocol': 'str',
        'ptrace_request': 'str',
        'query': 'str',
        'query_name': 'str',
        'query_results': 'str',
        'query_status': 'str',
        'run_path': 'str',
        'sam_account_name': 'str',
        'service_account': 'str',
        'service_file_name': 'str',
        'service_name': 'str',
        'service_start_type': 'str',
        'service_type': 'str',
        'severity_name': 'str',
        'sid': 'str',
        'signature': 'str',
        'signature_status': 'str',
        'sip': 'str',
        'socket_argv': 'str',
        'socket_pid': 'str',
        'source_ip': 'str',
        'source_image': 'str',
        'source_name': 'str',
        'source_port': 'str',
        'source_process_guid': 'str',
        'source_user': 'str',
        'sport': 'str',
        'src_list': 'str',
        'ssh': 'str',
        'ssh_info': 'str',
        'stack_trace_format': 'str',
        'stack_trace_hash': 'str',
        'start_function': 'str',
        'start_module': 'str',
        'static_file': 'str',
        'stdin': 'str',
        'stdout': 'str',
        'subject_user_name': 'str',
        'symbol_hooked': 'str',
        'symbol_so': 'str',
        'syscall_number': 'str',
        'target_argv': 'str',
        'target_domain_name': 'str',
        'target_filename': 'str',
        'target_object': 'str',
        'target_pid': 'str',
        'target_user_name': 'str',
        'task_content': 'str',
        'task_name': 'str',
        'threat_name': 'str',
        'timestamp': 'str',
        'types': 'str',
        'uid': 'str',
        'user': 'str',
        'user_principal_name': 'str',
        'username': 'str',
        'virus_hit_data_list': 'list[VirusHitDataListForGetMlpAlarmSummaryInfoOutput]'
    }

    attribute_map = {
        'account_expires': 'AccountExpires',
        'additional_actions_string': 'AdditionalActionsString',
        'args_array': 'ArgsArray',
        'argv': 'Argv',
        'bruteforce_sip': 'BruteforceSip',
        'category_name': 'CategoryName',
        '_class': 'Class',
        'cmd': 'Cmd',
        'comm': 'Comm',
        'command_line': 'CommandLine',
        'configuration': 'Configuration',
        'configuration_file_hash': 'ConfigurationFileHash',
        'connect_info': 'ConnectInfo',
        'contents': 'Contents',
        'create_at': 'CreateAt',
        'current_directory': 'CurrentDirectory',
        'data_type': 'DataType',
        'description': 'Description',
        'destination_hostname': 'DestinationHostname',
        'destination_ip': 'DestinationIP',
        'destination_port': 'DestinationPort',
        'destination_port_name': 'DestinationPortName',
        'details': 'Details',
        'detection_user': 'DetectionUser',
        'dip': 'Dip',
        'dport': 'Dport',
        'dst_list': 'DstList',
        'event_type': 'EventType',
        'exe': 'Exe',
        'exe_hash': 'ExeHash',
        'external_conns': 'ExternalConns',
        'failure_reason': 'FailureReason',
        'fd_name': 'FdName',
        'file_downloadable': 'FileDownloadable',
        'file_path': 'FilePath',
        'flags': 'Flags',
        'hash': 'Hash',
        'hashes': 'Hashes',
        'highlight_fields': 'HighlightFields',
        'hit_argv_list': 'HitArgvList',
        'hit_data': 'HitData',
        'home_directory': 'HomeDirectory',
        'image': 'Image',
        'image_loaded': 'ImageLoaded',
        'interrupt_number': 'InterruptNumber',
        'ioc_detail': 'IocDetail',
        'ioc_meta': 'IocMeta',
        'ioc_severity': 'IocSeverity',
        'ioc_source': 'IocSource',
        'ip_address': 'IpAddress',
        'ko_file': 'KoFile',
        'ld_preload': 'LdPreload',
        'logon_type': 'LogonType',
        'md5_hash': 'Md5Hash',
        'mod_info': 'ModInfo',
        'modify_at': 'ModifyAt',
        'module_name': 'ModuleName',
        'name': 'Name',
        'new_name': 'NewName',
        'new_thread_id': 'NewThreadID',
        'nspid': 'Nspid',
        'old_name': 'OldName',
        'old_uid': 'OldUid',
        'old_username': 'OldUsername',
        'original_file_name': 'OriginalFileName',
        'parent_command_line': 'ParentCommandLine',
        'parent_image': 'ParentImage',
        'path': 'Path',
        'pgid': 'Pgid',
        'pgid_argv': 'PgidArgv',
        'pid': 'Pid',
        'pid_set': 'PidSet',
        'pid_tree': 'PidTree',
        'ppid': 'Ppid',
        'ppid_argv': 'PpidArgv',
        'privilege_list': 'PrivilegeList',
        'probe_hook': 'ProbeHook',
        'process_guid': 'ProcessGuid',
        'process_id': 'ProcessID',
        'process_name': 'ProcessName',
        'protocol': 'Protocol',
        'ptrace_request': 'PtraceRequest',
        'query': 'Query',
        'query_name': 'QueryName',
        'query_results': 'QueryResults',
        'query_status': 'QueryStatus',
        'run_path': 'RunPath',
        'sam_account_name': 'SamAccountName',
        'service_account': 'ServiceAccount',
        'service_file_name': 'ServiceFileName',
        'service_name': 'ServiceName',
        'service_start_type': 'ServiceStartType',
        'service_type': 'ServiceType',
        'severity_name': 'SeverityName',
        'sid': 'Sid',
        'signature': 'Signature',
        'signature_status': 'SignatureStatus',
        'sip': 'Sip',
        'socket_argv': 'SocketArgv',
        'socket_pid': 'SocketPid',
        'source_ip': 'SourceIP',
        'source_image': 'SourceImage',
        'source_name': 'SourceName',
        'source_port': 'SourcePort',
        'source_process_guid': 'SourceProcessGuid',
        'source_user': 'SourceUser',
        'sport': 'Sport',
        'src_list': 'SrcList',
        'ssh': 'Ssh',
        'ssh_info': 'SshInfo',
        'stack_trace_format': 'StackTraceFormat',
        'stack_trace_hash': 'StackTraceHash',
        'start_function': 'StartFunction',
        'start_module': 'StartModule',
        'static_file': 'StaticFile',
        'stdin': 'Stdin',
        'stdout': 'Stdout',
        'subject_user_name': 'SubjectUserName',
        'symbol_hooked': 'SymbolHooked',
        'symbol_so': 'SymbolSo',
        'syscall_number': 'SyscallNumber',
        'target_argv': 'TargetArgv',
        'target_domain_name': 'TargetDomainName',
        'target_filename': 'TargetFilename',
        'target_object': 'TargetObject',
        'target_pid': 'TargetPid',
        'target_user_name': 'TargetUserName',
        'task_content': 'TaskContent',
        'task_name': 'TaskName',
        'threat_name': 'ThreatName',
        'timestamp': 'Timestamp',
        'types': 'Types',
        'uid': 'Uid',
        'user': 'User',
        'user_principal_name': 'UserPrincipalName',
        'username': 'Username',
        'virus_hit_data_list': 'VirusHitDataList'
    }

    def __init__(self, account_expires=None, additional_actions_string=None, args_array=None, argv=None, bruteforce_sip=None, category_name=None, _class=None, cmd=None, comm=None, command_line=None, configuration=None, configuration_file_hash=None, connect_info=None, contents=None, create_at=None, current_directory=None, data_type=None, description=None, destination_hostname=None, destination_ip=None, destination_port=None, destination_port_name=None, details=None, detection_user=None, dip=None, dport=None, dst_list=None, event_type=None, exe=None, exe_hash=None, external_conns=None, failure_reason=None, fd_name=None, file_downloadable=None, file_path=None, flags=None, hash=None, hashes=None, highlight_fields=None, hit_argv_list=None, hit_data=None, home_directory=None, image=None, image_loaded=None, interrupt_number=None, ioc_detail=None, ioc_meta=None, ioc_severity=None, ioc_source=None, ip_address=None, ko_file=None, ld_preload=None, logon_type=None, md5_hash=None, mod_info=None, modify_at=None, module_name=None, name=None, new_name=None, new_thread_id=None, nspid=None, old_name=None, old_uid=None, old_username=None, original_file_name=None, parent_command_line=None, parent_image=None, path=None, pgid=None, pgid_argv=None, pid=None, pid_set=None, pid_tree=None, ppid=None, ppid_argv=None, privilege_list=None, probe_hook=None, process_guid=None, process_id=None, process_name=None, protocol=None, ptrace_request=None, query=None, query_name=None, query_results=None, query_status=None, run_path=None, sam_account_name=None, service_account=None, service_file_name=None, service_name=None, service_start_type=None, service_type=None, severity_name=None, sid=None, signature=None, signature_status=None, sip=None, socket_argv=None, socket_pid=None, source_ip=None, source_image=None, source_name=None, source_port=None, source_process_guid=None, source_user=None, sport=None, src_list=None, ssh=None, ssh_info=None, stack_trace_format=None, stack_trace_hash=None, start_function=None, start_module=None, static_file=None, stdin=None, stdout=None, subject_user_name=None, symbol_hooked=None, symbol_so=None, syscall_number=None, target_argv=None, target_domain_name=None, target_filename=None, target_object=None, target_pid=None, target_user_name=None, task_content=None, task_name=None, threat_name=None, timestamp=None, types=None, uid=None, user=None, user_principal_name=None, username=None, virus_hit_data_list=None, _configuration=None):  # noqa: E501
        """AlarmNodeForGetMlpAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_expires = None
        self._additional_actions_string = None
        self._args_array = None
        self._argv = None
        self._bruteforce_sip = None
        self._category_name = None
        self.__class = None
        self._cmd = None
        self._comm = None
        self._command_line = None
        self._configuration = None
        self._configuration_file_hash = None
        self._connect_info = None
        self._contents = None
        self._create_at = None
        self._current_directory = None
        self._data_type = None
        self._description = None
        self._destination_hostname = None
        self._destination_ip = None
        self._destination_port = None
        self._destination_port_name = None
        self._details = None
        self._detection_user = None
        self._dip = None
        self._dport = None
        self._dst_list = None
        self._event_type = None
        self._exe = None
        self._exe_hash = None
        self._external_conns = None
        self._failure_reason = None
        self._fd_name = None
        self._file_downloadable = None
        self._file_path = None
        self._flags = None
        self._hash = None
        self._hashes = None
        self._highlight_fields = None
        self._hit_argv_list = None
        self._hit_data = None
        self._home_directory = None
        self._image = None
        self._image_loaded = None
        self._interrupt_number = None
        self._ioc_detail = None
        self._ioc_meta = None
        self._ioc_severity = None
        self._ioc_source = None
        self._ip_address = None
        self._ko_file = None
        self._ld_preload = None
        self._logon_type = None
        self._md5_hash = None
        self._mod_info = None
        self._modify_at = None
        self._module_name = None
        self._name = None
        self._new_name = None
        self._new_thread_id = None
        self._nspid = None
        self._old_name = None
        self._old_uid = None
        self._old_username = None
        self._original_file_name = None
        self._parent_command_line = None
        self._parent_image = None
        self._path = None
        self._pgid = None
        self._pgid_argv = None
        self._pid = None
        self._pid_set = None
        self._pid_tree = None
        self._ppid = None
        self._ppid_argv = None
        self._privilege_list = None
        self._probe_hook = None
        self._process_guid = None
        self._process_id = None
        self._process_name = None
        self._protocol = None
        self._ptrace_request = None
        self._query = None
        self._query_name = None
        self._query_results = None
        self._query_status = None
        self._run_path = None
        self._sam_account_name = None
        self._service_account = None
        self._service_file_name = None
        self._service_name = None
        self._service_start_type = None
        self._service_type = None
        self._severity_name = None
        self._sid = None
        self._signature = None
        self._signature_status = None
        self._sip = None
        self._socket_argv = None
        self._socket_pid = None
        self._source_ip = None
        self._source_image = None
        self._source_name = None
        self._source_port = None
        self._source_process_guid = None
        self._source_user = None
        self._sport = None
        self._src_list = None
        self._ssh = None
        self._ssh_info = None
        self._stack_trace_format = None
        self._stack_trace_hash = None
        self._start_function = None
        self._start_module = None
        self._static_file = None
        self._stdin = None
        self._stdout = None
        self._subject_user_name = None
        self._symbol_hooked = None
        self._symbol_so = None
        self._syscall_number = None
        self._target_argv = None
        self._target_domain_name = None
        self._target_filename = None
        self._target_object = None
        self._target_pid = None
        self._target_user_name = None
        self._task_content = None
        self._task_name = None
        self._threat_name = None
        self._timestamp = None
        self._types = None
        self._uid = None
        self._user = None
        self._user_principal_name = None
        self._username = None
        self._virus_hit_data_list = None
        self.discriminator = None

        if account_expires is not None:
            self.account_expires = account_expires
        if additional_actions_string is not None:
            self.additional_actions_string = additional_actions_string
        if args_array is not None:
            self.args_array = args_array
        if argv is not None:
            self.argv = argv
        if bruteforce_sip is not None:
            self.bruteforce_sip = bruteforce_sip
        if category_name is not None:
            self.category_name = category_name
        if _class is not None:
            self._class = _class
        if cmd is not None:
            self.cmd = cmd
        if comm is not None:
            self.comm = comm
        if command_line is not None:
            self.command_line = command_line
        if configuration is not None:
            self.configuration = configuration
        if configuration_file_hash is not None:
            self.configuration_file_hash = configuration_file_hash
        if connect_info is not None:
            self.connect_info = connect_info
        if contents is not None:
            self.contents = contents
        if create_at is not None:
            self.create_at = create_at
        if current_directory is not None:
            self.current_directory = current_directory
        if data_type is not None:
            self.data_type = data_type
        if description is not None:
            self.description = description
        if destination_hostname is not None:
            self.destination_hostname = destination_hostname
        if destination_ip is not None:
            self.destination_ip = destination_ip
        if destination_port is not None:
            self.destination_port = destination_port
        if destination_port_name is not None:
            self.destination_port_name = destination_port_name
        if details is not None:
            self.details = details
        if detection_user is not None:
            self.detection_user = detection_user
        if dip is not None:
            self.dip = dip
        if dport is not None:
            self.dport = dport
        if dst_list is not None:
            self.dst_list = dst_list
        if event_type is not None:
            self.event_type = event_type
        if exe is not None:
            self.exe = exe
        if exe_hash is not None:
            self.exe_hash = exe_hash
        if external_conns is not None:
            self.external_conns = external_conns
        if failure_reason is not None:
            self.failure_reason = failure_reason
        if fd_name is not None:
            self.fd_name = fd_name
        if file_downloadable is not None:
            self.file_downloadable = file_downloadable
        if file_path is not None:
            self.file_path = file_path
        if flags is not None:
            self.flags = flags
        if hash is not None:
            self.hash = hash
        if hashes is not None:
            self.hashes = hashes
        if highlight_fields is not None:
            self.highlight_fields = highlight_fields
        if hit_argv_list is not None:
            self.hit_argv_list = hit_argv_list
        if hit_data is not None:
            self.hit_data = hit_data
        if home_directory is not None:
            self.home_directory = home_directory
        if image is not None:
            self.image = image
        if image_loaded is not None:
            self.image_loaded = image_loaded
        if interrupt_number is not None:
            self.interrupt_number = interrupt_number
        if ioc_detail is not None:
            self.ioc_detail = ioc_detail
        if ioc_meta is not None:
            self.ioc_meta = ioc_meta
        if ioc_severity is not None:
            self.ioc_severity = ioc_severity
        if ioc_source is not None:
            self.ioc_source = ioc_source
        if ip_address is not None:
            self.ip_address = ip_address
        if ko_file is not None:
            self.ko_file = ko_file
        if ld_preload is not None:
            self.ld_preload = ld_preload
        if logon_type is not None:
            self.logon_type = logon_type
        if md5_hash is not None:
            self.md5_hash = md5_hash
        if mod_info is not None:
            self.mod_info = mod_info
        if modify_at is not None:
            self.modify_at = modify_at
        if module_name is not None:
            self.module_name = module_name
        if name is not None:
            self.name = name
        if new_name is not None:
            self.new_name = new_name
        if new_thread_id is not None:
            self.new_thread_id = new_thread_id
        if nspid is not None:
            self.nspid = nspid
        if old_name is not None:
            self.old_name = old_name
        if old_uid is not None:
            self.old_uid = old_uid
        if old_username is not None:
            self.old_username = old_username
        if original_file_name is not None:
            self.original_file_name = original_file_name
        if parent_command_line is not None:
            self.parent_command_line = parent_command_line
        if parent_image is not None:
            self.parent_image = parent_image
        if path is not None:
            self.path = path
        if pgid is not None:
            self.pgid = pgid
        if pgid_argv is not None:
            self.pgid_argv = pgid_argv
        if pid is not None:
            self.pid = pid
        if pid_set is not None:
            self.pid_set = pid_set
        if pid_tree is not None:
            self.pid_tree = pid_tree
        if ppid is not None:
            self.ppid = ppid
        if ppid_argv is not None:
            self.ppid_argv = ppid_argv
        if privilege_list is not None:
            self.privilege_list = privilege_list
        if probe_hook is not None:
            self.probe_hook = probe_hook
        if process_guid is not None:
            self.process_guid = process_guid
        if process_id is not None:
            self.process_id = process_id
        if process_name is not None:
            self.process_name = process_name
        if protocol is not None:
            self.protocol = protocol
        if ptrace_request is not None:
            self.ptrace_request = ptrace_request
        if query is not None:
            self.query = query
        if query_name is not None:
            self.query_name = query_name
        if query_results is not None:
            self.query_results = query_results
        if query_status is not None:
            self.query_status = query_status
        if run_path is not None:
            self.run_path = run_path
        if sam_account_name is not None:
            self.sam_account_name = sam_account_name
        if service_account is not None:
            self.service_account = service_account
        if service_file_name is not None:
            self.service_file_name = service_file_name
        if service_name is not None:
            self.service_name = service_name
        if service_start_type is not None:
            self.service_start_type = service_start_type
        if service_type is not None:
            self.service_type = service_type
        if severity_name is not None:
            self.severity_name = severity_name
        if sid is not None:
            self.sid = sid
        if signature is not None:
            self.signature = signature
        if signature_status is not None:
            self.signature_status = signature_status
        if sip is not None:
            self.sip = sip
        if socket_argv is not None:
            self.socket_argv = socket_argv
        if socket_pid is not None:
            self.socket_pid = socket_pid
        if source_ip is not None:
            self.source_ip = source_ip
        if source_image is not None:
            self.source_image = source_image
        if source_name is not None:
            self.source_name = source_name
        if source_port is not None:
            self.source_port = source_port
        if source_process_guid is not None:
            self.source_process_guid = source_process_guid
        if source_user is not None:
            self.source_user = source_user
        if sport is not None:
            self.sport = sport
        if src_list is not None:
            self.src_list = src_list
        if ssh is not None:
            self.ssh = ssh
        if ssh_info is not None:
            self.ssh_info = ssh_info
        if stack_trace_format is not None:
            self.stack_trace_format = stack_trace_format
        if stack_trace_hash is not None:
            self.stack_trace_hash = stack_trace_hash
        if start_function is not None:
            self.start_function = start_function
        if start_module is not None:
            self.start_module = start_module
        if static_file is not None:
            self.static_file = static_file
        if stdin is not None:
            self.stdin = stdin
        if stdout is not None:
            self.stdout = stdout
        if subject_user_name is not None:
            self.subject_user_name = subject_user_name
        if symbol_hooked is not None:
            self.symbol_hooked = symbol_hooked
        if symbol_so is not None:
            self.symbol_so = symbol_so
        if syscall_number is not None:
            self.syscall_number = syscall_number
        if target_argv is not None:
            self.target_argv = target_argv
        if target_domain_name is not None:
            self.target_domain_name = target_domain_name
        if target_filename is not None:
            self.target_filename = target_filename
        if target_object is not None:
            self.target_object = target_object
        if target_pid is not None:
            self.target_pid = target_pid
        if target_user_name is not None:
            self.target_user_name = target_user_name
        if task_content is not None:
            self.task_content = task_content
        if task_name is not None:
            self.task_name = task_name
        if threat_name is not None:
            self.threat_name = threat_name
        if timestamp is not None:
            self.timestamp = timestamp
        if types is not None:
            self.types = types
        if uid is not None:
            self.uid = uid
        if user is not None:
            self.user = user
        if user_principal_name is not None:
            self.user_principal_name = user_principal_name
        if username is not None:
            self.username = username
        if virus_hit_data_list is not None:
            self.virus_hit_data_list = virus_hit_data_list

    @property
    def account_expires(self):
        """Gets the account_expires of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The account_expires of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_expires

    @account_expires.setter
    def account_expires(self, account_expires):
        """Sets the account_expires of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param account_expires: The account_expires of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._account_expires = account_expires

    @property
    def additional_actions_string(self):
        """Gets the additional_actions_string of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The additional_actions_string of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._additional_actions_string

    @additional_actions_string.setter
    def additional_actions_string(self, additional_actions_string):
        """Sets the additional_actions_string of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param additional_actions_string: The additional_actions_string of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._additional_actions_string = additional_actions_string

    @property
    def args_array(self):
        """Gets the args_array of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The args_array of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._args_array

    @args_array.setter
    def args_array(self, args_array):
        """Sets the args_array of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param args_array: The args_array of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._args_array = args_array

    @property
    def argv(self):
        """Gets the argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._argv

    @argv.setter
    def argv(self, argv):
        """Sets the argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param argv: The argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._argv = argv

    @property
    def bruteforce_sip(self):
        """Gets the bruteforce_sip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The bruteforce_sip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._bruteforce_sip

    @bruteforce_sip.setter
    def bruteforce_sip(self, bruteforce_sip):
        """Sets the bruteforce_sip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param bruteforce_sip: The bruteforce_sip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._bruteforce_sip = bruteforce_sip

    @property
    def category_name(self):
        """Gets the category_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The category_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._category_name

    @category_name.setter
    def category_name(self, category_name):
        """Sets the category_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param category_name: The category_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._category_name = category_name

    @property
    def _class(self):
        """Gets the _class of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The _class of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self.__class

    @_class.setter
    def _class(self, _class):
        """Sets the _class of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param _class: The _class of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self.__class = _class

    @property
    def cmd(self):
        """Gets the cmd of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The cmd of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cmd

    @cmd.setter
    def cmd(self, cmd):
        """Sets the cmd of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param cmd: The cmd of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._cmd = cmd

    @property
    def comm(self):
        """Gets the comm of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The comm of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._comm

    @comm.setter
    def comm(self, comm):
        """Sets the comm of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param comm: The comm of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._comm = comm

    @property
    def command_line(self):
        """Gets the command_line of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The command_line of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._command_line

    @command_line.setter
    def command_line(self, command_line):
        """Sets the command_line of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param command_line: The command_line of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._command_line = command_line

    @property
    def configuration(self):
        """Gets the configuration of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The configuration of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration

    @configuration.setter
    def configuration(self, configuration):
        """Sets the configuration of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param configuration: The configuration of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._configuration = configuration

    @property
    def configuration_file_hash(self):
        """Gets the configuration_file_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The configuration_file_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_file_hash

    @configuration_file_hash.setter
    def configuration_file_hash(self, configuration_file_hash):
        """Sets the configuration_file_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param configuration_file_hash: The configuration_file_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._configuration_file_hash = configuration_file_hash

    @property
    def connect_info(self):
        """Gets the connect_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The connect_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._connect_info

    @connect_info.setter
    def connect_info(self, connect_info):
        """Sets the connect_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param connect_info: The connect_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._connect_info = connect_info

    @property
    def contents(self):
        """Gets the contents of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The contents of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._contents

    @contents.setter
    def contents(self, contents):
        """Sets the contents of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param contents: The contents of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._contents = contents

    @property
    def create_at(self):
        """Gets the create_at of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The create_at of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_at

    @create_at.setter
    def create_at(self, create_at):
        """Sets the create_at of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param create_at: The create_at of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._create_at = create_at

    @property
    def current_directory(self):
        """Gets the current_directory of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The current_directory of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._current_directory

    @current_directory.setter
    def current_directory(self, current_directory):
        """Sets the current_directory of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param current_directory: The current_directory of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._current_directory = current_directory

    @property
    def data_type(self):
        """Gets the data_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The data_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param data_type: The data_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._data_type = data_type

    @property
    def description(self):
        """Gets the description of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The description of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param description: The description of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def destination_hostname(self):
        """Gets the destination_hostname of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The destination_hostname of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination_hostname

    @destination_hostname.setter
    def destination_hostname(self, destination_hostname):
        """Sets the destination_hostname of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param destination_hostname: The destination_hostname of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._destination_hostname = destination_hostname

    @property
    def destination_ip(self):
        """Gets the destination_ip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The destination_ip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination_ip

    @destination_ip.setter
    def destination_ip(self, destination_ip):
        """Sets the destination_ip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param destination_ip: The destination_ip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._destination_ip = destination_ip

    @property
    def destination_port(self):
        """Gets the destination_port of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The destination_port of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination_port

    @destination_port.setter
    def destination_port(self, destination_port):
        """Sets the destination_port of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param destination_port: The destination_port of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._destination_port = destination_port

    @property
    def destination_port_name(self):
        """Gets the destination_port_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The destination_port_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination_port_name

    @destination_port_name.setter
    def destination_port_name(self, destination_port_name):
        """Sets the destination_port_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param destination_port_name: The destination_port_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._destination_port_name = destination_port_name

    @property
    def details(self):
        """Gets the details of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The details of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._details

    @details.setter
    def details(self, details):
        """Sets the details of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param details: The details of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._details = details

    @property
    def detection_user(self):
        """Gets the detection_user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The detection_user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._detection_user

    @detection_user.setter
    def detection_user(self, detection_user):
        """Sets the detection_user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param detection_user: The detection_user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._detection_user = detection_user

    @property
    def dip(self):
        """Gets the dip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The dip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._dip

    @dip.setter
    def dip(self, dip):
        """Sets the dip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param dip: The dip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._dip = dip

    @property
    def dport(self):
        """Gets the dport of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The dport of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._dport

    @dport.setter
    def dport(self, dport):
        """Sets the dport of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param dport: The dport of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._dport = dport

    @property
    def dst_list(self):
        """Gets the dst_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The dst_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._dst_list

    @dst_list.setter
    def dst_list(self, dst_list):
        """Sets the dst_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param dst_list: The dst_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._dst_list = dst_list

    @property
    def event_type(self):
        """Gets the event_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The event_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_type

    @event_type.setter
    def event_type(self, event_type):
        """Sets the event_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param event_type: The event_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._event_type = event_type

    @property
    def exe(self):
        """Gets the exe of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The exe of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param exe: The exe of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def exe_hash(self):
        """Gets the exe_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The exe_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe_hash

    @exe_hash.setter
    def exe_hash(self, exe_hash):
        """Sets the exe_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param exe_hash: The exe_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._exe_hash = exe_hash

    @property
    def external_conns(self):
        """Gets the external_conns of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The external_conns of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_conns

    @external_conns.setter
    def external_conns(self, external_conns):
        """Sets the external_conns of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param external_conns: The external_conns of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._external_conns = external_conns

    @property
    def failure_reason(self):
        """Gets the failure_reason of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The failure_reason of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._failure_reason

    @failure_reason.setter
    def failure_reason(self, failure_reason):
        """Sets the failure_reason of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param failure_reason: The failure_reason of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._failure_reason = failure_reason

    @property
    def fd_name(self):
        """Gets the fd_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The fd_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._fd_name

    @fd_name.setter
    def fd_name(self, fd_name):
        """Sets the fd_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param fd_name: The fd_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._fd_name = fd_name

    @property
    def file_downloadable(self):
        """Gets the file_downloadable of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The file_downloadable of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: bool
        """
        return self._file_downloadable

    @file_downloadable.setter
    def file_downloadable(self, file_downloadable):
        """Sets the file_downloadable of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param file_downloadable: The file_downloadable of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: bool
        """

        self._file_downloadable = file_downloadable

    @property
    def file_path(self):
        """Gets the file_path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The file_path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param file_path: The file_path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def flags(self):
        """Gets the flags of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The flags of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._flags

    @flags.setter
    def flags(self, flags):
        """Sets the flags of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param flags: The flags of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._flags = flags

    @property
    def hash(self):
        """Gets the hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._hash

    @hash.setter
    def hash(self, hash):
        """Sets the hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param hash: The hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._hash = hash

    @property
    def hashes(self):
        """Gets the hashes of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The hashes of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._hashes

    @hashes.setter
    def hashes(self, hashes):
        """Sets the hashes of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param hashes: The hashes of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._hashes = hashes

    @property
    def highlight_fields(self):
        """Gets the highlight_fields of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The highlight_fields of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._highlight_fields

    @highlight_fields.setter
    def highlight_fields(self, highlight_fields):
        """Sets the highlight_fields of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param highlight_fields: The highlight_fields of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._highlight_fields = highlight_fields

    @property
    def hit_argv_list(self):
        """Gets the hit_argv_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The hit_argv_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._hit_argv_list

    @hit_argv_list.setter
    def hit_argv_list(self, hit_argv_list):
        """Sets the hit_argv_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param hit_argv_list: The hit_argv_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._hit_argv_list = hit_argv_list

    @property
    def hit_data(self):
        """Gets the hit_data of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The hit_data of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._hit_data

    @hit_data.setter
    def hit_data(self, hit_data):
        """Sets the hit_data of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param hit_data: The hit_data of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._hit_data = hit_data

    @property
    def home_directory(self):
        """Gets the home_directory of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The home_directory of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._home_directory

    @home_directory.setter
    def home_directory(self, home_directory):
        """Sets the home_directory of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param home_directory: The home_directory of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._home_directory = home_directory

    @property
    def image(self):
        """Gets the image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._image

    @image.setter
    def image(self, image):
        """Sets the image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param image: The image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._image = image

    @property
    def image_loaded(self):
        """Gets the image_loaded of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The image_loaded of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_loaded

    @image_loaded.setter
    def image_loaded(self, image_loaded):
        """Sets the image_loaded of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param image_loaded: The image_loaded of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._image_loaded = image_loaded

    @property
    def interrupt_number(self):
        """Gets the interrupt_number of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The interrupt_number of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._interrupt_number

    @interrupt_number.setter
    def interrupt_number(self, interrupt_number):
        """Sets the interrupt_number of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param interrupt_number: The interrupt_number of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._interrupt_number = interrupt_number

    @property
    def ioc_detail(self):
        """Gets the ioc_detail of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ioc_detail of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ioc_detail

    @ioc_detail.setter
    def ioc_detail(self, ioc_detail):
        """Sets the ioc_detail of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ioc_detail: The ioc_detail of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ioc_detail = ioc_detail

    @property
    def ioc_meta(self):
        """Gets the ioc_meta of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ioc_meta of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ioc_meta

    @ioc_meta.setter
    def ioc_meta(self, ioc_meta):
        """Sets the ioc_meta of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ioc_meta: The ioc_meta of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ioc_meta = ioc_meta

    @property
    def ioc_severity(self):
        """Gets the ioc_severity of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ioc_severity of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ioc_severity

    @ioc_severity.setter
    def ioc_severity(self, ioc_severity):
        """Sets the ioc_severity of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ioc_severity: The ioc_severity of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ioc_severity = ioc_severity

    @property
    def ioc_source(self):
        """Gets the ioc_source of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ioc_source of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ioc_source

    @ioc_source.setter
    def ioc_source(self, ioc_source):
        """Sets the ioc_source of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ioc_source: The ioc_source of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ioc_source = ioc_source

    @property
    def ip_address(self):
        """Gets the ip_address of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ip_address of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip_address

    @ip_address.setter
    def ip_address(self, ip_address):
        """Sets the ip_address of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ip_address: The ip_address of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ip_address = ip_address

    @property
    def ko_file(self):
        """Gets the ko_file of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ko_file of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ko_file

    @ko_file.setter
    def ko_file(self, ko_file):
        """Sets the ko_file of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ko_file: The ko_file of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ko_file = ko_file

    @property
    def ld_preload(self):
        """Gets the ld_preload of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ld_preload of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ld_preload

    @ld_preload.setter
    def ld_preload(self, ld_preload):
        """Sets the ld_preload of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ld_preload: The ld_preload of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ld_preload = ld_preload

    @property
    def logon_type(self):
        """Gets the logon_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The logon_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._logon_type

    @logon_type.setter
    def logon_type(self, logon_type):
        """Sets the logon_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param logon_type: The logon_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._logon_type = logon_type

    @property
    def md5_hash(self):
        """Gets the md5_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The md5_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._md5_hash

    @md5_hash.setter
    def md5_hash(self, md5_hash):
        """Sets the md5_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param md5_hash: The md5_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._md5_hash = md5_hash

    @property
    def mod_info(self):
        """Gets the mod_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The mod_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._mod_info

    @mod_info.setter
    def mod_info(self, mod_info):
        """Sets the mod_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param mod_info: The mod_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._mod_info = mod_info

    @property
    def modify_at(self):
        """Gets the modify_at of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The modify_at of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._modify_at

    @modify_at.setter
    def modify_at(self, modify_at):
        """Sets the modify_at of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param modify_at: The modify_at of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._modify_at = modify_at

    @property
    def module_name(self):
        """Gets the module_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The module_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._module_name

    @module_name.setter
    def module_name(self, module_name):
        """Sets the module_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param module_name: The module_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._module_name = module_name

    @property
    def name(self):
        """Gets the name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param name: The name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def new_name(self):
        """Gets the new_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The new_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._new_name

    @new_name.setter
    def new_name(self, new_name):
        """Sets the new_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param new_name: The new_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._new_name = new_name

    @property
    def new_thread_id(self):
        """Gets the new_thread_id of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The new_thread_id of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._new_thread_id

    @new_thread_id.setter
    def new_thread_id(self, new_thread_id):
        """Sets the new_thread_id of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param new_thread_id: The new_thread_id of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._new_thread_id = new_thread_id

    @property
    def nspid(self):
        """Gets the nspid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The nspid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._nspid

    @nspid.setter
    def nspid(self, nspid):
        """Sets the nspid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param nspid: The nspid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._nspid = nspid

    @property
    def old_name(self):
        """Gets the old_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The old_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._old_name

    @old_name.setter
    def old_name(self, old_name):
        """Sets the old_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param old_name: The old_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._old_name = old_name

    @property
    def old_uid(self):
        """Gets the old_uid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The old_uid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._old_uid

    @old_uid.setter
    def old_uid(self, old_uid):
        """Sets the old_uid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param old_uid: The old_uid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._old_uid = old_uid

    @property
    def old_username(self):
        """Gets the old_username of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The old_username of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._old_username

    @old_username.setter
    def old_username(self, old_username):
        """Sets the old_username of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param old_username: The old_username of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._old_username = old_username

    @property
    def original_file_name(self):
        """Gets the original_file_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The original_file_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._original_file_name

    @original_file_name.setter
    def original_file_name(self, original_file_name):
        """Sets the original_file_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param original_file_name: The original_file_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._original_file_name = original_file_name

    @property
    def parent_command_line(self):
        """Gets the parent_command_line of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The parent_command_line of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._parent_command_line

    @parent_command_line.setter
    def parent_command_line(self, parent_command_line):
        """Sets the parent_command_line of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param parent_command_line: The parent_command_line of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._parent_command_line = parent_command_line

    @property
    def parent_image(self):
        """Gets the parent_image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The parent_image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._parent_image

    @parent_image.setter
    def parent_image(self, parent_image):
        """Sets the parent_image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param parent_image: The parent_image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._parent_image = parent_image

    @property
    def path(self):
        """Gets the path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param path: The path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def pgid(self):
        """Gets the pgid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pgid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pgid

    @pgid.setter
    def pgid(self, pgid):
        """Sets the pgid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param pgid: The pgid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pgid = pgid

    @property
    def pgid_argv(self):
        """Gets the pgid_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pgid_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pgid_argv

    @pgid_argv.setter
    def pgid_argv(self, pgid_argv):
        """Sets the pgid_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param pgid_argv: The pgid_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pgid_argv = pgid_argv

    @property
    def pid(self):
        """Gets the pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param pid: The pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def pid_set(self):
        """Gets the pid_set of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pid_set of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid_set

    @pid_set.setter
    def pid_set(self, pid_set):
        """Sets the pid_set of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param pid_set: The pid_set of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pid_set = pid_set

    @property
    def pid_tree(self):
        """Gets the pid_tree of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The pid_tree of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid_tree

    @pid_tree.setter
    def pid_tree(self, pid_tree):
        """Sets the pid_tree of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param pid_tree: The pid_tree of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._pid_tree = pid_tree

    @property
    def ppid(self):
        """Gets the ppid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ppid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ppid

    @ppid.setter
    def ppid(self, ppid):
        """Sets the ppid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ppid: The ppid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ppid = ppid

    @property
    def ppid_argv(self):
        """Gets the ppid_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ppid_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ppid_argv

    @ppid_argv.setter
    def ppid_argv(self, ppid_argv):
        """Sets the ppid_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ppid_argv: The ppid_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ppid_argv = ppid_argv

    @property
    def privilege_list(self):
        """Gets the privilege_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The privilege_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._privilege_list

    @privilege_list.setter
    def privilege_list(self, privilege_list):
        """Sets the privilege_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param privilege_list: The privilege_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._privilege_list = privilege_list

    @property
    def probe_hook(self):
        """Gets the probe_hook of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The probe_hook of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._probe_hook

    @probe_hook.setter
    def probe_hook(self, probe_hook):
        """Sets the probe_hook of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param probe_hook: The probe_hook of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._probe_hook = probe_hook

    @property
    def process_guid(self):
        """Gets the process_guid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The process_guid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._process_guid

    @process_guid.setter
    def process_guid(self, process_guid):
        """Sets the process_guid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param process_guid: The process_guid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._process_guid = process_guid

    @property
    def process_id(self):
        """Gets the process_id of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The process_id of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._process_id

    @process_id.setter
    def process_id(self, process_id):
        """Sets the process_id of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param process_id: The process_id of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._process_id = process_id

    @property
    def process_name(self):
        """Gets the process_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The process_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._process_name

    @process_name.setter
    def process_name(self, process_name):
        """Sets the process_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param process_name: The process_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._process_name = process_name

    @property
    def protocol(self):
        """Gets the protocol of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The protocol of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param protocol: The protocol of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def ptrace_request(self):
        """Gets the ptrace_request of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ptrace_request of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ptrace_request

    @ptrace_request.setter
    def ptrace_request(self, ptrace_request):
        """Sets the ptrace_request of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ptrace_request: The ptrace_request of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ptrace_request = ptrace_request

    @property
    def query(self):
        """Gets the query of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The query of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._query

    @query.setter
    def query(self, query):
        """Sets the query of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param query: The query of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._query = query

    @property
    def query_name(self):
        """Gets the query_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The query_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._query_name

    @query_name.setter
    def query_name(self, query_name):
        """Sets the query_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param query_name: The query_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._query_name = query_name

    @property
    def query_results(self):
        """Gets the query_results of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The query_results of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._query_results

    @query_results.setter
    def query_results(self, query_results):
        """Sets the query_results of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param query_results: The query_results of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._query_results = query_results

    @property
    def query_status(self):
        """Gets the query_status of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The query_status of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._query_status

    @query_status.setter
    def query_status(self, query_status):
        """Sets the query_status of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param query_status: The query_status of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._query_status = query_status

    @property
    def run_path(self):
        """Gets the run_path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The run_path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._run_path

    @run_path.setter
    def run_path(self, run_path):
        """Sets the run_path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param run_path: The run_path of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._run_path = run_path

    @property
    def sam_account_name(self):
        """Gets the sam_account_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The sam_account_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._sam_account_name

    @sam_account_name.setter
    def sam_account_name(self, sam_account_name):
        """Sets the sam_account_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param sam_account_name: The sam_account_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._sam_account_name = sam_account_name

    @property
    def service_account(self):
        """Gets the service_account of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The service_account of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_account

    @service_account.setter
    def service_account(self, service_account):
        """Sets the service_account of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param service_account: The service_account of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._service_account = service_account

    @property
    def service_file_name(self):
        """Gets the service_file_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The service_file_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_file_name

    @service_file_name.setter
    def service_file_name(self, service_file_name):
        """Sets the service_file_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param service_file_name: The service_file_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._service_file_name = service_file_name

    @property
    def service_name(self):
        """Gets the service_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The service_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param service_name: The service_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._service_name = service_name

    @property
    def service_start_type(self):
        """Gets the service_start_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The service_start_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_start_type

    @service_start_type.setter
    def service_start_type(self, service_start_type):
        """Sets the service_start_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param service_start_type: The service_start_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._service_start_type = service_start_type

    @property
    def service_type(self):
        """Gets the service_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The service_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_type

    @service_type.setter
    def service_type(self, service_type):
        """Sets the service_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param service_type: The service_type of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._service_type = service_type

    @property
    def severity_name(self):
        """Gets the severity_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The severity_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._severity_name

    @severity_name.setter
    def severity_name(self, severity_name):
        """Sets the severity_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param severity_name: The severity_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._severity_name = severity_name

    @property
    def sid(self):
        """Gets the sid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The sid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._sid

    @sid.setter
    def sid(self, sid):
        """Sets the sid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param sid: The sid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._sid = sid

    @property
    def signature(self):
        """Gets the signature of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The signature of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._signature

    @signature.setter
    def signature(self, signature):
        """Sets the signature of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param signature: The signature of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._signature = signature

    @property
    def signature_status(self):
        """Gets the signature_status of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The signature_status of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._signature_status

    @signature_status.setter
    def signature_status(self, signature_status):
        """Sets the signature_status of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param signature_status: The signature_status of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._signature_status = signature_status

    @property
    def sip(self):
        """Gets the sip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The sip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._sip

    @sip.setter
    def sip(self, sip):
        """Sets the sip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param sip: The sip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._sip = sip

    @property
    def socket_argv(self):
        """Gets the socket_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The socket_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._socket_argv

    @socket_argv.setter
    def socket_argv(self, socket_argv):
        """Sets the socket_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param socket_argv: The socket_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._socket_argv = socket_argv

    @property
    def socket_pid(self):
        """Gets the socket_pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The socket_pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._socket_pid

    @socket_pid.setter
    def socket_pid(self, socket_pid):
        """Sets the socket_pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param socket_pid: The socket_pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._socket_pid = socket_pid

    @property
    def source_ip(self):
        """Gets the source_ip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The source_ip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_ip

    @source_ip.setter
    def source_ip(self, source_ip):
        """Sets the source_ip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param source_ip: The source_ip of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._source_ip = source_ip

    @property
    def source_image(self):
        """Gets the source_image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The source_image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_image

    @source_image.setter
    def source_image(self, source_image):
        """Sets the source_image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param source_image: The source_image of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._source_image = source_image

    @property
    def source_name(self):
        """Gets the source_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The source_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_name

    @source_name.setter
    def source_name(self, source_name):
        """Sets the source_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param source_name: The source_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._source_name = source_name

    @property
    def source_port(self):
        """Gets the source_port of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The source_port of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_port

    @source_port.setter
    def source_port(self, source_port):
        """Sets the source_port of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param source_port: The source_port of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._source_port = source_port

    @property
    def source_process_guid(self):
        """Gets the source_process_guid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The source_process_guid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_process_guid

    @source_process_guid.setter
    def source_process_guid(self, source_process_guid):
        """Sets the source_process_guid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param source_process_guid: The source_process_guid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._source_process_guid = source_process_guid

    @property
    def source_user(self):
        """Gets the source_user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The source_user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_user

    @source_user.setter
    def source_user(self, source_user):
        """Sets the source_user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param source_user: The source_user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._source_user = source_user

    @property
    def sport(self):
        """Gets the sport of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The sport of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._sport

    @sport.setter
    def sport(self, sport):
        """Sets the sport of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param sport: The sport of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._sport = sport

    @property
    def src_list(self):
        """Gets the src_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The src_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._src_list

    @src_list.setter
    def src_list(self, src_list):
        """Sets the src_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param src_list: The src_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._src_list = src_list

    @property
    def ssh(self):
        """Gets the ssh of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ssh of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ssh

    @ssh.setter
    def ssh(self, ssh):
        """Sets the ssh of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ssh: The ssh of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ssh = ssh

    @property
    def ssh_info(self):
        """Gets the ssh_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The ssh_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._ssh_info

    @ssh_info.setter
    def ssh_info(self, ssh_info):
        """Sets the ssh_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param ssh_info: The ssh_info of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._ssh_info = ssh_info

    @property
    def stack_trace_format(self):
        """Gets the stack_trace_format of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The stack_trace_format of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._stack_trace_format

    @stack_trace_format.setter
    def stack_trace_format(self, stack_trace_format):
        """Sets the stack_trace_format of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param stack_trace_format: The stack_trace_format of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._stack_trace_format = stack_trace_format

    @property
    def stack_trace_hash(self):
        """Gets the stack_trace_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The stack_trace_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._stack_trace_hash

    @stack_trace_hash.setter
    def stack_trace_hash(self, stack_trace_hash):
        """Sets the stack_trace_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param stack_trace_hash: The stack_trace_hash of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._stack_trace_hash = stack_trace_hash

    @property
    def start_function(self):
        """Gets the start_function of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The start_function of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_function

    @start_function.setter
    def start_function(self, start_function):
        """Sets the start_function of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param start_function: The start_function of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._start_function = start_function

    @property
    def start_module(self):
        """Gets the start_module of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The start_module of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_module

    @start_module.setter
    def start_module(self, start_module):
        """Sets the start_module of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param start_module: The start_module of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._start_module = start_module

    @property
    def static_file(self):
        """Gets the static_file of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The static_file of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._static_file

    @static_file.setter
    def static_file(self, static_file):
        """Sets the static_file of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param static_file: The static_file of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._static_file = static_file

    @property
    def stdin(self):
        """Gets the stdin of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The stdin of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._stdin

    @stdin.setter
    def stdin(self, stdin):
        """Sets the stdin of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param stdin: The stdin of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._stdin = stdin

    @property
    def stdout(self):
        """Gets the stdout of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The stdout of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._stdout

    @stdout.setter
    def stdout(self, stdout):
        """Sets the stdout of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param stdout: The stdout of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._stdout = stdout

    @property
    def subject_user_name(self):
        """Gets the subject_user_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The subject_user_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject_user_name

    @subject_user_name.setter
    def subject_user_name(self, subject_user_name):
        """Sets the subject_user_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param subject_user_name: The subject_user_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._subject_user_name = subject_user_name

    @property
    def symbol_hooked(self):
        """Gets the symbol_hooked of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The symbol_hooked of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._symbol_hooked

    @symbol_hooked.setter
    def symbol_hooked(self, symbol_hooked):
        """Sets the symbol_hooked of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param symbol_hooked: The symbol_hooked of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._symbol_hooked = symbol_hooked

    @property
    def symbol_so(self):
        """Gets the symbol_so of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The symbol_so of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._symbol_so

    @symbol_so.setter
    def symbol_so(self, symbol_so):
        """Sets the symbol_so of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param symbol_so: The symbol_so of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._symbol_so = symbol_so

    @property
    def syscall_number(self):
        """Gets the syscall_number of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The syscall_number of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._syscall_number

    @syscall_number.setter
    def syscall_number(self, syscall_number):
        """Sets the syscall_number of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param syscall_number: The syscall_number of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._syscall_number = syscall_number

    @property
    def target_argv(self):
        """Gets the target_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The target_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_argv

    @target_argv.setter
    def target_argv(self, target_argv):
        """Sets the target_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param target_argv: The target_argv of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._target_argv = target_argv

    @property
    def target_domain_name(self):
        """Gets the target_domain_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The target_domain_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_domain_name

    @target_domain_name.setter
    def target_domain_name(self, target_domain_name):
        """Sets the target_domain_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param target_domain_name: The target_domain_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._target_domain_name = target_domain_name

    @property
    def target_filename(self):
        """Gets the target_filename of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The target_filename of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_filename

    @target_filename.setter
    def target_filename(self, target_filename):
        """Sets the target_filename of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param target_filename: The target_filename of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._target_filename = target_filename

    @property
    def target_object(self):
        """Gets the target_object of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The target_object of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_object

    @target_object.setter
    def target_object(self, target_object):
        """Sets the target_object of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param target_object: The target_object of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._target_object = target_object

    @property
    def target_pid(self):
        """Gets the target_pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The target_pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_pid

    @target_pid.setter
    def target_pid(self, target_pid):
        """Sets the target_pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param target_pid: The target_pid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._target_pid = target_pid

    @property
    def target_user_name(self):
        """Gets the target_user_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The target_user_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._target_user_name

    @target_user_name.setter
    def target_user_name(self, target_user_name):
        """Sets the target_user_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param target_user_name: The target_user_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._target_user_name = target_user_name

    @property
    def task_content(self):
        """Gets the task_content of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The task_content of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_content

    @task_content.setter
    def task_content(self, task_content):
        """Sets the task_content of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param task_content: The task_content of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._task_content = task_content

    @property
    def task_name(self):
        """Gets the task_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The task_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_name

    @task_name.setter
    def task_name(self, task_name):
        """Sets the task_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param task_name: The task_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._task_name = task_name

    @property
    def threat_name(self):
        """Gets the threat_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The threat_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._threat_name

    @threat_name.setter
    def threat_name(self, threat_name):
        """Sets the threat_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param threat_name: The threat_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._threat_name = threat_name

    @property
    def timestamp(self):
        """Gets the timestamp of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The timestamp of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param timestamp: The timestamp of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._timestamp = timestamp

    @property
    def types(self):
        """Gets the types of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The types of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._types

    @types.setter
    def types(self, types):
        """Sets the types of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param types: The types of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._types = types

    @property
    def uid(self):
        """Gets the uid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The uid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param uid: The uid of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    @property
    def user(self):
        """Gets the user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user

    @user.setter
    def user(self, user):
        """Sets the user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param user: The user of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._user = user

    @property
    def user_principal_name(self):
        """Gets the user_principal_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The user_principal_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_principal_name

    @user_principal_name.setter
    def user_principal_name(self, user_principal_name):
        """Sets the user_principal_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param user_principal_name: The user_principal_name of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._user_principal_name = user_principal_name

    @property
    def username(self):
        """Gets the username of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The username of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param username: The username of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._username = username

    @property
    def virus_hit_data_list(self):
        """Gets the virus_hit_data_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501


        :return: The virus_hit_data_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[VirusHitDataListForGetMlpAlarmSummaryInfoOutput]
        """
        return self._virus_hit_data_list

    @virus_hit_data_list.setter
    def virus_hit_data_list(self, virus_hit_data_list):
        """Sets the virus_hit_data_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.


        :param virus_hit_data_list: The virus_hit_data_list of this AlarmNodeForGetMlpAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[VirusHitDataListForGetMlpAlarmSummaryInfoOutput]
        """

        self._virus_hit_data_list = virus_hit_data_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AlarmNodeForGetMlpAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AlarmNodeForGetMlpAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AlarmNodeForGetMlpAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
