# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ObservableConfigForCreateJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_metric_service': 'CustomMetricServiceForCreateJobInput',
        'dashboard_private_url': 'str',
        'dashboard_public_url': 'str',
        'tensorboard_storage': 'TensorboardStorageForCreateJobInput'
    }

    attribute_map = {
        'custom_metric_service': 'CustomMetricService',
        'dashboard_private_url': 'DashboardPrivateUrl',
        'dashboard_public_url': 'DashboardPublicUrl',
        'tensorboard_storage': 'TensorboardStorage'
    }

    def __init__(self, custom_metric_service=None, dashboard_private_url=None, dashboard_public_url=None, tensorboard_storage=None, _configuration=None):  # noqa: E501
        """ObservableConfigForCreateJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_metric_service = None
        self._dashboard_private_url = None
        self._dashboard_public_url = None
        self._tensorboard_storage = None
        self.discriminator = None

        if custom_metric_service is not None:
            self.custom_metric_service = custom_metric_service
        if dashboard_private_url is not None:
            self.dashboard_private_url = dashboard_private_url
        if dashboard_public_url is not None:
            self.dashboard_public_url = dashboard_public_url
        if tensorboard_storage is not None:
            self.tensorboard_storage = tensorboard_storage

    @property
    def custom_metric_service(self):
        """Gets the custom_metric_service of this ObservableConfigForCreateJobInput.  # noqa: E501


        :return: The custom_metric_service of this ObservableConfigForCreateJobInput.  # noqa: E501
        :rtype: CustomMetricServiceForCreateJobInput
        """
        return self._custom_metric_service

    @custom_metric_service.setter
    def custom_metric_service(self, custom_metric_service):
        """Sets the custom_metric_service of this ObservableConfigForCreateJobInput.


        :param custom_metric_service: The custom_metric_service of this ObservableConfigForCreateJobInput.  # noqa: E501
        :type: CustomMetricServiceForCreateJobInput
        """

        self._custom_metric_service = custom_metric_service

    @property
    def dashboard_private_url(self):
        """Gets the dashboard_private_url of this ObservableConfigForCreateJobInput.  # noqa: E501


        :return: The dashboard_private_url of this ObservableConfigForCreateJobInput.  # noqa: E501
        :rtype: str
        """
        return self._dashboard_private_url

    @dashboard_private_url.setter
    def dashboard_private_url(self, dashboard_private_url):
        """Sets the dashboard_private_url of this ObservableConfigForCreateJobInput.


        :param dashboard_private_url: The dashboard_private_url of this ObservableConfigForCreateJobInput.  # noqa: E501
        :type: str
        """

        self._dashboard_private_url = dashboard_private_url

    @property
    def dashboard_public_url(self):
        """Gets the dashboard_public_url of this ObservableConfigForCreateJobInput.  # noqa: E501


        :return: The dashboard_public_url of this ObservableConfigForCreateJobInput.  # noqa: E501
        :rtype: str
        """
        return self._dashboard_public_url

    @dashboard_public_url.setter
    def dashboard_public_url(self, dashboard_public_url):
        """Sets the dashboard_public_url of this ObservableConfigForCreateJobInput.


        :param dashboard_public_url: The dashboard_public_url of this ObservableConfigForCreateJobInput.  # noqa: E501
        :type: str
        """

        self._dashboard_public_url = dashboard_public_url

    @property
    def tensorboard_storage(self):
        """Gets the tensorboard_storage of this ObservableConfigForCreateJobInput.  # noqa: E501


        :return: The tensorboard_storage of this ObservableConfigForCreateJobInput.  # noqa: E501
        :rtype: TensorboardStorageForCreateJobInput
        """
        return self._tensorboard_storage

    @tensorboard_storage.setter
    def tensorboard_storage(self, tensorboard_storage):
        """Sets the tensorboard_storage of this ObservableConfigForCreateJobInput.


        :param tensorboard_storage: The tensorboard_storage of this ObservableConfigForCreateJobInput.  # noqa: E501
        :type: TensorboardStorageForCreateJobInput
        """

        self._tensorboard_storage = tensorboard_storage

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ObservableConfigForCreateJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ObservableConfigForCreateJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ObservableConfigForCreateJobInput):
            return True

        return self.to_dict() != other.to_dict()
