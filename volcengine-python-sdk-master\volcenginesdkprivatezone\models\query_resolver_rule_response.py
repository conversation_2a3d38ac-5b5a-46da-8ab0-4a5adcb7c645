# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryResolverRuleResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bind_vpcs': 'list[BindVPCForQueryResolverRuleOutput]',
        'created_at': 'str',
        'enable': 'bool',
        'endpoint_id': 'int',
        'forward_ips': 'list[ForwardIPForQueryResolverRuleOutput]',
        'id': 'int',
        'last_operator': 'str',
        'line': 'str',
        'name': 'str',
        'project_name': 'str',
        'tags': 'list[TagForQueryResolverRuleOutput]',
        'type': 'str',
        'updated_at': 'str',
        'zone_name': 'str'
    }

    attribute_map = {
        'bind_vpcs': 'BindVPCs',
        'created_at': 'CreatedAt',
        'enable': 'Enable',
        'endpoint_id': 'EndpointID',
        'forward_ips': 'ForwardIPs',
        'id': 'ID',
        'last_operator': 'LastOperator',
        'line': 'Line',
        'name': 'Name',
        'project_name': 'ProjectName',
        'tags': 'Tags',
        'type': 'Type',
        'updated_at': 'UpdatedAt',
        'zone_name': 'ZoneName'
    }

    def __init__(self, bind_vpcs=None, created_at=None, enable=None, endpoint_id=None, forward_ips=None, id=None, last_operator=None, line=None, name=None, project_name=None, tags=None, type=None, updated_at=None, zone_name=None, _configuration=None):  # noqa: E501
        """QueryResolverRuleResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bind_vpcs = None
        self._created_at = None
        self._enable = None
        self._endpoint_id = None
        self._forward_ips = None
        self._id = None
        self._last_operator = None
        self._line = None
        self._name = None
        self._project_name = None
        self._tags = None
        self._type = None
        self._updated_at = None
        self._zone_name = None
        self.discriminator = None

        if bind_vpcs is not None:
            self.bind_vpcs = bind_vpcs
        if created_at is not None:
            self.created_at = created_at
        if enable is not None:
            self.enable = enable
        if endpoint_id is not None:
            self.endpoint_id = endpoint_id
        if forward_ips is not None:
            self.forward_ips = forward_ips
        if id is not None:
            self.id = id
        if last_operator is not None:
            self.last_operator = last_operator
        if line is not None:
            self.line = line
        if name is not None:
            self.name = name
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags
        if type is not None:
            self.type = type
        if updated_at is not None:
            self.updated_at = updated_at
        if zone_name is not None:
            self.zone_name = zone_name

    @property
    def bind_vpcs(self):
        """Gets the bind_vpcs of this QueryResolverRuleResponse.  # noqa: E501


        :return: The bind_vpcs of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: list[BindVPCForQueryResolverRuleOutput]
        """
        return self._bind_vpcs

    @bind_vpcs.setter
    def bind_vpcs(self, bind_vpcs):
        """Sets the bind_vpcs of this QueryResolverRuleResponse.


        :param bind_vpcs: The bind_vpcs of this QueryResolverRuleResponse.  # noqa: E501
        :type: list[BindVPCForQueryResolverRuleOutput]
        """

        self._bind_vpcs = bind_vpcs

    @property
    def created_at(self):
        """Gets the created_at of this QueryResolverRuleResponse.  # noqa: E501


        :return: The created_at of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this QueryResolverRuleResponse.


        :param created_at: The created_at of this QueryResolverRuleResponse.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def enable(self):
        """Gets the enable of this QueryResolverRuleResponse.  # noqa: E501


        :return: The enable of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this QueryResolverRuleResponse.


        :param enable: The enable of this QueryResolverRuleResponse.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this QueryResolverRuleResponse.  # noqa: E501


        :return: The endpoint_id of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: int
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this QueryResolverRuleResponse.


        :param endpoint_id: The endpoint_id of this QueryResolverRuleResponse.  # noqa: E501
        :type: int
        """

        self._endpoint_id = endpoint_id

    @property
    def forward_ips(self):
        """Gets the forward_ips of this QueryResolverRuleResponse.  # noqa: E501


        :return: The forward_ips of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: list[ForwardIPForQueryResolverRuleOutput]
        """
        return self._forward_ips

    @forward_ips.setter
    def forward_ips(self, forward_ips):
        """Sets the forward_ips of this QueryResolverRuleResponse.


        :param forward_ips: The forward_ips of this QueryResolverRuleResponse.  # noqa: E501
        :type: list[ForwardIPForQueryResolverRuleOutput]
        """

        self._forward_ips = forward_ips

    @property
    def id(self):
        """Gets the id of this QueryResolverRuleResponse.  # noqa: E501


        :return: The id of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this QueryResolverRuleResponse.


        :param id: The id of this QueryResolverRuleResponse.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def last_operator(self):
        """Gets the last_operator of this QueryResolverRuleResponse.  # noqa: E501


        :return: The last_operator of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: str
        """
        return self._last_operator

    @last_operator.setter
    def last_operator(self, last_operator):
        """Sets the last_operator of this QueryResolverRuleResponse.


        :param last_operator: The last_operator of this QueryResolverRuleResponse.  # noqa: E501
        :type: str
        """

        self._last_operator = last_operator

    @property
    def line(self):
        """Gets the line of this QueryResolverRuleResponse.  # noqa: E501


        :return: The line of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: str
        """
        return self._line

    @line.setter
    def line(self, line):
        """Sets the line of this QueryResolverRuleResponse.


        :param line: The line of this QueryResolverRuleResponse.  # noqa: E501
        :type: str
        """

        self._line = line

    @property
    def name(self):
        """Gets the name of this QueryResolverRuleResponse.  # noqa: E501


        :return: The name of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this QueryResolverRuleResponse.


        :param name: The name of this QueryResolverRuleResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this QueryResolverRuleResponse.  # noqa: E501


        :return: The project_name of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this QueryResolverRuleResponse.


        :param project_name: The project_name of this QueryResolverRuleResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this QueryResolverRuleResponse.  # noqa: E501


        :return: The tags of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: list[TagForQueryResolverRuleOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this QueryResolverRuleResponse.


        :param tags: The tags of this QueryResolverRuleResponse.  # noqa: E501
        :type: list[TagForQueryResolverRuleOutput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this QueryResolverRuleResponse.  # noqa: E501


        :return: The type of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this QueryResolverRuleResponse.


        :param type: The type of this QueryResolverRuleResponse.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def updated_at(self):
        """Gets the updated_at of this QueryResolverRuleResponse.  # noqa: E501


        :return: The updated_at of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this QueryResolverRuleResponse.


        :param updated_at: The updated_at of this QueryResolverRuleResponse.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def zone_name(self):
        """Gets the zone_name of this QueryResolverRuleResponse.  # noqa: E501


        :return: The zone_name of this QueryResolverRuleResponse.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this QueryResolverRuleResponse.


        :param zone_name: The zone_name of this QueryResolverRuleResponse.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryResolverRuleResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryResolverRuleResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryResolverRuleResponse):
            return True

        return self.to_dict() != other.to_dict()
