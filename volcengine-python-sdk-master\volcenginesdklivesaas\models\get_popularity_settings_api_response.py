# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetPopularitySettingsAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_virtual_popularity_enable': 'int',
        'virtual_people_increase_max': 'int',
        'virtual_people_increase_min': 'int',
        'virtual_people_reduce_max': 'int',
        'virtual_people_reduce_min': 'int',
        'virtual_popularity': 'int'
    }

    attribute_map = {
        'is_virtual_popularity_enable': 'IsVirtualPopularityEnable',
        'virtual_people_increase_max': 'VirtualPeopleIncreaseMax',
        'virtual_people_increase_min': 'VirtualPeopleIncreaseMin',
        'virtual_people_reduce_max': 'VirtualPeopleReduceMax',
        'virtual_people_reduce_min': 'VirtualPeopleReduceMin',
        'virtual_popularity': 'VirtualPopularity'
    }

    def __init__(self, is_virtual_popularity_enable=None, virtual_people_increase_max=None, virtual_people_increase_min=None, virtual_people_reduce_max=None, virtual_people_reduce_min=None, virtual_popularity=None, _configuration=None):  # noqa: E501
        """GetPopularitySettingsAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_virtual_popularity_enable = None
        self._virtual_people_increase_max = None
        self._virtual_people_increase_min = None
        self._virtual_people_reduce_max = None
        self._virtual_people_reduce_min = None
        self._virtual_popularity = None
        self.discriminator = None

        if is_virtual_popularity_enable is not None:
            self.is_virtual_popularity_enable = is_virtual_popularity_enable
        if virtual_people_increase_max is not None:
            self.virtual_people_increase_max = virtual_people_increase_max
        if virtual_people_increase_min is not None:
            self.virtual_people_increase_min = virtual_people_increase_min
        if virtual_people_reduce_max is not None:
            self.virtual_people_reduce_max = virtual_people_reduce_max
        if virtual_people_reduce_min is not None:
            self.virtual_people_reduce_min = virtual_people_reduce_min
        if virtual_popularity is not None:
            self.virtual_popularity = virtual_popularity

    @property
    def is_virtual_popularity_enable(self):
        """Gets the is_virtual_popularity_enable of this GetPopularitySettingsAPIResponse.  # noqa: E501


        :return: The is_virtual_popularity_enable of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._is_virtual_popularity_enable

    @is_virtual_popularity_enable.setter
    def is_virtual_popularity_enable(self, is_virtual_popularity_enable):
        """Sets the is_virtual_popularity_enable of this GetPopularitySettingsAPIResponse.


        :param is_virtual_popularity_enable: The is_virtual_popularity_enable of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :type: int
        """

        self._is_virtual_popularity_enable = is_virtual_popularity_enable

    @property
    def virtual_people_increase_max(self):
        """Gets the virtual_people_increase_max of this GetPopularitySettingsAPIResponse.  # noqa: E501


        :return: The virtual_people_increase_max of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._virtual_people_increase_max

    @virtual_people_increase_max.setter
    def virtual_people_increase_max(self, virtual_people_increase_max):
        """Sets the virtual_people_increase_max of this GetPopularitySettingsAPIResponse.


        :param virtual_people_increase_max: The virtual_people_increase_max of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :type: int
        """

        self._virtual_people_increase_max = virtual_people_increase_max

    @property
    def virtual_people_increase_min(self):
        """Gets the virtual_people_increase_min of this GetPopularitySettingsAPIResponse.  # noqa: E501


        :return: The virtual_people_increase_min of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._virtual_people_increase_min

    @virtual_people_increase_min.setter
    def virtual_people_increase_min(self, virtual_people_increase_min):
        """Sets the virtual_people_increase_min of this GetPopularitySettingsAPIResponse.


        :param virtual_people_increase_min: The virtual_people_increase_min of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :type: int
        """

        self._virtual_people_increase_min = virtual_people_increase_min

    @property
    def virtual_people_reduce_max(self):
        """Gets the virtual_people_reduce_max of this GetPopularitySettingsAPIResponse.  # noqa: E501


        :return: The virtual_people_reduce_max of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._virtual_people_reduce_max

    @virtual_people_reduce_max.setter
    def virtual_people_reduce_max(self, virtual_people_reduce_max):
        """Sets the virtual_people_reduce_max of this GetPopularitySettingsAPIResponse.


        :param virtual_people_reduce_max: The virtual_people_reduce_max of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :type: int
        """

        self._virtual_people_reduce_max = virtual_people_reduce_max

    @property
    def virtual_people_reduce_min(self):
        """Gets the virtual_people_reduce_min of this GetPopularitySettingsAPIResponse.  # noqa: E501


        :return: The virtual_people_reduce_min of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._virtual_people_reduce_min

    @virtual_people_reduce_min.setter
    def virtual_people_reduce_min(self, virtual_people_reduce_min):
        """Sets the virtual_people_reduce_min of this GetPopularitySettingsAPIResponse.


        :param virtual_people_reduce_min: The virtual_people_reduce_min of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :type: int
        """

        self._virtual_people_reduce_min = virtual_people_reduce_min

    @property
    def virtual_popularity(self):
        """Gets the virtual_popularity of this GetPopularitySettingsAPIResponse.  # noqa: E501


        :return: The virtual_popularity of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._virtual_popularity

    @virtual_popularity.setter
    def virtual_popularity(self, virtual_popularity):
        """Sets the virtual_popularity of this GetPopularitySettingsAPIResponse.


        :param virtual_popularity: The virtual_popularity of this GetPopularitySettingsAPIResponse.  # noqa: E501
        :type: int
        """

        self._virtual_popularity = virtual_popularity

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetPopularitySettingsAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetPopularitySettingsAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetPopularitySettingsAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
