# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeLoadBalancerAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_log': 'AccessLogForDescribeLoadBalancerAttributesOutput',
        'account_id': 'str',
        'address_ip_version': 'str',
        'allowed_ports': 'list[str]',
        'business_status': 'str',
        'bypass_security_group_enabled': 'str',
        'create_time': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'eip': 'EipForDescribeLoadBalancerAttributesOutput',
        'eip_address': 'str',
        'eip_id': 'str',
        'enabled': 'bool',
        'eni_address': 'str',
        'eni_address_num': 'int',
        'eni_addresses': 'list[EniAddressForDescribeLoadBalancerAttributesOutput]',
        'eni_id': 'str',
        'eni_ipv6_address': 'str',
        'exclusive_cluster_id': 'str',
        'expired_time': 'str',
        'ipv6_address_bandwidth': 'Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput',
        'ipv6_eip_id': 'str',
        'listeners': 'list[ListenerForDescribeLoadBalancerAttributesOutput]',
        'load_balancer_billing_type': 'int',
        'load_balancer_id': 'str',
        'load_balancer_name': 'str',
        'load_balancer_spec': 'str',
        'lock_reason': 'str',
        'log_topic_id': 'str',
        'master_zone_id': 'str',
        'modification_protection_reason': 'str',
        'modification_protection_status': 'str',
        'new_arch': 'bool',
        'overdue_time': 'str',
        'project_name': 'str',
        'request_id': 'str',
        'server_groups': 'list[ServerGroupForDescribeLoadBalancerAttributesOutput]',
        'service_managed': 'bool',
        'slave_zone_id': 'str',
        'status': 'str',
        'subnet_id': 'str',
        'tags': 'list[TagForDescribeLoadBalancerAttributesOutput]',
        'timestamp_remove_enabled': 'str',
        'type': 'str',
        'update_time': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'access_log': 'AccessLog',
        'account_id': 'AccountId',
        'address_ip_version': 'AddressIpVersion',
        'allowed_ports': 'AllowedPorts',
        'business_status': 'BusinessStatus',
        'bypass_security_group_enabled': 'BypassSecurityGroupEnabled',
        'create_time': 'CreateTime',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'eip': 'Eip',
        'eip_address': 'EipAddress',
        'eip_id': 'EipID',
        'enabled': 'Enabled',
        'eni_address': 'EniAddress',
        'eni_address_num': 'EniAddressNum',
        'eni_addresses': 'EniAddresses',
        'eni_id': 'EniID',
        'eni_ipv6_address': 'EniIpv6Address',
        'exclusive_cluster_id': 'ExclusiveClusterId',
        'expired_time': 'ExpiredTime',
        'ipv6_address_bandwidth': 'Ipv6AddressBandwidth',
        'ipv6_eip_id': 'Ipv6EipId',
        'listeners': 'Listeners',
        'load_balancer_billing_type': 'LoadBalancerBillingType',
        'load_balancer_id': 'LoadBalancerId',
        'load_balancer_name': 'LoadBalancerName',
        'load_balancer_spec': 'LoadBalancerSpec',
        'lock_reason': 'LockReason',
        'log_topic_id': 'LogTopicId',
        'master_zone_id': 'MasterZoneId',
        'modification_protection_reason': 'ModificationProtectionReason',
        'modification_protection_status': 'ModificationProtectionStatus',
        'new_arch': 'NewArch',
        'overdue_time': 'OverdueTime',
        'project_name': 'ProjectName',
        'request_id': 'RequestId',
        'server_groups': 'ServerGroups',
        'service_managed': 'ServiceManaged',
        'slave_zone_id': 'SlaveZoneId',
        'status': 'Status',
        'subnet_id': 'SubnetId',
        'tags': 'Tags',
        'timestamp_remove_enabled': 'TimestampRemoveEnabled',
        'type': 'Type',
        'update_time': 'UpdateTime',
        'vpc_id': 'VpcId'
    }

    def __init__(self, access_log=None, account_id=None, address_ip_version=None, allowed_ports=None, business_status=None, bypass_security_group_enabled=None, create_time=None, deleted_time=None, description=None, eip=None, eip_address=None, eip_id=None, enabled=None, eni_address=None, eni_address_num=None, eni_addresses=None, eni_id=None, eni_ipv6_address=None, exclusive_cluster_id=None, expired_time=None, ipv6_address_bandwidth=None, ipv6_eip_id=None, listeners=None, load_balancer_billing_type=None, load_balancer_id=None, load_balancer_name=None, load_balancer_spec=None, lock_reason=None, log_topic_id=None, master_zone_id=None, modification_protection_reason=None, modification_protection_status=None, new_arch=None, overdue_time=None, project_name=None, request_id=None, server_groups=None, service_managed=None, slave_zone_id=None, status=None, subnet_id=None, tags=None, timestamp_remove_enabled=None, type=None, update_time=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DescribeLoadBalancerAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_log = None
        self._account_id = None
        self._address_ip_version = None
        self._allowed_ports = None
        self._business_status = None
        self._bypass_security_group_enabled = None
        self._create_time = None
        self._deleted_time = None
        self._description = None
        self._eip = None
        self._eip_address = None
        self._eip_id = None
        self._enabled = None
        self._eni_address = None
        self._eni_address_num = None
        self._eni_addresses = None
        self._eni_id = None
        self._eni_ipv6_address = None
        self._exclusive_cluster_id = None
        self._expired_time = None
        self._ipv6_address_bandwidth = None
        self._ipv6_eip_id = None
        self._listeners = None
        self._load_balancer_billing_type = None
        self._load_balancer_id = None
        self._load_balancer_name = None
        self._load_balancer_spec = None
        self._lock_reason = None
        self._log_topic_id = None
        self._master_zone_id = None
        self._modification_protection_reason = None
        self._modification_protection_status = None
        self._new_arch = None
        self._overdue_time = None
        self._project_name = None
        self._request_id = None
        self._server_groups = None
        self._service_managed = None
        self._slave_zone_id = None
        self._status = None
        self._subnet_id = None
        self._tags = None
        self._timestamp_remove_enabled = None
        self._type = None
        self._update_time = None
        self._vpc_id = None
        self.discriminator = None

        if access_log is not None:
            self.access_log = access_log
        if account_id is not None:
            self.account_id = account_id
        if address_ip_version is not None:
            self.address_ip_version = address_ip_version
        if allowed_ports is not None:
            self.allowed_ports = allowed_ports
        if business_status is not None:
            self.business_status = business_status
        if bypass_security_group_enabled is not None:
            self.bypass_security_group_enabled = bypass_security_group_enabled
        if create_time is not None:
            self.create_time = create_time
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if eip is not None:
            self.eip = eip
        if eip_address is not None:
            self.eip_address = eip_address
        if eip_id is not None:
            self.eip_id = eip_id
        if enabled is not None:
            self.enabled = enabled
        if eni_address is not None:
            self.eni_address = eni_address
        if eni_address_num is not None:
            self.eni_address_num = eni_address_num
        if eni_addresses is not None:
            self.eni_addresses = eni_addresses
        if eni_id is not None:
            self.eni_id = eni_id
        if eni_ipv6_address is not None:
            self.eni_ipv6_address = eni_ipv6_address
        if exclusive_cluster_id is not None:
            self.exclusive_cluster_id = exclusive_cluster_id
        if expired_time is not None:
            self.expired_time = expired_time
        if ipv6_address_bandwidth is not None:
            self.ipv6_address_bandwidth = ipv6_address_bandwidth
        if ipv6_eip_id is not None:
            self.ipv6_eip_id = ipv6_eip_id
        if listeners is not None:
            self.listeners = listeners
        if load_balancer_billing_type is not None:
            self.load_balancer_billing_type = load_balancer_billing_type
        if load_balancer_id is not None:
            self.load_balancer_id = load_balancer_id
        if load_balancer_name is not None:
            self.load_balancer_name = load_balancer_name
        if load_balancer_spec is not None:
            self.load_balancer_spec = load_balancer_spec
        if lock_reason is not None:
            self.lock_reason = lock_reason
        if log_topic_id is not None:
            self.log_topic_id = log_topic_id
        if master_zone_id is not None:
            self.master_zone_id = master_zone_id
        if modification_protection_reason is not None:
            self.modification_protection_reason = modification_protection_reason
        if modification_protection_status is not None:
            self.modification_protection_status = modification_protection_status
        if new_arch is not None:
            self.new_arch = new_arch
        if overdue_time is not None:
            self.overdue_time = overdue_time
        if project_name is not None:
            self.project_name = project_name
        if request_id is not None:
            self.request_id = request_id
        if server_groups is not None:
            self.server_groups = server_groups
        if service_managed is not None:
            self.service_managed = service_managed
        if slave_zone_id is not None:
            self.slave_zone_id = slave_zone_id
        if status is not None:
            self.status = status
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags
        if timestamp_remove_enabled is not None:
            self.timestamp_remove_enabled = timestamp_remove_enabled
        if type is not None:
            self.type = type
        if update_time is not None:
            self.update_time = update_time
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def access_log(self):
        """Gets the access_log of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The access_log of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: AccessLogForDescribeLoadBalancerAttributesOutput
        """
        return self._access_log

    @access_log.setter
    def access_log(self, access_log):
        """Sets the access_log of this DescribeLoadBalancerAttributesResponse.


        :param access_log: The access_log of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: AccessLogForDescribeLoadBalancerAttributesOutput
        """

        self._access_log = access_log

    @property
    def account_id(self):
        """Gets the account_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The account_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeLoadBalancerAttributesResponse.


        :param account_id: The account_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def address_ip_version(self):
        """Gets the address_ip_version of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The address_ip_version of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._address_ip_version

    @address_ip_version.setter
    def address_ip_version(self, address_ip_version):
        """Sets the address_ip_version of this DescribeLoadBalancerAttributesResponse.


        :param address_ip_version: The address_ip_version of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._address_ip_version = address_ip_version

    @property
    def allowed_ports(self):
        """Gets the allowed_ports of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The allowed_ports of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._allowed_ports

    @allowed_ports.setter
    def allowed_ports(self, allowed_ports):
        """Sets the allowed_ports of this DescribeLoadBalancerAttributesResponse.


        :param allowed_ports: The allowed_ports of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: list[str]
        """

        self._allowed_ports = allowed_ports

    @property
    def business_status(self):
        """Gets the business_status of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The business_status of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._business_status

    @business_status.setter
    def business_status(self, business_status):
        """Sets the business_status of this DescribeLoadBalancerAttributesResponse.


        :param business_status: The business_status of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._business_status = business_status

    @property
    def bypass_security_group_enabled(self):
        """Gets the bypass_security_group_enabled of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The bypass_security_group_enabled of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._bypass_security_group_enabled

    @bypass_security_group_enabled.setter
    def bypass_security_group_enabled(self, bypass_security_group_enabled):
        """Sets the bypass_security_group_enabled of this DescribeLoadBalancerAttributesResponse.


        :param bypass_security_group_enabled: The bypass_security_group_enabled of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._bypass_security_group_enabled = bypass_security_group_enabled

    @property
    def create_time(self):
        """Gets the create_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The create_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeLoadBalancerAttributesResponse.


        :param create_time: The create_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def deleted_time(self):
        """Gets the deleted_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The deleted_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this DescribeLoadBalancerAttributesResponse.


        :param deleted_time: The deleted_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The description of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeLoadBalancerAttributesResponse.


        :param description: The description of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def eip(self):
        """Gets the eip of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The eip of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: EipForDescribeLoadBalancerAttributesOutput
        """
        return self._eip

    @eip.setter
    def eip(self, eip):
        """Sets the eip of this DescribeLoadBalancerAttributesResponse.


        :param eip: The eip of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: EipForDescribeLoadBalancerAttributesOutput
        """

        self._eip = eip

    @property
    def eip_address(self):
        """Gets the eip_address of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The eip_address of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this DescribeLoadBalancerAttributesResponse.


        :param eip_address: The eip_address of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def eip_id(self):
        """Gets the eip_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The eip_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._eip_id

    @eip_id.setter
    def eip_id(self, eip_id):
        """Sets the eip_id of this DescribeLoadBalancerAttributesResponse.


        :param eip_id: The eip_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._eip_id = eip_id

    @property
    def enabled(self):
        """Gets the enabled of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The enabled of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this DescribeLoadBalancerAttributesResponse.


        :param enabled: The enabled of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def eni_address(self):
        """Gets the eni_address of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The eni_address of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._eni_address

    @eni_address.setter
    def eni_address(self, eni_address):
        """Sets the eni_address of this DescribeLoadBalancerAttributesResponse.


        :param eni_address: The eni_address of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._eni_address = eni_address

    @property
    def eni_address_num(self):
        """Gets the eni_address_num of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The eni_address_num of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._eni_address_num

    @eni_address_num.setter
    def eni_address_num(self, eni_address_num):
        """Sets the eni_address_num of this DescribeLoadBalancerAttributesResponse.


        :param eni_address_num: The eni_address_num of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: int
        """

        self._eni_address_num = eni_address_num

    @property
    def eni_addresses(self):
        """Gets the eni_addresses of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The eni_addresses of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: list[EniAddressForDescribeLoadBalancerAttributesOutput]
        """
        return self._eni_addresses

    @eni_addresses.setter
    def eni_addresses(self, eni_addresses):
        """Sets the eni_addresses of this DescribeLoadBalancerAttributesResponse.


        :param eni_addresses: The eni_addresses of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: list[EniAddressForDescribeLoadBalancerAttributesOutput]
        """

        self._eni_addresses = eni_addresses

    @property
    def eni_id(self):
        """Gets the eni_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The eni_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._eni_id

    @eni_id.setter
    def eni_id(self, eni_id):
        """Sets the eni_id of this DescribeLoadBalancerAttributesResponse.


        :param eni_id: The eni_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._eni_id = eni_id

    @property
    def eni_ipv6_address(self):
        """Gets the eni_ipv6_address of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The eni_ipv6_address of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._eni_ipv6_address

    @eni_ipv6_address.setter
    def eni_ipv6_address(self, eni_ipv6_address):
        """Sets the eni_ipv6_address of this DescribeLoadBalancerAttributesResponse.


        :param eni_ipv6_address: The eni_ipv6_address of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._eni_ipv6_address = eni_ipv6_address

    @property
    def exclusive_cluster_id(self):
        """Gets the exclusive_cluster_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The exclusive_cluster_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._exclusive_cluster_id

    @exclusive_cluster_id.setter
    def exclusive_cluster_id(self, exclusive_cluster_id):
        """Sets the exclusive_cluster_id of this DescribeLoadBalancerAttributesResponse.


        :param exclusive_cluster_id: The exclusive_cluster_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._exclusive_cluster_id = exclusive_cluster_id

    @property
    def expired_time(self):
        """Gets the expired_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The expired_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this DescribeLoadBalancerAttributesResponse.


        :param expired_time: The expired_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def ipv6_address_bandwidth(self):
        """Gets the ipv6_address_bandwidth of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The ipv6_address_bandwidth of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput
        """
        return self._ipv6_address_bandwidth

    @ipv6_address_bandwidth.setter
    def ipv6_address_bandwidth(self, ipv6_address_bandwidth):
        """Sets the ipv6_address_bandwidth of this DescribeLoadBalancerAttributesResponse.


        :param ipv6_address_bandwidth: The ipv6_address_bandwidth of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: Ipv6AddressBandwidthForDescribeLoadBalancerAttributesOutput
        """

        self._ipv6_address_bandwidth = ipv6_address_bandwidth

    @property
    def ipv6_eip_id(self):
        """Gets the ipv6_eip_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The ipv6_eip_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_eip_id

    @ipv6_eip_id.setter
    def ipv6_eip_id(self, ipv6_eip_id):
        """Sets the ipv6_eip_id of this DescribeLoadBalancerAttributesResponse.


        :param ipv6_eip_id: The ipv6_eip_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._ipv6_eip_id = ipv6_eip_id

    @property
    def listeners(self):
        """Gets the listeners of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The listeners of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: list[ListenerForDescribeLoadBalancerAttributesOutput]
        """
        return self._listeners

    @listeners.setter
    def listeners(self, listeners):
        """Sets the listeners of this DescribeLoadBalancerAttributesResponse.


        :param listeners: The listeners of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: list[ListenerForDescribeLoadBalancerAttributesOutput]
        """

        self._listeners = listeners

    @property
    def load_balancer_billing_type(self):
        """Gets the load_balancer_billing_type of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The load_balancer_billing_type of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._load_balancer_billing_type

    @load_balancer_billing_type.setter
    def load_balancer_billing_type(self, load_balancer_billing_type):
        """Sets the load_balancer_billing_type of this DescribeLoadBalancerAttributesResponse.


        :param load_balancer_billing_type: The load_balancer_billing_type of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: int
        """

        self._load_balancer_billing_type = load_balancer_billing_type

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The load_balancer_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this DescribeLoadBalancerAttributesResponse.


        :param load_balancer_id: The load_balancer_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._load_balancer_id = load_balancer_id

    @property
    def load_balancer_name(self):
        """Gets the load_balancer_name of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The load_balancer_name of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_name

    @load_balancer_name.setter
    def load_balancer_name(self, load_balancer_name):
        """Sets the load_balancer_name of this DescribeLoadBalancerAttributesResponse.


        :param load_balancer_name: The load_balancer_name of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._load_balancer_name = load_balancer_name

    @property
    def load_balancer_spec(self):
        """Gets the load_balancer_spec of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The load_balancer_spec of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_spec

    @load_balancer_spec.setter
    def load_balancer_spec(self, load_balancer_spec):
        """Sets the load_balancer_spec of this DescribeLoadBalancerAttributesResponse.


        :param load_balancer_spec: The load_balancer_spec of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._load_balancer_spec = load_balancer_spec

    @property
    def lock_reason(self):
        """Gets the lock_reason of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The lock_reason of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._lock_reason

    @lock_reason.setter
    def lock_reason(self, lock_reason):
        """Sets the lock_reason of this DescribeLoadBalancerAttributesResponse.


        :param lock_reason: The lock_reason of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._lock_reason = lock_reason

    @property
    def log_topic_id(self):
        """Gets the log_topic_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The log_topic_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._log_topic_id

    @log_topic_id.setter
    def log_topic_id(self, log_topic_id):
        """Sets the log_topic_id of this DescribeLoadBalancerAttributesResponse.


        :param log_topic_id: The log_topic_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._log_topic_id = log_topic_id

    @property
    def master_zone_id(self):
        """Gets the master_zone_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The master_zone_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._master_zone_id

    @master_zone_id.setter
    def master_zone_id(self, master_zone_id):
        """Sets the master_zone_id of this DescribeLoadBalancerAttributesResponse.


        :param master_zone_id: The master_zone_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._master_zone_id = master_zone_id

    @property
    def modification_protection_reason(self):
        """Gets the modification_protection_reason of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The modification_protection_reason of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_reason

    @modification_protection_reason.setter
    def modification_protection_reason(self, modification_protection_reason):
        """Sets the modification_protection_reason of this DescribeLoadBalancerAttributesResponse.


        :param modification_protection_reason: The modification_protection_reason of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._modification_protection_reason = modification_protection_reason

    @property
    def modification_protection_status(self):
        """Gets the modification_protection_status of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The modification_protection_status of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_status

    @modification_protection_status.setter
    def modification_protection_status(self, modification_protection_status):
        """Sets the modification_protection_status of this DescribeLoadBalancerAttributesResponse.


        :param modification_protection_status: The modification_protection_status of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._modification_protection_status = modification_protection_status

    @property
    def new_arch(self):
        """Gets the new_arch of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The new_arch of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._new_arch

    @new_arch.setter
    def new_arch(self, new_arch):
        """Sets the new_arch of this DescribeLoadBalancerAttributesResponse.


        :param new_arch: The new_arch of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._new_arch = new_arch

    @property
    def overdue_time(self):
        """Gets the overdue_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The overdue_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._overdue_time

    @overdue_time.setter
    def overdue_time(self, overdue_time):
        """Sets the overdue_time of this DescribeLoadBalancerAttributesResponse.


        :param overdue_time: The overdue_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._overdue_time = overdue_time

    @property
    def project_name(self):
        """Gets the project_name of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The project_name of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeLoadBalancerAttributesResponse.


        :param project_name: The project_name of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def request_id(self):
        """Gets the request_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeLoadBalancerAttributesResponse.


        :param request_id: The request_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def server_groups(self):
        """Gets the server_groups of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The server_groups of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: list[ServerGroupForDescribeLoadBalancerAttributesOutput]
        """
        return self._server_groups

    @server_groups.setter
    def server_groups(self, server_groups):
        """Sets the server_groups of this DescribeLoadBalancerAttributesResponse.


        :param server_groups: The server_groups of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: list[ServerGroupForDescribeLoadBalancerAttributesOutput]
        """

        self._server_groups = server_groups

    @property
    def service_managed(self):
        """Gets the service_managed of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The service_managed of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._service_managed

    @service_managed.setter
    def service_managed(self, service_managed):
        """Sets the service_managed of this DescribeLoadBalancerAttributesResponse.


        :param service_managed: The service_managed of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._service_managed = service_managed

    @property
    def slave_zone_id(self):
        """Gets the slave_zone_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The slave_zone_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._slave_zone_id

    @slave_zone_id.setter
    def slave_zone_id(self, slave_zone_id):
        """Sets the slave_zone_id of this DescribeLoadBalancerAttributesResponse.


        :param slave_zone_id: The slave_zone_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._slave_zone_id = slave_zone_id

    @property
    def status(self):
        """Gets the status of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The status of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeLoadBalancerAttributesResponse.


        :param status: The status of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def subnet_id(self):
        """Gets the subnet_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The subnet_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this DescribeLoadBalancerAttributesResponse.


        :param subnet_id: The subnet_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The tags of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: list[TagForDescribeLoadBalancerAttributesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DescribeLoadBalancerAttributesResponse.


        :param tags: The tags of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: list[TagForDescribeLoadBalancerAttributesOutput]
        """

        self._tags = tags

    @property
    def timestamp_remove_enabled(self):
        """Gets the timestamp_remove_enabled of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The timestamp_remove_enabled of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._timestamp_remove_enabled

    @timestamp_remove_enabled.setter
    def timestamp_remove_enabled(self, timestamp_remove_enabled):
        """Sets the timestamp_remove_enabled of this DescribeLoadBalancerAttributesResponse.


        :param timestamp_remove_enabled: The timestamp_remove_enabled of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._timestamp_remove_enabled = timestamp_remove_enabled

    @property
    def type(self):
        """Gets the type of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The type of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DescribeLoadBalancerAttributesResponse.


        :param type: The type of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def update_time(self):
        """Gets the update_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeLoadBalancerAttributesResponse.


        :param update_time: The update_time of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501


        :return: The vpc_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeLoadBalancerAttributesResponse.


        :param vpc_id: The vpc_id of this DescribeLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeLoadBalancerAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeLoadBalancerAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeLoadBalancerAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
