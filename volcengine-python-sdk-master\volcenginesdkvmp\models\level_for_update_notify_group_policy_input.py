# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LevelForUpdateNotifyGroupPolicyInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'group_by': 'list[str]',
        'group_interval': 'str',
        'group_wait': 'str',
        'level': 'str',
        'repeat_interval': 'str'
    }

    attribute_map = {
        'group_by': 'GroupBy',
        'group_interval': 'GroupInterval',
        'group_wait': 'GroupWait',
        'level': 'Level',
        'repeat_interval': 'RepeatInterval'
    }

    def __init__(self, group_by=None, group_interval=None, group_wait=None, level=None, repeat_interval=None, _configuration=None):  # noqa: E501
        """LevelForUpdateNotifyGroupPolicyInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._group_by = None
        self._group_interval = None
        self._group_wait = None
        self._level = None
        self._repeat_interval = None
        self.discriminator = None

        if group_by is not None:
            self.group_by = group_by
        if group_interval is not None:
            self.group_interval = group_interval
        if group_wait is not None:
            self.group_wait = group_wait
        if level is not None:
            self.level = level
        if repeat_interval is not None:
            self.repeat_interval = repeat_interval

    @property
    def group_by(self):
        """Gets the group_by of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501


        :return: The group_by of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._group_by

    @group_by.setter
    def group_by(self, group_by):
        """Sets the group_by of this LevelForUpdateNotifyGroupPolicyInput.


        :param group_by: The group_by of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :type: list[str]
        """

        self._group_by = group_by

    @property
    def group_interval(self):
        """Gets the group_interval of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501


        :return: The group_interval of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :rtype: str
        """
        return self._group_interval

    @group_interval.setter
    def group_interval(self, group_interval):
        """Sets the group_interval of this LevelForUpdateNotifyGroupPolicyInput.


        :param group_interval: The group_interval of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :type: str
        """

        self._group_interval = group_interval

    @property
    def group_wait(self):
        """Gets the group_wait of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501


        :return: The group_wait of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :rtype: str
        """
        return self._group_wait

    @group_wait.setter
    def group_wait(self, group_wait):
        """Sets the group_wait of this LevelForUpdateNotifyGroupPolicyInput.


        :param group_wait: The group_wait of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :type: str
        """

        self._group_wait = group_wait

    @property
    def level(self):
        """Gets the level of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501


        :return: The level of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this LevelForUpdateNotifyGroupPolicyInput.


        :param level: The level of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def repeat_interval(self):
        """Gets the repeat_interval of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501


        :return: The repeat_interval of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :rtype: str
        """
        return self._repeat_interval

    @repeat_interval.setter
    def repeat_interval(self, repeat_interval):
        """Sets the repeat_interval of this LevelForUpdateNotifyGroupPolicyInput.


        :param repeat_interval: The repeat_interval of this LevelForUpdateNotifyGroupPolicyInput.  # noqa: E501
        :type: str
        """

        self._repeat_interval = repeat_interval

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LevelForUpdateNotifyGroupPolicyInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LevelForUpdateNotifyGroupPolicyInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LevelForUpdateNotifyGroupPolicyInput):
            return True

        return self.to_dict() != other.to_dict()
