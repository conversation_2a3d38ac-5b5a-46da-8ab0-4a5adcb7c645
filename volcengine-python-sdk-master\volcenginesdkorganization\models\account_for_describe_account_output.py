# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AccountForDescribeAccountOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'account_name': 'str',
        'allow_console': 'int',
        'allow_exit': 'int',
        'created_time': 'str',
        'delete_uk': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'id': 'str',
        'iam_role': 'str',
        'join_type': 'int',
        'main_name': 'str',
        'org_id': 'str',
        'org_type': 'int',
        'org_unit_id': 'str',
        'org_verification_id': 'str',
        'owner': 'str',
        'show_name': 'str',
        'updated_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'account_name': 'AccountName',
        'allow_console': 'AllowConsole',
        'allow_exit': 'AllowExit',
        'created_time': 'CreatedTime',
        'delete_uk': 'DeleteUk',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'id': 'ID',
        'iam_role': 'IamRole',
        'join_type': 'JoinType',
        'main_name': 'MainName',
        'org_id': 'OrgID',
        'org_type': 'OrgType',
        'org_unit_id': 'OrgUnitID',
        'org_verification_id': 'OrgVerificationID',
        'owner': 'Owner',
        'show_name': 'ShowName',
        'updated_time': 'UpdatedTime'
    }

    def __init__(self, account_id=None, account_name=None, allow_console=None, allow_exit=None, created_time=None, delete_uk=None, deleted_time=None, description=None, id=None, iam_role=None, join_type=None, main_name=None, org_id=None, org_type=None, org_unit_id=None, org_verification_id=None, owner=None, show_name=None, updated_time=None, _configuration=None):  # noqa: E501
        """AccountForDescribeAccountOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._account_name = None
        self._allow_console = None
        self._allow_exit = None
        self._created_time = None
        self._delete_uk = None
        self._deleted_time = None
        self._description = None
        self._id = None
        self._iam_role = None
        self._join_type = None
        self._main_name = None
        self._org_id = None
        self._org_type = None
        self._org_unit_id = None
        self._org_verification_id = None
        self._owner = None
        self._show_name = None
        self._updated_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if account_name is not None:
            self.account_name = account_name
        if allow_console is not None:
            self.allow_console = allow_console
        if allow_exit is not None:
            self.allow_exit = allow_exit
        if created_time is not None:
            self.created_time = created_time
        if delete_uk is not None:
            self.delete_uk = delete_uk
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if iam_role is not None:
            self.iam_role = iam_role
        if join_type is not None:
            self.join_type = join_type
        if main_name is not None:
            self.main_name = main_name
        if org_id is not None:
            self.org_id = org_id
        if org_type is not None:
            self.org_type = org_type
        if org_unit_id is not None:
            self.org_unit_id = org_unit_id
        if org_verification_id is not None:
            self.org_verification_id = org_verification_id
        if owner is not None:
            self.owner = owner
        if show_name is not None:
            self.show_name = show_name
        if updated_time is not None:
            self.updated_time = updated_time

    @property
    def account_id(self):
        """Gets the account_id of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The account_id of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this AccountForDescribeAccountOutput.


        :param account_id: The account_id of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def account_name(self):
        """Gets the account_name of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The account_name of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this AccountForDescribeAccountOutput.


        :param account_name: The account_name of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._account_name = account_name

    @property
    def allow_console(self):
        """Gets the allow_console of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The allow_console of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: int
        """
        return self._allow_console

    @allow_console.setter
    def allow_console(self, allow_console):
        """Sets the allow_console of this AccountForDescribeAccountOutput.


        :param allow_console: The allow_console of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: int
        """

        self._allow_console = allow_console

    @property
    def allow_exit(self):
        """Gets the allow_exit of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The allow_exit of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: int
        """
        return self._allow_exit

    @allow_exit.setter
    def allow_exit(self, allow_exit):
        """Sets the allow_exit of this AccountForDescribeAccountOutput.


        :param allow_exit: The allow_exit of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: int
        """

        self._allow_exit = allow_exit

    @property
    def created_time(self):
        """Gets the created_time of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The created_time of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this AccountForDescribeAccountOutput.


        :param created_time: The created_time of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def delete_uk(self):
        """Gets the delete_uk of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The delete_uk of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._delete_uk

    @delete_uk.setter
    def delete_uk(self, delete_uk):
        """Sets the delete_uk of this AccountForDescribeAccountOutput.


        :param delete_uk: The delete_uk of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._delete_uk = delete_uk

    @property
    def deleted_time(self):
        """Gets the deleted_time of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The deleted_time of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this AccountForDescribeAccountOutput.


        :param deleted_time: The deleted_time of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The description of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this AccountForDescribeAccountOutput.


        :param description: The description of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The id of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this AccountForDescribeAccountOutput.


        :param id: The id of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def iam_role(self):
        """Gets the iam_role of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The iam_role of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._iam_role

    @iam_role.setter
    def iam_role(self, iam_role):
        """Sets the iam_role of this AccountForDescribeAccountOutput.


        :param iam_role: The iam_role of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._iam_role = iam_role

    @property
    def join_type(self):
        """Gets the join_type of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The join_type of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: int
        """
        return self._join_type

    @join_type.setter
    def join_type(self, join_type):
        """Sets the join_type of this AccountForDescribeAccountOutput.


        :param join_type: The join_type of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: int
        """

        self._join_type = join_type

    @property
    def main_name(self):
        """Gets the main_name of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The main_name of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._main_name

    @main_name.setter
    def main_name(self, main_name):
        """Sets the main_name of this AccountForDescribeAccountOutput.


        :param main_name: The main_name of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._main_name = main_name

    @property
    def org_id(self):
        """Gets the org_id of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The org_id of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._org_id

    @org_id.setter
    def org_id(self, org_id):
        """Sets the org_id of this AccountForDescribeAccountOutput.


        :param org_id: The org_id of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._org_id = org_id

    @property
    def org_type(self):
        """Gets the org_type of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The org_type of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: int
        """
        return self._org_type

    @org_type.setter
    def org_type(self, org_type):
        """Sets the org_type of this AccountForDescribeAccountOutput.


        :param org_type: The org_type of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: int
        """

        self._org_type = org_type

    @property
    def org_unit_id(self):
        """Gets the org_unit_id of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The org_unit_id of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._org_unit_id

    @org_unit_id.setter
    def org_unit_id(self, org_unit_id):
        """Sets the org_unit_id of this AccountForDescribeAccountOutput.


        :param org_unit_id: The org_unit_id of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._org_unit_id = org_unit_id

    @property
    def org_verification_id(self):
        """Gets the org_verification_id of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The org_verification_id of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._org_verification_id

    @org_verification_id.setter
    def org_verification_id(self, org_verification_id):
        """Sets the org_verification_id of this AccountForDescribeAccountOutput.


        :param org_verification_id: The org_verification_id of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._org_verification_id = org_verification_id

    @property
    def owner(self):
        """Gets the owner of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The owner of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner

    @owner.setter
    def owner(self, owner):
        """Sets the owner of this AccountForDescribeAccountOutput.


        :param owner: The owner of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._owner = owner

    @property
    def show_name(self):
        """Gets the show_name of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The show_name of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._show_name

    @show_name.setter
    def show_name(self, show_name):
        """Sets the show_name of this AccountForDescribeAccountOutput.


        :param show_name: The show_name of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._show_name = show_name

    @property
    def updated_time(self):
        """Gets the updated_time of this AccountForDescribeAccountOutput.  # noqa: E501


        :return: The updated_time of this AccountForDescribeAccountOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_time

    @updated_time.setter
    def updated_time(self, updated_time):
        """Sets the updated_time of this AccountForDescribeAccountOutput.


        :param updated_time: The updated_time of this AccountForDescribeAccountOutput.  # noqa: E501
        :type: str
        """

        self._updated_time = updated_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AccountForDescribeAccountOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AccountForDescribeAccountOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AccountForDescribeAccountOutput):
            return True

        return self.to_dict() != other.to_dict()
