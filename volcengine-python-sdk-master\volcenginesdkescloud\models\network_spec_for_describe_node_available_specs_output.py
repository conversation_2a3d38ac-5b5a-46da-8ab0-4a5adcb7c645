# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NetworkSpecForDescribeNodeAvailableSpecsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'network_role': 'str',
        'spec_name': 'str'
    }

    attribute_map = {
        'network_role': 'NetworkRole',
        'spec_name': 'SpecName'
    }

    def __init__(self, network_role=None, spec_name=None, _configuration=None):  # noqa: E501
        """NetworkSpecForDescribeNodeAvailableSpecsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._network_role = None
        self._spec_name = None
        self.discriminator = None

        if network_role is not None:
            self.network_role = network_role
        if spec_name is not None:
            self.spec_name = spec_name

    @property
    def network_role(self):
        """Gets the network_role of this NetworkSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501


        :return: The network_role of this NetworkSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._network_role

    @network_role.setter
    def network_role(self, network_role):
        """Sets the network_role of this NetworkSpecForDescribeNodeAvailableSpecsOutput.


        :param network_role: The network_role of this NetworkSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :type: str
        """

        self._network_role = network_role

    @property
    def spec_name(self):
        """Gets the spec_name of this NetworkSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501


        :return: The spec_name of this NetworkSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :rtype: str
        """
        return self._spec_name

    @spec_name.setter
    def spec_name(self, spec_name):
        """Sets the spec_name of this NetworkSpecForDescribeNodeAvailableSpecsOutput.


        :param spec_name: The spec_name of this NetworkSpecForDescribeNodeAvailableSpecsOutput.  # noqa: E501
        :type: str
        """

        self._spec_name = spec_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NetworkSpecForDescribeNodeAvailableSpecsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NetworkSpecForDescribeNodeAvailableSpecsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NetworkSpecForDescribeNodeAvailableSpecsOutput):
            return True

        return self.to_dict() != other.to_dict()
