# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListBatchInferenceJobsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'completion_window': 'str',
        'create_time': 'str',
        'description': 'str',
        'expire_time': 'str',
        'id': 'str',
        'input_file_tos_location': 'InputFileTosLocationForListBatchInferenceJobsOutput',
        'model_reference': 'ModelReferenceForListBatchInferenceJobsOutput',
        'name': 'str',
        'output_dir_tos_location': 'OutputDirTosLocationForListBatchInferenceJobsOutput',
        'project_name': 'str',
        'request_counts': 'RequestCountsForListBatchInferenceJobsOutput',
        'status': 'StatusForListBatchInferenceJobsOutput',
        'tags': 'list[TagForListBatchInferenceJobsOutput]',
        'update_time': 'str'
    }

    attribute_map = {
        'completion_window': 'CompletionWindow',
        'create_time': 'CreateTime',
        'description': 'Description',
        'expire_time': 'ExpireTime',
        'id': 'Id',
        'input_file_tos_location': 'InputFileTosLocation',
        'model_reference': 'ModelReference',
        'name': 'Name',
        'output_dir_tos_location': 'OutputDirTosLocation',
        'project_name': 'ProjectName',
        'request_counts': 'RequestCounts',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime'
    }

    def __init__(self, completion_window=None, create_time=None, description=None, expire_time=None, id=None, input_file_tos_location=None, model_reference=None, name=None, output_dir_tos_location=None, project_name=None, request_counts=None, status=None, tags=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListBatchInferenceJobsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._completion_window = None
        self._create_time = None
        self._description = None
        self._expire_time = None
        self._id = None
        self._input_file_tos_location = None
        self._model_reference = None
        self._name = None
        self._output_dir_tos_location = None
        self._project_name = None
        self._request_counts = None
        self._status = None
        self._tags = None
        self._update_time = None
        self.discriminator = None

        if completion_window is not None:
            self.completion_window = completion_window
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if expire_time is not None:
            self.expire_time = expire_time
        if id is not None:
            self.id = id
        if input_file_tos_location is not None:
            self.input_file_tos_location = input_file_tos_location
        if model_reference is not None:
            self.model_reference = model_reference
        if name is not None:
            self.name = name
        if output_dir_tos_location is not None:
            self.output_dir_tos_location = output_dir_tos_location
        if project_name is not None:
            self.project_name = project_name
        if request_counts is not None:
            self.request_counts = request_counts
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time

    @property
    def completion_window(self):
        """Gets the completion_window of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The completion_window of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._completion_window

    @completion_window.setter
    def completion_window(self, completion_window):
        """Sets the completion_window of this ItemForListBatchInferenceJobsOutput.


        :param completion_window: The completion_window of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._completion_window = completion_window

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The create_time of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListBatchInferenceJobsOutput.


        :param create_time: The create_time of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The description of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListBatchInferenceJobsOutput.


        :param description: The description of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def expire_time(self):
        """Gets the expire_time of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The expire_time of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this ItemForListBatchInferenceJobsOutput.


        :param expire_time: The expire_time of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._expire_time = expire_time

    @property
    def id(self):
        """Gets the id of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The id of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListBatchInferenceJobsOutput.


        :param id: The id of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def input_file_tos_location(self):
        """Gets the input_file_tos_location of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The input_file_tos_location of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: InputFileTosLocationForListBatchInferenceJobsOutput
        """
        return self._input_file_tos_location

    @input_file_tos_location.setter
    def input_file_tos_location(self, input_file_tos_location):
        """Sets the input_file_tos_location of this ItemForListBatchInferenceJobsOutput.


        :param input_file_tos_location: The input_file_tos_location of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: InputFileTosLocationForListBatchInferenceJobsOutput
        """

        self._input_file_tos_location = input_file_tos_location

    @property
    def model_reference(self):
        """Gets the model_reference of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The model_reference of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: ModelReferenceForListBatchInferenceJobsOutput
        """
        return self._model_reference

    @model_reference.setter
    def model_reference(self, model_reference):
        """Sets the model_reference of this ItemForListBatchInferenceJobsOutput.


        :param model_reference: The model_reference of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: ModelReferenceForListBatchInferenceJobsOutput
        """

        self._model_reference = model_reference

    @property
    def name(self):
        """Gets the name of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The name of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListBatchInferenceJobsOutput.


        :param name: The name of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def output_dir_tos_location(self):
        """Gets the output_dir_tos_location of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The output_dir_tos_location of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: OutputDirTosLocationForListBatchInferenceJobsOutput
        """
        return self._output_dir_tos_location

    @output_dir_tos_location.setter
    def output_dir_tos_location(self, output_dir_tos_location):
        """Sets the output_dir_tos_location of this ItemForListBatchInferenceJobsOutput.


        :param output_dir_tos_location: The output_dir_tos_location of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: OutputDirTosLocationForListBatchInferenceJobsOutput
        """

        self._output_dir_tos_location = output_dir_tos_location

    @property
    def project_name(self):
        """Gets the project_name of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The project_name of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ItemForListBatchInferenceJobsOutput.


        :param project_name: The project_name of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def request_counts(self):
        """Gets the request_counts of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The request_counts of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: RequestCountsForListBatchInferenceJobsOutput
        """
        return self._request_counts

    @request_counts.setter
    def request_counts(self, request_counts):
        """Sets the request_counts of this ItemForListBatchInferenceJobsOutput.


        :param request_counts: The request_counts of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: RequestCountsForListBatchInferenceJobsOutput
        """

        self._request_counts = request_counts

    @property
    def status(self):
        """Gets the status of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The status of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: StatusForListBatchInferenceJobsOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListBatchInferenceJobsOutput.


        :param status: The status of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: StatusForListBatchInferenceJobsOutput
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The tags of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: list[TagForListBatchInferenceJobsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ItemForListBatchInferenceJobsOutput.


        :param tags: The tags of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: list[TagForListBatchInferenceJobsOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListBatchInferenceJobsOutput.  # noqa: E501


        :return: The update_time of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListBatchInferenceJobsOutput.


        :param update_time: The update_time of this ItemForListBatchInferenceJobsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListBatchInferenceJobsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListBatchInferenceJobsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListBatchInferenceJobsOutput):
            return True

        return self.to_dict() != other.to_dict()
