# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateSiteTagAPIV2Request(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'site_tags': 'list[SiteTagForUpdateSiteTagAPIV2Input]',
        'text_site_tags': 'list[TextSiteTagForUpdateSiteTagAPIV2Input]'
    }

    attribute_map = {
        'site_tags': 'SiteTags',
        'text_site_tags': 'TextSiteTags'
    }

    def __init__(self, site_tags=None, text_site_tags=None, _configuration=None):  # noqa: E501
        """UpdateSiteTagAPIV2Request - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._site_tags = None
        self._text_site_tags = None
        self.discriminator = None

        if site_tags is not None:
            self.site_tags = site_tags
        if text_site_tags is not None:
            self.text_site_tags = text_site_tags

    @property
    def site_tags(self):
        """Gets the site_tags of this UpdateSiteTagAPIV2Request.  # noqa: E501


        :return: The site_tags of this UpdateSiteTagAPIV2Request.  # noqa: E501
        :rtype: list[SiteTagForUpdateSiteTagAPIV2Input]
        """
        return self._site_tags

    @site_tags.setter
    def site_tags(self, site_tags):
        """Sets the site_tags of this UpdateSiteTagAPIV2Request.


        :param site_tags: The site_tags of this UpdateSiteTagAPIV2Request.  # noqa: E501
        :type: list[SiteTagForUpdateSiteTagAPIV2Input]
        """

        self._site_tags = site_tags

    @property
    def text_site_tags(self):
        """Gets the text_site_tags of this UpdateSiteTagAPIV2Request.  # noqa: E501


        :return: The text_site_tags of this UpdateSiteTagAPIV2Request.  # noqa: E501
        :rtype: list[TextSiteTagForUpdateSiteTagAPIV2Input]
        """
        return self._text_site_tags

    @text_site_tags.setter
    def text_site_tags(self, text_site_tags):
        """Sets the text_site_tags of this UpdateSiteTagAPIV2Request.


        :param text_site_tags: The text_site_tags of this UpdateSiteTagAPIV2Request.  # noqa: E501
        :type: list[TextSiteTagForUpdateSiteTagAPIV2Input]
        """

        self._text_site_tags = text_site_tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateSiteTagAPIV2Request, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateSiteTagAPIV2Request):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateSiteTagAPIV2Request):
            return True

        return self.to_dict() != other.to_dict()
