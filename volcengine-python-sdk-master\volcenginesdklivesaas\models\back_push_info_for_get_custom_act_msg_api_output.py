# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BackPushInfoForGetCustomActMsgAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'push_path': 'str',
        'push_url': 'str',
        'streaming_code': 'str'
    }

    attribute_map = {
        'push_path': 'PushPath',
        'push_url': 'PushUrl',
        'streaming_code': 'StreamingCode'
    }

    def __init__(self, push_path=None, push_url=None, streaming_code=None, _configuration=None):  # noqa: E501
        """BackPushInfoForGetCustomActMsgAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._push_path = None
        self._push_url = None
        self._streaming_code = None
        self.discriminator = None

        if push_path is not None:
            self.push_path = push_path
        if push_url is not None:
            self.push_url = push_url
        if streaming_code is not None:
            self.streaming_code = streaming_code

    @property
    def push_path(self):
        """Gets the push_path of this BackPushInfoForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The push_path of this BackPushInfoForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._push_path

    @push_path.setter
    def push_path(self, push_path):
        """Sets the push_path of this BackPushInfoForGetCustomActMsgAPIOutput.


        :param push_path: The push_path of this BackPushInfoForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._push_path = push_path

    @property
    def push_url(self):
        """Gets the push_url of this BackPushInfoForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The push_url of this BackPushInfoForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._push_url

    @push_url.setter
    def push_url(self, push_url):
        """Sets the push_url of this BackPushInfoForGetCustomActMsgAPIOutput.


        :param push_url: The push_url of this BackPushInfoForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._push_url = push_url

    @property
    def streaming_code(self):
        """Gets the streaming_code of this BackPushInfoForGetCustomActMsgAPIOutput.  # noqa: E501


        :return: The streaming_code of this BackPushInfoForGetCustomActMsgAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._streaming_code

    @streaming_code.setter
    def streaming_code(self, streaming_code):
        """Sets the streaming_code of this BackPushInfoForGetCustomActMsgAPIOutput.


        :param streaming_code: The streaming_code of this BackPushInfoForGetCustomActMsgAPIOutput.  # noqa: E501
        :type: str
        """

        self._streaming_code = streaming_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BackPushInfoForGetCustomActMsgAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BackPushInfoForGetCustomActMsgAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BackPushInfoForGetCustomActMsgAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
