# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListOperationsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'cluster_ids': 'list[str]',
        'create_time_after': 'int',
        'create_time_before': 'int',
        'operate_ids': 'list[str]',
        'operate_name': 'str',
        'operator_name': 'str',
        'project_name': 'str',
        'tags': 'list[TagForListOperationsInput]'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'cluster_ids': 'ClusterIds',
        'create_time_after': 'CreateTimeAfter',
        'create_time_before': 'CreateTimeBefore',
        'operate_ids': 'OperateIds',
        'operate_name': 'OperateName',
        'operator_name': 'OperatorName',
        'project_name': 'ProjectName',
        'tags': 'Tags'
    }

    def __init__(self, cluster_id=None, cluster_ids=None, create_time_after=None, create_time_before=None, operate_ids=None, operate_name=None, operator_name=None, project_name=None, tags=None, _configuration=None):  # noqa: E501
        """ListOperationsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._cluster_ids = None
        self._create_time_after = None
        self._create_time_before = None
        self._operate_ids = None
        self._operate_name = None
        self._operator_name = None
        self._project_name = None
        self._tags = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_ids is not None:
            self.cluster_ids = cluster_ids
        if create_time_after is not None:
            self.create_time_after = create_time_after
        if create_time_before is not None:
            self.create_time_before = create_time_before
        if operate_ids is not None:
            self.operate_ids = operate_ids
        if operate_name is not None:
            self.operate_name = operate_name
        if operator_name is not None:
            self.operator_name = operator_name
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListOperationsRequest.  # noqa: E501


        :return: The cluster_id of this ListOperationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListOperationsRequest.


        :param cluster_id: The cluster_id of this ListOperationsRequest.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_ids(self):
        """Gets the cluster_ids of this ListOperationsRequest.  # noqa: E501


        :return: The cluster_ids of this ListOperationsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_ids

    @cluster_ids.setter
    def cluster_ids(self, cluster_ids):
        """Sets the cluster_ids of this ListOperationsRequest.


        :param cluster_ids: The cluster_ids of this ListOperationsRequest.  # noqa: E501
        :type: list[str]
        """

        self._cluster_ids = cluster_ids

    @property
    def create_time_after(self):
        """Gets the create_time_after of this ListOperationsRequest.  # noqa: E501


        :return: The create_time_after of this ListOperationsRequest.  # noqa: E501
        :rtype: int
        """
        return self._create_time_after

    @create_time_after.setter
    def create_time_after(self, create_time_after):
        """Sets the create_time_after of this ListOperationsRequest.


        :param create_time_after: The create_time_after of this ListOperationsRequest.  # noqa: E501
        :type: int
        """

        self._create_time_after = create_time_after

    @property
    def create_time_before(self):
        """Gets the create_time_before of this ListOperationsRequest.  # noqa: E501


        :return: The create_time_before of this ListOperationsRequest.  # noqa: E501
        :rtype: int
        """
        return self._create_time_before

    @create_time_before.setter
    def create_time_before(self, create_time_before):
        """Sets the create_time_before of this ListOperationsRequest.


        :param create_time_before: The create_time_before of this ListOperationsRequest.  # noqa: E501
        :type: int
        """

        self._create_time_before = create_time_before

    @property
    def operate_ids(self):
        """Gets the operate_ids of this ListOperationsRequest.  # noqa: E501


        :return: The operate_ids of this ListOperationsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._operate_ids

    @operate_ids.setter
    def operate_ids(self, operate_ids):
        """Sets the operate_ids of this ListOperationsRequest.


        :param operate_ids: The operate_ids of this ListOperationsRequest.  # noqa: E501
        :type: list[str]
        """

        self._operate_ids = operate_ids

    @property
    def operate_name(self):
        """Gets the operate_name of this ListOperationsRequest.  # noqa: E501


        :return: The operate_name of this ListOperationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._operate_name

    @operate_name.setter
    def operate_name(self, operate_name):
        """Sets the operate_name of this ListOperationsRequest.


        :param operate_name: The operate_name of this ListOperationsRequest.  # noqa: E501
        :type: str
        """

        self._operate_name = operate_name

    @property
    def operator_name(self):
        """Gets the operator_name of this ListOperationsRequest.  # noqa: E501


        :return: The operator_name of this ListOperationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._operator_name

    @operator_name.setter
    def operator_name(self, operator_name):
        """Sets the operator_name of this ListOperationsRequest.


        :param operator_name: The operator_name of this ListOperationsRequest.  # noqa: E501
        :type: str
        """

        self._operator_name = operator_name

    @property
    def project_name(self):
        """Gets the project_name of this ListOperationsRequest.  # noqa: E501


        :return: The project_name of this ListOperationsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListOperationsRequest.


        :param project_name: The project_name of this ListOperationsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this ListOperationsRequest.  # noqa: E501


        :return: The tags of this ListOperationsRequest.  # noqa: E501
        :rtype: list[TagForListOperationsInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ListOperationsRequest.


        :param tags: The tags of this ListOperationsRequest.  # noqa: E501
        :type: list[TagForListOperationsInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListOperationsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListOperationsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListOperationsRequest):
            return True

        return self.to_dict() != other.to_dict()
