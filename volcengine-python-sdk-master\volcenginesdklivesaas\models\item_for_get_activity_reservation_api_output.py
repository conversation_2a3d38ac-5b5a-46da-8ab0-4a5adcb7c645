# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForGetActivityReservationAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'channel_name': 'str',
        'external_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'ip_address': 'str',
        'nick_name': 'str',
        'source': 'int',
        'submit_time': 'int',
        'tel_reserve': 'str',
        'user_device': 'str',
        'user_id': 'int'
    }

    attribute_map = {
        'channel_name': 'ChannelName',
        'external_id': 'ExternalId',
        'extra': 'Extra',
        'ip': 'IP',
        'ip_address': 'IpAddress',
        'nick_name': 'NickName',
        'source': 'Source',
        'submit_time': 'SubmitTime',
        'tel_reserve': 'TelReserve',
        'user_device': 'UserDevice',
        'user_id': 'UserId'
    }

    def __init__(self, channel_name=None, external_id=None, extra=None, ip=None, ip_address=None, nick_name=None, source=None, submit_time=None, tel_reserve=None, user_device=None, user_id=None, _configuration=None):  # noqa: E501
        """ItemForGetActivityReservationAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._channel_name = None
        self._external_id = None
        self._extra = None
        self._ip = None
        self._ip_address = None
        self._nick_name = None
        self._source = None
        self._submit_time = None
        self._tel_reserve = None
        self._user_device = None
        self._user_id = None
        self.discriminator = None

        if channel_name is not None:
            self.channel_name = channel_name
        if external_id is not None:
            self.external_id = external_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if ip_address is not None:
            self.ip_address = ip_address
        if nick_name is not None:
            self.nick_name = nick_name
        if source is not None:
            self.source = source
        if submit_time is not None:
            self.submit_time = submit_time
        if tel_reserve is not None:
            self.tel_reserve = tel_reserve
        if user_device is not None:
            self.user_device = user_device
        if user_id is not None:
            self.user_id = user_id

    @property
    def channel_name(self):
        """Gets the channel_name of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The channel_name of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._channel_name

    @channel_name.setter
    def channel_name(self, channel_name):
        """Sets the channel_name of this ItemForGetActivityReservationAPIOutput.


        :param channel_name: The channel_name of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: str
        """

        self._channel_name = channel_name

    @property
    def external_id(self):
        """Gets the external_id of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The external_id of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this ItemForGetActivityReservationAPIOutput.


        :param external_id: The external_id of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def extra(self):
        """Gets the extra of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The extra of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this ItemForGetActivityReservationAPIOutput.


        :param extra: The extra of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The ip of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ItemForGetActivityReservationAPIOutput.


        :param ip: The ip of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def ip_address(self):
        """Gets the ip_address of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The ip_address of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip_address

    @ip_address.setter
    def ip_address(self, ip_address):
        """Sets the ip_address of this ItemForGetActivityReservationAPIOutput.


        :param ip_address: The ip_address of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: str
        """

        self._ip_address = ip_address

    @property
    def nick_name(self):
        """Gets the nick_name of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The nick_name of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._nick_name

    @nick_name.setter
    def nick_name(self, nick_name):
        """Sets the nick_name of this ItemForGetActivityReservationAPIOutput.


        :param nick_name: The nick_name of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: str
        """

        self._nick_name = nick_name

    @property
    def source(self):
        """Gets the source of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The source of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this ItemForGetActivityReservationAPIOutput.


        :param source: The source of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: int
        """

        self._source = source

    @property
    def submit_time(self):
        """Gets the submit_time of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The submit_time of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._submit_time

    @submit_time.setter
    def submit_time(self, submit_time):
        """Sets the submit_time of this ItemForGetActivityReservationAPIOutput.


        :param submit_time: The submit_time of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: int
        """

        self._submit_time = submit_time

    @property
    def tel_reserve(self):
        """Gets the tel_reserve of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The tel_reserve of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._tel_reserve

    @tel_reserve.setter
    def tel_reserve(self, tel_reserve):
        """Sets the tel_reserve of this ItemForGetActivityReservationAPIOutput.


        :param tel_reserve: The tel_reserve of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: str
        """

        self._tel_reserve = tel_reserve

    @property
    def user_device(self):
        """Gets the user_device of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The user_device of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_device

    @user_device.setter
    def user_device(self, user_device):
        """Sets the user_device of this ItemForGetActivityReservationAPIOutput.


        :param user_device: The user_device of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_device = user_device

    @property
    def user_id(self):
        """Gets the user_id of this ItemForGetActivityReservationAPIOutput.  # noqa: E501


        :return: The user_id of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this ItemForGetActivityReservationAPIOutput.


        :param user_id: The user_id of this ItemForGetActivityReservationAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForGetActivityReservationAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForGetActivityReservationAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForGetActivityReservationAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
