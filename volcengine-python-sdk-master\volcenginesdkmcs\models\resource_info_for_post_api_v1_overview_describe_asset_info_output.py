# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'resource_type': 'str',
        'risk': 'int',
        'riskless': 'int',
        'total': 'int'
    }

    attribute_map = {
        'resource_type': 'resource_type',
        'risk': 'risk',
        'riskless': 'riskless',
        'total': 'total'
    }

    def __init__(self, resource_type=None, risk=None, riskless=None, total=None, _configuration=None):  # noqa: E501
        """ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._resource_type = None
        self._risk = None
        self._riskless = None
        self._total = None
        self.discriminator = None

        if resource_type is not None:
            self.resource_type = resource_type
        if risk is not None:
            self.risk = risk
        if riskless is not None:
            self.riskless = riskless
        if total is not None:
            self.total = total

    @property
    def resource_type(self):
        """Gets the resource_type of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501


        :return: The resource_type of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.


        :param resource_type: The resource_type of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def risk(self):
        """Gets the risk of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501


        :return: The risk of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.


        :param risk: The risk of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501
        :type: int
        """

        self._risk = risk

    @property
    def riskless(self):
        """Gets the riskless of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501


        :return: The riskless of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._riskless

    @riskless.setter
    def riskless(self, riskless):
        """Sets the riskless of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.


        :param riskless: The riskless of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501
        :type: int
        """

        self._riskless = riskless

    @property
    def total(self):
        """Gets the total of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501


        :return: The total of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.


        :param total: The total of this ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceInfoForPostApiV1OverviewDescribeAssetInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
