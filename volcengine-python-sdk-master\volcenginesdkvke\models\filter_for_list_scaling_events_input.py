# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListScalingEventsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'str',
        'ids': 'list[str]',
        'node_ids': 'list[str]',
        'node_pool_ids': 'list[str]',
        'start_time': 'str',
        'statuses': 'list[str]',
        'type': 'str'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'ids': 'Ids',
        'node_ids': 'NodeIds',
        'node_pool_ids': 'NodePoolIds',
        'start_time': 'StartTime',
        'statuses': 'Statuses',
        'type': 'Type'
    }

    def __init__(self, end_time=None, ids=None, node_ids=None, node_pool_ids=None, start_time=None, statuses=None, type=None, _configuration=None):  # noqa: E501
        """FilterForListScalingEventsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._ids = None
        self._node_ids = None
        self._node_pool_ids = None
        self._start_time = None
        self._statuses = None
        self._type = None
        self.discriminator = None

        if end_time is not None:
            self.end_time = end_time
        if ids is not None:
            self.ids = ids
        if node_ids is not None:
            self.node_ids = node_ids
        if node_pool_ids is not None:
            self.node_pool_ids = node_pool_ids
        if start_time is not None:
            self.start_time = start_time
        if statuses is not None:
            self.statuses = statuses
        if type is not None:
            self.type = type

    @property
    def end_time(self):
        """Gets the end_time of this FilterForListScalingEventsInput.  # noqa: E501


        :return: The end_time of this FilterForListScalingEventsInput.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this FilterForListScalingEventsInput.


        :param end_time: The end_time of this FilterForListScalingEventsInput.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def ids(self):
        """Gets the ids of this FilterForListScalingEventsInput.  # noqa: E501


        :return: The ids of this FilterForListScalingEventsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListScalingEventsInput.


        :param ids: The ids of this FilterForListScalingEventsInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def node_ids(self):
        """Gets the node_ids of this FilterForListScalingEventsInput.  # noqa: E501


        :return: The node_ids of this FilterForListScalingEventsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_ids

    @node_ids.setter
    def node_ids(self, node_ids):
        """Sets the node_ids of this FilterForListScalingEventsInput.


        :param node_ids: The node_ids of this FilterForListScalingEventsInput.  # noqa: E501
        :type: list[str]
        """

        self._node_ids = node_ids

    @property
    def node_pool_ids(self):
        """Gets the node_pool_ids of this FilterForListScalingEventsInput.  # noqa: E501


        :return: The node_pool_ids of this FilterForListScalingEventsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._node_pool_ids

    @node_pool_ids.setter
    def node_pool_ids(self, node_pool_ids):
        """Sets the node_pool_ids of this FilterForListScalingEventsInput.


        :param node_pool_ids: The node_pool_ids of this FilterForListScalingEventsInput.  # noqa: E501
        :type: list[str]
        """

        self._node_pool_ids = node_pool_ids

    @property
    def start_time(self):
        """Gets the start_time of this FilterForListScalingEventsInput.  # noqa: E501


        :return: The start_time of this FilterForListScalingEventsInput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this FilterForListScalingEventsInput.


        :param start_time: The start_time of this FilterForListScalingEventsInput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def statuses(self):
        """Gets the statuses of this FilterForListScalingEventsInput.  # noqa: E501


        :return: The statuses of this FilterForListScalingEventsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._statuses

    @statuses.setter
    def statuses(self, statuses):
        """Sets the statuses of this FilterForListScalingEventsInput.


        :param statuses: The statuses of this FilterForListScalingEventsInput.  # noqa: E501
        :type: list[str]
        """

        self._statuses = statuses

    @property
    def type(self):
        """Gets the type of this FilterForListScalingEventsInput.  # noqa: E501


        :return: The type of this FilterForListScalingEventsInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this FilterForListScalingEventsInput.


        :param type: The type of this FilterForListScalingEventsInput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListScalingEventsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListScalingEventsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListScalingEventsInput):
            return True

        return self.to_dict() != other.to_dict()
