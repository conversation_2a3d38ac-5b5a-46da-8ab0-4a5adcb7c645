# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateFileSystemRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'capacity': 'int',
        'charge_type': 'str',
        'description': 'str',
        'file_system_name': 'str',
        'file_system_type': 'str',
        'project': 'str',
        'protocol_type': 'str',
        'store_type': 'str',
        'subnet_id': 'str',
        'tags': 'list[TagForCreateFileSystemInput]',
        'version_number': 'str',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'capacity': 'Capacity',
        'charge_type': 'ChargeType',
        'description': 'Description',
        'file_system_name': 'FileSystemName',
        'file_system_type': 'FileSystemType',
        'project': 'Project',
        'protocol_type': 'ProtocolType',
        'store_type': 'StoreType',
        'subnet_id': 'SubnetId',
        'tags': 'Tags',
        'version_number': 'VersionNumber',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, capacity=None, charge_type=None, description=None, file_system_name=None, file_system_type=None, project=None, protocol_type=None, store_type=None, subnet_id=None, tags=None, version_number=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """CreateFileSystemRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._capacity = None
        self._charge_type = None
        self._description = None
        self._file_system_name = None
        self._file_system_type = None
        self._project = None
        self._protocol_type = None
        self._store_type = None
        self._subnet_id = None
        self._tags = None
        self._version_number = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if capacity is not None:
            self.capacity = capacity
        self.charge_type = charge_type
        if description is not None:
            self.description = description
        self.file_system_name = file_system_name
        self.file_system_type = file_system_type
        if project is not None:
            self.project = project
        self.protocol_type = protocol_type
        self.store_type = store_type
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags
        if version_number is not None:
            self.version_number = version_number
        if vpc_id is not None:
            self.vpc_id = vpc_id
        self.zone_id = zone_id

    @property
    def capacity(self):
        """Gets the capacity of this CreateFileSystemRequest.  # noqa: E501


        :return: The capacity of this CreateFileSystemRequest.  # noqa: E501
        :rtype: int
        """
        return self._capacity

    @capacity.setter
    def capacity(self, capacity):
        """Sets the capacity of this CreateFileSystemRequest.


        :param capacity: The capacity of this CreateFileSystemRequest.  # noqa: E501
        :type: int
        """

        self._capacity = capacity

    @property
    def charge_type(self):
        """Gets the charge_type of this CreateFileSystemRequest.  # noqa: E501


        :return: The charge_type of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this CreateFileSystemRequest.


        :param charge_type: The charge_type of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and charge_type is None:
            raise ValueError("Invalid value for `charge_type`, must not be `None`")  # noqa: E501
        allowed_values = ["PayAsYouGo"]  # noqa: E501
        if (self._configuration.client_side_validation and
                charge_type not in allowed_values):
            raise ValueError(
                "Invalid value for `charge_type` ({0}), must be one of {1}"  # noqa: E501
                .format(charge_type, allowed_values)
            )

        self._charge_type = charge_type

    @property
    def description(self):
        """Gets the description of this CreateFileSystemRequest.  # noqa: E501


        :return: The description of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateFileSystemRequest.


        :param description: The description of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def file_system_name(self):
        """Gets the file_system_name of this CreateFileSystemRequest.  # noqa: E501


        :return: The file_system_name of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_system_name

    @file_system_name.setter
    def file_system_name(self, file_system_name):
        """Sets the file_system_name of this CreateFileSystemRequest.


        :param file_system_name: The file_system_name of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and file_system_name is None:
            raise ValueError("Invalid value for `file_system_name`, must not be `None`")  # noqa: E501

        self._file_system_name = file_system_name

    @property
    def file_system_type(self):
        """Gets the file_system_type of this CreateFileSystemRequest.  # noqa: E501


        :return: The file_system_type of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_system_type

    @file_system_type.setter
    def file_system_type(self, file_system_type):
        """Sets the file_system_type of this CreateFileSystemRequest.


        :param file_system_type: The file_system_type of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and file_system_type is None:
            raise ValueError("Invalid value for `file_system_type`, must not be `None`")  # noqa: E501
        allowed_values = ["VePFS"]  # noqa: E501
        if (self._configuration.client_side_validation and
                file_system_type not in allowed_values):
            raise ValueError(
                "Invalid value for `file_system_type` ({0}), must be one of {1}"  # noqa: E501
                .format(file_system_type, allowed_values)
            )

        self._file_system_type = file_system_type

    @property
    def project(self):
        """Gets the project of this CreateFileSystemRequest.  # noqa: E501


        :return: The project of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this CreateFileSystemRequest.


        :param project: The project of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def protocol_type(self):
        """Gets the protocol_type of this CreateFileSystemRequest.  # noqa: E501


        :return: The protocol_type of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._protocol_type

    @protocol_type.setter
    def protocol_type(self, protocol_type):
        """Sets the protocol_type of this CreateFileSystemRequest.


        :param protocol_type: The protocol_type of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and protocol_type is None:
            raise ValueError("Invalid value for `protocol_type`, must not be `None`")  # noqa: E501
        allowed_values = ["NFS", "SMB", "VePFS", "FSX"]  # noqa: E501
        if (self._configuration.client_side_validation and
                protocol_type not in allowed_values):
            raise ValueError(
                "Invalid value for `protocol_type` ({0}), must be one of {1}"  # noqa: E501
                .format(protocol_type, allowed_values)
            )

        self._protocol_type = protocol_type

    @property
    def store_type(self):
        """Gets the store_type of this CreateFileSystemRequest.  # noqa: E501


        :return: The store_type of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._store_type

    @store_type.setter
    def store_type(self, store_type):
        """Sets the store_type of this CreateFileSystemRequest.


        :param store_type: The store_type of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and store_type is None:
            raise ValueError("Invalid value for `store_type`, must not be `None`")  # noqa: E501

        self._store_type = store_type

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateFileSystemRequest.  # noqa: E501


        :return: The subnet_id of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateFileSystemRequest.


        :param subnet_id: The subnet_id of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this CreateFileSystemRequest.  # noqa: E501


        :return: The tags of this CreateFileSystemRequest.  # noqa: E501
        :rtype: list[TagForCreateFileSystemInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateFileSystemRequest.


        :param tags: The tags of this CreateFileSystemRequest.  # noqa: E501
        :type: list[TagForCreateFileSystemInput]
        """

        self._tags = tags

    @property
    def version_number(self):
        """Gets the version_number of this CreateFileSystemRequest.  # noqa: E501


        :return: The version_number of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._version_number

    @version_number.setter
    def version_number(self, version_number):
        """Sets the version_number of this CreateFileSystemRequest.


        :param version_number: The version_number of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """

        self._version_number = version_number

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateFileSystemRequest.  # noqa: E501


        :return: The vpc_id of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateFileSystemRequest.


        :param vpc_id: The vpc_id of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this CreateFileSystemRequest.  # noqa: E501


        :return: The zone_id of this CreateFileSystemRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this CreateFileSystemRequest.


        :param zone_id: The zone_id of this CreateFileSystemRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and zone_id is None:
            raise ValueError("Invalid value for `zone_id`, must not be `None`")  # noqa: E501

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateFileSystemRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateFileSystemRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateFileSystemRequest):
            return True

        return self.to_dict() != other.to_dict()
