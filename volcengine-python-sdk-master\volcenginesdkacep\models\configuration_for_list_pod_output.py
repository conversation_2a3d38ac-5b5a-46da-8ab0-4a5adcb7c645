# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConfigurationForListPodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'configuration_code': 'str',
        'configuration_name': 'str',
        'configuration_type': 'int',
        'server_type_code': 'str'
    }

    attribute_map = {
        'configuration_code': 'ConfigurationCode',
        'configuration_name': 'ConfigurationName',
        'configuration_type': 'ConfigurationType',
        'server_type_code': 'ServerTypeCode'
    }

    def __init__(self, configuration_code=None, configuration_name=None, configuration_type=None, server_type_code=None, _configuration=None):  # noqa: E501
        """ConfigurationForListPodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._configuration_code = None
        self._configuration_name = None
        self._configuration_type = None
        self._server_type_code = None
        self.discriminator = None

        if configuration_code is not None:
            self.configuration_code = configuration_code
        if configuration_name is not None:
            self.configuration_name = configuration_name
        if configuration_type is not None:
            self.configuration_type = configuration_type
        if server_type_code is not None:
            self.server_type_code = server_type_code

    @property
    def configuration_code(self):
        """Gets the configuration_code of this ConfigurationForListPodOutput.  # noqa: E501


        :return: The configuration_code of this ConfigurationForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this ConfigurationForListPodOutput.


        :param configuration_code: The configuration_code of this ConfigurationForListPodOutput.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def configuration_name(self):
        """Gets the configuration_name of this ConfigurationForListPodOutput.  # noqa: E501


        :return: The configuration_name of this ConfigurationForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_name

    @configuration_name.setter
    def configuration_name(self, configuration_name):
        """Sets the configuration_name of this ConfigurationForListPodOutput.


        :param configuration_name: The configuration_name of this ConfigurationForListPodOutput.  # noqa: E501
        :type: str
        """

        self._configuration_name = configuration_name

    @property
    def configuration_type(self):
        """Gets the configuration_type of this ConfigurationForListPodOutput.  # noqa: E501


        :return: The configuration_type of this ConfigurationForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._configuration_type

    @configuration_type.setter
    def configuration_type(self, configuration_type):
        """Sets the configuration_type of this ConfigurationForListPodOutput.


        :param configuration_type: The configuration_type of this ConfigurationForListPodOutput.  # noqa: E501
        :type: int
        """

        self._configuration_type = configuration_type

    @property
    def server_type_code(self):
        """Gets the server_type_code of this ConfigurationForListPodOutput.  # noqa: E501


        :return: The server_type_code of this ConfigurationForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_type_code

    @server_type_code.setter
    def server_type_code(self, server_type_code):
        """Sets the server_type_code of this ConfigurationForListPodOutput.


        :param server_type_code: The server_type_code of this ConfigurationForListPodOutput.  # noqa: E501
        :type: str
        """

        self._server_type_code = server_type_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConfigurationForListPodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConfigurationForListPodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConfigurationForListPodOutput):
            return True

        return self.to_dict() != other.to_dict()
