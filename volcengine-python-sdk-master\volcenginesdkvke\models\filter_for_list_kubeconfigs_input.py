# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListKubeconfigsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_ids': 'list[str]',
        'ids': 'list[str]',
        'role_ids': 'list[int]',
        'types': 'list[str]',
        'user_ids': 'list[int]'
    }

    attribute_map = {
        'cluster_ids': 'ClusterIds',
        'ids': 'Ids',
        'role_ids': 'RoleIds',
        'types': 'Types',
        'user_ids': 'UserIds'
    }

    def __init__(self, cluster_ids=None, ids=None, role_ids=None, types=None, user_ids=None, _configuration=None):  # noqa: E501
        """FilterForListKubeconfigsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_ids = None
        self._ids = None
        self._role_ids = None
        self._types = None
        self._user_ids = None
        self.discriminator = None

        if cluster_ids is not None:
            self.cluster_ids = cluster_ids
        if ids is not None:
            self.ids = ids
        if role_ids is not None:
            self.role_ids = role_ids
        if types is not None:
            self.types = types
        if user_ids is not None:
            self.user_ids = user_ids

    @property
    def cluster_ids(self):
        """Gets the cluster_ids of this FilterForListKubeconfigsInput.  # noqa: E501


        :return: The cluster_ids of this FilterForListKubeconfigsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_ids

    @cluster_ids.setter
    def cluster_ids(self, cluster_ids):
        """Sets the cluster_ids of this FilterForListKubeconfigsInput.


        :param cluster_ids: The cluster_ids of this FilterForListKubeconfigsInput.  # noqa: E501
        :type: list[str]
        """

        self._cluster_ids = cluster_ids

    @property
    def ids(self):
        """Gets the ids of this FilterForListKubeconfigsInput.  # noqa: E501


        :return: The ids of this FilterForListKubeconfigsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListKubeconfigsInput.


        :param ids: The ids of this FilterForListKubeconfigsInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def role_ids(self):
        """Gets the role_ids of this FilterForListKubeconfigsInput.  # noqa: E501


        :return: The role_ids of this FilterForListKubeconfigsInput.  # noqa: E501
        :rtype: list[int]
        """
        return self._role_ids

    @role_ids.setter
    def role_ids(self, role_ids):
        """Sets the role_ids of this FilterForListKubeconfigsInput.


        :param role_ids: The role_ids of this FilterForListKubeconfigsInput.  # noqa: E501
        :type: list[int]
        """

        self._role_ids = role_ids

    @property
    def types(self):
        """Gets the types of this FilterForListKubeconfigsInput.  # noqa: E501


        :return: The types of this FilterForListKubeconfigsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._types

    @types.setter
    def types(self, types):
        """Sets the types of this FilterForListKubeconfigsInput.


        :param types: The types of this FilterForListKubeconfigsInput.  # noqa: E501
        :type: list[str]
        """

        self._types = types

    @property
    def user_ids(self):
        """Gets the user_ids of this FilterForListKubeconfigsInput.  # noqa: E501


        :return: The user_ids of this FilterForListKubeconfigsInput.  # noqa: E501
        :rtype: list[int]
        """
        return self._user_ids

    @user_ids.setter
    def user_ids(self, user_ids):
        """Sets the user_ids of this FilterForListKubeconfigsInput.


        :param user_ids: The user_ids of this FilterForListKubeconfigsInput.  # noqa: E501
        :type: list[int]
        """

        self._user_ids = user_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListKubeconfigsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListKubeconfigsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListKubeconfigsInput):
            return True

        return self.to_dict() != other.to_dict()
