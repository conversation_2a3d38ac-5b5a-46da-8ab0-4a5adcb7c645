# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SnapshotForDescribeSnapshotsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'description': 'str',
        'file_system_id': 'str',
        'file_system_name': 'str',
        'is_encrypt': 'bool',
        'progress': 'str',
        'retention_days': 'int',
        'snapshot_id': 'str',
        'snapshot_name': 'str',
        'snapshot_type': 'str',
        'source_size': 'int',
        'source_version': 'str',
        'status': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'description': 'Description',
        'file_system_id': 'FileSystemId',
        'file_system_name': 'FileSystemName',
        'is_encrypt': 'IsEncrypt',
        'progress': 'Progress',
        'retention_days': 'RetentionDays',
        'snapshot_id': 'SnapshotId',
        'snapshot_name': 'SnapshotName',
        'snapshot_type': 'SnapshotType',
        'source_size': 'SourceSize',
        'source_version': 'SourceVersion',
        'status': 'Status',
        'zone_id': 'ZoneId'
    }

    def __init__(self, create_time=None, description=None, file_system_id=None, file_system_name=None, is_encrypt=None, progress=None, retention_days=None, snapshot_id=None, snapshot_name=None, snapshot_type=None, source_size=None, source_version=None, status=None, zone_id=None, _configuration=None):  # noqa: E501
        """SnapshotForDescribeSnapshotsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._description = None
        self._file_system_id = None
        self._file_system_name = None
        self._is_encrypt = None
        self._progress = None
        self._retention_days = None
        self._snapshot_id = None
        self._snapshot_name = None
        self._snapshot_type = None
        self._source_size = None
        self._source_version = None
        self._status = None
        self._zone_id = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if file_system_id is not None:
            self.file_system_id = file_system_id
        if file_system_name is not None:
            self.file_system_name = file_system_name
        if is_encrypt is not None:
            self.is_encrypt = is_encrypt
        if progress is not None:
            self.progress = progress
        if retention_days is not None:
            self.retention_days = retention_days
        if snapshot_id is not None:
            self.snapshot_id = snapshot_id
        if snapshot_name is not None:
            self.snapshot_name = snapshot_name
        if snapshot_type is not None:
            self.snapshot_type = snapshot_type
        if source_size is not None:
            self.source_size = source_size
        if source_version is not None:
            self.source_version = source_version
        if status is not None:
            self.status = status
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def create_time(self):
        """Gets the create_time of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The create_time of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this SnapshotForDescribeSnapshotsOutput.


        :param create_time: The create_time of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The description of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this SnapshotForDescribeSnapshotsOutput.


        :param description: The description of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def file_system_id(self):
        """Gets the file_system_id of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The file_system_id of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this SnapshotForDescribeSnapshotsOutput.


        :param file_system_id: The file_system_id of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._file_system_id = file_system_id

    @property
    def file_system_name(self):
        """Gets the file_system_name of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The file_system_name of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_name

    @file_system_name.setter
    def file_system_name(self, file_system_name):
        """Sets the file_system_name of this SnapshotForDescribeSnapshotsOutput.


        :param file_system_name: The file_system_name of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._file_system_name = file_system_name

    @property
    def is_encrypt(self):
        """Gets the is_encrypt of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The is_encrypt of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_encrypt

    @is_encrypt.setter
    def is_encrypt(self, is_encrypt):
        """Sets the is_encrypt of this SnapshotForDescribeSnapshotsOutput.


        :param is_encrypt: The is_encrypt of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: bool
        """

        self._is_encrypt = is_encrypt

    @property
    def progress(self):
        """Gets the progress of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The progress of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._progress

    @progress.setter
    def progress(self, progress):
        """Sets the progress of this SnapshotForDescribeSnapshotsOutput.


        :param progress: The progress of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._progress = progress

    @property
    def retention_days(self):
        """Gets the retention_days of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The retention_days of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: int
        """
        return self._retention_days

    @retention_days.setter
    def retention_days(self, retention_days):
        """Sets the retention_days of this SnapshotForDescribeSnapshotsOutput.


        :param retention_days: The retention_days of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: int
        """

        self._retention_days = retention_days

    @property
    def snapshot_id(self):
        """Gets the snapshot_id of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The snapshot_id of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_id

    @snapshot_id.setter
    def snapshot_id(self, snapshot_id):
        """Sets the snapshot_id of this SnapshotForDescribeSnapshotsOutput.


        :param snapshot_id: The snapshot_id of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._snapshot_id = snapshot_id

    @property
    def snapshot_name(self):
        """Gets the snapshot_name of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The snapshot_name of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_name

    @snapshot_name.setter
    def snapshot_name(self, snapshot_name):
        """Sets the snapshot_name of this SnapshotForDescribeSnapshotsOutput.


        :param snapshot_name: The snapshot_name of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._snapshot_name = snapshot_name

    @property
    def snapshot_type(self):
        """Gets the snapshot_type of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The snapshot_type of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_type

    @snapshot_type.setter
    def snapshot_type(self, snapshot_type):
        """Sets the snapshot_type of this SnapshotForDescribeSnapshotsOutput.


        :param snapshot_type: The snapshot_type of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._snapshot_type = snapshot_type

    @property
    def source_size(self):
        """Gets the source_size of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The source_size of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: int
        """
        return self._source_size

    @source_size.setter
    def source_size(self, source_size):
        """Sets the source_size of this SnapshotForDescribeSnapshotsOutput.


        :param source_size: The source_size of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: int
        """

        self._source_size = source_size

    @property
    def source_version(self):
        """Gets the source_version of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The source_version of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_version

    @source_version.setter
    def source_version(self, source_version):
        """Sets the source_version of this SnapshotForDescribeSnapshotsOutput.


        :param source_version: The source_version of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._source_version = source_version

    @property
    def status(self):
        """Gets the status of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The status of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SnapshotForDescribeSnapshotsOutput.


        :param status: The status of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def zone_id(self):
        """Gets the zone_id of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501


        :return: The zone_id of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this SnapshotForDescribeSnapshotsOutput.


        :param zone_id: The zone_id of this SnapshotForDescribeSnapshotsOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SnapshotForDescribeSnapshotsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SnapshotForDescribeSnapshotsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SnapshotForDescribeSnapshotsOutput):
            return True

        return self.to_dict() != other.to_dict()
