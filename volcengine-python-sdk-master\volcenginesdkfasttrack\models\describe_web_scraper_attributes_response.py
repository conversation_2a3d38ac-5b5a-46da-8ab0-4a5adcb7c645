# coding: utf-8

"""
    fasttrack

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeWebScraperAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'billing_type': 'int',
        'business_status': 'str',
        'create_time': 'str',
        'description': 'str',
        'endpoint': 'str',
        'lock_reason': 'str',
        'overdue_time': 'str',
        'reclaim_time': 'str',
        'request_id': 'str',
        'service_port': 'int',
        'service_type': 'str',
        'status': 'str',
        'subnet_id': 'str',
        'update_time': 'str',
        'vpc_id': 'str',
        'web_scraper_id': 'str',
        'web_scraper_name': 'str'
    }

    attribute_map = {
        'billing_type': 'BillingType',
        'business_status': 'BusinessStatus',
        'create_time': 'CreateTime',
        'description': 'Description',
        'endpoint': 'Endpoint',
        'lock_reason': 'LockReason',
        'overdue_time': 'OverdueTime',
        'reclaim_time': 'ReclaimTime',
        'request_id': 'RequestId',
        'service_port': 'ServicePort',
        'service_type': 'ServiceType',
        'status': 'Status',
        'subnet_id': 'SubnetId',
        'update_time': 'UpdateTime',
        'vpc_id': 'VpcId',
        'web_scraper_id': 'WebScraperId',
        'web_scraper_name': 'WebScraperName'
    }

    def __init__(self, billing_type=None, business_status=None, create_time=None, description=None, endpoint=None, lock_reason=None, overdue_time=None, reclaim_time=None, request_id=None, service_port=None, service_type=None, status=None, subnet_id=None, update_time=None, vpc_id=None, web_scraper_id=None, web_scraper_name=None, _configuration=None):  # noqa: E501
        """DescribeWebScraperAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._billing_type = None
        self._business_status = None
        self._create_time = None
        self._description = None
        self._endpoint = None
        self._lock_reason = None
        self._overdue_time = None
        self._reclaim_time = None
        self._request_id = None
        self._service_port = None
        self._service_type = None
        self._status = None
        self._subnet_id = None
        self._update_time = None
        self._vpc_id = None
        self._web_scraper_id = None
        self._web_scraper_name = None
        self.discriminator = None

        if billing_type is not None:
            self.billing_type = billing_type
        if business_status is not None:
            self.business_status = business_status
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if endpoint is not None:
            self.endpoint = endpoint
        if lock_reason is not None:
            self.lock_reason = lock_reason
        if overdue_time is not None:
            self.overdue_time = overdue_time
        if reclaim_time is not None:
            self.reclaim_time = reclaim_time
        if request_id is not None:
            self.request_id = request_id
        if service_port is not None:
            self.service_port = service_port
        if service_type is not None:
            self.service_type = service_type
        if status is not None:
            self.status = status
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if update_time is not None:
            self.update_time = update_time
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if web_scraper_id is not None:
            self.web_scraper_id = web_scraper_id
        if web_scraper_name is not None:
            self.web_scraper_name = web_scraper_name

    @property
    def billing_type(self):
        """Gets the billing_type of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The billing_type of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this DescribeWebScraperAttributesResponse.


        :param billing_type: The billing_type of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: int
        """

        self._billing_type = billing_type

    @property
    def business_status(self):
        """Gets the business_status of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The business_status of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._business_status

    @business_status.setter
    def business_status(self, business_status):
        """Sets the business_status of this DescribeWebScraperAttributesResponse.


        :param business_status: The business_status of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._business_status = business_status

    @property
    def create_time(self):
        """Gets the create_time of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The create_time of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeWebScraperAttributesResponse.


        :param create_time: The create_time of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The description of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeWebScraperAttributesResponse.


        :param description: The description of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def endpoint(self):
        """Gets the endpoint of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The endpoint of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._endpoint

    @endpoint.setter
    def endpoint(self, endpoint):
        """Sets the endpoint of this DescribeWebScraperAttributesResponse.


        :param endpoint: The endpoint of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._endpoint = endpoint

    @property
    def lock_reason(self):
        """Gets the lock_reason of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The lock_reason of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._lock_reason

    @lock_reason.setter
    def lock_reason(self, lock_reason):
        """Sets the lock_reason of this DescribeWebScraperAttributesResponse.


        :param lock_reason: The lock_reason of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._lock_reason = lock_reason

    @property
    def overdue_time(self):
        """Gets the overdue_time of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The overdue_time of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._overdue_time

    @overdue_time.setter
    def overdue_time(self, overdue_time):
        """Sets the overdue_time of this DescribeWebScraperAttributesResponse.


        :param overdue_time: The overdue_time of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._overdue_time = overdue_time

    @property
    def reclaim_time(self):
        """Gets the reclaim_time of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The reclaim_time of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._reclaim_time

    @reclaim_time.setter
    def reclaim_time(self, reclaim_time):
        """Sets the reclaim_time of this DescribeWebScraperAttributesResponse.


        :param reclaim_time: The reclaim_time of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._reclaim_time = reclaim_time

    @property
    def request_id(self):
        """Gets the request_id of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeWebScraperAttributesResponse.


        :param request_id: The request_id of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def service_port(self):
        """Gets the service_port of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The service_port of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._service_port

    @service_port.setter
    def service_port(self, service_port):
        """Sets the service_port of this DescribeWebScraperAttributesResponse.


        :param service_port: The service_port of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: int
        """

        self._service_port = service_port

    @property
    def service_type(self):
        """Gets the service_type of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The service_type of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._service_type

    @service_type.setter
    def service_type(self, service_type):
        """Sets the service_type of this DescribeWebScraperAttributesResponse.


        :param service_type: The service_type of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._service_type = service_type

    @property
    def status(self):
        """Gets the status of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The status of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeWebScraperAttributesResponse.


        :param status: The status of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def subnet_id(self):
        """Gets the subnet_id of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The subnet_id of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this DescribeWebScraperAttributesResponse.


        :param subnet_id: The subnet_id of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def update_time(self):
        """Gets the update_time of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeWebScraperAttributesResponse.


        :param update_time: The update_time of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The vpc_id of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeWebScraperAttributesResponse.


        :param vpc_id: The vpc_id of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def web_scraper_id(self):
        """Gets the web_scraper_id of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The web_scraper_id of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._web_scraper_id

    @web_scraper_id.setter
    def web_scraper_id(self, web_scraper_id):
        """Sets the web_scraper_id of this DescribeWebScraperAttributesResponse.


        :param web_scraper_id: The web_scraper_id of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._web_scraper_id = web_scraper_id

    @property
    def web_scraper_name(self):
        """Gets the web_scraper_name of this DescribeWebScraperAttributesResponse.  # noqa: E501


        :return: The web_scraper_name of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._web_scraper_name

    @web_scraper_name.setter
    def web_scraper_name(self, web_scraper_name):
        """Sets the web_scraper_name of this DescribeWebScraperAttributesResponse.


        :param web_scraper_name: The web_scraper_name of this DescribeWebScraperAttributesResponse.  # noqa: E501
        :type: str
        """

        self._web_scraper_name = web_scraper_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeWebScraperAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeWebScraperAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeWebScraperAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
