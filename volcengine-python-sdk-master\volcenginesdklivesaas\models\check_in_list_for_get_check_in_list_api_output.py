# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckInListForGetCheckInListAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'auto_send_time': 'int',
        'cannot_wathc_live_when_not_check_in': 'int',
        'check_in_count': 'int',
        'check_in_id': 'str',
        'check_in_info': 'str',
        'check_in_name': 'str',
        'comment_content': 'str',
        'deadline': 'int',
        'deadline_second': 'int',
        'is_send_comment': 'int',
        'need_person_info': 'int',
        'open_check_time': 'int',
        'send_type': 'int',
        'start_time': 'str',
        'status': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'auto_send_time': 'AutoSendTime',
        'cannot_wathc_live_when_not_check_in': 'CannotWathcLiveWhenNotCheckIn',
        'check_in_count': 'CheckInCount',
        'check_in_id': 'CheckInId',
        'check_in_info': 'CheckInInfo',
        'check_in_name': 'CheckInName',
        'comment_content': 'CommentContent',
        'deadline': 'Deadline',
        'deadline_second': 'DeadlineSecond',
        'is_send_comment': 'IsSendComment',
        'need_person_info': 'NeedPersonInfo',
        'open_check_time': 'OpenCheckTime',
        'send_type': 'SendType',
        'start_time': 'StartTime',
        'status': 'Status'
    }

    def __init__(self, activity_id=None, auto_send_time=None, cannot_wathc_live_when_not_check_in=None, check_in_count=None, check_in_id=None, check_in_info=None, check_in_name=None, comment_content=None, deadline=None, deadline_second=None, is_send_comment=None, need_person_info=None, open_check_time=None, send_type=None, start_time=None, status=None, _configuration=None):  # noqa: E501
        """CheckInListForGetCheckInListAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._auto_send_time = None
        self._cannot_wathc_live_when_not_check_in = None
        self._check_in_count = None
        self._check_in_id = None
        self._check_in_info = None
        self._check_in_name = None
        self._comment_content = None
        self._deadline = None
        self._deadline_second = None
        self._is_send_comment = None
        self._need_person_info = None
        self._open_check_time = None
        self._send_type = None
        self._start_time = None
        self._status = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if auto_send_time is not None:
            self.auto_send_time = auto_send_time
        if cannot_wathc_live_when_not_check_in is not None:
            self.cannot_wathc_live_when_not_check_in = cannot_wathc_live_when_not_check_in
        if check_in_count is not None:
            self.check_in_count = check_in_count
        if check_in_id is not None:
            self.check_in_id = check_in_id
        if check_in_info is not None:
            self.check_in_info = check_in_info
        if check_in_name is not None:
            self.check_in_name = check_in_name
        if comment_content is not None:
            self.comment_content = comment_content
        if deadline is not None:
            self.deadline = deadline
        if deadline_second is not None:
            self.deadline_second = deadline_second
        if is_send_comment is not None:
            self.is_send_comment = is_send_comment
        if need_person_info is not None:
            self.need_person_info = need_person_info
        if open_check_time is not None:
            self.open_check_time = open_check_time
        if send_type is not None:
            self.send_type = send_type
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status

    @property
    def activity_id(self):
        """Gets the activity_id of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The activity_id of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this CheckInListForGetCheckInListAPIOutput.


        :param activity_id: The activity_id of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def auto_send_time(self):
        """Gets the auto_send_time of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The auto_send_time of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._auto_send_time

    @auto_send_time.setter
    def auto_send_time(self, auto_send_time):
        """Sets the auto_send_time of this CheckInListForGetCheckInListAPIOutput.


        :param auto_send_time: The auto_send_time of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._auto_send_time = auto_send_time

    @property
    def cannot_wathc_live_when_not_check_in(self):
        """Gets the cannot_wathc_live_when_not_check_in of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The cannot_wathc_live_when_not_check_in of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._cannot_wathc_live_when_not_check_in

    @cannot_wathc_live_when_not_check_in.setter
    def cannot_wathc_live_when_not_check_in(self, cannot_wathc_live_when_not_check_in):
        """Sets the cannot_wathc_live_when_not_check_in of this CheckInListForGetCheckInListAPIOutput.


        :param cannot_wathc_live_when_not_check_in: The cannot_wathc_live_when_not_check_in of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._cannot_wathc_live_when_not_check_in = cannot_wathc_live_when_not_check_in

    @property
    def check_in_count(self):
        """Gets the check_in_count of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The check_in_count of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._check_in_count

    @check_in_count.setter
    def check_in_count(self, check_in_count):
        """Sets the check_in_count of this CheckInListForGetCheckInListAPIOutput.


        :param check_in_count: The check_in_count of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._check_in_count = check_in_count

    @property
    def check_in_id(self):
        """Gets the check_in_id of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The check_in_id of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_in_id

    @check_in_id.setter
    def check_in_id(self, check_in_id):
        """Sets the check_in_id of this CheckInListForGetCheckInListAPIOutput.


        :param check_in_id: The check_in_id of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: str
        """

        self._check_in_id = check_in_id

    @property
    def check_in_info(self):
        """Gets the check_in_info of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The check_in_info of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_in_info

    @check_in_info.setter
    def check_in_info(self, check_in_info):
        """Sets the check_in_info of this CheckInListForGetCheckInListAPIOutput.


        :param check_in_info: The check_in_info of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: str
        """

        self._check_in_info = check_in_info

    @property
    def check_in_name(self):
        """Gets the check_in_name of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The check_in_name of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_in_name

    @check_in_name.setter
    def check_in_name(self, check_in_name):
        """Sets the check_in_name of this CheckInListForGetCheckInListAPIOutput.


        :param check_in_name: The check_in_name of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: str
        """

        self._check_in_name = check_in_name

    @property
    def comment_content(self):
        """Gets the comment_content of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The comment_content of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._comment_content

    @comment_content.setter
    def comment_content(self, comment_content):
        """Sets the comment_content of this CheckInListForGetCheckInListAPIOutput.


        :param comment_content: The comment_content of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: str
        """

        self._comment_content = comment_content

    @property
    def deadline(self):
        """Gets the deadline of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The deadline of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._deadline

    @deadline.setter
    def deadline(self, deadline):
        """Sets the deadline of this CheckInListForGetCheckInListAPIOutput.


        :param deadline: The deadline of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._deadline = deadline

    @property
    def deadline_second(self):
        """Gets the deadline_second of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The deadline_second of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._deadline_second

    @deadline_second.setter
    def deadline_second(self, deadline_second):
        """Sets the deadline_second of this CheckInListForGetCheckInListAPIOutput.


        :param deadline_second: The deadline_second of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._deadline_second = deadline_second

    @property
    def is_send_comment(self):
        """Gets the is_send_comment of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The is_send_comment of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_send_comment

    @is_send_comment.setter
    def is_send_comment(self, is_send_comment):
        """Sets the is_send_comment of this CheckInListForGetCheckInListAPIOutput.


        :param is_send_comment: The is_send_comment of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._is_send_comment = is_send_comment

    @property
    def need_person_info(self):
        """Gets the need_person_info of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The need_person_info of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._need_person_info

    @need_person_info.setter
    def need_person_info(self, need_person_info):
        """Sets the need_person_info of this CheckInListForGetCheckInListAPIOutput.


        :param need_person_info: The need_person_info of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._need_person_info = need_person_info

    @property
    def open_check_time(self):
        """Gets the open_check_time of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The open_check_time of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._open_check_time

    @open_check_time.setter
    def open_check_time(self, open_check_time):
        """Sets the open_check_time of this CheckInListForGetCheckInListAPIOutput.


        :param open_check_time: The open_check_time of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._open_check_time = open_check_time

    @property
    def send_type(self):
        """Gets the send_type of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The send_type of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_type

    @send_type.setter
    def send_type(self, send_type):
        """Sets the send_type of this CheckInListForGetCheckInListAPIOutput.


        :param send_type: The send_type of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: int
        """

        self._send_type = send_type

    @property
    def start_time(self):
        """Gets the start_time of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The start_time of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this CheckInListForGetCheckInListAPIOutput.


        :param start_time: The start_time of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501


        :return: The status of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CheckInListForGetCheckInListAPIOutput.


        :param status: The status of this CheckInListForGetCheckInListAPIOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckInListForGetCheckInListAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckInListForGetCheckInListAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckInListForGetCheckInListAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
