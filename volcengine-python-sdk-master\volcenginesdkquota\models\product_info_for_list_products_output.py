# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProductInfoForListProductsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'category_name': 'str',
        'category_name_en': 'str',
        'provider_code': 'str',
        'provider_name': 'str',
        'total_quota': 'int'
    }

    attribute_map = {
        'category_name': 'CategoryName',
        'category_name_en': 'CategoryNameEn',
        'provider_code': 'ProviderCode',
        'provider_name': 'ProviderName',
        'total_quota': 'TotalQuota'
    }

    def __init__(self, category_name=None, category_name_en=None, provider_code=None, provider_name=None, total_quota=None, _configuration=None):  # noqa: E501
        """ProductInfoForListProductsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._category_name = None
        self._category_name_en = None
        self._provider_code = None
        self._provider_name = None
        self._total_quota = None
        self.discriminator = None

        if category_name is not None:
            self.category_name = category_name
        if category_name_en is not None:
            self.category_name_en = category_name_en
        if provider_code is not None:
            self.provider_code = provider_code
        if provider_name is not None:
            self.provider_name = provider_name
        if total_quota is not None:
            self.total_quota = total_quota

    @property
    def category_name(self):
        """Gets the category_name of this ProductInfoForListProductsOutput.  # noqa: E501


        :return: The category_name of this ProductInfoForListProductsOutput.  # noqa: E501
        :rtype: str
        """
        return self._category_name

    @category_name.setter
    def category_name(self, category_name):
        """Sets the category_name of this ProductInfoForListProductsOutput.


        :param category_name: The category_name of this ProductInfoForListProductsOutput.  # noqa: E501
        :type: str
        """

        self._category_name = category_name

    @property
    def category_name_en(self):
        """Gets the category_name_en of this ProductInfoForListProductsOutput.  # noqa: E501


        :return: The category_name_en of this ProductInfoForListProductsOutput.  # noqa: E501
        :rtype: str
        """
        return self._category_name_en

    @category_name_en.setter
    def category_name_en(self, category_name_en):
        """Sets the category_name_en of this ProductInfoForListProductsOutput.


        :param category_name_en: The category_name_en of this ProductInfoForListProductsOutput.  # noqa: E501
        :type: str
        """

        self._category_name_en = category_name_en

    @property
    def provider_code(self):
        """Gets the provider_code of this ProductInfoForListProductsOutput.  # noqa: E501


        :return: The provider_code of this ProductInfoForListProductsOutput.  # noqa: E501
        :rtype: str
        """
        return self._provider_code

    @provider_code.setter
    def provider_code(self, provider_code):
        """Sets the provider_code of this ProductInfoForListProductsOutput.


        :param provider_code: The provider_code of this ProductInfoForListProductsOutput.  # noqa: E501
        :type: str
        """

        self._provider_code = provider_code

    @property
    def provider_name(self):
        """Gets the provider_name of this ProductInfoForListProductsOutput.  # noqa: E501


        :return: The provider_name of this ProductInfoForListProductsOutput.  # noqa: E501
        :rtype: str
        """
        return self._provider_name

    @provider_name.setter
    def provider_name(self, provider_name):
        """Sets the provider_name of this ProductInfoForListProductsOutput.


        :param provider_name: The provider_name of this ProductInfoForListProductsOutput.  # noqa: E501
        :type: str
        """

        self._provider_name = provider_name

    @property
    def total_quota(self):
        """Gets the total_quota of this ProductInfoForListProductsOutput.  # noqa: E501


        :return: The total_quota of this ProductInfoForListProductsOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_quota

    @total_quota.setter
    def total_quota(self, total_quota):
        """Sets the total_quota of this ProductInfoForListProductsOutput.


        :param total_quota: The total_quota of this ProductInfoForListProductsOutput.  # noqa: E501
        :type: int
        """

        self._total_quota = total_quota

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProductInfoForListProductsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProductInfoForListProductsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProductInfoForListProductsOutput):
            return True

        return self.to_dict() != other.to_dict()
