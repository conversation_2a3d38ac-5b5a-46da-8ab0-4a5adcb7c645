# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListClusterUsersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'create_time_order': 'str',
        'createor_name': 'str',
        'keyword': 'str',
        'update_time_order': 'str',
        'user_group_name': 'str',
        'user_name': 'str',
        'user_name_order': 'str',
        'user_names': 'list[str]'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'create_time_order': 'CreateTimeOrder',
        'createor_name': 'CreateorName',
        'keyword': 'Keyword',
        'update_time_order': 'UpdateTimeOrder',
        'user_group_name': 'UserGroupName',
        'user_name': 'UserName',
        'user_name_order': 'UserNameOrder',
        'user_names': 'UserNames'
    }

    def __init__(self, cluster_id=None, create_time_order=None, createor_name=None, keyword=None, update_time_order=None, user_group_name=None, user_name=None, user_name_order=None, user_names=None, _configuration=None):  # noqa: E501
        """ListClusterUsersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._create_time_order = None
        self._createor_name = None
        self._keyword = None
        self._update_time_order = None
        self._user_group_name = None
        self._user_name = None
        self._user_name_order = None
        self._user_names = None
        self.discriminator = None

        self.cluster_id = cluster_id
        if create_time_order is not None:
            self.create_time_order = create_time_order
        if createor_name is not None:
            self.createor_name = createor_name
        if keyword is not None:
            self.keyword = keyword
        if update_time_order is not None:
            self.update_time_order = update_time_order
        if user_group_name is not None:
            self.user_group_name = user_group_name
        if user_name is not None:
            self.user_name = user_name
        if user_name_order is not None:
            self.user_name_order = user_name_order
        if user_names is not None:
            self.user_names = user_names

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListClusterUsersRequest.  # noqa: E501


        :return: The cluster_id of this ListClusterUsersRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListClusterUsersRequest.


        :param cluster_id: The cluster_id of this ListClusterUsersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def create_time_order(self):
        """Gets the create_time_order of this ListClusterUsersRequest.  # noqa: E501


        :return: The create_time_order of this ListClusterUsersRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_order

    @create_time_order.setter
    def create_time_order(self, create_time_order):
        """Sets the create_time_order of this ListClusterUsersRequest.


        :param create_time_order: The create_time_order of this ListClusterUsersRequest.  # noqa: E501
        :type: str
        """

        self._create_time_order = create_time_order

    @property
    def createor_name(self):
        """Gets the createor_name of this ListClusterUsersRequest.  # noqa: E501


        :return: The createor_name of this ListClusterUsersRequest.  # noqa: E501
        :rtype: str
        """
        return self._createor_name

    @createor_name.setter
    def createor_name(self, createor_name):
        """Sets the createor_name of this ListClusterUsersRequest.


        :param createor_name: The createor_name of this ListClusterUsersRequest.  # noqa: E501
        :type: str
        """

        self._createor_name = createor_name

    @property
    def keyword(self):
        """Gets the keyword of this ListClusterUsersRequest.  # noqa: E501


        :return: The keyword of this ListClusterUsersRequest.  # noqa: E501
        :rtype: str
        """
        return self._keyword

    @keyword.setter
    def keyword(self, keyword):
        """Sets the keyword of this ListClusterUsersRequest.


        :param keyword: The keyword of this ListClusterUsersRequest.  # noqa: E501
        :type: str
        """

        self._keyword = keyword

    @property
    def update_time_order(self):
        """Gets the update_time_order of this ListClusterUsersRequest.  # noqa: E501


        :return: The update_time_order of this ListClusterUsersRequest.  # noqa: E501
        :rtype: str
        """
        return self._update_time_order

    @update_time_order.setter
    def update_time_order(self, update_time_order):
        """Sets the update_time_order of this ListClusterUsersRequest.


        :param update_time_order: The update_time_order of this ListClusterUsersRequest.  # noqa: E501
        :type: str
        """

        self._update_time_order = update_time_order

    @property
    def user_group_name(self):
        """Gets the user_group_name of this ListClusterUsersRequest.  # noqa: E501


        :return: The user_group_name of this ListClusterUsersRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_group_name

    @user_group_name.setter
    def user_group_name(self, user_group_name):
        """Sets the user_group_name of this ListClusterUsersRequest.


        :param user_group_name: The user_group_name of this ListClusterUsersRequest.  # noqa: E501
        :type: str
        """

        self._user_group_name = user_group_name

    @property
    def user_name(self):
        """Gets the user_name of this ListClusterUsersRequest.  # noqa: E501


        :return: The user_name of this ListClusterUsersRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this ListClusterUsersRequest.


        :param user_name: The user_name of this ListClusterUsersRequest.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def user_name_order(self):
        """Gets the user_name_order of this ListClusterUsersRequest.  # noqa: E501


        :return: The user_name_order of this ListClusterUsersRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_name_order

    @user_name_order.setter
    def user_name_order(self, user_name_order):
        """Sets the user_name_order of this ListClusterUsersRequest.


        :param user_name_order: The user_name_order of this ListClusterUsersRequest.  # noqa: E501
        :type: str
        """

        self._user_name_order = user_name_order

    @property
    def user_names(self):
        """Gets the user_names of this ListClusterUsersRequest.  # noqa: E501


        :return: The user_names of this ListClusterUsersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._user_names

    @user_names.setter
    def user_names(self, user_names):
        """Sets the user_names of this ListClusterUsersRequest.


        :param user_names: The user_names of this ListClusterUsersRequest.  # noqa: E501
        :type: list[str]
        """

        self._user_names = user_names

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListClusterUsersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListClusterUsersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListClusterUsersRequest):
            return True

        return self.to_dict() != other.to_dict()
