# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateSAMLProviderRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'new_encoded_saml_metadata_document': 'str',
        'saml_provider_name': 'str',
        'status': 'int'
    }

    attribute_map = {
        'description': 'Description',
        'new_encoded_saml_metadata_document': 'NewEncodedSAMLMetadataDocument',
        'saml_provider_name': 'SAMLProviderName',
        'status': 'Status'
    }

    def __init__(self, description=None, new_encoded_saml_metadata_document=None, saml_provider_name=None, status=None, _configuration=None):  # noqa: E501
        """UpdateSAMLProviderRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._new_encoded_saml_metadata_document = None
        self._saml_provider_name = None
        self._status = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if new_encoded_saml_metadata_document is not None:
            self.new_encoded_saml_metadata_document = new_encoded_saml_metadata_document
        self.saml_provider_name = saml_provider_name
        if status is not None:
            self.status = status

    @property
    def description(self):
        """Gets the description of this UpdateSAMLProviderRequest.  # noqa: E501


        :return: The description of this UpdateSAMLProviderRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateSAMLProviderRequest.


        :param description: The description of this UpdateSAMLProviderRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def new_encoded_saml_metadata_document(self):
        """Gets the new_encoded_saml_metadata_document of this UpdateSAMLProviderRequest.  # noqa: E501


        :return: The new_encoded_saml_metadata_document of this UpdateSAMLProviderRequest.  # noqa: E501
        :rtype: str
        """
        return self._new_encoded_saml_metadata_document

    @new_encoded_saml_metadata_document.setter
    def new_encoded_saml_metadata_document(self, new_encoded_saml_metadata_document):
        """Sets the new_encoded_saml_metadata_document of this UpdateSAMLProviderRequest.


        :param new_encoded_saml_metadata_document: The new_encoded_saml_metadata_document of this UpdateSAMLProviderRequest.  # noqa: E501
        :type: str
        """

        self._new_encoded_saml_metadata_document = new_encoded_saml_metadata_document

    @property
    def saml_provider_name(self):
        """Gets the saml_provider_name of this UpdateSAMLProviderRequest.  # noqa: E501


        :return: The saml_provider_name of this UpdateSAMLProviderRequest.  # noqa: E501
        :rtype: str
        """
        return self._saml_provider_name

    @saml_provider_name.setter
    def saml_provider_name(self, saml_provider_name):
        """Sets the saml_provider_name of this UpdateSAMLProviderRequest.


        :param saml_provider_name: The saml_provider_name of this UpdateSAMLProviderRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and saml_provider_name is None:
            raise ValueError("Invalid value for `saml_provider_name`, must not be `None`")  # noqa: E501

        self._saml_provider_name = saml_provider_name

    @property
    def status(self):
        """Gets the status of this UpdateSAMLProviderRequest.  # noqa: E501


        :return: The status of this UpdateSAMLProviderRequest.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this UpdateSAMLProviderRequest.


        :param status: The status of this UpdateSAMLProviderRequest.  # noqa: E501
        :type: int
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateSAMLProviderRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateSAMLProviderRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateSAMLProviderRequest):
            return True

        return self.to_dict() != other.to_dict()
