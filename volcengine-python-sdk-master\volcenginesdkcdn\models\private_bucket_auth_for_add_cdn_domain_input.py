# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PrivateBucketAuthForAddCdnDomainInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_type': 'str',
        'switch': 'bool',
        'tos_auth_information': 'TosAuthInformationForAddCdnDomainInput'
    }

    attribute_map = {
        'auth_type': 'AuthType',
        'switch': 'Switch',
        'tos_auth_information': 'TosAuthInformation'
    }

    def __init__(self, auth_type=None, switch=None, tos_auth_information=None, _configuration=None):  # noqa: E501
        """PrivateBucketAuthForAddCdnDomainInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_type = None
        self._switch = None
        self._tos_auth_information = None
        self.discriminator = None

        if auth_type is not None:
            self.auth_type = auth_type
        if switch is not None:
            self.switch = switch
        if tos_auth_information is not None:
            self.tos_auth_information = tos_auth_information

    @property
    def auth_type(self):
        """Gets the auth_type of this PrivateBucketAuthForAddCdnDomainInput.  # noqa: E501


        :return: The auth_type of this PrivateBucketAuthForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._auth_type

    @auth_type.setter
    def auth_type(self, auth_type):
        """Sets the auth_type of this PrivateBucketAuthForAddCdnDomainInput.


        :param auth_type: The auth_type of this PrivateBucketAuthForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._auth_type = auth_type

    @property
    def switch(self):
        """Gets the switch of this PrivateBucketAuthForAddCdnDomainInput.  # noqa: E501


        :return: The switch of this PrivateBucketAuthForAddCdnDomainInput.  # noqa: E501
        :rtype: bool
        """
        return self._switch

    @switch.setter
    def switch(self, switch):
        """Sets the switch of this PrivateBucketAuthForAddCdnDomainInput.


        :param switch: The switch of this PrivateBucketAuthForAddCdnDomainInput.  # noqa: E501
        :type: bool
        """

        self._switch = switch

    @property
    def tos_auth_information(self):
        """Gets the tos_auth_information of this PrivateBucketAuthForAddCdnDomainInput.  # noqa: E501


        :return: The tos_auth_information of this PrivateBucketAuthForAddCdnDomainInput.  # noqa: E501
        :rtype: TosAuthInformationForAddCdnDomainInput
        """
        return self._tos_auth_information

    @tos_auth_information.setter
    def tos_auth_information(self, tos_auth_information):
        """Sets the tos_auth_information of this PrivateBucketAuthForAddCdnDomainInput.


        :param tos_auth_information: The tos_auth_information of this PrivateBucketAuthForAddCdnDomainInput.  # noqa: E501
        :type: TosAuthInformationForAddCdnDomainInput
        """

        self._tos_auth_information = tos_auth_information

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PrivateBucketAuthForAddCdnDomainInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PrivateBucketAuthForAddCdnDomainInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PrivateBucketAuthForAddCdnDomainInput):
            return True

        return self.to_dict() != other.to_dict()
