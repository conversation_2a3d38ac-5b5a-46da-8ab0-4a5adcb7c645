# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListDXPInstancesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'area': 'str',
        'health_status': 'str',
        'input': 'str',
        'page_number': 'int',
        'page_size': 'str',
        'status_list': 'list[str]'
    }

    attribute_map = {
        'area': 'Area',
        'health_status': 'HealthStatus',
        'input': 'Input',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'status_list': 'StatusList'
    }

    def __init__(self, area=None, health_status=None, input=None, page_number=None, page_size=None, status_list=None, _configuration=None):  # noqa: E501
        """ListDXPInstancesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._area = None
        self._health_status = None
        self._input = None
        self._page_number = None
        self._page_size = None
        self._status_list = None
        self.discriminator = None

        if area is not None:
            self.area = area
        self.health_status = health_status
        if input is not None:
            self.input = input
        self.page_number = page_number
        self.page_size = page_size
        if status_list is not None:
            self.status_list = status_list

    @property
    def area(self):
        """Gets the area of this ListDXPInstancesRequest.  # noqa: E501


        :return: The area of this ListDXPInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._area

    @area.setter
    def area(self, area):
        """Sets the area of this ListDXPInstancesRequest.


        :param area: The area of this ListDXPInstancesRequest.  # noqa: E501
        :type: str
        """

        self._area = area

    @property
    def health_status(self):
        """Gets the health_status of this ListDXPInstancesRequest.  # noqa: E501


        :return: The health_status of this ListDXPInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._health_status

    @health_status.setter
    def health_status(self, health_status):
        """Sets the health_status of this ListDXPInstancesRequest.


        :param health_status: The health_status of this ListDXPInstancesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and health_status is None:
            raise ValueError("Invalid value for `health_status`, must not be `None`")  # noqa: E501

        self._health_status = health_status

    @property
    def input(self):
        """Gets the input of this ListDXPInstancesRequest.  # noqa: E501


        :return: The input of this ListDXPInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._input

    @input.setter
    def input(self, input):
        """Sets the input of this ListDXPInstancesRequest.


        :param input: The input of this ListDXPInstancesRequest.  # noqa: E501
        :type: str
        """

        self._input = input

    @property
    def page_number(self):
        """Gets the page_number of this ListDXPInstancesRequest.  # noqa: E501


        :return: The page_number of this ListDXPInstancesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListDXPInstancesRequest.


        :param page_number: The page_number of this ListDXPInstancesRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListDXPInstancesRequest.  # noqa: E501


        :return: The page_size of this ListDXPInstancesRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListDXPInstancesRequest.


        :param page_size: The page_size of this ListDXPInstancesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def status_list(self):
        """Gets the status_list of this ListDXPInstancesRequest.  # noqa: E501


        :return: The status_list of this ListDXPInstancesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status_list

    @status_list.setter
    def status_list(self, status_list):
        """Sets the status_list of this ListDXPInstancesRequest.


        :param status_list: The status_list of this ListDXPInstancesRequest.  # noqa: E501
        :type: list[str]
        """

        self._status_list = status_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListDXPInstancesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListDXPInstancesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListDXPInstancesRequest):
            return True

        return self.to_dict() != other.to_dict()
