# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ServerNodeForDescribeDBInstanceShardsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'current_role': 'str',
        'node_id': 'str',
        'status': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'current_role': 'CurrentRole',
        'node_id': 'NodeId',
        'status': 'Status',
        'zone_id': 'ZoneId'
    }

    def __init__(self, current_role=None, node_id=None, status=None, zone_id=None, _configuration=None):  # noqa: E501
        """ServerNodeForDescribeDBInstanceShardsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._current_role = None
        self._node_id = None
        self._status = None
        self._zone_id = None
        self.discriminator = None

        if current_role is not None:
            self.current_role = current_role
        if node_id is not None:
            self.node_id = node_id
        if status is not None:
            self.status = status
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def current_role(self):
        """Gets the current_role of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501


        :return: The current_role of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501
        :rtype: str
        """
        return self._current_role

    @current_role.setter
    def current_role(self, current_role):
        """Sets the current_role of this ServerNodeForDescribeDBInstanceShardsOutput.


        :param current_role: The current_role of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501
        :type: str
        """

        self._current_role = current_role

    @property
    def node_id(self):
        """Gets the node_id of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501


        :return: The node_id of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this ServerNodeForDescribeDBInstanceShardsOutput.


        :param node_id: The node_id of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def status(self):
        """Gets the status of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501


        :return: The status of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ServerNodeForDescribeDBInstanceShardsOutput.


        :param status: The status of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def zone_id(self):
        """Gets the zone_id of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501


        :return: The zone_id of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ServerNodeForDescribeDBInstanceShardsOutput.


        :param zone_id: The zone_id of this ServerNodeForDescribeDBInstanceShardsOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ServerNodeForDescribeDBInstanceShardsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ServerNodeForDescribeDBInstanceShardsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ServerNodeForDescribeDBInstanceShardsOutput):
            return True

        return self.to_dict() != other.to_dict()
