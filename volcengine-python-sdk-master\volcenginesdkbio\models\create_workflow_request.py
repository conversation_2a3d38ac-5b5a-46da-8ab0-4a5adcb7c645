# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateWorkflowRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'content': 'str',
        'description': 'str',
        'language': 'str',
        'main_workflow_path': 'str',
        'name': 'str',
        'source': 'str',
        'source_type': 'str',
        'tag': 'str',
        'token': 'str',
        'workspace_id': 'str'
    }

    attribute_map = {
        'content': 'Content',
        'description': 'Description',
        'language': 'Language',
        'main_workflow_path': 'MainWorkflowPath',
        'name': 'Name',
        'source': 'Source',
        'source_type': 'SourceType',
        'tag': 'Tag',
        'token': 'Token',
        'workspace_id': 'WorkspaceID'
    }

    def __init__(self, content=None, description=None, language=None, main_workflow_path=None, name=None, source=None, source_type=None, tag=None, token=None, workspace_id=None, _configuration=None):  # noqa: E501
        """CreateWorkflowRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._content = None
        self._description = None
        self._language = None
        self._main_workflow_path = None
        self._name = None
        self._source = None
        self._source_type = None
        self._tag = None
        self._token = None
        self._workspace_id = None
        self.discriminator = None

        if content is not None:
            self.content = content
        if description is not None:
            self.description = description
        self.language = language
        if main_workflow_path is not None:
            self.main_workflow_path = main_workflow_path
        self.name = name
        if source is not None:
            self.source = source
        self.source_type = source_type
        if tag is not None:
            self.tag = tag
        if token is not None:
            self.token = token
        self.workspace_id = workspace_id

    @property
    def content(self):
        """Gets the content of this CreateWorkflowRequest.  # noqa: E501


        :return: The content of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._content

    @content.setter
    def content(self, content):
        """Sets the content of this CreateWorkflowRequest.


        :param content: The content of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """

        self._content = content

    @property
    def description(self):
        """Gets the description of this CreateWorkflowRequest.  # noqa: E501


        :return: The description of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateWorkflowRequest.


        :param description: The description of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 1000):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `1000`")  # noqa: E501

        self._description = description

    @property
    def language(self):
        """Gets the language of this CreateWorkflowRequest.  # noqa: E501


        :return: The language of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._language

    @language.setter
    def language(self, language):
        """Sets the language of this CreateWorkflowRequest.


        :param language: The language of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and language is None:
            raise ValueError("Invalid value for `language`, must not be `None`")  # noqa: E501
        allowed_values = ["WDL", "CWL", "Nextflow"]  # noqa: E501
        if (self._configuration.client_side_validation and
                language not in allowed_values):
            raise ValueError(
                "Invalid value for `language` ({0}), must be one of {1}"  # noqa: E501
                .format(language, allowed_values)
            )

        self._language = language

    @property
    def main_workflow_path(self):
        """Gets the main_workflow_path of this CreateWorkflowRequest.  # noqa: E501


        :return: The main_workflow_path of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._main_workflow_path

    @main_workflow_path.setter
    def main_workflow_path(self, main_workflow_path):
        """Sets the main_workflow_path of this CreateWorkflowRequest.


        :param main_workflow_path: The main_workflow_path of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """

        self._main_workflow_path = main_workflow_path

    @property
    def name(self):
        """Gets the name of this CreateWorkflowRequest.  # noqa: E501


        :return: The name of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateWorkflowRequest.


        :param name: The name of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) > 200):
            raise ValueError("Invalid value for `name`, length must be less than or equal to `200`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) < 1):
            raise ValueError("Invalid value for `name`, length must be greater than or equal to `1`")  # noqa: E501

        self._name = name

    @property
    def source(self):
        """Gets the source of this CreateWorkflowRequest.  # noqa: E501


        :return: The source of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this CreateWorkflowRequest.


        :param source: The source of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """

        self._source = source

    @property
    def source_type(self):
        """Gets the source_type of this CreateWorkflowRequest.  # noqa: E501


        :return: The source_type of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this CreateWorkflowRequest.


        :param source_type: The source_type of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and source_type is None:
            raise ValueError("Invalid value for `source_type`, must not be `None`")  # noqa: E501

        self._source_type = source_type

    @property
    def tag(self):
        """Gets the tag of this CreateWorkflowRequest.  # noqa: E501


        :return: The tag of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this CreateWorkflowRequest.


        :param tag: The tag of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def token(self):
        """Gets the token of this CreateWorkflowRequest.  # noqa: E501


        :return: The token of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._token

    @token.setter
    def token(self, token):
        """Sets the token of this CreateWorkflowRequest.


        :param token: The token of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """

        self._token = token

    @property
    def workspace_id(self):
        """Gets the workspace_id of this CreateWorkflowRequest.  # noqa: E501


        :return: The workspace_id of this CreateWorkflowRequest.  # noqa: E501
        :rtype: str
        """
        return self._workspace_id

    @workspace_id.setter
    def workspace_id(self, workspace_id):
        """Sets the workspace_id of this CreateWorkflowRequest.


        :param workspace_id: The workspace_id of this CreateWorkflowRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and workspace_id is None:
            raise ValueError("Invalid value for `workspace_id`, must not be `None`")  # noqa: E501

        self._workspace_id = workspace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateWorkflowRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateWorkflowRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateWorkflowRequest):
            return True

        return self.to_dict() != other.to_dict()
