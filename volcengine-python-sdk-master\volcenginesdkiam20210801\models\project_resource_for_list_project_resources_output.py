# coding: utf-8

"""
    iam20210801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProjectResourceForListProjectResourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'int',
        'create_date': 'str',
        'project_name': 'str',
        'resource_id': 'str',
        'resource_region': 'str',
        'resource_trn': 'str',
        'resource_type': 'str',
        'service_name': 'str',
        'update_date': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'create_date': 'CreateDate',
        'project_name': 'ProjectName',
        'resource_id': 'ResourceID',
        'resource_region': 'ResourceRegion',
        'resource_trn': 'ResourceTrn',
        'resource_type': 'ResourceType',
        'service_name': 'ServiceName',
        'update_date': 'UpdateDate'
    }

    def __init__(self, account_id=None, create_date=None, project_name=None, resource_id=None, resource_region=None, resource_trn=None, resource_type=None, service_name=None, update_date=None, _configuration=None):  # noqa: E501
        """ProjectResourceForListProjectResourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._create_date = None
        self._project_name = None
        self._resource_id = None
        self._resource_region = None
        self._resource_trn = None
        self._resource_type = None
        self._service_name = None
        self._update_date = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if create_date is not None:
            self.create_date = create_date
        if project_name is not None:
            self.project_name = project_name
        if resource_id is not None:
            self.resource_id = resource_id
        if resource_region is not None:
            self.resource_region = resource_region
        if resource_trn is not None:
            self.resource_trn = resource_trn
        if resource_type is not None:
            self.resource_type = resource_type
        if service_name is not None:
            self.service_name = service_name
        if update_date is not None:
            self.update_date = update_date

    @property
    def account_id(self):
        """Gets the account_id of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501


        :return: The account_id of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this ProjectResourceForListProjectResourcesOutput.


        :param account_id: The account_id of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :type: int
        """

        self._account_id = account_id

    @property
    def create_date(self):
        """Gets the create_date of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501


        :return: The create_date of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_date

    @create_date.setter
    def create_date(self, create_date):
        """Sets the create_date of this ProjectResourceForListProjectResourcesOutput.


        :param create_date: The create_date of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :type: str
        """

        self._create_date = create_date

    @property
    def project_name(self):
        """Gets the project_name of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501


        :return: The project_name of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ProjectResourceForListProjectResourcesOutput.


        :param project_name: The project_name of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resource_id(self):
        """Gets the resource_id of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501


        :return: The resource_id of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this ProjectResourceForListProjectResourcesOutput.


        :param resource_id: The resource_id of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def resource_region(self):
        """Gets the resource_region of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501


        :return: The resource_region of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_region

    @resource_region.setter
    def resource_region(self, resource_region):
        """Sets the resource_region of this ProjectResourceForListProjectResourcesOutput.


        :param resource_region: The resource_region of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_region = resource_region

    @property
    def resource_trn(self):
        """Gets the resource_trn of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501


        :return: The resource_trn of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_trn

    @resource_trn.setter
    def resource_trn(self, resource_trn):
        """Sets the resource_trn of this ProjectResourceForListProjectResourcesOutput.


        :param resource_trn: The resource_trn of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_trn = resource_trn

    @property
    def resource_type(self):
        """Gets the resource_type of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501


        :return: The resource_type of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ProjectResourceForListProjectResourcesOutput.


        :param resource_type: The resource_type of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def service_name(self):
        """Gets the service_name of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501


        :return: The service_name of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this ProjectResourceForListProjectResourcesOutput.


        :param service_name: The service_name of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :type: str
        """

        self._service_name = service_name

    @property
    def update_date(self):
        """Gets the update_date of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501


        :return: The update_date of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_date

    @update_date.setter
    def update_date(self, update_date):
        """Sets the update_date of this ProjectResourceForListProjectResourcesOutput.


        :param update_date: The update_date of this ProjectResourceForListProjectResourcesOutput.  # noqa: E501
        :type: str
        """

        self._update_date = update_date

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProjectResourceForListProjectResourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProjectResourceForListProjectResourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProjectResourceForListProjectResourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
