# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class WorkloadInfoForUpdateResourceQueueInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'default_priority': 'int',
        'workload_type': 'str'
    }

    attribute_map = {
        'default_priority': 'DefaultPriority',
        'workload_type': 'WorkloadType'
    }

    def __init__(self, default_priority=None, workload_type=None, _configuration=None):  # noqa: E501
        """WorkloadInfoForUpdateResourceQueueInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._default_priority = None
        self._workload_type = None
        self.discriminator = None

        if default_priority is not None:
            self.default_priority = default_priority
        if workload_type is not None:
            self.workload_type = workload_type

    @property
    def default_priority(self):
        """Gets the default_priority of this WorkloadInfoForUpdateResourceQueueInput.  # noqa: E501


        :return: The default_priority of this WorkloadInfoForUpdateResourceQueueInput.  # noqa: E501
        :rtype: int
        """
        return self._default_priority

    @default_priority.setter
    def default_priority(self, default_priority):
        """Sets the default_priority of this WorkloadInfoForUpdateResourceQueueInput.


        :param default_priority: The default_priority of this WorkloadInfoForUpdateResourceQueueInput.  # noqa: E501
        :type: int
        """

        self._default_priority = default_priority

    @property
    def workload_type(self):
        """Gets the workload_type of this WorkloadInfoForUpdateResourceQueueInput.  # noqa: E501


        :return: The workload_type of this WorkloadInfoForUpdateResourceQueueInput.  # noqa: E501
        :rtype: str
        """
        return self._workload_type

    @workload_type.setter
    def workload_type(self, workload_type):
        """Sets the workload_type of this WorkloadInfoForUpdateResourceQueueInput.


        :param workload_type: The workload_type of this WorkloadInfoForUpdateResourceQueueInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Job", "DevInstance", "Service"]  # noqa: E501
        if (self._configuration.client_side_validation and
                workload_type not in allowed_values):
            raise ValueError(
                "Invalid value for `workload_type` ({0}), must be one of {1}"  # noqa: E501
                .format(workload_type, allowed_values)
            )

        self._workload_type = workload_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(WorkloadInfoForUpdateResourceQueueInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WorkloadInfoForUpdateResourceQueueInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WorkloadInfoForUpdateResourceQueueInput):
            return True

        return self.to_dict() != other.to_dict()
