# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetRiskResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affect_asset_count': 'int',
        'reference_data': 'ReferenceDataForGetRiskOutput',
        'repair_proposal_type': 'str',
        'risk_base_info': 'RiskBaseInfoForGetRiskOutput',
        'strategy': 'StrategyForGetRiskOutput',
        'strategy_risk_resource_config_infos': 'list[StrategyRiskResourceConfigInfoForGetRiskOutput]',
        'vuln': 'VulnForGetRiskOutput'
    }

    attribute_map = {
        'affect_asset_count': 'AffectAssetCount',
        'reference_data': 'ReferenceData',
        'repair_proposal_type': 'RepairProposalType',
        'risk_base_info': 'RiskBaseInfo',
        'strategy': 'Strategy',
        'strategy_risk_resource_config_infos': 'StrategyRiskResourceConfigInfos',
        'vuln': 'Vuln'
    }

    def __init__(self, affect_asset_count=None, reference_data=None, repair_proposal_type=None, risk_base_info=None, strategy=None, strategy_risk_resource_config_infos=None, vuln=None, _configuration=None):  # noqa: E501
        """GetRiskResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affect_asset_count = None
        self._reference_data = None
        self._repair_proposal_type = None
        self._risk_base_info = None
        self._strategy = None
        self._strategy_risk_resource_config_infos = None
        self._vuln = None
        self.discriminator = None

        if affect_asset_count is not None:
            self.affect_asset_count = affect_asset_count
        if reference_data is not None:
            self.reference_data = reference_data
        if repair_proposal_type is not None:
            self.repair_proposal_type = repair_proposal_type
        if risk_base_info is not None:
            self.risk_base_info = risk_base_info
        if strategy is not None:
            self.strategy = strategy
        if strategy_risk_resource_config_infos is not None:
            self.strategy_risk_resource_config_infos = strategy_risk_resource_config_infos
        if vuln is not None:
            self.vuln = vuln

    @property
    def affect_asset_count(self):
        """Gets the affect_asset_count of this GetRiskResponse.  # noqa: E501


        :return: The affect_asset_count of this GetRiskResponse.  # noqa: E501
        :rtype: int
        """
        return self._affect_asset_count

    @affect_asset_count.setter
    def affect_asset_count(self, affect_asset_count):
        """Sets the affect_asset_count of this GetRiskResponse.


        :param affect_asset_count: The affect_asset_count of this GetRiskResponse.  # noqa: E501
        :type: int
        """

        self._affect_asset_count = affect_asset_count

    @property
    def reference_data(self):
        """Gets the reference_data of this GetRiskResponse.  # noqa: E501


        :return: The reference_data of this GetRiskResponse.  # noqa: E501
        :rtype: ReferenceDataForGetRiskOutput
        """
        return self._reference_data

    @reference_data.setter
    def reference_data(self, reference_data):
        """Sets the reference_data of this GetRiskResponse.


        :param reference_data: The reference_data of this GetRiskResponse.  # noqa: E501
        :type: ReferenceDataForGetRiskOutput
        """

        self._reference_data = reference_data

    @property
    def repair_proposal_type(self):
        """Gets the repair_proposal_type of this GetRiskResponse.  # noqa: E501


        :return: The repair_proposal_type of this GetRiskResponse.  # noqa: E501
        :rtype: str
        """
        return self._repair_proposal_type

    @repair_proposal_type.setter
    def repair_proposal_type(self, repair_proposal_type):
        """Sets the repair_proposal_type of this GetRiskResponse.


        :param repair_proposal_type: The repair_proposal_type of this GetRiskResponse.  # noqa: E501
        :type: str
        """

        self._repair_proposal_type = repair_proposal_type

    @property
    def risk_base_info(self):
        """Gets the risk_base_info of this GetRiskResponse.  # noqa: E501


        :return: The risk_base_info of this GetRiskResponse.  # noqa: E501
        :rtype: RiskBaseInfoForGetRiskOutput
        """
        return self._risk_base_info

    @risk_base_info.setter
    def risk_base_info(self, risk_base_info):
        """Sets the risk_base_info of this GetRiskResponse.


        :param risk_base_info: The risk_base_info of this GetRiskResponse.  # noqa: E501
        :type: RiskBaseInfoForGetRiskOutput
        """

        self._risk_base_info = risk_base_info

    @property
    def strategy(self):
        """Gets the strategy of this GetRiskResponse.  # noqa: E501


        :return: The strategy of this GetRiskResponse.  # noqa: E501
        :rtype: StrategyForGetRiskOutput
        """
        return self._strategy

    @strategy.setter
    def strategy(self, strategy):
        """Sets the strategy of this GetRiskResponse.


        :param strategy: The strategy of this GetRiskResponse.  # noqa: E501
        :type: StrategyForGetRiskOutput
        """

        self._strategy = strategy

    @property
    def strategy_risk_resource_config_infos(self):
        """Gets the strategy_risk_resource_config_infos of this GetRiskResponse.  # noqa: E501


        :return: The strategy_risk_resource_config_infos of this GetRiskResponse.  # noqa: E501
        :rtype: list[StrategyRiskResourceConfigInfoForGetRiskOutput]
        """
        return self._strategy_risk_resource_config_infos

    @strategy_risk_resource_config_infos.setter
    def strategy_risk_resource_config_infos(self, strategy_risk_resource_config_infos):
        """Sets the strategy_risk_resource_config_infos of this GetRiskResponse.


        :param strategy_risk_resource_config_infos: The strategy_risk_resource_config_infos of this GetRiskResponse.  # noqa: E501
        :type: list[StrategyRiskResourceConfigInfoForGetRiskOutput]
        """

        self._strategy_risk_resource_config_infos = strategy_risk_resource_config_infos

    @property
    def vuln(self):
        """Gets the vuln of this GetRiskResponse.  # noqa: E501


        :return: The vuln of this GetRiskResponse.  # noqa: E501
        :rtype: VulnForGetRiskOutput
        """
        return self._vuln

    @vuln.setter
    def vuln(self, vuln):
        """Sets the vuln of this GetRiskResponse.


        :param vuln: The vuln of this GetRiskResponse.  # noqa: E501
        :type: VulnForGetRiskOutput
        """

        self._vuln = vuln

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetRiskResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetRiskResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetRiskResponse):
            return True

        return self.to_dict() != other.to_dict()
