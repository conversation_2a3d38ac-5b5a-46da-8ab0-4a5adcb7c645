# coding: utf-8

"""
    iam20210801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListProjectResourcesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'limit': 'int',
        'offset': 'int',
        'order_by': 'str',
        'project_name': 'str',
        'query': 'str',
        'resource_region': 'str',
        'resource_type': 'str',
        'service_name': 'str',
        'sort': 'int',
        'with_sub_project_resource': 'int'
    }

    attribute_map = {
        'limit': 'Limit',
        'offset': 'Offset',
        'order_by': 'OrderBy',
        'project_name': 'ProjectName',
        'query': 'Query',
        'resource_region': 'ResourceRegion',
        'resource_type': 'ResourceType',
        'service_name': 'ServiceName',
        'sort': 'Sort',
        'with_sub_project_resource': 'WithSubProjectResource'
    }

    def __init__(self, limit=None, offset=None, order_by=None, project_name=None, query=None, resource_region=None, resource_type=None, service_name=None, sort=None, with_sub_project_resource=None, _configuration=None):  # noqa: E501
        """ListProjectResourcesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._limit = None
        self._offset = None
        self._order_by = None
        self._project_name = None
        self._query = None
        self._resource_region = None
        self._resource_type = None
        self._service_name = None
        self._sort = None
        self._with_sub_project_resource = None
        self.discriminator = None

        if limit is not None:
            self.limit = limit
        if offset is not None:
            self.offset = offset
        if order_by is not None:
            self.order_by = order_by
        self.project_name = project_name
        if query is not None:
            self.query = query
        if resource_region is not None:
            self.resource_region = resource_region
        if resource_type is not None:
            self.resource_type = resource_type
        if service_name is not None:
            self.service_name = service_name
        if sort is not None:
            self.sort = sort
        if with_sub_project_resource is not None:
            self.with_sub_project_resource = with_sub_project_resource

    @property
    def limit(self):
        """Gets the limit of this ListProjectResourcesRequest.  # noqa: E501


        :return: The limit of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListProjectResourcesRequest.


        :param limit: The limit of this ListProjectResourcesRequest.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def offset(self):
        """Gets the offset of this ListProjectResourcesRequest.  # noqa: E501


        :return: The offset of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this ListProjectResourcesRequest.


        :param offset: The offset of this ListProjectResourcesRequest.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def order_by(self):
        """Gets the order_by of this ListProjectResourcesRequest.  # noqa: E501


        :return: The order_by of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_by

    @order_by.setter
    def order_by(self, order_by):
        """Sets the order_by of this ListProjectResourcesRequest.


        :param order_by: The order_by of this ListProjectResourcesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["CreatedTime", "UpdatedTime"]  # noqa: E501
        if (self._configuration.client_side_validation and
                order_by not in allowed_values):
            raise ValueError(
                "Invalid value for `order_by` ({0}), must be one of {1}"  # noqa: E501
                .format(order_by, allowed_values)
            )

        self._order_by = order_by

    @property
    def project_name(self):
        """Gets the project_name of this ListProjectResourcesRequest.  # noqa: E501


        :return: The project_name of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListProjectResourcesRequest.


        :param project_name: The project_name of this ListProjectResourcesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and project_name is None:
            raise ValueError("Invalid value for `project_name`, must not be `None`")  # noqa: E501

        self._project_name = project_name

    @property
    def query(self):
        """Gets the query of this ListProjectResourcesRequest.  # noqa: E501


        :return: The query of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._query

    @query.setter
    def query(self, query):
        """Sets the query of this ListProjectResourcesRequest.


        :param query: The query of this ListProjectResourcesRequest.  # noqa: E501
        :type: str
        """

        self._query = query

    @property
    def resource_region(self):
        """Gets the resource_region of this ListProjectResourcesRequest.  # noqa: E501


        :return: The resource_region of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_region

    @resource_region.setter
    def resource_region(self, resource_region):
        """Sets the resource_region of this ListProjectResourcesRequest.


        :param resource_region: The resource_region of this ListProjectResourcesRequest.  # noqa: E501
        :type: str
        """

        self._resource_region = resource_region

    @property
    def resource_type(self):
        """Gets the resource_type of this ListProjectResourcesRequest.  # noqa: E501


        :return: The resource_type of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ListProjectResourcesRequest.


        :param resource_type: The resource_type of this ListProjectResourcesRequest.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def service_name(self):
        """Gets the service_name of this ListProjectResourcesRequest.  # noqa: E501


        :return: The service_name of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this ListProjectResourcesRequest.


        :param service_name: The service_name of this ListProjectResourcesRequest.  # noqa: E501
        :type: str
        """

        self._service_name = service_name

    @property
    def sort(self):
        """Gets the sort of this ListProjectResourcesRequest.  # noqa: E501


        :return: The sort of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: int
        """
        return self._sort

    @sort.setter
    def sort(self, sort):
        """Sets the sort of this ListProjectResourcesRequest.


        :param sort: The sort of this ListProjectResourcesRequest.  # noqa: E501
        :type: int
        """

        self._sort = sort

    @property
    def with_sub_project_resource(self):
        """Gets the with_sub_project_resource of this ListProjectResourcesRequest.  # noqa: E501


        :return: The with_sub_project_resource of this ListProjectResourcesRequest.  # noqa: E501
        :rtype: int
        """
        return self._with_sub_project_resource

    @with_sub_project_resource.setter
    def with_sub_project_resource(self, with_sub_project_resource):
        """Sets the with_sub_project_resource of this ListProjectResourcesRequest.


        :param with_sub_project_resource: The with_sub_project_resource of this ListProjectResourcesRequest.  # noqa: E501
        :type: int
        """

        self._with_sub_project_resource = with_sub_project_resource

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListProjectResourcesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListProjectResourcesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListProjectResourcesRequest):
            return True

        return self.to_dict() != other.to_dict()
