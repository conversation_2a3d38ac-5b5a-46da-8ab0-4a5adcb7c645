# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateActivityMessageConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'is_custom_message_enable': 'int',
        'is_dynamic_emoji_message_enable': 'int',
        'is_entry_message_enable': 'int',
        'is_gift_message_enable': 'int',
        'is_lottery_message_enable': 'int',
        'is_red_packet_message_enable': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'is_custom_message_enable': 'IsCustomMessageEnable',
        'is_dynamic_emoji_message_enable': 'IsDynamicEmojiMessageEnable',
        'is_entry_message_enable': 'IsEntryMessageEnable',
        'is_gift_message_enable': 'IsGiftMessageEnable',
        'is_lottery_message_enable': 'IsLotteryMessageEnable',
        'is_red_packet_message_enable': 'IsRedPacketMessageEnable'
    }

    def __init__(self, activity_id=None, is_custom_message_enable=None, is_dynamic_emoji_message_enable=None, is_entry_message_enable=None, is_gift_message_enable=None, is_lottery_message_enable=None, is_red_packet_message_enable=None, _configuration=None):  # noqa: E501
        """UpdateActivityMessageConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._is_custom_message_enable = None
        self._is_dynamic_emoji_message_enable = None
        self._is_entry_message_enable = None
        self._is_gift_message_enable = None
        self._is_lottery_message_enable = None
        self._is_red_packet_message_enable = None
        self.discriminator = None

        self.activity_id = activity_id
        if is_custom_message_enable is not None:
            self.is_custom_message_enable = is_custom_message_enable
        if is_dynamic_emoji_message_enable is not None:
            self.is_dynamic_emoji_message_enable = is_dynamic_emoji_message_enable
        if is_entry_message_enable is not None:
            self.is_entry_message_enable = is_entry_message_enable
        if is_gift_message_enable is not None:
            self.is_gift_message_enable = is_gift_message_enable
        if is_lottery_message_enable is not None:
            self.is_lottery_message_enable = is_lottery_message_enable
        if is_red_packet_message_enable is not None:
            self.is_red_packet_message_enable = is_red_packet_message_enable

    @property
    def activity_id(self):
        """Gets the activity_id of this UpdateActivityMessageConfigRequest.  # noqa: E501


        :return: The activity_id of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this UpdateActivityMessageConfigRequest.


        :param activity_id: The activity_id of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def is_custom_message_enable(self):
        """Gets the is_custom_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501


        :return: The is_custom_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_custom_message_enable

    @is_custom_message_enable.setter
    def is_custom_message_enable(self, is_custom_message_enable):
        """Sets the is_custom_message_enable of this UpdateActivityMessageConfigRequest.


        :param is_custom_message_enable: The is_custom_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :type: int
        """

        self._is_custom_message_enable = is_custom_message_enable

    @property
    def is_dynamic_emoji_message_enable(self):
        """Gets the is_dynamic_emoji_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501


        :return: The is_dynamic_emoji_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_dynamic_emoji_message_enable

    @is_dynamic_emoji_message_enable.setter
    def is_dynamic_emoji_message_enable(self, is_dynamic_emoji_message_enable):
        """Sets the is_dynamic_emoji_message_enable of this UpdateActivityMessageConfigRequest.


        :param is_dynamic_emoji_message_enable: The is_dynamic_emoji_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :type: int
        """

        self._is_dynamic_emoji_message_enable = is_dynamic_emoji_message_enable

    @property
    def is_entry_message_enable(self):
        """Gets the is_entry_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501


        :return: The is_entry_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_entry_message_enable

    @is_entry_message_enable.setter
    def is_entry_message_enable(self, is_entry_message_enable):
        """Sets the is_entry_message_enable of this UpdateActivityMessageConfigRequest.


        :param is_entry_message_enable: The is_entry_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :type: int
        """

        self._is_entry_message_enable = is_entry_message_enable

    @property
    def is_gift_message_enable(self):
        """Gets the is_gift_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501


        :return: The is_gift_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_gift_message_enable

    @is_gift_message_enable.setter
    def is_gift_message_enable(self, is_gift_message_enable):
        """Sets the is_gift_message_enable of this UpdateActivityMessageConfigRequest.


        :param is_gift_message_enable: The is_gift_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :type: int
        """

        self._is_gift_message_enable = is_gift_message_enable

    @property
    def is_lottery_message_enable(self):
        """Gets the is_lottery_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501


        :return: The is_lottery_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_lottery_message_enable

    @is_lottery_message_enable.setter
    def is_lottery_message_enable(self, is_lottery_message_enable):
        """Sets the is_lottery_message_enable of this UpdateActivityMessageConfigRequest.


        :param is_lottery_message_enable: The is_lottery_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :type: int
        """

        self._is_lottery_message_enable = is_lottery_message_enable

    @property
    def is_red_packet_message_enable(self):
        """Gets the is_red_packet_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501


        :return: The is_red_packet_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._is_red_packet_message_enable

    @is_red_packet_message_enable.setter
    def is_red_packet_message_enable(self, is_red_packet_message_enable):
        """Sets the is_red_packet_message_enable of this UpdateActivityMessageConfigRequest.


        :param is_red_packet_message_enable: The is_red_packet_message_enable of this UpdateActivityMessageConfigRequest.  # noqa: E501
        :type: int
        """

        self._is_red_packet_message_enable = is_red_packet_message_enable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateActivityMessageConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateActivityMessageConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateActivityMessageConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
