# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ClusterListForListWhiteListsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'cluster_name': 'str',
        'cluster_region': 'str',
        'cluster_tags': 'list[str]'
    }

    attribute_map = {
        'cluster_id': 'ClusterID',
        'cluster_name': 'ClusterName',
        'cluster_region': 'ClusterRegion',
        'cluster_tags': 'ClusterTags'
    }

    def __init__(self, cluster_id=None, cluster_name=None, cluster_region=None, cluster_tags=None, _configuration=None):  # noqa: E501
        """ClusterListForListWhiteListsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._cluster_name = None
        self._cluster_region = None
        self._cluster_tags = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if cluster_region is not None:
            self.cluster_region = cluster_region
        if cluster_tags is not None:
            self.cluster_tags = cluster_tags

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ClusterListForListWhiteListsOutput.  # noqa: E501


        :return: The cluster_id of this ClusterListForListWhiteListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ClusterListForListWhiteListsOutput.


        :param cluster_id: The cluster_id of this ClusterListForListWhiteListsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this ClusterListForListWhiteListsOutput.  # noqa: E501


        :return: The cluster_name of this ClusterListForListWhiteListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this ClusterListForListWhiteListsOutput.


        :param cluster_name: The cluster_name of this ClusterListForListWhiteListsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def cluster_region(self):
        """Gets the cluster_region of this ClusterListForListWhiteListsOutput.  # noqa: E501


        :return: The cluster_region of this ClusterListForListWhiteListsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_region

    @cluster_region.setter
    def cluster_region(self, cluster_region):
        """Sets the cluster_region of this ClusterListForListWhiteListsOutput.


        :param cluster_region: The cluster_region of this ClusterListForListWhiteListsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_region = cluster_region

    @property
    def cluster_tags(self):
        """Gets the cluster_tags of this ClusterListForListWhiteListsOutput.  # noqa: E501


        :return: The cluster_tags of this ClusterListForListWhiteListsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_tags

    @cluster_tags.setter
    def cluster_tags(self, cluster_tags):
        """Sets the cluster_tags of this ClusterListForListWhiteListsOutput.


        :param cluster_tags: The cluster_tags of this ClusterListForListWhiteListsOutput.  # noqa: E501
        :type: list[str]
        """

        self._cluster_tags = cluster_tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ClusterListForListWhiteListsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ClusterListForListWhiteListsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ClusterListForListWhiteListsOutput):
            return True

        return self.to_dict() != other.to_dict()
