# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserInfoForListQuestionnaireAnswerDataAPIV2Output(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'country': 'str',
        'email': 'str',
        'external_id': 'str',
        'extra': 'str',
        'nick_name': 'str',
        'province': 'str',
        'telephone': 'str',
        'user_id': 'str'
    }

    attribute_map = {
        'country': 'Country',
        'email': 'Email',
        'external_id': 'ExternalId',
        'extra': 'Extra',
        'nick_name': 'NickName',
        'province': 'Province',
        'telephone': 'Telephone',
        'user_id': 'UserId'
    }

    def __init__(self, country=None, email=None, external_id=None, extra=None, nick_name=None, province=None, telephone=None, user_id=None, _configuration=None):  # noqa: E501
        """UserInfoForListQuestionnaireAnswerDataAPIV2Output - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._country = None
        self._email = None
        self._external_id = None
        self._extra = None
        self._nick_name = None
        self._province = None
        self._telephone = None
        self._user_id = None
        self.discriminator = None

        if country is not None:
            self.country = country
        if email is not None:
            self.email = email
        if external_id is not None:
            self.external_id = external_id
        if extra is not None:
            self.extra = extra
        if nick_name is not None:
            self.nick_name = nick_name
        if province is not None:
            self.province = province
        if telephone is not None:
            self.telephone = telephone
        if user_id is not None:
            self.user_id = user_id

    @property
    def country(self):
        """Gets the country of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The country of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._country

    @country.setter
    def country(self, country):
        """Sets the country of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.


        :param country: The country of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._country = country

    @property
    def email(self):
        """Gets the email of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The email of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.


        :param email: The email of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def external_id(self):
        """Gets the external_id of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The external_id of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.


        :param external_id: The external_id of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def extra(self):
        """Gets the extra of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The extra of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.


        :param extra: The extra of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def nick_name(self):
        """Gets the nick_name of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The nick_name of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._nick_name

    @nick_name.setter
    def nick_name(self, nick_name):
        """Sets the nick_name of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.


        :param nick_name: The nick_name of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._nick_name = nick_name

    @property
    def province(self):
        """Gets the province of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The province of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._province

    @province.setter
    def province(self, province):
        """Sets the province of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.


        :param province: The province of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._province = province

    @property
    def telephone(self):
        """Gets the telephone of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The telephone of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._telephone

    @telephone.setter
    def telephone(self, telephone):
        """Sets the telephone of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.


        :param telephone: The telephone of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._telephone = telephone

    @property
    def user_id(self):
        """Gets the user_id of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501


        :return: The user_id of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.


        :param user_id: The user_id of this UserInfoForListQuestionnaireAnswerDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._user_id = user_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserInfoForListQuestionnaireAnswerDataAPIV2Output, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserInfoForListQuestionnaireAnswerDataAPIV2Output):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserInfoForListQuestionnaireAnswerDataAPIV2Output):
            return True

        return self.to_dict() != other.to_dict()
