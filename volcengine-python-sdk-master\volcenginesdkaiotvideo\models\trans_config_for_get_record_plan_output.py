# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TransConfigForGetRecordPlanOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'days': 'int',
        'storage_class': 'str'
    }

    attribute_map = {
        'days': 'Days',
        'storage_class': 'StorageClass'
    }

    def __init__(self, days=None, storage_class=None, _configuration=None):  # noqa: E501
        """TransConfigForGetRecordPlanOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._days = None
        self._storage_class = None
        self.discriminator = None

        if days is not None:
            self.days = days
        if storage_class is not None:
            self.storage_class = storage_class

    @property
    def days(self):
        """Gets the days of this TransConfigForGetRecordPlanOutput.  # noqa: E501


        :return: The days of this TransConfigForGetRecordPlanOutput.  # noqa: E501
        :rtype: int
        """
        return self._days

    @days.setter
    def days(self, days):
        """Sets the days of this TransConfigForGetRecordPlanOutput.


        :param days: The days of this TransConfigForGetRecordPlanOutput.  # noqa: E501
        :type: int
        """

        self._days = days

    @property
    def storage_class(self):
        """Gets the storage_class of this TransConfigForGetRecordPlanOutput.  # noqa: E501


        :return: The storage_class of this TransConfigForGetRecordPlanOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_class

    @storage_class.setter
    def storage_class(self, storage_class):
        """Sets the storage_class of this TransConfigForGetRecordPlanOutput.


        :param storage_class: The storage_class of this TransConfigForGetRecordPlanOutput.  # noqa: E501
        :type: str
        """

        self._storage_class = storage_class

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransConfigForGetRecordPlanOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransConfigForGetRecordPlanOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransConfigForGetRecordPlanOutput):
            return True

        return self.to_dict() != other.to_dict()
