# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListForListDevInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_image_build': 'bool',
        'create_time': 'str',
        'creator_trn': 'str',
        'description': 'str',
        'expected_stop_time': 'str',
        'id': 'str',
        'image': 'ImageForListDevInstancesOutput',
        'is_building_image': 'bool',
        'launch_time': 'str',
        'name': 'str',
        'node_affinity_spec': 'NodeAffinitySpecForListDevInstancesOutput',
        'numa_affinity': 'str',
        'numa_status': 'str',
        'ports': 'list[PortForListDevInstancesOutput]',
        'resource_claim': 'ResourceClaimForListDevInstancesOutput',
        'resource_queue_id': 'str',
        'ssh_public_key': 'str',
        'status': 'StatusForListDevInstancesOutput',
        'stop_time': 'str',
        'stopped_time': 'str',
        'storages': 'list[StorageForListDevInstancesOutput]',
        'update_time': 'str',
        'volume': 'VolumeForListDevInstancesOutput',
        'zone_id': 'str'
    }

    attribute_map = {
        'allow_image_build': 'AllowImageBuild',
        'create_time': 'CreateTime',
        'creator_trn': 'CreatorTrn',
        'description': 'Description',
        'expected_stop_time': 'ExpectedStopTime',
        'id': 'Id',
        'image': 'Image',
        'is_building_image': 'IsBuildingImage',
        'launch_time': 'LaunchTime',
        'name': 'Name',
        'node_affinity_spec': 'NodeAffinitySpec',
        'numa_affinity': 'NumaAffinity',
        'numa_status': 'NumaStatus',
        'ports': 'Ports',
        'resource_claim': 'ResourceClaim',
        'resource_queue_id': 'ResourceQueueId',
        'ssh_public_key': 'SshPublicKey',
        'status': 'Status',
        'stop_time': 'StopTime',
        'stopped_time': 'StoppedTime',
        'storages': 'Storages',
        'update_time': 'UpdateTime',
        'volume': 'Volume',
        'zone_id': 'ZoneID'
    }

    def __init__(self, allow_image_build=None, create_time=None, creator_trn=None, description=None, expected_stop_time=None, id=None, image=None, is_building_image=None, launch_time=None, name=None, node_affinity_spec=None, numa_affinity=None, numa_status=None, ports=None, resource_claim=None, resource_queue_id=None, ssh_public_key=None, status=None, stop_time=None, stopped_time=None, storages=None, update_time=None, volume=None, zone_id=None, _configuration=None):  # noqa: E501
        """ListForListDevInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_image_build = None
        self._create_time = None
        self._creator_trn = None
        self._description = None
        self._expected_stop_time = None
        self._id = None
        self._image = None
        self._is_building_image = None
        self._launch_time = None
        self._name = None
        self._node_affinity_spec = None
        self._numa_affinity = None
        self._numa_status = None
        self._ports = None
        self._resource_claim = None
        self._resource_queue_id = None
        self._ssh_public_key = None
        self._status = None
        self._stop_time = None
        self._stopped_time = None
        self._storages = None
        self._update_time = None
        self._volume = None
        self._zone_id = None
        self.discriminator = None

        if allow_image_build is not None:
            self.allow_image_build = allow_image_build
        if create_time is not None:
            self.create_time = create_time
        if creator_trn is not None:
            self.creator_trn = creator_trn
        if description is not None:
            self.description = description
        if expected_stop_time is not None:
            self.expected_stop_time = expected_stop_time
        if id is not None:
            self.id = id
        if image is not None:
            self.image = image
        if is_building_image is not None:
            self.is_building_image = is_building_image
        if launch_time is not None:
            self.launch_time = launch_time
        if name is not None:
            self.name = name
        if node_affinity_spec is not None:
            self.node_affinity_spec = node_affinity_spec
        if numa_affinity is not None:
            self.numa_affinity = numa_affinity
        if numa_status is not None:
            self.numa_status = numa_status
        if ports is not None:
            self.ports = ports
        if resource_claim is not None:
            self.resource_claim = resource_claim
        if resource_queue_id is not None:
            self.resource_queue_id = resource_queue_id
        if ssh_public_key is not None:
            self.ssh_public_key = ssh_public_key
        if status is not None:
            self.status = status
        if stop_time is not None:
            self.stop_time = stop_time
        if stopped_time is not None:
            self.stopped_time = stopped_time
        if storages is not None:
            self.storages = storages
        if update_time is not None:
            self.update_time = update_time
        if volume is not None:
            self.volume = volume
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def allow_image_build(self):
        """Gets the allow_image_build of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The allow_image_build of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._allow_image_build

    @allow_image_build.setter
    def allow_image_build(self, allow_image_build):
        """Sets the allow_image_build of this ListForListDevInstancesOutput.


        :param allow_image_build: The allow_image_build of this ListForListDevInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._allow_image_build = allow_image_build

    @property
    def create_time(self):
        """Gets the create_time of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The create_time of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ListForListDevInstancesOutput.


        :param create_time: The create_time of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def creator_trn(self):
        """Gets the creator_trn of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The creator_trn of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creator_trn

    @creator_trn.setter
    def creator_trn(self, creator_trn):
        """Sets the creator_trn of this ListForListDevInstancesOutput.


        :param creator_trn: The creator_trn of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._creator_trn = creator_trn

    @property
    def description(self):
        """Gets the description of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The description of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ListForListDevInstancesOutput.


        :param description: The description of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def expected_stop_time(self):
        """Gets the expected_stop_time of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The expected_stop_time of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expected_stop_time

    @expected_stop_time.setter
    def expected_stop_time(self, expected_stop_time):
        """Sets the expected_stop_time of this ListForListDevInstancesOutput.


        :param expected_stop_time: The expected_stop_time of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._expected_stop_time = expected_stop_time

    @property
    def id(self):
        """Gets the id of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The id of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ListForListDevInstancesOutput.


        :param id: The id of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def image(self):
        """Gets the image of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The image of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: ImageForListDevInstancesOutput
        """
        return self._image

    @image.setter
    def image(self, image):
        """Sets the image of this ListForListDevInstancesOutput.


        :param image: The image of this ListForListDevInstancesOutput.  # noqa: E501
        :type: ImageForListDevInstancesOutput
        """

        self._image = image

    @property
    def is_building_image(self):
        """Gets the is_building_image of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The is_building_image of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_building_image

    @is_building_image.setter
    def is_building_image(self, is_building_image):
        """Sets the is_building_image of this ListForListDevInstancesOutput.


        :param is_building_image: The is_building_image of this ListForListDevInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._is_building_image = is_building_image

    @property
    def launch_time(self):
        """Gets the launch_time of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The launch_time of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._launch_time

    @launch_time.setter
    def launch_time(self, launch_time):
        """Sets the launch_time of this ListForListDevInstancesOutput.


        :param launch_time: The launch_time of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._launch_time = launch_time

    @property
    def name(self):
        """Gets the name of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The name of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListForListDevInstancesOutput.


        :param name: The name of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def node_affinity_spec(self):
        """Gets the node_affinity_spec of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The node_affinity_spec of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: NodeAffinitySpecForListDevInstancesOutput
        """
        return self._node_affinity_spec

    @node_affinity_spec.setter
    def node_affinity_spec(self, node_affinity_spec):
        """Sets the node_affinity_spec of this ListForListDevInstancesOutput.


        :param node_affinity_spec: The node_affinity_spec of this ListForListDevInstancesOutput.  # noqa: E501
        :type: NodeAffinitySpecForListDevInstancesOutput
        """

        self._node_affinity_spec = node_affinity_spec

    @property
    def numa_affinity(self):
        """Gets the numa_affinity of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The numa_affinity of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._numa_affinity

    @numa_affinity.setter
    def numa_affinity(self, numa_affinity):
        """Sets the numa_affinity of this ListForListDevInstancesOutput.


        :param numa_affinity: The numa_affinity of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._numa_affinity = numa_affinity

    @property
    def numa_status(self):
        """Gets the numa_status of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The numa_status of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._numa_status

    @numa_status.setter
    def numa_status(self, numa_status):
        """Sets the numa_status of this ListForListDevInstancesOutput.


        :param numa_status: The numa_status of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._numa_status = numa_status

    @property
    def ports(self):
        """Gets the ports of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The ports of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: list[PortForListDevInstancesOutput]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this ListForListDevInstancesOutput.


        :param ports: The ports of this ListForListDevInstancesOutput.  # noqa: E501
        :type: list[PortForListDevInstancesOutput]
        """

        self._ports = ports

    @property
    def resource_claim(self):
        """Gets the resource_claim of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The resource_claim of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: ResourceClaimForListDevInstancesOutput
        """
        return self._resource_claim

    @resource_claim.setter
    def resource_claim(self, resource_claim):
        """Sets the resource_claim of this ListForListDevInstancesOutput.


        :param resource_claim: The resource_claim of this ListForListDevInstancesOutput.  # noqa: E501
        :type: ResourceClaimForListDevInstancesOutput
        """

        self._resource_claim = resource_claim

    @property
    def resource_queue_id(self):
        """Gets the resource_queue_id of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The resource_queue_id of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_queue_id

    @resource_queue_id.setter
    def resource_queue_id(self, resource_queue_id):
        """Sets the resource_queue_id of this ListForListDevInstancesOutput.


        :param resource_queue_id: The resource_queue_id of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._resource_queue_id = resource_queue_id

    @property
    def ssh_public_key(self):
        """Gets the ssh_public_key of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The ssh_public_key of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ssh_public_key

    @ssh_public_key.setter
    def ssh_public_key(self, ssh_public_key):
        """Sets the ssh_public_key of this ListForListDevInstancesOutput.


        :param ssh_public_key: The ssh_public_key of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._ssh_public_key = ssh_public_key

    @property
    def status(self):
        """Gets the status of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The status of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: StatusForListDevInstancesOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListForListDevInstancesOutput.


        :param status: The status of this ListForListDevInstancesOutput.  # noqa: E501
        :type: StatusForListDevInstancesOutput
        """

        self._status = status

    @property
    def stop_time(self):
        """Gets the stop_time of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The stop_time of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._stop_time

    @stop_time.setter
    def stop_time(self, stop_time):
        """Sets the stop_time of this ListForListDevInstancesOutput.


        :param stop_time: The stop_time of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._stop_time = stop_time

    @property
    def stopped_time(self):
        """Gets the stopped_time of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The stopped_time of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._stopped_time

    @stopped_time.setter
    def stopped_time(self, stopped_time):
        """Sets the stopped_time of this ListForListDevInstancesOutput.


        :param stopped_time: The stopped_time of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._stopped_time = stopped_time

    @property
    def storages(self):
        """Gets the storages of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The storages of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: list[StorageForListDevInstancesOutput]
        """
        return self._storages

    @storages.setter
    def storages(self, storages):
        """Sets the storages of this ListForListDevInstancesOutput.


        :param storages: The storages of this ListForListDevInstancesOutput.  # noqa: E501
        :type: list[StorageForListDevInstancesOutput]
        """

        self._storages = storages

    @property
    def update_time(self):
        """Gets the update_time of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The update_time of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ListForListDevInstancesOutput.


        :param update_time: The update_time of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def volume(self):
        """Gets the volume of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The volume of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: VolumeForListDevInstancesOutput
        """
        return self._volume

    @volume.setter
    def volume(self, volume):
        """Sets the volume of this ListForListDevInstancesOutput.


        :param volume: The volume of this ListForListDevInstancesOutput.  # noqa: E501
        :type: VolumeForListDevInstancesOutput
        """

        self._volume = volume

    @property
    def zone_id(self):
        """Gets the zone_id of this ListForListDevInstancesOutput.  # noqa: E501


        :return: The zone_id of this ListForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ListForListDevInstancesOutput.


        :param zone_id: The zone_id of this ListForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListForListDevInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListForListDevInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListForListDevInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
