# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceConfigurationForCreateInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'admin_password': 'str',
        'auto_renew': 'bool',
        'charge_type': 'str',
        'configuration_code': 'str',
        'deletion_protection': 'bool',
        'enable_https': 'bool',
        'enable_pure_master': 'bool',
        'instance_name': 'str',
        'network_specs': 'list[NetworkSpecForCreateInstanceInput]',
        'node_specs_assigns': 'list[NodeSpecsAssignForCreateInstanceInput]',
        'period': 'int',
        'project_name': 'str',
        'region_id': 'str',
        'subnet': 'SubnetForCreateInstanceInput',
        'vpc': 'VPCForCreateInstanceInput',
        'version': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'admin_password': 'AdminPassword',
        'auto_renew': 'AutoRenew',
        'charge_type': 'ChargeType',
        'configuration_code': 'ConfigurationCode',
        'deletion_protection': 'DeletionProtection',
        'enable_https': 'EnableHttps',
        'enable_pure_master': 'EnablePureMaster',
        'instance_name': 'InstanceName',
        'network_specs': 'NetworkSpecs',
        'node_specs_assigns': 'NodeSpecsAssigns',
        'period': 'Period',
        'project_name': 'ProjectName',
        'region_id': 'RegionId',
        'subnet': 'Subnet',
        'vpc': 'VPC',
        'version': 'Version',
        'zone_id': 'ZoneId'
    }

    def __init__(self, admin_password=None, auto_renew=None, charge_type=None, configuration_code=None, deletion_protection=None, enable_https=None, enable_pure_master=None, instance_name=None, network_specs=None, node_specs_assigns=None, period=None, project_name=None, region_id=None, subnet=None, vpc=None, version=None, zone_id=None, _configuration=None):  # noqa: E501
        """InstanceConfigurationForCreateInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._admin_password = None
        self._auto_renew = None
        self._charge_type = None
        self._configuration_code = None
        self._deletion_protection = None
        self._enable_https = None
        self._enable_pure_master = None
        self._instance_name = None
        self._network_specs = None
        self._node_specs_assigns = None
        self._period = None
        self._project_name = None
        self._region_id = None
        self._subnet = None
        self._vpc = None
        self._version = None
        self._zone_id = None
        self.discriminator = None

        if admin_password is not None:
            self.admin_password = admin_password
        if auto_renew is not None:
            self.auto_renew = auto_renew
        if charge_type is not None:
            self.charge_type = charge_type
        if configuration_code is not None:
            self.configuration_code = configuration_code
        if deletion_protection is not None:
            self.deletion_protection = deletion_protection
        if enable_https is not None:
            self.enable_https = enable_https
        if enable_pure_master is not None:
            self.enable_pure_master = enable_pure_master
        if instance_name is not None:
            self.instance_name = instance_name
        if network_specs is not None:
            self.network_specs = network_specs
        if node_specs_assigns is not None:
            self.node_specs_assigns = node_specs_assigns
        if period is not None:
            self.period = period
        if project_name is not None:
            self.project_name = project_name
        if region_id is not None:
            self.region_id = region_id
        if subnet is not None:
            self.subnet = subnet
        if vpc is not None:
            self.vpc = vpc
        if version is not None:
            self.version = version
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def admin_password(self):
        """Gets the admin_password of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The admin_password of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._admin_password

    @admin_password.setter
    def admin_password(self, admin_password):
        """Sets the admin_password of this InstanceConfigurationForCreateInstanceInput.


        :param admin_password: The admin_password of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._admin_password = admin_password

    @property
    def auto_renew(self):
        """Gets the auto_renew of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The auto_renew of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this InstanceConfigurationForCreateInstanceInput.


        :param auto_renew: The auto_renew of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def charge_type(self):
        """Gets the charge_type of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The charge_type of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this InstanceConfigurationForCreateInstanceInput.


        :param charge_type: The charge_type of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["PrePaid", "PostPaid"]  # noqa: E501
        if (self._configuration.client_side_validation and
                charge_type not in allowed_values):
            raise ValueError(
                "Invalid value for `charge_type` ({0}), must be one of {1}"  # noqa: E501
                .format(charge_type, allowed_values)
            )

        self._charge_type = charge_type

    @property
    def configuration_code(self):
        """Gets the configuration_code of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The configuration_code of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this InstanceConfigurationForCreateInstanceInput.


        :param configuration_code: The configuration_code of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def deletion_protection(self):
        """Gets the deletion_protection of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The deletion_protection of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._deletion_protection

    @deletion_protection.setter
    def deletion_protection(self, deletion_protection):
        """Sets the deletion_protection of this InstanceConfigurationForCreateInstanceInput.


        :param deletion_protection: The deletion_protection of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: bool
        """

        self._deletion_protection = deletion_protection

    @property
    def enable_https(self):
        """Gets the enable_https of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The enable_https of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_https

    @enable_https.setter
    def enable_https(self, enable_https):
        """Sets the enable_https of this InstanceConfigurationForCreateInstanceInput.


        :param enable_https: The enable_https of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: bool
        """

        self._enable_https = enable_https

    @property
    def enable_pure_master(self):
        """Gets the enable_pure_master of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The enable_pure_master of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_pure_master

    @enable_pure_master.setter
    def enable_pure_master(self, enable_pure_master):
        """Sets the enable_pure_master of this InstanceConfigurationForCreateInstanceInput.


        :param enable_pure_master: The enable_pure_master of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: bool
        """

        self._enable_pure_master = enable_pure_master

    @property
    def instance_name(self):
        """Gets the instance_name of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The instance_name of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this InstanceConfigurationForCreateInstanceInput.


        :param instance_name: The instance_name of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def network_specs(self):
        """Gets the network_specs of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The network_specs of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: list[NetworkSpecForCreateInstanceInput]
        """
        return self._network_specs

    @network_specs.setter
    def network_specs(self, network_specs):
        """Sets the network_specs of this InstanceConfigurationForCreateInstanceInput.


        :param network_specs: The network_specs of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: list[NetworkSpecForCreateInstanceInput]
        """

        self._network_specs = network_specs

    @property
    def node_specs_assigns(self):
        """Gets the node_specs_assigns of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The node_specs_assigns of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: list[NodeSpecsAssignForCreateInstanceInput]
        """
        return self._node_specs_assigns

    @node_specs_assigns.setter
    def node_specs_assigns(self, node_specs_assigns):
        """Sets the node_specs_assigns of this InstanceConfigurationForCreateInstanceInput.


        :param node_specs_assigns: The node_specs_assigns of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: list[NodeSpecsAssignForCreateInstanceInput]
        """

        self._node_specs_assigns = node_specs_assigns

    @property
    def period(self):
        """Gets the period of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The period of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this InstanceConfigurationForCreateInstanceInput.


        :param period: The period of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def project_name(self):
        """Gets the project_name of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The project_name of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this InstanceConfigurationForCreateInstanceInput.


        :param project_name: The project_name of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region_id(self):
        """Gets the region_id of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The region_id of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this InstanceConfigurationForCreateInstanceInput.


        :param region_id: The region_id of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def subnet(self):
        """Gets the subnet of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The subnet of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: SubnetForCreateInstanceInput
        """
        return self._subnet

    @subnet.setter
    def subnet(self, subnet):
        """Sets the subnet of this InstanceConfigurationForCreateInstanceInput.


        :param subnet: The subnet of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: SubnetForCreateInstanceInput
        """

        self._subnet = subnet

    @property
    def vpc(self):
        """Gets the vpc of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The vpc of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: VPCForCreateInstanceInput
        """
        return self._vpc

    @vpc.setter
    def vpc(self, vpc):
        """Sets the vpc of this InstanceConfigurationForCreateInstanceInput.


        :param vpc: The vpc of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: VPCForCreateInstanceInput
        """

        self._vpc = vpc

    @property
    def version(self):
        """Gets the version of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The version of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this InstanceConfigurationForCreateInstanceInput.


        :param version: The version of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["V6_7", "V7_10", "V7_TLS", "V7_BYTE", "OPEN_SEARCH_2_9"]  # noqa: E501
        if (self._configuration.client_side_validation and
                version not in allowed_values):
            raise ValueError(
                "Invalid value for `version` ({0}), must be one of {1}"  # noqa: E501
                .format(version, allowed_values)
            )

        self._version = version

    @property
    def zone_id(self):
        """Gets the zone_id of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501


        :return: The zone_id of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this InstanceConfigurationForCreateInstanceInput.


        :param zone_id: The zone_id of this InstanceConfigurationForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceConfigurationForCreateInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceConfigurationForCreateInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceConfigurationForCreateInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
