# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetRaspProtectStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'hit_white_process_count': 'int',
        'process_count': 'int',
        'protect_abnormal_process_count': 'int',
        'protected_process_count': 'int',
        'rasp_close_count': 'int',
        'rasp_open_count': 'int',
        'rasp_open_fail_count': 'int',
        'to_be_protected_process_count': 'int'
    }

    attribute_map = {
        'hit_white_process_count': 'HitWhiteProcessCount',
        'process_count': 'ProcessCount',
        'protect_abnormal_process_count': 'ProtectAbnormalProcessCount',
        'protected_process_count': 'ProtectedProcessCount',
        'rasp_close_count': 'RaspCloseCount',
        'rasp_open_count': 'RaspOpenCount',
        'rasp_open_fail_count': 'RaspOpenFailCount',
        'to_be_protected_process_count': 'ToBeProtectedProcessCount'
    }

    def __init__(self, hit_white_process_count=None, process_count=None, protect_abnormal_process_count=None, protected_process_count=None, rasp_close_count=None, rasp_open_count=None, rasp_open_fail_count=None, to_be_protected_process_count=None, _configuration=None):  # noqa: E501
        """GetRaspProtectStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._hit_white_process_count = None
        self._process_count = None
        self._protect_abnormal_process_count = None
        self._protected_process_count = None
        self._rasp_close_count = None
        self._rasp_open_count = None
        self._rasp_open_fail_count = None
        self._to_be_protected_process_count = None
        self.discriminator = None

        if hit_white_process_count is not None:
            self.hit_white_process_count = hit_white_process_count
        if process_count is not None:
            self.process_count = process_count
        if protect_abnormal_process_count is not None:
            self.protect_abnormal_process_count = protect_abnormal_process_count
        if protected_process_count is not None:
            self.protected_process_count = protected_process_count
        if rasp_close_count is not None:
            self.rasp_close_count = rasp_close_count
        if rasp_open_count is not None:
            self.rasp_open_count = rasp_open_count
        if rasp_open_fail_count is not None:
            self.rasp_open_fail_count = rasp_open_fail_count
        if to_be_protected_process_count is not None:
            self.to_be_protected_process_count = to_be_protected_process_count

    @property
    def hit_white_process_count(self):
        """Gets the hit_white_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501


        :return: The hit_white_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._hit_white_process_count

    @hit_white_process_count.setter
    def hit_white_process_count(self, hit_white_process_count):
        """Sets the hit_white_process_count of this GetRaspProtectStatisticsResponse.


        :param hit_white_process_count: The hit_white_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._hit_white_process_count = hit_white_process_count

    @property
    def process_count(self):
        """Gets the process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501


        :return: The process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._process_count

    @process_count.setter
    def process_count(self, process_count):
        """Sets the process_count of this GetRaspProtectStatisticsResponse.


        :param process_count: The process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._process_count = process_count

    @property
    def protect_abnormal_process_count(self):
        """Gets the protect_abnormal_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501


        :return: The protect_abnormal_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._protect_abnormal_process_count

    @protect_abnormal_process_count.setter
    def protect_abnormal_process_count(self, protect_abnormal_process_count):
        """Sets the protect_abnormal_process_count of this GetRaspProtectStatisticsResponse.


        :param protect_abnormal_process_count: The protect_abnormal_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._protect_abnormal_process_count = protect_abnormal_process_count

    @property
    def protected_process_count(self):
        """Gets the protected_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501


        :return: The protected_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._protected_process_count

    @protected_process_count.setter
    def protected_process_count(self, protected_process_count):
        """Sets the protected_process_count of this GetRaspProtectStatisticsResponse.


        :param protected_process_count: The protected_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._protected_process_count = protected_process_count

    @property
    def rasp_close_count(self):
        """Gets the rasp_close_count of this GetRaspProtectStatisticsResponse.  # noqa: E501


        :return: The rasp_close_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._rasp_close_count

    @rasp_close_count.setter
    def rasp_close_count(self, rasp_close_count):
        """Sets the rasp_close_count of this GetRaspProtectStatisticsResponse.


        :param rasp_close_count: The rasp_close_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._rasp_close_count = rasp_close_count

    @property
    def rasp_open_count(self):
        """Gets the rasp_open_count of this GetRaspProtectStatisticsResponse.  # noqa: E501


        :return: The rasp_open_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._rasp_open_count

    @rasp_open_count.setter
    def rasp_open_count(self, rasp_open_count):
        """Sets the rasp_open_count of this GetRaspProtectStatisticsResponse.


        :param rasp_open_count: The rasp_open_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._rasp_open_count = rasp_open_count

    @property
    def rasp_open_fail_count(self):
        """Gets the rasp_open_fail_count of this GetRaspProtectStatisticsResponse.  # noqa: E501


        :return: The rasp_open_fail_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._rasp_open_fail_count

    @rasp_open_fail_count.setter
    def rasp_open_fail_count(self, rasp_open_fail_count):
        """Sets the rasp_open_fail_count of this GetRaspProtectStatisticsResponse.


        :param rasp_open_fail_count: The rasp_open_fail_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._rasp_open_fail_count = rasp_open_fail_count

    @property
    def to_be_protected_process_count(self):
        """Gets the to_be_protected_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501


        :return: The to_be_protected_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._to_be_protected_process_count

    @to_be_protected_process_count.setter
    def to_be_protected_process_count(self, to_be_protected_process_count):
        """Sets the to_be_protected_process_count of this GetRaspProtectStatisticsResponse.


        :param to_be_protected_process_count: The to_be_protected_process_count of this GetRaspProtectStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._to_be_protected_process_count = to_be_protected_process_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetRaspProtectStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetRaspProtectStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetRaspProtectStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
