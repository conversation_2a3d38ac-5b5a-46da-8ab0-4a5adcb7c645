# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SaveRepoImageScanCronRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cron_term': 'str',
        'cron_time': 'str',
        'enabled': 'int'
    }

    attribute_map = {
        'cron_term': 'CronTerm',
        'cron_time': 'CronTime',
        'enabled': 'Enabled'
    }

    def __init__(self, cron_term=None, cron_time=None, enabled=None, _configuration=None):  # noqa: E501
        """SaveRepoImageScanCronRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cron_term = None
        self._cron_time = None
        self._enabled = None
        self.discriminator = None

        if cron_term is not None:
            self.cron_term = cron_term
        if cron_time is not None:
            self.cron_time = cron_time
        self.enabled = enabled

    @property
    def cron_term(self):
        """Gets the cron_term of this SaveRepoImageScanCronRequest.  # noqa: E501


        :return: The cron_term of this SaveRepoImageScanCronRequest.  # noqa: E501
        :rtype: str
        """
        return self._cron_term

    @cron_term.setter
    def cron_term(self, cron_term):
        """Sets the cron_term of this SaveRepoImageScanCronRequest.


        :param cron_term: The cron_term of this SaveRepoImageScanCronRequest.  # noqa: E501
        :type: str
        """

        self._cron_term = cron_term

    @property
    def cron_time(self):
        """Gets the cron_time of this SaveRepoImageScanCronRequest.  # noqa: E501


        :return: The cron_time of this SaveRepoImageScanCronRequest.  # noqa: E501
        :rtype: str
        """
        return self._cron_time

    @cron_time.setter
    def cron_time(self, cron_time):
        """Sets the cron_time of this SaveRepoImageScanCronRequest.


        :param cron_time: The cron_time of this SaveRepoImageScanCronRequest.  # noqa: E501
        :type: str
        """

        self._cron_time = cron_time

    @property
    def enabled(self):
        """Gets the enabled of this SaveRepoImageScanCronRequest.  # noqa: E501


        :return: The enabled of this SaveRepoImageScanCronRequest.  # noqa: E501
        :rtype: int
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this SaveRepoImageScanCronRequest.


        :param enabled: The enabled of this SaveRepoImageScanCronRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and enabled is None:
            raise ValueError("Invalid value for `enabled`, must not be `None`")  # noqa: E501

        self._enabled = enabled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SaveRepoImageScanCronRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SaveRepoImageScanCronRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SaveRepoImageScanCronRequest):
            return True

        return self.to_dict() != other.to_dict()
