# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTransitRoutersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeTransitRoutersInput]',
        'transit_router_ids': 'list[str]',
        'transit_router_name': 'str'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters',
        'transit_router_ids': 'TransitRouterIds',
        'transit_router_name': 'TransitRouterName'
    }

    def __init__(self, page_number=None, page_size=None, project_name=None, tag_filters=None, transit_router_ids=None, transit_router_name=None, _configuration=None):  # noqa: E501
        """DescribeTransitRoutersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._tag_filters = None
        self._transit_router_ids = None
        self._transit_router_name = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if transit_router_ids is not None:
            self.transit_router_ids = transit_router_ids
        if transit_router_name is not None:
            self.transit_router_name = transit_router_name

    @property
    def page_number(self):
        """Gets the page_number of this DescribeTransitRoutersRequest.  # noqa: E501


        :return: The page_number of this DescribeTransitRoutersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeTransitRoutersRequest.


        :param page_number: The page_number of this DescribeTransitRoutersRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeTransitRoutersRequest.  # noqa: E501


        :return: The page_size of this DescribeTransitRoutersRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeTransitRoutersRequest.


        :param page_size: The page_size of this DescribeTransitRoutersRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeTransitRoutersRequest.  # noqa: E501


        :return: The project_name of this DescribeTransitRoutersRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeTransitRoutersRequest.


        :param project_name: The project_name of this DescribeTransitRoutersRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeTransitRoutersRequest.  # noqa: E501


        :return: The tag_filters of this DescribeTransitRoutersRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeTransitRoutersInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeTransitRoutersRequest.


        :param tag_filters: The tag_filters of this DescribeTransitRoutersRequest.  # noqa: E501
        :type: list[TagFilterForDescribeTransitRoutersInput]
        """

        self._tag_filters = tag_filters

    @property
    def transit_router_ids(self):
        """Gets the transit_router_ids of this DescribeTransitRoutersRequest.  # noqa: E501


        :return: The transit_router_ids of this DescribeTransitRoutersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._transit_router_ids

    @transit_router_ids.setter
    def transit_router_ids(self, transit_router_ids):
        """Sets the transit_router_ids of this DescribeTransitRoutersRequest.


        :param transit_router_ids: The transit_router_ids of this DescribeTransitRoutersRequest.  # noqa: E501
        :type: list[str]
        """

        self._transit_router_ids = transit_router_ids

    @property
    def transit_router_name(self):
        """Gets the transit_router_name of this DescribeTransitRoutersRequest.  # noqa: E501


        :return: The transit_router_name of this DescribeTransitRoutersRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_name

    @transit_router_name.setter
    def transit_router_name(self, transit_router_name):
        """Sets the transit_router_name of this DescribeTransitRoutersRequest.


        :param transit_router_name: The transit_router_name of this DescribeTransitRoutersRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_name = transit_router_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTransitRoutersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTransitRoutersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTransitRoutersRequest):
            return True

        return self.to_dict() != other.to_dict()
