# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAccountsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'include_tags': 'bool',
        'limit': 'int',
        'offset': 'int',
        'order_by': 'int',
        'org_unit_id': 'str',
        'search': 'str',
        'verification_id': 'str'
    }

    attribute_map = {
        'include_tags': 'IncludeTags',
        'limit': 'Limit',
        'offset': 'Offset',
        'order_by': 'OrderBy',
        'org_unit_id': 'OrgUnitId',
        'search': 'Search',
        'verification_id': 'VerificationId'
    }

    def __init__(self, include_tags=None, limit=None, offset=None, order_by=None, org_unit_id=None, search=None, verification_id=None, _configuration=None):  # noqa: E501
        """ListAccountsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._include_tags = None
        self._limit = None
        self._offset = None
        self._order_by = None
        self._org_unit_id = None
        self._search = None
        self._verification_id = None
        self.discriminator = None

        if include_tags is not None:
            self.include_tags = include_tags
        if limit is not None:
            self.limit = limit
        if offset is not None:
            self.offset = offset
        if order_by is not None:
            self.order_by = order_by
        if org_unit_id is not None:
            self.org_unit_id = org_unit_id
        if search is not None:
            self.search = search
        if verification_id is not None:
            self.verification_id = verification_id

    @property
    def include_tags(self):
        """Gets the include_tags of this ListAccountsRequest.  # noqa: E501


        :return: The include_tags of this ListAccountsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._include_tags

    @include_tags.setter
    def include_tags(self, include_tags):
        """Sets the include_tags of this ListAccountsRequest.


        :param include_tags: The include_tags of this ListAccountsRequest.  # noqa: E501
        :type: bool
        """

        self._include_tags = include_tags

    @property
    def limit(self):
        """Gets the limit of this ListAccountsRequest.  # noqa: E501


        :return: The limit of this ListAccountsRequest.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListAccountsRequest.


        :param limit: The limit of this ListAccountsRequest.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def offset(self):
        """Gets the offset of this ListAccountsRequest.  # noqa: E501


        :return: The offset of this ListAccountsRequest.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this ListAccountsRequest.


        :param offset: The offset of this ListAccountsRequest.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def order_by(self):
        """Gets the order_by of this ListAccountsRequest.  # noqa: E501


        :return: The order_by of this ListAccountsRequest.  # noqa: E501
        :rtype: int
        """
        return self._order_by

    @order_by.setter
    def order_by(self, order_by):
        """Sets the order_by of this ListAccountsRequest.


        :param order_by: The order_by of this ListAccountsRequest.  # noqa: E501
        :type: int
        """

        self._order_by = order_by

    @property
    def org_unit_id(self):
        """Gets the org_unit_id of this ListAccountsRequest.  # noqa: E501


        :return: The org_unit_id of this ListAccountsRequest.  # noqa: E501
        :rtype: str
        """
        return self._org_unit_id

    @org_unit_id.setter
    def org_unit_id(self, org_unit_id):
        """Sets the org_unit_id of this ListAccountsRequest.


        :param org_unit_id: The org_unit_id of this ListAccountsRequest.  # noqa: E501
        :type: str
        """

        self._org_unit_id = org_unit_id

    @property
    def search(self):
        """Gets the search of this ListAccountsRequest.  # noqa: E501


        :return: The search of this ListAccountsRequest.  # noqa: E501
        :rtype: str
        """
        return self._search

    @search.setter
    def search(self, search):
        """Sets the search of this ListAccountsRequest.


        :param search: The search of this ListAccountsRequest.  # noqa: E501
        :type: str
        """

        self._search = search

    @property
    def verification_id(self):
        """Gets the verification_id of this ListAccountsRequest.  # noqa: E501


        :return: The verification_id of this ListAccountsRequest.  # noqa: E501
        :rtype: str
        """
        return self._verification_id

    @verification_id.setter
    def verification_id(self, verification_id):
        """Sets the verification_id of this ListAccountsRequest.


        :param verification_id: The verification_id of this ListAccountsRequest.  # noqa: E501
        :type: str
        """

        self._verification_id = verification_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAccountsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAccountsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAccountsRequest):
            return True

        return self.to_dict() != other.to_dict()
