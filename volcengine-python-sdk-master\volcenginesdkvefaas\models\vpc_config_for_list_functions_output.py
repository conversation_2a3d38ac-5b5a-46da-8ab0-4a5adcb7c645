# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VpcConfigForListFunctionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_shared_internet_access': 'bool',
        'enable_vpc': 'bool',
        'security_group_ids': 'list[str]',
        'subnet_ids': 'list[str]',
        'vpc_id': 'str'
    }

    attribute_map = {
        'enable_shared_internet_access': 'EnableSharedInternetAccess',
        'enable_vpc': 'EnableVpc',
        'security_group_ids': 'SecurityGroupIds',
        'subnet_ids': 'SubnetIds',
        'vpc_id': 'VpcId'
    }

    def __init__(self, enable_shared_internet_access=None, enable_vpc=None, security_group_ids=None, subnet_ids=None, vpc_id=None, _configuration=None):  # noqa: E501
        """VpcConfigForListFunctionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_shared_internet_access = None
        self._enable_vpc = None
        self._security_group_ids = None
        self._subnet_ids = None
        self._vpc_id = None
        self.discriminator = None

        if enable_shared_internet_access is not None:
            self.enable_shared_internet_access = enable_shared_internet_access
        if enable_vpc is not None:
            self.enable_vpc = enable_vpc
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        if subnet_ids is not None:
            self.subnet_ids = subnet_ids
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def enable_shared_internet_access(self):
        """Gets the enable_shared_internet_access of this VpcConfigForListFunctionsOutput.  # noqa: E501


        :return: The enable_shared_internet_access of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_shared_internet_access

    @enable_shared_internet_access.setter
    def enable_shared_internet_access(self, enable_shared_internet_access):
        """Sets the enable_shared_internet_access of this VpcConfigForListFunctionsOutput.


        :param enable_shared_internet_access: The enable_shared_internet_access of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :type: bool
        """

        self._enable_shared_internet_access = enable_shared_internet_access

    @property
    def enable_vpc(self):
        """Gets the enable_vpc of this VpcConfigForListFunctionsOutput.  # noqa: E501


        :return: The enable_vpc of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_vpc

    @enable_vpc.setter
    def enable_vpc(self, enable_vpc):
        """Sets the enable_vpc of this VpcConfigForListFunctionsOutput.


        :param enable_vpc: The enable_vpc of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :type: bool
        """

        self._enable_vpc = enable_vpc

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this VpcConfigForListFunctionsOutput.  # noqa: E501


        :return: The security_group_ids of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this VpcConfigForListFunctionsOutput.


        :param security_group_ids: The security_group_ids of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def subnet_ids(self):
        """Gets the subnet_ids of this VpcConfigForListFunctionsOutput.  # noqa: E501


        :return: The subnet_ids of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._subnet_ids

    @subnet_ids.setter
    def subnet_ids(self, subnet_ids):
        """Sets the subnet_ids of this VpcConfigForListFunctionsOutput.


        :param subnet_ids: The subnet_ids of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :type: list[str]
        """

        self._subnet_ids = subnet_ids

    @property
    def vpc_id(self):
        """Gets the vpc_id of this VpcConfigForListFunctionsOutput.  # noqa: E501


        :return: The vpc_id of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this VpcConfigForListFunctionsOutput.


        :param vpc_id: The vpc_id of this VpcConfigForListFunctionsOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VpcConfigForListFunctionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VpcConfigForListFunctionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VpcConfigForListFunctionsOutput):
            return True

        return self.to_dict() != other.to_dict()
