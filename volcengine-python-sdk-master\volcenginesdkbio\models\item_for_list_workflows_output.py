# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListWorkflowsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'int',
        'description': 'str',
        'graph': 'str',
        'id': 'str',
        'inputs': 'list[InputForListWorkflowsOutput]',
        'language': 'str',
        'main_workflow_path': 'str',
        'name': 'str',
        'outputs': 'list[OutputForListWorkflowsOutput]',
        'owner_name': 'str',
        'source': 'str',
        'source_type': 'str',
        'status': 'StatusForListWorkflowsOutput',
        'tag': 'str',
        'token': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'description': 'Description',
        'graph': 'Graph',
        'id': 'ID',
        'inputs': 'Inputs',
        'language': 'Language',
        'main_workflow_path': 'MainWorkflowPath',
        'name': 'Name',
        'outputs': 'Outputs',
        'owner_name': 'OwnerName',
        'source': 'Source',
        'source_type': 'SourceType',
        'status': 'Status',
        'tag': 'Tag',
        'token': 'Token',
        'update_time': 'UpdateTime'
    }

    def __init__(self, create_time=None, description=None, graph=None, id=None, inputs=None, language=None, main_workflow_path=None, name=None, outputs=None, owner_name=None, source=None, source_type=None, status=None, tag=None, token=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListWorkflowsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._description = None
        self._graph = None
        self._id = None
        self._inputs = None
        self._language = None
        self._main_workflow_path = None
        self._name = None
        self._outputs = None
        self._owner_name = None
        self._source = None
        self._source_type = None
        self._status = None
        self._tag = None
        self._token = None
        self._update_time = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if graph is not None:
            self.graph = graph
        if id is not None:
            self.id = id
        if inputs is not None:
            self.inputs = inputs
        if language is not None:
            self.language = language
        if main_workflow_path is not None:
            self.main_workflow_path = main_workflow_path
        if name is not None:
            self.name = name
        if outputs is not None:
            self.outputs = outputs
        if owner_name is not None:
            self.owner_name = owner_name
        if source is not None:
            self.source = source
        if source_type is not None:
            self.source_type = source_type
        if status is not None:
            self.status = status
        if tag is not None:
            self.tag = tag
        if token is not None:
            self.token = token
        if update_time is not None:
            self.update_time = update_time

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The create_time of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListWorkflowsOutput.


        :param create_time: The create_time of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The description of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListWorkflowsOutput.


        :param description: The description of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def graph(self):
        """Gets the graph of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The graph of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._graph

    @graph.setter
    def graph(self, graph):
        """Sets the graph of this ItemForListWorkflowsOutput.


        :param graph: The graph of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._graph = graph

    @property
    def id(self):
        """Gets the id of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The id of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListWorkflowsOutput.


        :param id: The id of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def inputs(self):
        """Gets the inputs of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The inputs of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: list[InputForListWorkflowsOutput]
        """
        return self._inputs

    @inputs.setter
    def inputs(self, inputs):
        """Sets the inputs of this ItemForListWorkflowsOutput.


        :param inputs: The inputs of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: list[InputForListWorkflowsOutput]
        """

        self._inputs = inputs

    @property
    def language(self):
        """Gets the language of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The language of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._language

    @language.setter
    def language(self, language):
        """Sets the language of this ItemForListWorkflowsOutput.


        :param language: The language of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._language = language

    @property
    def main_workflow_path(self):
        """Gets the main_workflow_path of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The main_workflow_path of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._main_workflow_path

    @main_workflow_path.setter
    def main_workflow_path(self, main_workflow_path):
        """Sets the main_workflow_path of this ItemForListWorkflowsOutput.


        :param main_workflow_path: The main_workflow_path of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._main_workflow_path = main_workflow_path

    @property
    def name(self):
        """Gets the name of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The name of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListWorkflowsOutput.


        :param name: The name of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def outputs(self):
        """Gets the outputs of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The outputs of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: list[OutputForListWorkflowsOutput]
        """
        return self._outputs

    @outputs.setter
    def outputs(self, outputs):
        """Sets the outputs of this ItemForListWorkflowsOutput.


        :param outputs: The outputs of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: list[OutputForListWorkflowsOutput]
        """

        self._outputs = outputs

    @property
    def owner_name(self):
        """Gets the owner_name of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The owner_name of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner_name

    @owner_name.setter
    def owner_name(self, owner_name):
        """Sets the owner_name of this ItemForListWorkflowsOutput.


        :param owner_name: The owner_name of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._owner_name = owner_name

    @property
    def source(self):
        """Gets the source of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The source of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this ItemForListWorkflowsOutput.


        :param source: The source of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._source = source

    @property
    def source_type(self):
        """Gets the source_type of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The source_type of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this ItemForListWorkflowsOutput.


        :param source_type: The source_type of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._source_type = source_type

    @property
    def status(self):
        """Gets the status of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The status of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: StatusForListWorkflowsOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListWorkflowsOutput.


        :param status: The status of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: StatusForListWorkflowsOutput
        """

        self._status = status

    @property
    def tag(self):
        """Gets the tag of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The tag of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this ItemForListWorkflowsOutput.


        :param tag: The tag of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def token(self):
        """Gets the token of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The token of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._token

    @token.setter
    def token(self, token):
        """Sets the token of this ItemForListWorkflowsOutput.


        :param token: The token of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._token = token

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListWorkflowsOutput.  # noqa: E501


        :return: The update_time of this ItemForListWorkflowsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListWorkflowsOutput.


        :param update_time: The update_time of this ItemForListWorkflowsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListWorkflowsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListWorkflowsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListWorkflowsOutput):
            return True

        return self.to_dict() != other.to_dict()
