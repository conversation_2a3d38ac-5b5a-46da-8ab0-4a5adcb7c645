# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ContainerInfoForGetOneRaspAlarmOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'container_image': 'str',
        'container_name': 'str'
    }

    attribute_map = {
        'container_image': 'ContainerImage',
        'container_name': 'ContainerName'
    }

    def __init__(self, container_image=None, container_name=None, _configuration=None):  # noqa: E501
        """ContainerInfoForGetOneRaspAlarmOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._container_image = None
        self._container_name = None
        self.discriminator = None

        if container_image is not None:
            self.container_image = container_image
        if container_name is not None:
            self.container_name = container_name

    @property
    def container_image(self):
        """Gets the container_image of this ContainerInfoForGetOneRaspAlarmOutput.  # noqa: E501


        :return: The container_image of this ContainerInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_image

    @container_image.setter
    def container_image(self, container_image):
        """Sets the container_image of this ContainerInfoForGetOneRaspAlarmOutput.


        :param container_image: The container_image of this ContainerInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :type: str
        """

        self._container_image = container_image

    @property
    def container_name(self):
        """Gets the container_name of this ContainerInfoForGetOneRaspAlarmOutput.  # noqa: E501


        :return: The container_name of this ContainerInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_name

    @container_name.setter
    def container_name(self, container_name):
        """Sets the container_name of this ContainerInfoForGetOneRaspAlarmOutput.


        :param container_name: The container_name of this ContainerInfoForGetOneRaspAlarmOutput.  # noqa: E501
        :type: str
        """

        self._container_name = container_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ContainerInfoForGetOneRaspAlarmOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ContainerInfoForGetOneRaspAlarmOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ContainerInfoForGetOneRaspAlarmOutput):
            return True

        return self.to_dict() != other.to_dict()
