# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListOperationsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'create_time': 'int',
        'end_time': 'int',
        'operate_id': 'str',
        'operate_name': 'str',
        'operate_state': 'str',
        'operator_id': 'str',
        'operator_name': 'str',
        'start_time': 'int',
        'update_time': 'int'
    }

    attribute_map = {
        'cluster_id': 'ClusterId',
        'create_time': 'CreateTime',
        'end_time': 'EndTime',
        'operate_id': 'OperateId',
        'operate_name': 'OperateName',
        'operate_state': 'OperateState',
        'operator_id': 'OperatorId',
        'operator_name': 'OperatorName',
        'start_time': 'StartTime',
        'update_time': 'UpdateTime'
    }

    def __init__(self, cluster_id=None, create_time=None, end_time=None, operate_id=None, operate_name=None, operate_state=None, operator_id=None, operator_name=None, start_time=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListOperationsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._create_time = None
        self._end_time = None
        self._operate_id = None
        self._operate_name = None
        self._operate_state = None
        self._operator_id = None
        self._operator_name = None
        self._start_time = None
        self._update_time = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if create_time is not None:
            self.create_time = create_time
        if end_time is not None:
            self.end_time = end_time
        if operate_id is not None:
            self.operate_id = operate_id
        if operate_name is not None:
            self.operate_name = operate_name
        if operate_state is not None:
            self.operate_state = operate_state
        if operator_id is not None:
            self.operator_id = operator_id
        if operator_name is not None:
            self.operator_name = operator_name
        if start_time is not None:
            self.start_time = start_time
        if update_time is not None:
            self.update_time = update_time

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ItemForListOperationsOutput.  # noqa: E501


        :return: The cluster_id of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ItemForListOperationsOutput.


        :param cluster_id: The cluster_id of this ItemForListOperationsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListOperationsOutput.  # noqa: E501


        :return: The create_time of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListOperationsOutput.


        :param create_time: The create_time of this ItemForListOperationsOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def end_time(self):
        """Gets the end_time of this ItemForListOperationsOutput.  # noqa: E501


        :return: The end_time of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this ItemForListOperationsOutput.


        :param end_time: The end_time of this ItemForListOperationsOutput.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def operate_id(self):
        """Gets the operate_id of this ItemForListOperationsOutput.  # noqa: E501


        :return: The operate_id of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._operate_id

    @operate_id.setter
    def operate_id(self, operate_id):
        """Sets the operate_id of this ItemForListOperationsOutput.


        :param operate_id: The operate_id of this ItemForListOperationsOutput.  # noqa: E501
        :type: str
        """

        self._operate_id = operate_id

    @property
    def operate_name(self):
        """Gets the operate_name of this ItemForListOperationsOutput.  # noqa: E501


        :return: The operate_name of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._operate_name

    @operate_name.setter
    def operate_name(self, operate_name):
        """Sets the operate_name of this ItemForListOperationsOutput.


        :param operate_name: The operate_name of this ItemForListOperationsOutput.  # noqa: E501
        :type: str
        """

        self._operate_name = operate_name

    @property
    def operate_state(self):
        """Gets the operate_state of this ItemForListOperationsOutput.  # noqa: E501


        :return: The operate_state of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._operate_state

    @operate_state.setter
    def operate_state(self, operate_state):
        """Sets the operate_state of this ItemForListOperationsOutput.


        :param operate_state: The operate_state of this ItemForListOperationsOutput.  # noqa: E501
        :type: str
        """

        self._operate_state = operate_state

    @property
    def operator_id(self):
        """Gets the operator_id of this ItemForListOperationsOutput.  # noqa: E501


        :return: The operator_id of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._operator_id

    @operator_id.setter
    def operator_id(self, operator_id):
        """Sets the operator_id of this ItemForListOperationsOutput.


        :param operator_id: The operator_id of this ItemForListOperationsOutput.  # noqa: E501
        :type: str
        """

        self._operator_id = operator_id

    @property
    def operator_name(self):
        """Gets the operator_name of this ItemForListOperationsOutput.  # noqa: E501


        :return: The operator_name of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._operator_name

    @operator_name.setter
    def operator_name(self, operator_name):
        """Sets the operator_name of this ItemForListOperationsOutput.


        :param operator_name: The operator_name of this ItemForListOperationsOutput.  # noqa: E501
        :type: str
        """

        self._operator_name = operator_name

    @property
    def start_time(self):
        """Gets the start_time of this ItemForListOperationsOutput.  # noqa: E501


        :return: The start_time of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ItemForListOperationsOutput.


        :param start_time: The start_time of this ItemForListOperationsOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListOperationsOutput.  # noqa: E501


        :return: The update_time of this ItemForListOperationsOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListOperationsOutput.


        :param update_time: The update_time of this ItemForListOperationsOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListOperationsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListOperationsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListOperationsOutput):
            return True

        return self.to_dict() != other.to_dict()
