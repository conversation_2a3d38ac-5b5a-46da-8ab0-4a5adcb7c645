# coding: utf-8

"""
    cr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ImageAttributeForListTagsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'architecture': 'str',
        'author': 'str',
        'digest': 'str',
        'os': 'str',
        'size': 'int'
    }

    attribute_map = {
        'architecture': 'Architecture',
        'author': 'Author',
        'digest': 'Digest',
        'os': 'Os',
        'size': 'Size'
    }

    def __init__(self, architecture=None, author=None, digest=None, os=None, size=None, _configuration=None):  # noqa: E501
        """ImageAttributeForListTagsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._architecture = None
        self._author = None
        self._digest = None
        self._os = None
        self._size = None
        self.discriminator = None

        if architecture is not None:
            self.architecture = architecture
        if author is not None:
            self.author = author
        if digest is not None:
            self.digest = digest
        if os is not None:
            self.os = os
        if size is not None:
            self.size = size

    @property
    def architecture(self):
        """Gets the architecture of this ImageAttributeForListTagsOutput.  # noqa: E501


        :return: The architecture of this ImageAttributeForListTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._architecture

    @architecture.setter
    def architecture(self, architecture):
        """Sets the architecture of this ImageAttributeForListTagsOutput.


        :param architecture: The architecture of this ImageAttributeForListTagsOutput.  # noqa: E501
        :type: str
        """

        self._architecture = architecture

    @property
    def author(self):
        """Gets the author of this ImageAttributeForListTagsOutput.  # noqa: E501


        :return: The author of this ImageAttributeForListTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._author

    @author.setter
    def author(self, author):
        """Sets the author of this ImageAttributeForListTagsOutput.


        :param author: The author of this ImageAttributeForListTagsOutput.  # noqa: E501
        :type: str
        """

        self._author = author

    @property
    def digest(self):
        """Gets the digest of this ImageAttributeForListTagsOutput.  # noqa: E501


        :return: The digest of this ImageAttributeForListTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._digest

    @digest.setter
    def digest(self, digest):
        """Sets the digest of this ImageAttributeForListTagsOutput.


        :param digest: The digest of this ImageAttributeForListTagsOutput.  # noqa: E501
        :type: str
        """

        self._digest = digest

    @property
    def os(self):
        """Gets the os of this ImageAttributeForListTagsOutput.  # noqa: E501


        :return: The os of this ImageAttributeForListTagsOutput.  # noqa: E501
        :rtype: str
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this ImageAttributeForListTagsOutput.


        :param os: The os of this ImageAttributeForListTagsOutput.  # noqa: E501
        :type: str
        """

        self._os = os

    @property
    def size(self):
        """Gets the size of this ImageAttributeForListTagsOutput.  # noqa: E501


        :return: The size of this ImageAttributeForListTagsOutput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this ImageAttributeForListTagsOutput.


        :param size: The size of this ImageAttributeForListTagsOutput.  # noqa: E501
        :type: int
        """

        self._size = size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ImageAttributeForListTagsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ImageAttributeForListTagsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ImageAttributeForListTagsOutput):
            return True

        return self.to_dict() != other.to_dict()
