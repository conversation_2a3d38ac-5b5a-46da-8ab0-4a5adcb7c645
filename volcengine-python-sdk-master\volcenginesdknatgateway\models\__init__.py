# coding: utf-8

# flake8: noqa
"""
    natgateway

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdknatgateway.models.create_dnat_entry_request import CreateDnatEntryRequest
from volcenginesdknatgateway.models.create_dnat_entry_response import CreateDnatEntryResponse
from volcenginesdknatgateway.models.create_nat_gateway_request import CreateNatGatewayRequest
from volcenginesdknatgateway.models.create_nat_gateway_response import CreateNatGatewayResponse
from volcenginesdknatgateway.models.create_snat_entry_request import CreateSnatEntryRequest
from volcenginesdknatgateway.models.create_snat_entry_response import CreateSnatEntryResponse
from volcenginesdknatgateway.models.delete_dnat_entry_request import DeleteDnatEntryRequest
from volcenginesdknatgateway.models.delete_dnat_entry_response import DeleteDnatEntryResponse
from volcenginesdknatgateway.models.delete_nat_gateway_request import DeleteNatGatewayRequest
from volcenginesdknatgateway.models.delete_nat_gateway_response import DeleteNatGatewayResponse
from volcenginesdknatgateway.models.delete_snat_entry_request import DeleteSnatEntryRequest
from volcenginesdknatgateway.models.delete_snat_entry_response import DeleteSnatEntryResponse
from volcenginesdknatgateway.models.describe_dnat_entries_request import DescribeDnatEntriesRequest
from volcenginesdknatgateway.models.describe_dnat_entries_response import DescribeDnatEntriesResponse
from volcenginesdknatgateway.models.describe_dnat_entry_attributes_request import DescribeDnatEntryAttributesRequest
from volcenginesdknatgateway.models.describe_dnat_entry_attributes_response import DescribeDnatEntryAttributesResponse
from volcenginesdknatgateway.models.describe_nat_gateway_attributes_request import DescribeNatGatewayAttributesRequest
from volcenginesdknatgateway.models.describe_nat_gateway_attributes_response import DescribeNatGatewayAttributesResponse
from volcenginesdknatgateway.models.describe_nat_gateways_request import DescribeNatGatewaysRequest
from volcenginesdknatgateway.models.describe_nat_gateways_response import DescribeNatGatewaysResponse
from volcenginesdknatgateway.models.describe_snat_entries_request import DescribeSnatEntriesRequest
from volcenginesdknatgateway.models.describe_snat_entries_response import DescribeSnatEntriesResponse
from volcenginesdknatgateway.models.describe_snat_entry_attributes_request import DescribeSnatEntryAttributesRequest
from volcenginesdknatgateway.models.describe_snat_entry_attributes_response import DescribeSnatEntryAttributesResponse
from volcenginesdknatgateway.models.dnat_entry_for_describe_dnat_entries_output import DnatEntryForDescribeDnatEntriesOutput
from volcenginesdknatgateway.models.eip_address_for_describe_nat_gateway_attributes_output import EipAddressForDescribeNatGatewayAttributesOutput
from volcenginesdknatgateway.models.eip_address_for_describe_nat_gateways_output import EipAddressForDescribeNatGatewaysOutput
from volcenginesdknatgateway.models.list_nat_gateway_available_zones_request import ListNatGatewayAvailableZonesRequest
from volcenginesdknatgateway.models.list_nat_gateway_available_zones_response import ListNatGatewayAvailableZonesResponse
from volcenginesdknatgateway.models.modify_dnat_entry_attributes_request import ModifyDnatEntryAttributesRequest
from volcenginesdknatgateway.models.modify_dnat_entry_attributes_response import ModifyDnatEntryAttributesResponse
from volcenginesdknatgateway.models.modify_nat_gateway_attributes_request import ModifyNatGatewayAttributesRequest
from volcenginesdknatgateway.models.modify_nat_gateway_attributes_response import ModifyNatGatewayAttributesResponse
from volcenginesdknatgateway.models.modify_snat_entry_attributes_request import ModifySnatEntryAttributesRequest
from volcenginesdknatgateway.models.modify_snat_entry_attributes_response import ModifySnatEntryAttributesResponse
from volcenginesdknatgateway.models.nat_gateway_for_describe_nat_gateways_output import NatGatewayForDescribeNatGatewaysOutput
from volcenginesdknatgateway.models.snat_entry_for_describe_snat_entries_output import SnatEntryForDescribeSnatEntriesOutput
from volcenginesdknatgateway.models.tag_filter_for_describe_nat_gateways_input import TagFilterForDescribeNatGatewaysInput
from volcenginesdknatgateway.models.tag_for_create_nat_gateway_input import TagForCreateNatGatewayInput
from volcenginesdknatgateway.models.tag_for_describe_nat_gateway_attributes_output import TagForDescribeNatGatewayAttributesOutput
from volcenginesdknatgateway.models.tag_for_describe_nat_gateways_output import TagForDescribeNatGatewaysOutput
from volcenginesdknatgateway.models.zone_for_list_nat_gateway_available_zones_output import ZoneForListNatGatewayAvailableZonesOutput
