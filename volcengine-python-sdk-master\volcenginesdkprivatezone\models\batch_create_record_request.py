# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BatchCreateRecordRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'records': 'list[RecordForBatchCreateRecordInput]',
        'zid': 'int'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'records': 'Records',
        'zid': 'ZID'
    }

    def __init__(self, client_token=None, records=None, zid=None, _configuration=None):  # noqa: E501
        """BatchCreateRecordRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._records = None
        self._zid = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if records is not None:
            self.records = records
        self.zid = zid

    @property
    def client_token(self):
        """Gets the client_token of this BatchCreateRecordRequest.  # noqa: E501


        :return: The client_token of this BatchCreateRecordRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this BatchCreateRecordRequest.


        :param client_token: The client_token of this BatchCreateRecordRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def records(self):
        """Gets the records of this BatchCreateRecordRequest.  # noqa: E501


        :return: The records of this BatchCreateRecordRequest.  # noqa: E501
        :rtype: list[RecordForBatchCreateRecordInput]
        """
        return self._records

    @records.setter
    def records(self, records):
        """Sets the records of this BatchCreateRecordRequest.


        :param records: The records of this BatchCreateRecordRequest.  # noqa: E501
        :type: list[RecordForBatchCreateRecordInput]
        """

        self._records = records

    @property
    def zid(self):
        """Gets the zid of this BatchCreateRecordRequest.  # noqa: E501


        :return: The zid of this BatchCreateRecordRequest.  # noqa: E501
        :rtype: int
        """
        return self._zid

    @zid.setter
    def zid(self, zid):
        """Sets the zid of this BatchCreateRecordRequest.


        :param zid: The zid of this BatchCreateRecordRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and zid is None:
            raise ValueError("Invalid value for `zid`, must not be `None`")  # noqa: E501

        self._zid = zid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BatchCreateRecordRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BatchCreateRecordRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BatchCreateRecordRequest):
            return True

        return self.to_dict() != other.to_dict()
