# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateViewingRestrictionResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'sub_viewing_restriction': 'SubViewingRestrictionForUpdateViewingRestrictionOutput',
        'viewing_restriction': 'ViewingRestrictionForUpdateViewingRestrictionOutput'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'sub_viewing_restriction': 'SubViewingRestriction',
        'viewing_restriction': 'ViewingRestriction'
    }

    def __init__(self, activity_id=None, sub_viewing_restriction=None, viewing_restriction=None, _configuration=None):  # noqa: E501
        """UpdateViewingRestrictionResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._sub_viewing_restriction = None
        self._viewing_restriction = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if sub_viewing_restriction is not None:
            self.sub_viewing_restriction = sub_viewing_restriction
        if viewing_restriction is not None:
            self.viewing_restriction = viewing_restriction

    @property
    def activity_id(self):
        """Gets the activity_id of this UpdateViewingRestrictionResponse.  # noqa: E501


        :return: The activity_id of this UpdateViewingRestrictionResponse.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this UpdateViewingRestrictionResponse.


        :param activity_id: The activity_id of this UpdateViewingRestrictionResponse.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def sub_viewing_restriction(self):
        """Gets the sub_viewing_restriction of this UpdateViewingRestrictionResponse.  # noqa: E501


        :return: The sub_viewing_restriction of this UpdateViewingRestrictionResponse.  # noqa: E501
        :rtype: SubViewingRestrictionForUpdateViewingRestrictionOutput
        """
        return self._sub_viewing_restriction

    @sub_viewing_restriction.setter
    def sub_viewing_restriction(self, sub_viewing_restriction):
        """Sets the sub_viewing_restriction of this UpdateViewingRestrictionResponse.


        :param sub_viewing_restriction: The sub_viewing_restriction of this UpdateViewingRestrictionResponse.  # noqa: E501
        :type: SubViewingRestrictionForUpdateViewingRestrictionOutput
        """

        self._sub_viewing_restriction = sub_viewing_restriction

    @property
    def viewing_restriction(self):
        """Gets the viewing_restriction of this UpdateViewingRestrictionResponse.  # noqa: E501


        :return: The viewing_restriction of this UpdateViewingRestrictionResponse.  # noqa: E501
        :rtype: ViewingRestrictionForUpdateViewingRestrictionOutput
        """
        return self._viewing_restriction

    @viewing_restriction.setter
    def viewing_restriction(self, viewing_restriction):
        """Sets the viewing_restriction of this UpdateViewingRestrictionResponse.


        :param viewing_restriction: The viewing_restriction of this UpdateViewingRestrictionResponse.  # noqa: E501
        :type: ViewingRestrictionForUpdateViewingRestrictionOutput
        """

        self._viewing_restriction = viewing_restriction

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateViewingRestrictionResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateViewingRestrictionResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateViewingRestrictionResponse):
            return True

        return self.to_dict() != other.to_dict()
