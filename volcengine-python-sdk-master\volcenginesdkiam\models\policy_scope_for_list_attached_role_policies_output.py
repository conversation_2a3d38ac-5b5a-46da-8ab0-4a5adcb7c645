# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PolicyScopeForListAttachedRolePoliciesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attach_date': 'str',
        'policy_scope_type': 'str',
        'project_display_name': 'str',
        'project_name': 'str'
    }

    attribute_map = {
        'attach_date': 'AttachDate',
        'policy_scope_type': 'PolicyScopeType',
        'project_display_name': 'ProjectDisplayName',
        'project_name': 'ProjectName'
    }

    def __init__(self, attach_date=None, policy_scope_type=None, project_display_name=None, project_name=None, _configuration=None):  # noqa: E501
        """PolicyScopeForListAttachedRolePoliciesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attach_date = None
        self._policy_scope_type = None
        self._project_display_name = None
        self._project_name = None
        self.discriminator = None

        if attach_date is not None:
            self.attach_date = attach_date
        if policy_scope_type is not None:
            self.policy_scope_type = policy_scope_type
        if project_display_name is not None:
            self.project_display_name = project_display_name
        if project_name is not None:
            self.project_name = project_name

    @property
    def attach_date(self):
        """Gets the attach_date of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501


        :return: The attach_date of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._attach_date

    @attach_date.setter
    def attach_date(self, attach_date):
        """Sets the attach_date of this PolicyScopeForListAttachedRolePoliciesOutput.


        :param attach_date: The attach_date of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._attach_date = attach_date

    @property
    def policy_scope_type(self):
        """Gets the policy_scope_type of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501


        :return: The policy_scope_type of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._policy_scope_type

    @policy_scope_type.setter
    def policy_scope_type(self, policy_scope_type):
        """Sets the policy_scope_type of this PolicyScopeForListAttachedRolePoliciesOutput.


        :param policy_scope_type: The policy_scope_type of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._policy_scope_type = policy_scope_type

    @property
    def project_display_name(self):
        """Gets the project_display_name of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501


        :return: The project_display_name of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_display_name

    @project_display_name.setter
    def project_display_name(self, project_display_name):
        """Sets the project_display_name of this PolicyScopeForListAttachedRolePoliciesOutput.


        :param project_display_name: The project_display_name of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._project_display_name = project_display_name

    @property
    def project_name(self):
        """Gets the project_name of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501


        :return: The project_name of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this PolicyScopeForListAttachedRolePoliciesOutput.


        :param project_name: The project_name of this PolicyScopeForListAttachedRolePoliciesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PolicyScopeForListAttachedRolePoliciesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PolicyScopeForListAttachedRolePoliciesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PolicyScopeForListAttachedRolePoliciesOutput):
            return True

        return self.to_dict() != other.to_dict()
