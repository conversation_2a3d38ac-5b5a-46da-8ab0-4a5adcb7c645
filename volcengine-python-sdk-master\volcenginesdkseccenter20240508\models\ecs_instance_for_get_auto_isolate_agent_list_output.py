# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EcsInstanceForGetAutoIsolateAgentListOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_key': 'str',
        'account_id': 'str',
        'cpu': 'int',
        'ecs_update_at': 'str',
        'eip_address': 'str',
        'host_name': 'str',
        'ip_list': 'list[str]',
        'instance_name': 'str',
        'mem': 'int',
        'os_name': 'str',
        'os_type': 'str',
        'platform': 'str',
        'platform_version': 'str',
        'primary_ip_address': 'str',
        'region': 'str',
        'status': 'str',
        'visibility': 'str',
        'vpc_id': 'str',
        'vpc_name': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'access_key': 'AccessKey',
        'account_id': 'AccountID',
        'cpu': 'Cpu',
        'ecs_update_at': 'ECSUpdateAt',
        'eip_address': 'EipAddress',
        'host_name': 'HostName',
        'ip_list': 'IPList',
        'instance_name': 'InstanceName',
        'mem': 'Mem',
        'os_name': 'OsName',
        'os_type': 'OsType',
        'platform': 'Platform',
        'platform_version': 'PlatformVersion',
        'primary_ip_address': 'PrimaryIpAddress',
        'region': 'Region',
        'status': 'Status',
        'visibility': 'Visibility',
        'vpc_id': 'VpcId',
        'vpc_name': 'VpcName',
        'zone_id': 'ZoneId'
    }

    def __init__(self, access_key=None, account_id=None, cpu=None, ecs_update_at=None, eip_address=None, host_name=None, ip_list=None, instance_name=None, mem=None, os_name=None, os_type=None, platform=None, platform_version=None, primary_ip_address=None, region=None, status=None, visibility=None, vpc_id=None, vpc_name=None, zone_id=None, _configuration=None):  # noqa: E501
        """EcsInstanceForGetAutoIsolateAgentListOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_key = None
        self._account_id = None
        self._cpu = None
        self._ecs_update_at = None
        self._eip_address = None
        self._host_name = None
        self._ip_list = None
        self._instance_name = None
        self._mem = None
        self._os_name = None
        self._os_type = None
        self._platform = None
        self._platform_version = None
        self._primary_ip_address = None
        self._region = None
        self._status = None
        self._visibility = None
        self._vpc_id = None
        self._vpc_name = None
        self._zone_id = None
        self.discriminator = None

        if access_key is not None:
            self.access_key = access_key
        if account_id is not None:
            self.account_id = account_id
        if cpu is not None:
            self.cpu = cpu
        if ecs_update_at is not None:
            self.ecs_update_at = ecs_update_at
        if eip_address is not None:
            self.eip_address = eip_address
        if host_name is not None:
            self.host_name = host_name
        if ip_list is not None:
            self.ip_list = ip_list
        if instance_name is not None:
            self.instance_name = instance_name
        if mem is not None:
            self.mem = mem
        if os_name is not None:
            self.os_name = os_name
        if os_type is not None:
            self.os_type = os_type
        if platform is not None:
            self.platform = platform
        if platform_version is not None:
            self.platform_version = platform_version
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if region is not None:
            self.region = region
        if status is not None:
            self.status = status
        if visibility is not None:
            self.visibility = visibility
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if vpc_name is not None:
            self.vpc_name = vpc_name
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def access_key(self):
        """Gets the access_key of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The access_key of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._access_key

    @access_key.setter
    def access_key(self, access_key):
        """Sets the access_key of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param access_key: The access_key of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._access_key = access_key

    @property
    def account_id(self):
        """Gets the account_id of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The account_id of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param account_id: The account_id of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def cpu(self):
        """Gets the cpu of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The cpu of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: int
        """
        return self._cpu

    @cpu.setter
    def cpu(self, cpu):
        """Sets the cpu of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param cpu: The cpu of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: int
        """

        self._cpu = cpu

    @property
    def ecs_update_at(self):
        """Gets the ecs_update_at of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The ecs_update_at of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_update_at

    @ecs_update_at.setter
    def ecs_update_at(self, ecs_update_at):
        """Sets the ecs_update_at of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param ecs_update_at: The ecs_update_at of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._ecs_update_at = ecs_update_at

    @property
    def eip_address(self):
        """Gets the eip_address of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The eip_address of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param eip_address: The eip_address of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def host_name(self):
        """Gets the host_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The host_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._host_name

    @host_name.setter
    def host_name(self, host_name):
        """Sets the host_name of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param host_name: The host_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._host_name = host_name

    @property
    def ip_list(self):
        """Gets the ip_list of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The ip_list of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ip_list

    @ip_list.setter
    def ip_list(self, ip_list):
        """Sets the ip_list of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param ip_list: The ip_list of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: list[str]
        """

        self._ip_list = ip_list

    @property
    def instance_name(self):
        """Gets the instance_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The instance_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param instance_name: The instance_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def mem(self):
        """Gets the mem of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The mem of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: int
        """
        return self._mem

    @mem.setter
    def mem(self, mem):
        """Sets the mem of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param mem: The mem of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: int
        """

        self._mem = mem

    @property
    def os_name(self):
        """Gets the os_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The os_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_name

    @os_name.setter
    def os_name(self, os_name):
        """Sets the os_name of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param os_name: The os_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._os_name = os_name

    @property
    def os_type(self):
        """Gets the os_type of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The os_type of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._os_type

    @os_type.setter
    def os_type(self, os_type):
        """Sets the os_type of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param os_type: The os_type of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._os_type = os_type

    @property
    def platform(self):
        """Gets the platform of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The platform of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param platform: The platform of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._platform = platform

    @property
    def platform_version(self):
        """Gets the platform_version of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The platform_version of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform_version

    @platform_version.setter
    def platform_version(self, platform_version):
        """Sets the platform_version of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param platform_version: The platform_version of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._platform_version = platform_version

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The primary_ip_address of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param primary_ip_address: The primary_ip_address of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def region(self):
        """Gets the region of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The region of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param region: The region of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def status(self):
        """Gets the status of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The status of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param status: The status of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def visibility(self):
        """Gets the visibility of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The visibility of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._visibility

    @visibility.setter
    def visibility(self, visibility):
        """Sets the visibility of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param visibility: The visibility of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._visibility = visibility

    @property
    def vpc_id(self):
        """Gets the vpc_id of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The vpc_id of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param vpc_id: The vpc_id of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def vpc_name(self):
        """Gets the vpc_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The vpc_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_name

    @vpc_name.setter
    def vpc_name(self, vpc_name):
        """Sets the vpc_name of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param vpc_name: The vpc_name of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._vpc_name = vpc_name

    @property
    def zone_id(self):
        """Gets the zone_id of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501


        :return: The zone_id of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this EcsInstanceForGetAutoIsolateAgentListOutput.


        :param zone_id: The zone_id of this EcsInstanceForGetAutoIsolateAgentListOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EcsInstanceForGetAutoIsolateAgentListOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EcsInstanceForGetAutoIsolateAgentListOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EcsInstanceForGetAutoIsolateAgentListOutput):
            return True

        return self.to_dict() != other.to_dict()
