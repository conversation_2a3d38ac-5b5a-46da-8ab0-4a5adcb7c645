# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MessageListForQueryMessageByTimestampOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'int',
        'key': 'str',
        'message': 'str',
        'message_offset': 'int',
        'message_size': 'int',
        'partition_id': 'int',
        'topic_name': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'key': 'Key',
        'message': 'Message',
        'message_offset': 'MessageOffset',
        'message_size': 'MessageSize',
        'partition_id': 'PartitionId',
        'topic_name': 'TopicName'
    }

    def __init__(self, create_time=None, key=None, message=None, message_offset=None, message_size=None, partition_id=None, topic_name=None, _configuration=None):  # noqa: E501
        """MessageListForQueryMessageByTimestampOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._key = None
        self._message = None
        self._message_offset = None
        self._message_size = None
        self._partition_id = None
        self._topic_name = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if key is not None:
            self.key = key
        if message is not None:
            self.message = message
        if message_offset is not None:
            self.message_offset = message_offset
        if message_size is not None:
            self.message_size = message_size
        if partition_id is not None:
            self.partition_id = partition_id
        if topic_name is not None:
            self.topic_name = topic_name

    @property
    def create_time(self):
        """Gets the create_time of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501


        :return: The create_time of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this MessageListForQueryMessageByTimestampOutput.


        :param create_time: The create_time of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def key(self):
        """Gets the key of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501


        :return: The key of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this MessageListForQueryMessageByTimestampOutput.


        :param key: The key of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :type: str
        """

        self._key = key

    @property
    def message(self):
        """Gets the message of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501


        :return: The message of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this MessageListForQueryMessageByTimestampOutput.


        :param message: The message of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def message_offset(self):
        """Gets the message_offset of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501


        :return: The message_offset of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :rtype: int
        """
        return self._message_offset

    @message_offset.setter
    def message_offset(self, message_offset):
        """Sets the message_offset of this MessageListForQueryMessageByTimestampOutput.


        :param message_offset: The message_offset of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :type: int
        """

        self._message_offset = message_offset

    @property
    def message_size(self):
        """Gets the message_size of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501


        :return: The message_size of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :rtype: int
        """
        return self._message_size

    @message_size.setter
    def message_size(self, message_size):
        """Sets the message_size of this MessageListForQueryMessageByTimestampOutput.


        :param message_size: The message_size of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :type: int
        """

        self._message_size = message_size

    @property
    def partition_id(self):
        """Gets the partition_id of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501


        :return: The partition_id of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :rtype: int
        """
        return self._partition_id

    @partition_id.setter
    def partition_id(self, partition_id):
        """Sets the partition_id of this MessageListForQueryMessageByTimestampOutput.


        :param partition_id: The partition_id of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :type: int
        """

        self._partition_id = partition_id

    @property
    def topic_name(self):
        """Gets the topic_name of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501


        :return: The topic_name of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :rtype: str
        """
        return self._topic_name

    @topic_name.setter
    def topic_name(self, topic_name):
        """Sets the topic_name of this MessageListForQueryMessageByTimestampOutput.


        :param topic_name: The topic_name of this MessageListForQueryMessageByTimestampOutput.  # noqa: E501
        :type: str
        """

        self._topic_name = topic_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MessageListForQueryMessageByTimestampOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MessageListForQueryMessageByTimestampOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MessageListForQueryMessageByTimestampOutput):
            return True

        return self.to_dict() != other.to_dict()
