# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'default_bandwidth': 'int',
        'default_max_connections': 'int',
        'shard_number': 'int'
    }

    attribute_map = {
        'default_bandwidth': 'DefaultBandwidth',
        'default_max_connections': 'DefaultMaxConnections',
        'shard_number': 'ShardNumber'
    }

    def __init__(self, default_bandwidth=None, default_max_connections=None, shard_number=None, _configuration=None):  # noqa: E501
        """ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._default_bandwidth = None
        self._default_max_connections = None
        self._shard_number = None
        self.discriminator = None

        if default_bandwidth is not None:
            self.default_bandwidth = default_bandwidth
        if default_max_connections is not None:
            self.default_max_connections = default_max_connections
        if shard_number is not None:
            self.shard_number = shard_number

    @property
    def default_bandwidth(self):
        """Gets the default_bandwidth of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501


        :return: The default_bandwidth of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._default_bandwidth

    @default_bandwidth.setter
    def default_bandwidth(self, default_bandwidth):
        """Sets the default_bandwidth of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.


        :param default_bandwidth: The default_bandwidth of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._default_bandwidth = default_bandwidth

    @property
    def default_max_connections(self):
        """Gets the default_max_connections of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501


        :return: The default_max_connections of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._default_max_connections

    @default_max_connections.setter
    def default_max_connections(self, default_max_connections):
        """Sets the default_max_connections of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.


        :param default_max_connections: The default_max_connections of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._default_max_connections = default_max_connections

    @property
    def shard_number(self):
        """Gets the shard_number of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501


        :return: The shard_number of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._shard_number

    @shard_number.setter
    def shard_number(self, shard_number):
        """Sets the shard_number of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.


        :param shard_number: The shard_number of this ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._shard_number = shard_number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ShardNumberSpecForDescribeEnterpriseDBInstanceSpecsOutput):
            return True

        return self.to_dict() != other.to_dict()
