# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetInstanceTypeResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'available_gpu_memory_mi_b': 'int',
        'cpu': 'float',
        'eni_count': 'int',
        'eni_maximum_bandwidth_mbps': 'int',
        'family': 'str',
        'gpu_count': 'int',
        'gpu_memory_mi_b': 'int',
        'gpu_type': 'str',
        'id': 'str',
        'kind': 'str',
        'memory_mi_b': 'float',
        'nvme_ssd_count': 'int',
        'price_by_day': 'float',
        'price_by_hour': 'float',
        'price_by_month': 'float',
        'rdma_eni_count': 'int',
        'rdma_eni_maximum_bandwidth_mbps': 'int',
        'reservation_plan_price_by_hour': 'float',
        'volume_maximum_bandwidth_mbps': 'int',
        'volume_maximum_iops': 'int',
        'volume_supported_types': 'list[str]',
        'zone_info': 'list[ZoneInfoForGetInstanceTypeOutput]'
    }

    attribute_map = {
        'available_gpu_memory_mi_b': 'AvailableGPUMemoryMiB',
        'cpu': 'Cpu',
        'eni_count': 'EniCount',
        'eni_maximum_bandwidth_mbps': 'EniMaximumBandwidthMbps',
        'family': 'Family',
        'gpu_count': 'GpuCount',
        'gpu_memory_mi_b': 'GpuMemoryMiB',
        'gpu_type': 'GpuType',
        'id': 'Id',
        'kind': 'Kind',
        'memory_mi_b': 'MemoryMiB',
        'nvme_ssd_count': 'NvmeSsdCount',
        'price_by_day': 'PriceByDay',
        'price_by_hour': 'PriceByHour',
        'price_by_month': 'PriceByMonth',
        'rdma_eni_count': 'RdmaEniCount',
        'rdma_eni_maximum_bandwidth_mbps': 'RdmaEniMaximumBandwidthMbps',
        'reservation_plan_price_by_hour': 'ReservationPlanPriceByHour',
        'volume_maximum_bandwidth_mbps': 'VolumeMaximumBandwidthMbps',
        'volume_maximum_iops': 'VolumeMaximumIops',
        'volume_supported_types': 'VolumeSupportedTypes',
        'zone_info': 'ZoneInfo'
    }

    def __init__(self, available_gpu_memory_mi_b=None, cpu=None, eni_count=None, eni_maximum_bandwidth_mbps=None, family=None, gpu_count=None, gpu_memory_mi_b=None, gpu_type=None, id=None, kind=None, memory_mi_b=None, nvme_ssd_count=None, price_by_day=None, price_by_hour=None, price_by_month=None, rdma_eni_count=None, rdma_eni_maximum_bandwidth_mbps=None, reservation_plan_price_by_hour=None, volume_maximum_bandwidth_mbps=None, volume_maximum_iops=None, volume_supported_types=None, zone_info=None, _configuration=None):  # noqa: E501
        """GetInstanceTypeResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._available_gpu_memory_mi_b = None
        self._cpu = None
        self._eni_count = None
        self._eni_maximum_bandwidth_mbps = None
        self._family = None
        self._gpu_count = None
        self._gpu_memory_mi_b = None
        self._gpu_type = None
        self._id = None
        self._kind = None
        self._memory_mi_b = None
        self._nvme_ssd_count = None
        self._price_by_day = None
        self._price_by_hour = None
        self._price_by_month = None
        self._rdma_eni_count = None
        self._rdma_eni_maximum_bandwidth_mbps = None
        self._reservation_plan_price_by_hour = None
        self._volume_maximum_bandwidth_mbps = None
        self._volume_maximum_iops = None
        self._volume_supported_types = None
        self._zone_info = None
        self.discriminator = None

        if available_gpu_memory_mi_b is not None:
            self.available_gpu_memory_mi_b = available_gpu_memory_mi_b
        if cpu is not None:
            self.cpu = cpu
        if eni_count is not None:
            self.eni_count = eni_count
        if eni_maximum_bandwidth_mbps is not None:
            self.eni_maximum_bandwidth_mbps = eni_maximum_bandwidth_mbps
        if family is not None:
            self.family = family
        if gpu_count is not None:
            self.gpu_count = gpu_count
        if gpu_memory_mi_b is not None:
            self.gpu_memory_mi_b = gpu_memory_mi_b
        if gpu_type is not None:
            self.gpu_type = gpu_type
        if id is not None:
            self.id = id
        if kind is not None:
            self.kind = kind
        if memory_mi_b is not None:
            self.memory_mi_b = memory_mi_b
        if nvme_ssd_count is not None:
            self.nvme_ssd_count = nvme_ssd_count
        if price_by_day is not None:
            self.price_by_day = price_by_day
        if price_by_hour is not None:
            self.price_by_hour = price_by_hour
        if price_by_month is not None:
            self.price_by_month = price_by_month
        if rdma_eni_count is not None:
            self.rdma_eni_count = rdma_eni_count
        if rdma_eni_maximum_bandwidth_mbps is not None:
            self.rdma_eni_maximum_bandwidth_mbps = rdma_eni_maximum_bandwidth_mbps
        if reservation_plan_price_by_hour is not None:
            self.reservation_plan_price_by_hour = reservation_plan_price_by_hour
        if volume_maximum_bandwidth_mbps is not None:
            self.volume_maximum_bandwidth_mbps = volume_maximum_bandwidth_mbps
        if volume_maximum_iops is not None:
            self.volume_maximum_iops = volume_maximum_iops
        if volume_supported_types is not None:
            self.volume_supported_types = volume_supported_types
        if zone_info is not None:
            self.zone_info = zone_info

    @property
    def available_gpu_memory_mi_b(self):
        """Gets the available_gpu_memory_mi_b of this GetInstanceTypeResponse.  # noqa: E501


        :return: The available_gpu_memory_mi_b of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._available_gpu_memory_mi_b

    @available_gpu_memory_mi_b.setter
    def available_gpu_memory_mi_b(self, available_gpu_memory_mi_b):
        """Sets the available_gpu_memory_mi_b of this GetInstanceTypeResponse.


        :param available_gpu_memory_mi_b: The available_gpu_memory_mi_b of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._available_gpu_memory_mi_b = available_gpu_memory_mi_b

    @property
    def cpu(self):
        """Gets the cpu of this GetInstanceTypeResponse.  # noqa: E501


        :return: The cpu of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: float
        """
        return self._cpu

    @cpu.setter
    def cpu(self, cpu):
        """Sets the cpu of this GetInstanceTypeResponse.


        :param cpu: The cpu of this GetInstanceTypeResponse.  # noqa: E501
        :type: float
        """

        self._cpu = cpu

    @property
    def eni_count(self):
        """Gets the eni_count of this GetInstanceTypeResponse.  # noqa: E501


        :return: The eni_count of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._eni_count

    @eni_count.setter
    def eni_count(self, eni_count):
        """Sets the eni_count of this GetInstanceTypeResponse.


        :param eni_count: The eni_count of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._eni_count = eni_count

    @property
    def eni_maximum_bandwidth_mbps(self):
        """Gets the eni_maximum_bandwidth_mbps of this GetInstanceTypeResponse.  # noqa: E501


        :return: The eni_maximum_bandwidth_mbps of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._eni_maximum_bandwidth_mbps

    @eni_maximum_bandwidth_mbps.setter
    def eni_maximum_bandwidth_mbps(self, eni_maximum_bandwidth_mbps):
        """Sets the eni_maximum_bandwidth_mbps of this GetInstanceTypeResponse.


        :param eni_maximum_bandwidth_mbps: The eni_maximum_bandwidth_mbps of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._eni_maximum_bandwidth_mbps = eni_maximum_bandwidth_mbps

    @property
    def family(self):
        """Gets the family of this GetInstanceTypeResponse.  # noqa: E501


        :return: The family of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: str
        """
        return self._family

    @family.setter
    def family(self, family):
        """Sets the family of this GetInstanceTypeResponse.


        :param family: The family of this GetInstanceTypeResponse.  # noqa: E501
        :type: str
        """

        self._family = family

    @property
    def gpu_count(self):
        """Gets the gpu_count of this GetInstanceTypeResponse.  # noqa: E501


        :return: The gpu_count of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._gpu_count

    @gpu_count.setter
    def gpu_count(self, gpu_count):
        """Sets the gpu_count of this GetInstanceTypeResponse.


        :param gpu_count: The gpu_count of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._gpu_count = gpu_count

    @property
    def gpu_memory_mi_b(self):
        """Gets the gpu_memory_mi_b of this GetInstanceTypeResponse.  # noqa: E501


        :return: The gpu_memory_mi_b of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._gpu_memory_mi_b

    @gpu_memory_mi_b.setter
    def gpu_memory_mi_b(self, gpu_memory_mi_b):
        """Sets the gpu_memory_mi_b of this GetInstanceTypeResponse.


        :param gpu_memory_mi_b: The gpu_memory_mi_b of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._gpu_memory_mi_b = gpu_memory_mi_b

    @property
    def gpu_type(self):
        """Gets the gpu_type of this GetInstanceTypeResponse.  # noqa: E501


        :return: The gpu_type of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: str
        """
        return self._gpu_type

    @gpu_type.setter
    def gpu_type(self, gpu_type):
        """Sets the gpu_type of this GetInstanceTypeResponse.


        :param gpu_type: The gpu_type of this GetInstanceTypeResponse.  # noqa: E501
        :type: str
        """

        self._gpu_type = gpu_type

    @property
    def id(self):
        """Gets the id of this GetInstanceTypeResponse.  # noqa: E501


        :return: The id of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetInstanceTypeResponse.


        :param id: The id of this GetInstanceTypeResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def kind(self):
        """Gets the kind of this GetInstanceTypeResponse.  # noqa: E501


        :return: The kind of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: str
        """
        return self._kind

    @kind.setter
    def kind(self, kind):
        """Sets the kind of this GetInstanceTypeResponse.


        :param kind: The kind of this GetInstanceTypeResponse.  # noqa: E501
        :type: str
        """

        self._kind = kind

    @property
    def memory_mi_b(self):
        """Gets the memory_mi_b of this GetInstanceTypeResponse.  # noqa: E501


        :return: The memory_mi_b of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: float
        """
        return self._memory_mi_b

    @memory_mi_b.setter
    def memory_mi_b(self, memory_mi_b):
        """Sets the memory_mi_b of this GetInstanceTypeResponse.


        :param memory_mi_b: The memory_mi_b of this GetInstanceTypeResponse.  # noqa: E501
        :type: float
        """

        self._memory_mi_b = memory_mi_b

    @property
    def nvme_ssd_count(self):
        """Gets the nvme_ssd_count of this GetInstanceTypeResponse.  # noqa: E501


        :return: The nvme_ssd_count of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._nvme_ssd_count

    @nvme_ssd_count.setter
    def nvme_ssd_count(self, nvme_ssd_count):
        """Sets the nvme_ssd_count of this GetInstanceTypeResponse.


        :param nvme_ssd_count: The nvme_ssd_count of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._nvme_ssd_count = nvme_ssd_count

    @property
    def price_by_day(self):
        """Gets the price_by_day of this GetInstanceTypeResponse.  # noqa: E501


        :return: The price_by_day of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: float
        """
        return self._price_by_day

    @price_by_day.setter
    def price_by_day(self, price_by_day):
        """Sets the price_by_day of this GetInstanceTypeResponse.


        :param price_by_day: The price_by_day of this GetInstanceTypeResponse.  # noqa: E501
        :type: float
        """

        self._price_by_day = price_by_day

    @property
    def price_by_hour(self):
        """Gets the price_by_hour of this GetInstanceTypeResponse.  # noqa: E501


        :return: The price_by_hour of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: float
        """
        return self._price_by_hour

    @price_by_hour.setter
    def price_by_hour(self, price_by_hour):
        """Sets the price_by_hour of this GetInstanceTypeResponse.


        :param price_by_hour: The price_by_hour of this GetInstanceTypeResponse.  # noqa: E501
        :type: float
        """

        self._price_by_hour = price_by_hour

    @property
    def price_by_month(self):
        """Gets the price_by_month of this GetInstanceTypeResponse.  # noqa: E501


        :return: The price_by_month of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: float
        """
        return self._price_by_month

    @price_by_month.setter
    def price_by_month(self, price_by_month):
        """Sets the price_by_month of this GetInstanceTypeResponse.


        :param price_by_month: The price_by_month of this GetInstanceTypeResponse.  # noqa: E501
        :type: float
        """

        self._price_by_month = price_by_month

    @property
    def rdma_eni_count(self):
        """Gets the rdma_eni_count of this GetInstanceTypeResponse.  # noqa: E501


        :return: The rdma_eni_count of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._rdma_eni_count

    @rdma_eni_count.setter
    def rdma_eni_count(self, rdma_eni_count):
        """Sets the rdma_eni_count of this GetInstanceTypeResponse.


        :param rdma_eni_count: The rdma_eni_count of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._rdma_eni_count = rdma_eni_count

    @property
    def rdma_eni_maximum_bandwidth_mbps(self):
        """Gets the rdma_eni_maximum_bandwidth_mbps of this GetInstanceTypeResponse.  # noqa: E501


        :return: The rdma_eni_maximum_bandwidth_mbps of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._rdma_eni_maximum_bandwidth_mbps

    @rdma_eni_maximum_bandwidth_mbps.setter
    def rdma_eni_maximum_bandwidth_mbps(self, rdma_eni_maximum_bandwidth_mbps):
        """Sets the rdma_eni_maximum_bandwidth_mbps of this GetInstanceTypeResponse.


        :param rdma_eni_maximum_bandwidth_mbps: The rdma_eni_maximum_bandwidth_mbps of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._rdma_eni_maximum_bandwidth_mbps = rdma_eni_maximum_bandwidth_mbps

    @property
    def reservation_plan_price_by_hour(self):
        """Gets the reservation_plan_price_by_hour of this GetInstanceTypeResponse.  # noqa: E501


        :return: The reservation_plan_price_by_hour of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: float
        """
        return self._reservation_plan_price_by_hour

    @reservation_plan_price_by_hour.setter
    def reservation_plan_price_by_hour(self, reservation_plan_price_by_hour):
        """Sets the reservation_plan_price_by_hour of this GetInstanceTypeResponse.


        :param reservation_plan_price_by_hour: The reservation_plan_price_by_hour of this GetInstanceTypeResponse.  # noqa: E501
        :type: float
        """

        self._reservation_plan_price_by_hour = reservation_plan_price_by_hour

    @property
    def volume_maximum_bandwidth_mbps(self):
        """Gets the volume_maximum_bandwidth_mbps of this GetInstanceTypeResponse.  # noqa: E501


        :return: The volume_maximum_bandwidth_mbps of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._volume_maximum_bandwidth_mbps

    @volume_maximum_bandwidth_mbps.setter
    def volume_maximum_bandwidth_mbps(self, volume_maximum_bandwidth_mbps):
        """Sets the volume_maximum_bandwidth_mbps of this GetInstanceTypeResponse.


        :param volume_maximum_bandwidth_mbps: The volume_maximum_bandwidth_mbps of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._volume_maximum_bandwidth_mbps = volume_maximum_bandwidth_mbps

    @property
    def volume_maximum_iops(self):
        """Gets the volume_maximum_iops of this GetInstanceTypeResponse.  # noqa: E501


        :return: The volume_maximum_iops of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: int
        """
        return self._volume_maximum_iops

    @volume_maximum_iops.setter
    def volume_maximum_iops(self, volume_maximum_iops):
        """Sets the volume_maximum_iops of this GetInstanceTypeResponse.


        :param volume_maximum_iops: The volume_maximum_iops of this GetInstanceTypeResponse.  # noqa: E501
        :type: int
        """

        self._volume_maximum_iops = volume_maximum_iops

    @property
    def volume_supported_types(self):
        """Gets the volume_supported_types of this GetInstanceTypeResponse.  # noqa: E501


        :return: The volume_supported_types of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._volume_supported_types

    @volume_supported_types.setter
    def volume_supported_types(self, volume_supported_types):
        """Sets the volume_supported_types of this GetInstanceTypeResponse.


        :param volume_supported_types: The volume_supported_types of this GetInstanceTypeResponse.  # noqa: E501
        :type: list[str]
        """

        self._volume_supported_types = volume_supported_types

    @property
    def zone_info(self):
        """Gets the zone_info of this GetInstanceTypeResponse.  # noqa: E501


        :return: The zone_info of this GetInstanceTypeResponse.  # noqa: E501
        :rtype: list[ZoneInfoForGetInstanceTypeOutput]
        """
        return self._zone_info

    @zone_info.setter
    def zone_info(self, zone_info):
        """Sets the zone_info of this GetInstanceTypeResponse.


        :param zone_info: The zone_info of this GetInstanceTypeResponse.  # noqa: E501
        :type: list[ZoneInfoForGetInstanceTypeOutput]
        """

        self._zone_info = zone_info

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetInstanceTypeResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetInstanceTypeResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetInstanceTypeResponse):
            return True

        return self.to_dict() != other.to_dict()
