# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateRegistryFlowRateLimitRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'flow_rate_limit': 'int',
        'registry_id': 'str'
    }

    attribute_map = {
        'flow_rate_limit': 'FlowRateLimit',
        'registry_id': 'RegistryID'
    }

    def __init__(self, flow_rate_limit=None, registry_id=None, _configuration=None):  # noqa: E501
        """UpdateRegistryFlowRateLimitRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._flow_rate_limit = None
        self._registry_id = None
        self.discriminator = None

        self.flow_rate_limit = flow_rate_limit
        self.registry_id = registry_id

    @property
    def flow_rate_limit(self):
        """Gets the flow_rate_limit of this UpdateRegistryFlowRateLimitRequest.  # noqa: E501


        :return: The flow_rate_limit of this UpdateRegistryFlowRateLimitRequest.  # noqa: E501
        :rtype: int
        """
        return self._flow_rate_limit

    @flow_rate_limit.setter
    def flow_rate_limit(self, flow_rate_limit):
        """Sets the flow_rate_limit of this UpdateRegistryFlowRateLimitRequest.


        :param flow_rate_limit: The flow_rate_limit of this UpdateRegistryFlowRateLimitRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and flow_rate_limit is None:
            raise ValueError("Invalid value for `flow_rate_limit`, must not be `None`")  # noqa: E501

        self._flow_rate_limit = flow_rate_limit

    @property
    def registry_id(self):
        """Gets the registry_id of this UpdateRegistryFlowRateLimitRequest.  # noqa: E501


        :return: The registry_id of this UpdateRegistryFlowRateLimitRequest.  # noqa: E501
        :rtype: str
        """
        return self._registry_id

    @registry_id.setter
    def registry_id(self, registry_id):
        """Sets the registry_id of this UpdateRegistryFlowRateLimitRequest.


        :param registry_id: The registry_id of this UpdateRegistryFlowRateLimitRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and registry_id is None:
            raise ValueError("Invalid value for `registry_id`, must not be `None`")  # noqa: E501

        self._registry_id = registry_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateRegistryFlowRateLimitRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateRegistryFlowRateLimitRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateRegistryFlowRateLimitRequest):
            return True

        return self.to_dict() != other.to_dict()
