# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSnapshotsUsageResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'snapshot_count': 'int',
        'snapshot_size': 'int'
    }

    attribute_map = {
        'snapshot_count': 'SnapshotCount',
        'snapshot_size': 'SnapshotSize'
    }

    def __init__(self, snapshot_count=None, snapshot_size=None, _configuration=None):  # noqa: E501
        """DescribeSnapshotsUsageResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._snapshot_count = None
        self._snapshot_size = None
        self.discriminator = None

        if snapshot_count is not None:
            self.snapshot_count = snapshot_count
        if snapshot_size is not None:
            self.snapshot_size = snapshot_size

    @property
    def snapshot_count(self):
        """Gets the snapshot_count of this DescribeSnapshotsUsageResponse.  # noqa: E501


        :return: The snapshot_count of this DescribeSnapshotsUsageResponse.  # noqa: E501
        :rtype: int
        """
        return self._snapshot_count

    @snapshot_count.setter
    def snapshot_count(self, snapshot_count):
        """Sets the snapshot_count of this DescribeSnapshotsUsageResponse.


        :param snapshot_count: The snapshot_count of this DescribeSnapshotsUsageResponse.  # noqa: E501
        :type: int
        """

        self._snapshot_count = snapshot_count

    @property
    def snapshot_size(self):
        """Gets the snapshot_size of this DescribeSnapshotsUsageResponse.  # noqa: E501


        :return: The snapshot_size of this DescribeSnapshotsUsageResponse.  # noqa: E501
        :rtype: int
        """
        return self._snapshot_size

    @snapshot_size.setter
    def snapshot_size(self, snapshot_size):
        """Sets the snapshot_size of this DescribeSnapshotsUsageResponse.


        :param snapshot_size: The snapshot_size of this DescribeSnapshotsUsageResponse.  # noqa: E501
        :type: int
        """

        self._snapshot_size = snapshot_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSnapshotsUsageResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSnapshotsUsageResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSnapshotsUsageResponse):
            return True

        return self.to_dict() != other.to_dict()
