# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateSnapshotRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'description': 'str',
        'project_name': 'str',
        'retention_days': 'int',
        'snapshot_name': 'str',
        'tags': 'list[TagForCreateSnapshotInput]',
        'volume_id': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'description': 'Description',
        'project_name': 'ProjectName',
        'retention_days': 'RetentionDays',
        'snapshot_name': 'SnapshotName',
        'tags': 'Tags',
        'volume_id': 'VolumeId'
    }

    def __init__(self, client_token=None, description=None, project_name=None, retention_days=None, snapshot_name=None, tags=None, volume_id=None, _configuration=None):  # noqa: E501
        """CreateSnapshotRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._description = None
        self._project_name = None
        self._retention_days = None
        self._snapshot_name = None
        self._tags = None
        self._volume_id = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if project_name is not None:
            self.project_name = project_name
        if retention_days is not None:
            self.retention_days = retention_days
        self.snapshot_name = snapshot_name
        if tags is not None:
            self.tags = tags
        self.volume_id = volume_id

    @property
    def client_token(self):
        """Gets the client_token of this CreateSnapshotRequest.  # noqa: E501


        :return: The client_token of this CreateSnapshotRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateSnapshotRequest.


        :param client_token: The client_token of this CreateSnapshotRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateSnapshotRequest.  # noqa: E501


        :return: The description of this CreateSnapshotRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateSnapshotRequest.


        :param description: The description of this CreateSnapshotRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def project_name(self):
        """Gets the project_name of this CreateSnapshotRequest.  # noqa: E501


        :return: The project_name of this CreateSnapshotRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateSnapshotRequest.


        :param project_name: The project_name of this CreateSnapshotRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def retention_days(self):
        """Gets the retention_days of this CreateSnapshotRequest.  # noqa: E501


        :return: The retention_days of this CreateSnapshotRequest.  # noqa: E501
        :rtype: int
        """
        return self._retention_days

    @retention_days.setter
    def retention_days(self, retention_days):
        """Sets the retention_days of this CreateSnapshotRequest.


        :param retention_days: The retention_days of this CreateSnapshotRequest.  # noqa: E501
        :type: int
        """

        self._retention_days = retention_days

    @property
    def snapshot_name(self):
        """Gets the snapshot_name of this CreateSnapshotRequest.  # noqa: E501


        :return: The snapshot_name of this CreateSnapshotRequest.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_name

    @snapshot_name.setter
    def snapshot_name(self, snapshot_name):
        """Sets the snapshot_name of this CreateSnapshotRequest.


        :param snapshot_name: The snapshot_name of this CreateSnapshotRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and snapshot_name is None:
            raise ValueError("Invalid value for `snapshot_name`, must not be `None`")  # noqa: E501

        self._snapshot_name = snapshot_name

    @property
    def tags(self):
        """Gets the tags of this CreateSnapshotRequest.  # noqa: E501


        :return: The tags of this CreateSnapshotRequest.  # noqa: E501
        :rtype: list[TagForCreateSnapshotInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateSnapshotRequest.


        :param tags: The tags of this CreateSnapshotRequest.  # noqa: E501
        :type: list[TagForCreateSnapshotInput]
        """

        self._tags = tags

    @property
    def volume_id(self):
        """Gets the volume_id of this CreateSnapshotRequest.  # noqa: E501


        :return: The volume_id of this CreateSnapshotRequest.  # noqa: E501
        :rtype: str
        """
        return self._volume_id

    @volume_id.setter
    def volume_id(self, volume_id):
        """Sets the volume_id of this CreateSnapshotRequest.


        :param volume_id: The volume_id of this CreateSnapshotRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and volume_id is None:
            raise ValueError("Invalid value for `volume_id`, must not be `None`")  # noqa: E501

        self._volume_id = volume_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateSnapshotRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateSnapshotRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateSnapshotRequest):
            return True

        return self.to_dict() != other.to_dict()
