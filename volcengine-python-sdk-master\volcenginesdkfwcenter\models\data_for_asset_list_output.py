# coding: utf-8

"""
    fwcenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForAssetListOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'instance_id': 'str',
        'eip_id': 'str',
        'enable': 'bool',
        'ip': 'str',
        'latest_7_days_peak_traffic': 'int',
        'name': 'str',
        'region': 'str',
        'region_n': 'str',
        'type': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'instance_id': 'InstanceID',
        'eip_id': 'eip_id',
        'enable': 'enable',
        'ip': 'ip',
        'latest_7_days_peak_traffic': 'latest_7_days_peak_traffic',
        'name': 'name',
        'region': 'region',
        'region_n': 'regionN',
        'type': 'type'
    }

    def __init__(self, account_id=None, instance_id=None, eip_id=None, enable=None, ip=None, latest_7_days_peak_traffic=None, name=None, region=None, region_n=None, type=None, _configuration=None):  # noqa: E501
        """DataForAssetListOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._instance_id = None
        self._eip_id = None
        self._enable = None
        self._ip = None
        self._latest_7_days_peak_traffic = None
        self._name = None
        self._region = None
        self._region_n = None
        self._type = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if instance_id is not None:
            self.instance_id = instance_id
        if eip_id is not None:
            self.eip_id = eip_id
        if enable is not None:
            self.enable = enable
        if ip is not None:
            self.ip = ip
        if latest_7_days_peak_traffic is not None:
            self.latest_7_days_peak_traffic = latest_7_days_peak_traffic
        if name is not None:
            self.name = name
        if region is not None:
            self.region = region
        if region_n is not None:
            self.region_n = region_n
        if type is not None:
            self.type = type

    @property
    def account_id(self):
        """Gets the account_id of this DataForAssetListOutput.  # noqa: E501


        :return: The account_id of this DataForAssetListOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForAssetListOutput.


        :param account_id: The account_id of this DataForAssetListOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def instance_id(self):
        """Gets the instance_id of this DataForAssetListOutput.  # noqa: E501


        :return: The instance_id of this DataForAssetListOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DataForAssetListOutput.


        :param instance_id: The instance_id of this DataForAssetListOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def eip_id(self):
        """Gets the eip_id of this DataForAssetListOutput.  # noqa: E501


        :return: The eip_id of this DataForAssetListOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_id

    @eip_id.setter
    def eip_id(self, eip_id):
        """Sets the eip_id of this DataForAssetListOutput.


        :param eip_id: The eip_id of this DataForAssetListOutput.  # noqa: E501
        :type: str
        """

        self._eip_id = eip_id

    @property
    def enable(self):
        """Gets the enable of this DataForAssetListOutput.  # noqa: E501


        :return: The enable of this DataForAssetListOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this DataForAssetListOutput.


        :param enable: The enable of this DataForAssetListOutput.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def ip(self):
        """Gets the ip of this DataForAssetListOutput.  # noqa: E501


        :return: The ip of this DataForAssetListOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this DataForAssetListOutput.


        :param ip: The ip of this DataForAssetListOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def latest_7_days_peak_traffic(self):
        """Gets the latest_7_days_peak_traffic of this DataForAssetListOutput.  # noqa: E501


        :return: The latest_7_days_peak_traffic of this DataForAssetListOutput.  # noqa: E501
        :rtype: int
        """
        return self._latest_7_days_peak_traffic

    @latest_7_days_peak_traffic.setter
    def latest_7_days_peak_traffic(self, latest_7_days_peak_traffic):
        """Sets the latest_7_days_peak_traffic of this DataForAssetListOutput.


        :param latest_7_days_peak_traffic: The latest_7_days_peak_traffic of this DataForAssetListOutput.  # noqa: E501
        :type: int
        """

        self._latest_7_days_peak_traffic = latest_7_days_peak_traffic

    @property
    def name(self):
        """Gets the name of this DataForAssetListOutput.  # noqa: E501


        :return: The name of this DataForAssetListOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForAssetListOutput.


        :param name: The name of this DataForAssetListOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def region(self):
        """Gets the region of this DataForAssetListOutput.  # noqa: E501


        :return: The region of this DataForAssetListOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForAssetListOutput.


        :param region: The region of this DataForAssetListOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def region_n(self):
        """Gets the region_n of this DataForAssetListOutput.  # noqa: E501


        :return: The region_n of this DataForAssetListOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_n

    @region_n.setter
    def region_n(self, region_n):
        """Sets the region_n of this DataForAssetListOutput.


        :param region_n: The region_n of this DataForAssetListOutput.  # noqa: E501
        :type: str
        """

        self._region_n = region_n

    @property
    def type(self):
        """Gets the type of this DataForAssetListOutput.  # noqa: E501


        :return: The type of this DataForAssetListOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DataForAssetListOutput.


        :param type: The type of this DataForAssetListOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForAssetListOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForAssetListOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForAssetListOutput):
            return True

        return self.to_dict() != other.to_dict()
