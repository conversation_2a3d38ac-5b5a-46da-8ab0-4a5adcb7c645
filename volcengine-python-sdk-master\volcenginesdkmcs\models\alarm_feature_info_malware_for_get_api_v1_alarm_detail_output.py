# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'malware_downloadable': 'bool',
        'malware_family': 'str',
        'malware_file': 'MalwareFileForGetApiV1AlarmDetailOutput',
        'malware_hit_data': 'MalwareHitDataForGetApiV1AlarmDetailOutput',
        'malware_labels': 'list[str]'
    }

    attribute_map = {
        'malware_downloadable': 'malware_downloadable',
        'malware_family': 'malware_family',
        'malware_file': 'malware_file',
        'malware_hit_data': 'malware_hit_data',
        'malware_labels': 'malware_labels'
    }

    def __init__(self, malware_downloadable=None, malware_family=None, malware_file=None, malware_hit_data=None, malware_labels=None, _configuration=None):  # noqa: E501
        """AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._malware_downloadable = None
        self._malware_family = None
        self._malware_file = None
        self._malware_hit_data = None
        self._malware_labels = None
        self.discriminator = None

        if malware_downloadable is not None:
            self.malware_downloadable = malware_downloadable
        if malware_family is not None:
            self.malware_family = malware_family
        if malware_file is not None:
            self.malware_file = malware_file
        if malware_hit_data is not None:
            self.malware_hit_data = malware_hit_data
        if malware_labels is not None:
            self.malware_labels = malware_labels

    @property
    def malware_downloadable(self):
        """Gets the malware_downloadable of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The malware_downloadable of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: bool
        """
        return self._malware_downloadable

    @malware_downloadable.setter
    def malware_downloadable(self, malware_downloadable):
        """Sets the malware_downloadable of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.


        :param malware_downloadable: The malware_downloadable of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: bool
        """

        self._malware_downloadable = malware_downloadable

    @property
    def malware_family(self):
        """Gets the malware_family of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The malware_family of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._malware_family

    @malware_family.setter
    def malware_family(self, malware_family):
        """Sets the malware_family of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.


        :param malware_family: The malware_family of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._malware_family = malware_family

    @property
    def malware_file(self):
        """Gets the malware_file of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The malware_file of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: MalwareFileForGetApiV1AlarmDetailOutput
        """
        return self._malware_file

    @malware_file.setter
    def malware_file(self, malware_file):
        """Sets the malware_file of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.


        :param malware_file: The malware_file of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: MalwareFileForGetApiV1AlarmDetailOutput
        """

        self._malware_file = malware_file

    @property
    def malware_hit_data(self):
        """Gets the malware_hit_data of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The malware_hit_data of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: MalwareHitDataForGetApiV1AlarmDetailOutput
        """
        return self._malware_hit_data

    @malware_hit_data.setter
    def malware_hit_data(self, malware_hit_data):
        """Sets the malware_hit_data of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.


        :param malware_hit_data: The malware_hit_data of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: MalwareHitDataForGetApiV1AlarmDetailOutput
        """

        self._malware_hit_data = malware_hit_data

    @property
    def malware_labels(self):
        """Gets the malware_labels of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501


        :return: The malware_labels of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._malware_labels

    @malware_labels.setter
    def malware_labels(self, malware_labels):
        """Sets the malware_labels of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.


        :param malware_labels: The malware_labels of this AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput.  # noqa: E501
        :type: list[str]
        """

        self._malware_labels = malware_labels

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AlarmFeatureInfoMalwareForGetApiV1AlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
