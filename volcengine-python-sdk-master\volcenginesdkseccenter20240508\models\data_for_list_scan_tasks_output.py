# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListScanTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'action': 'str',
        'create_time': 'int',
        'file_path': 'str',
        'id_count': 'int',
        'result_num': 'int',
        'sub_task_created': 'int',
        'sub_task_failed': 'int',
        'sub_task_running': 'int',
        'sub_task_stopped': 'int',
        'sub_task_succeed': 'int',
        'tag': 'str',
        'task_id': 'str',
        'task_name': 'str',
        'task_status': 'str',
        'task_timeout': 'int',
        'task_type': 'str',
        'task_user': 'str',
        'top_group_id': 'str',
        'update_time': 'int',
        'virus_task_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'action': 'Action',
        'create_time': 'CreateTime',
        'file_path': 'FilePath',
        'id_count': 'IDCount',
        'result_num': 'ResultNum',
        'sub_task_created': 'SubTaskCreated',
        'sub_task_failed': 'SubTaskFailed',
        'sub_task_running': 'SubTaskRunning',
        'sub_task_stopped': 'SubTaskStopped',
        'sub_task_succeed': 'SubTaskSucceed',
        'tag': 'Tag',
        'task_id': 'TaskID',
        'task_name': 'TaskName',
        'task_status': 'TaskStatus',
        'task_timeout': 'TaskTimeout',
        'task_type': 'TaskType',
        'task_user': 'TaskUser',
        'top_group_id': 'TopGroupID',
        'update_time': 'UpdateTime',
        'virus_task_id': 'VirusTaskID'
    }

    def __init__(self, account_id=None, action=None, create_time=None, file_path=None, id_count=None, result_num=None, sub_task_created=None, sub_task_failed=None, sub_task_running=None, sub_task_stopped=None, sub_task_succeed=None, tag=None, task_id=None, task_name=None, task_status=None, task_timeout=None, task_type=None, task_user=None, top_group_id=None, update_time=None, virus_task_id=None, _configuration=None):  # noqa: E501
        """DataForListScanTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._action = None
        self._create_time = None
        self._file_path = None
        self._id_count = None
        self._result_num = None
        self._sub_task_created = None
        self._sub_task_failed = None
        self._sub_task_running = None
        self._sub_task_stopped = None
        self._sub_task_succeed = None
        self._tag = None
        self._task_id = None
        self._task_name = None
        self._task_status = None
        self._task_timeout = None
        self._task_type = None
        self._task_user = None
        self._top_group_id = None
        self._update_time = None
        self._virus_task_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if action is not None:
            self.action = action
        if create_time is not None:
            self.create_time = create_time
        if file_path is not None:
            self.file_path = file_path
        if id_count is not None:
            self.id_count = id_count
        if result_num is not None:
            self.result_num = result_num
        if sub_task_created is not None:
            self.sub_task_created = sub_task_created
        if sub_task_failed is not None:
            self.sub_task_failed = sub_task_failed
        if sub_task_running is not None:
            self.sub_task_running = sub_task_running
        if sub_task_stopped is not None:
            self.sub_task_stopped = sub_task_stopped
        if sub_task_succeed is not None:
            self.sub_task_succeed = sub_task_succeed
        if tag is not None:
            self.tag = tag
        if task_id is not None:
            self.task_id = task_id
        if task_name is not None:
            self.task_name = task_name
        if task_status is not None:
            self.task_status = task_status
        if task_timeout is not None:
            self.task_timeout = task_timeout
        if task_type is not None:
            self.task_type = task_type
        if task_user is not None:
            self.task_user = task_user
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if update_time is not None:
            self.update_time = update_time
        if virus_task_id is not None:
            self.virus_task_id = virus_task_id

    @property
    def account_id(self):
        """Gets the account_id of this DataForListScanTasksOutput.  # noqa: E501


        :return: The account_id of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForListScanTasksOutput.


        :param account_id: The account_id of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def action(self):
        """Gets the action of this DataForListScanTasksOutput.  # noqa: E501


        :return: The action of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this DataForListScanTasksOutput.


        :param action: The action of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def create_time(self):
        """Gets the create_time of this DataForListScanTasksOutput.  # noqa: E501


        :return: The create_time of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForListScanTasksOutput.


        :param create_time: The create_time of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def file_path(self):
        """Gets the file_path of this DataForListScanTasksOutput.  # noqa: E501


        :return: The file_path of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this DataForListScanTasksOutput.


        :param file_path: The file_path of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def id_count(self):
        """Gets the id_count of this DataForListScanTasksOutput.  # noqa: E501


        :return: The id_count of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._id_count

    @id_count.setter
    def id_count(self, id_count):
        """Sets the id_count of this DataForListScanTasksOutput.


        :param id_count: The id_count of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._id_count = id_count

    @property
    def result_num(self):
        """Gets the result_num of this DataForListScanTasksOutput.  # noqa: E501


        :return: The result_num of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._result_num

    @result_num.setter
    def result_num(self, result_num):
        """Sets the result_num of this DataForListScanTasksOutput.


        :param result_num: The result_num of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._result_num = result_num

    @property
    def sub_task_created(self):
        """Gets the sub_task_created of this DataForListScanTasksOutput.  # noqa: E501


        :return: The sub_task_created of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_created

    @sub_task_created.setter
    def sub_task_created(self, sub_task_created):
        """Sets the sub_task_created of this DataForListScanTasksOutput.


        :param sub_task_created: The sub_task_created of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._sub_task_created = sub_task_created

    @property
    def sub_task_failed(self):
        """Gets the sub_task_failed of this DataForListScanTasksOutput.  # noqa: E501


        :return: The sub_task_failed of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_failed

    @sub_task_failed.setter
    def sub_task_failed(self, sub_task_failed):
        """Sets the sub_task_failed of this DataForListScanTasksOutput.


        :param sub_task_failed: The sub_task_failed of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._sub_task_failed = sub_task_failed

    @property
    def sub_task_running(self):
        """Gets the sub_task_running of this DataForListScanTasksOutput.  # noqa: E501


        :return: The sub_task_running of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_running

    @sub_task_running.setter
    def sub_task_running(self, sub_task_running):
        """Sets the sub_task_running of this DataForListScanTasksOutput.


        :param sub_task_running: The sub_task_running of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._sub_task_running = sub_task_running

    @property
    def sub_task_stopped(self):
        """Gets the sub_task_stopped of this DataForListScanTasksOutput.  # noqa: E501


        :return: The sub_task_stopped of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_stopped

    @sub_task_stopped.setter
    def sub_task_stopped(self, sub_task_stopped):
        """Sets the sub_task_stopped of this DataForListScanTasksOutput.


        :param sub_task_stopped: The sub_task_stopped of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._sub_task_stopped = sub_task_stopped

    @property
    def sub_task_succeed(self):
        """Gets the sub_task_succeed of this DataForListScanTasksOutput.  # noqa: E501


        :return: The sub_task_succeed of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._sub_task_succeed

    @sub_task_succeed.setter
    def sub_task_succeed(self, sub_task_succeed):
        """Sets the sub_task_succeed of this DataForListScanTasksOutput.


        :param sub_task_succeed: The sub_task_succeed of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._sub_task_succeed = sub_task_succeed

    @property
    def tag(self):
        """Gets the tag of this DataForListScanTasksOutput.  # noqa: E501


        :return: The tag of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this DataForListScanTasksOutput.


        :param tag: The tag of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def task_id(self):
        """Gets the task_id of this DataForListScanTasksOutput.  # noqa: E501


        :return: The task_id of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this DataForListScanTasksOutput.


        :param task_id: The task_id of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_id = task_id

    @property
    def task_name(self):
        """Gets the task_name of this DataForListScanTasksOutput.  # noqa: E501


        :return: The task_name of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_name

    @task_name.setter
    def task_name(self, task_name):
        """Sets the task_name of this DataForListScanTasksOutput.


        :param task_name: The task_name of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_name = task_name

    @property
    def task_status(self):
        """Gets the task_status of this DataForListScanTasksOutput.  # noqa: E501


        :return: The task_status of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_status

    @task_status.setter
    def task_status(self, task_status):
        """Sets the task_status of this DataForListScanTasksOutput.


        :param task_status: The task_status of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_status = task_status

    @property
    def task_timeout(self):
        """Gets the task_timeout of this DataForListScanTasksOutput.  # noqa: E501


        :return: The task_timeout of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._task_timeout

    @task_timeout.setter
    def task_timeout(self, task_timeout):
        """Sets the task_timeout of this DataForListScanTasksOutput.


        :param task_timeout: The task_timeout of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._task_timeout = task_timeout

    @property
    def task_type(self):
        """Gets the task_type of this DataForListScanTasksOutput.  # noqa: E501


        :return: The task_type of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_type

    @task_type.setter
    def task_type(self, task_type):
        """Sets the task_type of this DataForListScanTasksOutput.


        :param task_type: The task_type of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_type = task_type

    @property
    def task_user(self):
        """Gets the task_user of this DataForListScanTasksOutput.  # noqa: E501


        :return: The task_user of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_user

    @task_user.setter
    def task_user(self, task_user):
        """Sets the task_user of this DataForListScanTasksOutput.


        :param task_user: The task_user of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._task_user = task_user

    @property
    def top_group_id(self):
        """Gets the top_group_id of this DataForListScanTasksOutput.  # noqa: E501


        :return: The top_group_id of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this DataForListScanTasksOutput.


        :param top_group_id: The top_group_id of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def update_time(self):
        """Gets the update_time of this DataForListScanTasksOutput.  # noqa: E501


        :return: The update_time of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForListScanTasksOutput.


        :param update_time: The update_time of this DataForListScanTasksOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def virus_task_id(self):
        """Gets the virus_task_id of this DataForListScanTasksOutput.  # noqa: E501


        :return: The virus_task_id of this DataForListScanTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._virus_task_id

    @virus_task_id.setter
    def virus_task_id(self, virus_task_id):
        """Sets the virus_task_id of this DataForListScanTasksOutput.


        :param virus_task_id: The virus_task_id of this DataForListScanTasksOutput.  # noqa: E501
        :type: str
        """

        self._virus_task_id = virus_task_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListScanTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListScanTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListScanTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
