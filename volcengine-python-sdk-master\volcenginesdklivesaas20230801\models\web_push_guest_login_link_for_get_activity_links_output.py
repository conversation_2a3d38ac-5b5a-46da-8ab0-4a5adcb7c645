# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class WebPushGuestLoginLinkForGetActivityLinksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enable_key': 'int',
        'link_key': 'str',
        'login_url': 'str'
    }

    attribute_map = {
        'enable_key': 'EnableKey',
        'link_key': 'LinkKey',
        'login_url': 'LoginUrl'
    }

    def __init__(self, enable_key=None, link_key=None, login_url=None, _configuration=None):  # noqa: E501
        """WebPushGuestLoginLinkForGetActivityLinksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enable_key = None
        self._link_key = None
        self._login_url = None
        self.discriminator = None

        if enable_key is not None:
            self.enable_key = enable_key
        if link_key is not None:
            self.link_key = link_key
        if login_url is not None:
            self.login_url = login_url

    @property
    def enable_key(self):
        """Gets the enable_key of this WebPushGuestLoginLinkForGetActivityLinksOutput.  # noqa: E501


        :return: The enable_key of this WebPushGuestLoginLinkForGetActivityLinksOutput.  # noqa: E501
        :rtype: int
        """
        return self._enable_key

    @enable_key.setter
    def enable_key(self, enable_key):
        """Sets the enable_key of this WebPushGuestLoginLinkForGetActivityLinksOutput.


        :param enable_key: The enable_key of this WebPushGuestLoginLinkForGetActivityLinksOutput.  # noqa: E501
        :type: int
        """

        self._enable_key = enable_key

    @property
    def link_key(self):
        """Gets the link_key of this WebPushGuestLoginLinkForGetActivityLinksOutput.  # noqa: E501


        :return: The link_key of this WebPushGuestLoginLinkForGetActivityLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._link_key

    @link_key.setter
    def link_key(self, link_key):
        """Sets the link_key of this WebPushGuestLoginLinkForGetActivityLinksOutput.


        :param link_key: The link_key of this WebPushGuestLoginLinkForGetActivityLinksOutput.  # noqa: E501
        :type: str
        """

        self._link_key = link_key

    @property
    def login_url(self):
        """Gets the login_url of this WebPushGuestLoginLinkForGetActivityLinksOutput.  # noqa: E501


        :return: The login_url of this WebPushGuestLoginLinkForGetActivityLinksOutput.  # noqa: E501
        :rtype: str
        """
        return self._login_url

    @login_url.setter
    def login_url(self, login_url):
        """Sets the login_url of this WebPushGuestLoginLinkForGetActivityLinksOutput.


        :param login_url: The login_url of this WebPushGuestLoginLinkForGetActivityLinksOutput.  # noqa: E501
        :type: str
        """

        self._login_url = login_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(WebPushGuestLoginLinkForGetActivityLinksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, WebPushGuestLoginLinkForGetActivityLinksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, WebPushGuestLoginLinkForGetActivityLinksOutput):
            return True

        return self.to_dict() != other.to_dict()
