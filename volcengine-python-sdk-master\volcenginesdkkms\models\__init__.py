# coding: utf-8

# flake8: noqa
"""
    kms

    No description provided (generated by <PERSON>wagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkkms.models.archive_key_request import ArchiveKeyRequest
from volcenginesdkkms.models.archive_key_response import ArchiveKeyResponse
from volcenginesdkkms.models.asymmetric_decrypt_request import AsymmetricDecryptRequest
from volcenginesdkkms.models.asymmetric_decrypt_response import AsymmetricDecryptResponse
from volcenginesdkkms.models.asymmetric_encrypt_request import AsymmetricEncryptRequest
from volcenginesdkkms.models.asymmetric_encrypt_response import AsymmetricEncryptResponse
from volcenginesdkkms.models.asymmetric_sign_request import AsymmetricSignRequest
from volcenginesdkkms.models.asymmetric_sign_response import AsymmetricSignResponse
from volcenginesdkkms.models.asymmetric_verify_request import AsymmetricVerifyRequest
from volcenginesdkkms.models.asymmetric_verify_response import AsymmetricVerifyResponse
from volcenginesdkkms.models.backup_secret_request import BackupSecretRequest
from volcenginesdkkms.models.backup_secret_response import BackupSecretResponse
from volcenginesdkkms.models.batch_get_secret_value_request import BatchGetSecretValueRequest
from volcenginesdkkms.models.batch_get_secret_value_response import BatchGetSecretValueResponse
from volcenginesdkkms.models.cancel_archive_key_request import CancelArchiveKeyRequest
from volcenginesdkkms.models.cancel_archive_key_response import CancelArchiveKeyResponse
from volcenginesdkkms.models.cancel_key_deletion_request import CancelKeyDeletionRequest
from volcenginesdkkms.models.cancel_key_deletion_response import CancelKeyDeletionResponse
from volcenginesdkkms.models.cancel_secret_deletion_request import CancelSecretDeletionRequest
from volcenginesdkkms.models.cancel_secret_deletion_response import CancelSecretDeletionResponse
from volcenginesdkkms.models.convert_replica_key_for_replicate_key_output import ConvertReplicaKeyForReplicateKeyOutput
from volcenginesdkkms.models.create_key_request import CreateKeyRequest
from volcenginesdkkms.models.create_key_response import CreateKeyResponse
from volcenginesdkkms.models.create_keyring_request import CreateKeyringRequest
from volcenginesdkkms.models.create_keyring_response import CreateKeyringResponse
from volcenginesdkkms.models.create_secret_request import CreateSecretRequest
from volcenginesdkkms.models.create_secret_response import CreateSecretResponse
from volcenginesdkkms.models.decrypt_request import DecryptRequest
from volcenginesdkkms.models.decrypt_response import DecryptResponse
from volcenginesdkkms.models.delete_key_material_request import DeleteKeyMaterialRequest
from volcenginesdkkms.models.delete_key_material_response import DeleteKeyMaterialResponse
from volcenginesdkkms.models.delete_keyring_request import DeleteKeyringRequest
from volcenginesdkkms.models.delete_keyring_response import DeleteKeyringResponse
from volcenginesdkkms.models.describe_key_request import DescribeKeyRequest
from volcenginesdkkms.models.describe_key_response import DescribeKeyResponse
from volcenginesdkkms.models.describe_keyrings_request import DescribeKeyringsRequest
from volcenginesdkkms.models.describe_keyrings_response import DescribeKeyringsResponse
from volcenginesdkkms.models.describe_keys_request import DescribeKeysRequest
from volcenginesdkkms.models.describe_keys_response import DescribeKeysResponse
from volcenginesdkkms.models.describe_regions_request import DescribeRegionsRequest
from volcenginesdkkms.models.describe_regions_response import DescribeRegionsResponse
from volcenginesdkkms.models.describe_secret_request import DescribeSecretRequest
from volcenginesdkkms.models.describe_secret_response import DescribeSecretResponse
from volcenginesdkkms.models.describe_secret_versions_request import DescribeSecretVersionsRequest
from volcenginesdkkms.models.describe_secret_versions_response import DescribeSecretVersionsResponse
from volcenginesdkkms.models.describe_secrets_request import DescribeSecretsRequest
from volcenginesdkkms.models.describe_secrets_response import DescribeSecretsResponse
from volcenginesdkkms.models.disable_key_request import DisableKeyRequest
from volcenginesdkkms.models.disable_key_response import DisableKeyResponse
from volcenginesdkkms.models.disable_key_rotation_request import DisableKeyRotationRequest
from volcenginesdkkms.models.disable_key_rotation_response import DisableKeyRotationResponse
from volcenginesdkkms.models.enable_key_request import EnableKeyRequest
from volcenginesdkkms.models.enable_key_response import EnableKeyResponse
from volcenginesdkkms.models.enable_key_rotation_request import EnableKeyRotationRequest
from volcenginesdkkms.models.enable_key_rotation_response import EnableKeyRotationResponse
from volcenginesdkkms.models.encrypt_request import EncryptRequest
from volcenginesdkkms.models.encrypt_response import EncryptResponse
from volcenginesdkkms.models.generate_data_key_request import GenerateDataKeyRequest
from volcenginesdkkms.models.generate_data_key_response import GenerateDataKeyResponse
from volcenginesdkkms.models.generate_mac_request import GenerateMacRequest
from volcenginesdkkms.models.generate_mac_response import GenerateMacResponse
from volcenginesdkkms.models.get_parameters_for_import_request import GetParametersForImportRequest
from volcenginesdkkms.models.get_parameters_for_import_response import GetParametersForImportResponse
from volcenginesdkkms.models.get_public_key_request import GetPublicKeyRequest
from volcenginesdkkms.models.get_public_key_response import GetPublicKeyResponse
from volcenginesdkkms.models.get_secret_value_request import GetSecretValueRequest
from volcenginesdkkms.models.get_secret_value_response import GetSecretValueResponse
from volcenginesdkkms.models.import_key_material_request import ImportKeyMaterialRequest
from volcenginesdkkms.models.import_key_material_response import ImportKeyMaterialResponse
from volcenginesdkkms.models.key_for_create_key_output import KeyForCreateKeyOutput
from volcenginesdkkms.models.key_for_describe_key_output import KeyForDescribeKeyOutput
from volcenginesdkkms.models.key_for_describe_keys_output import KeyForDescribeKeysOutput
from volcenginesdkkms.models.keyring_for_create_keyring_output import KeyringForCreateKeyringOutput
from volcenginesdkkms.models.keyring_for_describe_keyrings_output import KeyringForDescribeKeyringsOutput
from volcenginesdkkms.models.keyring_for_query_keyring_output import KeyringForQueryKeyringOutput
from volcenginesdkkms.models.list_tags_for_resources_request import ListTagsForResourcesRequest
from volcenginesdkkms.models.list_tags_for_resources_response import ListTagsForResourcesResponse
from volcenginesdkkms.models.multi_region_configuration_for_create_key_output import MultiRegionConfigurationForCreateKeyOutput
from volcenginesdkkms.models.multi_region_configuration_for_describe_key_output import MultiRegionConfigurationForDescribeKeyOutput
from volcenginesdkkms.models.multi_region_configuration_for_describe_keys_output import MultiRegionConfigurationForDescribeKeysOutput
from volcenginesdkkms.models.multi_region_configuration_for_replicate_key_output import MultiRegionConfigurationForReplicateKeyOutput
from volcenginesdkkms.models.page_info_for_describe_keyrings_output import PageInfoForDescribeKeyringsOutput
from volcenginesdkkms.models.page_info_for_describe_keys_output import PageInfoForDescribeKeysOutput
from volcenginesdkkms.models.page_info_for_describe_secret_versions_output import PageInfoForDescribeSecretVersionsOutput
from volcenginesdkkms.models.page_info_for_describe_secrets_output import PageInfoForDescribeSecretsOutput
from volcenginesdkkms.models.primary_key_for_create_key_output import PrimaryKeyForCreateKeyOutput
from volcenginesdkkms.models.primary_key_for_describe_key_output import PrimaryKeyForDescribeKeyOutput
from volcenginesdkkms.models.primary_key_for_describe_keys_output import PrimaryKeyForDescribeKeysOutput
from volcenginesdkkms.models.primary_key_for_replicate_key_output import PrimaryKeyForReplicateKeyOutput
from volcenginesdkkms.models.query_keyring_request import QueryKeyringRequest
from volcenginesdkkms.models.query_keyring_response import QueryKeyringResponse
from volcenginesdkkms.models.re_encrypt_request import ReEncryptRequest
from volcenginesdkkms.models.re_encrypt_response import ReEncryptResponse
from volcenginesdkkms.models.region_for_describe_regions_output import RegionForDescribeRegionsOutput
from volcenginesdkkms.models.replica_key_for_create_key_output import ReplicaKeyForCreateKeyOutput
from volcenginesdkkms.models.replica_key_for_describe_key_output import ReplicaKeyForDescribeKeyOutput
from volcenginesdkkms.models.replica_key_for_describe_keys_output import ReplicaKeyForDescribeKeysOutput
from volcenginesdkkms.models.replica_key_for_replicate_key_output import ReplicaKeyForReplicateKeyOutput
from volcenginesdkkms.models.replicate_key_request import ReplicateKeyRequest
from volcenginesdkkms.models.replicate_key_response import ReplicateKeyResponse
from volcenginesdkkms.models.resource_tag_for_list_tags_for_resources_output import ResourceTagForListTagsForResourcesOutput
from volcenginesdkkms.models.restore_secret_request import RestoreSecretRequest
from volcenginesdkkms.models.restore_secret_response import RestoreSecretResponse
from volcenginesdkkms.models.rotate_secret_request import RotateSecretRequest
from volcenginesdkkms.models.rotate_secret_response import RotateSecretResponse
from volcenginesdkkms.models.schedule_key_deletion_request import ScheduleKeyDeletionRequest
from volcenginesdkkms.models.schedule_key_deletion_response import ScheduleKeyDeletionResponse
from volcenginesdkkms.models.schedule_secret_deletion_request import ScheduleSecretDeletionRequest
from volcenginesdkkms.models.schedule_secret_deletion_response import ScheduleSecretDeletionResponse
from volcenginesdkkms.models.secret_for_create_secret_output import SecretForCreateSecretOutput
from volcenginesdkkms.models.secret_for_describe_secret_output import SecretForDescribeSecretOutput
from volcenginesdkkms.models.secret_for_describe_secrets_output import SecretForDescribeSecretsOutput
from volcenginesdkkms.models.secret_value_for_batch_get_secret_value_output import SecretValueForBatchGetSecretValueOutput
from volcenginesdkkms.models.secret_version_for_describe_secret_versions_output import SecretVersionForDescribeSecretVersionsOutput
from volcenginesdkkms.models.set_secret_value_request import SetSecretValueRequest
from volcenginesdkkms.models.set_secret_value_response import SetSecretValueResponse
from volcenginesdkkms.models.tag_filter_for_describe_keys_input import TagFilterForDescribeKeysInput
from volcenginesdkkms.models.tag_filter_for_list_tags_for_resources_input import TagFilterForListTagsForResourcesInput
from volcenginesdkkms.models.tag_for_create_key_input import TagForCreateKeyInput
from volcenginesdkkms.models.tag_for_create_key_output import TagForCreateKeyOutput
from volcenginesdkkms.models.tag_for_describe_key_output import TagForDescribeKeyOutput
from volcenginesdkkms.models.tag_for_describe_keys_output import TagForDescribeKeysOutput
from volcenginesdkkms.models.tag_for_replicate_key_input import TagForReplicateKeyInput
from volcenginesdkkms.models.tag_for_replicate_key_output import TagForReplicateKeyOutput
from volcenginesdkkms.models.tag_for_tag_resources_input import TagForTagResourcesInput
from volcenginesdkkms.models.tag_resources_request import TagResourcesRequest
from volcenginesdkkms.models.tag_resources_response import TagResourcesResponse
from volcenginesdkkms.models.untag_resources_request import UntagResourcesRequest
from volcenginesdkkms.models.untag_resources_response import UntagResourcesResponse
from volcenginesdkkms.models.update_key_request import UpdateKeyRequest
from volcenginesdkkms.models.update_key_response import UpdateKeyResponse
from volcenginesdkkms.models.update_keyring_request import UpdateKeyringRequest
from volcenginesdkkms.models.update_keyring_response import UpdateKeyringResponse
from volcenginesdkkms.models.update_primary_region_request import UpdatePrimaryRegionRequest
from volcenginesdkkms.models.update_primary_region_response import UpdatePrimaryRegionResponse
from volcenginesdkkms.models.update_secret_request import UpdateSecretRequest
from volcenginesdkkms.models.update_secret_response import UpdateSecretResponse
from volcenginesdkkms.models.update_secret_rotation_policy_request import UpdateSecretRotationPolicyRequest
from volcenginesdkkms.models.update_secret_rotation_policy_response import UpdateSecretRotationPolicyResponse
from volcenginesdkkms.models.verify_mac_request import VerifyMacRequest
from volcenginesdkkms.models.verify_mac_response import VerifyMacResponse
