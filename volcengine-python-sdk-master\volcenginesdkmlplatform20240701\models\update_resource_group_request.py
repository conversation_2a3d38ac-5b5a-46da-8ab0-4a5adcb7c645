# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateResourceGroupRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'resource_config': 'ResourceConfigForUpdateResourceGroupInput',
        'storage_config': 'StorageConfigForUpdateResourceGroupInput',
        'workload_network_config': 'WorkloadNetworkConfigForUpdateResourceGroupInput',
        'workload_network_mode': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'id': 'Id',
        'name': 'Name',
        'resource_config': 'ResourceConfig',
        'storage_config': 'StorageConfig',
        'workload_network_config': 'WorkloadNetworkConfig',
        'workload_network_mode': 'WorkloadNetworkMode'
    }

    def __init__(self, description=None, id=None, name=None, resource_config=None, storage_config=None, workload_network_config=None, workload_network_mode=None, _configuration=None):  # noqa: E501
        """UpdateResourceGroupRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._id = None
        self._name = None
        self._resource_config = None
        self._storage_config = None
        self._workload_network_config = None
        self._workload_network_mode = None
        self.discriminator = None

        if description is not None:
            self.description = description
        self.id = id
        if name is not None:
            self.name = name
        if resource_config is not None:
            self.resource_config = resource_config
        if storage_config is not None:
            self.storage_config = storage_config
        if workload_network_config is not None:
            self.workload_network_config = workload_network_config
        if workload_network_mode is not None:
            self.workload_network_mode = workload_network_mode

    @property
    def description(self):
        """Gets the description of this UpdateResourceGroupRequest.  # noqa: E501


        :return: The description of this UpdateResourceGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateResourceGroupRequest.


        :param description: The description of this UpdateResourceGroupRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 500):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `500`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def id(self):
        """Gets the id of this UpdateResourceGroupRequest.  # noqa: E501


        :return: The id of this UpdateResourceGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateResourceGroupRequest.


        :param id: The id of this UpdateResourceGroupRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this UpdateResourceGroupRequest.  # noqa: E501


        :return: The name of this UpdateResourceGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateResourceGroupRequest.


        :param name: The name of this UpdateResourceGroupRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                name is not None and len(name) > 200):
            raise ValueError("Invalid value for `name`, length must be less than or equal to `200`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) < 1):
            raise ValueError("Invalid value for `name`, length must be greater than or equal to `1`")  # noqa: E501

        self._name = name

    @property
    def resource_config(self):
        """Gets the resource_config of this UpdateResourceGroupRequest.  # noqa: E501


        :return: The resource_config of this UpdateResourceGroupRequest.  # noqa: E501
        :rtype: ResourceConfigForUpdateResourceGroupInput
        """
        return self._resource_config

    @resource_config.setter
    def resource_config(self, resource_config):
        """Sets the resource_config of this UpdateResourceGroupRequest.


        :param resource_config: The resource_config of this UpdateResourceGroupRequest.  # noqa: E501
        :type: ResourceConfigForUpdateResourceGroupInput
        """

        self._resource_config = resource_config

    @property
    def storage_config(self):
        """Gets the storage_config of this UpdateResourceGroupRequest.  # noqa: E501


        :return: The storage_config of this UpdateResourceGroupRequest.  # noqa: E501
        :rtype: StorageConfigForUpdateResourceGroupInput
        """
        return self._storage_config

    @storage_config.setter
    def storage_config(self, storage_config):
        """Sets the storage_config of this UpdateResourceGroupRequest.


        :param storage_config: The storage_config of this UpdateResourceGroupRequest.  # noqa: E501
        :type: StorageConfigForUpdateResourceGroupInput
        """

        self._storage_config = storage_config

    @property
    def workload_network_config(self):
        """Gets the workload_network_config of this UpdateResourceGroupRequest.  # noqa: E501


        :return: The workload_network_config of this UpdateResourceGroupRequest.  # noqa: E501
        :rtype: WorkloadNetworkConfigForUpdateResourceGroupInput
        """
        return self._workload_network_config

    @workload_network_config.setter
    def workload_network_config(self, workload_network_config):
        """Sets the workload_network_config of this UpdateResourceGroupRequest.


        :param workload_network_config: The workload_network_config of this UpdateResourceGroupRequest.  # noqa: E501
        :type: WorkloadNetworkConfigForUpdateResourceGroupInput
        """

        self._workload_network_config = workload_network_config

    @property
    def workload_network_mode(self):
        """Gets the workload_network_mode of this UpdateResourceGroupRequest.  # noqa: E501


        :return: The workload_network_mode of this UpdateResourceGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._workload_network_mode

    @workload_network_mode.setter
    def workload_network_mode(self, workload_network_mode):
        """Sets the workload_network_mode of this UpdateResourceGroupRequest.


        :param workload_network_mode: The workload_network_mode of this UpdateResourceGroupRequest.  # noqa: E501
        :type: str
        """

        self._workload_network_mode = workload_network_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateResourceGroupRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateResourceGroupRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateResourceGroupRequest):
            return True

        return self.to_dict() != other.to_dict()
