# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DiagnoseDetailForGetTaskResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dns_detail': 'str',
        'mtr_detail': 'str',
        'ping_detail': 'str'
    }

    attribute_map = {
        'dns_detail': 'DNSDetail',
        'mtr_detail': 'MtrDetail',
        'ping_detail': 'PingDetail'
    }

    def __init__(self, dns_detail=None, mtr_detail=None, ping_detail=None, _configuration=None):  # noqa: E501
        """DiagnoseDetailForGetTaskResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dns_detail = None
        self._mtr_detail = None
        self._ping_detail = None
        self.discriminator = None

        if dns_detail is not None:
            self.dns_detail = dns_detail
        if mtr_detail is not None:
            self.mtr_detail = mtr_detail
        if ping_detail is not None:
            self.ping_detail = ping_detail

    @property
    def dns_detail(self):
        """Gets the dns_detail of this DiagnoseDetailForGetTaskResultOutput.  # noqa: E501


        :return: The dns_detail of this DiagnoseDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._dns_detail

    @dns_detail.setter
    def dns_detail(self, dns_detail):
        """Sets the dns_detail of this DiagnoseDetailForGetTaskResultOutput.


        :param dns_detail: The dns_detail of this DiagnoseDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._dns_detail = dns_detail

    @property
    def mtr_detail(self):
        """Gets the mtr_detail of this DiagnoseDetailForGetTaskResultOutput.  # noqa: E501


        :return: The mtr_detail of this DiagnoseDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._mtr_detail

    @mtr_detail.setter
    def mtr_detail(self, mtr_detail):
        """Sets the mtr_detail of this DiagnoseDetailForGetTaskResultOutput.


        :param mtr_detail: The mtr_detail of this DiagnoseDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._mtr_detail = mtr_detail

    @property
    def ping_detail(self):
        """Gets the ping_detail of this DiagnoseDetailForGetTaskResultOutput.  # noqa: E501


        :return: The ping_detail of this DiagnoseDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._ping_detail

    @ping_detail.setter
    def ping_detail(self, ping_detail):
        """Sets the ping_detail of this DiagnoseDetailForGetTaskResultOutput.


        :param ping_detail: The ping_detail of this DiagnoseDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._ping_detail = ping_detail

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DiagnoseDetailForGetTaskResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DiagnoseDetailForGetTaskResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DiagnoseDetailForGetTaskResultOutput):
            return True

        return self.to_dict() != other.to_dict()
