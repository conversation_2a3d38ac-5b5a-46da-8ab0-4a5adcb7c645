# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GBMediaForGetLocalDownloadOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'call_id': 'str',
        'end_time': 'int',
        'file_name': 'str',
        'file_size': 'int',
        'file_stream_length': 'int',
        'id': 'str',
        'msg': 'str',
        'start_time': 'int',
        'status': 'str',
        'subtitle_url': 'str',
        'url': 'str'
    }

    attribute_map = {
        'call_id': 'CallID',
        'end_time': 'EndTime',
        'file_name': 'FileName',
        'file_size': 'FileSize',
        'file_stream_length': 'FileStreamLength',
        'id': 'ID',
        'msg': 'Msg',
        'start_time': 'StartTime',
        'status': 'Status',
        'subtitle_url': 'SubtitleUrl',
        'url': 'Url'
    }

    def __init__(self, call_id=None, end_time=None, file_name=None, file_size=None, file_stream_length=None, id=None, msg=None, start_time=None, status=None, subtitle_url=None, url=None, _configuration=None):  # noqa: E501
        """GBMediaForGetLocalDownloadOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._call_id = None
        self._end_time = None
        self._file_name = None
        self._file_size = None
        self._file_stream_length = None
        self._id = None
        self._msg = None
        self._start_time = None
        self._status = None
        self._subtitle_url = None
        self._url = None
        self.discriminator = None

        if call_id is not None:
            self.call_id = call_id
        if end_time is not None:
            self.end_time = end_time
        if file_name is not None:
            self.file_name = file_name
        if file_size is not None:
            self.file_size = file_size
        if file_stream_length is not None:
            self.file_stream_length = file_stream_length
        if id is not None:
            self.id = id
        if msg is not None:
            self.msg = msg
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if subtitle_url is not None:
            self.subtitle_url = subtitle_url
        if url is not None:
            self.url = url

    @property
    def call_id(self):
        """Gets the call_id of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The call_id of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._call_id

    @call_id.setter
    def call_id(self, call_id):
        """Sets the call_id of this GBMediaForGetLocalDownloadOutput.


        :param call_id: The call_id of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: str
        """

        self._call_id = call_id

    @property
    def end_time(self):
        """Gets the end_time of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The end_time of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this GBMediaForGetLocalDownloadOutput.


        :param end_time: The end_time of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def file_name(self):
        """Gets the file_name of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The file_name of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_name

    @file_name.setter
    def file_name(self, file_name):
        """Sets the file_name of this GBMediaForGetLocalDownloadOutput.


        :param file_name: The file_name of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: str
        """

        self._file_name = file_name

    @property
    def file_size(self):
        """Gets the file_size of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The file_size of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: int
        """
        return self._file_size

    @file_size.setter
    def file_size(self, file_size):
        """Sets the file_size of this GBMediaForGetLocalDownloadOutput.


        :param file_size: The file_size of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: int
        """

        self._file_size = file_size

    @property
    def file_stream_length(self):
        """Gets the file_stream_length of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The file_stream_length of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: int
        """
        return self._file_stream_length

    @file_stream_length.setter
    def file_stream_length(self, file_stream_length):
        """Sets the file_stream_length of this GBMediaForGetLocalDownloadOutput.


        :param file_stream_length: The file_stream_length of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: int
        """

        self._file_stream_length = file_stream_length

    @property
    def id(self):
        """Gets the id of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The id of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GBMediaForGetLocalDownloadOutput.


        :param id: The id of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def msg(self):
        """Gets the msg of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The msg of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._msg

    @msg.setter
    def msg(self, msg):
        """Sets the msg of this GBMediaForGetLocalDownloadOutput.


        :param msg: The msg of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: str
        """

        self._msg = msg

    @property
    def start_time(self):
        """Gets the start_time of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The start_time of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this GBMediaForGetLocalDownloadOutput.


        :param start_time: The start_time of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The status of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GBMediaForGetLocalDownloadOutput.


        :param status: The status of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def subtitle_url(self):
        """Gets the subtitle_url of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The subtitle_url of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._subtitle_url

    @subtitle_url.setter
    def subtitle_url(self, subtitle_url):
        """Sets the subtitle_url of this GBMediaForGetLocalDownloadOutput.


        :param subtitle_url: The subtitle_url of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: str
        """

        self._subtitle_url = subtitle_url

    @property
    def url(self):
        """Gets the url of this GBMediaForGetLocalDownloadOutput.  # noqa: E501


        :return: The url of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this GBMediaForGetLocalDownloadOutput.


        :param url: The url of this GBMediaForGetLocalDownloadOutput.  # noqa: E501
        :type: str
        """

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GBMediaForGetLocalDownloadOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GBMediaForGetLocalDownloadOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GBMediaForGetLocalDownloadOutput):
            return True

        return self.to_dict() != other.to_dict()
