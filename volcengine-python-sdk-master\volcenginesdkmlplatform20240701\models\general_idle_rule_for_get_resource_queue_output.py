# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GeneralIdleRuleForGetResourceQueueOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'creator_trn': 'str',
        'enabled': 'bool',
        'id': 'str',
        'idle_shutdown_rule_types': 'list[str]',
        'minute_period': 'int',
        'period': 'int',
        'threshold': 'float',
        'triggered_times': 'int',
        'update_time': 'str',
        'updator_trn': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'creator_trn': 'CreatorTrn',
        'enabled': 'Enabled',
        'id': 'Id',
        'idle_shutdown_rule_types': 'IdleShutdownRuleTypes',
        'minute_period': 'MinutePeriod',
        'period': 'Period',
        'threshold': 'Threshold',
        'triggered_times': 'TriggeredTimes',
        'update_time': 'UpdateTime',
        'updator_trn': 'UpdatorTrn'
    }

    def __init__(self, create_time=None, creator_trn=None, enabled=None, id=None, idle_shutdown_rule_types=None, minute_period=None, period=None, threshold=None, triggered_times=None, update_time=None, updator_trn=None, _configuration=None):  # noqa: E501
        """GeneralIdleRuleForGetResourceQueueOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._creator_trn = None
        self._enabled = None
        self._id = None
        self._idle_shutdown_rule_types = None
        self._minute_period = None
        self._period = None
        self._threshold = None
        self._triggered_times = None
        self._update_time = None
        self._updator_trn = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if creator_trn is not None:
            self.creator_trn = creator_trn
        if enabled is not None:
            self.enabled = enabled
        if id is not None:
            self.id = id
        if idle_shutdown_rule_types is not None:
            self.idle_shutdown_rule_types = idle_shutdown_rule_types
        if minute_period is not None:
            self.minute_period = minute_period
        if period is not None:
            self.period = period
        if threshold is not None:
            self.threshold = threshold
        if triggered_times is not None:
            self.triggered_times = triggered_times
        if update_time is not None:
            self.update_time = update_time
        if updator_trn is not None:
            self.updator_trn = updator_trn

    @property
    def create_time(self):
        """Gets the create_time of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The create_time of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this GeneralIdleRuleForGetResourceQueueOutput.


        :param create_time: The create_time of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def creator_trn(self):
        """Gets the creator_trn of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The creator_trn of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: str
        """
        return self._creator_trn

    @creator_trn.setter
    def creator_trn(self, creator_trn):
        """Sets the creator_trn of this GeneralIdleRuleForGetResourceQueueOutput.


        :param creator_trn: The creator_trn of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: str
        """

        self._creator_trn = creator_trn

    @property
    def enabled(self):
        """Gets the enabled of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The enabled of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this GeneralIdleRuleForGetResourceQueueOutput.


        :param enabled: The enabled of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def id(self):
        """Gets the id of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The id of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GeneralIdleRuleForGetResourceQueueOutput.


        :param id: The id of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def idle_shutdown_rule_types(self):
        """Gets the idle_shutdown_rule_types of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The idle_shutdown_rule_types of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._idle_shutdown_rule_types

    @idle_shutdown_rule_types.setter
    def idle_shutdown_rule_types(self, idle_shutdown_rule_types):
        """Sets the idle_shutdown_rule_types of this GeneralIdleRuleForGetResourceQueueOutput.


        :param idle_shutdown_rule_types: The idle_shutdown_rule_types of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: list[str]
        """

        self._idle_shutdown_rule_types = idle_shutdown_rule_types

    @property
    def minute_period(self):
        """Gets the minute_period of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The minute_period of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: int
        """
        return self._minute_period

    @minute_period.setter
    def minute_period(self, minute_period):
        """Sets the minute_period of this GeneralIdleRuleForGetResourceQueueOutput.


        :param minute_period: The minute_period of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: int
        """

        self._minute_period = minute_period

    @property
    def period(self):
        """Gets the period of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The period of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this GeneralIdleRuleForGetResourceQueueOutput.


        :param period: The period of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def threshold(self):
        """Gets the threshold of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The threshold of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: float
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this GeneralIdleRuleForGetResourceQueueOutput.


        :param threshold: The threshold of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: float
        """

        self._threshold = threshold

    @property
    def triggered_times(self):
        """Gets the triggered_times of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The triggered_times of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: int
        """
        return self._triggered_times

    @triggered_times.setter
    def triggered_times(self, triggered_times):
        """Sets the triggered_times of this GeneralIdleRuleForGetResourceQueueOutput.


        :param triggered_times: The triggered_times of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: int
        """

        self._triggered_times = triggered_times

    @property
    def update_time(self):
        """Gets the update_time of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The update_time of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GeneralIdleRuleForGetResourceQueueOutput.


        :param update_time: The update_time of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def updator_trn(self):
        """Gets the updator_trn of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501


        :return: The updator_trn of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :rtype: str
        """
        return self._updator_trn

    @updator_trn.setter
    def updator_trn(self, updator_trn):
        """Sets the updator_trn of this GeneralIdleRuleForGetResourceQueueOutput.


        :param updator_trn: The updator_trn of this GeneralIdleRuleForGetResourceQueueOutput.  # noqa: E501
        :type: str
        """

        self._updator_trn = updator_trn

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GeneralIdleRuleForGetResourceQueueOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GeneralIdleRuleForGetResourceQueueOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GeneralIdleRuleForGetResourceQueueOutput):
            return True

        return self.to_dict() != other.to_dict()
