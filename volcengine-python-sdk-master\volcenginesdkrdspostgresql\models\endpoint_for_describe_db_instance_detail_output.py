# coding: utf-8

"""
    rds_postgresql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EndpointForDescribeDBInstanceDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address': 'list[AddressForDescribeDBInstanceDetailOutput]',
        'auto_add_new_nodes': 'str',
        'description': 'str',
        'enable_read_only': 'str',
        'enable_read_write_splitting': 'str',
        'endpoint_id': 'str',
        'endpoint_name': 'str',
        'endpoint_type': 'str',
        'read_only_node_distribution_type': 'str',
        'read_only_node_max_delay_time': 'int',
        'read_only_node_weight': 'list[ReadOnlyNodeWeightForDescribeDBInstanceDetailOutput]',
        'read_write_mode': 'str'
    }

    attribute_map = {
        'address': 'Address',
        'auto_add_new_nodes': 'AutoAddNewNodes',
        'description': 'Description',
        'enable_read_only': 'EnableReadOnly',
        'enable_read_write_splitting': 'EnableReadWriteSplitting',
        'endpoint_id': 'EndpointId',
        'endpoint_name': 'EndpointName',
        'endpoint_type': 'EndpointType',
        'read_only_node_distribution_type': 'ReadOnlyNodeDistributionType',
        'read_only_node_max_delay_time': 'ReadOnlyNodeMaxDelayTime',
        'read_only_node_weight': 'ReadOnlyNodeWeight',
        'read_write_mode': 'ReadWriteMode'
    }

    def __init__(self, address=None, auto_add_new_nodes=None, description=None, enable_read_only=None, enable_read_write_splitting=None, endpoint_id=None, endpoint_name=None, endpoint_type=None, read_only_node_distribution_type=None, read_only_node_max_delay_time=None, read_only_node_weight=None, read_write_mode=None, _configuration=None):  # noqa: E501
        """EndpointForDescribeDBInstanceDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address = None
        self._auto_add_new_nodes = None
        self._description = None
        self._enable_read_only = None
        self._enable_read_write_splitting = None
        self._endpoint_id = None
        self._endpoint_name = None
        self._endpoint_type = None
        self._read_only_node_distribution_type = None
        self._read_only_node_max_delay_time = None
        self._read_only_node_weight = None
        self._read_write_mode = None
        self.discriminator = None

        if address is not None:
            self.address = address
        if auto_add_new_nodes is not None:
            self.auto_add_new_nodes = auto_add_new_nodes
        if description is not None:
            self.description = description
        if enable_read_only is not None:
            self.enable_read_only = enable_read_only
        if enable_read_write_splitting is not None:
            self.enable_read_write_splitting = enable_read_write_splitting
        if endpoint_id is not None:
            self.endpoint_id = endpoint_id
        if endpoint_name is not None:
            self.endpoint_name = endpoint_name
        if endpoint_type is not None:
            self.endpoint_type = endpoint_type
        if read_only_node_distribution_type is not None:
            self.read_only_node_distribution_type = read_only_node_distribution_type
        if read_only_node_max_delay_time is not None:
            self.read_only_node_max_delay_time = read_only_node_max_delay_time
        if read_only_node_weight is not None:
            self.read_only_node_weight = read_only_node_weight
        if read_write_mode is not None:
            self.read_write_mode = read_write_mode

    @property
    def address(self):
        """Gets the address of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The address of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[AddressForDescribeDBInstanceDetailOutput]
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this EndpointForDescribeDBInstanceDetailOutput.


        :param address: The address of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[AddressForDescribeDBInstanceDetailOutput]
        """

        self._address = address

    @property
    def auto_add_new_nodes(self):
        """Gets the auto_add_new_nodes of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The auto_add_new_nodes of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._auto_add_new_nodes

    @auto_add_new_nodes.setter
    def auto_add_new_nodes(self, auto_add_new_nodes):
        """Sets the auto_add_new_nodes of this EndpointForDescribeDBInstanceDetailOutput.


        :param auto_add_new_nodes: The auto_add_new_nodes of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._auto_add_new_nodes = auto_add_new_nodes

    @property
    def description(self):
        """Gets the description of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The description of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this EndpointForDescribeDBInstanceDetailOutput.


        :param description: The description of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enable_read_only(self):
        """Gets the enable_read_only of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The enable_read_only of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_read_only

    @enable_read_only.setter
    def enable_read_only(self, enable_read_only):
        """Sets the enable_read_only of this EndpointForDescribeDBInstanceDetailOutput.


        :param enable_read_only: The enable_read_only of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._enable_read_only = enable_read_only

    @property
    def enable_read_write_splitting(self):
        """Gets the enable_read_write_splitting of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The enable_read_write_splitting of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_read_write_splitting

    @enable_read_write_splitting.setter
    def enable_read_write_splitting(self, enable_read_write_splitting):
        """Sets the enable_read_write_splitting of this EndpointForDescribeDBInstanceDetailOutput.


        :param enable_read_write_splitting: The enable_read_write_splitting of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._enable_read_write_splitting = enable_read_write_splitting

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The endpoint_id of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this EndpointForDescribeDBInstanceDetailOutput.


        :param endpoint_id: The endpoint_id of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_id = endpoint_id

    @property
    def endpoint_name(self):
        """Gets the endpoint_name of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The endpoint_name of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_name

    @endpoint_name.setter
    def endpoint_name(self, endpoint_name):
        """Sets the endpoint_name of this EndpointForDescribeDBInstanceDetailOutput.


        :param endpoint_name: The endpoint_name of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_name = endpoint_name

    @property
    def endpoint_type(self):
        """Gets the endpoint_type of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The endpoint_type of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_type

    @endpoint_type.setter
    def endpoint_type(self, endpoint_type):
        """Sets the endpoint_type of this EndpointForDescribeDBInstanceDetailOutput.


        :param endpoint_type: The endpoint_type of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_type = endpoint_type

    @property
    def read_only_node_distribution_type(self):
        """Gets the read_only_node_distribution_type of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The read_only_node_distribution_type of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._read_only_node_distribution_type

    @read_only_node_distribution_type.setter
    def read_only_node_distribution_type(self, read_only_node_distribution_type):
        """Sets the read_only_node_distribution_type of this EndpointForDescribeDBInstanceDetailOutput.


        :param read_only_node_distribution_type: The read_only_node_distribution_type of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._read_only_node_distribution_type = read_only_node_distribution_type

    @property
    def read_only_node_max_delay_time(self):
        """Gets the read_only_node_max_delay_time of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The read_only_node_max_delay_time of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._read_only_node_max_delay_time

    @read_only_node_max_delay_time.setter
    def read_only_node_max_delay_time(self, read_only_node_max_delay_time):
        """Sets the read_only_node_max_delay_time of this EndpointForDescribeDBInstanceDetailOutput.


        :param read_only_node_max_delay_time: The read_only_node_max_delay_time of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: int
        """

        self._read_only_node_max_delay_time = read_only_node_max_delay_time

    @property
    def read_only_node_weight(self):
        """Gets the read_only_node_weight of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The read_only_node_weight of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: list[ReadOnlyNodeWeightForDescribeDBInstanceDetailOutput]
        """
        return self._read_only_node_weight

    @read_only_node_weight.setter
    def read_only_node_weight(self, read_only_node_weight):
        """Sets the read_only_node_weight of this EndpointForDescribeDBInstanceDetailOutput.


        :param read_only_node_weight: The read_only_node_weight of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: list[ReadOnlyNodeWeightForDescribeDBInstanceDetailOutput]
        """

        self._read_only_node_weight = read_only_node_weight

    @property
    def read_write_mode(self):
        """Gets the read_write_mode of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501


        :return: The read_write_mode of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._read_write_mode

    @read_write_mode.setter
    def read_write_mode(self, read_write_mode):
        """Sets the read_write_mode of this EndpointForDescribeDBInstanceDetailOutput.


        :param read_write_mode: The read_write_mode of this EndpointForDescribeDBInstanceDetailOutput.  # noqa: E501
        :type: str
        """

        self._read_write_mode = read_write_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EndpointForDescribeDBInstanceDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EndpointForDescribeDBInstanceDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EndpointForDescribeDBInstanceDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
