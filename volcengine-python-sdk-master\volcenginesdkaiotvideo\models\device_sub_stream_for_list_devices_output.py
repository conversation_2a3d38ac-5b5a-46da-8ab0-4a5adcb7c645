# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeviceSubStreamForListDevicesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'pull_urls': 'list[str]',
        'push_url': 'str',
        'status': 'str',
        'stream_id': 'str'
    }

    attribute_map = {
        'pull_urls': 'PullUrls',
        'push_url': 'PushUrl',
        'status': 'Status',
        'stream_id': 'StreamID'
    }

    def __init__(self, pull_urls=None, push_url=None, status=None, stream_id=None, _configuration=None):  # noqa: E501
        """DeviceSubStreamForListDevicesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._pull_urls = None
        self._push_url = None
        self._status = None
        self._stream_id = None
        self.discriminator = None

        if pull_urls is not None:
            self.pull_urls = pull_urls
        if push_url is not None:
            self.push_url = push_url
        if status is not None:
            self.status = status
        if stream_id is not None:
            self.stream_id = stream_id

    @property
    def pull_urls(self):
        """Gets the pull_urls of this DeviceSubStreamForListDevicesOutput.  # noqa: E501


        :return: The pull_urls of this DeviceSubStreamForListDevicesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._pull_urls

    @pull_urls.setter
    def pull_urls(self, pull_urls):
        """Sets the pull_urls of this DeviceSubStreamForListDevicesOutput.


        :param pull_urls: The pull_urls of this DeviceSubStreamForListDevicesOutput.  # noqa: E501
        :type: list[str]
        """

        self._pull_urls = pull_urls

    @property
    def push_url(self):
        """Gets the push_url of this DeviceSubStreamForListDevicesOutput.  # noqa: E501


        :return: The push_url of this DeviceSubStreamForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._push_url

    @push_url.setter
    def push_url(self, push_url):
        """Sets the push_url of this DeviceSubStreamForListDevicesOutput.


        :param push_url: The push_url of this DeviceSubStreamForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._push_url = push_url

    @property
    def status(self):
        """Gets the status of this DeviceSubStreamForListDevicesOutput.  # noqa: E501


        :return: The status of this DeviceSubStreamForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DeviceSubStreamForListDevicesOutput.


        :param status: The status of this DeviceSubStreamForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def stream_id(self):
        """Gets the stream_id of this DeviceSubStreamForListDevicesOutput.  # noqa: E501


        :return: The stream_id of this DeviceSubStreamForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._stream_id

    @stream_id.setter
    def stream_id(self, stream_id):
        """Sets the stream_id of this DeviceSubStreamForListDevicesOutput.


        :param stream_id: The stream_id of this DeviceSubStreamForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._stream_id = stream_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeviceSubStreamForListDevicesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeviceSubStreamForListDevicesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeviceSubStreamForListDevicesOutput):
            return True

        return self.to_dict() != other.to_dict()
