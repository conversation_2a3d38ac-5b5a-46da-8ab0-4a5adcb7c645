# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VodPlayerConfigForListVodPlayerConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_default_vod_player_config': 'int',
        'update_time': 'int',
        'vod_player_config_id': 'str',
        'vod_player_name': 'str'
    }

    attribute_map = {
        'is_default_vod_player_config': 'IsDefaultVodPlayerConfig',
        'update_time': 'UpdateTime',
        'vod_player_config_id': 'VodPlayerConfigId',
        'vod_player_name': 'VodPlayerName'
    }

    def __init__(self, is_default_vod_player_config=None, update_time=None, vod_player_config_id=None, vod_player_name=None, _configuration=None):  # noqa: E501
        """VodPlayerConfigForListVodPlayerConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_default_vod_player_config = None
        self._update_time = None
        self._vod_player_config_id = None
        self._vod_player_name = None
        self.discriminator = None

        if is_default_vod_player_config is not None:
            self.is_default_vod_player_config = is_default_vod_player_config
        if update_time is not None:
            self.update_time = update_time
        if vod_player_config_id is not None:
            self.vod_player_config_id = vod_player_config_id
        if vod_player_name is not None:
            self.vod_player_name = vod_player_name

    @property
    def is_default_vod_player_config(self):
        """Gets the is_default_vod_player_config of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501


        :return: The is_default_vod_player_config of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_default_vod_player_config

    @is_default_vod_player_config.setter
    def is_default_vod_player_config(self, is_default_vod_player_config):
        """Sets the is_default_vod_player_config of this VodPlayerConfigForListVodPlayerConfigOutput.


        :param is_default_vod_player_config: The is_default_vod_player_config of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501
        :type: int
        """

        self._is_default_vod_player_config = is_default_vod_player_config

    @property
    def update_time(self):
        """Gets the update_time of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501


        :return: The update_time of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this VodPlayerConfigForListVodPlayerConfigOutput.


        :param update_time: The update_time of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def vod_player_config_id(self):
        """Gets the vod_player_config_id of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501


        :return: The vod_player_config_id of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._vod_player_config_id

    @vod_player_config_id.setter
    def vod_player_config_id(self, vod_player_config_id):
        """Sets the vod_player_config_id of this VodPlayerConfigForListVodPlayerConfigOutput.


        :param vod_player_config_id: The vod_player_config_id of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501
        :type: str
        """

        self._vod_player_config_id = vod_player_config_id

    @property
    def vod_player_name(self):
        """Gets the vod_player_name of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501


        :return: The vod_player_name of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._vod_player_name

    @vod_player_name.setter
    def vod_player_name(self, vod_player_name):
        """Sets the vod_player_name of this VodPlayerConfigForListVodPlayerConfigOutput.


        :param vod_player_name: The vod_player_name of this VodPlayerConfigForListVodPlayerConfigOutput.  # noqa: E501
        :type: str
        """

        self._vod_player_name = vod_player_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VodPlayerConfigForListVodPlayerConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VodPlayerConfigForListVodPlayerConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VodPlayerConfigForListVodPlayerConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
