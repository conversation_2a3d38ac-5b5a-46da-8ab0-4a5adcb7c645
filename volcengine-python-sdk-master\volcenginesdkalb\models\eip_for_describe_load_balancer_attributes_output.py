# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EipForDescribeLoadBalancerAttributesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'association_mode': 'str',
        'bandwidth': 'int',
        'eip_address': 'str',
        'eip_billing_type': 'int',
        'eip_type': 'str',
        'isp': 'str',
        'pop_locations': 'list[PopLocationForDescribeLoadBalancerAttributesOutput]',
        'security_protection_types': 'list[str]'
    }

    attribute_map = {
        'association_mode': 'AssociationMode',
        'bandwidth': 'Bandwidth',
        'eip_address': 'EipAddress',
        'eip_billing_type': 'EipBillingType',
        'eip_type': 'EipType',
        'isp': 'ISP',
        'pop_locations': 'PopLocations',
        'security_protection_types': 'SecurityProtectionTypes'
    }

    def __init__(self, association_mode=None, bandwidth=None, eip_address=None, eip_billing_type=None, eip_type=None, isp=None, pop_locations=None, security_protection_types=None, _configuration=None):  # noqa: E501
        """EipForDescribeLoadBalancerAttributesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._association_mode = None
        self._bandwidth = None
        self._eip_address = None
        self._eip_billing_type = None
        self._eip_type = None
        self._isp = None
        self._pop_locations = None
        self._security_protection_types = None
        self.discriminator = None

        if association_mode is not None:
            self.association_mode = association_mode
        if bandwidth is not None:
            self.bandwidth = bandwidth
        if eip_address is not None:
            self.eip_address = eip_address
        if eip_billing_type is not None:
            self.eip_billing_type = eip_billing_type
        if eip_type is not None:
            self.eip_type = eip_type
        if isp is not None:
            self.isp = isp
        if pop_locations is not None:
            self.pop_locations = pop_locations
        if security_protection_types is not None:
            self.security_protection_types = security_protection_types

    @property
    def association_mode(self):
        """Gets the association_mode of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The association_mode of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._association_mode

    @association_mode.setter
    def association_mode(self, association_mode):
        """Sets the association_mode of this EipForDescribeLoadBalancerAttributesOutput.


        :param association_mode: The association_mode of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._association_mode = association_mode

    @property
    def bandwidth(self):
        """Gets the bandwidth of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The bandwidth of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this EipForDescribeLoadBalancerAttributesOutput.


        :param bandwidth: The bandwidth of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def eip_address(self):
        """Gets the eip_address of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The eip_address of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this EipForDescribeLoadBalancerAttributesOutput.


        :param eip_address: The eip_address of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def eip_billing_type(self):
        """Gets the eip_billing_type of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The eip_billing_type of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: int
        """
        return self._eip_billing_type

    @eip_billing_type.setter
    def eip_billing_type(self, eip_billing_type):
        """Sets the eip_billing_type of this EipForDescribeLoadBalancerAttributesOutput.


        :param eip_billing_type: The eip_billing_type of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: int
        """

        self._eip_billing_type = eip_billing_type

    @property
    def eip_type(self):
        """Gets the eip_type of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The eip_type of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_type

    @eip_type.setter
    def eip_type(self, eip_type):
        """Sets the eip_type of this EipForDescribeLoadBalancerAttributesOutput.


        :param eip_type: The eip_type of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._eip_type = eip_type

    @property
    def isp(self):
        """Gets the isp of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The isp of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this EipForDescribeLoadBalancerAttributesOutput.


        :param isp: The isp of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def pop_locations(self):
        """Gets the pop_locations of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The pop_locations of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: list[PopLocationForDescribeLoadBalancerAttributesOutput]
        """
        return self._pop_locations

    @pop_locations.setter
    def pop_locations(self, pop_locations):
        """Sets the pop_locations of this EipForDescribeLoadBalancerAttributesOutput.


        :param pop_locations: The pop_locations of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: list[PopLocationForDescribeLoadBalancerAttributesOutput]
        """

        self._pop_locations = pop_locations

    @property
    def security_protection_types(self):
        """Gets the security_protection_types of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The security_protection_types of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_protection_types

    @security_protection_types.setter
    def security_protection_types(self, security_protection_types):
        """Sets the security_protection_types of this EipForDescribeLoadBalancerAttributesOutput.


        :param security_protection_types: The security_protection_types of this EipForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: list[str]
        """

        self._security_protection_types = security_protection_types

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EipForDescribeLoadBalancerAttributesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EipForDescribeLoadBalancerAttributesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EipForDescribeLoadBalancerAttributesOutput):
            return True

        return self.to_dict() != other.to_dict()
