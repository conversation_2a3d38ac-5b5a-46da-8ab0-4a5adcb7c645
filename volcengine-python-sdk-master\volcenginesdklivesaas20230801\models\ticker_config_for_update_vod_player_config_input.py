# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TickerConfigForUpdateVodPlayerConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'content': 'str',
        'display_type': 'int',
        'font_color': 'str',
        'font_size': 'int',
        'is_play_page_ticker_enable': 'int',
        'opacity': 'int',
        'ticker_speed': 'str'
    }

    attribute_map = {
        'content': 'Content',
        'display_type': 'DisplayType',
        'font_color': 'FontColor',
        'font_size': 'FontSize',
        'is_play_page_ticker_enable': 'IsPlayPageTickerEnable',
        'opacity': 'Opacity',
        'ticker_speed': 'TickerSpeed'
    }

    def __init__(self, content=None, display_type=None, font_color=None, font_size=None, is_play_page_ticker_enable=None, opacity=None, ticker_speed=None, _configuration=None):  # noqa: E501
        """TickerConfigForUpdateVodPlayerConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._content = None
        self._display_type = None
        self._font_color = None
        self._font_size = None
        self._is_play_page_ticker_enable = None
        self._opacity = None
        self._ticker_speed = None
        self.discriminator = None

        if content is not None:
            self.content = content
        if display_type is not None:
            self.display_type = display_type
        if font_color is not None:
            self.font_color = font_color
        if font_size is not None:
            self.font_size = font_size
        if is_play_page_ticker_enable is not None:
            self.is_play_page_ticker_enable = is_play_page_ticker_enable
        if opacity is not None:
            self.opacity = opacity
        if ticker_speed is not None:
            self.ticker_speed = ticker_speed

    @property
    def content(self):
        """Gets the content of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The content of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._content

    @content.setter
    def content(self, content):
        """Sets the content of this TickerConfigForUpdateVodPlayerConfigInput.


        :param content: The content of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: str
        """

        self._content = content

    @property
    def display_type(self):
        """Gets the display_type of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The display_type of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._display_type

    @display_type.setter
    def display_type(self, display_type):
        """Sets the display_type of this TickerConfigForUpdateVodPlayerConfigInput.


        :param display_type: The display_type of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: int
        """

        self._display_type = display_type

    @property
    def font_color(self):
        """Gets the font_color of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The font_color of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._font_color

    @font_color.setter
    def font_color(self, font_color):
        """Sets the font_color of this TickerConfigForUpdateVodPlayerConfigInput.


        :param font_color: The font_color of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: str
        """

        self._font_color = font_color

    @property
    def font_size(self):
        """Gets the font_size of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The font_size of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._font_size

    @font_size.setter
    def font_size(self, font_size):
        """Sets the font_size of this TickerConfigForUpdateVodPlayerConfigInput.


        :param font_size: The font_size of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: int
        """

        self._font_size = font_size

    @property
    def is_play_page_ticker_enable(self):
        """Gets the is_play_page_ticker_enable of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The is_play_page_ticker_enable of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_play_page_ticker_enable

    @is_play_page_ticker_enable.setter
    def is_play_page_ticker_enable(self, is_play_page_ticker_enable):
        """Sets the is_play_page_ticker_enable of this TickerConfigForUpdateVodPlayerConfigInput.


        :param is_play_page_ticker_enable: The is_play_page_ticker_enable of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: int
        """

        self._is_play_page_ticker_enable = is_play_page_ticker_enable

    @property
    def opacity(self):
        """Gets the opacity of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The opacity of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._opacity

    @opacity.setter
    def opacity(self, opacity):
        """Sets the opacity of this TickerConfigForUpdateVodPlayerConfigInput.


        :param opacity: The opacity of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: int
        """

        self._opacity = opacity

    @property
    def ticker_speed(self):
        """Gets the ticker_speed of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501


        :return: The ticker_speed of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._ticker_speed

    @ticker_speed.setter
    def ticker_speed(self, ticker_speed):
        """Sets the ticker_speed of this TickerConfigForUpdateVodPlayerConfigInput.


        :param ticker_speed: The ticker_speed of this TickerConfigForUpdateVodPlayerConfigInput.  # noqa: E501
        :type: str
        """

        self._ticker_speed = ticker_speed

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TickerConfigForUpdateVodPlayerConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TickerConfigForUpdateVodPlayerConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TickerConfigForUpdateVodPlayerConfigInput):
            return True

        return self.to_dict() != other.to_dict()
