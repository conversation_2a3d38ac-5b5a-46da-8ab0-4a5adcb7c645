# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetRegistriesPermissionResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'has_authorize': 'bool',
        'policy': 'str',
        'role': 'str',
        'service_name': 'str'
    }

    attribute_map = {
        'has_authorize': 'HasAuthorize',
        'policy': 'Policy',
        'role': 'Role',
        'service_name': 'ServiceName'
    }

    def __init__(self, has_authorize=None, policy=None, role=None, service_name=None, _configuration=None):  # noqa: E501
        """DataForGetRegistriesPermissionResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._has_authorize = None
        self._policy = None
        self._role = None
        self._service_name = None
        self.discriminator = None

        if has_authorize is not None:
            self.has_authorize = has_authorize
        if policy is not None:
            self.policy = policy
        if role is not None:
            self.role = role
        if service_name is not None:
            self.service_name = service_name

    @property
    def has_authorize(self):
        """Gets the has_authorize of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501


        :return: The has_authorize of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501
        :rtype: bool
        """
        return self._has_authorize

    @has_authorize.setter
    def has_authorize(self, has_authorize):
        """Sets the has_authorize of this DataForGetRegistriesPermissionResultOutput.


        :param has_authorize: The has_authorize of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501
        :type: bool
        """

        self._has_authorize = has_authorize

    @property
    def policy(self):
        """Gets the policy of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501


        :return: The policy of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._policy

    @policy.setter
    def policy(self, policy):
        """Sets the policy of this DataForGetRegistriesPermissionResultOutput.


        :param policy: The policy of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501
        :type: str
        """

        self._policy = policy

    @property
    def role(self):
        """Gets the role of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501


        :return: The role of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._role

    @role.setter
    def role(self, role):
        """Sets the role of this DataForGetRegistriesPermissionResultOutput.


        :param role: The role of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501
        :type: str
        """

        self._role = role

    @property
    def service_name(self):
        """Gets the service_name of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501


        :return: The service_name of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this DataForGetRegistriesPermissionResultOutput.


        :param service_name: The service_name of this DataForGetRegistriesPermissionResultOutput.  # noqa: E501
        :type: str
        """

        self._service_name = service_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetRegistriesPermissionResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetRegistriesPermissionResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetRegistriesPermissionResultOutput):
            return True

        return self.to_dict() != other.to_dict()
