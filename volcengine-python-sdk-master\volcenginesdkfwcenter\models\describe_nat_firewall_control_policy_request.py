# coding: utf-8

"""
    fwcenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNatFirewallControlPolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'list[str]',
        'description': 'str',
        'dest_port': 'list[str]',
        'destination': 'list[str]',
        'direction': 'str',
        'nat_firewall_id': 'str',
        'order_by': 'str',
        'order_dir': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'proto': 'list[str]',
        'repeat_type': 'list[str]',
        'rule_id': 'list[str]',
        'source': 'list[str]',
        'status': 'list[bool]'
    }

    attribute_map = {
        'action': 'Action',
        'description': 'Description',
        'dest_port': 'DestPort',
        'destination': 'Destination',
        'direction': 'Direction',
        'nat_firewall_id': 'NatFirewallId',
        'order_by': 'OrderBy',
        'order_dir': 'OrderDir',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'proto': 'Proto',
        'repeat_type': 'RepeatType',
        'rule_id': 'RuleId',
        'source': 'Source',
        'status': 'Status'
    }

    def __init__(self, action=None, description=None, dest_port=None, destination=None, direction=None, nat_firewall_id=None, order_by=None, order_dir=None, page_number=None, page_size=None, proto=None, repeat_type=None, rule_id=None, source=None, status=None, _configuration=None):  # noqa: E501
        """DescribeNatFirewallControlPolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._description = None
        self._dest_port = None
        self._destination = None
        self._direction = None
        self._nat_firewall_id = None
        self._order_by = None
        self._order_dir = None
        self._page_number = None
        self._page_size = None
        self._proto = None
        self._repeat_type = None
        self._rule_id = None
        self._source = None
        self._status = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if description is not None:
            self.description = description
        if dest_port is not None:
            self.dest_port = dest_port
        if destination is not None:
            self.destination = destination
        self.direction = direction
        self.nat_firewall_id = nat_firewall_id
        if order_by is not None:
            self.order_by = order_by
        if order_dir is not None:
            self.order_dir = order_dir
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if proto is not None:
            self.proto = proto
        if repeat_type is not None:
            self.repeat_type = repeat_type
        if rule_id is not None:
            self.rule_id = rule_id
        if source is not None:
            self.source = source
        if status is not None:
            self.status = status

    @property
    def action(self):
        """Gets the action of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The action of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this DescribeNatFirewallControlPolicyRequest.


        :param action: The action of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._action = action

    @property
    def description(self):
        """Gets the description of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The description of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeNatFirewallControlPolicyRequest.


        :param description: The description of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dest_port(self):
        """Gets the dest_port of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The dest_port of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._dest_port

    @dest_port.setter
    def dest_port(self, dest_port):
        """Sets the dest_port of this DescribeNatFirewallControlPolicyRequest.


        :param dest_port: The dest_port of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._dest_port = dest_port

    @property
    def destination(self):
        """Gets the destination of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The destination of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._destination

    @destination.setter
    def destination(self, destination):
        """Sets the destination of this DescribeNatFirewallControlPolicyRequest.


        :param destination: The destination of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._destination = destination

    @property
    def direction(self):
        """Gets the direction of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The direction of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._direction

    @direction.setter
    def direction(self, direction):
        """Sets the direction of this DescribeNatFirewallControlPolicyRequest.


        :param direction: The direction of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and direction is None:
            raise ValueError("Invalid value for `direction`, must not be `None`")  # noqa: E501
        allowed_values = ["in", "out"]  # noqa: E501
        if (self._configuration.client_side_validation and
                direction not in allowed_values):
            raise ValueError(
                "Invalid value for `direction` ({0}), must be one of {1}"  # noqa: E501
                .format(direction, allowed_values)
            )

        self._direction = direction

    @property
    def nat_firewall_id(self):
        """Gets the nat_firewall_id of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The nat_firewall_id of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._nat_firewall_id

    @nat_firewall_id.setter
    def nat_firewall_id(self, nat_firewall_id):
        """Sets the nat_firewall_id of this DescribeNatFirewallControlPolicyRequest.


        :param nat_firewall_id: The nat_firewall_id of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and nat_firewall_id is None:
            raise ValueError("Invalid value for `nat_firewall_id`, must not be `None`")  # noqa: E501

        self._nat_firewall_id = nat_firewall_id

    @property
    def order_by(self):
        """Gets the order_by of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The order_by of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_by

    @order_by.setter
    def order_by(self, order_by):
        """Sets the order_by of this DescribeNatFirewallControlPolicyRequest.


        :param order_by: The order_by of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: str
        """

        self._order_by = order_by

    @property
    def order_dir(self):
        """Gets the order_dir of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The order_dir of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_dir

    @order_dir.setter
    def order_dir(self, order_dir):
        """Sets the order_dir of this DescribeNatFirewallControlPolicyRequest.


        :param order_dir: The order_dir of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["asc", "desc"]  # noqa: E501
        if (self._configuration.client_side_validation and
                order_dir not in allowed_values):
            raise ValueError(
                "Invalid value for `order_dir` ({0}), must be one of {1}"  # noqa: E501
                .format(order_dir, allowed_values)
            )

        self._order_dir = order_dir

    @property
    def page_number(self):
        """Gets the page_number of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The page_number of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeNatFirewallControlPolicyRequest.


        :param page_number: The page_number of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The page_size of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeNatFirewallControlPolicyRequest.


        :param page_size: The page_size of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def proto(self):
        """Gets the proto of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The proto of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._proto

    @proto.setter
    def proto(self, proto):
        """Sets the proto of this DescribeNatFirewallControlPolicyRequest.


        :param proto: The proto of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._proto = proto

    @property
    def repeat_type(self):
        """Gets the repeat_type of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The repeat_type of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._repeat_type

    @repeat_type.setter
    def repeat_type(self, repeat_type):
        """Sets the repeat_type of this DescribeNatFirewallControlPolicyRequest.


        :param repeat_type: The repeat_type of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._repeat_type = repeat_type

    @property
    def rule_id(self):
        """Gets the rule_id of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The rule_id of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this DescribeNatFirewallControlPolicyRequest.


        :param rule_id: The rule_id of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._rule_id = rule_id

    @property
    def source(self):
        """Gets the source of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The source of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this DescribeNatFirewallControlPolicyRequest.


        :param source: The source of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._source = source

    @property
    def status(self):
        """Gets the status of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501


        :return: The status of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :rtype: list[bool]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeNatFirewallControlPolicyRequest.


        :param status: The status of this DescribeNatFirewallControlPolicyRequest.  # noqa: E501
        :type: list[bool]
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNatFirewallControlPolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNatFirewallControlPolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNatFirewallControlPolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
