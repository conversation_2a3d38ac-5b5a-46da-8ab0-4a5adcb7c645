# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DiagnoseConfigForCreateTaskInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dns_config': 'DNSConfigForCreateTaskInput',
        'method_list': 'list[int]',
        'mtr_config': 'MtrConfigForCreateTaskInput',
        'pcap_status': 'int',
        'ping_config': 'PingConfigForCreateTaskInput'
    }

    attribute_map = {
        'dns_config': 'DNSConfig',
        'method_list': 'MethodList',
        'mtr_config': 'MtrConfig',
        'pcap_status': 'PcapStatus',
        'ping_config': 'PingConfig'
    }

    def __init__(self, dns_config=None, method_list=None, mtr_config=None, pcap_status=None, ping_config=None, _configuration=None):  # noqa: E501
        """DiagnoseConfigForCreateTaskInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dns_config = None
        self._method_list = None
        self._mtr_config = None
        self._pcap_status = None
        self._ping_config = None
        self.discriminator = None

        if dns_config is not None:
            self.dns_config = dns_config
        if method_list is not None:
            self.method_list = method_list
        if mtr_config is not None:
            self.mtr_config = mtr_config
        if pcap_status is not None:
            self.pcap_status = pcap_status
        if ping_config is not None:
            self.ping_config = ping_config

    @property
    def dns_config(self):
        """Gets the dns_config of this DiagnoseConfigForCreateTaskInput.  # noqa: E501


        :return: The dns_config of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :rtype: DNSConfigForCreateTaskInput
        """
        return self._dns_config

    @dns_config.setter
    def dns_config(self, dns_config):
        """Sets the dns_config of this DiagnoseConfigForCreateTaskInput.


        :param dns_config: The dns_config of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :type: DNSConfigForCreateTaskInput
        """

        self._dns_config = dns_config

    @property
    def method_list(self):
        """Gets the method_list of this DiagnoseConfigForCreateTaskInput.  # noqa: E501


        :return: The method_list of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :rtype: list[int]
        """
        return self._method_list

    @method_list.setter
    def method_list(self, method_list):
        """Sets the method_list of this DiagnoseConfigForCreateTaskInput.


        :param method_list: The method_list of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :type: list[int]
        """

        self._method_list = method_list

    @property
    def mtr_config(self):
        """Gets the mtr_config of this DiagnoseConfigForCreateTaskInput.  # noqa: E501


        :return: The mtr_config of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :rtype: MtrConfigForCreateTaskInput
        """
        return self._mtr_config

    @mtr_config.setter
    def mtr_config(self, mtr_config):
        """Sets the mtr_config of this DiagnoseConfigForCreateTaskInput.


        :param mtr_config: The mtr_config of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :type: MtrConfigForCreateTaskInput
        """

        self._mtr_config = mtr_config

    @property
    def pcap_status(self):
        """Gets the pcap_status of this DiagnoseConfigForCreateTaskInput.  # noqa: E501


        :return: The pcap_status of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._pcap_status

    @pcap_status.setter
    def pcap_status(self, pcap_status):
        """Sets the pcap_status of this DiagnoseConfigForCreateTaskInput.


        :param pcap_status: The pcap_status of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._pcap_status = pcap_status

    @property
    def ping_config(self):
        """Gets the ping_config of this DiagnoseConfigForCreateTaskInput.  # noqa: E501


        :return: The ping_config of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :rtype: PingConfigForCreateTaskInput
        """
        return self._ping_config

    @ping_config.setter
    def ping_config(self, ping_config):
        """Sets the ping_config of this DiagnoseConfigForCreateTaskInput.


        :param ping_config: The ping_config of this DiagnoseConfigForCreateTaskInput.  # noqa: E501
        :type: PingConfigForCreateTaskInput
        """

        self._ping_config = ping_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DiagnoseConfigForCreateTaskInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DiagnoseConfigForCreateTaskInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DiagnoseConfigForCreateTaskInput):
            return True

        return self.to_dict() != other.to_dict()
