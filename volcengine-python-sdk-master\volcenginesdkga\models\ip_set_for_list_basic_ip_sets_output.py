# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IPSetForListBasicIPSetsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'edge_nodes': 'list[EdgeNodeForListBasicIPSetsOutput]',
        'exist_bound_ip': 'bool',
        'ip_set_id': 'str',
        'ip_version': 'str',
        'region': 'str',
        'state': 'str',
        'accelerate_ips': 'list[str]'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'edge_nodes': 'EdgeNodes',
        'exist_bound_ip': 'ExistBoundIP',
        'ip_set_id': 'IPSetId',
        'ip_version': 'IPVersion',
        'region': 'Region',
        'state': 'State',
        'accelerate_ips': 'accelerateIPs'
    }

    def __init__(self, accelerator_id=None, edge_nodes=None, exist_bound_ip=None, ip_set_id=None, ip_version=None, region=None, state=None, accelerate_ips=None, _configuration=None):  # noqa: E501
        """IPSetForListBasicIPSetsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._edge_nodes = None
        self._exist_bound_ip = None
        self._ip_set_id = None
        self._ip_version = None
        self._region = None
        self._state = None
        self._accelerate_ips = None
        self.discriminator = None

        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if edge_nodes is not None:
            self.edge_nodes = edge_nodes
        if exist_bound_ip is not None:
            self.exist_bound_ip = exist_bound_ip
        if ip_set_id is not None:
            self.ip_set_id = ip_set_id
        if ip_version is not None:
            self.ip_version = ip_version
        if region is not None:
            self.region = region
        if state is not None:
            self.state = state
        if accelerate_ips is not None:
            self.accelerate_ips = accelerate_ips

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this IPSetForListBasicIPSetsOutput.  # noqa: E501


        :return: The accelerator_id of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this IPSetForListBasicIPSetsOutput.


        :param accelerator_id: The accelerator_id of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def edge_nodes(self):
        """Gets the edge_nodes of this IPSetForListBasicIPSetsOutput.  # noqa: E501


        :return: The edge_nodes of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :rtype: list[EdgeNodeForListBasicIPSetsOutput]
        """
        return self._edge_nodes

    @edge_nodes.setter
    def edge_nodes(self, edge_nodes):
        """Sets the edge_nodes of this IPSetForListBasicIPSetsOutput.


        :param edge_nodes: The edge_nodes of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :type: list[EdgeNodeForListBasicIPSetsOutput]
        """

        self._edge_nodes = edge_nodes

    @property
    def exist_bound_ip(self):
        """Gets the exist_bound_ip of this IPSetForListBasicIPSetsOutput.  # noqa: E501


        :return: The exist_bound_ip of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._exist_bound_ip

    @exist_bound_ip.setter
    def exist_bound_ip(self, exist_bound_ip):
        """Sets the exist_bound_ip of this IPSetForListBasicIPSetsOutput.


        :param exist_bound_ip: The exist_bound_ip of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :type: bool
        """

        self._exist_bound_ip = exist_bound_ip

    @property
    def ip_set_id(self):
        """Gets the ip_set_id of this IPSetForListBasicIPSetsOutput.  # noqa: E501


        :return: The ip_set_id of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip_set_id

    @ip_set_id.setter
    def ip_set_id(self, ip_set_id):
        """Sets the ip_set_id of this IPSetForListBasicIPSetsOutput.


        :param ip_set_id: The ip_set_id of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :type: str
        """

        self._ip_set_id = ip_set_id

    @property
    def ip_version(self):
        """Gets the ip_version of this IPSetForListBasicIPSetsOutput.  # noqa: E501


        :return: The ip_version of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip_version

    @ip_version.setter
    def ip_version(self, ip_version):
        """Sets the ip_version of this IPSetForListBasicIPSetsOutput.


        :param ip_version: The ip_version of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :type: str
        """

        self._ip_version = ip_version

    @property
    def region(self):
        """Gets the region of this IPSetForListBasicIPSetsOutput.  # noqa: E501


        :return: The region of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this IPSetForListBasicIPSetsOutput.


        :param region: The region of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def state(self):
        """Gets the state of this IPSetForListBasicIPSetsOutput.  # noqa: E501


        :return: The state of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this IPSetForListBasicIPSetsOutput.


        :param state: The state of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def accelerate_ips(self):
        """Gets the accelerate_ips of this IPSetForListBasicIPSetsOutput.  # noqa: E501


        :return: The accelerate_ips of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._accelerate_ips

    @accelerate_ips.setter
    def accelerate_ips(self, accelerate_ips):
        """Sets the accelerate_ips of this IPSetForListBasicIPSetsOutput.


        :param accelerate_ips: The accelerate_ips of this IPSetForListBasicIPSetsOutput.  # noqa: E501
        :type: list[str]
        """

        self._accelerate_ips = accelerate_ips

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IPSetForListBasicIPSetsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IPSetForListBasicIPSetsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IPSetForListBasicIPSetsOutput):
            return True

        return self.to_dict() != other.to_dict()
