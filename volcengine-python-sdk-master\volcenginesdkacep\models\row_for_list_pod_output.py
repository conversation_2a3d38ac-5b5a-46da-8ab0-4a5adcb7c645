# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RowForListPodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'adb': 'str',
        'adb_expire_time': 'int',
        'adb_status': 'int',
        'aosp_version': 'str',
        'archive_status': 'int',
        'authority_expire_time': 'int',
        'authority_status': 'int',
        'cidr_block': 'str',
        'configuration': 'ConfigurationForListPodOutput',
        'create_at': 'int',
        'dnsid': 'str',
        'data_size': 'str',
        'data_size_used': 'str',
        'dc_info': 'DcInfoForListPodOutput',
        'display_layout_id': 'str',
        'display_status': 'DisplayStatusForListPodOutput',
        'down_bandwidth_limit': 'int',
        'eip': 'EipForListPodOutput',
        'host_id': 'str',
        'image_id': 'str',
        'image_name': 'str',
        'intranet_ip': 'str',
        'online': 'int',
        'pod_id': 'str',
        'pod_name': 'str',
        'port_mapping_rule_list': 'list[PortMappingRuleListForListPodOutput]',
        'product_id': 'str',
        'snatid': 'str',
        'server_type_code': 'str',
        'stream_status': 'int',
        'tag': 'TagForListPodOutput',
        'up_bandwidth_limit': 'int'
    }

    attribute_map = {
        'adb': 'Adb',
        'adb_expire_time': 'AdbExpireTime',
        'adb_status': 'AdbStatus',
        'aosp_version': 'AospVersion',
        'archive_status': 'ArchiveStatus',
        'authority_expire_time': 'AuthorityExpireTime',
        'authority_status': 'AuthorityStatus',
        'cidr_block': 'CidrBlock',
        'configuration': 'Configuration',
        'create_at': 'CreateAt',
        'dnsid': 'DNSId',
        'data_size': 'DataSize',
        'data_size_used': 'DataSizeUsed',
        'dc_info': 'DcInfo',
        'display_layout_id': 'DisplayLayoutId',
        'display_status': 'DisplayStatus',
        'down_bandwidth_limit': 'DownBandwidthLimit',
        'eip': 'Eip',
        'host_id': 'HostId',
        'image_id': 'ImageId',
        'image_name': 'ImageName',
        'intranet_ip': 'IntranetIP',
        'online': 'Online',
        'pod_id': 'PodId',
        'pod_name': 'PodName',
        'port_mapping_rule_list': 'PortMappingRuleList',
        'product_id': 'ProductId',
        'snatid': 'SNATId',
        'server_type_code': 'ServerTypeCode',
        'stream_status': 'StreamStatus',
        'tag': 'Tag',
        'up_bandwidth_limit': 'UpBandwidthLimit'
    }

    def __init__(self, adb=None, adb_expire_time=None, adb_status=None, aosp_version=None, archive_status=None, authority_expire_time=None, authority_status=None, cidr_block=None, configuration=None, create_at=None, dnsid=None, data_size=None, data_size_used=None, dc_info=None, display_layout_id=None, display_status=None, down_bandwidth_limit=None, eip=None, host_id=None, image_id=None, image_name=None, intranet_ip=None, online=None, pod_id=None, pod_name=None, port_mapping_rule_list=None, product_id=None, snatid=None, server_type_code=None, stream_status=None, tag=None, up_bandwidth_limit=None, _configuration=None):  # noqa: E501
        """RowForListPodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._adb = None
        self._adb_expire_time = None
        self._adb_status = None
        self._aosp_version = None
        self._archive_status = None
        self._authority_expire_time = None
        self._authority_status = None
        self._cidr_block = None
        self._configuration = None
        self._create_at = None
        self._dnsid = None
        self._data_size = None
        self._data_size_used = None
        self._dc_info = None
        self._display_layout_id = None
        self._display_status = None
        self._down_bandwidth_limit = None
        self._eip = None
        self._host_id = None
        self._image_id = None
        self._image_name = None
        self._intranet_ip = None
        self._online = None
        self._pod_id = None
        self._pod_name = None
        self._port_mapping_rule_list = None
        self._product_id = None
        self._snatid = None
        self._server_type_code = None
        self._stream_status = None
        self._tag = None
        self._up_bandwidth_limit = None
        self.discriminator = None

        if adb is not None:
            self.adb = adb
        if adb_expire_time is not None:
            self.adb_expire_time = adb_expire_time
        if adb_status is not None:
            self.adb_status = adb_status
        if aosp_version is not None:
            self.aosp_version = aosp_version
        if archive_status is not None:
            self.archive_status = archive_status
        if authority_expire_time is not None:
            self.authority_expire_time = authority_expire_time
        if authority_status is not None:
            self.authority_status = authority_status
        if cidr_block is not None:
            self.cidr_block = cidr_block
        if configuration is not None:
            self.configuration = configuration
        if create_at is not None:
            self.create_at = create_at
        if dnsid is not None:
            self.dnsid = dnsid
        if data_size is not None:
            self.data_size = data_size
        if data_size_used is not None:
            self.data_size_used = data_size_used
        if dc_info is not None:
            self.dc_info = dc_info
        if display_layout_id is not None:
            self.display_layout_id = display_layout_id
        if display_status is not None:
            self.display_status = display_status
        if down_bandwidth_limit is not None:
            self.down_bandwidth_limit = down_bandwidth_limit
        if eip is not None:
            self.eip = eip
        if host_id is not None:
            self.host_id = host_id
        if image_id is not None:
            self.image_id = image_id
        if image_name is not None:
            self.image_name = image_name
        if intranet_ip is not None:
            self.intranet_ip = intranet_ip
        if online is not None:
            self.online = online
        if pod_id is not None:
            self.pod_id = pod_id
        if pod_name is not None:
            self.pod_name = pod_name
        if port_mapping_rule_list is not None:
            self.port_mapping_rule_list = port_mapping_rule_list
        if product_id is not None:
            self.product_id = product_id
        if snatid is not None:
            self.snatid = snatid
        if server_type_code is not None:
            self.server_type_code = server_type_code
        if stream_status is not None:
            self.stream_status = stream_status
        if tag is not None:
            self.tag = tag
        if up_bandwidth_limit is not None:
            self.up_bandwidth_limit = up_bandwidth_limit

    @property
    def adb(self):
        """Gets the adb of this RowForListPodOutput.  # noqa: E501


        :return: The adb of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._adb

    @adb.setter
    def adb(self, adb):
        """Sets the adb of this RowForListPodOutput.


        :param adb: The adb of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._adb = adb

    @property
    def adb_expire_time(self):
        """Gets the adb_expire_time of this RowForListPodOutput.  # noqa: E501


        :return: The adb_expire_time of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._adb_expire_time

    @adb_expire_time.setter
    def adb_expire_time(self, adb_expire_time):
        """Sets the adb_expire_time of this RowForListPodOutput.


        :param adb_expire_time: The adb_expire_time of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._adb_expire_time = adb_expire_time

    @property
    def adb_status(self):
        """Gets the adb_status of this RowForListPodOutput.  # noqa: E501


        :return: The adb_status of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._adb_status

    @adb_status.setter
    def adb_status(self, adb_status):
        """Sets the adb_status of this RowForListPodOutput.


        :param adb_status: The adb_status of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._adb_status = adb_status

    @property
    def aosp_version(self):
        """Gets the aosp_version of this RowForListPodOutput.  # noqa: E501


        :return: The aosp_version of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._aosp_version

    @aosp_version.setter
    def aosp_version(self, aosp_version):
        """Sets the aosp_version of this RowForListPodOutput.


        :param aosp_version: The aosp_version of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._aosp_version = aosp_version

    @property
    def archive_status(self):
        """Gets the archive_status of this RowForListPodOutput.  # noqa: E501


        :return: The archive_status of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._archive_status

    @archive_status.setter
    def archive_status(self, archive_status):
        """Sets the archive_status of this RowForListPodOutput.


        :param archive_status: The archive_status of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._archive_status = archive_status

    @property
    def authority_expire_time(self):
        """Gets the authority_expire_time of this RowForListPodOutput.  # noqa: E501


        :return: The authority_expire_time of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._authority_expire_time

    @authority_expire_time.setter
    def authority_expire_time(self, authority_expire_time):
        """Sets the authority_expire_time of this RowForListPodOutput.


        :param authority_expire_time: The authority_expire_time of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._authority_expire_time = authority_expire_time

    @property
    def authority_status(self):
        """Gets the authority_status of this RowForListPodOutput.  # noqa: E501


        :return: The authority_status of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._authority_status

    @authority_status.setter
    def authority_status(self, authority_status):
        """Sets the authority_status of this RowForListPodOutput.


        :param authority_status: The authority_status of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._authority_status = authority_status

    @property
    def cidr_block(self):
        """Gets the cidr_block of this RowForListPodOutput.  # noqa: E501


        :return: The cidr_block of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._cidr_block

    @cidr_block.setter
    def cidr_block(self, cidr_block):
        """Sets the cidr_block of this RowForListPodOutput.


        :param cidr_block: The cidr_block of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._cidr_block = cidr_block

    @property
    def configuration(self):
        """Gets the configuration of this RowForListPodOutput.  # noqa: E501


        :return: The configuration of this RowForListPodOutput.  # noqa: E501
        :rtype: ConfigurationForListPodOutput
        """
        return self._configuration

    @configuration.setter
    def configuration(self, configuration):
        """Sets the configuration of this RowForListPodOutput.


        :param configuration: The configuration of this RowForListPodOutput.  # noqa: E501
        :type: ConfigurationForListPodOutput
        """

        self._configuration = configuration

    @property
    def create_at(self):
        """Gets the create_at of this RowForListPodOutput.  # noqa: E501


        :return: The create_at of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_at

    @create_at.setter
    def create_at(self, create_at):
        """Sets the create_at of this RowForListPodOutput.


        :param create_at: The create_at of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._create_at = create_at

    @property
    def dnsid(self):
        """Gets the dnsid of this RowForListPodOutput.  # noqa: E501


        :return: The dnsid of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._dnsid

    @dnsid.setter
    def dnsid(self, dnsid):
        """Sets the dnsid of this RowForListPodOutput.


        :param dnsid: The dnsid of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._dnsid = dnsid

    @property
    def data_size(self):
        """Gets the data_size of this RowForListPodOutput.  # noqa: E501


        :return: The data_size of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_size

    @data_size.setter
    def data_size(self, data_size):
        """Sets the data_size of this RowForListPodOutput.


        :param data_size: The data_size of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._data_size = data_size

    @property
    def data_size_used(self):
        """Gets the data_size_used of this RowForListPodOutput.  # noqa: E501


        :return: The data_size_used of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_size_used

    @data_size_used.setter
    def data_size_used(self, data_size_used):
        """Sets the data_size_used of this RowForListPodOutput.


        :param data_size_used: The data_size_used of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._data_size_used = data_size_used

    @property
    def dc_info(self):
        """Gets the dc_info of this RowForListPodOutput.  # noqa: E501


        :return: The dc_info of this RowForListPodOutput.  # noqa: E501
        :rtype: DcInfoForListPodOutput
        """
        return self._dc_info

    @dc_info.setter
    def dc_info(self, dc_info):
        """Sets the dc_info of this RowForListPodOutput.


        :param dc_info: The dc_info of this RowForListPodOutput.  # noqa: E501
        :type: DcInfoForListPodOutput
        """

        self._dc_info = dc_info

    @property
    def display_layout_id(self):
        """Gets the display_layout_id of this RowForListPodOutput.  # noqa: E501


        :return: The display_layout_id of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._display_layout_id

    @display_layout_id.setter
    def display_layout_id(self, display_layout_id):
        """Sets the display_layout_id of this RowForListPodOutput.


        :param display_layout_id: The display_layout_id of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._display_layout_id = display_layout_id

    @property
    def display_status(self):
        """Gets the display_status of this RowForListPodOutput.  # noqa: E501


        :return: The display_status of this RowForListPodOutput.  # noqa: E501
        :rtype: DisplayStatusForListPodOutput
        """
        return self._display_status

    @display_status.setter
    def display_status(self, display_status):
        """Sets the display_status of this RowForListPodOutput.


        :param display_status: The display_status of this RowForListPodOutput.  # noqa: E501
        :type: DisplayStatusForListPodOutput
        """

        self._display_status = display_status

    @property
    def down_bandwidth_limit(self):
        """Gets the down_bandwidth_limit of this RowForListPodOutput.  # noqa: E501


        :return: The down_bandwidth_limit of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._down_bandwidth_limit

    @down_bandwidth_limit.setter
    def down_bandwidth_limit(self, down_bandwidth_limit):
        """Sets the down_bandwidth_limit of this RowForListPodOutput.


        :param down_bandwidth_limit: The down_bandwidth_limit of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._down_bandwidth_limit = down_bandwidth_limit

    @property
    def eip(self):
        """Gets the eip of this RowForListPodOutput.  # noqa: E501


        :return: The eip of this RowForListPodOutput.  # noqa: E501
        :rtype: EipForListPodOutput
        """
        return self._eip

    @eip.setter
    def eip(self, eip):
        """Sets the eip of this RowForListPodOutput.


        :param eip: The eip of this RowForListPodOutput.  # noqa: E501
        :type: EipForListPodOutput
        """

        self._eip = eip

    @property
    def host_id(self):
        """Gets the host_id of this RowForListPodOutput.  # noqa: E501


        :return: The host_id of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._host_id

    @host_id.setter
    def host_id(self, host_id):
        """Sets the host_id of this RowForListPodOutput.


        :param host_id: The host_id of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._host_id = host_id

    @property
    def image_id(self):
        """Gets the image_id of this RowForListPodOutput.  # noqa: E501


        :return: The image_id of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this RowForListPodOutput.


        :param image_id: The image_id of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def image_name(self):
        """Gets the image_name of this RowForListPodOutput.  # noqa: E501


        :return: The image_name of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this RowForListPodOutput.


        :param image_name: The image_name of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._image_name = image_name

    @property
    def intranet_ip(self):
        """Gets the intranet_ip of this RowForListPodOutput.  # noqa: E501


        :return: The intranet_ip of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._intranet_ip

    @intranet_ip.setter
    def intranet_ip(self, intranet_ip):
        """Sets the intranet_ip of this RowForListPodOutput.


        :param intranet_ip: The intranet_ip of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._intranet_ip = intranet_ip

    @property
    def online(self):
        """Gets the online of this RowForListPodOutput.  # noqa: E501


        :return: The online of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._online

    @online.setter
    def online(self, online):
        """Sets the online of this RowForListPodOutput.


        :param online: The online of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._online = online

    @property
    def pod_id(self):
        """Gets the pod_id of this RowForListPodOutput.  # noqa: E501


        :return: The pod_id of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._pod_id

    @pod_id.setter
    def pod_id(self, pod_id):
        """Sets the pod_id of this RowForListPodOutput.


        :param pod_id: The pod_id of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._pod_id = pod_id

    @property
    def pod_name(self):
        """Gets the pod_name of this RowForListPodOutput.  # noqa: E501


        :return: The pod_name of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._pod_name

    @pod_name.setter
    def pod_name(self, pod_name):
        """Sets the pod_name of this RowForListPodOutput.


        :param pod_name: The pod_name of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._pod_name = pod_name

    @property
    def port_mapping_rule_list(self):
        """Gets the port_mapping_rule_list of this RowForListPodOutput.  # noqa: E501


        :return: The port_mapping_rule_list of this RowForListPodOutput.  # noqa: E501
        :rtype: list[PortMappingRuleListForListPodOutput]
        """
        return self._port_mapping_rule_list

    @port_mapping_rule_list.setter
    def port_mapping_rule_list(self, port_mapping_rule_list):
        """Sets the port_mapping_rule_list of this RowForListPodOutput.


        :param port_mapping_rule_list: The port_mapping_rule_list of this RowForListPodOutput.  # noqa: E501
        :type: list[PortMappingRuleListForListPodOutput]
        """

        self._port_mapping_rule_list = port_mapping_rule_list

    @property
    def product_id(self):
        """Gets the product_id of this RowForListPodOutput.  # noqa: E501


        :return: The product_id of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_id

    @product_id.setter
    def product_id(self, product_id):
        """Sets the product_id of this RowForListPodOutput.


        :param product_id: The product_id of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._product_id = product_id

    @property
    def snatid(self):
        """Gets the snatid of this RowForListPodOutput.  # noqa: E501


        :return: The snatid of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._snatid

    @snatid.setter
    def snatid(self, snatid):
        """Sets the snatid of this RowForListPodOutput.


        :param snatid: The snatid of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._snatid = snatid

    @property
    def server_type_code(self):
        """Gets the server_type_code of this RowForListPodOutput.  # noqa: E501


        :return: The server_type_code of this RowForListPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_type_code

    @server_type_code.setter
    def server_type_code(self, server_type_code):
        """Sets the server_type_code of this RowForListPodOutput.


        :param server_type_code: The server_type_code of this RowForListPodOutput.  # noqa: E501
        :type: str
        """

        self._server_type_code = server_type_code

    @property
    def stream_status(self):
        """Gets the stream_status of this RowForListPodOutput.  # noqa: E501


        :return: The stream_status of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._stream_status

    @stream_status.setter
    def stream_status(self, stream_status):
        """Sets the stream_status of this RowForListPodOutput.


        :param stream_status: The stream_status of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._stream_status = stream_status

    @property
    def tag(self):
        """Gets the tag of this RowForListPodOutput.  # noqa: E501


        :return: The tag of this RowForListPodOutput.  # noqa: E501
        :rtype: TagForListPodOutput
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this RowForListPodOutput.


        :param tag: The tag of this RowForListPodOutput.  # noqa: E501
        :type: TagForListPodOutput
        """

        self._tag = tag

    @property
    def up_bandwidth_limit(self):
        """Gets the up_bandwidth_limit of this RowForListPodOutput.  # noqa: E501


        :return: The up_bandwidth_limit of this RowForListPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._up_bandwidth_limit

    @up_bandwidth_limit.setter
    def up_bandwidth_limit(self, up_bandwidth_limit):
        """Sets the up_bandwidth_limit of this RowForListPodOutput.


        :param up_bandwidth_limit: The up_bandwidth_limit of this RowForListPodOutput.  # noqa: E501
        :type: int
        """

        self._up_bandwidth_limit = up_bandwidth_limit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RowForListPodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RowForListPodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RowForListPodOutput):
            return True

        return self.to_dict() != other.to_dict()
