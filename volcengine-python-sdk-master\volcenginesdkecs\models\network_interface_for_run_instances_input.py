# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NetworkInterfaceForRunInstancesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ipv6_address_count': 'int',
        'primary_ip_address': 'str',
        'private_ip_addresses': 'list[str]',
        'security_group_ids': 'list[str]',
        'subnet_id': 'str'
    }

    attribute_map = {
        'ipv6_address_count': 'Ipv6AddressCount',
        'primary_ip_address': 'PrimaryIpAddress',
        'private_ip_addresses': 'PrivateIpAddresses',
        'security_group_ids': 'SecurityGroupIds',
        'subnet_id': 'SubnetId'
    }

    def __init__(self, ipv6_address_count=None, primary_ip_address=None, private_ip_addresses=None, security_group_ids=None, subnet_id=None, _configuration=None):  # noqa: E501
        """NetworkInterfaceForRunInstancesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ipv6_address_count = None
        self._primary_ip_address = None
        self._private_ip_addresses = None
        self._security_group_ids = None
        self._subnet_id = None
        self.discriminator = None

        if ipv6_address_count is not None:
            self.ipv6_address_count = ipv6_address_count
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if private_ip_addresses is not None:
            self.private_ip_addresses = private_ip_addresses
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        self.subnet_id = subnet_id

    @property
    def ipv6_address_count(self):
        """Gets the ipv6_address_count of this NetworkInterfaceForRunInstancesInput.  # noqa: E501


        :return: The ipv6_address_count of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :rtype: int
        """
        return self._ipv6_address_count

    @ipv6_address_count.setter
    def ipv6_address_count(self, ipv6_address_count):
        """Sets the ipv6_address_count of this NetworkInterfaceForRunInstancesInput.


        :param ipv6_address_count: The ipv6_address_count of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :type: int
        """

        self._ipv6_address_count = ipv6_address_count

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this NetworkInterfaceForRunInstancesInput.  # noqa: E501


        :return: The primary_ip_address of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this NetworkInterfaceForRunInstancesInput.


        :param primary_ip_address: The primary_ip_address of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def private_ip_addresses(self):
        """Gets the private_ip_addresses of this NetworkInterfaceForRunInstancesInput.  # noqa: E501


        :return: The private_ip_addresses of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._private_ip_addresses

    @private_ip_addresses.setter
    def private_ip_addresses(self, private_ip_addresses):
        """Sets the private_ip_addresses of this NetworkInterfaceForRunInstancesInput.


        :param private_ip_addresses: The private_ip_addresses of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :type: list[str]
        """

        self._private_ip_addresses = private_ip_addresses

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this NetworkInterfaceForRunInstancesInput.  # noqa: E501


        :return: The security_group_ids of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this NetworkInterfaceForRunInstancesInput.


        :param security_group_ids: The security_group_ids of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def subnet_id(self):
        """Gets the subnet_id of this NetworkInterfaceForRunInstancesInput.  # noqa: E501


        :return: The subnet_id of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this NetworkInterfaceForRunInstancesInput.


        :param subnet_id: The subnet_id of this NetworkInterfaceForRunInstancesInput.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NetworkInterfaceForRunInstancesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NetworkInterfaceForRunInstancesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NetworkInterfaceForRunInstancesInput):
            return True

        return self.to_dict() != other.to_dict()
