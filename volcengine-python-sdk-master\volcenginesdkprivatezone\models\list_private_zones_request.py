# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListPrivateZonesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'line_mode': 'int',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'recursion_mode': 'bool',
        'region': 'str',
        'search_mode': 'str',
        'tag_filters': 'list[TagFilterForListPrivateZonesInput]',
        'vpc_id': 'str',
        'vpc_trns': 'list[str]',
        'zids': 'list[int]',
        'zids_n': 'list[int]',
        'zone_name': 'str'
    }

    attribute_map = {
        'line_mode': 'LineMode',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'recursion_mode': 'RecursionMode',
        'region': 'Region',
        'search_mode': 'SearchMode',
        'tag_filters': 'TagFilters',
        'vpc_id': 'VpcID',
        'vpc_trns': 'VpcTrns',
        'zids': 'ZIDs',
        'zids_n': 'ZIDs.N',
        'zone_name': 'ZoneName'
    }

    def __init__(self, line_mode=None, page_number=None, page_size=None, project_name=None, recursion_mode=None, region=None, search_mode=None, tag_filters=None, vpc_id=None, vpc_trns=None, zids=None, zids_n=None, zone_name=None, _configuration=None):  # noqa: E501
        """ListPrivateZonesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._line_mode = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._recursion_mode = None
        self._region = None
        self._search_mode = None
        self._tag_filters = None
        self._vpc_id = None
        self._vpc_trns = None
        self._zids = None
        self._zids_n = None
        self._zone_name = None
        self.discriminator = None

        if line_mode is not None:
            self.line_mode = line_mode
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if recursion_mode is not None:
            self.recursion_mode = recursion_mode
        if region is not None:
            self.region = region
        if search_mode is not None:
            self.search_mode = search_mode
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if vpc_trns is not None:
            self.vpc_trns = vpc_trns
        if zids is not None:
            self.zids = zids
        if zids_n is not None:
            self.zids_n = zids_n
        if zone_name is not None:
            self.zone_name = zone_name

    @property
    def line_mode(self):
        """Gets the line_mode of this ListPrivateZonesRequest.  # noqa: E501


        :return: The line_mode of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: int
        """
        return self._line_mode

    @line_mode.setter
    def line_mode(self, line_mode):
        """Sets the line_mode of this ListPrivateZonesRequest.


        :param line_mode: The line_mode of this ListPrivateZonesRequest.  # noqa: E501
        :type: int
        """

        self._line_mode = line_mode

    @property
    def page_number(self):
        """Gets the page_number of this ListPrivateZonesRequest.  # noqa: E501


        :return: The page_number of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListPrivateZonesRequest.


        :param page_number: The page_number of this ListPrivateZonesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListPrivateZonesRequest.  # noqa: E501


        :return: The page_size of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListPrivateZonesRequest.


        :param page_size: The page_size of this ListPrivateZonesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListPrivateZonesRequest.  # noqa: E501


        :return: The project_name of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListPrivateZonesRequest.


        :param project_name: The project_name of this ListPrivateZonesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def recursion_mode(self):
        """Gets the recursion_mode of this ListPrivateZonesRequest.  # noqa: E501


        :return: The recursion_mode of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._recursion_mode

    @recursion_mode.setter
    def recursion_mode(self, recursion_mode):
        """Sets the recursion_mode of this ListPrivateZonesRequest.


        :param recursion_mode: The recursion_mode of this ListPrivateZonesRequest.  # noqa: E501
        :type: bool
        """

        self._recursion_mode = recursion_mode

    @property
    def region(self):
        """Gets the region of this ListPrivateZonesRequest.  # noqa: E501


        :return: The region of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ListPrivateZonesRequest.


        :param region: The region of this ListPrivateZonesRequest.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def search_mode(self):
        """Gets the search_mode of this ListPrivateZonesRequest.  # noqa: E501


        :return: The search_mode of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._search_mode

    @search_mode.setter
    def search_mode(self, search_mode):
        """Sets the search_mode of this ListPrivateZonesRequest.


        :param search_mode: The search_mode of this ListPrivateZonesRequest.  # noqa: E501
        :type: str
        """

        self._search_mode = search_mode

    @property
    def tag_filters(self):
        """Gets the tag_filters of this ListPrivateZonesRequest.  # noqa: E501


        :return: The tag_filters of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: list[TagFilterForListPrivateZonesInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this ListPrivateZonesRequest.


        :param tag_filters: The tag_filters of this ListPrivateZonesRequest.  # noqa: E501
        :type: list[TagFilterForListPrivateZonesInput]
        """

        self._tag_filters = tag_filters

    @property
    def vpc_id(self):
        """Gets the vpc_id of this ListPrivateZonesRequest.  # noqa: E501


        :return: The vpc_id of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this ListPrivateZonesRequest.


        :param vpc_id: The vpc_id of this ListPrivateZonesRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def vpc_trns(self):
        """Gets the vpc_trns of this ListPrivateZonesRequest.  # noqa: E501


        :return: The vpc_trns of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_trns

    @vpc_trns.setter
    def vpc_trns(self, vpc_trns):
        """Sets the vpc_trns of this ListPrivateZonesRequest.


        :param vpc_trns: The vpc_trns of this ListPrivateZonesRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpc_trns = vpc_trns

    @property
    def zids(self):
        """Gets the zids of this ListPrivateZonesRequest.  # noqa: E501


        :return: The zids of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._zids

    @zids.setter
    def zids(self, zids):
        """Sets the zids of this ListPrivateZonesRequest.


        :param zids: The zids of this ListPrivateZonesRequest.  # noqa: E501
        :type: list[int]
        """

        self._zids = zids

    @property
    def zids_n(self):
        """Gets the zids_n of this ListPrivateZonesRequest.  # noqa: E501


        :return: The zids_n of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._zids_n

    @zids_n.setter
    def zids_n(self, zids_n):
        """Sets the zids_n of this ListPrivateZonesRequest.


        :param zids_n: The zids_n of this ListPrivateZonesRequest.  # noqa: E501
        :type: list[int]
        """

        self._zids_n = zids_n

    @property
    def zone_name(self):
        """Gets the zone_name of this ListPrivateZonesRequest.  # noqa: E501


        :return: The zone_name of this ListPrivateZonesRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this ListPrivateZonesRequest.


        :param zone_name: The zone_name of this ListPrivateZonesRequest.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListPrivateZonesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListPrivateZonesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListPrivateZonesRequest):
            return True

        return self.to_dict() != other.to_dict()
