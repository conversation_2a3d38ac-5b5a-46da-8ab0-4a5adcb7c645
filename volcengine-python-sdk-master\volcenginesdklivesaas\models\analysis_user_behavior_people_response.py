# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AnalysisUserBehaviorPeopleResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'analysis_people': 'int',
        'total_people': 'int',
        'total_time': 'int'
    }

    attribute_map = {
        'analysis_people': 'AnalysisPeople',
        'total_people': 'TotalPeople',
        'total_time': 'TotalTime'
    }

    def __init__(self, analysis_people=None, total_people=None, total_time=None, _configuration=None):  # noqa: E501
        """AnalysisUserBehaviorPeopleResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._analysis_people = None
        self._total_people = None
        self._total_time = None
        self.discriminator = None

        if analysis_people is not None:
            self.analysis_people = analysis_people
        if total_people is not None:
            self.total_people = total_people
        if total_time is not None:
            self.total_time = total_time

    @property
    def analysis_people(self):
        """Gets the analysis_people of this AnalysisUserBehaviorPeopleResponse.  # noqa: E501


        :return: The analysis_people of this AnalysisUserBehaviorPeopleResponse.  # noqa: E501
        :rtype: int
        """
        return self._analysis_people

    @analysis_people.setter
    def analysis_people(self, analysis_people):
        """Sets the analysis_people of this AnalysisUserBehaviorPeopleResponse.


        :param analysis_people: The analysis_people of this AnalysisUserBehaviorPeopleResponse.  # noqa: E501
        :type: int
        """

        self._analysis_people = analysis_people

    @property
    def total_people(self):
        """Gets the total_people of this AnalysisUserBehaviorPeopleResponse.  # noqa: E501


        :return: The total_people of this AnalysisUserBehaviorPeopleResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_people

    @total_people.setter
    def total_people(self, total_people):
        """Sets the total_people of this AnalysisUserBehaviorPeopleResponse.


        :param total_people: The total_people of this AnalysisUserBehaviorPeopleResponse.  # noqa: E501
        :type: int
        """

        self._total_people = total_people

    @property
    def total_time(self):
        """Gets the total_time of this AnalysisUserBehaviorPeopleResponse.  # noqa: E501


        :return: The total_time of this AnalysisUserBehaviorPeopleResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_time

    @total_time.setter
    def total_time(self, total_time):
        """Sets the total_time of this AnalysisUserBehaviorPeopleResponse.


        :param total_time: The total_time of this AnalysisUserBehaviorPeopleResponse.  # noqa: E501
        :type: int
        """

        self._total_time = total_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AnalysisUserBehaviorPeopleResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AnalysisUserBehaviorPeopleResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AnalysisUserBehaviorPeopleResponse):
            return True

        return self.to_dict() != other.to_dict()
