# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataVolumeForUpdateNodePoolConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system': 'str',
        'mount_point': 'str',
        'size': 'int',
        'snapshot_id': 'str',
        'type': 'str'
    }

    attribute_map = {
        'file_system': 'FileSystem',
        'mount_point': 'MountPoint',
        'size': 'Size',
        'snapshot_id': 'SnapshotId',
        'type': 'Type'
    }

    def __init__(self, file_system=None, mount_point=None, size=None, snapshot_id=None, type=None, _configuration=None):  # noqa: E501
        """DataVolumeForUpdateNodePoolConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system = None
        self._mount_point = None
        self._size = None
        self._snapshot_id = None
        self._type = None
        self.discriminator = None

        if file_system is not None:
            self.file_system = file_system
        if mount_point is not None:
            self.mount_point = mount_point
        if size is not None:
            self.size = size
        if snapshot_id is not None:
            self.snapshot_id = snapshot_id
        if type is not None:
            self.type = type

    @property
    def file_system(self):
        """Gets the file_system of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The file_system of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._file_system

    @file_system.setter
    def file_system(self, file_system):
        """Sets the file_system of this DataVolumeForUpdateNodePoolConfigInput.


        :param file_system: The file_system of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Ext4", "Xfs"]  # noqa: E501
        if (self._configuration.client_side_validation and
                file_system not in allowed_values):
            raise ValueError(
                "Invalid value for `file_system` ({0}), must be one of {1}"  # noqa: E501
                .format(file_system, allowed_values)
            )

        self._file_system = file_system

    @property
    def mount_point(self):
        """Gets the mount_point of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The mount_point of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._mount_point

    @mount_point.setter
    def mount_point(self, mount_point):
        """Sets the mount_point of this DataVolumeForUpdateNodePoolConfigInput.


        :param mount_point: The mount_point of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :type: str
        """

        self._mount_point = mount_point

    @property
    def size(self):
        """Gets the size of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The size of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this DataVolumeForUpdateNodePoolConfigInput.


        :param size: The size of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def snapshot_id(self):
        """Gets the snapshot_id of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The snapshot_id of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._snapshot_id

    @snapshot_id.setter
    def snapshot_id(self, snapshot_id):
        """Sets the snapshot_id of this DataVolumeForUpdateNodePoolConfigInput.


        :param snapshot_id: The snapshot_id of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :type: str
        """

        self._snapshot_id = snapshot_id

    @property
    def type(self):
        """Gets the type of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The type of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DataVolumeForUpdateNodePoolConfigInput.


        :param type: The type of this DataVolumeForUpdateNodePoolConfigInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["ESSD_PL0", "ESSD_FlexPL"]  # noqa: E501
        if (self._configuration.client_side_validation and
                type not in allowed_values):
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataVolumeForUpdateNodePoolConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataVolumeForUpdateNodePoolConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataVolumeForUpdateNodePoolConfigInput):
            return True

        return self.to_dict() != other.to_dict()
