# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListServicesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'created_by': 'str',
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'status': 'StatusForListServicesOutput',
        'update_time': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'created_by': 'CreatedBy',
        'description': 'Description',
        'id': 'Id',
        'name': 'Name',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, create_time=None, created_by=None, description=None, id=None, name=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListServicesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._created_by = None
        self._description = None
        self._id = None
        self._name = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if created_by is not None:
            self.created_by = created_by
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListServicesOutput.  # noqa: E501


        :return: The create_time of this ItemForListServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListServicesOutput.


        :param create_time: The create_time of this ItemForListServicesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def created_by(self):
        """Gets the created_by of this ItemForListServicesOutput.  # noqa: E501


        :return: The created_by of this ItemForListServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_by

    @created_by.setter
    def created_by(self, created_by):
        """Sets the created_by of this ItemForListServicesOutput.


        :param created_by: The created_by of this ItemForListServicesOutput.  # noqa: E501
        :type: str
        """

        self._created_by = created_by

    @property
    def description(self):
        """Gets the description of this ItemForListServicesOutput.  # noqa: E501


        :return: The description of this ItemForListServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListServicesOutput.


        :param description: The description of this ItemForListServicesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this ItemForListServicesOutput.  # noqa: E501


        :return: The id of this ItemForListServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListServicesOutput.


        :param id: The id of this ItemForListServicesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this ItemForListServicesOutput.  # noqa: E501


        :return: The name of this ItemForListServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListServicesOutput.


        :param name: The name of this ItemForListServicesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def status(self):
        """Gets the status of this ItemForListServicesOutput.  # noqa: E501


        :return: The status of this ItemForListServicesOutput.  # noqa: E501
        :rtype: StatusForListServicesOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListServicesOutput.


        :param status: The status of this ItemForListServicesOutput.  # noqa: E501
        :type: StatusForListServicesOutput
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListServicesOutput.  # noqa: E501


        :return: The update_time of this ItemForListServicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListServicesOutput.


        :param update_time: The update_time of this ItemForListServicesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListServicesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListServicesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListServicesOutput):
            return True

        return self.to_dict() != other.to_dict()
