# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FixedRewardPointConfigForGetActivityRedPacketOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'reward_point_amount': 'int',
        'reward_point_number': 'int'
    }

    attribute_map = {
        'reward_point_amount': 'RewardPointAmount',
        'reward_point_number': 'RewardPointNumber'
    }

    def __init__(self, reward_point_amount=None, reward_point_number=None, _configuration=None):  # noqa: E501
        """FixedRewardPointConfigForGetActivityRedPacketOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._reward_point_amount = None
        self._reward_point_number = None
        self.discriminator = None

        if reward_point_amount is not None:
            self.reward_point_amount = reward_point_amount
        if reward_point_number is not None:
            self.reward_point_number = reward_point_number

    @property
    def reward_point_amount(self):
        """Gets the reward_point_amount of this FixedRewardPointConfigForGetActivityRedPacketOutput.  # noqa: E501


        :return: The reward_point_amount of this FixedRewardPointConfigForGetActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._reward_point_amount

    @reward_point_amount.setter
    def reward_point_amount(self, reward_point_amount):
        """Sets the reward_point_amount of this FixedRewardPointConfigForGetActivityRedPacketOutput.


        :param reward_point_amount: The reward_point_amount of this FixedRewardPointConfigForGetActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._reward_point_amount = reward_point_amount

    @property
    def reward_point_number(self):
        """Gets the reward_point_number of this FixedRewardPointConfigForGetActivityRedPacketOutput.  # noqa: E501


        :return: The reward_point_number of this FixedRewardPointConfigForGetActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._reward_point_number

    @reward_point_number.setter
    def reward_point_number(self, reward_point_number):
        """Sets the reward_point_number of this FixedRewardPointConfigForGetActivityRedPacketOutput.


        :param reward_point_number: The reward_point_number of this FixedRewardPointConfigForGetActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._reward_point_number = reward_point_number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FixedRewardPointConfigForGetActivityRedPacketOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FixedRewardPointConfigForGetActivityRedPacketOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FixedRewardPointConfigForGetActivityRedPacketOutput):
            return True

        return self.to_dict() != other.to_dict()
