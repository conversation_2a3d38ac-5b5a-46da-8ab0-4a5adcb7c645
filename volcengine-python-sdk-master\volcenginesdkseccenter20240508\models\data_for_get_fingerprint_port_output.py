# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetFingerprintPortOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_tags': 'list[str]',
        'cmdline': 'str',
        'comm': 'str',
        'container_id': 'str',
        'container_name': 'str',
        'dip': 'str',
        'dport': 'str',
        'eip_address': 'str',
        'exe': 'str',
        'family': 'str',
        'hostname': 'str',
        'id': 'str',
        'inode': 'str',
        'interface': 'str',
        'pid': 'str',
        'primary_ip_address': 'str',
        'sip': 'str',
        'sport': 'str',
        'start_time': 'int',
        'state': 'str',
        'type': 'int',
        'uid': 'str',
        'update_time': 'int',
        'username': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_tags': 'AgentTags',
        'cmdline': 'Cmdline',
        'comm': 'Comm',
        'container_id': 'ContainerID',
        'container_name': 'ContainerName',
        'dip': 'Dip',
        'dport': 'Dport',
        'eip_address': 'EipAddress',
        'exe': 'Exe',
        'family': 'Family',
        'hostname': 'Hostname',
        'id': 'ID',
        'inode': 'Inode',
        'interface': 'Interface',
        'pid': 'Pid',
        'primary_ip_address': 'PrimaryIpAddress',
        'sip': 'Sip',
        'sport': 'Sport',
        'start_time': 'StartTime',
        'state': 'State',
        'type': 'Type',
        'uid': 'Uid',
        'update_time': 'UpdateTime',
        'username': 'Username'
    }

    def __init__(self, agent_id=None, agent_tags=None, cmdline=None, comm=None, container_id=None, container_name=None, dip=None, dport=None, eip_address=None, exe=None, family=None, hostname=None, id=None, inode=None, interface=None, pid=None, primary_ip_address=None, sip=None, sport=None, start_time=None, state=None, type=None, uid=None, update_time=None, username=None, _configuration=None):  # noqa: E501
        """DataForGetFingerprintPortOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_tags = None
        self._cmdline = None
        self._comm = None
        self._container_id = None
        self._container_name = None
        self._dip = None
        self._dport = None
        self._eip_address = None
        self._exe = None
        self._family = None
        self._hostname = None
        self._id = None
        self._inode = None
        self._interface = None
        self._pid = None
        self._primary_ip_address = None
        self._sip = None
        self._sport = None
        self._start_time = None
        self._state = None
        self._type = None
        self._uid = None
        self._update_time = None
        self._username = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if cmdline is not None:
            self.cmdline = cmdline
        if comm is not None:
            self.comm = comm
        if container_id is not None:
            self.container_id = container_id
        if container_name is not None:
            self.container_name = container_name
        if dip is not None:
            self.dip = dip
        if dport is not None:
            self.dport = dport
        if eip_address is not None:
            self.eip_address = eip_address
        if exe is not None:
            self.exe = exe
        if family is not None:
            self.family = family
        if hostname is not None:
            self.hostname = hostname
        if id is not None:
            self.id = id
        if inode is not None:
            self.inode = inode
        if interface is not None:
            self.interface = interface
        if pid is not None:
            self.pid = pid
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if sip is not None:
            self.sip = sip
        if sport is not None:
            self.sport = sport
        if start_time is not None:
            self.start_time = start_time
        if state is not None:
            self.state = state
        if type is not None:
            self.type = type
        if uid is not None:
            self.uid = uid
        if update_time is not None:
            self.update_time = update_time
        if username is not None:
            self.username = username

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The agent_id of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForGetFingerprintPortOutput.


        :param agent_id: The agent_id of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The agent_tags of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this DataForGetFingerprintPortOutput.


        :param agent_tags: The agent_tags of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def cmdline(self):
        """Gets the cmdline of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The cmdline of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this DataForGetFingerprintPortOutput.


        :param cmdline: The cmdline of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def comm(self):
        """Gets the comm of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The comm of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._comm

    @comm.setter
    def comm(self, comm):
        """Sets the comm of this DataForGetFingerprintPortOutput.


        :param comm: The comm of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._comm = comm

    @property
    def container_id(self):
        """Gets the container_id of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The container_id of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_id

    @container_id.setter
    def container_id(self, container_id):
        """Sets the container_id of this DataForGetFingerprintPortOutput.


        :param container_id: The container_id of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._container_id = container_id

    @property
    def container_name(self):
        """Gets the container_name of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The container_name of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_name

    @container_name.setter
    def container_name(self, container_name):
        """Sets the container_name of this DataForGetFingerprintPortOutput.


        :param container_name: The container_name of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._container_name = container_name

    @property
    def dip(self):
        """Gets the dip of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The dip of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._dip

    @dip.setter
    def dip(self, dip):
        """Sets the dip of this DataForGetFingerprintPortOutput.


        :param dip: The dip of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._dip = dip

    @property
    def dport(self):
        """Gets the dport of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The dport of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._dport

    @dport.setter
    def dport(self, dport):
        """Sets the dport of this DataForGetFingerprintPortOutput.


        :param dport: The dport of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._dport = dport

    @property
    def eip_address(self):
        """Gets the eip_address of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The eip_address of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this DataForGetFingerprintPortOutput.


        :param eip_address: The eip_address of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def exe(self):
        """Gets the exe of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The exe of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this DataForGetFingerprintPortOutput.


        :param exe: The exe of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def family(self):
        """Gets the family of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The family of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._family

    @family.setter
    def family(self, family):
        """Sets the family of this DataForGetFingerprintPortOutput.


        :param family: The family of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._family = family

    @property
    def hostname(self):
        """Gets the hostname of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The hostname of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this DataForGetFingerprintPortOutput.


        :param hostname: The hostname of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def id(self):
        """Gets the id of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The id of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForGetFingerprintPortOutput.


        :param id: The id of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def inode(self):
        """Gets the inode of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The inode of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._inode

    @inode.setter
    def inode(self, inode):
        """Sets the inode of this DataForGetFingerprintPortOutput.


        :param inode: The inode of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._inode = inode

    @property
    def interface(self):
        """Gets the interface of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The interface of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._interface

    @interface.setter
    def interface(self, interface):
        """Sets the interface of this DataForGetFingerprintPortOutput.


        :param interface: The interface of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._interface = interface

    @property
    def pid(self):
        """Gets the pid of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The pid of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this DataForGetFingerprintPortOutput.


        :param pid: The pid of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The primary_ip_address of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this DataForGetFingerprintPortOutput.


        :param primary_ip_address: The primary_ip_address of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def sip(self):
        """Gets the sip of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The sip of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._sip

    @sip.setter
    def sip(self, sip):
        """Sets the sip of this DataForGetFingerprintPortOutput.


        :param sip: The sip of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._sip = sip

    @property
    def sport(self):
        """Gets the sport of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The sport of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._sport

    @sport.setter
    def sport(self, sport):
        """Sets the sport of this DataForGetFingerprintPortOutput.


        :param sport: The sport of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._sport = sport

    @property
    def start_time(self):
        """Gets the start_time of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The start_time of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForGetFingerprintPortOutput.


        :param start_time: The start_time of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def state(self):
        """Gets the state of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The state of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this DataForGetFingerprintPortOutput.


        :param state: The state of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def type(self):
        """Gets the type of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The type of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: int
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DataForGetFingerprintPortOutput.


        :param type: The type of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: int
        """

        self._type = type

    @property
    def uid(self):
        """Gets the uid of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The uid of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this DataForGetFingerprintPortOutput.


        :param uid: The uid of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    @property
    def update_time(self):
        """Gets the update_time of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The update_time of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForGetFingerprintPortOutput.


        :param update_time: The update_time of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def username(self):
        """Gets the username of this DataForGetFingerprintPortOutput.  # noqa: E501


        :return: The username of this DataForGetFingerprintPortOutput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this DataForGetFingerprintPortOutput.


        :param username: The username of this DataForGetFingerprintPortOutput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetFingerprintPortOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetFingerprintPortOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetFingerprintPortOutput):
            return True

        return self.to_dict() != other.to_dict()
