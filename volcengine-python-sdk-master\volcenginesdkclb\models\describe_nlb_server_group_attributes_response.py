# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNLBServerGroupAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'address_ip_version': 'str',
        'any_port_enabled': 'bool',
        'bypass_security_group_enabled': 'bool',
        'connection_drain_enabled': 'bool',
        'connection_drain_timeout': 'int',
        'create_time': 'str',
        'description': 'str',
        'health_check': 'HealthCheckForDescribeNLBServerGroupAttributesOutput',
        'ip_address_version': 'str',
        'preserve_client_ip_enabled': 'bool',
        'project_name': 'str',
        'protocol': 'str',
        'proxy_protocol_type': 'str',
        'related_load_balancer_ids': 'list[str]',
        'request_id': 'str',
        'scheduler': 'str',
        'server_count': 'int',
        'server_group_id': 'str',
        'server_group_name': 'str',
        'servers': 'list[ServerForDescribeNLBServerGroupAttributesOutput]',
        'session_persistence_enabled': 'bool',
        'session_persistence_timeout': 'int',
        'status': 'str',
        'tags': 'list[TagForDescribeNLBServerGroupAttributesOutput]',
        'timestamp_remove_enabled': 'bool',
        'type': 'str',
        'update_time': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'address_ip_version': 'AddressIpVersion',
        'any_port_enabled': 'AnyPortEnabled',
        'bypass_security_group_enabled': 'BypassSecurityGroupEnabled',
        'connection_drain_enabled': 'ConnectionDrainEnabled',
        'connection_drain_timeout': 'ConnectionDrainTimeout',
        'create_time': 'CreateTime',
        'description': 'Description',
        'health_check': 'HealthCheck',
        'ip_address_version': 'IpAddressVersion',
        'preserve_client_ip_enabled': 'PreserveClientIpEnabled',
        'project_name': 'ProjectName',
        'protocol': 'Protocol',
        'proxy_protocol_type': 'ProxyProtocolType',
        'related_load_balancer_ids': 'RelatedLoadBalancerIds',
        'request_id': 'RequestId',
        'scheduler': 'Scheduler',
        'server_count': 'ServerCount',
        'server_group_id': 'ServerGroupId',
        'server_group_name': 'ServerGroupName',
        'servers': 'Servers',
        'session_persistence_enabled': 'SessionPersistenceEnabled',
        'session_persistence_timeout': 'SessionPersistenceTimeout',
        'status': 'Status',
        'tags': 'Tags',
        'timestamp_remove_enabled': 'TimestampRemoveEnabled',
        'type': 'Type',
        'update_time': 'UpdateTime',
        'vpc_id': 'VpcId'
    }

    def __init__(self, account_id=None, address_ip_version=None, any_port_enabled=None, bypass_security_group_enabled=None, connection_drain_enabled=None, connection_drain_timeout=None, create_time=None, description=None, health_check=None, ip_address_version=None, preserve_client_ip_enabled=None, project_name=None, protocol=None, proxy_protocol_type=None, related_load_balancer_ids=None, request_id=None, scheduler=None, server_count=None, server_group_id=None, server_group_name=None, servers=None, session_persistence_enabled=None, session_persistence_timeout=None, status=None, tags=None, timestamp_remove_enabled=None, type=None, update_time=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DescribeNLBServerGroupAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._address_ip_version = None
        self._any_port_enabled = None
        self._bypass_security_group_enabled = None
        self._connection_drain_enabled = None
        self._connection_drain_timeout = None
        self._create_time = None
        self._description = None
        self._health_check = None
        self._ip_address_version = None
        self._preserve_client_ip_enabled = None
        self._project_name = None
        self._protocol = None
        self._proxy_protocol_type = None
        self._related_load_balancer_ids = None
        self._request_id = None
        self._scheduler = None
        self._server_count = None
        self._server_group_id = None
        self._server_group_name = None
        self._servers = None
        self._session_persistence_enabled = None
        self._session_persistence_timeout = None
        self._status = None
        self._tags = None
        self._timestamp_remove_enabled = None
        self._type = None
        self._update_time = None
        self._vpc_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if address_ip_version is not None:
            self.address_ip_version = address_ip_version
        if any_port_enabled is not None:
            self.any_port_enabled = any_port_enabled
        if bypass_security_group_enabled is not None:
            self.bypass_security_group_enabled = bypass_security_group_enabled
        if connection_drain_enabled is not None:
            self.connection_drain_enabled = connection_drain_enabled
        if connection_drain_timeout is not None:
            self.connection_drain_timeout = connection_drain_timeout
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if health_check is not None:
            self.health_check = health_check
        if ip_address_version is not None:
            self.ip_address_version = ip_address_version
        if preserve_client_ip_enabled is not None:
            self.preserve_client_ip_enabled = preserve_client_ip_enabled
        if project_name is not None:
            self.project_name = project_name
        if protocol is not None:
            self.protocol = protocol
        if proxy_protocol_type is not None:
            self.proxy_protocol_type = proxy_protocol_type
        if related_load_balancer_ids is not None:
            self.related_load_balancer_ids = related_load_balancer_ids
        if request_id is not None:
            self.request_id = request_id
        if scheduler is not None:
            self.scheduler = scheduler
        if server_count is not None:
            self.server_count = server_count
        if server_group_id is not None:
            self.server_group_id = server_group_id
        if server_group_name is not None:
            self.server_group_name = server_group_name
        if servers is not None:
            self.servers = servers
        if session_persistence_enabled is not None:
            self.session_persistence_enabled = session_persistence_enabled
        if session_persistence_timeout is not None:
            self.session_persistence_timeout = session_persistence_timeout
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if timestamp_remove_enabled is not None:
            self.timestamp_remove_enabled = timestamp_remove_enabled
        if type is not None:
            self.type = type
        if update_time is not None:
            self.update_time = update_time
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def account_id(self):
        """Gets the account_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The account_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeNLBServerGroupAttributesResponse.


        :param account_id: The account_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def address_ip_version(self):
        """Gets the address_ip_version of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The address_ip_version of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._address_ip_version

    @address_ip_version.setter
    def address_ip_version(self, address_ip_version):
        """Sets the address_ip_version of this DescribeNLBServerGroupAttributesResponse.


        :param address_ip_version: The address_ip_version of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._address_ip_version = address_ip_version

    @property
    def any_port_enabled(self):
        """Gets the any_port_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The any_port_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._any_port_enabled

    @any_port_enabled.setter
    def any_port_enabled(self, any_port_enabled):
        """Sets the any_port_enabled of this DescribeNLBServerGroupAttributesResponse.


        :param any_port_enabled: The any_port_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._any_port_enabled = any_port_enabled

    @property
    def bypass_security_group_enabled(self):
        """Gets the bypass_security_group_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The bypass_security_group_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._bypass_security_group_enabled

    @bypass_security_group_enabled.setter
    def bypass_security_group_enabled(self, bypass_security_group_enabled):
        """Sets the bypass_security_group_enabled of this DescribeNLBServerGroupAttributesResponse.


        :param bypass_security_group_enabled: The bypass_security_group_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._bypass_security_group_enabled = bypass_security_group_enabled

    @property
    def connection_drain_enabled(self):
        """Gets the connection_drain_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The connection_drain_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._connection_drain_enabled

    @connection_drain_enabled.setter
    def connection_drain_enabled(self, connection_drain_enabled):
        """Sets the connection_drain_enabled of this DescribeNLBServerGroupAttributesResponse.


        :param connection_drain_enabled: The connection_drain_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._connection_drain_enabled = connection_drain_enabled

    @property
    def connection_drain_timeout(self):
        """Gets the connection_drain_timeout of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The connection_drain_timeout of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._connection_drain_timeout

    @connection_drain_timeout.setter
    def connection_drain_timeout(self, connection_drain_timeout):
        """Sets the connection_drain_timeout of this DescribeNLBServerGroupAttributesResponse.


        :param connection_drain_timeout: The connection_drain_timeout of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: int
        """

        self._connection_drain_timeout = connection_drain_timeout

    @property
    def create_time(self):
        """Gets the create_time of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The create_time of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeNLBServerGroupAttributesResponse.


        :param create_time: The create_time of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The description of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeNLBServerGroupAttributesResponse.


        :param description: The description of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def health_check(self):
        """Gets the health_check of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The health_check of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: HealthCheckForDescribeNLBServerGroupAttributesOutput
        """
        return self._health_check

    @health_check.setter
    def health_check(self, health_check):
        """Sets the health_check of this DescribeNLBServerGroupAttributesResponse.


        :param health_check: The health_check of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: HealthCheckForDescribeNLBServerGroupAttributesOutput
        """

        self._health_check = health_check

    @property
    def ip_address_version(self):
        """Gets the ip_address_version of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The ip_address_version of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._ip_address_version

    @ip_address_version.setter
    def ip_address_version(self, ip_address_version):
        """Sets the ip_address_version of this DescribeNLBServerGroupAttributesResponse.


        :param ip_address_version: The ip_address_version of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._ip_address_version = ip_address_version

    @property
    def preserve_client_ip_enabled(self):
        """Gets the preserve_client_ip_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The preserve_client_ip_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._preserve_client_ip_enabled

    @preserve_client_ip_enabled.setter
    def preserve_client_ip_enabled(self, preserve_client_ip_enabled):
        """Sets the preserve_client_ip_enabled of this DescribeNLBServerGroupAttributesResponse.


        :param preserve_client_ip_enabled: The preserve_client_ip_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._preserve_client_ip_enabled = preserve_client_ip_enabled

    @property
    def project_name(self):
        """Gets the project_name of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The project_name of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeNLBServerGroupAttributesResponse.


        :param project_name: The project_name of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def protocol(self):
        """Gets the protocol of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The protocol of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this DescribeNLBServerGroupAttributesResponse.


        :param protocol: The protocol of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def proxy_protocol_type(self):
        """Gets the proxy_protocol_type of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The proxy_protocol_type of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._proxy_protocol_type

    @proxy_protocol_type.setter
    def proxy_protocol_type(self, proxy_protocol_type):
        """Sets the proxy_protocol_type of this DescribeNLBServerGroupAttributesResponse.


        :param proxy_protocol_type: The proxy_protocol_type of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._proxy_protocol_type = proxy_protocol_type

    @property
    def related_load_balancer_ids(self):
        """Gets the related_load_balancer_ids of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The related_load_balancer_ids of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._related_load_balancer_ids

    @related_load_balancer_ids.setter
    def related_load_balancer_ids(self, related_load_balancer_ids):
        """Sets the related_load_balancer_ids of this DescribeNLBServerGroupAttributesResponse.


        :param related_load_balancer_ids: The related_load_balancer_ids of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: list[str]
        """

        self._related_load_balancer_ids = related_load_balancer_ids

    @property
    def request_id(self):
        """Gets the request_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeNLBServerGroupAttributesResponse.


        :param request_id: The request_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def scheduler(self):
        """Gets the scheduler of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The scheduler of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._scheduler

    @scheduler.setter
    def scheduler(self, scheduler):
        """Sets the scheduler of this DescribeNLBServerGroupAttributesResponse.


        :param scheduler: The scheduler of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._scheduler = scheduler

    @property
    def server_count(self):
        """Gets the server_count of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The server_count of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._server_count

    @server_count.setter
    def server_count(self, server_count):
        """Sets the server_count of this DescribeNLBServerGroupAttributesResponse.


        :param server_count: The server_count of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: int
        """

        self._server_count = server_count

    @property
    def server_group_id(self):
        """Gets the server_group_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The server_group_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this DescribeNLBServerGroupAttributesResponse.


        :param server_group_id: The server_group_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    @property
    def server_group_name(self):
        """Gets the server_group_name of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The server_group_name of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._server_group_name

    @server_group_name.setter
    def server_group_name(self, server_group_name):
        """Sets the server_group_name of this DescribeNLBServerGroupAttributesResponse.


        :param server_group_name: The server_group_name of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._server_group_name = server_group_name

    @property
    def servers(self):
        """Gets the servers of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The servers of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: list[ServerForDescribeNLBServerGroupAttributesOutput]
        """
        return self._servers

    @servers.setter
    def servers(self, servers):
        """Sets the servers of this DescribeNLBServerGroupAttributesResponse.


        :param servers: The servers of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: list[ServerForDescribeNLBServerGroupAttributesOutput]
        """

        self._servers = servers

    @property
    def session_persistence_enabled(self):
        """Gets the session_persistence_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The session_persistence_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._session_persistence_enabled

    @session_persistence_enabled.setter
    def session_persistence_enabled(self, session_persistence_enabled):
        """Sets the session_persistence_enabled of this DescribeNLBServerGroupAttributesResponse.


        :param session_persistence_enabled: The session_persistence_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._session_persistence_enabled = session_persistence_enabled

    @property
    def session_persistence_timeout(self):
        """Gets the session_persistence_timeout of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The session_persistence_timeout of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._session_persistence_timeout

    @session_persistence_timeout.setter
    def session_persistence_timeout(self, session_persistence_timeout):
        """Sets the session_persistence_timeout of this DescribeNLBServerGroupAttributesResponse.


        :param session_persistence_timeout: The session_persistence_timeout of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: int
        """

        self._session_persistence_timeout = session_persistence_timeout

    @property
    def status(self):
        """Gets the status of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The status of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeNLBServerGroupAttributesResponse.


        :param status: The status of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The tags of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: list[TagForDescribeNLBServerGroupAttributesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DescribeNLBServerGroupAttributesResponse.


        :param tags: The tags of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: list[TagForDescribeNLBServerGroupAttributesOutput]
        """

        self._tags = tags

    @property
    def timestamp_remove_enabled(self):
        """Gets the timestamp_remove_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The timestamp_remove_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._timestamp_remove_enabled

    @timestamp_remove_enabled.setter
    def timestamp_remove_enabled(self, timestamp_remove_enabled):
        """Sets the timestamp_remove_enabled of this DescribeNLBServerGroupAttributesResponse.


        :param timestamp_remove_enabled: The timestamp_remove_enabled of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._timestamp_remove_enabled = timestamp_remove_enabled

    @property
    def type(self):
        """Gets the type of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The type of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DescribeNLBServerGroupAttributesResponse.


        :param type: The type of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def update_time(self):
        """Gets the update_time of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeNLBServerGroupAttributesResponse.


        :param update_time: The update_time of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501


        :return: The vpc_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeNLBServerGroupAttributesResponse.


        :param vpc_id: The vpc_id of this DescribeNLBServerGroupAttributesResponse.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNLBServerGroupAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNLBServerGroupAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNLBServerGroupAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
