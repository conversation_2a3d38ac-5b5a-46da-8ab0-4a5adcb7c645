# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVirusTaskStatisticsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'fininshed_task_num': 'int',
        'last_scan_time': 'int',
        'running_task_num': 'int'
    }

    attribute_map = {
        'fininshed_task_num': 'FininshedTaskNum',
        'last_scan_time': 'LastScanTime',
        'running_task_num': 'RunningTaskNum'
    }

    def __init__(self, fininshed_task_num=None, last_scan_time=None, running_task_num=None, _configuration=None):  # noqa: E501
        """GetVirusTaskStatisticsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._fininshed_task_num = None
        self._last_scan_time = None
        self._running_task_num = None
        self.discriminator = None

        if fininshed_task_num is not None:
            self.fininshed_task_num = fininshed_task_num
        if last_scan_time is not None:
            self.last_scan_time = last_scan_time
        if running_task_num is not None:
            self.running_task_num = running_task_num

    @property
    def fininshed_task_num(self):
        """Gets the fininshed_task_num of this GetVirusTaskStatisticsResponse.  # noqa: E501


        :return: The fininshed_task_num of this GetVirusTaskStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._fininshed_task_num

    @fininshed_task_num.setter
    def fininshed_task_num(self, fininshed_task_num):
        """Sets the fininshed_task_num of this GetVirusTaskStatisticsResponse.


        :param fininshed_task_num: The fininshed_task_num of this GetVirusTaskStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._fininshed_task_num = fininshed_task_num

    @property
    def last_scan_time(self):
        """Gets the last_scan_time of this GetVirusTaskStatisticsResponse.  # noqa: E501


        :return: The last_scan_time of this GetVirusTaskStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._last_scan_time

    @last_scan_time.setter
    def last_scan_time(self, last_scan_time):
        """Sets the last_scan_time of this GetVirusTaskStatisticsResponse.


        :param last_scan_time: The last_scan_time of this GetVirusTaskStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._last_scan_time = last_scan_time

    @property
    def running_task_num(self):
        """Gets the running_task_num of this GetVirusTaskStatisticsResponse.  # noqa: E501


        :return: The running_task_num of this GetVirusTaskStatisticsResponse.  # noqa: E501
        :rtype: int
        """
        return self._running_task_num

    @running_task_num.setter
    def running_task_num(self, running_task_num):
        """Sets the running_task_num of this GetVirusTaskStatisticsResponse.


        :param running_task_num: The running_task_num of this GetVirusTaskStatisticsResponse.  # noqa: E501
        :type: int
        """

        self._running_task_num = running_task_num

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVirusTaskStatisticsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVirusTaskStatisticsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVirusTaskStatisticsResponse):
            return True

        return self.to_dict() != other.to_dict()
