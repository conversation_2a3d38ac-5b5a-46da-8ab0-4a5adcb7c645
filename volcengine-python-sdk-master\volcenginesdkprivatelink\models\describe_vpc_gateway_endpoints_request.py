# coding: utf-8

"""
    privatelink

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeVpcGatewayEndpointsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'endpoint_ids': 'list[str]',
        'endpoint_name': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'tag_filters': 'list[TagFilterForDescribeVpcGatewayEndpointsInput]',
        'vpc_id': 'str'
    }

    attribute_map = {
        'endpoint_ids': 'EndpointIds',
        'endpoint_name': 'EndpointName',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'tag_filters': 'TagFilters',
        'vpc_id': 'VpcId'
    }

    def __init__(self, endpoint_ids=None, endpoint_name=None, page_number=None, page_size=None, project_name=None, tag_filters=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DescribeVpcGatewayEndpointsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._endpoint_ids = None
        self._endpoint_name = None
        self._page_number = None
        self._page_size = None
        self._project_name = None
        self._tag_filters = None
        self._vpc_id = None
        self.discriminator = None

        if endpoint_ids is not None:
            self.endpoint_ids = endpoint_ids
        if endpoint_name is not None:
            self.endpoint_name = endpoint_name
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if tag_filters is not None:
            self.tag_filters = tag_filters
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def endpoint_ids(self):
        """Gets the endpoint_ids of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501


        :return: The endpoint_ids of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._endpoint_ids

    @endpoint_ids.setter
    def endpoint_ids(self, endpoint_ids):
        """Sets the endpoint_ids of this DescribeVpcGatewayEndpointsRequest.


        :param endpoint_ids: The endpoint_ids of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :type: list[str]
        """

        self._endpoint_ids = endpoint_ids

    @property
    def endpoint_name(self):
        """Gets the endpoint_name of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501


        :return: The endpoint_name of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_name

    @endpoint_name.setter
    def endpoint_name(self, endpoint_name):
        """Sets the endpoint_name of this DescribeVpcGatewayEndpointsRequest.


        :param endpoint_name: The endpoint_name of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :type: str
        """

        self._endpoint_name = endpoint_name

    @property
    def page_number(self):
        """Gets the page_number of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501


        :return: The page_number of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeVpcGatewayEndpointsRequest.


        :param page_number: The page_number of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501


        :return: The page_size of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeVpcGatewayEndpointsRequest.


        :param page_size: The page_size of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501


        :return: The project_name of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeVpcGatewayEndpointsRequest.


        :param project_name: The project_name of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeVpcGatewayEndpointsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeVpcGatewayEndpointsRequest.


        :param tag_filters: The tag_filters of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeVpcGatewayEndpointsInput]
        """

        self._tag_filters = tag_filters

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501


        :return: The vpc_id of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeVpcGatewayEndpointsRequest.


        :param vpc_id: The vpc_id of this DescribeVpcGatewayEndpointsRequest.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeVpcGatewayEndpointsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeVpcGatewayEndpointsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeVpcGatewayEndpointsRequest):
            return True

        return self.to_dict() != other.to_dict()
