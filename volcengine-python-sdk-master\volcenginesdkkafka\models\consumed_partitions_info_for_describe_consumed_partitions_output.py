# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accumulation': 'int',
        'consumed_client': 'str',
        'consumed_offset': 'int',
        'end_offset': 'int',
        'partition_id': 'int',
        'start_offset': 'int'
    }

    attribute_map = {
        'accumulation': 'Accumulation',
        'consumed_client': 'ConsumedClient',
        'consumed_offset': 'ConsumedOffset',
        'end_offset': 'EndOffset',
        'partition_id': 'PartitionId',
        'start_offset': 'StartOffset'
    }

    def __init__(self, accumulation=None, consumed_client=None, consumed_offset=None, end_offset=None, partition_id=None, start_offset=None, _configuration=None):  # noqa: E501
        """ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accumulation = None
        self._consumed_client = None
        self._consumed_offset = None
        self._end_offset = None
        self._partition_id = None
        self._start_offset = None
        self.discriminator = None

        if accumulation is not None:
            self.accumulation = accumulation
        if consumed_client is not None:
            self.consumed_client = consumed_client
        if consumed_offset is not None:
            self.consumed_offset = consumed_offset
        if end_offset is not None:
            self.end_offset = end_offset
        if partition_id is not None:
            self.partition_id = partition_id
        if start_offset is not None:
            self.start_offset = start_offset

    @property
    def accumulation(self):
        """Gets the accumulation of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501


        :return: The accumulation of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._accumulation

    @accumulation.setter
    def accumulation(self, accumulation):
        """Sets the accumulation of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.


        :param accumulation: The accumulation of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._accumulation = accumulation

    @property
    def consumed_client(self):
        """Gets the consumed_client of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501


        :return: The consumed_client of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._consumed_client

    @consumed_client.setter
    def consumed_client(self, consumed_client):
        """Sets the consumed_client of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.


        :param consumed_client: The consumed_client of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :type: str
        """

        self._consumed_client = consumed_client

    @property
    def consumed_offset(self):
        """Gets the consumed_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501


        :return: The consumed_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._consumed_offset

    @consumed_offset.setter
    def consumed_offset(self, consumed_offset):
        """Sets the consumed_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.


        :param consumed_offset: The consumed_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._consumed_offset = consumed_offset

    @property
    def end_offset(self):
        """Gets the end_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501


        :return: The end_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._end_offset

    @end_offset.setter
    def end_offset(self, end_offset):
        """Sets the end_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.


        :param end_offset: The end_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._end_offset = end_offset

    @property
    def partition_id(self):
        """Gets the partition_id of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501


        :return: The partition_id of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._partition_id

    @partition_id.setter
    def partition_id(self, partition_id):
        """Sets the partition_id of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.


        :param partition_id: The partition_id of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._partition_id = partition_id

    @property
    def start_offset(self):
        """Gets the start_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501


        :return: The start_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_offset

    @start_offset.setter
    def start_offset(self, start_offset):
        """Sets the start_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.


        :param start_offset: The start_offset of this ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput.  # noqa: E501
        :type: int
        """

        self._start_offset = start_offset

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConsumedPartitionsInfoForDescribeConsumedPartitionsOutput):
            return True

        return self.to_dict() != other.to_dict()
