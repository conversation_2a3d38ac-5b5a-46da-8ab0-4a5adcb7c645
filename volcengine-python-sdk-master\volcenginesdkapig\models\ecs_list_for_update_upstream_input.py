# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EcsListForUpdateUpstreamInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ecs_id': 'str',
        'ip': 'str',
        'port': 'int'
    }

    attribute_map = {
        'ecs_id': 'EcsId',
        'ip': 'IP',
        'port': 'Port'
    }

    def __init__(self, ecs_id=None, ip=None, port=None, _configuration=None):  # noqa: E501
        """EcsListForUpdateUpstreamInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ecs_id = None
        self._ip = None
        self._port = None
        self.discriminator = None

        if ecs_id is not None:
            self.ecs_id = ecs_id
        if ip is not None:
            self.ip = ip
        if port is not None:
            self.port = port

    @property
    def ecs_id(self):
        """Gets the ecs_id of this EcsListForUpdateUpstreamInput.  # noqa: E501


        :return: The ecs_id of this EcsListForUpdateUpstreamInput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_id

    @ecs_id.setter
    def ecs_id(self, ecs_id):
        """Sets the ecs_id of this EcsListForUpdateUpstreamInput.


        :param ecs_id: The ecs_id of this EcsListForUpdateUpstreamInput.  # noqa: E501
        :type: str
        """

        self._ecs_id = ecs_id

    @property
    def ip(self):
        """Gets the ip of this EcsListForUpdateUpstreamInput.  # noqa: E501


        :return: The ip of this EcsListForUpdateUpstreamInput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this EcsListForUpdateUpstreamInput.


        :param ip: The ip of this EcsListForUpdateUpstreamInput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def port(self):
        """Gets the port of this EcsListForUpdateUpstreamInput.  # noqa: E501


        :return: The port of this EcsListForUpdateUpstreamInput.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this EcsListForUpdateUpstreamInput.


        :param port: The port of this EcsListForUpdateUpstreamInput.  # noqa: E501
        :type: int
        """

        self._port = port

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EcsListForUpdateUpstreamInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EcsListForUpdateUpstreamInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EcsListForUpdateUpstreamInput):
            return True

        return self.to_dict() != other.to_dict()
