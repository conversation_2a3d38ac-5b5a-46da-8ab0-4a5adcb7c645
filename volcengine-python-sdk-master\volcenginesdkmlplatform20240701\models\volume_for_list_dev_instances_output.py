# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VolumeForListDevInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'size': 'int',
        'status': 'StatusForListDevInstancesOutput',
        'used': 'str',
        'volume_type_id': 'str'
    }

    attribute_map = {
        'id': 'Id',
        'size': 'Size',
        'status': 'Status',
        'used': 'Used',
        'volume_type_id': 'VolumeTypeId'
    }

    def __init__(self, id=None, size=None, status=None, used=None, volume_type_id=None, _configuration=None):  # noqa: E501
        """VolumeForListDevInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._size = None
        self._status = None
        self._used = None
        self._volume_type_id = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if size is not None:
            self.size = size
        if status is not None:
            self.status = status
        if used is not None:
            self.used = used
        if volume_type_id is not None:
            self.volume_type_id = volume_type_id

    @property
    def id(self):
        """Gets the id of this VolumeForListDevInstancesOutput.  # noqa: E501


        :return: The id of this VolumeForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VolumeForListDevInstancesOutput.


        :param id: The id of this VolumeForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def size(self):
        """Gets the size of this VolumeForListDevInstancesOutput.  # noqa: E501


        :return: The size of this VolumeForListDevInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this VolumeForListDevInstancesOutput.


        :param size: The size of this VolumeForListDevInstancesOutput.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def status(self):
        """Gets the status of this VolumeForListDevInstancesOutput.  # noqa: E501


        :return: The status of this VolumeForListDevInstancesOutput.  # noqa: E501
        :rtype: StatusForListDevInstancesOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this VolumeForListDevInstancesOutput.


        :param status: The status of this VolumeForListDevInstancesOutput.  # noqa: E501
        :type: StatusForListDevInstancesOutput
        """

        self._status = status

    @property
    def used(self):
        """Gets the used of this VolumeForListDevInstancesOutput.  # noqa: E501


        :return: The used of this VolumeForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._used

    @used.setter
    def used(self, used):
        """Sets the used of this VolumeForListDevInstancesOutput.


        :param used: The used of this VolumeForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._used = used

    @property
    def volume_type_id(self):
        """Gets the volume_type_id of this VolumeForListDevInstancesOutput.  # noqa: E501


        :return: The volume_type_id of this VolumeForListDevInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._volume_type_id

    @volume_type_id.setter
    def volume_type_id(self, volume_type_id):
        """Sets the volume_type_id of this VolumeForListDevInstancesOutput.


        :param volume_type_id: The volume_type_id of this VolumeForListDevInstancesOutput.  # noqa: E501
        :type: str
        """

        self._volume_type_id = volume_type_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VolumeForListDevInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VolumeForListDevInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VolumeForListDevInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
