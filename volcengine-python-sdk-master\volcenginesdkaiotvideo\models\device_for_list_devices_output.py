# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeviceForListDevicesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_notification': 'AlertNotificationForListDevicesOutput',
        'auto_pull_after_register': 'bool',
        'channel_num': 'int',
        'coordinates': 'CoordinatesForListDevicesOutput',
        'created_at': 'str',
        'description': 'str',
        'device_id': 'str',
        'device_nsid': 'str',
        'device_name': 'str',
        'device_streams': 'DeviceStreamsForListDevicesOutput',
        'device_sub_streams': 'list[DeviceSubStreamForListDevicesOutput]',
        'location': 'str',
        'manufactory': 'str',
        'password': 'str',
        'rtp_transport_tcp': 'bool',
        'space_id': 'str',
        'status': 'str',
        'type': 'str',
        'updated_at': 'str',
        'use_sub_stream': 'bool',
        'username': 'str'
    }

    attribute_map = {
        'alert_notification': 'AlertNotification',
        'auto_pull_after_register': 'AutoPullAfterRegister',
        'channel_num': 'ChannelNum',
        'coordinates': 'Coordinates',
        'created_at': 'CreatedAt',
        'description': 'Description',
        'device_id': 'DeviceID',
        'device_nsid': 'DeviceNSID',
        'device_name': 'DeviceName',
        'device_streams': 'DeviceStreams',
        'device_sub_streams': 'DeviceSubStreams',
        'location': 'Location',
        'manufactory': 'Manufactory',
        'password': 'Password',
        'rtp_transport_tcp': 'RtpTransportTcp',
        'space_id': 'SpaceID',
        'status': 'Status',
        'type': 'Type',
        'updated_at': 'UpdatedAt',
        'use_sub_stream': 'UseSubStream',
        'username': 'Username'
    }

    def __init__(self, alert_notification=None, auto_pull_after_register=None, channel_num=None, coordinates=None, created_at=None, description=None, device_id=None, device_nsid=None, device_name=None, device_streams=None, device_sub_streams=None, location=None, manufactory=None, password=None, rtp_transport_tcp=None, space_id=None, status=None, type=None, updated_at=None, use_sub_stream=None, username=None, _configuration=None):  # noqa: E501
        """DeviceForListDevicesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_notification = None
        self._auto_pull_after_register = None
        self._channel_num = None
        self._coordinates = None
        self._created_at = None
        self._description = None
        self._device_id = None
        self._device_nsid = None
        self._device_name = None
        self._device_streams = None
        self._device_sub_streams = None
        self._location = None
        self._manufactory = None
        self._password = None
        self._rtp_transport_tcp = None
        self._space_id = None
        self._status = None
        self._type = None
        self._updated_at = None
        self._use_sub_stream = None
        self._username = None
        self.discriminator = None

        if alert_notification is not None:
            self.alert_notification = alert_notification
        if auto_pull_after_register is not None:
            self.auto_pull_after_register = auto_pull_after_register
        if channel_num is not None:
            self.channel_num = channel_num
        if coordinates is not None:
            self.coordinates = coordinates
        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if device_id is not None:
            self.device_id = device_id
        if device_nsid is not None:
            self.device_nsid = device_nsid
        if device_name is not None:
            self.device_name = device_name
        if device_streams is not None:
            self.device_streams = device_streams
        if device_sub_streams is not None:
            self.device_sub_streams = device_sub_streams
        if location is not None:
            self.location = location
        if manufactory is not None:
            self.manufactory = manufactory
        if password is not None:
            self.password = password
        if rtp_transport_tcp is not None:
            self.rtp_transport_tcp = rtp_transport_tcp
        if space_id is not None:
            self.space_id = space_id
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type
        if updated_at is not None:
            self.updated_at = updated_at
        if use_sub_stream is not None:
            self.use_sub_stream = use_sub_stream
        if username is not None:
            self.username = username

    @property
    def alert_notification(self):
        """Gets the alert_notification of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The alert_notification of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: AlertNotificationForListDevicesOutput
        """
        return self._alert_notification

    @alert_notification.setter
    def alert_notification(self, alert_notification):
        """Sets the alert_notification of this DeviceForListDevicesOutput.


        :param alert_notification: The alert_notification of this DeviceForListDevicesOutput.  # noqa: E501
        :type: AlertNotificationForListDevicesOutput
        """

        self._alert_notification = alert_notification

    @property
    def auto_pull_after_register(self):
        """Gets the auto_pull_after_register of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The auto_pull_after_register of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_pull_after_register

    @auto_pull_after_register.setter
    def auto_pull_after_register(self, auto_pull_after_register):
        """Sets the auto_pull_after_register of this DeviceForListDevicesOutput.


        :param auto_pull_after_register: The auto_pull_after_register of this DeviceForListDevicesOutput.  # noqa: E501
        :type: bool
        """

        self._auto_pull_after_register = auto_pull_after_register

    @property
    def channel_num(self):
        """Gets the channel_num of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The channel_num of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: int
        """
        return self._channel_num

    @channel_num.setter
    def channel_num(self, channel_num):
        """Sets the channel_num of this DeviceForListDevicesOutput.


        :param channel_num: The channel_num of this DeviceForListDevicesOutput.  # noqa: E501
        :type: int
        """

        self._channel_num = channel_num

    @property
    def coordinates(self):
        """Gets the coordinates of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The coordinates of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: CoordinatesForListDevicesOutput
        """
        return self._coordinates

    @coordinates.setter
    def coordinates(self, coordinates):
        """Sets the coordinates of this DeviceForListDevicesOutput.


        :param coordinates: The coordinates of this DeviceForListDevicesOutput.  # noqa: E501
        :type: CoordinatesForListDevicesOutput
        """

        self._coordinates = coordinates

    @property
    def created_at(self):
        """Gets the created_at of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The created_at of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this DeviceForListDevicesOutput.


        :param created_at: The created_at of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The description of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DeviceForListDevicesOutput.


        :param description: The description of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def device_id(self):
        """Gets the device_id of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The device_id of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._device_id

    @device_id.setter
    def device_id(self, device_id):
        """Sets the device_id of this DeviceForListDevicesOutput.


        :param device_id: The device_id of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._device_id = device_id

    @property
    def device_nsid(self):
        """Gets the device_nsid of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The device_nsid of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._device_nsid

    @device_nsid.setter
    def device_nsid(self, device_nsid):
        """Sets the device_nsid of this DeviceForListDevicesOutput.


        :param device_nsid: The device_nsid of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._device_nsid = device_nsid

    @property
    def device_name(self):
        """Gets the device_name of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The device_name of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._device_name

    @device_name.setter
    def device_name(self, device_name):
        """Sets the device_name of this DeviceForListDevicesOutput.


        :param device_name: The device_name of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._device_name = device_name

    @property
    def device_streams(self):
        """Gets the device_streams of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The device_streams of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: DeviceStreamsForListDevicesOutput
        """
        return self._device_streams

    @device_streams.setter
    def device_streams(self, device_streams):
        """Sets the device_streams of this DeviceForListDevicesOutput.


        :param device_streams: The device_streams of this DeviceForListDevicesOutput.  # noqa: E501
        :type: DeviceStreamsForListDevicesOutput
        """

        self._device_streams = device_streams

    @property
    def device_sub_streams(self):
        """Gets the device_sub_streams of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The device_sub_streams of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: list[DeviceSubStreamForListDevicesOutput]
        """
        return self._device_sub_streams

    @device_sub_streams.setter
    def device_sub_streams(self, device_sub_streams):
        """Sets the device_sub_streams of this DeviceForListDevicesOutput.


        :param device_sub_streams: The device_sub_streams of this DeviceForListDevicesOutput.  # noqa: E501
        :type: list[DeviceSubStreamForListDevicesOutput]
        """

        self._device_sub_streams = device_sub_streams

    @property
    def location(self):
        """Gets the location of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The location of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._location

    @location.setter
    def location(self, location):
        """Sets the location of this DeviceForListDevicesOutput.


        :param location: The location of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._location = location

    @property
    def manufactory(self):
        """Gets the manufactory of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The manufactory of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._manufactory

    @manufactory.setter
    def manufactory(self, manufactory):
        """Sets the manufactory of this DeviceForListDevicesOutput.


        :param manufactory: The manufactory of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._manufactory = manufactory

    @property
    def password(self):
        """Gets the password of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The password of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this DeviceForListDevicesOutput.


        :param password: The password of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._password = password

    @property
    def rtp_transport_tcp(self):
        """Gets the rtp_transport_tcp of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The rtp_transport_tcp of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._rtp_transport_tcp

    @rtp_transport_tcp.setter
    def rtp_transport_tcp(self, rtp_transport_tcp):
        """Sets the rtp_transport_tcp of this DeviceForListDevicesOutput.


        :param rtp_transport_tcp: The rtp_transport_tcp of this DeviceForListDevicesOutput.  # noqa: E501
        :type: bool
        """

        self._rtp_transport_tcp = rtp_transport_tcp

    @property
    def space_id(self):
        """Gets the space_id of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The space_id of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this DeviceForListDevicesOutput.


        :param space_id: The space_id of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._space_id = space_id

    @property
    def status(self):
        """Gets the status of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The status of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DeviceForListDevicesOutput.


        :param status: The status of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The type of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DeviceForListDevicesOutput.


        :param type: The type of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def updated_at(self):
        """Gets the updated_at of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The updated_at of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this DeviceForListDevicesOutput.


        :param updated_at: The updated_at of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def use_sub_stream(self):
        """Gets the use_sub_stream of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The use_sub_stream of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._use_sub_stream

    @use_sub_stream.setter
    def use_sub_stream(self, use_sub_stream):
        """Sets the use_sub_stream of this DeviceForListDevicesOutput.


        :param use_sub_stream: The use_sub_stream of this DeviceForListDevicesOutput.  # noqa: E501
        :type: bool
        """

        self._use_sub_stream = use_sub_stream

    @property
    def username(self):
        """Gets the username of this DeviceForListDevicesOutput.  # noqa: E501


        :return: The username of this DeviceForListDevicesOutput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this DeviceForListDevicesOutput.


        :param username: The username of this DeviceForListDevicesOutput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeviceForListDevicesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeviceForListDevicesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeviceForListDevicesOutput):
            return True

        return self.to_dict() != other.to_dict()
