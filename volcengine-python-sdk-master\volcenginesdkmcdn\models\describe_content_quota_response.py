# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeContentQuotaResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'quotas': 'list[QuotaForDescribeContentQuotaOutput]',
        'vendors_meta_data': 'list[VendorsMetaDataForDescribeContentQuotaOutput]'
    }

    attribute_map = {
        'quotas': 'Quotas',
        'vendors_meta_data': 'VendorsMetaData'
    }

    def __init__(self, quotas=None, vendors_meta_data=None, _configuration=None):  # noqa: E501
        """DescribeContentQuotaResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._quotas = None
        self._vendors_meta_data = None
        self.discriminator = None

        if quotas is not None:
            self.quotas = quotas
        if vendors_meta_data is not None:
            self.vendors_meta_data = vendors_meta_data

    @property
    def quotas(self):
        """Gets the quotas of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The quotas of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: list[QuotaForDescribeContentQuotaOutput]
        """
        return self._quotas

    @quotas.setter
    def quotas(self, quotas):
        """Sets the quotas of this DescribeContentQuotaResponse.


        :param quotas: The quotas of this DescribeContentQuotaResponse.  # noqa: E501
        :type: list[QuotaForDescribeContentQuotaOutput]
        """

        self._quotas = quotas

    @property
    def vendors_meta_data(self):
        """Gets the vendors_meta_data of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The vendors_meta_data of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: list[VendorsMetaDataForDescribeContentQuotaOutput]
        """
        return self._vendors_meta_data

    @vendors_meta_data.setter
    def vendors_meta_data(self, vendors_meta_data):
        """Sets the vendors_meta_data of this DescribeContentQuotaResponse.


        :param vendors_meta_data: The vendors_meta_data of this DescribeContentQuotaResponse.  # noqa: E501
        :type: list[VendorsMetaDataForDescribeContentQuotaOutput]
        """

        self._vendors_meta_data = vendors_meta_data

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeContentQuotaResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeContentQuotaResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeContentQuotaResponse):
            return True

        return self.to_dict() != other.to_dict()
