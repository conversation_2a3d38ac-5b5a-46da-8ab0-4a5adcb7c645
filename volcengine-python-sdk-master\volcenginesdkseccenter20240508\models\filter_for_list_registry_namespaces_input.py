# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListRegistryNamespacesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'namespace': 'str',
        'namespace_ids': 'list[str]',
        'registry_name': 'str'
    }

    attribute_map = {
        'namespace': 'Namespace',
        'namespace_ids': 'NamespaceIDs',
        'registry_name': 'RegistryName'
    }

    def __init__(self, namespace=None, namespace_ids=None, registry_name=None, _configuration=None):  # noqa: E501
        """FilterForListRegistryNamespacesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._namespace = None
        self._namespace_ids = None
        self._registry_name = None
        self.discriminator = None

        if namespace is not None:
            self.namespace = namespace
        if namespace_ids is not None:
            self.namespace_ids = namespace_ids
        if registry_name is not None:
            self.registry_name = registry_name

    @property
    def namespace(self):
        """Gets the namespace of this FilterForListRegistryNamespacesInput.  # noqa: E501


        :return: The namespace of this FilterForListRegistryNamespacesInput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this FilterForListRegistryNamespacesInput.


        :param namespace: The namespace of this FilterForListRegistryNamespacesInput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def namespace_ids(self):
        """Gets the namespace_ids of this FilterForListRegistryNamespacesInput.  # noqa: E501


        :return: The namespace_ids of this FilterForListRegistryNamespacesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._namespace_ids

    @namespace_ids.setter
    def namespace_ids(self, namespace_ids):
        """Sets the namespace_ids of this FilterForListRegistryNamespacesInput.


        :param namespace_ids: The namespace_ids of this FilterForListRegistryNamespacesInput.  # noqa: E501
        :type: list[str]
        """

        self._namespace_ids = namespace_ids

    @property
    def registry_name(self):
        """Gets the registry_name of this FilterForListRegistryNamespacesInput.  # noqa: E501


        :return: The registry_name of this FilterForListRegistryNamespacesInput.  # noqa: E501
        :rtype: str
        """
        return self._registry_name

    @registry_name.setter
    def registry_name(self, registry_name):
        """Sets the registry_name of this FilterForListRegistryNamespacesInput.


        :param registry_name: The registry_name of this FilterForListRegistryNamespacesInput.  # noqa: E501
        :type: str
        """

        self._registry_name = registry_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListRegistryNamespacesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListRegistryNamespacesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListRegistryNamespacesInput):
            return True

        return self.to_dict() != other.to_dict()
