# coding: utf-8

"""
    rds_postgresql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstancePriceDetailRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_info': 'ChargeInfoForDescribeDBInstancePriceDetailInput',
        'node_info': 'list[NodeInfoForDescribeDBInstancePriceDetailInput]',
        'storage_space': 'int',
        'storage_type': 'str'
    }

    attribute_map = {
        'charge_info': 'ChargeInfo',
        'node_info': 'NodeInfo',
        'storage_space': 'StorageSpace',
        'storage_type': 'StorageType'
    }

    def __init__(self, charge_info=None, node_info=None, storage_space=None, storage_type=None, _configuration=None):  # noqa: E501
        """DescribeDBInstancePriceDetailRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_info = None
        self._node_info = None
        self._storage_space = None
        self._storage_type = None
        self.discriminator = None

        if charge_info is not None:
            self.charge_info = charge_info
        if node_info is not None:
            self.node_info = node_info
        self.storage_space = storage_space
        self.storage_type = storage_type

    @property
    def charge_info(self):
        """Gets the charge_info of this DescribeDBInstancePriceDetailRequest.  # noqa: E501


        :return: The charge_info of this DescribeDBInstancePriceDetailRequest.  # noqa: E501
        :rtype: ChargeInfoForDescribeDBInstancePriceDetailInput
        """
        return self._charge_info

    @charge_info.setter
    def charge_info(self, charge_info):
        """Sets the charge_info of this DescribeDBInstancePriceDetailRequest.


        :param charge_info: The charge_info of this DescribeDBInstancePriceDetailRequest.  # noqa: E501
        :type: ChargeInfoForDescribeDBInstancePriceDetailInput
        """

        self._charge_info = charge_info

    @property
    def node_info(self):
        """Gets the node_info of this DescribeDBInstancePriceDetailRequest.  # noqa: E501


        :return: The node_info of this DescribeDBInstancePriceDetailRequest.  # noqa: E501
        :rtype: list[NodeInfoForDescribeDBInstancePriceDetailInput]
        """
        return self._node_info

    @node_info.setter
    def node_info(self, node_info):
        """Sets the node_info of this DescribeDBInstancePriceDetailRequest.


        :param node_info: The node_info of this DescribeDBInstancePriceDetailRequest.  # noqa: E501
        :type: list[NodeInfoForDescribeDBInstancePriceDetailInput]
        """

        self._node_info = node_info

    @property
    def storage_space(self):
        """Gets the storage_space of this DescribeDBInstancePriceDetailRequest.  # noqa: E501


        :return: The storage_space of this DescribeDBInstancePriceDetailRequest.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this DescribeDBInstancePriceDetailRequest.


        :param storage_space: The storage_space of this DescribeDBInstancePriceDetailRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and storage_space is None:
            raise ValueError("Invalid value for `storage_space`, must not be `None`")  # noqa: E501

        self._storage_space = storage_space

    @property
    def storage_type(self):
        """Gets the storage_type of this DescribeDBInstancePriceDetailRequest.  # noqa: E501


        :return: The storage_type of this DescribeDBInstancePriceDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this DescribeDBInstancePriceDetailRequest.


        :param storage_type: The storage_type of this DescribeDBInstancePriceDetailRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and storage_type is None:
            raise ValueError("Invalid value for `storage_type`, must not be `None`")  # noqa: E501

        self._storage_type = storage_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstancePriceDetailRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstancePriceDetailRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstancePriceDetailRequest):
            return True

        return self.to_dict() != other.to_dict()
