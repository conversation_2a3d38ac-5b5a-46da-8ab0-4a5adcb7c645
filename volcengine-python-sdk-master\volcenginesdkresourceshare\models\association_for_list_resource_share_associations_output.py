# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AssociationForListResourceShareAssociationsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'associate_time': 'str',
        'association_entity': 'str',
        'association_type': 'str',
        'external': 'bool',
        'resource_share_id': 'str',
        'resource_share_name': 'str',
        'resource_share_trn': 'str',
        'status': 'str'
    }

    attribute_map = {
        'associate_time': 'AssociateTime',
        'association_entity': 'AssociationEntity',
        'association_type': 'AssociationType',
        'external': 'External',
        'resource_share_id': 'ResourceShareId',
        'resource_share_name': 'ResourceShareName',
        'resource_share_trn': 'ResourceShareTrn',
        'status': 'Status'
    }

    def __init__(self, associate_time=None, association_entity=None, association_type=None, external=None, resource_share_id=None, resource_share_name=None, resource_share_trn=None, status=None, _configuration=None):  # noqa: E501
        """AssociationForListResourceShareAssociationsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._associate_time = None
        self._association_entity = None
        self._association_type = None
        self._external = None
        self._resource_share_id = None
        self._resource_share_name = None
        self._resource_share_trn = None
        self._status = None
        self.discriminator = None

        if associate_time is not None:
            self.associate_time = associate_time
        if association_entity is not None:
            self.association_entity = association_entity
        if association_type is not None:
            self.association_type = association_type
        if external is not None:
            self.external = external
        if resource_share_id is not None:
            self.resource_share_id = resource_share_id
        if resource_share_name is not None:
            self.resource_share_name = resource_share_name
        if resource_share_trn is not None:
            self.resource_share_trn = resource_share_trn
        if status is not None:
            self.status = status

    @property
    def associate_time(self):
        """Gets the associate_time of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501


        :return: The associate_time of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._associate_time

    @associate_time.setter
    def associate_time(self, associate_time):
        """Sets the associate_time of this AssociationForListResourceShareAssociationsOutput.


        :param associate_time: The associate_time of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._associate_time = associate_time

    @property
    def association_entity(self):
        """Gets the association_entity of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501


        :return: The association_entity of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._association_entity

    @association_entity.setter
    def association_entity(self, association_entity):
        """Sets the association_entity of this AssociationForListResourceShareAssociationsOutput.


        :param association_entity: The association_entity of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._association_entity = association_entity

    @property
    def association_type(self):
        """Gets the association_type of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501


        :return: The association_type of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._association_type

    @association_type.setter
    def association_type(self, association_type):
        """Sets the association_type of this AssociationForListResourceShareAssociationsOutput.


        :param association_type: The association_type of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._association_type = association_type

    @property
    def external(self):
        """Gets the external of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501


        :return: The external of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._external

    @external.setter
    def external(self, external):
        """Sets the external of this AssociationForListResourceShareAssociationsOutput.


        :param external: The external of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :type: bool
        """

        self._external = external

    @property
    def resource_share_id(self):
        """Gets the resource_share_id of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501


        :return: The resource_share_id of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_id

    @resource_share_id.setter
    def resource_share_id(self, resource_share_id):
        """Sets the resource_share_id of this AssociationForListResourceShareAssociationsOutput.


        :param resource_share_id: The resource_share_id of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_id = resource_share_id

    @property
    def resource_share_name(self):
        """Gets the resource_share_name of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501


        :return: The resource_share_name of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_name

    @resource_share_name.setter
    def resource_share_name(self, resource_share_name):
        """Sets the resource_share_name of this AssociationForListResourceShareAssociationsOutput.


        :param resource_share_name: The resource_share_name of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_name = resource_share_name

    @property
    def resource_share_trn(self):
        """Gets the resource_share_trn of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501


        :return: The resource_share_trn of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_share_trn

    @resource_share_trn.setter
    def resource_share_trn(self, resource_share_trn):
        """Sets the resource_share_trn of this AssociationForListResourceShareAssociationsOutput.


        :param resource_share_trn: The resource_share_trn of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._resource_share_trn = resource_share_trn

    @property
    def status(self):
        """Gets the status of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501


        :return: The status of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this AssociationForListResourceShareAssociationsOutput.


        :param status: The status of this AssociationForListResourceShareAssociationsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AssociationForListResourceShareAssociationsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AssociationForListResourceShareAssociationsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AssociationForListResourceShareAssociationsOutput):
            return True

        return self.to_dict() != other.to_dict()
