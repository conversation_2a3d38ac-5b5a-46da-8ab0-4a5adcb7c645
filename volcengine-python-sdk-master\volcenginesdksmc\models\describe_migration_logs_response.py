# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeMigrationLogsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'finished_at': 'str',
        'logs': 'list[LogForDescribeMigrationLogsOutput]',
        'started_at': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'finished_at': 'FinishedAt',
        'logs': 'Logs',
        'started_at': 'StartedAt'
    }

    def __init__(self, created_at=None, finished_at=None, logs=None, started_at=None, _configuration=None):  # noqa: E501
        """DescribeMigrationLogsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._finished_at = None
        self._logs = None
        self._started_at = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if finished_at is not None:
            self.finished_at = finished_at
        if logs is not None:
            self.logs = logs
        if started_at is not None:
            self.started_at = started_at

    @property
    def created_at(self):
        """Gets the created_at of this DescribeMigrationLogsResponse.  # noqa: E501


        :return: The created_at of this DescribeMigrationLogsResponse.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this DescribeMigrationLogsResponse.


        :param created_at: The created_at of this DescribeMigrationLogsResponse.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def finished_at(self):
        """Gets the finished_at of this DescribeMigrationLogsResponse.  # noqa: E501


        :return: The finished_at of this DescribeMigrationLogsResponse.  # noqa: E501
        :rtype: str
        """
        return self._finished_at

    @finished_at.setter
    def finished_at(self, finished_at):
        """Sets the finished_at of this DescribeMigrationLogsResponse.


        :param finished_at: The finished_at of this DescribeMigrationLogsResponse.  # noqa: E501
        :type: str
        """

        self._finished_at = finished_at

    @property
    def logs(self):
        """Gets the logs of this DescribeMigrationLogsResponse.  # noqa: E501


        :return: The logs of this DescribeMigrationLogsResponse.  # noqa: E501
        :rtype: list[LogForDescribeMigrationLogsOutput]
        """
        return self._logs

    @logs.setter
    def logs(self, logs):
        """Sets the logs of this DescribeMigrationLogsResponse.


        :param logs: The logs of this DescribeMigrationLogsResponse.  # noqa: E501
        :type: list[LogForDescribeMigrationLogsOutput]
        """

        self._logs = logs

    @property
    def started_at(self):
        """Gets the started_at of this DescribeMigrationLogsResponse.  # noqa: E501


        :return: The started_at of this DescribeMigrationLogsResponse.  # noqa: E501
        :rtype: str
        """
        return self._started_at

    @started_at.setter
    def started_at(self, started_at):
        """Sets the started_at of this DescribeMigrationLogsResponse.


        :param started_at: The started_at of this DescribeMigrationLogsResponse.  # noqa: E501
        :type: str
        """

        self._started_at = started_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeMigrationLogsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeMigrationLogsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeMigrationLogsResponse):
            return True

        return self.to_dict() != other.to_dict()
