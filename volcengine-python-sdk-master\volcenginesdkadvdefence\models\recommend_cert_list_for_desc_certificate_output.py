# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RecommendCertListForDescCertificateOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'hosts': 'list[str]',
        'name': 'str',
        'user_cert_id': 'str'
    }

    attribute_map = {
        'hosts': 'Hosts',
        'name': 'Name',
        'user_cert_id': 'UserCertId'
    }

    def __init__(self, hosts=None, name=None, user_cert_id=None, _configuration=None):  # noqa: E501
        """RecommendCertListForDescCertificateOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._hosts = None
        self._name = None
        self._user_cert_id = None
        self.discriminator = None

        if hosts is not None:
            self.hosts = hosts
        if name is not None:
            self.name = name
        if user_cert_id is not None:
            self.user_cert_id = user_cert_id

    @property
    def hosts(self):
        """Gets the hosts of this RecommendCertListForDescCertificateOutput.  # noqa: E501


        :return: The hosts of this RecommendCertListForDescCertificateOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._hosts

    @hosts.setter
    def hosts(self, hosts):
        """Sets the hosts of this RecommendCertListForDescCertificateOutput.


        :param hosts: The hosts of this RecommendCertListForDescCertificateOutput.  # noqa: E501
        :type: list[str]
        """

        self._hosts = hosts

    @property
    def name(self):
        """Gets the name of this RecommendCertListForDescCertificateOutput.  # noqa: E501


        :return: The name of this RecommendCertListForDescCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this RecommendCertListForDescCertificateOutput.


        :param name: The name of this RecommendCertListForDescCertificateOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def user_cert_id(self):
        """Gets the user_cert_id of this RecommendCertListForDescCertificateOutput.  # noqa: E501


        :return: The user_cert_id of this RecommendCertListForDescCertificateOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_cert_id

    @user_cert_id.setter
    def user_cert_id(self, user_cert_id):
        """Sets the user_cert_id of this RecommendCertListForDescCertificateOutput.


        :param user_cert_id: The user_cert_id of this RecommendCertListForDescCertificateOutput.  # noqa: E501
        :type: str
        """

        self._user_cert_id = user_cert_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RecommendCertListForDescCertificateOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RecommendCertListForDescCertificateOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RecommendCertListForDescCertificateOutput):
            return True

        return self.to_dict() != other.to_dict()
