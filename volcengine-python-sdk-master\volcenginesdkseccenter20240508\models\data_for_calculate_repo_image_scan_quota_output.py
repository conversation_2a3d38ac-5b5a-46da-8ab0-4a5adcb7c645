# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForCalculateRepoImageScanQuotaOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_cnt': 'int',
        'cost_quota_cnt': 'int',
        'remain_quota_cnt': 'int'
    }

    attribute_map = {
        'asset_cnt': 'AssetCnt',
        'cost_quota_cnt': 'CostQuotaCnt',
        'remain_quota_cnt': 'RemainQuotaCnt'
    }

    def __init__(self, asset_cnt=None, cost_quota_cnt=None, remain_quota_cnt=None, _configuration=None):  # noqa: E501
        """DataForCalculateRepoImageScanQuotaOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_cnt = None
        self._cost_quota_cnt = None
        self._remain_quota_cnt = None
        self.discriminator = None

        if asset_cnt is not None:
            self.asset_cnt = asset_cnt
        if cost_quota_cnt is not None:
            self.cost_quota_cnt = cost_quota_cnt
        if remain_quota_cnt is not None:
            self.remain_quota_cnt = remain_quota_cnt

    @property
    def asset_cnt(self):
        """Gets the asset_cnt of this DataForCalculateRepoImageScanQuotaOutput.  # noqa: E501


        :return: The asset_cnt of this DataForCalculateRepoImageScanQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._asset_cnt

    @asset_cnt.setter
    def asset_cnt(self, asset_cnt):
        """Sets the asset_cnt of this DataForCalculateRepoImageScanQuotaOutput.


        :param asset_cnt: The asset_cnt of this DataForCalculateRepoImageScanQuotaOutput.  # noqa: E501
        :type: int
        """

        self._asset_cnt = asset_cnt

    @property
    def cost_quota_cnt(self):
        """Gets the cost_quota_cnt of this DataForCalculateRepoImageScanQuotaOutput.  # noqa: E501


        :return: The cost_quota_cnt of this DataForCalculateRepoImageScanQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._cost_quota_cnt

    @cost_quota_cnt.setter
    def cost_quota_cnt(self, cost_quota_cnt):
        """Sets the cost_quota_cnt of this DataForCalculateRepoImageScanQuotaOutput.


        :param cost_quota_cnt: The cost_quota_cnt of this DataForCalculateRepoImageScanQuotaOutput.  # noqa: E501
        :type: int
        """

        self._cost_quota_cnt = cost_quota_cnt

    @property
    def remain_quota_cnt(self):
        """Gets the remain_quota_cnt of this DataForCalculateRepoImageScanQuotaOutput.  # noqa: E501


        :return: The remain_quota_cnt of this DataForCalculateRepoImageScanQuotaOutput.  # noqa: E501
        :rtype: int
        """
        return self._remain_quota_cnt

    @remain_quota_cnt.setter
    def remain_quota_cnt(self, remain_quota_cnt):
        """Sets the remain_quota_cnt of this DataForCalculateRepoImageScanQuotaOutput.


        :param remain_quota_cnt: The remain_quota_cnt of this DataForCalculateRepoImageScanQuotaOutput.  # noqa: E501
        :type: int
        """

        self._remain_quota_cnt = remain_quota_cnt

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForCalculateRepoImageScanQuotaOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForCalculateRepoImageScanQuotaOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForCalculateRepoImageScanQuotaOutput):
            return True

        return self.to_dict() != other.to_dict()
