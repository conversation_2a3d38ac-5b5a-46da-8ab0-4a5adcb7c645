# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetActivityLivePromotionDataResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'app_live_template_live_count': 'int',
        'app_live_template_live_duration': 'int',
        'live_count': 'int',
        'live_promotion_count': 'int',
        'live_promotion_duration': 'int',
        'live_promotion_platform_count': 'int'
    }

    attribute_map = {
        'app_live_template_live_count': 'AppLiveTemplateLiveCount',
        'app_live_template_live_duration': 'AppLiveTemplateLiveDuration',
        'live_count': 'LiveCount',
        'live_promotion_count': 'LivePromotionCount',
        'live_promotion_duration': 'LivePromotionDuration',
        'live_promotion_platform_count': 'LivePromotionPlatformCount'
    }

    def __init__(self, app_live_template_live_count=None, app_live_template_live_duration=None, live_count=None, live_promotion_count=None, live_promotion_duration=None, live_promotion_platform_count=None, _configuration=None):  # noqa: E501
        """GetActivityLivePromotionDataResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._app_live_template_live_count = None
        self._app_live_template_live_duration = None
        self._live_count = None
        self._live_promotion_count = None
        self._live_promotion_duration = None
        self._live_promotion_platform_count = None
        self.discriminator = None

        if app_live_template_live_count is not None:
            self.app_live_template_live_count = app_live_template_live_count
        if app_live_template_live_duration is not None:
            self.app_live_template_live_duration = app_live_template_live_duration
        if live_count is not None:
            self.live_count = live_count
        if live_promotion_count is not None:
            self.live_promotion_count = live_promotion_count
        if live_promotion_duration is not None:
            self.live_promotion_duration = live_promotion_duration
        if live_promotion_platform_count is not None:
            self.live_promotion_platform_count = live_promotion_platform_count

    @property
    def app_live_template_live_count(self):
        """Gets the app_live_template_live_count of this GetActivityLivePromotionDataResponse.  # noqa: E501


        :return: The app_live_template_live_count of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :rtype: int
        """
        return self._app_live_template_live_count

    @app_live_template_live_count.setter
    def app_live_template_live_count(self, app_live_template_live_count):
        """Sets the app_live_template_live_count of this GetActivityLivePromotionDataResponse.


        :param app_live_template_live_count: The app_live_template_live_count of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :type: int
        """

        self._app_live_template_live_count = app_live_template_live_count

    @property
    def app_live_template_live_duration(self):
        """Gets the app_live_template_live_duration of this GetActivityLivePromotionDataResponse.  # noqa: E501


        :return: The app_live_template_live_duration of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :rtype: int
        """
        return self._app_live_template_live_duration

    @app_live_template_live_duration.setter
    def app_live_template_live_duration(self, app_live_template_live_duration):
        """Sets the app_live_template_live_duration of this GetActivityLivePromotionDataResponse.


        :param app_live_template_live_duration: The app_live_template_live_duration of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :type: int
        """

        self._app_live_template_live_duration = app_live_template_live_duration

    @property
    def live_count(self):
        """Gets the live_count of this GetActivityLivePromotionDataResponse.  # noqa: E501


        :return: The live_count of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_count

    @live_count.setter
    def live_count(self, live_count):
        """Sets the live_count of this GetActivityLivePromotionDataResponse.


        :param live_count: The live_count of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :type: int
        """

        self._live_count = live_count

    @property
    def live_promotion_count(self):
        """Gets the live_promotion_count of this GetActivityLivePromotionDataResponse.  # noqa: E501


        :return: The live_promotion_count of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_promotion_count

    @live_promotion_count.setter
    def live_promotion_count(self, live_promotion_count):
        """Sets the live_promotion_count of this GetActivityLivePromotionDataResponse.


        :param live_promotion_count: The live_promotion_count of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :type: int
        """

        self._live_promotion_count = live_promotion_count

    @property
    def live_promotion_duration(self):
        """Gets the live_promotion_duration of this GetActivityLivePromotionDataResponse.  # noqa: E501


        :return: The live_promotion_duration of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_promotion_duration

    @live_promotion_duration.setter
    def live_promotion_duration(self, live_promotion_duration):
        """Sets the live_promotion_duration of this GetActivityLivePromotionDataResponse.


        :param live_promotion_duration: The live_promotion_duration of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :type: int
        """

        self._live_promotion_duration = live_promotion_duration

    @property
    def live_promotion_platform_count(self):
        """Gets the live_promotion_platform_count of this GetActivityLivePromotionDataResponse.  # noqa: E501


        :return: The live_promotion_platform_count of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_promotion_platform_count

    @live_promotion_platform_count.setter
    def live_promotion_platform_count(self, live_promotion_platform_count):
        """Sets the live_promotion_platform_count of this GetActivityLivePromotionDataResponse.


        :param live_promotion_platform_count: The live_promotion_platform_count of this GetActivityLivePromotionDataResponse.  # noqa: E501
        :type: int
        """

        self._live_promotion_platform_count = live_promotion_platform_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetActivityLivePromotionDataResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetActivityLivePromotionDataResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetActivityLivePromotionDataResponse):
            return True

        return self.to_dict() != other.to_dict()
