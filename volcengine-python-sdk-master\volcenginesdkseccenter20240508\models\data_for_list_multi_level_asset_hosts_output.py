# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListMultiLevelAssetHostsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'abnormal_plugins_list': 'str',
        'account_id': 'str',
        'agent_id': 'str',
        'agent_status': 'str',
        'cloud_provider': 'str',
        'cpu_usage': 'float',
        'dns': 'str',
        'ecs_instance': 'EcsInstanceForListMultiLevelAssetHostsOutput',
        'first_heartbeat_time': 'int',
        'gateway': 'str',
        'group': 'str',
        'group_path': 'str',
        'institution_id': 'str',
        'kernel_version': 'str',
        'last_heartbeat_time': 'int',
        'mem_usage': 'float',
        'online': 'bool',
        'platform': 'str',
        'plugins_brief_info': 'str',
        'plugins_status': 'str',
        'reason': 'str',
        'risk': 'RiskForListMultiLevelAssetHostsOutput',
        'security_enhancement': 'bool',
        'start_time': 'int',
        'status': 'str',
        'tags': 'list[str]',
        'total_mem': 'int',
        'update_time': 'int',
        'user_status': 'str',
        'user_status_reason': 'str'
    }

    attribute_map = {
        'abnormal_plugins_list': 'AbnormalPluginsList',
        'account_id': 'AccountID',
        'agent_id': 'AgentID',
        'agent_status': 'AgentStatus',
        'cloud_provider': 'CloudProvider',
        'cpu_usage': 'CpuUsage',
        'dns': 'Dns',
        'ecs_instance': 'EcsInstance',
        'first_heartbeat_time': 'FirstHeartbeatTime',
        'gateway': 'Gateway',
        'group': 'Group',
        'group_path': 'GroupPath',
        'institution_id': 'InstitutionID',
        'kernel_version': 'KernelVersion',
        'last_heartbeat_time': 'LastHeartbeatTime',
        'mem_usage': 'MemUsage',
        'online': 'Online',
        'platform': 'Platform',
        'plugins_brief_info': 'PluginsBriefInfo',
        'plugins_status': 'PluginsStatus',
        'reason': 'Reason',
        'risk': 'Risk',
        'security_enhancement': 'SecurityEnhancement',
        'start_time': 'StartTime',
        'status': 'Status',
        'tags': 'Tags',
        'total_mem': 'TotalMem',
        'update_time': 'UpdateTime',
        'user_status': 'UserStatus',
        'user_status_reason': 'UserStatusReason'
    }

    def __init__(self, abnormal_plugins_list=None, account_id=None, agent_id=None, agent_status=None, cloud_provider=None, cpu_usage=None, dns=None, ecs_instance=None, first_heartbeat_time=None, gateway=None, group=None, group_path=None, institution_id=None, kernel_version=None, last_heartbeat_time=None, mem_usage=None, online=None, platform=None, plugins_brief_info=None, plugins_status=None, reason=None, risk=None, security_enhancement=None, start_time=None, status=None, tags=None, total_mem=None, update_time=None, user_status=None, user_status_reason=None, _configuration=None):  # noqa: E501
        """DataForListMultiLevelAssetHostsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._abnormal_plugins_list = None
        self._account_id = None
        self._agent_id = None
        self._agent_status = None
        self._cloud_provider = None
        self._cpu_usage = None
        self._dns = None
        self._ecs_instance = None
        self._first_heartbeat_time = None
        self._gateway = None
        self._group = None
        self._group_path = None
        self._institution_id = None
        self._kernel_version = None
        self._last_heartbeat_time = None
        self._mem_usage = None
        self._online = None
        self._platform = None
        self._plugins_brief_info = None
        self._plugins_status = None
        self._reason = None
        self._risk = None
        self._security_enhancement = None
        self._start_time = None
        self._status = None
        self._tags = None
        self._total_mem = None
        self._update_time = None
        self._user_status = None
        self._user_status_reason = None
        self.discriminator = None

        if abnormal_plugins_list is not None:
            self.abnormal_plugins_list = abnormal_plugins_list
        if account_id is not None:
            self.account_id = account_id
        if agent_id is not None:
            self.agent_id = agent_id
        if agent_status is not None:
            self.agent_status = agent_status
        if cloud_provider is not None:
            self.cloud_provider = cloud_provider
        if cpu_usage is not None:
            self.cpu_usage = cpu_usage
        if dns is not None:
            self.dns = dns
        if ecs_instance is not None:
            self.ecs_instance = ecs_instance
        if first_heartbeat_time is not None:
            self.first_heartbeat_time = first_heartbeat_time
        if gateway is not None:
            self.gateway = gateway
        if group is not None:
            self.group = group
        if group_path is not None:
            self.group_path = group_path
        if institution_id is not None:
            self.institution_id = institution_id
        if kernel_version is not None:
            self.kernel_version = kernel_version
        if last_heartbeat_time is not None:
            self.last_heartbeat_time = last_heartbeat_time
        if mem_usage is not None:
            self.mem_usage = mem_usage
        if online is not None:
            self.online = online
        if platform is not None:
            self.platform = platform
        if plugins_brief_info is not None:
            self.plugins_brief_info = plugins_brief_info
        if plugins_status is not None:
            self.plugins_status = plugins_status
        if reason is not None:
            self.reason = reason
        if risk is not None:
            self.risk = risk
        if security_enhancement is not None:
            self.security_enhancement = security_enhancement
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if total_mem is not None:
            self.total_mem = total_mem
        if update_time is not None:
            self.update_time = update_time
        if user_status is not None:
            self.user_status = user_status
        if user_status_reason is not None:
            self.user_status_reason = user_status_reason

    @property
    def abnormal_plugins_list(self):
        """Gets the abnormal_plugins_list of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The abnormal_plugins_list of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._abnormal_plugins_list

    @abnormal_plugins_list.setter
    def abnormal_plugins_list(self, abnormal_plugins_list):
        """Sets the abnormal_plugins_list of this DataForListMultiLevelAssetHostsOutput.


        :param abnormal_plugins_list: The abnormal_plugins_list of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._abnormal_plugins_list = abnormal_plugins_list

    @property
    def account_id(self):
        """Gets the account_id of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The account_id of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForListMultiLevelAssetHostsOutput.


        :param account_id: The account_id of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The agent_id of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForListMultiLevelAssetHostsOutput.


        :param agent_id: The agent_id of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_status(self):
        """Gets the agent_status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The agent_status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_status

    @agent_status.setter
    def agent_status(self, agent_status):
        """Sets the agent_status of this DataForListMultiLevelAssetHostsOutput.


        :param agent_status: The agent_status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._agent_status = agent_status

    @property
    def cloud_provider(self):
        """Gets the cloud_provider of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The cloud_provider of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_provider

    @cloud_provider.setter
    def cloud_provider(self, cloud_provider):
        """Sets the cloud_provider of this DataForListMultiLevelAssetHostsOutput.


        :param cloud_provider: The cloud_provider of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._cloud_provider = cloud_provider

    @property
    def cpu_usage(self):
        """Gets the cpu_usage of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The cpu_usage of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: float
        """
        return self._cpu_usage

    @cpu_usage.setter
    def cpu_usage(self, cpu_usage):
        """Sets the cpu_usage of this DataForListMultiLevelAssetHostsOutput.


        :param cpu_usage: The cpu_usage of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: float
        """

        self._cpu_usage = cpu_usage

    @property
    def dns(self):
        """Gets the dns of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The dns of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._dns

    @dns.setter
    def dns(self, dns):
        """Sets the dns of this DataForListMultiLevelAssetHostsOutput.


        :param dns: The dns of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._dns = dns

    @property
    def ecs_instance(self):
        """Gets the ecs_instance of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The ecs_instance of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: EcsInstanceForListMultiLevelAssetHostsOutput
        """
        return self._ecs_instance

    @ecs_instance.setter
    def ecs_instance(self, ecs_instance):
        """Sets the ecs_instance of this DataForListMultiLevelAssetHostsOutput.


        :param ecs_instance: The ecs_instance of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: EcsInstanceForListMultiLevelAssetHostsOutput
        """

        self._ecs_instance = ecs_instance

    @property
    def first_heartbeat_time(self):
        """Gets the first_heartbeat_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The first_heartbeat_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: int
        """
        return self._first_heartbeat_time

    @first_heartbeat_time.setter
    def first_heartbeat_time(self, first_heartbeat_time):
        """Sets the first_heartbeat_time of this DataForListMultiLevelAssetHostsOutput.


        :param first_heartbeat_time: The first_heartbeat_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: int
        """

        self._first_heartbeat_time = first_heartbeat_time

    @property
    def gateway(self):
        """Gets the gateway of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The gateway of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._gateway

    @gateway.setter
    def gateway(self, gateway):
        """Sets the gateway of this DataForListMultiLevelAssetHostsOutput.


        :param gateway: The gateway of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._gateway = gateway

    @property
    def group(self):
        """Gets the group of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The group of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._group

    @group.setter
    def group(self, group):
        """Sets the group of this DataForListMultiLevelAssetHostsOutput.


        :param group: The group of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._group = group

    @property
    def group_path(self):
        """Gets the group_path of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The group_path of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_path

    @group_path.setter
    def group_path(self, group_path):
        """Sets the group_path of this DataForListMultiLevelAssetHostsOutput.


        :param group_path: The group_path of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._group_path = group_path

    @property
    def institution_id(self):
        """Gets the institution_id of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The institution_id of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._institution_id

    @institution_id.setter
    def institution_id(self, institution_id):
        """Sets the institution_id of this DataForListMultiLevelAssetHostsOutput.


        :param institution_id: The institution_id of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._institution_id = institution_id

    @property
    def kernel_version(self):
        """Gets the kernel_version of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The kernel_version of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._kernel_version

    @kernel_version.setter
    def kernel_version(self, kernel_version):
        """Sets the kernel_version of this DataForListMultiLevelAssetHostsOutput.


        :param kernel_version: The kernel_version of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._kernel_version = kernel_version

    @property
    def last_heartbeat_time(self):
        """Gets the last_heartbeat_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The last_heartbeat_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: int
        """
        return self._last_heartbeat_time

    @last_heartbeat_time.setter
    def last_heartbeat_time(self, last_heartbeat_time):
        """Sets the last_heartbeat_time of this DataForListMultiLevelAssetHostsOutput.


        :param last_heartbeat_time: The last_heartbeat_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: int
        """

        self._last_heartbeat_time = last_heartbeat_time

    @property
    def mem_usage(self):
        """Gets the mem_usage of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The mem_usage of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: float
        """
        return self._mem_usage

    @mem_usage.setter
    def mem_usage(self, mem_usage):
        """Sets the mem_usage of this DataForListMultiLevelAssetHostsOutput.


        :param mem_usage: The mem_usage of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: float
        """

        self._mem_usage = mem_usage

    @property
    def online(self):
        """Gets the online of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The online of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._online

    @online.setter
    def online(self, online):
        """Sets the online of this DataForListMultiLevelAssetHostsOutput.


        :param online: The online of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: bool
        """

        self._online = online

    @property
    def platform(self):
        """Gets the platform of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The platform of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this DataForListMultiLevelAssetHostsOutput.


        :param platform: The platform of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._platform = platform

    @property
    def plugins_brief_info(self):
        """Gets the plugins_brief_info of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The plugins_brief_info of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._plugins_brief_info

    @plugins_brief_info.setter
    def plugins_brief_info(self, plugins_brief_info):
        """Sets the plugins_brief_info of this DataForListMultiLevelAssetHostsOutput.


        :param plugins_brief_info: The plugins_brief_info of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._plugins_brief_info = plugins_brief_info

    @property
    def plugins_status(self):
        """Gets the plugins_status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The plugins_status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._plugins_status

    @plugins_status.setter
    def plugins_status(self, plugins_status):
        """Sets the plugins_status of this DataForListMultiLevelAssetHostsOutput.


        :param plugins_status: The plugins_status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._plugins_status = plugins_status

    @property
    def reason(self):
        """Gets the reason of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The reason of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this DataForListMultiLevelAssetHostsOutput.


        :param reason: The reason of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._reason = reason

    @property
    def risk(self):
        """Gets the risk of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The risk of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: RiskForListMultiLevelAssetHostsOutput
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this DataForListMultiLevelAssetHostsOutput.


        :param risk: The risk of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: RiskForListMultiLevelAssetHostsOutput
        """

        self._risk = risk

    @property
    def security_enhancement(self):
        """Gets the security_enhancement of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The security_enhancement of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._security_enhancement

    @security_enhancement.setter
    def security_enhancement(self, security_enhancement):
        """Sets the security_enhancement of this DataForListMultiLevelAssetHostsOutput.


        :param security_enhancement: The security_enhancement of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: bool
        """

        self._security_enhancement = security_enhancement

    @property
    def start_time(self):
        """Gets the start_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The start_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForListMultiLevelAssetHostsOutput.


        :param start_time: The start_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListMultiLevelAssetHostsOutput.


        :param status: The status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The tags of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DataForListMultiLevelAssetHostsOutput.


        :param tags: The tags of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def total_mem(self):
        """Gets the total_mem of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The total_mem of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_mem

    @total_mem.setter
    def total_mem(self, total_mem):
        """Sets the total_mem of this DataForListMultiLevelAssetHostsOutput.


        :param total_mem: The total_mem of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: int
        """

        self._total_mem = total_mem

    @property
    def update_time(self):
        """Gets the update_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The update_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForListMultiLevelAssetHostsOutput.


        :param update_time: The update_time of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def user_status(self):
        """Gets the user_status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The user_status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_status

    @user_status.setter
    def user_status(self, user_status):
        """Sets the user_status of this DataForListMultiLevelAssetHostsOutput.


        :param user_status: The user_status of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._user_status = user_status

    @property
    def user_status_reason(self):
        """Gets the user_status_reason of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501


        :return: The user_status_reason of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_status_reason

    @user_status_reason.setter
    def user_status_reason(self, user_status_reason):
        """Sets the user_status_reason of this DataForListMultiLevelAssetHostsOutput.


        :param user_status_reason: The user_status_reason of this DataForListMultiLevelAssetHostsOutput.  # noqa: E501
        :type: str
        """

        self._user_status_reason = user_status_reason

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListMultiLevelAssetHostsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListMultiLevelAssetHostsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListMultiLevelAssetHostsOutput):
            return True

        return self.to_dict() != other.to_dict()
