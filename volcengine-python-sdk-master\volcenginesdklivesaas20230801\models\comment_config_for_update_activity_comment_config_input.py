# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CommentConfigForUpdateActivityCommentConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'hot_list_count': 'int',
        'hot_list_name': 'str',
        'input_box_prompt': 'str',
        'is_auto_silence_all_enable': 'int',
        'is_bullet_screen_enable': 'int',
        'is_comment_llm_analyze_enable': 'int',
        'is_comment_translate_enable': 'int',
        'is_hot_list_enable': 'int',
        'is_image_comment_enable': 'int',
        'is_like_number_show_enable': 'int',
        'is_manual_hot_list_enable': 'int',
        'is_send_comment_enable': 'int',
        'is_view_in_purchase_enable': 'int',
        'is_view_order_enable': 'int',
        'is_viewer_delete_comments_enable': 'int',
        'is_welcome_message_enable': 'int',
        'menu_name': 'str',
        'presenter_name': 'str',
        'voice_interval': 'int',
        'welcome_message_content': 'str',
        'welcome_message_title': 'str'
    }

    attribute_map = {
        'hot_list_count': 'HotListCount',
        'hot_list_name': 'HotListName',
        'input_box_prompt': 'InputBoxPrompt',
        'is_auto_silence_all_enable': 'IsAutoSilenceAllEnable',
        'is_bullet_screen_enable': 'IsBulletScreenEnable',
        'is_comment_llm_analyze_enable': 'IsCommentLLMAnalyzeEnable',
        'is_comment_translate_enable': 'IsCommentTranslateEnable',
        'is_hot_list_enable': 'IsHotListEnable',
        'is_image_comment_enable': 'IsImageCommentEnable',
        'is_like_number_show_enable': 'IsLikeNumberShowEnable',
        'is_manual_hot_list_enable': 'IsManualHotListEnable',
        'is_send_comment_enable': 'IsSendCommentEnable',
        'is_view_in_purchase_enable': 'IsViewInPurchaseEnable',
        'is_view_order_enable': 'IsViewOrderEnable',
        'is_viewer_delete_comments_enable': 'IsViewerDeleteCommentsEnable',
        'is_welcome_message_enable': 'IsWelcomeMessageEnable',
        'menu_name': 'MenuName',
        'presenter_name': 'PresenterName',
        'voice_interval': 'VoiceInterval',
        'welcome_message_content': 'WelcomeMessageContent',
        'welcome_message_title': 'WelcomeMessageTitle'
    }

    def __init__(self, hot_list_count=None, hot_list_name=None, input_box_prompt=None, is_auto_silence_all_enable=None, is_bullet_screen_enable=None, is_comment_llm_analyze_enable=None, is_comment_translate_enable=None, is_hot_list_enable=None, is_image_comment_enable=None, is_like_number_show_enable=None, is_manual_hot_list_enable=None, is_send_comment_enable=None, is_view_in_purchase_enable=None, is_view_order_enable=None, is_viewer_delete_comments_enable=None, is_welcome_message_enable=None, menu_name=None, presenter_name=None, voice_interval=None, welcome_message_content=None, welcome_message_title=None, _configuration=None):  # noqa: E501
        """CommentConfigForUpdateActivityCommentConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._hot_list_count = None
        self._hot_list_name = None
        self._input_box_prompt = None
        self._is_auto_silence_all_enable = None
        self._is_bullet_screen_enable = None
        self._is_comment_llm_analyze_enable = None
        self._is_comment_translate_enable = None
        self._is_hot_list_enable = None
        self._is_image_comment_enable = None
        self._is_like_number_show_enable = None
        self._is_manual_hot_list_enable = None
        self._is_send_comment_enable = None
        self._is_view_in_purchase_enable = None
        self._is_view_order_enable = None
        self._is_viewer_delete_comments_enable = None
        self._is_welcome_message_enable = None
        self._menu_name = None
        self._presenter_name = None
        self._voice_interval = None
        self._welcome_message_content = None
        self._welcome_message_title = None
        self.discriminator = None

        if hot_list_count is not None:
            self.hot_list_count = hot_list_count
        if hot_list_name is not None:
            self.hot_list_name = hot_list_name
        if input_box_prompt is not None:
            self.input_box_prompt = input_box_prompt
        if is_auto_silence_all_enable is not None:
            self.is_auto_silence_all_enable = is_auto_silence_all_enable
        if is_bullet_screen_enable is not None:
            self.is_bullet_screen_enable = is_bullet_screen_enable
        if is_comment_llm_analyze_enable is not None:
            self.is_comment_llm_analyze_enable = is_comment_llm_analyze_enable
        if is_comment_translate_enable is not None:
            self.is_comment_translate_enable = is_comment_translate_enable
        if is_hot_list_enable is not None:
            self.is_hot_list_enable = is_hot_list_enable
        if is_image_comment_enable is not None:
            self.is_image_comment_enable = is_image_comment_enable
        if is_like_number_show_enable is not None:
            self.is_like_number_show_enable = is_like_number_show_enable
        if is_manual_hot_list_enable is not None:
            self.is_manual_hot_list_enable = is_manual_hot_list_enable
        if is_send_comment_enable is not None:
            self.is_send_comment_enable = is_send_comment_enable
        if is_view_in_purchase_enable is not None:
            self.is_view_in_purchase_enable = is_view_in_purchase_enable
        if is_view_order_enable is not None:
            self.is_view_order_enable = is_view_order_enable
        if is_viewer_delete_comments_enable is not None:
            self.is_viewer_delete_comments_enable = is_viewer_delete_comments_enable
        if is_welcome_message_enable is not None:
            self.is_welcome_message_enable = is_welcome_message_enable
        if menu_name is not None:
            self.menu_name = menu_name
        if presenter_name is not None:
            self.presenter_name = presenter_name
        if voice_interval is not None:
            self.voice_interval = voice_interval
        if welcome_message_content is not None:
            self.welcome_message_content = welcome_message_content
        if welcome_message_title is not None:
            self.welcome_message_title = welcome_message_title

    @property
    def hot_list_count(self):
        """Gets the hot_list_count of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The hot_list_count of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._hot_list_count

    @hot_list_count.setter
    def hot_list_count(self, hot_list_count):
        """Sets the hot_list_count of this CommentConfigForUpdateActivityCommentConfigInput.


        :param hot_list_count: The hot_list_count of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._hot_list_count = hot_list_count

    @property
    def hot_list_name(self):
        """Gets the hot_list_name of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The hot_list_name of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._hot_list_name

    @hot_list_name.setter
    def hot_list_name(self, hot_list_name):
        """Sets the hot_list_name of this CommentConfigForUpdateActivityCommentConfigInput.


        :param hot_list_name: The hot_list_name of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: str
        """

        self._hot_list_name = hot_list_name

    @property
    def input_box_prompt(self):
        """Gets the input_box_prompt of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The input_box_prompt of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._input_box_prompt

    @input_box_prompt.setter
    def input_box_prompt(self, input_box_prompt):
        """Sets the input_box_prompt of this CommentConfigForUpdateActivityCommentConfigInput.


        :param input_box_prompt: The input_box_prompt of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: str
        """

        self._input_box_prompt = input_box_prompt

    @property
    def is_auto_silence_all_enable(self):
        """Gets the is_auto_silence_all_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_auto_silence_all_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_auto_silence_all_enable

    @is_auto_silence_all_enable.setter
    def is_auto_silence_all_enable(self, is_auto_silence_all_enable):
        """Sets the is_auto_silence_all_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_auto_silence_all_enable: The is_auto_silence_all_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_auto_silence_all_enable = is_auto_silence_all_enable

    @property
    def is_bullet_screen_enable(self):
        """Gets the is_bullet_screen_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_bullet_screen_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_bullet_screen_enable

    @is_bullet_screen_enable.setter
    def is_bullet_screen_enable(self, is_bullet_screen_enable):
        """Sets the is_bullet_screen_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_bullet_screen_enable: The is_bullet_screen_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_bullet_screen_enable = is_bullet_screen_enable

    @property
    def is_comment_llm_analyze_enable(self):
        """Gets the is_comment_llm_analyze_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_comment_llm_analyze_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_comment_llm_analyze_enable

    @is_comment_llm_analyze_enable.setter
    def is_comment_llm_analyze_enable(self, is_comment_llm_analyze_enable):
        """Sets the is_comment_llm_analyze_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_comment_llm_analyze_enable: The is_comment_llm_analyze_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_comment_llm_analyze_enable = is_comment_llm_analyze_enable

    @property
    def is_comment_translate_enable(self):
        """Gets the is_comment_translate_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_comment_translate_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_comment_translate_enable

    @is_comment_translate_enable.setter
    def is_comment_translate_enable(self, is_comment_translate_enable):
        """Sets the is_comment_translate_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_comment_translate_enable: The is_comment_translate_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_comment_translate_enable = is_comment_translate_enable

    @property
    def is_hot_list_enable(self):
        """Gets the is_hot_list_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_hot_list_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_hot_list_enable

    @is_hot_list_enable.setter
    def is_hot_list_enable(self, is_hot_list_enable):
        """Sets the is_hot_list_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_hot_list_enable: The is_hot_list_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_hot_list_enable = is_hot_list_enable

    @property
    def is_image_comment_enable(self):
        """Gets the is_image_comment_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_image_comment_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_image_comment_enable

    @is_image_comment_enable.setter
    def is_image_comment_enable(self, is_image_comment_enable):
        """Sets the is_image_comment_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_image_comment_enable: The is_image_comment_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_image_comment_enable = is_image_comment_enable

    @property
    def is_like_number_show_enable(self):
        """Gets the is_like_number_show_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_like_number_show_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_like_number_show_enable

    @is_like_number_show_enable.setter
    def is_like_number_show_enable(self, is_like_number_show_enable):
        """Sets the is_like_number_show_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_like_number_show_enable: The is_like_number_show_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_like_number_show_enable = is_like_number_show_enable

    @property
    def is_manual_hot_list_enable(self):
        """Gets the is_manual_hot_list_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_manual_hot_list_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_manual_hot_list_enable

    @is_manual_hot_list_enable.setter
    def is_manual_hot_list_enable(self, is_manual_hot_list_enable):
        """Sets the is_manual_hot_list_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_manual_hot_list_enable: The is_manual_hot_list_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_manual_hot_list_enable = is_manual_hot_list_enable

    @property
    def is_send_comment_enable(self):
        """Gets the is_send_comment_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_send_comment_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_send_comment_enable

    @is_send_comment_enable.setter
    def is_send_comment_enable(self, is_send_comment_enable):
        """Sets the is_send_comment_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_send_comment_enable: The is_send_comment_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_send_comment_enable = is_send_comment_enable

    @property
    def is_view_in_purchase_enable(self):
        """Gets the is_view_in_purchase_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_view_in_purchase_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_view_in_purchase_enable

    @is_view_in_purchase_enable.setter
    def is_view_in_purchase_enable(self, is_view_in_purchase_enable):
        """Sets the is_view_in_purchase_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_view_in_purchase_enable: The is_view_in_purchase_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_view_in_purchase_enable = is_view_in_purchase_enable

    @property
    def is_view_order_enable(self):
        """Gets the is_view_order_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_view_order_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_view_order_enable

    @is_view_order_enable.setter
    def is_view_order_enable(self, is_view_order_enable):
        """Sets the is_view_order_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_view_order_enable: The is_view_order_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_view_order_enable = is_view_order_enable

    @property
    def is_viewer_delete_comments_enable(self):
        """Gets the is_viewer_delete_comments_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_viewer_delete_comments_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_viewer_delete_comments_enable

    @is_viewer_delete_comments_enable.setter
    def is_viewer_delete_comments_enable(self, is_viewer_delete_comments_enable):
        """Sets the is_viewer_delete_comments_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_viewer_delete_comments_enable: The is_viewer_delete_comments_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_viewer_delete_comments_enable = is_viewer_delete_comments_enable

    @property
    def is_welcome_message_enable(self):
        """Gets the is_welcome_message_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The is_welcome_message_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_welcome_message_enable

    @is_welcome_message_enable.setter
    def is_welcome_message_enable(self, is_welcome_message_enable):
        """Sets the is_welcome_message_enable of this CommentConfigForUpdateActivityCommentConfigInput.


        :param is_welcome_message_enable: The is_welcome_message_enable of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_welcome_message_enable = is_welcome_message_enable

    @property
    def menu_name(self):
        """Gets the menu_name of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The menu_name of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._menu_name

    @menu_name.setter
    def menu_name(self, menu_name):
        """Sets the menu_name of this CommentConfigForUpdateActivityCommentConfigInput.


        :param menu_name: The menu_name of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: str
        """

        self._menu_name = menu_name

    @property
    def presenter_name(self):
        """Gets the presenter_name of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The presenter_name of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._presenter_name

    @presenter_name.setter
    def presenter_name(self, presenter_name):
        """Sets the presenter_name of this CommentConfigForUpdateActivityCommentConfigInput.


        :param presenter_name: The presenter_name of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: str
        """

        self._presenter_name = presenter_name

    @property
    def voice_interval(self):
        """Gets the voice_interval of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The voice_interval of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._voice_interval

    @voice_interval.setter
    def voice_interval(self, voice_interval):
        """Sets the voice_interval of this CommentConfigForUpdateActivityCommentConfigInput.


        :param voice_interval: The voice_interval of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._voice_interval = voice_interval

    @property
    def welcome_message_content(self):
        """Gets the welcome_message_content of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The welcome_message_content of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._welcome_message_content

    @welcome_message_content.setter
    def welcome_message_content(self, welcome_message_content):
        """Sets the welcome_message_content of this CommentConfigForUpdateActivityCommentConfigInput.


        :param welcome_message_content: The welcome_message_content of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: str
        """

        self._welcome_message_content = welcome_message_content

    @property
    def welcome_message_title(self):
        """Gets the welcome_message_title of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501


        :return: The welcome_message_title of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._welcome_message_title

    @welcome_message_title.setter
    def welcome_message_title(self, welcome_message_title):
        """Sets the welcome_message_title of this CommentConfigForUpdateActivityCommentConfigInput.


        :param welcome_message_title: The welcome_message_title of this CommentConfigForUpdateActivityCommentConfigInput.  # noqa: E501
        :type: str
        """

        self._welcome_message_title = welcome_message_title

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CommentConfigForUpdateActivityCommentConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CommentConfigForUpdateActivityCommentConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CommentConfigForUpdateActivityCommentConfigInput):
            return True

        return self.to_dict() != other.to_dict()
