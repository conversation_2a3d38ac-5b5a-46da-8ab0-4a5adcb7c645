# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetRepoImageScanScopeOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'image_size_limit': 'int',
        'latest_push': 'int',
        'namespace_ids': 'list[str]',
        'scan_items': 'list[str]',
        'timeout': 'int'
    }

    attribute_map = {
        'image_size_limit': 'ImageSizeLimit',
        'latest_push': 'LatestPush',
        'namespace_ids': 'NamespaceIDs',
        'scan_items': 'ScanItems',
        'timeout': 'Timeout'
    }

    def __init__(self, image_size_limit=None, latest_push=None, namespace_ids=None, scan_items=None, timeout=None, _configuration=None):  # noqa: E501
        """DataForGetRepoImageScanScopeOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._image_size_limit = None
        self._latest_push = None
        self._namespace_ids = None
        self._scan_items = None
        self._timeout = None
        self.discriminator = None

        if image_size_limit is not None:
            self.image_size_limit = image_size_limit
        if latest_push is not None:
            self.latest_push = latest_push
        if namespace_ids is not None:
            self.namespace_ids = namespace_ids
        if scan_items is not None:
            self.scan_items = scan_items
        if timeout is not None:
            self.timeout = timeout

    @property
    def image_size_limit(self):
        """Gets the image_size_limit of this DataForGetRepoImageScanScopeOutput.  # noqa: E501


        :return: The image_size_limit of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :rtype: int
        """
        return self._image_size_limit

    @image_size_limit.setter
    def image_size_limit(self, image_size_limit):
        """Sets the image_size_limit of this DataForGetRepoImageScanScopeOutput.


        :param image_size_limit: The image_size_limit of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :type: int
        """

        self._image_size_limit = image_size_limit

    @property
    def latest_push(self):
        """Gets the latest_push of this DataForGetRepoImageScanScopeOutput.  # noqa: E501


        :return: The latest_push of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :rtype: int
        """
        return self._latest_push

    @latest_push.setter
    def latest_push(self, latest_push):
        """Sets the latest_push of this DataForGetRepoImageScanScopeOutput.


        :param latest_push: The latest_push of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :type: int
        """

        self._latest_push = latest_push

    @property
    def namespace_ids(self):
        """Gets the namespace_ids of this DataForGetRepoImageScanScopeOutput.  # noqa: E501


        :return: The namespace_ids of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._namespace_ids

    @namespace_ids.setter
    def namespace_ids(self, namespace_ids):
        """Sets the namespace_ids of this DataForGetRepoImageScanScopeOutput.


        :param namespace_ids: The namespace_ids of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :type: list[str]
        """

        self._namespace_ids = namespace_ids

    @property
    def scan_items(self):
        """Gets the scan_items of this DataForGetRepoImageScanScopeOutput.  # noqa: E501


        :return: The scan_items of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._scan_items

    @scan_items.setter
    def scan_items(self, scan_items):
        """Sets the scan_items of this DataForGetRepoImageScanScopeOutput.


        :param scan_items: The scan_items of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :type: list[str]
        """

        self._scan_items = scan_items

    @property
    def timeout(self):
        """Gets the timeout of this DataForGetRepoImageScanScopeOutput.  # noqa: E501


        :return: The timeout of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this DataForGetRepoImageScanScopeOutput.


        :param timeout: The timeout of this DataForGetRepoImageScanScopeOutput.  # noqa: E501
        :type: int
        """

        self._timeout = timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetRepoImageScanScopeOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetRepoImageScanScopeOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetRepoImageScanScopeOutput):
            return True

        return self.to_dict() != other.to_dict()
