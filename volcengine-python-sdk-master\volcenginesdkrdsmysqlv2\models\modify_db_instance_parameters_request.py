# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBInstanceParametersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_node_ids': 'list[str]',
        'instance_id': 'str',
        'param_apply_scope': 'str',
        'parameters': 'list[ParameterForModifyDBInstanceParametersInput]'
    }

    attribute_map = {
        'custom_node_ids': 'CustomNodeIds',
        'instance_id': 'InstanceId',
        'param_apply_scope': 'ParamApplyScope',
        'parameters': 'Parameters'
    }

    def __init__(self, custom_node_ids=None, instance_id=None, param_apply_scope=None, parameters=None, _configuration=None):  # noqa: E501
        """ModifyDBInstanceParametersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_node_ids = None
        self._instance_id = None
        self._param_apply_scope = None
        self._parameters = None
        self.discriminator = None

        if custom_node_ids is not None:
            self.custom_node_ids = custom_node_ids
        self.instance_id = instance_id
        if param_apply_scope is not None:
            self.param_apply_scope = param_apply_scope
        if parameters is not None:
            self.parameters = parameters

    @property
    def custom_node_ids(self):
        """Gets the custom_node_ids of this ModifyDBInstanceParametersRequest.  # noqa: E501


        :return: The custom_node_ids of this ModifyDBInstanceParametersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._custom_node_ids

    @custom_node_ids.setter
    def custom_node_ids(self, custom_node_ids):
        """Sets the custom_node_ids of this ModifyDBInstanceParametersRequest.


        :param custom_node_ids: The custom_node_ids of this ModifyDBInstanceParametersRequest.  # noqa: E501
        :type: list[str]
        """

        self._custom_node_ids = custom_node_ids

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBInstanceParametersRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBInstanceParametersRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBInstanceParametersRequest.


        :param instance_id: The instance_id of this ModifyDBInstanceParametersRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def param_apply_scope(self):
        """Gets the param_apply_scope of this ModifyDBInstanceParametersRequest.  # noqa: E501


        :return: The param_apply_scope of this ModifyDBInstanceParametersRequest.  # noqa: E501
        :rtype: str
        """
        return self._param_apply_scope

    @param_apply_scope.setter
    def param_apply_scope(self, param_apply_scope):
        """Sets the param_apply_scope of this ModifyDBInstanceParametersRequest.


        :param param_apply_scope: The param_apply_scope of this ModifyDBInstanceParametersRequest.  # noqa: E501
        :type: str
        """

        self._param_apply_scope = param_apply_scope

    @property
    def parameters(self):
        """Gets the parameters of this ModifyDBInstanceParametersRequest.  # noqa: E501


        :return: The parameters of this ModifyDBInstanceParametersRequest.  # noqa: E501
        :rtype: list[ParameterForModifyDBInstanceParametersInput]
        """
        return self._parameters

    @parameters.setter
    def parameters(self, parameters):
        """Sets the parameters of this ModifyDBInstanceParametersRequest.


        :param parameters: The parameters of this ModifyDBInstanceParametersRequest.  # noqa: E501
        :type: list[ParameterForModifyDBInstanceParametersInput]
        """

        self._parameters = parameters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBInstanceParametersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBInstanceParametersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBInstanceParametersRequest):
            return True

        return self.to_dict() != other.to_dict()
