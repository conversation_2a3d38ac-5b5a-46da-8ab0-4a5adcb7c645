# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AvailableZoneForDescribeAvailableResourceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'available_resources': 'list[AvailableResourceForDescribeAvailableResourceOutput]',
        'region_id': 'str',
        'status': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'available_resources': 'AvailableResources',
        'region_id': 'RegionId',
        'status': 'Status',
        'zone_id': 'ZoneId'
    }

    def __init__(self, available_resources=None, region_id=None, status=None, zone_id=None, _configuration=None):  # noqa: E501
        """AvailableZoneForDescribeAvailableResourceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._available_resources = None
        self._region_id = None
        self._status = None
        self._zone_id = None
        self.discriminator = None

        if available_resources is not None:
            self.available_resources = available_resources
        if region_id is not None:
            self.region_id = region_id
        if status is not None:
            self.status = status
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def available_resources(self):
        """Gets the available_resources of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501


        :return: The available_resources of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501
        :rtype: list[AvailableResourceForDescribeAvailableResourceOutput]
        """
        return self._available_resources

    @available_resources.setter
    def available_resources(self, available_resources):
        """Sets the available_resources of this AvailableZoneForDescribeAvailableResourceOutput.


        :param available_resources: The available_resources of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501
        :type: list[AvailableResourceForDescribeAvailableResourceOutput]
        """

        self._available_resources = available_resources

    @property
    def region_id(self):
        """Gets the region_id of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501


        :return: The region_id of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this AvailableZoneForDescribeAvailableResourceOutput.


        :param region_id: The region_id of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def status(self):
        """Gets the status of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501


        :return: The status of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this AvailableZoneForDescribeAvailableResourceOutput.


        :param status: The status of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def zone_id(self):
        """Gets the zone_id of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501


        :return: The zone_id of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this AvailableZoneForDescribeAvailableResourceOutput.


        :param zone_id: The zone_id of this AvailableZoneForDescribeAvailableResourceOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AvailableZoneForDescribeAvailableResourceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AvailableZoneForDescribeAvailableResourceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AvailableZoneForDescribeAvailableResourceOutput):
            return True

        return self.to_dict() != other.to_dict()
