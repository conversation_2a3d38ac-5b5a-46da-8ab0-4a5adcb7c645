# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TCPDetailForGetTaskResultOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'diagnose_detail': 'DiagnoseDetailForGetTaskResultOutput',
        'tcp_response': 'str',
        'tcp_time_connect': 'int',
        'tcp_time_dns': 'int',
        'tcp_time_receive': 'int',
        'tcp_time_response': 'int',
        'tcp_time_total': 'int'
    }

    attribute_map = {
        'diagnose_detail': 'DiagnoseDetail',
        'tcp_response': 'TCPResponse',
        'tcp_time_connect': 'TCPTimeConnect',
        'tcp_time_dns': 'TCPTimeDNS',
        'tcp_time_receive': 'TCPTimeReceive',
        'tcp_time_response': 'TCPTimeResponse',
        'tcp_time_total': 'TCPTimeTotal'
    }

    def __init__(self, diagnose_detail=None, tcp_response=None, tcp_time_connect=None, tcp_time_dns=None, tcp_time_receive=None, tcp_time_response=None, tcp_time_total=None, _configuration=None):  # noqa: E501
        """TCPDetailForGetTaskResultOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._diagnose_detail = None
        self._tcp_response = None
        self._tcp_time_connect = None
        self._tcp_time_dns = None
        self._tcp_time_receive = None
        self._tcp_time_response = None
        self._tcp_time_total = None
        self.discriminator = None

        if diagnose_detail is not None:
            self.diagnose_detail = diagnose_detail
        if tcp_response is not None:
            self.tcp_response = tcp_response
        if tcp_time_connect is not None:
            self.tcp_time_connect = tcp_time_connect
        if tcp_time_dns is not None:
            self.tcp_time_dns = tcp_time_dns
        if tcp_time_receive is not None:
            self.tcp_time_receive = tcp_time_receive
        if tcp_time_response is not None:
            self.tcp_time_response = tcp_time_response
        if tcp_time_total is not None:
            self.tcp_time_total = tcp_time_total

    @property
    def diagnose_detail(self):
        """Gets the diagnose_detail of this TCPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The diagnose_detail of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: DiagnoseDetailForGetTaskResultOutput
        """
        return self._diagnose_detail

    @diagnose_detail.setter
    def diagnose_detail(self, diagnose_detail):
        """Sets the diagnose_detail of this TCPDetailForGetTaskResultOutput.


        :param diagnose_detail: The diagnose_detail of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :type: DiagnoseDetailForGetTaskResultOutput
        """

        self._diagnose_detail = diagnose_detail

    @property
    def tcp_response(self):
        """Gets the tcp_response of this TCPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The tcp_response of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: str
        """
        return self._tcp_response

    @tcp_response.setter
    def tcp_response(self, tcp_response):
        """Sets the tcp_response of this TCPDetailForGetTaskResultOutput.


        :param tcp_response: The tcp_response of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :type: str
        """

        self._tcp_response = tcp_response

    @property
    def tcp_time_connect(self):
        """Gets the tcp_time_connect of this TCPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The tcp_time_connect of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._tcp_time_connect

    @tcp_time_connect.setter
    def tcp_time_connect(self, tcp_time_connect):
        """Sets the tcp_time_connect of this TCPDetailForGetTaskResultOutput.


        :param tcp_time_connect: The tcp_time_connect of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._tcp_time_connect = tcp_time_connect

    @property
    def tcp_time_dns(self):
        """Gets the tcp_time_dns of this TCPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The tcp_time_dns of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._tcp_time_dns

    @tcp_time_dns.setter
    def tcp_time_dns(self, tcp_time_dns):
        """Sets the tcp_time_dns of this TCPDetailForGetTaskResultOutput.


        :param tcp_time_dns: The tcp_time_dns of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._tcp_time_dns = tcp_time_dns

    @property
    def tcp_time_receive(self):
        """Gets the tcp_time_receive of this TCPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The tcp_time_receive of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._tcp_time_receive

    @tcp_time_receive.setter
    def tcp_time_receive(self, tcp_time_receive):
        """Sets the tcp_time_receive of this TCPDetailForGetTaskResultOutput.


        :param tcp_time_receive: The tcp_time_receive of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._tcp_time_receive = tcp_time_receive

    @property
    def tcp_time_response(self):
        """Gets the tcp_time_response of this TCPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The tcp_time_response of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._tcp_time_response

    @tcp_time_response.setter
    def tcp_time_response(self, tcp_time_response):
        """Sets the tcp_time_response of this TCPDetailForGetTaskResultOutput.


        :param tcp_time_response: The tcp_time_response of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._tcp_time_response = tcp_time_response

    @property
    def tcp_time_total(self):
        """Gets the tcp_time_total of this TCPDetailForGetTaskResultOutput.  # noqa: E501


        :return: The tcp_time_total of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :rtype: int
        """
        return self._tcp_time_total

    @tcp_time_total.setter
    def tcp_time_total(self, tcp_time_total):
        """Sets the tcp_time_total of this TCPDetailForGetTaskResultOutput.


        :param tcp_time_total: The tcp_time_total of this TCPDetailForGetTaskResultOutput.  # noqa: E501
        :type: int
        """

        self._tcp_time_total = tcp_time_total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TCPDetailForGetTaskResultOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TCPDetailForGetTaskResultOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TCPDetailForGetTaskResultOutput):
            return True

        return self.to_dict() != other.to_dict()
