# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateServiceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'apig_config': 'ApigConfigForUpdateServiceInput',
        'clb_config': 'CLBConfigForUpdateServiceInput',
        'description': 'str',
        'dry_run': 'bool',
        'id': 'str',
        'ports': 'list[PortForUpdateServiceInput]',
        'service_name': 'str',
        'traffic_config': 'TrafficConfigForUpdateServiceInput'
    }

    attribute_map = {
        'apig_config': 'ApigConfig',
        'clb_config': 'CLBConfig',
        'description': 'Description',
        'dry_run': 'DryRun',
        'id': 'Id',
        'ports': 'Ports',
        'service_name': 'ServiceName',
        'traffic_config': 'TrafficConfig'
    }

    def __init__(self, apig_config=None, clb_config=None, description=None, dry_run=None, id=None, ports=None, service_name=None, traffic_config=None, _configuration=None):  # noqa: E501
        """UpdateServiceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._apig_config = None
        self._clb_config = None
        self._description = None
        self._dry_run = None
        self._id = None
        self._ports = None
        self._service_name = None
        self._traffic_config = None
        self.discriminator = None

        if apig_config is not None:
            self.apig_config = apig_config
        if clb_config is not None:
            self.clb_config = clb_config
        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        self.id = id
        if ports is not None:
            self.ports = ports
        if service_name is not None:
            self.service_name = service_name
        if traffic_config is not None:
            self.traffic_config = traffic_config

    @property
    def apig_config(self):
        """Gets the apig_config of this UpdateServiceRequest.  # noqa: E501


        :return: The apig_config of this UpdateServiceRequest.  # noqa: E501
        :rtype: ApigConfigForUpdateServiceInput
        """
        return self._apig_config

    @apig_config.setter
    def apig_config(self, apig_config):
        """Sets the apig_config of this UpdateServiceRequest.


        :param apig_config: The apig_config of this UpdateServiceRequest.  # noqa: E501
        :type: ApigConfigForUpdateServiceInput
        """

        self._apig_config = apig_config

    @property
    def clb_config(self):
        """Gets the clb_config of this UpdateServiceRequest.  # noqa: E501


        :return: The clb_config of this UpdateServiceRequest.  # noqa: E501
        :rtype: CLBConfigForUpdateServiceInput
        """
        return self._clb_config

    @clb_config.setter
    def clb_config(self, clb_config):
        """Sets the clb_config of this UpdateServiceRequest.


        :param clb_config: The clb_config of this UpdateServiceRequest.  # noqa: E501
        :type: CLBConfigForUpdateServiceInput
        """

        self._clb_config = clb_config

    @property
    def description(self):
        """Gets the description of this UpdateServiceRequest.  # noqa: E501


        :return: The description of this UpdateServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateServiceRequest.


        :param description: The description of this UpdateServiceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this UpdateServiceRequest.  # noqa: E501


        :return: The dry_run of this UpdateServiceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this UpdateServiceRequest.


        :param dry_run: The dry_run of this UpdateServiceRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def id(self):
        """Gets the id of this UpdateServiceRequest.  # noqa: E501


        :return: The id of this UpdateServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateServiceRequest.


        :param id: The id of this UpdateServiceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def ports(self):
        """Gets the ports of this UpdateServiceRequest.  # noqa: E501


        :return: The ports of this UpdateServiceRequest.  # noqa: E501
        :rtype: list[PortForUpdateServiceInput]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this UpdateServiceRequest.


        :param ports: The ports of this UpdateServiceRequest.  # noqa: E501
        :type: list[PortForUpdateServiceInput]
        """

        self._ports = ports

    @property
    def service_name(self):
        """Gets the service_name of this UpdateServiceRequest.  # noqa: E501


        :return: The service_name of this UpdateServiceRequest.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this UpdateServiceRequest.


        :param service_name: The service_name of this UpdateServiceRequest.  # noqa: E501
        :type: str
        """

        self._service_name = service_name

    @property
    def traffic_config(self):
        """Gets the traffic_config of this UpdateServiceRequest.  # noqa: E501


        :return: The traffic_config of this UpdateServiceRequest.  # noqa: E501
        :rtype: TrafficConfigForUpdateServiceInput
        """
        return self._traffic_config

    @traffic_config.setter
    def traffic_config(self, traffic_config):
        """Sets the traffic_config of this UpdateServiceRequest.


        :param traffic_config: The traffic_config of this UpdateServiceRequest.  # noqa: E501
        :type: TrafficConfigForUpdateServiceInput
        """

        self._traffic_config = traffic_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateServiceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateServiceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateServiceRequest):
            return True

        return self.to_dict() != other.to_dict()
