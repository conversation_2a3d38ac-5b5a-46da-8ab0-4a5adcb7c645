# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAccountActivityDataRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_name': 'str',
        'end_live_time': 'int',
        'page_number': 'int',
        'page_size': 'int',
        'page_token': 'str',
        'play_status': 'str',
        'select_tags': 'list[SelectTagForListAccountActivityDataInput]',
        'sort_field': 'str',
        'sort_mode': 'str',
        'start_live_time': 'int'
    }

    attribute_map = {
        'activity_name': 'ActivityName',
        'end_live_time': 'EndLiveTime',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'page_token': 'PageToken',
        'play_status': 'PlayStatus',
        'select_tags': 'SelectTags',
        'sort_field': 'SortField',
        'sort_mode': 'SortMode',
        'start_live_time': 'StartLiveTime'
    }

    def __init__(self, activity_name=None, end_live_time=None, page_number=None, page_size=None, page_token=None, play_status=None, select_tags=None, sort_field=None, sort_mode=None, start_live_time=None, _configuration=None):  # noqa: E501
        """ListAccountActivityDataRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_name = None
        self._end_live_time = None
        self._page_number = None
        self._page_size = None
        self._page_token = None
        self._play_status = None
        self._select_tags = None
        self._sort_field = None
        self._sort_mode = None
        self._start_live_time = None
        self.discriminator = None

        if activity_name is not None:
            self.activity_name = activity_name
        self.end_live_time = end_live_time
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if page_token is not None:
            self.page_token = page_token
        if play_status is not None:
            self.play_status = play_status
        if select_tags is not None:
            self.select_tags = select_tags
        if sort_field is not None:
            self.sort_field = sort_field
        if sort_mode is not None:
            self.sort_mode = sort_mode
        self.start_live_time = start_live_time

    @property
    def activity_name(self):
        """Gets the activity_name of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The activity_name of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._activity_name

    @activity_name.setter
    def activity_name(self, activity_name):
        """Sets the activity_name of this ListAccountActivityDataRequest.


        :param activity_name: The activity_name of this ListAccountActivityDataRequest.  # noqa: E501
        :type: str
        """

        self._activity_name = activity_name

    @property
    def end_live_time(self):
        """Gets the end_live_time of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The end_live_time of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_live_time

    @end_live_time.setter
    def end_live_time(self, end_live_time):
        """Sets the end_live_time of this ListAccountActivityDataRequest.


        :param end_live_time: The end_live_time of this ListAccountActivityDataRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_live_time is None:
            raise ValueError("Invalid value for `end_live_time`, must not be `None`")  # noqa: E501

        self._end_live_time = end_live_time

    @property
    def page_number(self):
        """Gets the page_number of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The page_number of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListAccountActivityDataRequest.


        :param page_number: The page_number of this ListAccountActivityDataRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The page_size of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListAccountActivityDataRequest.


        :param page_size: The page_size of this ListAccountActivityDataRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def page_token(self):
        """Gets the page_token of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The page_token of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_token

    @page_token.setter
    def page_token(self, page_token):
        """Sets the page_token of this ListAccountActivityDataRequest.


        :param page_token: The page_token of this ListAccountActivityDataRequest.  # noqa: E501
        :type: str
        """

        self._page_token = page_token

    @property
    def play_status(self):
        """Gets the play_status of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The play_status of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._play_status

    @play_status.setter
    def play_status(self, play_status):
        """Sets the play_status of this ListAccountActivityDataRequest.


        :param play_status: The play_status of this ListAccountActivityDataRequest.  # noqa: E501
        :type: str
        """

        self._play_status = play_status

    @property
    def select_tags(self):
        """Gets the select_tags of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The select_tags of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: list[SelectTagForListAccountActivityDataInput]
        """
        return self._select_tags

    @select_tags.setter
    def select_tags(self, select_tags):
        """Sets the select_tags of this ListAccountActivityDataRequest.


        :param select_tags: The select_tags of this ListAccountActivityDataRequest.  # noqa: E501
        :type: list[SelectTagForListAccountActivityDataInput]
        """

        self._select_tags = select_tags

    @property
    def sort_field(self):
        """Gets the sort_field of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The sort_field of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_field

    @sort_field.setter
    def sort_field(self, sort_field):
        """Sets the sort_field of this ListAccountActivityDataRequest.


        :param sort_field: The sort_field of this ListAccountActivityDataRequest.  # noqa: E501
        :type: str
        """

        self._sort_field = sort_field

    @property
    def sort_mode(self):
        """Gets the sort_mode of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The sort_mode of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_mode

    @sort_mode.setter
    def sort_mode(self, sort_mode):
        """Sets the sort_mode of this ListAccountActivityDataRequest.


        :param sort_mode: The sort_mode of this ListAccountActivityDataRequest.  # noqa: E501
        :type: str
        """

        self._sort_mode = sort_mode

    @property
    def start_live_time(self):
        """Gets the start_live_time of this ListAccountActivityDataRequest.  # noqa: E501


        :return: The start_live_time of this ListAccountActivityDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_live_time

    @start_live_time.setter
    def start_live_time(self, start_live_time):
        """Sets the start_live_time of this ListAccountActivityDataRequest.


        :param start_live_time: The start_live_time of this ListAccountActivityDataRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and start_live_time is None:
            raise ValueError("Invalid value for `start_live_time`, must not be `None`")  # noqa: E501

        self._start_live_time = start_live_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAccountActivityDataRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAccountActivityDataRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAccountActivityDataRequest):
            return True

        return self.to_dict() != other.to_dict()
