# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DXPInstanceForGetDXPInstanceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ap': 'str',
        'area': 'str',
        'auto_renew': 'str',
        'bandwidth': 'str',
        'connection_info': 'ConnectionInfoForGetDXPInstanceOutput',
        'construction_info': 'ConstructionInfoForGetDXPInstanceOutput',
        'health_status': 'bool',
        'isp': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'instance_no': 'str',
        'module_type': 'str',
        'port_type': 'str',
        'status': 'str',
        'vifid_list': 'list[str]'
    }

    attribute_map = {
        'ap': 'AP',
        'area': 'Area',
        'auto_renew': 'AutoRenew',
        'bandwidth': 'Bandwidth',
        'connection_info': 'ConnectionInfo',
        'construction_info': 'ConstructionInfo',
        'health_status': 'HealthStatus',
        'isp': 'ISP',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'instance_no': 'InstanceNo',
        'module_type': 'ModuleType',
        'port_type': 'PortType',
        'status': 'Status',
        'vifid_list': 'VIFIdList'
    }

    def __init__(self, ap=None, area=None, auto_renew=None, bandwidth=None, connection_info=None, construction_info=None, health_status=None, isp=None, instance_id=None, instance_name=None, instance_no=None, module_type=None, port_type=None, status=None, vifid_list=None, _configuration=None):  # noqa: E501
        """DXPInstanceForGetDXPInstanceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ap = None
        self._area = None
        self._auto_renew = None
        self._bandwidth = None
        self._connection_info = None
        self._construction_info = None
        self._health_status = None
        self._isp = None
        self._instance_id = None
        self._instance_name = None
        self._instance_no = None
        self._module_type = None
        self._port_type = None
        self._status = None
        self._vifid_list = None
        self.discriminator = None

        if ap is not None:
            self.ap = ap
        if area is not None:
            self.area = area
        if auto_renew is not None:
            self.auto_renew = auto_renew
        if bandwidth is not None:
            self.bandwidth = bandwidth
        if connection_info is not None:
            self.connection_info = connection_info
        if construction_info is not None:
            self.construction_info = construction_info
        if health_status is not None:
            self.health_status = health_status
        if isp is not None:
            self.isp = isp
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_no is not None:
            self.instance_no = instance_no
        if module_type is not None:
            self.module_type = module_type
        if port_type is not None:
            self.port_type = port_type
        if status is not None:
            self.status = status
        if vifid_list is not None:
            self.vifid_list = vifid_list

    @property
    def ap(self):
        """Gets the ap of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The ap of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._ap

    @ap.setter
    def ap(self, ap):
        """Sets the ap of this DXPInstanceForGetDXPInstanceOutput.


        :param ap: The ap of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._ap = ap

    @property
    def area(self):
        """Gets the area of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The area of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._area

    @area.setter
    def area(self, area):
        """Sets the area of this DXPInstanceForGetDXPInstanceOutput.


        :param area: The area of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._area = area

    @property
    def auto_renew(self):
        """Gets the auto_renew of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The auto_renew of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this DXPInstanceForGetDXPInstanceOutput.


        :param auto_renew: The auto_renew of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._auto_renew = auto_renew

    @property
    def bandwidth(self):
        """Gets the bandwidth of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The bandwidth of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this DXPInstanceForGetDXPInstanceOutput.


        :param bandwidth: The bandwidth of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._bandwidth = bandwidth

    @property
    def connection_info(self):
        """Gets the connection_info of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The connection_info of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: ConnectionInfoForGetDXPInstanceOutput
        """
        return self._connection_info

    @connection_info.setter
    def connection_info(self, connection_info):
        """Sets the connection_info of this DXPInstanceForGetDXPInstanceOutput.


        :param connection_info: The connection_info of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: ConnectionInfoForGetDXPInstanceOutput
        """

        self._connection_info = connection_info

    @property
    def construction_info(self):
        """Gets the construction_info of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The construction_info of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: ConstructionInfoForGetDXPInstanceOutput
        """
        return self._construction_info

    @construction_info.setter
    def construction_info(self, construction_info):
        """Sets the construction_info of this DXPInstanceForGetDXPInstanceOutput.


        :param construction_info: The construction_info of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: ConstructionInfoForGetDXPInstanceOutput
        """

        self._construction_info = construction_info

    @property
    def health_status(self):
        """Gets the health_status of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The health_status of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: bool
        """
        return self._health_status

    @health_status.setter
    def health_status(self, health_status):
        """Sets the health_status of this DXPInstanceForGetDXPInstanceOutput.


        :param health_status: The health_status of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: bool
        """

        self._health_status = health_status

    @property
    def isp(self):
        """Gets the isp of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The isp of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this DXPInstanceForGetDXPInstanceOutput.


        :param isp: The isp of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def instance_id(self):
        """Gets the instance_id of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The instance_id of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DXPInstanceForGetDXPInstanceOutput.


        :param instance_id: The instance_id of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The instance_name of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this DXPInstanceForGetDXPInstanceOutput.


        :param instance_name: The instance_name of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_no(self):
        """Gets the instance_no of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The instance_no of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_no

    @instance_no.setter
    def instance_no(self, instance_no):
        """Sets the instance_no of this DXPInstanceForGetDXPInstanceOutput.


        :param instance_no: The instance_no of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._instance_no = instance_no

    @property
    def module_type(self):
        """Gets the module_type of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The module_type of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._module_type

    @module_type.setter
    def module_type(self, module_type):
        """Sets the module_type of this DXPInstanceForGetDXPInstanceOutput.


        :param module_type: The module_type of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._module_type = module_type

    @property
    def port_type(self):
        """Gets the port_type of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The port_type of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._port_type

    @port_type.setter
    def port_type(self, port_type):
        """Sets the port_type of this DXPInstanceForGetDXPInstanceOutput.


        :param port_type: The port_type of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._port_type = port_type

    @property
    def status(self):
        """Gets the status of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The status of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DXPInstanceForGetDXPInstanceOutput.


        :param status: The status of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def vifid_list(self):
        """Gets the vifid_list of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501


        :return: The vifid_list of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._vifid_list

    @vifid_list.setter
    def vifid_list(self, vifid_list):
        """Sets the vifid_list of this DXPInstanceForGetDXPInstanceOutput.


        :param vifid_list: The vifid_list of this DXPInstanceForGetDXPInstanceOutput.  # noqa: E501
        :type: list[str]
        """

        self._vifid_list = vifid_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DXPInstanceForGetDXPInstanceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DXPInstanceForGetDXPInstanceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DXPInstanceForGetDXPInstanceOutput):
            return True

        return self.to_dict() != other.to_dict()
