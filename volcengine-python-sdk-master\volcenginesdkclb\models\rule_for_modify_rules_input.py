# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RuleForModifyRulesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action_type': 'str',
        'description': 'str',
        'redirect_config': 'RedirectConfigForModifyRulesInput',
        'rule_id': 'str',
        'server_group_id': 'str'
    }

    attribute_map = {
        'action_type': 'ActionType',
        'description': 'Description',
        'redirect_config': 'RedirectConfig',
        'rule_id': 'RuleId',
        'server_group_id': 'ServerGroupId'
    }

    def __init__(self, action_type=None, description=None, redirect_config=None, rule_id=None, server_group_id=None, _configuration=None):  # noqa: E501
        """RuleForModifyRulesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action_type = None
        self._description = None
        self._redirect_config = None
        self._rule_id = None
        self._server_group_id = None
        self.discriminator = None

        if action_type is not None:
            self.action_type = action_type
        if description is not None:
            self.description = description
        if redirect_config is not None:
            self.redirect_config = redirect_config
        self.rule_id = rule_id
        if server_group_id is not None:
            self.server_group_id = server_group_id

    @property
    def action_type(self):
        """Gets the action_type of this RuleForModifyRulesInput.  # noqa: E501


        :return: The action_type of this RuleForModifyRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._action_type

    @action_type.setter
    def action_type(self, action_type):
        """Sets the action_type of this RuleForModifyRulesInput.


        :param action_type: The action_type of this RuleForModifyRulesInput.  # noqa: E501
        :type: str
        """

        self._action_type = action_type

    @property
    def description(self):
        """Gets the description of this RuleForModifyRulesInput.  # noqa: E501


        :return: The description of this RuleForModifyRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this RuleForModifyRulesInput.


        :param description: The description of this RuleForModifyRulesInput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def redirect_config(self):
        """Gets the redirect_config of this RuleForModifyRulesInput.  # noqa: E501


        :return: The redirect_config of this RuleForModifyRulesInput.  # noqa: E501
        :rtype: RedirectConfigForModifyRulesInput
        """
        return self._redirect_config

    @redirect_config.setter
    def redirect_config(self, redirect_config):
        """Sets the redirect_config of this RuleForModifyRulesInput.


        :param redirect_config: The redirect_config of this RuleForModifyRulesInput.  # noqa: E501
        :type: RedirectConfigForModifyRulesInput
        """

        self._redirect_config = redirect_config

    @property
    def rule_id(self):
        """Gets the rule_id of this RuleForModifyRulesInput.  # noqa: E501


        :return: The rule_id of this RuleForModifyRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this RuleForModifyRulesInput.


        :param rule_id: The rule_id of this RuleForModifyRulesInput.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_id is None:
            raise ValueError("Invalid value for `rule_id`, must not be `None`")  # noqa: E501

        self._rule_id = rule_id

    @property
    def server_group_id(self):
        """Gets the server_group_id of this RuleForModifyRulesInput.  # noqa: E501


        :return: The server_group_id of this RuleForModifyRulesInput.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this RuleForModifyRulesInput.


        :param server_group_id: The server_group_id of this RuleForModifyRulesInput.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RuleForModifyRulesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RuleForModifyRulesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RuleForModifyRulesInput):
            return True

        return self.to_dict() != other.to_dict()
