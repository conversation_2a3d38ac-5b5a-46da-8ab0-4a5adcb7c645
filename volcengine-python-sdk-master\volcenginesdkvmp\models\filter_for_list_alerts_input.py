# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListAlertsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alerting_rule_ids': 'list[str]',
        'current_phase': 'str',
        'ids': 'list[str]',
        'initial_alert_timestamp_end': 'str',
        'initial_alert_timestamp_start': 'str',
        'last_alert_timestamp_end': 'str',
        'last_alert_timestamp_start': 'str',
        'level': 'str'
    }

    attribute_map = {
        'alerting_rule_ids': 'AlertingRuleIds',
        'current_phase': 'CurrentPhase',
        'ids': 'Ids',
        'initial_alert_timestamp_end': 'InitialAlertTimestampEnd',
        'initial_alert_timestamp_start': 'InitialAlertTimestampStart',
        'last_alert_timestamp_end': 'LastAlertTimestampEnd',
        'last_alert_timestamp_start': 'LastAlertTimestampStart',
        'level': 'Level'
    }

    def __init__(self, alerting_rule_ids=None, current_phase=None, ids=None, initial_alert_timestamp_end=None, initial_alert_timestamp_start=None, last_alert_timestamp_end=None, last_alert_timestamp_start=None, level=None, _configuration=None):  # noqa: E501
        """FilterForListAlertsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alerting_rule_ids = None
        self._current_phase = None
        self._ids = None
        self._initial_alert_timestamp_end = None
        self._initial_alert_timestamp_start = None
        self._last_alert_timestamp_end = None
        self._last_alert_timestamp_start = None
        self._level = None
        self.discriminator = None

        if alerting_rule_ids is not None:
            self.alerting_rule_ids = alerting_rule_ids
        if current_phase is not None:
            self.current_phase = current_phase
        if ids is not None:
            self.ids = ids
        if initial_alert_timestamp_end is not None:
            self.initial_alert_timestamp_end = initial_alert_timestamp_end
        if initial_alert_timestamp_start is not None:
            self.initial_alert_timestamp_start = initial_alert_timestamp_start
        if last_alert_timestamp_end is not None:
            self.last_alert_timestamp_end = last_alert_timestamp_end
        if last_alert_timestamp_start is not None:
            self.last_alert_timestamp_start = last_alert_timestamp_start
        if level is not None:
            self.level = level

    @property
    def alerting_rule_ids(self):
        """Gets the alerting_rule_ids of this FilterForListAlertsInput.  # noqa: E501


        :return: The alerting_rule_ids of this FilterForListAlertsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._alerting_rule_ids

    @alerting_rule_ids.setter
    def alerting_rule_ids(self, alerting_rule_ids):
        """Sets the alerting_rule_ids of this FilterForListAlertsInput.


        :param alerting_rule_ids: The alerting_rule_ids of this FilterForListAlertsInput.  # noqa: E501
        :type: list[str]
        """

        self._alerting_rule_ids = alerting_rule_ids

    @property
    def current_phase(self):
        """Gets the current_phase of this FilterForListAlertsInput.  # noqa: E501


        :return: The current_phase of this FilterForListAlertsInput.  # noqa: E501
        :rtype: str
        """
        return self._current_phase

    @current_phase.setter
    def current_phase(self, current_phase):
        """Sets the current_phase of this FilterForListAlertsInput.


        :param current_phase: The current_phase of this FilterForListAlertsInput.  # noqa: E501
        :type: str
        """

        self._current_phase = current_phase

    @property
    def ids(self):
        """Gets the ids of this FilterForListAlertsInput.  # noqa: E501


        :return: The ids of this FilterForListAlertsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListAlertsInput.


        :param ids: The ids of this FilterForListAlertsInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def initial_alert_timestamp_end(self):
        """Gets the initial_alert_timestamp_end of this FilterForListAlertsInput.  # noqa: E501


        :return: The initial_alert_timestamp_end of this FilterForListAlertsInput.  # noqa: E501
        :rtype: str
        """
        return self._initial_alert_timestamp_end

    @initial_alert_timestamp_end.setter
    def initial_alert_timestamp_end(self, initial_alert_timestamp_end):
        """Sets the initial_alert_timestamp_end of this FilterForListAlertsInput.


        :param initial_alert_timestamp_end: The initial_alert_timestamp_end of this FilterForListAlertsInput.  # noqa: E501
        :type: str
        """

        self._initial_alert_timestamp_end = initial_alert_timestamp_end

    @property
    def initial_alert_timestamp_start(self):
        """Gets the initial_alert_timestamp_start of this FilterForListAlertsInput.  # noqa: E501


        :return: The initial_alert_timestamp_start of this FilterForListAlertsInput.  # noqa: E501
        :rtype: str
        """
        return self._initial_alert_timestamp_start

    @initial_alert_timestamp_start.setter
    def initial_alert_timestamp_start(self, initial_alert_timestamp_start):
        """Sets the initial_alert_timestamp_start of this FilterForListAlertsInput.


        :param initial_alert_timestamp_start: The initial_alert_timestamp_start of this FilterForListAlertsInput.  # noqa: E501
        :type: str
        """

        self._initial_alert_timestamp_start = initial_alert_timestamp_start

    @property
    def last_alert_timestamp_end(self):
        """Gets the last_alert_timestamp_end of this FilterForListAlertsInput.  # noqa: E501


        :return: The last_alert_timestamp_end of this FilterForListAlertsInput.  # noqa: E501
        :rtype: str
        """
        return self._last_alert_timestamp_end

    @last_alert_timestamp_end.setter
    def last_alert_timestamp_end(self, last_alert_timestamp_end):
        """Sets the last_alert_timestamp_end of this FilterForListAlertsInput.


        :param last_alert_timestamp_end: The last_alert_timestamp_end of this FilterForListAlertsInput.  # noqa: E501
        :type: str
        """

        self._last_alert_timestamp_end = last_alert_timestamp_end

    @property
    def last_alert_timestamp_start(self):
        """Gets the last_alert_timestamp_start of this FilterForListAlertsInput.  # noqa: E501


        :return: The last_alert_timestamp_start of this FilterForListAlertsInput.  # noqa: E501
        :rtype: str
        """
        return self._last_alert_timestamp_start

    @last_alert_timestamp_start.setter
    def last_alert_timestamp_start(self, last_alert_timestamp_start):
        """Sets the last_alert_timestamp_start of this FilterForListAlertsInput.


        :param last_alert_timestamp_start: The last_alert_timestamp_start of this FilterForListAlertsInput.  # noqa: E501
        :type: str
        """

        self._last_alert_timestamp_start = last_alert_timestamp_start

    @property
    def level(self):
        """Gets the level of this FilterForListAlertsInput.  # noqa: E501


        :return: The level of this FilterForListAlertsInput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this FilterForListAlertsInput.


        :param level: The level of this FilterForListAlertsInput.  # noqa: E501
        :type: str
        """

        self._level = level

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListAlertsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListAlertsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListAlertsInput):
            return True

        return self.to_dict() != other.to_dict()
