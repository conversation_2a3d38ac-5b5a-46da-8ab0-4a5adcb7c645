# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EditRaspConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'all_process_inject': 'bool',
        'blocks': 'list[BlockForEditRaspConfigInput]',
        'filters': 'list[FilterForEditRaspConfigInput]',
        'id': 'str',
        'if_protect': 'bool',
        'inject_config_list': 'list[InjectConfigListForEditRaspConfigInput]',
        'local_project_inject': 'bool',
        'name': 'str',
        'protect_method': 'str',
        'range': 'RangeForEditRaspConfigInput',
        'runtime': 'str',
        'switch': 'SwitchForEditRaspConfigInput',
        'top_group_id': 'str'
    }

    attribute_map = {
        'all_process_inject': 'AllProcessInject',
        'blocks': 'Blocks',
        'filters': 'Filters',
        'id': 'ID',
        'if_protect': 'IfProtect',
        'inject_config_list': 'InjectConfigList',
        'local_project_inject': 'LocalProjectInject',
        'name': 'Name',
        'protect_method': 'ProtectMethod',
        'range': 'Range',
        'runtime': 'Runtime',
        'switch': 'Switch',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, all_process_inject=None, blocks=None, filters=None, id=None, if_protect=None, inject_config_list=None, local_project_inject=None, name=None, protect_method=None, range=None, runtime=None, switch=None, top_group_id=None, _configuration=None):  # noqa: E501
        """EditRaspConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._all_process_inject = None
        self._blocks = None
        self._filters = None
        self._id = None
        self._if_protect = None
        self._inject_config_list = None
        self._local_project_inject = None
        self._name = None
        self._protect_method = None
        self._range = None
        self._runtime = None
        self._switch = None
        self._top_group_id = None
        self.discriminator = None

        if all_process_inject is not None:
            self.all_process_inject = all_process_inject
        if blocks is not None:
            self.blocks = blocks
        if filters is not None:
            self.filters = filters
        self.id = id
        if if_protect is not None:
            self.if_protect = if_protect
        if inject_config_list is not None:
            self.inject_config_list = inject_config_list
        if local_project_inject is not None:
            self.local_project_inject = local_project_inject
        self.name = name
        self.protect_method = protect_method
        if range is not None:
            self.range = range
        self.runtime = runtime
        if switch is not None:
            self.switch = switch
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def all_process_inject(self):
        """Gets the all_process_inject of this EditRaspConfigRequest.  # noqa: E501


        :return: The all_process_inject of this EditRaspConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._all_process_inject

    @all_process_inject.setter
    def all_process_inject(self, all_process_inject):
        """Sets the all_process_inject of this EditRaspConfigRequest.


        :param all_process_inject: The all_process_inject of this EditRaspConfigRequest.  # noqa: E501
        :type: bool
        """

        self._all_process_inject = all_process_inject

    @property
    def blocks(self):
        """Gets the blocks of this EditRaspConfigRequest.  # noqa: E501


        :return: The blocks of this EditRaspConfigRequest.  # noqa: E501
        :rtype: list[BlockForEditRaspConfigInput]
        """
        return self._blocks

    @blocks.setter
    def blocks(self, blocks):
        """Sets the blocks of this EditRaspConfigRequest.


        :param blocks: The blocks of this EditRaspConfigRequest.  # noqa: E501
        :type: list[BlockForEditRaspConfigInput]
        """

        self._blocks = blocks

    @property
    def filters(self):
        """Gets the filters of this EditRaspConfigRequest.  # noqa: E501


        :return: The filters of this EditRaspConfigRequest.  # noqa: E501
        :rtype: list[FilterForEditRaspConfigInput]
        """
        return self._filters

    @filters.setter
    def filters(self, filters):
        """Sets the filters of this EditRaspConfigRequest.


        :param filters: The filters of this EditRaspConfigRequest.  # noqa: E501
        :type: list[FilterForEditRaspConfigInput]
        """

        self._filters = filters

    @property
    def id(self):
        """Gets the id of this EditRaspConfigRequest.  # noqa: E501


        :return: The id of this EditRaspConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this EditRaspConfigRequest.


        :param id: The id of this EditRaspConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def if_protect(self):
        """Gets the if_protect of this EditRaspConfigRequest.  # noqa: E501


        :return: The if_protect of this EditRaspConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_protect

    @if_protect.setter
    def if_protect(self, if_protect):
        """Sets the if_protect of this EditRaspConfigRequest.


        :param if_protect: The if_protect of this EditRaspConfigRequest.  # noqa: E501
        :type: bool
        """

        self._if_protect = if_protect

    @property
    def inject_config_list(self):
        """Gets the inject_config_list of this EditRaspConfigRequest.  # noqa: E501


        :return: The inject_config_list of this EditRaspConfigRequest.  # noqa: E501
        :rtype: list[InjectConfigListForEditRaspConfigInput]
        """
        return self._inject_config_list

    @inject_config_list.setter
    def inject_config_list(self, inject_config_list):
        """Sets the inject_config_list of this EditRaspConfigRequest.


        :param inject_config_list: The inject_config_list of this EditRaspConfigRequest.  # noqa: E501
        :type: list[InjectConfigListForEditRaspConfigInput]
        """

        self._inject_config_list = inject_config_list

    @property
    def local_project_inject(self):
        """Gets the local_project_inject of this EditRaspConfigRequest.  # noqa: E501


        :return: The local_project_inject of this EditRaspConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._local_project_inject

    @local_project_inject.setter
    def local_project_inject(self, local_project_inject):
        """Sets the local_project_inject of this EditRaspConfigRequest.


        :param local_project_inject: The local_project_inject of this EditRaspConfigRequest.  # noqa: E501
        :type: bool
        """

        self._local_project_inject = local_project_inject

    @property
    def name(self):
        """Gets the name of this EditRaspConfigRequest.  # noqa: E501


        :return: The name of this EditRaspConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this EditRaspConfigRequest.


        :param name: The name of this EditRaspConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def protect_method(self):
        """Gets the protect_method of this EditRaspConfigRequest.  # noqa: E501


        :return: The protect_method of this EditRaspConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._protect_method

    @protect_method.setter
    def protect_method(self, protect_method):
        """Sets the protect_method of this EditRaspConfigRequest.


        :param protect_method: The protect_method of this EditRaspConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and protect_method is None:
            raise ValueError("Invalid value for `protect_method`, must not be `None`")  # noqa: E501

        self._protect_method = protect_method

    @property
    def range(self):
        """Gets the range of this EditRaspConfigRequest.  # noqa: E501


        :return: The range of this EditRaspConfigRequest.  # noqa: E501
        :rtype: RangeForEditRaspConfigInput
        """
        return self._range

    @range.setter
    def range(self, range):
        """Sets the range of this EditRaspConfigRequest.


        :param range: The range of this EditRaspConfigRequest.  # noqa: E501
        :type: RangeForEditRaspConfigInput
        """

        self._range = range

    @property
    def runtime(self):
        """Gets the runtime of this EditRaspConfigRequest.  # noqa: E501


        :return: The runtime of this EditRaspConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._runtime

    @runtime.setter
    def runtime(self, runtime):
        """Sets the runtime of this EditRaspConfigRequest.


        :param runtime: The runtime of this EditRaspConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and runtime is None:
            raise ValueError("Invalid value for `runtime`, must not be `None`")  # noqa: E501

        self._runtime = runtime

    @property
    def switch(self):
        """Gets the switch of this EditRaspConfigRequest.  # noqa: E501


        :return: The switch of this EditRaspConfigRequest.  # noqa: E501
        :rtype: SwitchForEditRaspConfigInput
        """
        return self._switch

    @switch.setter
    def switch(self, switch):
        """Sets the switch of this EditRaspConfigRequest.


        :param switch: The switch of this EditRaspConfigRequest.  # noqa: E501
        :type: SwitchForEditRaspConfigInput
        """

        self._switch = switch

    @property
    def top_group_id(self):
        """Gets the top_group_id of this EditRaspConfigRequest.  # noqa: E501


        :return: The top_group_id of this EditRaspConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this EditRaspConfigRequest.


        :param top_group_id: The top_group_id of this EditRaspConfigRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EditRaspConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EditRaspConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EditRaspConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
