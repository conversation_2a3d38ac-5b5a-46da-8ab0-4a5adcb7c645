# coding: utf-8

"""
    advdefence20230308

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DelWebDefCcRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ddos_id': 'int',
        'host': 'str'
    }

    attribute_map = {
        'ddos_id': 'DdosId',
        'host': 'Host'
    }

    def __init__(self, ddos_id=None, host=None, _configuration=None):  # noqa: E501
        """DelWebDefCcRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ddos_id = None
        self._host = None
        self.discriminator = None

        self.ddos_id = ddos_id
        self.host = host

    @property
    def ddos_id(self):
        """Gets the ddos_id of this DelWebDefCcRuleRequest.  # noqa: E501


        :return: The ddos_id of this DelWebDefCcRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._ddos_id

    @ddos_id.setter
    def ddos_id(self, ddos_id):
        """Sets the ddos_id of this DelWebDefCcRuleRequest.


        :param ddos_id: The ddos_id of this DelWebDefCcRuleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and ddos_id is None:
            raise ValueError("Invalid value for `ddos_id`, must not be `None`")  # noqa: E501

        self._ddos_id = ddos_id

    @property
    def host(self):
        """Gets the host of this DelWebDefCcRuleRequest.  # noqa: E501


        :return: The host of this DelWebDefCcRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this DelWebDefCcRuleRequest.


        :param host: The host of this DelWebDefCcRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and host is None:
            raise ValueError("Invalid value for `host`, must not be `None`")  # noqa: E501

        self._host = host

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DelWebDefCcRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DelWebDefCcRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DelWebDefCcRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
