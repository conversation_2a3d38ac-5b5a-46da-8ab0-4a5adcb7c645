# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListEntitiesForPolicyResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'limit': 'int',
        'offset': 'int',
        'policy_roles': 'list[PolicyRoleForListEntitiesForPolicyOutput]',
        'policy_user_groups': 'list[PolicyUserGroupForListEntitiesForPolicyOutput]',
        'policy_users': 'list[PolicyUserForListEntitiesForPolicyOutput]',
        'total': 'int'
    }

    attribute_map = {
        'limit': 'Limit',
        'offset': 'Offset',
        'policy_roles': 'PolicyRoles',
        'policy_user_groups': 'PolicyUserGroups',
        'policy_users': 'PolicyUsers',
        'total': 'Total'
    }

    def __init__(self, limit=None, offset=None, policy_roles=None, policy_user_groups=None, policy_users=None, total=None, _configuration=None):  # noqa: E501
        """ListEntitiesForPolicyResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._limit = None
        self._offset = None
        self._policy_roles = None
        self._policy_user_groups = None
        self._policy_users = None
        self._total = None
        self.discriminator = None

        if limit is not None:
            self.limit = limit
        if offset is not None:
            self.offset = offset
        if policy_roles is not None:
            self.policy_roles = policy_roles
        if policy_user_groups is not None:
            self.policy_user_groups = policy_user_groups
        if policy_users is not None:
            self.policy_users = policy_users
        if total is not None:
            self.total = total

    @property
    def limit(self):
        """Gets the limit of this ListEntitiesForPolicyResponse.  # noqa: E501


        :return: The limit of this ListEntitiesForPolicyResponse.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListEntitiesForPolicyResponse.


        :param limit: The limit of this ListEntitiesForPolicyResponse.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def offset(self):
        """Gets the offset of this ListEntitiesForPolicyResponse.  # noqa: E501


        :return: The offset of this ListEntitiesForPolicyResponse.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this ListEntitiesForPolicyResponse.


        :param offset: The offset of this ListEntitiesForPolicyResponse.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def policy_roles(self):
        """Gets the policy_roles of this ListEntitiesForPolicyResponse.  # noqa: E501


        :return: The policy_roles of this ListEntitiesForPolicyResponse.  # noqa: E501
        :rtype: list[PolicyRoleForListEntitiesForPolicyOutput]
        """
        return self._policy_roles

    @policy_roles.setter
    def policy_roles(self, policy_roles):
        """Sets the policy_roles of this ListEntitiesForPolicyResponse.


        :param policy_roles: The policy_roles of this ListEntitiesForPolicyResponse.  # noqa: E501
        :type: list[PolicyRoleForListEntitiesForPolicyOutput]
        """

        self._policy_roles = policy_roles

    @property
    def policy_user_groups(self):
        """Gets the policy_user_groups of this ListEntitiesForPolicyResponse.  # noqa: E501


        :return: The policy_user_groups of this ListEntitiesForPolicyResponse.  # noqa: E501
        :rtype: list[PolicyUserGroupForListEntitiesForPolicyOutput]
        """
        return self._policy_user_groups

    @policy_user_groups.setter
    def policy_user_groups(self, policy_user_groups):
        """Sets the policy_user_groups of this ListEntitiesForPolicyResponse.


        :param policy_user_groups: The policy_user_groups of this ListEntitiesForPolicyResponse.  # noqa: E501
        :type: list[PolicyUserGroupForListEntitiesForPolicyOutput]
        """

        self._policy_user_groups = policy_user_groups

    @property
    def policy_users(self):
        """Gets the policy_users of this ListEntitiesForPolicyResponse.  # noqa: E501


        :return: The policy_users of this ListEntitiesForPolicyResponse.  # noqa: E501
        :rtype: list[PolicyUserForListEntitiesForPolicyOutput]
        """
        return self._policy_users

    @policy_users.setter
    def policy_users(self, policy_users):
        """Sets the policy_users of this ListEntitiesForPolicyResponse.


        :param policy_users: The policy_users of this ListEntitiesForPolicyResponse.  # noqa: E501
        :type: list[PolicyUserForListEntitiesForPolicyOutput]
        """

        self._policy_users = policy_users

    @property
    def total(self):
        """Gets the total of this ListEntitiesForPolicyResponse.  # noqa: E501


        :return: The total of this ListEntitiesForPolicyResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this ListEntitiesForPolicyResponse.


        :param total: The total of this ListEntitiesForPolicyResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListEntitiesForPolicyResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListEntitiesForPolicyResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListEntitiesForPolicyResponse):
            return True

        return self.to_dict() != other.to_dict()
