# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VepfsForCreateDeploymentInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system_name': 'str',
        'host_path': 'str',
        'id': 'str',
        'sub_path': 'str'
    }

    attribute_map = {
        'file_system_name': 'FileSystemName',
        'host_path': 'HostPath',
        'id': 'Id',
        'sub_path': 'SubPath'
    }

    def __init__(self, file_system_name=None, host_path=None, id=None, sub_path=None, _configuration=None):  # noqa: E501
        """VepfsForCreateDeploymentInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system_name = None
        self._host_path = None
        self._id = None
        self._sub_path = None
        self.discriminator = None

        if file_system_name is not None:
            self.file_system_name = file_system_name
        if host_path is not None:
            self.host_path = host_path
        if id is not None:
            self.id = id
        if sub_path is not None:
            self.sub_path = sub_path

    @property
    def file_system_name(self):
        """Gets the file_system_name of this VepfsForCreateDeploymentInput.  # noqa: E501


        :return: The file_system_name of this VepfsForCreateDeploymentInput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_name

    @file_system_name.setter
    def file_system_name(self, file_system_name):
        """Sets the file_system_name of this VepfsForCreateDeploymentInput.


        :param file_system_name: The file_system_name of this VepfsForCreateDeploymentInput.  # noqa: E501
        :type: str
        """

        self._file_system_name = file_system_name

    @property
    def host_path(self):
        """Gets the host_path of this VepfsForCreateDeploymentInput.  # noqa: E501


        :return: The host_path of this VepfsForCreateDeploymentInput.  # noqa: E501
        :rtype: str
        """
        return self._host_path

    @host_path.setter
    def host_path(self, host_path):
        """Sets the host_path of this VepfsForCreateDeploymentInput.


        :param host_path: The host_path of this VepfsForCreateDeploymentInput.  # noqa: E501
        :type: str
        """

        self._host_path = host_path

    @property
    def id(self):
        """Gets the id of this VepfsForCreateDeploymentInput.  # noqa: E501


        :return: The id of this VepfsForCreateDeploymentInput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VepfsForCreateDeploymentInput.


        :param id: The id of this VepfsForCreateDeploymentInput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def sub_path(self):
        """Gets the sub_path of this VepfsForCreateDeploymentInput.  # noqa: E501


        :return: The sub_path of this VepfsForCreateDeploymentInput.  # noqa: E501
        :rtype: str
        """
        return self._sub_path

    @sub_path.setter
    def sub_path(self, sub_path):
        """Sets the sub_path of this VepfsForCreateDeploymentInput.


        :param sub_path: The sub_path of this VepfsForCreateDeploymentInput.  # noqa: E501
        :type: str
        """

        self._sub_path = sub_path

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VepfsForCreateDeploymentInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VepfsForCreateDeploymentInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VepfsForCreateDeploymentInput):
            return True

        return self.to_dict() != other.to_dict()
