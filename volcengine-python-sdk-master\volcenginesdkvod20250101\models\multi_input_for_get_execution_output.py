# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MultiInputForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'direct_url': 'DirectUrlForGetExecutionOutput',
        'file_id': 'FileIdForGetExecutionOutput',
        'type': 'str',
        'vid': 'str'
    }

    attribute_map = {
        'direct_url': 'DirectUrl',
        'file_id': 'FileId',
        'type': 'Type',
        'vid': 'Vid'
    }

    def __init__(self, direct_url=None, file_id=None, type=None, vid=None, _configuration=None):  # noqa: E501
        """MultiInputForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._direct_url = None
        self._file_id = None
        self._type = None
        self._vid = None
        self.discriminator = None

        if direct_url is not None:
            self.direct_url = direct_url
        if file_id is not None:
            self.file_id = file_id
        if type is not None:
            self.type = type
        if vid is not None:
            self.vid = vid

    @property
    def direct_url(self):
        """Gets the direct_url of this MultiInputForGetExecutionOutput.  # noqa: E501


        :return: The direct_url of this MultiInputForGetExecutionOutput.  # noqa: E501
        :rtype: DirectUrlForGetExecutionOutput
        """
        return self._direct_url

    @direct_url.setter
    def direct_url(self, direct_url):
        """Sets the direct_url of this MultiInputForGetExecutionOutput.


        :param direct_url: The direct_url of this MultiInputForGetExecutionOutput.  # noqa: E501
        :type: DirectUrlForGetExecutionOutput
        """

        self._direct_url = direct_url

    @property
    def file_id(self):
        """Gets the file_id of this MultiInputForGetExecutionOutput.  # noqa: E501


        :return: The file_id of this MultiInputForGetExecutionOutput.  # noqa: E501
        :rtype: FileIdForGetExecutionOutput
        """
        return self._file_id

    @file_id.setter
    def file_id(self, file_id):
        """Sets the file_id of this MultiInputForGetExecutionOutput.


        :param file_id: The file_id of this MultiInputForGetExecutionOutput.  # noqa: E501
        :type: FileIdForGetExecutionOutput
        """

        self._file_id = file_id

    @property
    def type(self):
        """Gets the type of this MultiInputForGetExecutionOutput.  # noqa: E501


        :return: The type of this MultiInputForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this MultiInputForGetExecutionOutput.


        :param type: The type of this MultiInputForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def vid(self):
        """Gets the vid of this MultiInputForGetExecutionOutput.  # noqa: E501


        :return: The vid of this MultiInputForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._vid

    @vid.setter
    def vid(self, vid):
        """Sets the vid of this MultiInputForGetExecutionOutput.


        :param vid: The vid of this MultiInputForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._vid = vid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MultiInputForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MultiInputForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MultiInputForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
