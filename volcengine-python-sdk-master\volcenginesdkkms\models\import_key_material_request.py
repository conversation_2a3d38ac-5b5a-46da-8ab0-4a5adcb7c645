# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ImportKeyMaterialRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'encrypted_key_material': 'str',
        'expiration_model': 'str',
        'import_token': 'str',
        'key_id': 'str',
        'key_name': 'str',
        'keyring_name': 'str',
        'valid_to': 'int'
    }

    attribute_map = {
        'encrypted_key_material': 'EncryptedKeyMaterial',
        'expiration_model': 'ExpirationModel',
        'import_token': 'ImportToken',
        'key_id': 'KeyID',
        'key_name': 'KeyName',
        'keyring_name': 'KeyringName',
        'valid_to': 'ValidTo'
    }

    def __init__(self, encrypted_key_material=None, expiration_model=None, import_token=None, key_id=None, key_name=None, keyring_name=None, valid_to=None, _configuration=None):  # noqa: E501
        """ImportKeyMaterialRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._encrypted_key_material = None
        self._expiration_model = None
        self._import_token = None
        self._key_id = None
        self._key_name = None
        self._keyring_name = None
        self._valid_to = None
        self.discriminator = None

        self.encrypted_key_material = encrypted_key_material
        if expiration_model is not None:
            self.expiration_model = expiration_model
        self.import_token = import_token
        if key_id is not None:
            self.key_id = key_id
        if key_name is not None:
            self.key_name = key_name
        if keyring_name is not None:
            self.keyring_name = keyring_name
        if valid_to is not None:
            self.valid_to = valid_to

    @property
    def encrypted_key_material(self):
        """Gets the encrypted_key_material of this ImportKeyMaterialRequest.  # noqa: E501


        :return: The encrypted_key_material of this ImportKeyMaterialRequest.  # noqa: E501
        :rtype: str
        """
        return self._encrypted_key_material

    @encrypted_key_material.setter
    def encrypted_key_material(self, encrypted_key_material):
        """Sets the encrypted_key_material of this ImportKeyMaterialRequest.


        :param encrypted_key_material: The encrypted_key_material of this ImportKeyMaterialRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and encrypted_key_material is None:
            raise ValueError("Invalid value for `encrypted_key_material`, must not be `None`")  # noqa: E501

        self._encrypted_key_material = encrypted_key_material

    @property
    def expiration_model(self):
        """Gets the expiration_model of this ImportKeyMaterialRequest.  # noqa: E501


        :return: The expiration_model of this ImportKeyMaterialRequest.  # noqa: E501
        :rtype: str
        """
        return self._expiration_model

    @expiration_model.setter
    def expiration_model(self, expiration_model):
        """Sets the expiration_model of this ImportKeyMaterialRequest.


        :param expiration_model: The expiration_model of this ImportKeyMaterialRequest.  # noqa: E501
        :type: str
        """

        self._expiration_model = expiration_model

    @property
    def import_token(self):
        """Gets the import_token of this ImportKeyMaterialRequest.  # noqa: E501


        :return: The import_token of this ImportKeyMaterialRequest.  # noqa: E501
        :rtype: str
        """
        return self._import_token

    @import_token.setter
    def import_token(self, import_token):
        """Sets the import_token of this ImportKeyMaterialRequest.


        :param import_token: The import_token of this ImportKeyMaterialRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and import_token is None:
            raise ValueError("Invalid value for `import_token`, must not be `None`")  # noqa: E501

        self._import_token = import_token

    @property
    def key_id(self):
        """Gets the key_id of this ImportKeyMaterialRequest.  # noqa: E501


        :return: The key_id of this ImportKeyMaterialRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_id

    @key_id.setter
    def key_id(self, key_id):
        """Sets the key_id of this ImportKeyMaterialRequest.


        :param key_id: The key_id of this ImportKeyMaterialRequest.  # noqa: E501
        :type: str
        """

        self._key_id = key_id

    @property
    def key_name(self):
        """Gets the key_name of this ImportKeyMaterialRequest.  # noqa: E501


        :return: The key_name of this ImportKeyMaterialRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_name

    @key_name.setter
    def key_name(self, key_name):
        """Sets the key_name of this ImportKeyMaterialRequest.


        :param key_name: The key_name of this ImportKeyMaterialRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                key_name is not None and len(key_name) > 31):
            raise ValueError("Invalid value for `key_name`, length must be less than or equal to `31`")  # noqa: E501
        if (self._configuration.client_side_validation and
                key_name is not None and len(key_name) < 2):
            raise ValueError("Invalid value for `key_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._key_name = key_name

    @property
    def keyring_name(self):
        """Gets the keyring_name of this ImportKeyMaterialRequest.  # noqa: E501


        :return: The keyring_name of this ImportKeyMaterialRequest.  # noqa: E501
        :rtype: str
        """
        return self._keyring_name

    @keyring_name.setter
    def keyring_name(self, keyring_name):
        """Sets the keyring_name of this ImportKeyMaterialRequest.


        :param keyring_name: The keyring_name of this ImportKeyMaterialRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                keyring_name is not None and len(keyring_name) > 31):
            raise ValueError("Invalid value for `keyring_name`, length must be less than or equal to `31`")  # noqa: E501
        if (self._configuration.client_side_validation and
                keyring_name is not None and len(keyring_name) < 2):
            raise ValueError("Invalid value for `keyring_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._keyring_name = keyring_name

    @property
    def valid_to(self):
        """Gets the valid_to of this ImportKeyMaterialRequest.  # noqa: E501


        :return: The valid_to of this ImportKeyMaterialRequest.  # noqa: E501
        :rtype: int
        """
        return self._valid_to

    @valid_to.setter
    def valid_to(self, valid_to):
        """Sets the valid_to of this ImportKeyMaterialRequest.


        :param valid_to: The valid_to of this ImportKeyMaterialRequest.  # noqa: E501
        :type: int
        """

        self._valid_to = valid_to

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ImportKeyMaterialRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ImportKeyMaterialRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ImportKeyMaterialRequest):
            return True

        return self.to_dict() != other.to_dict()
