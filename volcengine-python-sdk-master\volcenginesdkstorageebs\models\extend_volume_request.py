# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ExtendVolumeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'new_size': 'str',
        'volume_id': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'new_size': 'NewSize',
        'volume_id': 'VolumeId'
    }

    def __init__(self, client_token=None, new_size=None, volume_id=None, _configuration=None):  # noqa: E501
        """ExtendVolumeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._new_size = None
        self._volume_id = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        self.new_size = new_size
        self.volume_id = volume_id

    @property
    def client_token(self):
        """Gets the client_token of this ExtendVolumeRequest.  # noqa: E501


        :return: The client_token of this ExtendVolumeRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this ExtendVolumeRequest.


        :param client_token: The client_token of this ExtendVolumeRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def new_size(self):
        """Gets the new_size of this ExtendVolumeRequest.  # noqa: E501


        :return: The new_size of this ExtendVolumeRequest.  # noqa: E501
        :rtype: str
        """
        return self._new_size

    @new_size.setter
    def new_size(self, new_size):
        """Sets the new_size of this ExtendVolumeRequest.


        :param new_size: The new_size of this ExtendVolumeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and new_size is None:
            raise ValueError("Invalid value for `new_size`, must not be `None`")  # noqa: E501

        self._new_size = new_size

    @property
    def volume_id(self):
        """Gets the volume_id of this ExtendVolumeRequest.  # noqa: E501


        :return: The volume_id of this ExtendVolumeRequest.  # noqa: E501
        :rtype: str
        """
        return self._volume_id

    @volume_id.setter
    def volume_id(self, volume_id):
        """Sets the volume_id of this ExtendVolumeRequest.


        :param volume_id: The volume_id of this ExtendVolumeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and volume_id is None:
            raise ValueError("Invalid value for `volume_id`, must not be `None`")  # noqa: E501

        self._volume_id = volume_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExtendVolumeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExtendVolumeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExtendVolumeRequest):
            return True

        return self.to_dict() != other.to_dict()
