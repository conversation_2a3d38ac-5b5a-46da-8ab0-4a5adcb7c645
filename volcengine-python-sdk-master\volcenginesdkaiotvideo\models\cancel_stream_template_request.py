# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CancelStreamTemplateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'space_id': 'str',
        'stream_id': 'str',
        'template_type': 'str'
    }

    attribute_map = {
        'space_id': 'SpaceID',
        'stream_id': 'StreamID',
        'template_type': 'TemplateType'
    }

    def __init__(self, space_id=None, stream_id=None, template_type=None, _configuration=None):  # noqa: E501
        """CancelStreamTemplateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._space_id = None
        self._stream_id = None
        self._template_type = None
        self.discriminator = None

        if space_id is not None:
            self.space_id = space_id
        self.stream_id = stream_id
        self.template_type = template_type

    @property
    def space_id(self):
        """Gets the space_id of this CancelStreamTemplateRequest.  # noqa: E501


        :return: The space_id of this CancelStreamTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this CancelStreamTemplateRequest.


        :param space_id: The space_id of this CancelStreamTemplateRequest.  # noqa: E501
        :type: str
        """

        self._space_id = space_id

    @property
    def stream_id(self):
        """Gets the stream_id of this CancelStreamTemplateRequest.  # noqa: E501


        :return: The stream_id of this CancelStreamTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._stream_id

    @stream_id.setter
    def stream_id(self, stream_id):
        """Sets the stream_id of this CancelStreamTemplateRequest.


        :param stream_id: The stream_id of this CancelStreamTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and stream_id is None:
            raise ValueError("Invalid value for `stream_id`, must not be `None`")  # noqa: E501

        self._stream_id = stream_id

    @property
    def template_type(self):
        """Gets the template_type of this CancelStreamTemplateRequest.  # noqa: E501


        :return: The template_type of this CancelStreamTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._template_type

    @template_type.setter
    def template_type(self, template_type):
        """Sets the template_type of this CancelStreamTemplateRequest.


        :param template_type: The template_type of this CancelStreamTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and template_type is None:
            raise ValueError("Invalid value for `template_type`, must not be `None`")  # noqa: E501

        self._template_type = template_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CancelStreamTemplateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CancelStreamTemplateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CancelStreamTemplateRequest):
            return True

        return self.to_dict() != other.to_dict()
