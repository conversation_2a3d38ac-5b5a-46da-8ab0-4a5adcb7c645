# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAlarmStatOverviewV2Request(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'stat_scope_type': 'str',
        'trend_range_end_sec': 'int',
        'trend_range_start_sec': 'int'
    }

    attribute_map = {
        'stat_scope_type': 'StatScopeType',
        'trend_range_end_sec': 'TrendRangeEndSec',
        'trend_range_start_sec': 'TrendRangeStartSec'
    }

    def __init__(self, stat_scope_type=None, trend_range_end_sec=None, trend_range_start_sec=None, _configuration=None):  # noqa: E501
        """DescribeAlarmStatOverviewV2Request - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._stat_scope_type = None
        self._trend_range_end_sec = None
        self._trend_range_start_sec = None
        self.discriminator = None

        if stat_scope_type is not None:
            self.stat_scope_type = stat_scope_type
        if trend_range_end_sec is not None:
            self.trend_range_end_sec = trend_range_end_sec
        if trend_range_start_sec is not None:
            self.trend_range_start_sec = trend_range_start_sec

    @property
    def stat_scope_type(self):
        """Gets the stat_scope_type of this DescribeAlarmStatOverviewV2Request.  # noqa: E501


        :return: The stat_scope_type of this DescribeAlarmStatOverviewV2Request.  # noqa: E501
        :rtype: str
        """
        return self._stat_scope_type

    @stat_scope_type.setter
    def stat_scope_type(self, stat_scope_type):
        """Sets the stat_scope_type of this DescribeAlarmStatOverviewV2Request.


        :param stat_scope_type: The stat_scope_type of this DescribeAlarmStatOverviewV2Request.  # noqa: E501
        :type: str
        """

        self._stat_scope_type = stat_scope_type

    @property
    def trend_range_end_sec(self):
        """Gets the trend_range_end_sec of this DescribeAlarmStatOverviewV2Request.  # noqa: E501


        :return: The trend_range_end_sec of this DescribeAlarmStatOverviewV2Request.  # noqa: E501
        :rtype: int
        """
        return self._trend_range_end_sec

    @trend_range_end_sec.setter
    def trend_range_end_sec(self, trend_range_end_sec):
        """Sets the trend_range_end_sec of this DescribeAlarmStatOverviewV2Request.


        :param trend_range_end_sec: The trend_range_end_sec of this DescribeAlarmStatOverviewV2Request.  # noqa: E501
        :type: int
        """

        self._trend_range_end_sec = trend_range_end_sec

    @property
    def trend_range_start_sec(self):
        """Gets the trend_range_start_sec of this DescribeAlarmStatOverviewV2Request.  # noqa: E501


        :return: The trend_range_start_sec of this DescribeAlarmStatOverviewV2Request.  # noqa: E501
        :rtype: int
        """
        return self._trend_range_start_sec

    @trend_range_start_sec.setter
    def trend_range_start_sec(self, trend_range_start_sec):
        """Sets the trend_range_start_sec of this DescribeAlarmStatOverviewV2Request.


        :param trend_range_start_sec: The trend_range_start_sec of this DescribeAlarmStatOverviewV2Request.  # noqa: E501
        :type: int
        """

        self._trend_range_start_sec = trend_range_start_sec

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAlarmStatOverviewV2Request, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAlarmStatOverviewV2Request):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAlarmStatOverviewV2Request):
            return True

        return self.to_dict() != other.to_dict()
