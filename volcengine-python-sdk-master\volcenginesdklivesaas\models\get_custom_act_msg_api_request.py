# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetCustomActMsgAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'need_act_basic_msg': 'int',
        'need_act_msg': 'int',
        'need_act_stream_msg': 'int',
        'need_real_time_online_number': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'need_act_basic_msg': 'NeedActBasicMsg',
        'need_act_msg': 'NeedActMsg',
        'need_act_stream_msg': 'NeedActStreamMsg',
        'need_real_time_online_number': 'NeedRealTimeOnlineNumber'
    }

    def __init__(self, activity_id=None, need_act_basic_msg=None, need_act_msg=None, need_act_stream_msg=None, need_real_time_online_number=None, _configuration=None):  # noqa: E501
        """GetCustomActMsgAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._need_act_basic_msg = None
        self._need_act_msg = None
        self._need_act_stream_msg = None
        self._need_real_time_online_number = None
        self.discriminator = None

        self.activity_id = activity_id
        if need_act_basic_msg is not None:
            self.need_act_basic_msg = need_act_basic_msg
        if need_act_msg is not None:
            self.need_act_msg = need_act_msg
        if need_act_stream_msg is not None:
            self.need_act_stream_msg = need_act_stream_msg
        if need_real_time_online_number is not None:
            self.need_real_time_online_number = need_real_time_online_number

    @property
    def activity_id(self):
        """Gets the activity_id of this GetCustomActMsgAPIRequest.  # noqa: E501


        :return: The activity_id of this GetCustomActMsgAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetCustomActMsgAPIRequest.


        :param activity_id: The activity_id of this GetCustomActMsgAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def need_act_basic_msg(self):
        """Gets the need_act_basic_msg of this GetCustomActMsgAPIRequest.  # noqa: E501


        :return: The need_act_basic_msg of this GetCustomActMsgAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._need_act_basic_msg

    @need_act_basic_msg.setter
    def need_act_basic_msg(self, need_act_basic_msg):
        """Sets the need_act_basic_msg of this GetCustomActMsgAPIRequest.


        :param need_act_basic_msg: The need_act_basic_msg of this GetCustomActMsgAPIRequest.  # noqa: E501
        :type: int
        """

        self._need_act_basic_msg = need_act_basic_msg

    @property
    def need_act_msg(self):
        """Gets the need_act_msg of this GetCustomActMsgAPIRequest.  # noqa: E501


        :return: The need_act_msg of this GetCustomActMsgAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._need_act_msg

    @need_act_msg.setter
    def need_act_msg(self, need_act_msg):
        """Sets the need_act_msg of this GetCustomActMsgAPIRequest.


        :param need_act_msg: The need_act_msg of this GetCustomActMsgAPIRequest.  # noqa: E501
        :type: int
        """

        self._need_act_msg = need_act_msg

    @property
    def need_act_stream_msg(self):
        """Gets the need_act_stream_msg of this GetCustomActMsgAPIRequest.  # noqa: E501


        :return: The need_act_stream_msg of this GetCustomActMsgAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._need_act_stream_msg

    @need_act_stream_msg.setter
    def need_act_stream_msg(self, need_act_stream_msg):
        """Sets the need_act_stream_msg of this GetCustomActMsgAPIRequest.


        :param need_act_stream_msg: The need_act_stream_msg of this GetCustomActMsgAPIRequest.  # noqa: E501
        :type: int
        """

        self._need_act_stream_msg = need_act_stream_msg

    @property
    def need_real_time_online_number(self):
        """Gets the need_real_time_online_number of this GetCustomActMsgAPIRequest.  # noqa: E501


        :return: The need_real_time_online_number of this GetCustomActMsgAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._need_real_time_online_number

    @need_real_time_online_number.setter
    def need_real_time_online_number(self, need_real_time_online_number):
        """Sets the need_real_time_online_number of this GetCustomActMsgAPIRequest.


        :param need_real_time_online_number: The need_real_time_online_number of this GetCustomActMsgAPIRequest.  # noqa: E501
        :type: int
        """

        self._need_real_time_online_number = need_real_time_online_number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetCustomActMsgAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetCustomActMsgAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetCustomActMsgAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
