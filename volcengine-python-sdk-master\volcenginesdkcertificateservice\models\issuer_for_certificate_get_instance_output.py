# coding: utf-8

"""
    certificate_service

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IssuerForCertificateGetInstanceOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address': 'str',
        'common_name': 'str',
        'country': 'str',
        'locality': 'str',
        'organization': 'str',
        'organization_unit': 'str',
        'postal_code': 'str',
        'province': 'str'
    }

    attribute_map = {
        'address': 'Address',
        'common_name': 'CommonName',
        'country': 'Country',
        'locality': 'Locality',
        'organization': 'Organization',
        'organization_unit': 'OrganizationUnit',
        'postal_code': 'PostalCode',
        'province': 'Province'
    }

    def __init__(self, address=None, common_name=None, country=None, locality=None, organization=None, organization_unit=None, postal_code=None, province=None, _configuration=None):  # noqa: E501
        """IssuerForCertificateGetInstanceOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address = None
        self._common_name = None
        self._country = None
        self._locality = None
        self._organization = None
        self._organization_unit = None
        self._postal_code = None
        self._province = None
        self.discriminator = None

        if address is not None:
            self.address = address
        if common_name is not None:
            self.common_name = common_name
        if country is not None:
            self.country = country
        if locality is not None:
            self.locality = locality
        if organization is not None:
            self.organization = organization
        if organization_unit is not None:
            self.organization_unit = organization_unit
        if postal_code is not None:
            self.postal_code = postal_code
        if province is not None:
            self.province = province

    @property
    def address(self):
        """Gets the address of this IssuerForCertificateGetInstanceOutput.  # noqa: E501


        :return: The address of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this IssuerForCertificateGetInstanceOutput.


        :param address: The address of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :type: str
        """

        self._address = address

    @property
    def common_name(self):
        """Gets the common_name of this IssuerForCertificateGetInstanceOutput.  # noqa: E501


        :return: The common_name of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._common_name

    @common_name.setter
    def common_name(self, common_name):
        """Sets the common_name of this IssuerForCertificateGetInstanceOutput.


        :param common_name: The common_name of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :type: str
        """

        self._common_name = common_name

    @property
    def country(self):
        """Gets the country of this IssuerForCertificateGetInstanceOutput.  # noqa: E501


        :return: The country of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._country

    @country.setter
    def country(self, country):
        """Sets the country of this IssuerForCertificateGetInstanceOutput.


        :param country: The country of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :type: str
        """

        self._country = country

    @property
    def locality(self):
        """Gets the locality of this IssuerForCertificateGetInstanceOutput.  # noqa: E501


        :return: The locality of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._locality

    @locality.setter
    def locality(self, locality):
        """Sets the locality of this IssuerForCertificateGetInstanceOutput.


        :param locality: The locality of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :type: str
        """

        self._locality = locality

    @property
    def organization(self):
        """Gets the organization of this IssuerForCertificateGetInstanceOutput.  # noqa: E501


        :return: The organization of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._organization

    @organization.setter
    def organization(self, organization):
        """Sets the organization of this IssuerForCertificateGetInstanceOutput.


        :param organization: The organization of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :type: str
        """

        self._organization = organization

    @property
    def organization_unit(self):
        """Gets the organization_unit of this IssuerForCertificateGetInstanceOutput.  # noqa: E501


        :return: The organization_unit of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._organization_unit

    @organization_unit.setter
    def organization_unit(self, organization_unit):
        """Sets the organization_unit of this IssuerForCertificateGetInstanceOutput.


        :param organization_unit: The organization_unit of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :type: str
        """

        self._organization_unit = organization_unit

    @property
    def postal_code(self):
        """Gets the postal_code of this IssuerForCertificateGetInstanceOutput.  # noqa: E501


        :return: The postal_code of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._postal_code

    @postal_code.setter
    def postal_code(self, postal_code):
        """Sets the postal_code of this IssuerForCertificateGetInstanceOutput.


        :param postal_code: The postal_code of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :type: str
        """

        self._postal_code = postal_code

    @property
    def province(self):
        """Gets the province of this IssuerForCertificateGetInstanceOutput.  # noqa: E501


        :return: The province of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :rtype: str
        """
        return self._province

    @province.setter
    def province(self, province):
        """Sets the province of this IssuerForCertificateGetInstanceOutput.


        :param province: The province of this IssuerForCertificateGetInstanceOutput.  # noqa: E501
        :type: str
        """

        self._province = province

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IssuerForCertificateGetInstanceOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IssuerForCertificateGetInstanceOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IssuerForCertificateGetInstanceOutput):
            return True

        return self.to_dict() != other.to_dict()
