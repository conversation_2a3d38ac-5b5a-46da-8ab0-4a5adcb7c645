# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BinlogFileForDescribeBinlogFilesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_region': 'str',
        'backup_status': 'str',
        'download_status': 'str',
        'file_name': 'str',
        'file_size': 'int',
        'is_encrypted': 'bool',
        'node_id': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'backup_region': 'BackupRegion',
        'backup_status': 'BackupStatus',
        'download_status': 'DownloadStatus',
        'file_name': 'FileName',
        'file_size': 'FileSize',
        'is_encrypted': 'IsEncrypted',
        'node_id': 'NodeId',
        'update_time': 'UpdateTime'
    }

    def __init__(self, backup_region=None, backup_status=None, download_status=None, file_name=None, file_size=None, is_encrypted=None, node_id=None, update_time=None, _configuration=None):  # noqa: E501
        """BinlogFileForDescribeBinlogFilesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_region = None
        self._backup_status = None
        self._download_status = None
        self._file_name = None
        self._file_size = None
        self._is_encrypted = None
        self._node_id = None
        self._update_time = None
        self.discriminator = None

        if backup_region is not None:
            self.backup_region = backup_region
        if backup_status is not None:
            self.backup_status = backup_status
        if download_status is not None:
            self.download_status = download_status
        if file_name is not None:
            self.file_name = file_name
        if file_size is not None:
            self.file_size = file_size
        if is_encrypted is not None:
            self.is_encrypted = is_encrypted
        if node_id is not None:
            self.node_id = node_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def backup_region(self):
        """Gets the backup_region of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501


        :return: The backup_region of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_region

    @backup_region.setter
    def backup_region(self, backup_region):
        """Sets the backup_region of this BinlogFileForDescribeBinlogFilesOutput.


        :param backup_region: The backup_region of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :type: str
        """

        self._backup_region = backup_region

    @property
    def backup_status(self):
        """Gets the backup_status of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501


        :return: The backup_status of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_status

    @backup_status.setter
    def backup_status(self, backup_status):
        """Sets the backup_status of this BinlogFileForDescribeBinlogFilesOutput.


        :param backup_status: The backup_status of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :type: str
        """

        self._backup_status = backup_status

    @property
    def download_status(self):
        """Gets the download_status of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501


        :return: The download_status of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._download_status

    @download_status.setter
    def download_status(self, download_status):
        """Sets the download_status of this BinlogFileForDescribeBinlogFilesOutput.


        :param download_status: The download_status of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :type: str
        """

        self._download_status = download_status

    @property
    def file_name(self):
        """Gets the file_name of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501


        :return: The file_name of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_name

    @file_name.setter
    def file_name(self, file_name):
        """Sets the file_name of this BinlogFileForDescribeBinlogFilesOutput.


        :param file_name: The file_name of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :type: str
        """

        self._file_name = file_name

    @property
    def file_size(self):
        """Gets the file_size of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501


        :return: The file_size of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :rtype: int
        """
        return self._file_size

    @file_size.setter
    def file_size(self, file_size):
        """Sets the file_size of this BinlogFileForDescribeBinlogFilesOutput.


        :param file_size: The file_size of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :type: int
        """

        self._file_size = file_size

    @property
    def is_encrypted(self):
        """Gets the is_encrypted of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501


        :return: The is_encrypted of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_encrypted

    @is_encrypted.setter
    def is_encrypted(self, is_encrypted):
        """Sets the is_encrypted of this BinlogFileForDescribeBinlogFilesOutput.


        :param is_encrypted: The is_encrypted of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :type: bool
        """

        self._is_encrypted = is_encrypted

    @property
    def node_id(self):
        """Gets the node_id of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501


        :return: The node_id of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this BinlogFileForDescribeBinlogFilesOutput.


        :param node_id: The node_id of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def update_time(self):
        """Gets the update_time of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501


        :return: The update_time of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this BinlogFileForDescribeBinlogFilesOutput.


        :param update_time: The update_time of this BinlogFileForDescribeBinlogFilesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BinlogFileForDescribeBinlogFilesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BinlogFileForDescribeBinlogFilesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BinlogFileForDescribeBinlogFilesOutput):
            return True

        return self.to_dict() != other.to_dict()
