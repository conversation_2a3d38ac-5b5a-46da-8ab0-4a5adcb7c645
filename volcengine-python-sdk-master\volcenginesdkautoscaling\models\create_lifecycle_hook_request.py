# coding: utf-8

"""
    auto_scaling

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateLifecycleHookRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'lifecycle_command': 'LifecycleCommandForCreateLifecycleHookInput',
        'lifecycle_hook_name': 'str',
        'lifecycle_hook_policy': 'str',
        'lifecycle_hook_timeout': 'int',
        'lifecycle_hook_type': 'str',
        'scaling_group_id': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'lifecycle_command': 'LifecycleCommand',
        'lifecycle_hook_name': 'LifecycleHookName',
        'lifecycle_hook_policy': 'LifecycleHookPolicy',
        'lifecycle_hook_timeout': 'LifecycleHookTimeout',
        'lifecycle_hook_type': 'LifecycleHookType',
        'scaling_group_id': 'ScalingGroupId'
    }

    def __init__(self, client_token=None, lifecycle_command=None, lifecycle_hook_name=None, lifecycle_hook_policy=None, lifecycle_hook_timeout=None, lifecycle_hook_type=None, scaling_group_id=None, _configuration=None):  # noqa: E501
        """CreateLifecycleHookRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._lifecycle_command = None
        self._lifecycle_hook_name = None
        self._lifecycle_hook_policy = None
        self._lifecycle_hook_timeout = None
        self._lifecycle_hook_type = None
        self._scaling_group_id = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if lifecycle_command is not None:
            self.lifecycle_command = lifecycle_command
        self.lifecycle_hook_name = lifecycle_hook_name
        self.lifecycle_hook_policy = lifecycle_hook_policy
        self.lifecycle_hook_timeout = lifecycle_hook_timeout
        self.lifecycle_hook_type = lifecycle_hook_type
        self.scaling_group_id = scaling_group_id

    @property
    def client_token(self):
        """Gets the client_token of this CreateLifecycleHookRequest.  # noqa: E501


        :return: The client_token of this CreateLifecycleHookRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateLifecycleHookRequest.


        :param client_token: The client_token of this CreateLifecycleHookRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def lifecycle_command(self):
        """Gets the lifecycle_command of this CreateLifecycleHookRequest.  # noqa: E501


        :return: The lifecycle_command of this CreateLifecycleHookRequest.  # noqa: E501
        :rtype: LifecycleCommandForCreateLifecycleHookInput
        """
        return self._lifecycle_command

    @lifecycle_command.setter
    def lifecycle_command(self, lifecycle_command):
        """Sets the lifecycle_command of this CreateLifecycleHookRequest.


        :param lifecycle_command: The lifecycle_command of this CreateLifecycleHookRequest.  # noqa: E501
        :type: LifecycleCommandForCreateLifecycleHookInput
        """

        self._lifecycle_command = lifecycle_command

    @property
    def lifecycle_hook_name(self):
        """Gets the lifecycle_hook_name of this CreateLifecycleHookRequest.  # noqa: E501


        :return: The lifecycle_hook_name of this CreateLifecycleHookRequest.  # noqa: E501
        :rtype: str
        """
        return self._lifecycle_hook_name

    @lifecycle_hook_name.setter
    def lifecycle_hook_name(self, lifecycle_hook_name):
        """Sets the lifecycle_hook_name of this CreateLifecycleHookRequest.


        :param lifecycle_hook_name: The lifecycle_hook_name of this CreateLifecycleHookRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and lifecycle_hook_name is None:
            raise ValueError("Invalid value for `lifecycle_hook_name`, must not be `None`")  # noqa: E501

        self._lifecycle_hook_name = lifecycle_hook_name

    @property
    def lifecycle_hook_policy(self):
        """Gets the lifecycle_hook_policy of this CreateLifecycleHookRequest.  # noqa: E501


        :return: The lifecycle_hook_policy of this CreateLifecycleHookRequest.  # noqa: E501
        :rtype: str
        """
        return self._lifecycle_hook_policy

    @lifecycle_hook_policy.setter
    def lifecycle_hook_policy(self, lifecycle_hook_policy):
        """Sets the lifecycle_hook_policy of this CreateLifecycleHookRequest.


        :param lifecycle_hook_policy: The lifecycle_hook_policy of this CreateLifecycleHookRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and lifecycle_hook_policy is None:
            raise ValueError("Invalid value for `lifecycle_hook_policy`, must not be `None`")  # noqa: E501

        self._lifecycle_hook_policy = lifecycle_hook_policy

    @property
    def lifecycle_hook_timeout(self):
        """Gets the lifecycle_hook_timeout of this CreateLifecycleHookRequest.  # noqa: E501


        :return: The lifecycle_hook_timeout of this CreateLifecycleHookRequest.  # noqa: E501
        :rtype: int
        """
        return self._lifecycle_hook_timeout

    @lifecycle_hook_timeout.setter
    def lifecycle_hook_timeout(self, lifecycle_hook_timeout):
        """Sets the lifecycle_hook_timeout of this CreateLifecycleHookRequest.


        :param lifecycle_hook_timeout: The lifecycle_hook_timeout of this CreateLifecycleHookRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and lifecycle_hook_timeout is None:
            raise ValueError("Invalid value for `lifecycle_hook_timeout`, must not be `None`")  # noqa: E501

        self._lifecycle_hook_timeout = lifecycle_hook_timeout

    @property
    def lifecycle_hook_type(self):
        """Gets the lifecycle_hook_type of this CreateLifecycleHookRequest.  # noqa: E501


        :return: The lifecycle_hook_type of this CreateLifecycleHookRequest.  # noqa: E501
        :rtype: str
        """
        return self._lifecycle_hook_type

    @lifecycle_hook_type.setter
    def lifecycle_hook_type(self, lifecycle_hook_type):
        """Sets the lifecycle_hook_type of this CreateLifecycleHookRequest.


        :param lifecycle_hook_type: The lifecycle_hook_type of this CreateLifecycleHookRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and lifecycle_hook_type is None:
            raise ValueError("Invalid value for `lifecycle_hook_type`, must not be `None`")  # noqa: E501

        self._lifecycle_hook_type = lifecycle_hook_type

    @property
    def scaling_group_id(self):
        """Gets the scaling_group_id of this CreateLifecycleHookRequest.  # noqa: E501


        :return: The scaling_group_id of this CreateLifecycleHookRequest.  # noqa: E501
        :rtype: str
        """
        return self._scaling_group_id

    @scaling_group_id.setter
    def scaling_group_id(self, scaling_group_id):
        """Sets the scaling_group_id of this CreateLifecycleHookRequest.


        :param scaling_group_id: The scaling_group_id of this CreateLifecycleHookRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and scaling_group_id is None:
            raise ValueError("Invalid value for `scaling_group_id`, must not be `None`")  # noqa: E501

        self._scaling_group_id = scaling_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateLifecycleHookRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateLifecycleHookRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateLifecycleHookRequest):
            return True

        return self.to_dict() != other.to_dict()
