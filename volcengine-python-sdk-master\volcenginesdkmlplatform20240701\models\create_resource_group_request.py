# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateResourceGroupRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_renew': 'bool',
        'auto_renew_period': 'int',
        'charge_type': 'str',
        'description': 'str',
        'dry_run': 'bool',
        'm_gpu_enabled': 'bool',
        'name': 'str',
        'period': 'int',
        'period_unit': 'str',
        'resource_config': 'ResourceConfigForCreateResourceGroupInput',
        'storage_config': 'StorageConfigForCreateResourceGroupInput',
        'workload_network_config': 'WorkloadNetworkConfigForCreateResourceGroupInput',
        'workload_network_mode': 'str'
    }

    attribute_map = {
        'auto_renew': 'AutoRenew',
        'auto_renew_period': 'AutoRenewPeriod',
        'charge_type': 'ChargeType',
        'description': 'Description',
        'dry_run': 'DryRun',
        'm_gpu_enabled': 'MGpuEnabled',
        'name': 'Name',
        'period': 'Period',
        'period_unit': 'PeriodUnit',
        'resource_config': 'ResourceConfig',
        'storage_config': 'StorageConfig',
        'workload_network_config': 'WorkloadNetworkConfig',
        'workload_network_mode': 'WorkloadNetworkMode'
    }

    def __init__(self, auto_renew=None, auto_renew_period=None, charge_type=None, description=None, dry_run=None, m_gpu_enabled=None, name=None, period=None, period_unit=None, resource_config=None, storage_config=None, workload_network_config=None, workload_network_mode=None, _configuration=None):  # noqa: E501
        """CreateResourceGroupRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_renew = None
        self._auto_renew_period = None
        self._charge_type = None
        self._description = None
        self._dry_run = None
        self._m_gpu_enabled = None
        self._name = None
        self._period = None
        self._period_unit = None
        self._resource_config = None
        self._storage_config = None
        self._workload_network_config = None
        self._workload_network_mode = None
        self.discriminator = None

        if auto_renew is not None:
            self.auto_renew = auto_renew
        if auto_renew_period is not None:
            self.auto_renew_period = auto_renew_period
        self.charge_type = charge_type
        if description is not None:
            self.description = description
        if dry_run is not None:
            self.dry_run = dry_run
        if m_gpu_enabled is not None:
            self.m_gpu_enabled = m_gpu_enabled
        self.name = name
        if period is not None:
            self.period = period
        if period_unit is not None:
            self.period_unit = period_unit
        if resource_config is not None:
            self.resource_config = resource_config
        if storage_config is not None:
            self.storage_config = storage_config
        if workload_network_config is not None:
            self.workload_network_config = workload_network_config
        if workload_network_mode is not None:
            self.workload_network_mode = workload_network_mode

    @property
    def auto_renew(self):
        """Gets the auto_renew of this CreateResourceGroupRequest.  # noqa: E501


        :return: The auto_renew of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this CreateResourceGroupRequest.


        :param auto_renew: The auto_renew of this CreateResourceGroupRequest.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def auto_renew_period(self):
        """Gets the auto_renew_period of this CreateResourceGroupRequest.  # noqa: E501


        :return: The auto_renew_period of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: int
        """
        return self._auto_renew_period

    @auto_renew_period.setter
    def auto_renew_period(self, auto_renew_period):
        """Sets the auto_renew_period of this CreateResourceGroupRequest.


        :param auto_renew_period: The auto_renew_period of this CreateResourceGroupRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                auto_renew_period is not None and auto_renew_period < 1):  # noqa: E501
            raise ValueError("Invalid value for `auto_renew_period`, must be a value greater than or equal to `1`")  # noqa: E501

        self._auto_renew_period = auto_renew_period

    @property
    def charge_type(self):
        """Gets the charge_type of this CreateResourceGroupRequest.  # noqa: E501


        :return: The charge_type of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this CreateResourceGroupRequest.


        :param charge_type: The charge_type of this CreateResourceGroupRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and charge_type is None:
            raise ValueError("Invalid value for `charge_type`, must not be `None`")  # noqa: E501
        allowed_values = ["PrePaid", "PostPaid", "OnDemand"]  # noqa: E501
        if (self._configuration.client_side_validation and
                charge_type not in allowed_values):
            raise ValueError(
                "Invalid value for `charge_type` ({0}), must be one of {1}"  # noqa: E501
                .format(charge_type, allowed_values)
            )

        self._charge_type = charge_type

    @property
    def description(self):
        """Gets the description of this CreateResourceGroupRequest.  # noqa: E501


        :return: The description of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateResourceGroupRequest.


        :param description: The description of this CreateResourceGroupRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 500):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `500`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def dry_run(self):
        """Gets the dry_run of this CreateResourceGroupRequest.  # noqa: E501


        :return: The dry_run of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._dry_run

    @dry_run.setter
    def dry_run(self, dry_run):
        """Sets the dry_run of this CreateResourceGroupRequest.


        :param dry_run: The dry_run of this CreateResourceGroupRequest.  # noqa: E501
        :type: bool
        """

        self._dry_run = dry_run

    @property
    def m_gpu_enabled(self):
        """Gets the m_gpu_enabled of this CreateResourceGroupRequest.  # noqa: E501


        :return: The m_gpu_enabled of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._m_gpu_enabled

    @m_gpu_enabled.setter
    def m_gpu_enabled(self, m_gpu_enabled):
        """Sets the m_gpu_enabled of this CreateResourceGroupRequest.


        :param m_gpu_enabled: The m_gpu_enabled of this CreateResourceGroupRequest.  # noqa: E501
        :type: bool
        """

        self._m_gpu_enabled = m_gpu_enabled

    @property
    def name(self):
        """Gets the name of this CreateResourceGroupRequest.  # noqa: E501


        :return: The name of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateResourceGroupRequest.


        :param name: The name of this CreateResourceGroupRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) > 200):
            raise ValueError("Invalid value for `name`, length must be less than or equal to `200`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) < 1):
            raise ValueError("Invalid value for `name`, length must be greater than or equal to `1`")  # noqa: E501

        self._name = name

    @property
    def period(self):
        """Gets the period of this CreateResourceGroupRequest.  # noqa: E501


        :return: The period of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this CreateResourceGroupRequest.


        :param period: The period of this CreateResourceGroupRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                period is not None and period < 1):  # noqa: E501
            raise ValueError("Invalid value for `period`, must be a value greater than or equal to `1`")  # noqa: E501

        self._period = period

    @property
    def period_unit(self):
        """Gets the period_unit of this CreateResourceGroupRequest.  # noqa: E501


        :return: The period_unit of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this CreateResourceGroupRequest.


        :param period_unit: The period_unit of this CreateResourceGroupRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Day", "Month"]  # noqa: E501
        if (self._configuration.client_side_validation and
                period_unit not in allowed_values):
            raise ValueError(
                "Invalid value for `period_unit` ({0}), must be one of {1}"  # noqa: E501
                .format(period_unit, allowed_values)
            )

        self._period_unit = period_unit

    @property
    def resource_config(self):
        """Gets the resource_config of this CreateResourceGroupRequest.  # noqa: E501


        :return: The resource_config of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: ResourceConfigForCreateResourceGroupInput
        """
        return self._resource_config

    @resource_config.setter
    def resource_config(self, resource_config):
        """Sets the resource_config of this CreateResourceGroupRequest.


        :param resource_config: The resource_config of this CreateResourceGroupRequest.  # noqa: E501
        :type: ResourceConfigForCreateResourceGroupInput
        """

        self._resource_config = resource_config

    @property
    def storage_config(self):
        """Gets the storage_config of this CreateResourceGroupRequest.  # noqa: E501


        :return: The storage_config of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: StorageConfigForCreateResourceGroupInput
        """
        return self._storage_config

    @storage_config.setter
    def storage_config(self, storage_config):
        """Sets the storage_config of this CreateResourceGroupRequest.


        :param storage_config: The storage_config of this CreateResourceGroupRequest.  # noqa: E501
        :type: StorageConfigForCreateResourceGroupInput
        """

        self._storage_config = storage_config

    @property
    def workload_network_config(self):
        """Gets the workload_network_config of this CreateResourceGroupRequest.  # noqa: E501


        :return: The workload_network_config of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: WorkloadNetworkConfigForCreateResourceGroupInput
        """
        return self._workload_network_config

    @workload_network_config.setter
    def workload_network_config(self, workload_network_config):
        """Sets the workload_network_config of this CreateResourceGroupRequest.


        :param workload_network_config: The workload_network_config of this CreateResourceGroupRequest.  # noqa: E501
        :type: WorkloadNetworkConfigForCreateResourceGroupInput
        """

        self._workload_network_config = workload_network_config

    @property
    def workload_network_mode(self):
        """Gets the workload_network_mode of this CreateResourceGroupRequest.  # noqa: E501


        :return: The workload_network_mode of this CreateResourceGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._workload_network_mode

    @workload_network_mode.setter
    def workload_network_mode(self, workload_network_mode):
        """Sets the workload_network_mode of this CreateResourceGroupRequest.


        :param workload_network_mode: The workload_network_mode of this CreateResourceGroupRequest.  # noqa: E501
        :type: str
        """

        self._workload_network_mode = workload_network_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateResourceGroupRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateResourceGroupRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateResourceGroupRequest):
            return True

        return self.to_dict() != other.to_dict()
