# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ReleaseResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'current_traffic_weight': 'int',
        'error_code': 'str',
        'failed_instance_logs': 'str',
        'function_id': 'str',
        'new_revision_number': 'int',
        'old_revision_number': 'int',
        'release_record_id': 'str',
        'stable_revision_number': 'int',
        'start_time': 'str',
        'status': 'str',
        'status_message': 'str',
        'target_traffic_weight': 'int'
    }

    attribute_map = {
        'current_traffic_weight': 'CurrentTrafficWeight',
        'error_code': 'ErrorCode',
        'failed_instance_logs': 'FailedInstanceLogs',
        'function_id': 'FunctionId',
        'new_revision_number': 'NewRevisionNumber',
        'old_revision_number': 'OldRevisionNumber',
        'release_record_id': 'ReleaseRecordId',
        'stable_revision_number': 'StableRevisionNumber',
        'start_time': 'StartTime',
        'status': 'Status',
        'status_message': 'StatusMessage',
        'target_traffic_weight': 'TargetTrafficWeight'
    }

    def __init__(self, current_traffic_weight=None, error_code=None, failed_instance_logs=None, function_id=None, new_revision_number=None, old_revision_number=None, release_record_id=None, stable_revision_number=None, start_time=None, status=None, status_message=None, target_traffic_weight=None, _configuration=None):  # noqa: E501
        """ReleaseResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._current_traffic_weight = None
        self._error_code = None
        self._failed_instance_logs = None
        self._function_id = None
        self._new_revision_number = None
        self._old_revision_number = None
        self._release_record_id = None
        self._stable_revision_number = None
        self._start_time = None
        self._status = None
        self._status_message = None
        self._target_traffic_weight = None
        self.discriminator = None

        if current_traffic_weight is not None:
            self.current_traffic_weight = current_traffic_weight
        if error_code is not None:
            self.error_code = error_code
        if failed_instance_logs is not None:
            self.failed_instance_logs = failed_instance_logs
        if function_id is not None:
            self.function_id = function_id
        if new_revision_number is not None:
            self.new_revision_number = new_revision_number
        if old_revision_number is not None:
            self.old_revision_number = old_revision_number
        if release_record_id is not None:
            self.release_record_id = release_record_id
        if stable_revision_number is not None:
            self.stable_revision_number = stable_revision_number
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if status_message is not None:
            self.status_message = status_message
        if target_traffic_weight is not None:
            self.target_traffic_weight = target_traffic_weight

    @property
    def current_traffic_weight(self):
        """Gets the current_traffic_weight of this ReleaseResponse.  # noqa: E501


        :return: The current_traffic_weight of this ReleaseResponse.  # noqa: E501
        :rtype: int
        """
        return self._current_traffic_weight

    @current_traffic_weight.setter
    def current_traffic_weight(self, current_traffic_weight):
        """Sets the current_traffic_weight of this ReleaseResponse.


        :param current_traffic_weight: The current_traffic_weight of this ReleaseResponse.  # noqa: E501
        :type: int
        """

        self._current_traffic_weight = current_traffic_weight

    @property
    def error_code(self):
        """Gets the error_code of this ReleaseResponse.  # noqa: E501


        :return: The error_code of this ReleaseResponse.  # noqa: E501
        :rtype: str
        """
        return self._error_code

    @error_code.setter
    def error_code(self, error_code):
        """Sets the error_code of this ReleaseResponse.


        :param error_code: The error_code of this ReleaseResponse.  # noqa: E501
        :type: str
        """

        self._error_code = error_code

    @property
    def failed_instance_logs(self):
        """Gets the failed_instance_logs of this ReleaseResponse.  # noqa: E501


        :return: The failed_instance_logs of this ReleaseResponse.  # noqa: E501
        :rtype: str
        """
        return self._failed_instance_logs

    @failed_instance_logs.setter
    def failed_instance_logs(self, failed_instance_logs):
        """Sets the failed_instance_logs of this ReleaseResponse.


        :param failed_instance_logs: The failed_instance_logs of this ReleaseResponse.  # noqa: E501
        :type: str
        """

        self._failed_instance_logs = failed_instance_logs

    @property
    def function_id(self):
        """Gets the function_id of this ReleaseResponse.  # noqa: E501


        :return: The function_id of this ReleaseResponse.  # noqa: E501
        :rtype: str
        """
        return self._function_id

    @function_id.setter
    def function_id(self, function_id):
        """Sets the function_id of this ReleaseResponse.


        :param function_id: The function_id of this ReleaseResponse.  # noqa: E501
        :type: str
        """

        self._function_id = function_id

    @property
    def new_revision_number(self):
        """Gets the new_revision_number of this ReleaseResponse.  # noqa: E501


        :return: The new_revision_number of this ReleaseResponse.  # noqa: E501
        :rtype: int
        """
        return self._new_revision_number

    @new_revision_number.setter
    def new_revision_number(self, new_revision_number):
        """Sets the new_revision_number of this ReleaseResponse.


        :param new_revision_number: The new_revision_number of this ReleaseResponse.  # noqa: E501
        :type: int
        """

        self._new_revision_number = new_revision_number

    @property
    def old_revision_number(self):
        """Gets the old_revision_number of this ReleaseResponse.  # noqa: E501


        :return: The old_revision_number of this ReleaseResponse.  # noqa: E501
        :rtype: int
        """
        return self._old_revision_number

    @old_revision_number.setter
    def old_revision_number(self, old_revision_number):
        """Sets the old_revision_number of this ReleaseResponse.


        :param old_revision_number: The old_revision_number of this ReleaseResponse.  # noqa: E501
        :type: int
        """

        self._old_revision_number = old_revision_number

    @property
    def release_record_id(self):
        """Gets the release_record_id of this ReleaseResponse.  # noqa: E501


        :return: The release_record_id of this ReleaseResponse.  # noqa: E501
        :rtype: str
        """
        return self._release_record_id

    @release_record_id.setter
    def release_record_id(self, release_record_id):
        """Sets the release_record_id of this ReleaseResponse.


        :param release_record_id: The release_record_id of this ReleaseResponse.  # noqa: E501
        :type: str
        """

        self._release_record_id = release_record_id

    @property
    def stable_revision_number(self):
        """Gets the stable_revision_number of this ReleaseResponse.  # noqa: E501


        :return: The stable_revision_number of this ReleaseResponse.  # noqa: E501
        :rtype: int
        """
        return self._stable_revision_number

    @stable_revision_number.setter
    def stable_revision_number(self, stable_revision_number):
        """Sets the stable_revision_number of this ReleaseResponse.


        :param stable_revision_number: The stable_revision_number of this ReleaseResponse.  # noqa: E501
        :type: int
        """

        self._stable_revision_number = stable_revision_number

    @property
    def start_time(self):
        """Gets the start_time of this ReleaseResponse.  # noqa: E501


        :return: The start_time of this ReleaseResponse.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ReleaseResponse.


        :param start_time: The start_time of this ReleaseResponse.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this ReleaseResponse.  # noqa: E501


        :return: The status of this ReleaseResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ReleaseResponse.


        :param status: The status of this ReleaseResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def status_message(self):
        """Gets the status_message of this ReleaseResponse.  # noqa: E501


        :return: The status_message of this ReleaseResponse.  # noqa: E501
        :rtype: str
        """
        return self._status_message

    @status_message.setter
    def status_message(self, status_message):
        """Sets the status_message of this ReleaseResponse.


        :param status_message: The status_message of this ReleaseResponse.  # noqa: E501
        :type: str
        """

        self._status_message = status_message

    @property
    def target_traffic_weight(self):
        """Gets the target_traffic_weight of this ReleaseResponse.  # noqa: E501


        :return: The target_traffic_weight of this ReleaseResponse.  # noqa: E501
        :rtype: int
        """
        return self._target_traffic_weight

    @target_traffic_weight.setter
    def target_traffic_weight(self, target_traffic_weight):
        """Sets the target_traffic_weight of this ReleaseResponse.


        :param target_traffic_weight: The target_traffic_weight of this ReleaseResponse.  # noqa: E501
        :type: int
        """

        self._target_traffic_weight = target_traffic_weight

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReleaseResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReleaseResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ReleaseResponse):
            return True

        return self.to_dict() != other.to_dict()
