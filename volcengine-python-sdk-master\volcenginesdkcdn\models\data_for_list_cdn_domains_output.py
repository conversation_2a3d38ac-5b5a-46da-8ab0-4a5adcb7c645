# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListCdnDomainsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_cname': 'str',
        'backup_origin': 'list[str]',
        'cache_shared': 'str',
        'cache_shared_target_host': 'str',
        'cname': 'str',
        'config_status': 'str',
        'create_time': 'int',
        'domain': 'str',
        'domain_lock': 'DomainLockForListCdnDomainsOutput',
        'feature_config': 'FeatureConfigForListCdnDomainsOutput',
        'https': 'bool',
        'ipv6': 'bool',
        'is_conflict_domain': 'bool',
        'origin_protocol': 'str',
        'primary_origin': 'list[str]',
        'project': 'str',
        'resource_tags': 'list[ResourceTagForListCdnDomainsOutput]',
        'service_region': 'str',
        'service_type': 'str',
        'status': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'backup_cname': 'BackupCname',
        'backup_origin': 'BackupOrigin',
        'cache_shared': 'CacheShared',
        'cache_shared_target_host': 'CacheSharedTargetHost',
        'cname': 'Cname',
        'config_status': 'ConfigStatus',
        'create_time': 'CreateTime',
        'domain': 'Domain',
        'domain_lock': 'DomainLock',
        'feature_config': 'FeatureConfig',
        'https': 'HTTPS',
        'ipv6': 'IPv6',
        'is_conflict_domain': 'IsConflictDomain',
        'origin_protocol': 'OriginProtocol',
        'primary_origin': 'PrimaryOrigin',
        'project': 'Project',
        'resource_tags': 'ResourceTags',
        'service_region': 'ServiceRegion',
        'service_type': 'ServiceType',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, backup_cname=None, backup_origin=None, cache_shared=None, cache_shared_target_host=None, cname=None, config_status=None, create_time=None, domain=None, domain_lock=None, feature_config=None, https=None, ipv6=None, is_conflict_domain=None, origin_protocol=None, primary_origin=None, project=None, resource_tags=None, service_region=None, service_type=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """DataForListCdnDomainsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_cname = None
        self._backup_origin = None
        self._cache_shared = None
        self._cache_shared_target_host = None
        self._cname = None
        self._config_status = None
        self._create_time = None
        self._domain = None
        self._domain_lock = None
        self._feature_config = None
        self._https = None
        self._ipv6 = None
        self._is_conflict_domain = None
        self._origin_protocol = None
        self._primary_origin = None
        self._project = None
        self._resource_tags = None
        self._service_region = None
        self._service_type = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if backup_cname is not None:
            self.backup_cname = backup_cname
        if backup_origin is not None:
            self.backup_origin = backup_origin
        if cache_shared is not None:
            self.cache_shared = cache_shared
        if cache_shared_target_host is not None:
            self.cache_shared_target_host = cache_shared_target_host
        if cname is not None:
            self.cname = cname
        if config_status is not None:
            self.config_status = config_status
        if create_time is not None:
            self.create_time = create_time
        if domain is not None:
            self.domain = domain
        if domain_lock is not None:
            self.domain_lock = domain_lock
        if feature_config is not None:
            self.feature_config = feature_config
        if https is not None:
            self.https = https
        if ipv6 is not None:
            self.ipv6 = ipv6
        if is_conflict_domain is not None:
            self.is_conflict_domain = is_conflict_domain
        if origin_protocol is not None:
            self.origin_protocol = origin_protocol
        if primary_origin is not None:
            self.primary_origin = primary_origin
        if project is not None:
            self.project = project
        if resource_tags is not None:
            self.resource_tags = resource_tags
        if service_region is not None:
            self.service_region = service_region
        if service_type is not None:
            self.service_type = service_type
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def backup_cname(self):
        """Gets the backup_cname of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The backup_cname of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._backup_cname

    @backup_cname.setter
    def backup_cname(self, backup_cname):
        """Sets the backup_cname of this DataForListCdnDomainsOutput.


        :param backup_cname: The backup_cname of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._backup_cname = backup_cname

    @property
    def backup_origin(self):
        """Gets the backup_origin of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The backup_origin of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._backup_origin

    @backup_origin.setter
    def backup_origin(self, backup_origin):
        """Sets the backup_origin of this DataForListCdnDomainsOutput.


        :param backup_origin: The backup_origin of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: list[str]
        """

        self._backup_origin = backup_origin

    @property
    def cache_shared(self):
        """Gets the cache_shared of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The cache_shared of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cache_shared

    @cache_shared.setter
    def cache_shared(self, cache_shared):
        """Sets the cache_shared of this DataForListCdnDomainsOutput.


        :param cache_shared: The cache_shared of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._cache_shared = cache_shared

    @property
    def cache_shared_target_host(self):
        """Gets the cache_shared_target_host of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The cache_shared_target_host of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cache_shared_target_host

    @cache_shared_target_host.setter
    def cache_shared_target_host(self, cache_shared_target_host):
        """Sets the cache_shared_target_host of this DataForListCdnDomainsOutput.


        :param cache_shared_target_host: The cache_shared_target_host of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._cache_shared_target_host = cache_shared_target_host

    @property
    def cname(self):
        """Gets the cname of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The cname of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cname

    @cname.setter
    def cname(self, cname):
        """Sets the cname of this DataForListCdnDomainsOutput.


        :param cname: The cname of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._cname = cname

    @property
    def config_status(self):
        """Gets the config_status of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The config_status of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_status

    @config_status.setter
    def config_status(self, config_status):
        """Sets the config_status of this DataForListCdnDomainsOutput.


        :param config_status: The config_status of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._config_status = config_status

    @property
    def create_time(self):
        """Gets the create_time of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The create_time of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForListCdnDomainsOutput.


        :param create_time: The create_time of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def domain(self):
        """Gets the domain of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The domain of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this DataForListCdnDomainsOutput.


        :param domain: The domain of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._domain = domain

    @property
    def domain_lock(self):
        """Gets the domain_lock of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The domain_lock of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: DomainLockForListCdnDomainsOutput
        """
        return self._domain_lock

    @domain_lock.setter
    def domain_lock(self, domain_lock):
        """Sets the domain_lock of this DataForListCdnDomainsOutput.


        :param domain_lock: The domain_lock of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: DomainLockForListCdnDomainsOutput
        """

        self._domain_lock = domain_lock

    @property
    def feature_config(self):
        """Gets the feature_config of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The feature_config of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: FeatureConfigForListCdnDomainsOutput
        """
        return self._feature_config

    @feature_config.setter
    def feature_config(self, feature_config):
        """Sets the feature_config of this DataForListCdnDomainsOutput.


        :param feature_config: The feature_config of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: FeatureConfigForListCdnDomainsOutput
        """

        self._feature_config = feature_config

    @property
    def https(self):
        """Gets the https of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The https of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._https

    @https.setter
    def https(self, https):
        """Sets the https of this DataForListCdnDomainsOutput.


        :param https: The https of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: bool
        """

        self._https = https

    @property
    def ipv6(self):
        """Gets the ipv6 of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The ipv6 of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._ipv6

    @ipv6.setter
    def ipv6(self, ipv6):
        """Sets the ipv6 of this DataForListCdnDomainsOutput.


        :param ipv6: The ipv6 of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: bool
        """

        self._ipv6 = ipv6

    @property
    def is_conflict_domain(self):
        """Gets the is_conflict_domain of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The is_conflict_domain of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_conflict_domain

    @is_conflict_domain.setter
    def is_conflict_domain(self, is_conflict_domain):
        """Sets the is_conflict_domain of this DataForListCdnDomainsOutput.


        :param is_conflict_domain: The is_conflict_domain of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: bool
        """

        self._is_conflict_domain = is_conflict_domain

    @property
    def origin_protocol(self):
        """Gets the origin_protocol of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The origin_protocol of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._origin_protocol

    @origin_protocol.setter
    def origin_protocol(self, origin_protocol):
        """Sets the origin_protocol of this DataForListCdnDomainsOutput.


        :param origin_protocol: The origin_protocol of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._origin_protocol = origin_protocol

    @property
    def primary_origin(self):
        """Gets the primary_origin of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The primary_origin of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._primary_origin

    @primary_origin.setter
    def primary_origin(self, primary_origin):
        """Sets the primary_origin of this DataForListCdnDomainsOutput.


        :param primary_origin: The primary_origin of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: list[str]
        """

        self._primary_origin = primary_origin

    @property
    def project(self):
        """Gets the project of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The project of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this DataForListCdnDomainsOutput.


        :param project: The project of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def resource_tags(self):
        """Gets the resource_tags of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The resource_tags of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: list[ResourceTagForListCdnDomainsOutput]
        """
        return self._resource_tags

    @resource_tags.setter
    def resource_tags(self, resource_tags):
        """Sets the resource_tags of this DataForListCdnDomainsOutput.


        :param resource_tags: The resource_tags of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: list[ResourceTagForListCdnDomainsOutput]
        """

        self._resource_tags = resource_tags

    @property
    def service_region(self):
        """Gets the service_region of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The service_region of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_region

    @service_region.setter
    def service_region(self, service_region):
        """Sets the service_region of this DataForListCdnDomainsOutput.


        :param service_region: The service_region of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._service_region = service_region

    @property
    def service_type(self):
        """Gets the service_type of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The service_type of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_type

    @service_type.setter
    def service_type(self, service_type):
        """Sets the service_type of this DataForListCdnDomainsOutput.


        :param service_type: The service_type of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._service_type = service_type

    @property
    def status(self):
        """Gets the status of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The status of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListCdnDomainsOutput.


        :param status: The status of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this DataForListCdnDomainsOutput.  # noqa: E501


        :return: The update_time of this DataForListCdnDomainsOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForListCdnDomainsOutput.


        :param update_time: The update_time of this DataForListCdnDomainsOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListCdnDomainsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListCdnDomainsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListCdnDomainsOutput):
            return True

        return self.to_dict() != other.to_dict()
