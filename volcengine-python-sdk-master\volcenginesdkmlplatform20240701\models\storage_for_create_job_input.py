# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StorageForCreateJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'config': 'ConfigForCreateJobInput',
        'mount_path': 'str',
        'read_only': 'bool',
        'type': 'str'
    }

    attribute_map = {
        'config': 'Config',
        'mount_path': 'MountPath',
        'read_only': 'ReadOnly',
        'type': 'Type'
    }

    def __init__(self, config=None, mount_path=None, read_only=None, type=None, _configuration=None):  # noqa: E501
        """StorageForCreateJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._config = None
        self._mount_path = None
        self._read_only = None
        self._type = None
        self.discriminator = None

        if config is not None:
            self.config = config
        if mount_path is not None:
            self.mount_path = mount_path
        if read_only is not None:
            self.read_only = read_only
        if type is not None:
            self.type = type

    @property
    def config(self):
        """Gets the config of this StorageForCreateJobInput.  # noqa: E501


        :return: The config of this StorageForCreateJobInput.  # noqa: E501
        :rtype: ConfigForCreateJobInput
        """
        return self._config

    @config.setter
    def config(self, config):
        """Sets the config of this StorageForCreateJobInput.


        :param config: The config of this StorageForCreateJobInput.  # noqa: E501
        :type: ConfigForCreateJobInput
        """

        self._config = config

    @property
    def mount_path(self):
        """Gets the mount_path of this StorageForCreateJobInput.  # noqa: E501


        :return: The mount_path of this StorageForCreateJobInput.  # noqa: E501
        :rtype: str
        """
        return self._mount_path

    @mount_path.setter
    def mount_path(self, mount_path):
        """Sets the mount_path of this StorageForCreateJobInput.


        :param mount_path: The mount_path of this StorageForCreateJobInput.  # noqa: E501
        :type: str
        """

        self._mount_path = mount_path

    @property
    def read_only(self):
        """Gets the read_only of this StorageForCreateJobInput.  # noqa: E501


        :return: The read_only of this StorageForCreateJobInput.  # noqa: E501
        :rtype: bool
        """
        return self._read_only

    @read_only.setter
    def read_only(self, read_only):
        """Sets the read_only of this StorageForCreateJobInput.


        :param read_only: The read_only of this StorageForCreateJobInput.  # noqa: E501
        :type: bool
        """

        self._read_only = read_only

    @property
    def type(self):
        """Gets the type of this StorageForCreateJobInput.  # noqa: E501


        :return: The type of this StorageForCreateJobInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this StorageForCreateJobInput.


        :param type: The type of this StorageForCreateJobInput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StorageForCreateJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StorageForCreateJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StorageForCreateJobInput):
            return True

        return self.to_dict() != other.to_dict()
