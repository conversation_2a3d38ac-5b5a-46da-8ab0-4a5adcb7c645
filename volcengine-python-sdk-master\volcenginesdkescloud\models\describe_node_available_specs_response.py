# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNodeAvailableSpecsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'az_available_specs_sold_out': 'AZAvailableSpecsSoldOutForDescribeNodeAvailableSpecsOutput',
        'configuration_code': 'str',
        'network_specs': 'list[NetworkSpecForDescribeNodeAvailableSpecsOutput]',
        'node_available_specs': 'list[NodeAvailableSpecForDescribeNodeAvailableSpecsOutput]',
        'resource_specs': 'list[ResourceSpecForDescribeNodeAvailableSpecsOutput]',
        'storage_specs': 'list[StorageSpecForDescribeNodeAvailableSpecsOutput]'
    }

    attribute_map = {
        'az_available_specs_sold_out': 'AZAvailableSpecsSoldOut',
        'configuration_code': 'ConfigurationCode',
        'network_specs': 'NetworkSpecs',
        'node_available_specs': 'NodeAvailableSpecs',
        'resource_specs': 'ResourceSpecs',
        'storage_specs': 'StorageSpecs'
    }

    def __init__(self, az_available_specs_sold_out=None, configuration_code=None, network_specs=None, node_available_specs=None, resource_specs=None, storage_specs=None, _configuration=None):  # noqa: E501
        """DescribeNodeAvailableSpecsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._az_available_specs_sold_out = None
        self._configuration_code = None
        self._network_specs = None
        self._node_available_specs = None
        self._resource_specs = None
        self._storage_specs = None
        self.discriminator = None

        if az_available_specs_sold_out is not None:
            self.az_available_specs_sold_out = az_available_specs_sold_out
        if configuration_code is not None:
            self.configuration_code = configuration_code
        if network_specs is not None:
            self.network_specs = network_specs
        if node_available_specs is not None:
            self.node_available_specs = node_available_specs
        if resource_specs is not None:
            self.resource_specs = resource_specs
        if storage_specs is not None:
            self.storage_specs = storage_specs

    @property
    def az_available_specs_sold_out(self):
        """Gets the az_available_specs_sold_out of this DescribeNodeAvailableSpecsResponse.  # noqa: E501


        :return: The az_available_specs_sold_out of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :rtype: AZAvailableSpecsSoldOutForDescribeNodeAvailableSpecsOutput
        """
        return self._az_available_specs_sold_out

    @az_available_specs_sold_out.setter
    def az_available_specs_sold_out(self, az_available_specs_sold_out):
        """Sets the az_available_specs_sold_out of this DescribeNodeAvailableSpecsResponse.


        :param az_available_specs_sold_out: The az_available_specs_sold_out of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :type: AZAvailableSpecsSoldOutForDescribeNodeAvailableSpecsOutput
        """

        self._az_available_specs_sold_out = az_available_specs_sold_out

    @property
    def configuration_code(self):
        """Gets the configuration_code of this DescribeNodeAvailableSpecsResponse.  # noqa: E501


        :return: The configuration_code of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this DescribeNodeAvailableSpecsResponse.


        :param configuration_code: The configuration_code of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def network_specs(self):
        """Gets the network_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501


        :return: The network_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :rtype: list[NetworkSpecForDescribeNodeAvailableSpecsOutput]
        """
        return self._network_specs

    @network_specs.setter
    def network_specs(self, network_specs):
        """Sets the network_specs of this DescribeNodeAvailableSpecsResponse.


        :param network_specs: The network_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :type: list[NetworkSpecForDescribeNodeAvailableSpecsOutput]
        """

        self._network_specs = network_specs

    @property
    def node_available_specs(self):
        """Gets the node_available_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501


        :return: The node_available_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :rtype: list[NodeAvailableSpecForDescribeNodeAvailableSpecsOutput]
        """
        return self._node_available_specs

    @node_available_specs.setter
    def node_available_specs(self, node_available_specs):
        """Sets the node_available_specs of this DescribeNodeAvailableSpecsResponse.


        :param node_available_specs: The node_available_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :type: list[NodeAvailableSpecForDescribeNodeAvailableSpecsOutput]
        """

        self._node_available_specs = node_available_specs

    @property
    def resource_specs(self):
        """Gets the resource_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501


        :return: The resource_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :rtype: list[ResourceSpecForDescribeNodeAvailableSpecsOutput]
        """
        return self._resource_specs

    @resource_specs.setter
    def resource_specs(self, resource_specs):
        """Sets the resource_specs of this DescribeNodeAvailableSpecsResponse.


        :param resource_specs: The resource_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :type: list[ResourceSpecForDescribeNodeAvailableSpecsOutput]
        """

        self._resource_specs = resource_specs

    @property
    def storage_specs(self):
        """Gets the storage_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501


        :return: The storage_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :rtype: list[StorageSpecForDescribeNodeAvailableSpecsOutput]
        """
        return self._storage_specs

    @storage_specs.setter
    def storage_specs(self, storage_specs):
        """Sets the storage_specs of this DescribeNodeAvailableSpecsResponse.


        :param storage_specs: The storage_specs of this DescribeNodeAvailableSpecsResponse.  # noqa: E501
        :type: list[StorageSpecForDescribeNodeAvailableSpecsOutput]
        """

        self._storage_specs = storage_specs

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNodeAvailableSpecsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNodeAvailableSpecsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNodeAvailableSpecsResponse):
            return True

        return self.to_dict() != other.to_dict()
