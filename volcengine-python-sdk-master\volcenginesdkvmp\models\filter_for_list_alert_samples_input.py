# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListAlertSamplesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alert_id': 'str',
        'sample_since': 'int',
        'sample_until': 'int'
    }

    attribute_map = {
        'alert_id': 'AlertId',
        'sample_since': 'SampleSince',
        'sample_until': 'SampleUntil'
    }

    def __init__(self, alert_id=None, sample_since=None, sample_until=None, _configuration=None):  # noqa: E501
        """FilterForListAlertSamplesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alert_id = None
        self._sample_since = None
        self._sample_until = None
        self.discriminator = None

        if alert_id is not None:
            self.alert_id = alert_id
        if sample_since is not None:
            self.sample_since = sample_since
        if sample_until is not None:
            self.sample_until = sample_until

    @property
    def alert_id(self):
        """Gets the alert_id of this FilterForListAlertSamplesInput.  # noqa: E501


        :return: The alert_id of this FilterForListAlertSamplesInput.  # noqa: E501
        :rtype: str
        """
        return self._alert_id

    @alert_id.setter
    def alert_id(self, alert_id):
        """Sets the alert_id of this FilterForListAlertSamplesInput.


        :param alert_id: The alert_id of this FilterForListAlertSamplesInput.  # noqa: E501
        :type: str
        """

        self._alert_id = alert_id

    @property
    def sample_since(self):
        """Gets the sample_since of this FilterForListAlertSamplesInput.  # noqa: E501


        :return: The sample_since of this FilterForListAlertSamplesInput.  # noqa: E501
        :rtype: int
        """
        return self._sample_since

    @sample_since.setter
    def sample_since(self, sample_since):
        """Sets the sample_since of this FilterForListAlertSamplesInput.


        :param sample_since: The sample_since of this FilterForListAlertSamplesInput.  # noqa: E501
        :type: int
        """

        self._sample_since = sample_since

    @property
    def sample_until(self):
        """Gets the sample_until of this FilterForListAlertSamplesInput.  # noqa: E501


        :return: The sample_until of this FilterForListAlertSamplesInput.  # noqa: E501
        :rtype: int
        """
        return self._sample_until

    @sample_until.setter
    def sample_until(self, sample_until):
        """Sets the sample_until of this FilterForListAlertSamplesInput.


        :param sample_until: The sample_until of this FilterForListAlertSamplesInput.  # noqa: E501
        :type: int
        """

        self._sample_until = sample_until

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListAlertSamplesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListAlertSamplesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListAlertSamplesInput):
            return True

        return self.to_dict() != other.to_dict()
