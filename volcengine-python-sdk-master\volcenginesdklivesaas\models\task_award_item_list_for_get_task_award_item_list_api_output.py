# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskAwardItemListForGetTaskAwardItemListAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'award_count': 'int',
        'award_end_time': 'int',
        'award_item_amounts': 'int',
        'award_item_name': 'str',
        'award_item_num': 'str',
        'award_item_type': 'int',
        'award_lottery_ticket_addr': 'str',
        'award_start_time': 'int',
        'people_count': 'int',
        'task_award_icon': 'str',
        'task_award_item_id': 'str'
    }

    attribute_map = {
        'award_count': 'AwardCount',
        'award_end_time': 'AwardEndTime',
        'award_item_amounts': 'AwardItemAmounts',
        'award_item_name': 'AwardItemName',
        'award_item_num': 'AwardItemNum',
        'award_item_type': 'AwardItemType',
        'award_lottery_ticket_addr': 'AwardLotteryTicketAddr',
        'award_start_time': 'AwardStartTime',
        'people_count': 'PeopleCount',
        'task_award_icon': 'TaskAwardIcon',
        'task_award_item_id': 'TaskAwardItemId'
    }

    def __init__(self, award_count=None, award_end_time=None, award_item_amounts=None, award_item_name=None, award_item_num=None, award_item_type=None, award_lottery_ticket_addr=None, award_start_time=None, people_count=None, task_award_icon=None, task_award_item_id=None, _configuration=None):  # noqa: E501
        """TaskAwardItemListForGetTaskAwardItemListAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._award_count = None
        self._award_end_time = None
        self._award_item_amounts = None
        self._award_item_name = None
        self._award_item_num = None
        self._award_item_type = None
        self._award_lottery_ticket_addr = None
        self._award_start_time = None
        self._people_count = None
        self._task_award_icon = None
        self._task_award_item_id = None
        self.discriminator = None

        if award_count is not None:
            self.award_count = award_count
        if award_end_time is not None:
            self.award_end_time = award_end_time
        if award_item_amounts is not None:
            self.award_item_amounts = award_item_amounts
        if award_item_name is not None:
            self.award_item_name = award_item_name
        if award_item_num is not None:
            self.award_item_num = award_item_num
        if award_item_type is not None:
            self.award_item_type = award_item_type
        if award_lottery_ticket_addr is not None:
            self.award_lottery_ticket_addr = award_lottery_ticket_addr
        if award_start_time is not None:
            self.award_start_time = award_start_time
        if people_count is not None:
            self.people_count = people_count
        if task_award_icon is not None:
            self.task_award_icon = task_award_icon
        if task_award_item_id is not None:
            self.task_award_item_id = task_award_item_id

    @property
    def award_count(self):
        """Gets the award_count of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The award_count of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_count

    @award_count.setter
    def award_count(self, award_count):
        """Sets the award_count of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param award_count: The award_count of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_count = award_count

    @property
    def award_end_time(self):
        """Gets the award_end_time of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The award_end_time of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_end_time

    @award_end_time.setter
    def award_end_time(self, award_end_time):
        """Sets the award_end_time of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param award_end_time: The award_end_time of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_end_time = award_end_time

    @property
    def award_item_amounts(self):
        """Gets the award_item_amounts of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The award_item_amounts of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_amounts

    @award_item_amounts.setter
    def award_item_amounts(self, award_item_amounts):
        """Sets the award_item_amounts of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param award_item_amounts: The award_item_amounts of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_item_amounts = award_item_amounts

    @property
    def award_item_name(self):
        """Gets the award_item_name of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The award_item_name of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_name

    @award_item_name.setter
    def award_item_name(self, award_item_name):
        """Sets the award_item_name of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param award_item_name: The award_item_name of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: str
        """

        self._award_item_name = award_item_name

    @property
    def award_item_num(self):
        """Gets the award_item_num of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The award_item_num of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_item_num

    @award_item_num.setter
    def award_item_num(self, award_item_num):
        """Sets the award_item_num of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param award_item_num: The award_item_num of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: str
        """

        self._award_item_num = award_item_num

    @property
    def award_item_type(self):
        """Gets the award_item_type of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The award_item_type of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_item_type

    @award_item_type.setter
    def award_item_type(self, award_item_type):
        """Sets the award_item_type of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param award_item_type: The award_item_type of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_item_type = award_item_type

    @property
    def award_lottery_ticket_addr(self):
        """Gets the award_lottery_ticket_addr of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The award_lottery_ticket_addr of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_lottery_ticket_addr

    @award_lottery_ticket_addr.setter
    def award_lottery_ticket_addr(self, award_lottery_ticket_addr):
        """Sets the award_lottery_ticket_addr of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param award_lottery_ticket_addr: The award_lottery_ticket_addr of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: str
        """

        self._award_lottery_ticket_addr = award_lottery_ticket_addr

    @property
    def award_start_time(self):
        """Gets the award_start_time of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The award_start_time of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_start_time

    @award_start_time.setter
    def award_start_time(self, award_start_time):
        """Sets the award_start_time of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param award_start_time: The award_start_time of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: int
        """

        self._award_start_time = award_start_time

    @property
    def people_count(self):
        """Gets the people_count of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The people_count of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._people_count

    @people_count.setter
    def people_count(self, people_count):
        """Sets the people_count of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param people_count: The people_count of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: int
        """

        self._people_count = people_count

    @property
    def task_award_icon(self):
        """Gets the task_award_icon of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The task_award_icon of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_award_icon

    @task_award_icon.setter
    def task_award_icon(self, task_award_icon):
        """Sets the task_award_icon of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param task_award_icon: The task_award_icon of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: str
        """

        self._task_award_icon = task_award_icon

    @property
    def task_award_item_id(self):
        """Gets the task_award_item_id of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501


        :return: The task_award_item_id of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_award_item_id

    @task_award_item_id.setter
    def task_award_item_id(self, task_award_item_id):
        """Sets the task_award_item_id of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.


        :param task_award_item_id: The task_award_item_id of this TaskAwardItemListForGetTaskAwardItemListAPIOutput.  # noqa: E501
        :type: str
        """

        self._task_award_item_id = task_award_item_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskAwardItemListForGetTaskAwardItemListAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskAwardItemListForGetTaskAwardItemListAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskAwardItemListForGetTaskAwardItemListAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
