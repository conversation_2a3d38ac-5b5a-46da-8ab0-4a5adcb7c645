# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VGWRouteConflictForCreateEDXPeerLinkOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'route_conflicts': 'list[RouteConflictForCreateEDXPeerLinkOutput]',
        'vgw_instance_id': 'str',
        'vgw_instance_name': 'str'
    }

    attribute_map = {
        'route_conflicts': 'RouteConflicts',
        'vgw_instance_id': 'VGWInstanceId',
        'vgw_instance_name': 'VGWInstanceName'
    }

    def __init__(self, route_conflicts=None, vgw_instance_id=None, vgw_instance_name=None, _configuration=None):  # noqa: E501
        """VGWRouteConflictForCreateEDXPeerLinkOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._route_conflicts = None
        self._vgw_instance_id = None
        self._vgw_instance_name = None
        self.discriminator = None

        if route_conflicts is not None:
            self.route_conflicts = route_conflicts
        if vgw_instance_id is not None:
            self.vgw_instance_id = vgw_instance_id
        if vgw_instance_name is not None:
            self.vgw_instance_name = vgw_instance_name

    @property
    def route_conflicts(self):
        """Gets the route_conflicts of this VGWRouteConflictForCreateEDXPeerLinkOutput.  # noqa: E501


        :return: The route_conflicts of this VGWRouteConflictForCreateEDXPeerLinkOutput.  # noqa: E501
        :rtype: list[RouteConflictForCreateEDXPeerLinkOutput]
        """
        return self._route_conflicts

    @route_conflicts.setter
    def route_conflicts(self, route_conflicts):
        """Sets the route_conflicts of this VGWRouteConflictForCreateEDXPeerLinkOutput.


        :param route_conflicts: The route_conflicts of this VGWRouteConflictForCreateEDXPeerLinkOutput.  # noqa: E501
        :type: list[RouteConflictForCreateEDXPeerLinkOutput]
        """

        self._route_conflicts = route_conflicts

    @property
    def vgw_instance_id(self):
        """Gets the vgw_instance_id of this VGWRouteConflictForCreateEDXPeerLinkOutput.  # noqa: E501


        :return: The vgw_instance_id of this VGWRouteConflictForCreateEDXPeerLinkOutput.  # noqa: E501
        :rtype: str
        """
        return self._vgw_instance_id

    @vgw_instance_id.setter
    def vgw_instance_id(self, vgw_instance_id):
        """Sets the vgw_instance_id of this VGWRouteConflictForCreateEDXPeerLinkOutput.


        :param vgw_instance_id: The vgw_instance_id of this VGWRouteConflictForCreateEDXPeerLinkOutput.  # noqa: E501
        :type: str
        """

        self._vgw_instance_id = vgw_instance_id

    @property
    def vgw_instance_name(self):
        """Gets the vgw_instance_name of this VGWRouteConflictForCreateEDXPeerLinkOutput.  # noqa: E501


        :return: The vgw_instance_name of this VGWRouteConflictForCreateEDXPeerLinkOutput.  # noqa: E501
        :rtype: str
        """
        return self._vgw_instance_name

    @vgw_instance_name.setter
    def vgw_instance_name(self, vgw_instance_name):
        """Sets the vgw_instance_name of this VGWRouteConflictForCreateEDXPeerLinkOutput.


        :param vgw_instance_name: The vgw_instance_name of this VGWRouteConflictForCreateEDXPeerLinkOutput.  # noqa: E501
        :type: str
        """

        self._vgw_instance_name = vgw_instance_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VGWRouteConflictForCreateEDXPeerLinkOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VGWRouteConflictForCreateEDXPeerLinkOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VGWRouteConflictForCreateEDXPeerLinkOutput):
            return True

        return self.to_dict() != other.to_dict()
