# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class JobForDescribeMigrationJobsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'destination_config': 'DestinationConfigForDescribeMigrationJobsOutput',
        'finished_at': 'str',
        'last_sync_time': 'str',
        'migration_job_description': 'str',
        'migration_job_id': 'str',
        'migration_job_name': 'str',
        'migration_source_id': 'str',
        'migration_source_name': 'str',
        'migration_state_tips': 'str',
        'network_config': 'NetworkConfigForDescribeMigrationJobsOutput',
        'progress': 'int',
        'project_name': 'str',
        'source_project_name': 'str',
        'source_type': 'str',
        'started_at': 'str',
        'state': 'str',
        'sync_config': 'SyncConfigForDescribeMigrationJobsOutput',
        'system_config': 'SystemConfigForDescribeMigrationJobsOutput',
        'tags': 'list[TagForDescribeMigrationJobsOutput]',
        'temporary_resources': 'list[TemporaryResourceForDescribeMigrationJobsOutput]'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'destination_config': 'DestinationConfig',
        'finished_at': 'FinishedAt',
        'last_sync_time': 'LastSyncTime',
        'migration_job_description': 'MigrationJobDescription',
        'migration_job_id': 'MigrationJobId',
        'migration_job_name': 'MigrationJobName',
        'migration_source_id': 'MigrationSourceId',
        'migration_source_name': 'MigrationSourceName',
        'migration_state_tips': 'MigrationStateTips',
        'network_config': 'NetworkConfig',
        'progress': 'Progress',
        'project_name': 'ProjectName',
        'source_project_name': 'SourceProjectName',
        'source_type': 'SourceType',
        'started_at': 'StartedAt',
        'state': 'State',
        'sync_config': 'SyncConfig',
        'system_config': 'SystemConfig',
        'tags': 'Tags',
        'temporary_resources': 'TemporaryResources'
    }

    def __init__(self, created_at=None, destination_config=None, finished_at=None, last_sync_time=None, migration_job_description=None, migration_job_id=None, migration_job_name=None, migration_source_id=None, migration_source_name=None, migration_state_tips=None, network_config=None, progress=None, project_name=None, source_project_name=None, source_type=None, started_at=None, state=None, sync_config=None, system_config=None, tags=None, temporary_resources=None, _configuration=None):  # noqa: E501
        """JobForDescribeMigrationJobsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._destination_config = None
        self._finished_at = None
        self._last_sync_time = None
        self._migration_job_description = None
        self._migration_job_id = None
        self._migration_job_name = None
        self._migration_source_id = None
        self._migration_source_name = None
        self._migration_state_tips = None
        self._network_config = None
        self._progress = None
        self._project_name = None
        self._source_project_name = None
        self._source_type = None
        self._started_at = None
        self._state = None
        self._sync_config = None
        self._system_config = None
        self._tags = None
        self._temporary_resources = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if destination_config is not None:
            self.destination_config = destination_config
        if finished_at is not None:
            self.finished_at = finished_at
        if last_sync_time is not None:
            self.last_sync_time = last_sync_time
        if migration_job_description is not None:
            self.migration_job_description = migration_job_description
        if migration_job_id is not None:
            self.migration_job_id = migration_job_id
        if migration_job_name is not None:
            self.migration_job_name = migration_job_name
        if migration_source_id is not None:
            self.migration_source_id = migration_source_id
        if migration_source_name is not None:
            self.migration_source_name = migration_source_name
        if migration_state_tips is not None:
            self.migration_state_tips = migration_state_tips
        if network_config is not None:
            self.network_config = network_config
        if progress is not None:
            self.progress = progress
        if project_name is not None:
            self.project_name = project_name
        if source_project_name is not None:
            self.source_project_name = source_project_name
        if source_type is not None:
            self.source_type = source_type
        if started_at is not None:
            self.started_at = started_at
        if state is not None:
            self.state = state
        if sync_config is not None:
            self.sync_config = sync_config
        if system_config is not None:
            self.system_config = system_config
        if tags is not None:
            self.tags = tags
        if temporary_resources is not None:
            self.temporary_resources = temporary_resources

    @property
    def created_at(self):
        """Gets the created_at of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The created_at of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this JobForDescribeMigrationJobsOutput.


        :param created_at: The created_at of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def destination_config(self):
        """Gets the destination_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The destination_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: DestinationConfigForDescribeMigrationJobsOutput
        """
        return self._destination_config

    @destination_config.setter
    def destination_config(self, destination_config):
        """Sets the destination_config of this JobForDescribeMigrationJobsOutput.


        :param destination_config: The destination_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: DestinationConfigForDescribeMigrationJobsOutput
        """

        self._destination_config = destination_config

    @property
    def finished_at(self):
        """Gets the finished_at of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The finished_at of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._finished_at

    @finished_at.setter
    def finished_at(self, finished_at):
        """Sets the finished_at of this JobForDescribeMigrationJobsOutput.


        :param finished_at: The finished_at of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._finished_at = finished_at

    @property
    def last_sync_time(self):
        """Gets the last_sync_time of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The last_sync_time of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._last_sync_time

    @last_sync_time.setter
    def last_sync_time(self, last_sync_time):
        """Sets the last_sync_time of this JobForDescribeMigrationJobsOutput.


        :param last_sync_time: The last_sync_time of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._last_sync_time = last_sync_time

    @property
    def migration_job_description(self):
        """Gets the migration_job_description of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The migration_job_description of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_description

    @migration_job_description.setter
    def migration_job_description(self, migration_job_description):
        """Sets the migration_job_description of this JobForDescribeMigrationJobsOutput.


        :param migration_job_description: The migration_job_description of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._migration_job_description = migration_job_description

    @property
    def migration_job_id(self):
        """Gets the migration_job_id of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The migration_job_id of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_id

    @migration_job_id.setter
    def migration_job_id(self, migration_job_id):
        """Sets the migration_job_id of this JobForDescribeMigrationJobsOutput.


        :param migration_job_id: The migration_job_id of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._migration_job_id = migration_job_id

    @property
    def migration_job_name(self):
        """Gets the migration_job_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The migration_job_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._migration_job_name

    @migration_job_name.setter
    def migration_job_name(self, migration_job_name):
        """Sets the migration_job_name of this JobForDescribeMigrationJobsOutput.


        :param migration_job_name: The migration_job_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._migration_job_name = migration_job_name

    @property
    def migration_source_id(self):
        """Gets the migration_source_id of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The migration_source_id of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._migration_source_id

    @migration_source_id.setter
    def migration_source_id(self, migration_source_id):
        """Sets the migration_source_id of this JobForDescribeMigrationJobsOutput.


        :param migration_source_id: The migration_source_id of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._migration_source_id = migration_source_id

    @property
    def migration_source_name(self):
        """Gets the migration_source_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The migration_source_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._migration_source_name

    @migration_source_name.setter
    def migration_source_name(self, migration_source_name):
        """Sets the migration_source_name of this JobForDescribeMigrationJobsOutput.


        :param migration_source_name: The migration_source_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._migration_source_name = migration_source_name

    @property
    def migration_state_tips(self):
        """Gets the migration_state_tips of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The migration_state_tips of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._migration_state_tips

    @migration_state_tips.setter
    def migration_state_tips(self, migration_state_tips):
        """Sets the migration_state_tips of this JobForDescribeMigrationJobsOutput.


        :param migration_state_tips: The migration_state_tips of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._migration_state_tips = migration_state_tips

    @property
    def network_config(self):
        """Gets the network_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The network_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: NetworkConfigForDescribeMigrationJobsOutput
        """
        return self._network_config

    @network_config.setter
    def network_config(self, network_config):
        """Sets the network_config of this JobForDescribeMigrationJobsOutput.


        :param network_config: The network_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: NetworkConfigForDescribeMigrationJobsOutput
        """

        self._network_config = network_config

    @property
    def progress(self):
        """Gets the progress of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The progress of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: int
        """
        return self._progress

    @progress.setter
    def progress(self, progress):
        """Sets the progress of this JobForDescribeMigrationJobsOutput.


        :param progress: The progress of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: int
        """

        self._progress = progress

    @property
    def project_name(self):
        """Gets the project_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The project_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this JobForDescribeMigrationJobsOutput.


        :param project_name: The project_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def source_project_name(self):
        """Gets the source_project_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The source_project_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_project_name

    @source_project_name.setter
    def source_project_name(self, source_project_name):
        """Sets the source_project_name of this JobForDescribeMigrationJobsOutput.


        :param source_project_name: The source_project_name of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._source_project_name = source_project_name

    @property
    def source_type(self):
        """Gets the source_type of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The source_type of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this JobForDescribeMigrationJobsOutput.


        :param source_type: The source_type of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._source_type = source_type

    @property
    def started_at(self):
        """Gets the started_at of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The started_at of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._started_at

    @started_at.setter
    def started_at(self, started_at):
        """Sets the started_at of this JobForDescribeMigrationJobsOutput.


        :param started_at: The started_at of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._started_at = started_at

    @property
    def state(self):
        """Gets the state of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The state of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this JobForDescribeMigrationJobsOutput.


        :param state: The state of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def sync_config(self):
        """Gets the sync_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The sync_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: SyncConfigForDescribeMigrationJobsOutput
        """
        return self._sync_config

    @sync_config.setter
    def sync_config(self, sync_config):
        """Sets the sync_config of this JobForDescribeMigrationJobsOutput.


        :param sync_config: The sync_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: SyncConfigForDescribeMigrationJobsOutput
        """

        self._sync_config = sync_config

    @property
    def system_config(self):
        """Gets the system_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The system_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: SystemConfigForDescribeMigrationJobsOutput
        """
        return self._system_config

    @system_config.setter
    def system_config(self, system_config):
        """Sets the system_config of this JobForDescribeMigrationJobsOutput.


        :param system_config: The system_config of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: SystemConfigForDescribeMigrationJobsOutput
        """

        self._system_config = system_config

    @property
    def tags(self):
        """Gets the tags of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The tags of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: list[TagForDescribeMigrationJobsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this JobForDescribeMigrationJobsOutput.


        :param tags: The tags of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: list[TagForDescribeMigrationJobsOutput]
        """

        self._tags = tags

    @property
    def temporary_resources(self):
        """Gets the temporary_resources of this JobForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The temporary_resources of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: list[TemporaryResourceForDescribeMigrationJobsOutput]
        """
        return self._temporary_resources

    @temporary_resources.setter
    def temporary_resources(self, temporary_resources):
        """Sets the temporary_resources of this JobForDescribeMigrationJobsOutput.


        :param temporary_resources: The temporary_resources of this JobForDescribeMigrationJobsOutput.  # noqa: E501
        :type: list[TemporaryResourceForDescribeMigrationJobsOutput]
        """

        self._temporary_resources = temporary_resources

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(JobForDescribeMigrationJobsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, JobForDescribeMigrationJobsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, JobForDescribeMigrationJobsOutput):
            return True

        return self.to_dict() != other.to_dict()
