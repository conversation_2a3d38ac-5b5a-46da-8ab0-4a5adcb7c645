# coding: utf-8

"""
    cloud_detect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TCPConfigForCreateTaskInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'payload': 'str',
        'payload_type': 'int',
        'timeout': 'int'
    }

    attribute_map = {
        'payload': 'Payload',
        'payload_type': 'PayloadType',
        'timeout': 'Timeout'
    }

    def __init__(self, payload=None, payload_type=None, timeout=None, _configuration=None):  # noqa: E501
        """TCPConfigForCreateTaskInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._payload = None
        self._payload_type = None
        self._timeout = None
        self.discriminator = None

        if payload is not None:
            self.payload = payload
        if payload_type is not None:
            self.payload_type = payload_type
        if timeout is not None:
            self.timeout = timeout

    @property
    def payload(self):
        """Gets the payload of this TCPConfigForCreateTaskInput.  # noqa: E501


        :return: The payload of this TCPConfigForCreateTaskInput.  # noqa: E501
        :rtype: str
        """
        return self._payload

    @payload.setter
    def payload(self, payload):
        """Sets the payload of this TCPConfigForCreateTaskInput.


        :param payload: The payload of this TCPConfigForCreateTaskInput.  # noqa: E501
        :type: str
        """

        self._payload = payload

    @property
    def payload_type(self):
        """Gets the payload_type of this TCPConfigForCreateTaskInput.  # noqa: E501


        :return: The payload_type of this TCPConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._payload_type

    @payload_type.setter
    def payload_type(self, payload_type):
        """Sets the payload_type of this TCPConfigForCreateTaskInput.


        :param payload_type: The payload_type of this TCPConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._payload_type = payload_type

    @property
    def timeout(self):
        """Gets the timeout of this TCPConfigForCreateTaskInput.  # noqa: E501


        :return: The timeout of this TCPConfigForCreateTaskInput.  # noqa: E501
        :rtype: int
        """
        return self._timeout

    @timeout.setter
    def timeout(self, timeout):
        """Sets the timeout of this TCPConfigForCreateTaskInput.


        :param timeout: The timeout of this TCPConfigForCreateTaskInput.  # noqa: E501
        :type: int
        """

        self._timeout = timeout

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TCPConfigForCreateTaskInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TCPConfigForCreateTaskInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TCPConfigForCreateTaskInput):
            return True

        return self.to_dict() != other.to_dict()
