# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyDBInstanceAdditionalBandwidthPerShardRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'additional_bandwidth': 'int',
        'client_token': 'str',
        'instance_id': 'str'
    }

    attribute_map = {
        'additional_bandwidth': 'AdditionalBandwidth',
        'client_token': 'ClientToken',
        'instance_id': 'InstanceId'
    }

    def __init__(self, additional_bandwidth=None, client_token=None, instance_id=None, _configuration=None):  # noqa: E501
        """ModifyDBInstanceAdditionalBandwidthPerShardRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._additional_bandwidth = None
        self._client_token = None
        self._instance_id = None
        self.discriminator = None

        self.additional_bandwidth = additional_bandwidth
        if client_token is not None:
            self.client_token = client_token
        self.instance_id = instance_id

    @property
    def additional_bandwidth(self):
        """Gets the additional_bandwidth of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.  # noqa: E501


        :return: The additional_bandwidth of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.  # noqa: E501
        :rtype: int
        """
        return self._additional_bandwidth

    @additional_bandwidth.setter
    def additional_bandwidth(self, additional_bandwidth):
        """Sets the additional_bandwidth of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.


        :param additional_bandwidth: The additional_bandwidth of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and additional_bandwidth is None:
            raise ValueError("Invalid value for `additional_bandwidth`, must not be `None`")  # noqa: E501

        self._additional_bandwidth = additional_bandwidth

    @property
    def client_token(self):
        """Gets the client_token of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.  # noqa: E501


        :return: The client_token of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.


        :param client_token: The client_token of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.  # noqa: E501


        :return: The instance_id of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.


        :param instance_id: The instance_id of this ModifyDBInstanceAdditionalBandwidthPerShardRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyDBInstanceAdditionalBandwidthPerShardRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyDBInstanceAdditionalBandwidthPerShardRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyDBInstanceAdditionalBandwidthPerShardRequest):
            return True

        return self.to_dict() != other.to_dict()
