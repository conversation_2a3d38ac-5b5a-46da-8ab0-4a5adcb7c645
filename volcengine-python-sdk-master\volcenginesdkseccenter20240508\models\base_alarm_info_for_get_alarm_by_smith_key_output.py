# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BaseAlarmInfoForGetAlarmBySmithKeyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_type': 'str',
        'attack_id_list': 'list[str]',
        'create_time': 'int',
        'desc': 'str',
        'docker': 'str',
        'handle_time': 'int',
        'handle_user': 'str',
        'level': 'str',
        'name': 'str',
        'status': 'int',
        'suggest': 'str',
        'trace_id': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'alarm_type': 'AlarmType',
        'attack_id_list': 'AttackIDList',
        'create_time': 'CreateTime',
        'desc': 'Desc',
        'docker': 'Docker',
        'handle_time': 'HandleTime',
        'handle_user': 'HandleUser',
        'level': 'Level',
        'name': 'Name',
        'status': 'Status',
        'suggest': 'Suggest',
        'trace_id': 'TraceID',
        'update_time': 'UpdateTime'
    }

    def __init__(self, alarm_type=None, attack_id_list=None, create_time=None, desc=None, docker=None, handle_time=None, handle_user=None, level=None, name=None, status=None, suggest=None, trace_id=None, update_time=None, _configuration=None):  # noqa: E501
        """BaseAlarmInfoForGetAlarmBySmithKeyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_type = None
        self._attack_id_list = None
        self._create_time = None
        self._desc = None
        self._docker = None
        self._handle_time = None
        self._handle_user = None
        self._level = None
        self._name = None
        self._status = None
        self._suggest = None
        self._trace_id = None
        self._update_time = None
        self.discriminator = None

        if alarm_type is not None:
            self.alarm_type = alarm_type
        if attack_id_list is not None:
            self.attack_id_list = attack_id_list
        if create_time is not None:
            self.create_time = create_time
        if desc is not None:
            self.desc = desc
        if docker is not None:
            self.docker = docker
        if handle_time is not None:
            self.handle_time = handle_time
        if handle_user is not None:
            self.handle_user = handle_user
        if level is not None:
            self.level = level
        if name is not None:
            self.name = name
        if status is not None:
            self.status = status
        if suggest is not None:
            self.suggest = suggest
        if trace_id is not None:
            self.trace_id = trace_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def alarm_type(self):
        """Gets the alarm_type of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The alarm_type of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_type

    @alarm_type.setter
    def alarm_type(self, alarm_type):
        """Sets the alarm_type of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param alarm_type: The alarm_type of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._alarm_type = alarm_type

    @property
    def attack_id_list(self):
        """Gets the attack_id_list of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The attack_id_list of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._attack_id_list

    @attack_id_list.setter
    def attack_id_list(self, attack_id_list):
        """Sets the attack_id_list of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param attack_id_list: The attack_id_list of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: list[str]
        """

        self._attack_id_list = attack_id_list

    @property
    def create_time(self):
        """Gets the create_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The create_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param create_time: The create_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def desc(self):
        """Gets the desc of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The desc of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._desc

    @desc.setter
    def desc(self, desc):
        """Sets the desc of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param desc: The desc of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._desc = desc

    @property
    def docker(self):
        """Gets the docker of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The docker of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._docker

    @docker.setter
    def docker(self, docker):
        """Sets the docker of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param docker: The docker of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._docker = docker

    @property
    def handle_time(self):
        """Gets the handle_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The handle_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: int
        """
        return self._handle_time

    @handle_time.setter
    def handle_time(self, handle_time):
        """Sets the handle_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param handle_time: The handle_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: int
        """

        self._handle_time = handle_time

    @property
    def handle_user(self):
        """Gets the handle_user of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The handle_user of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._handle_user

    @handle_user.setter
    def handle_user(self, handle_user):
        """Sets the handle_user of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param handle_user: The handle_user of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._handle_user = handle_user

    @property
    def level(self):
        """Gets the level of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The level of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param level: The level of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def name(self):
        """Gets the name of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The name of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param name: The name of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def status(self):
        """Gets the status of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The status of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param status: The status of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def suggest(self):
        """Gets the suggest of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The suggest of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._suggest

    @suggest.setter
    def suggest(self, suggest):
        """Sets the suggest of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param suggest: The suggest of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._suggest = suggest

    @property
    def trace_id(self):
        """Gets the trace_id of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The trace_id of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: str
        """
        return self._trace_id

    @trace_id.setter
    def trace_id(self, trace_id):
        """Sets the trace_id of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param trace_id: The trace_id of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: str
        """

        self._trace_id = trace_id

    @property
    def update_time(self):
        """Gets the update_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501


        :return: The update_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.


        :param update_time: The update_time of this BaseAlarmInfoForGetAlarmBySmithKeyOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BaseAlarmInfoForGetAlarmBySmithKeyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BaseAlarmInfoForGetAlarmBySmithKeyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BaseAlarmInfoForGetAlarmBySmithKeyOutput):
            return True

        return self.to_dict() != other.to_dict()
