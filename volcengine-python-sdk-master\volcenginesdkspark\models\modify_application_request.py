# coding: utf-8

"""
    spark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyApplicationRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_trn': 'str',
        'args': 'str',
        'conf': 'dict(str, str)',
        'dependency': 'DependencyFormodifyApplicationInput',
        'deploy_request': 'DeployRequestFormodifyApplicationInput',
        'engine_version': 'str',
        'image': 'str',
        'jar': 'str',
        'job_name': 'str',
        'main_class': 'str',
        'sql_text': 'str',
        'unique_key': 'str'
    }

    attribute_map = {
        'application_trn': 'ApplicationTrn',
        'args': 'Args',
        'conf': 'Conf',
        'dependency': 'Dependency',
        'deploy_request': 'DeployRequest',
        'engine_version': 'EngineVersion',
        'image': 'Image',
        'jar': 'Jar',
        'job_name': 'JobName',
        'main_class': 'MainClass',
        'sql_text': 'SqlText',
        'unique_key': 'UniqueKey'
    }

    def __init__(self, application_trn=None, args=None, conf=None, dependency=None, deploy_request=None, engine_version=None, image=None, jar=None, job_name=None, main_class=None, sql_text=None, unique_key=None, _configuration=None):  # noqa: E501
        """ModifyApplicationRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_trn = None
        self._args = None
        self._conf = None
        self._dependency = None
        self._deploy_request = None
        self._engine_version = None
        self._image = None
        self._jar = None
        self._job_name = None
        self._main_class = None
        self._sql_text = None
        self._unique_key = None
        self.discriminator = None

        if application_trn is not None:
            self.application_trn = application_trn
        if args is not None:
            self.args = args
        if conf is not None:
            self.conf = conf
        if dependency is not None:
            self.dependency = dependency
        if deploy_request is not None:
            self.deploy_request = deploy_request
        if engine_version is not None:
            self.engine_version = engine_version
        if image is not None:
            self.image = image
        if jar is not None:
            self.jar = jar
        if job_name is not None:
            self.job_name = job_name
        if main_class is not None:
            self.main_class = main_class
        if sql_text is not None:
            self.sql_text = sql_text
        if unique_key is not None:
            self.unique_key = unique_key

    @property
    def application_trn(self):
        """Gets the application_trn of this ModifyApplicationRequest.  # noqa: E501


        :return: The application_trn of this ModifyApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._application_trn

    @application_trn.setter
    def application_trn(self, application_trn):
        """Sets the application_trn of this ModifyApplicationRequest.


        :param application_trn: The application_trn of this ModifyApplicationRequest.  # noqa: E501
        :type: str
        """

        self._application_trn = application_trn

    @property
    def args(self):
        """Gets the args of this ModifyApplicationRequest.  # noqa: E501


        :return: The args of this ModifyApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._args

    @args.setter
    def args(self, args):
        """Sets the args of this ModifyApplicationRequest.


        :param args: The args of this ModifyApplicationRequest.  # noqa: E501
        :type: str
        """

        self._args = args

    @property
    def conf(self):
        """Gets the conf of this ModifyApplicationRequest.  # noqa: E501


        :return: The conf of this ModifyApplicationRequest.  # noqa: E501
        :rtype: dict(str, str)
        """
        return self._conf

    @conf.setter
    def conf(self, conf):
        """Sets the conf of this ModifyApplicationRequest.


        :param conf: The conf of this ModifyApplicationRequest.  # noqa: E501
        :type: dict(str, str)
        """

        self._conf = conf

    @property
    def dependency(self):
        """Gets the dependency of this ModifyApplicationRequest.  # noqa: E501


        :return: The dependency of this ModifyApplicationRequest.  # noqa: E501
        :rtype: DependencyFormodifyApplicationInput
        """
        return self._dependency

    @dependency.setter
    def dependency(self, dependency):
        """Sets the dependency of this ModifyApplicationRequest.


        :param dependency: The dependency of this ModifyApplicationRequest.  # noqa: E501
        :type: DependencyFormodifyApplicationInput
        """

        self._dependency = dependency

    @property
    def deploy_request(self):
        """Gets the deploy_request of this ModifyApplicationRequest.  # noqa: E501


        :return: The deploy_request of this ModifyApplicationRequest.  # noqa: E501
        :rtype: DeployRequestFormodifyApplicationInput
        """
        return self._deploy_request

    @deploy_request.setter
    def deploy_request(self, deploy_request):
        """Sets the deploy_request of this ModifyApplicationRequest.


        :param deploy_request: The deploy_request of this ModifyApplicationRequest.  # noqa: E501
        :type: DeployRequestFormodifyApplicationInput
        """

        self._deploy_request = deploy_request

    @property
    def engine_version(self):
        """Gets the engine_version of this ModifyApplicationRequest.  # noqa: E501


        :return: The engine_version of this ModifyApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._engine_version

    @engine_version.setter
    def engine_version(self, engine_version):
        """Sets the engine_version of this ModifyApplicationRequest.


        :param engine_version: The engine_version of this ModifyApplicationRequest.  # noqa: E501
        :type: str
        """

        self._engine_version = engine_version

    @property
    def image(self):
        """Gets the image of this ModifyApplicationRequest.  # noqa: E501


        :return: The image of this ModifyApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._image

    @image.setter
    def image(self, image):
        """Sets the image of this ModifyApplicationRequest.


        :param image: The image of this ModifyApplicationRequest.  # noqa: E501
        :type: str
        """

        self._image = image

    @property
    def jar(self):
        """Gets the jar of this ModifyApplicationRequest.  # noqa: E501


        :return: The jar of this ModifyApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._jar

    @jar.setter
    def jar(self, jar):
        """Sets the jar of this ModifyApplicationRequest.


        :param jar: The jar of this ModifyApplicationRequest.  # noqa: E501
        :type: str
        """

        self._jar = jar

    @property
    def job_name(self):
        """Gets the job_name of this ModifyApplicationRequest.  # noqa: E501


        :return: The job_name of this ModifyApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._job_name

    @job_name.setter
    def job_name(self, job_name):
        """Sets the job_name of this ModifyApplicationRequest.


        :param job_name: The job_name of this ModifyApplicationRequest.  # noqa: E501
        :type: str
        """

        self._job_name = job_name

    @property
    def main_class(self):
        """Gets the main_class of this ModifyApplicationRequest.  # noqa: E501


        :return: The main_class of this ModifyApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._main_class

    @main_class.setter
    def main_class(self, main_class):
        """Sets the main_class of this ModifyApplicationRequest.


        :param main_class: The main_class of this ModifyApplicationRequest.  # noqa: E501
        :type: str
        """

        self._main_class = main_class

    @property
    def sql_text(self):
        """Gets the sql_text of this ModifyApplicationRequest.  # noqa: E501


        :return: The sql_text of this ModifyApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._sql_text

    @sql_text.setter
    def sql_text(self, sql_text):
        """Sets the sql_text of this ModifyApplicationRequest.


        :param sql_text: The sql_text of this ModifyApplicationRequest.  # noqa: E501
        :type: str
        """

        self._sql_text = sql_text

    @property
    def unique_key(self):
        """Gets the unique_key of this ModifyApplicationRequest.  # noqa: E501


        :return: The unique_key of this ModifyApplicationRequest.  # noqa: E501
        :rtype: str
        """
        return self._unique_key

    @unique_key.setter
    def unique_key(self, unique_key):
        """Sets the unique_key of this ModifyApplicationRequest.


        :param unique_key: The unique_key of this ModifyApplicationRequest.  # noqa: E501
        :type: str
        """

        self._unique_key = unique_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyApplicationRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyApplicationRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyApplicationRequest):
            return True

        return self.to_dict() != other.to_dict()
