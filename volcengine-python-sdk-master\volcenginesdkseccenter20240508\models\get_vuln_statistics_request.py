# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVulnStatisticsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'asset_id': 'str',
        'asset_type': 'str',
        'if_high_availability': 'bool',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'asset_id': 'AssetID',
        'asset_type': 'AssetType',
        'if_high_availability': 'IfHighAvailability',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id=None, asset_id=None, asset_type=None, if_high_availability=None, top_group_id=None, _configuration=None):  # noqa: E501
        """GetVulnStatisticsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._asset_id = None
        self._asset_type = None
        self._if_high_availability = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_type is not None:
            self.asset_type = asset_type
        if if_high_availability is not None:
            self.if_high_availability = if_high_availability
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id(self):
        """Gets the agent_id of this GetVulnStatisticsRequest.  # noqa: E501


        :return: The agent_id of this GetVulnStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this GetVulnStatisticsRequest.


        :param agent_id: The agent_id of this GetVulnStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def asset_id(self):
        """Gets the asset_id of this GetVulnStatisticsRequest.  # noqa: E501


        :return: The asset_id of this GetVulnStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this GetVulnStatisticsRequest.


        :param asset_id: The asset_id of this GetVulnStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_type(self):
        """Gets the asset_type of this GetVulnStatisticsRequest.  # noqa: E501


        :return: The asset_type of this GetVulnStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this GetVulnStatisticsRequest.


        :param asset_type: The asset_type of this GetVulnStatisticsRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def if_high_availability(self):
        """Gets the if_high_availability of this GetVulnStatisticsRequest.  # noqa: E501


        :return: The if_high_availability of this GetVulnStatisticsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_high_availability

    @if_high_availability.setter
    def if_high_availability(self, if_high_availability):
        """Sets the if_high_availability of this GetVulnStatisticsRequest.


        :param if_high_availability: The if_high_availability of this GetVulnStatisticsRequest.  # noqa: E501
        :type: bool
        """

        self._if_high_availability = if_high_availability

    @property
    def top_group_id(self):
        """Gets the top_group_id of this GetVulnStatisticsRequest.  # noqa: E501


        :return: The top_group_id of this GetVulnStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this GetVulnStatisticsRequest.


        :param top_group_id: The top_group_id of this GetVulnStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVulnStatisticsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVulnStatisticsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVulnStatisticsRequest):
            return True

        return self.to_dict() != other.to_dict()
