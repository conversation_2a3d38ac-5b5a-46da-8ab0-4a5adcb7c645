# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateNodeGroupAttributeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'cluster_id': 'str',
        'node_group_id': 'str',
        'node_group_name': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'cluster_id': 'ClusterId',
        'node_group_id': 'NodeGroupId',
        'node_group_name': 'NodeGroupName'
    }

    def __init__(self, client_token=None, cluster_id=None, node_group_id=None, node_group_name=None, _configuration=None):  # noqa: E501
        """UpdateNodeGroupAttributeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._cluster_id = None
        self._node_group_id = None
        self._node_group_name = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if cluster_id is not None:
            self.cluster_id = cluster_id
        self.node_group_id = node_group_id
        self.node_group_name = node_group_name

    @property
    def client_token(self):
        """Gets the client_token of this UpdateNodeGroupAttributeRequest.  # noqa: E501


        :return: The client_token of this UpdateNodeGroupAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this UpdateNodeGroupAttributeRequest.


        :param client_token: The client_token of this UpdateNodeGroupAttributeRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def cluster_id(self):
        """Gets the cluster_id of this UpdateNodeGroupAttributeRequest.  # noqa: E501


        :return: The cluster_id of this UpdateNodeGroupAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this UpdateNodeGroupAttributeRequest.


        :param cluster_id: The cluster_id of this UpdateNodeGroupAttributeRequest.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def node_group_id(self):
        """Gets the node_group_id of this UpdateNodeGroupAttributeRequest.  # noqa: E501


        :return: The node_group_id of this UpdateNodeGroupAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_group_id

    @node_group_id.setter
    def node_group_id(self, node_group_id):
        """Sets the node_group_id of this UpdateNodeGroupAttributeRequest.


        :param node_group_id: The node_group_id of this UpdateNodeGroupAttributeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and node_group_id is None:
            raise ValueError("Invalid value for `node_group_id`, must not be `None`")  # noqa: E501

        self._node_group_id = node_group_id

    @property
    def node_group_name(self):
        """Gets the node_group_name of this UpdateNodeGroupAttributeRequest.  # noqa: E501


        :return: The node_group_name of this UpdateNodeGroupAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_group_name

    @node_group_name.setter
    def node_group_name(self, node_group_name):
        """Sets the node_group_name of this UpdateNodeGroupAttributeRequest.


        :param node_group_name: The node_group_name of this UpdateNodeGroupAttributeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and node_group_name is None:
            raise ValueError("Invalid value for `node_group_name`, must not be `None`")  # noqa: E501

        self._node_group_name = node_group_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateNodeGroupAttributeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateNodeGroupAttributeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateNodeGroupAttributeRequest):
            return True

        return self.to_dict() != other.to_dict()
