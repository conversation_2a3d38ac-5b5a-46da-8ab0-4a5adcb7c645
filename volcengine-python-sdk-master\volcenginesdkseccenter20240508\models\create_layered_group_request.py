# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateLayeredGroupRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_ids': 'list[str]',
        'group_name': 'str',
        'parent_group_path': 'str',
        'parent_id': 'str',
        'reconfirm': 'bool'
    }

    attribute_map = {
        'agent_ids': 'AgentIds',
        'group_name': 'GroupName',
        'parent_group_path': 'ParentGroupPath',
        'parent_id': 'ParentID',
        'reconfirm': 'Reconfirm'
    }

    def __init__(self, agent_ids=None, group_name=None, parent_group_path=None, parent_id=None, reconfirm=None, _configuration=None):  # noqa: E501
        """CreateLayeredGroupRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_ids = None
        self._group_name = None
        self._parent_group_path = None
        self._parent_id = None
        self._reconfirm = None
        self.discriminator = None

        if agent_ids is not None:
            self.agent_ids = agent_ids
        if group_name is not None:
            self.group_name = group_name
        if parent_group_path is not None:
            self.parent_group_path = parent_group_path
        if parent_id is not None:
            self.parent_id = parent_id
        if reconfirm is not None:
            self.reconfirm = reconfirm

    @property
    def agent_ids(self):
        """Gets the agent_ids of this CreateLayeredGroupRequest.  # noqa: E501


        :return: The agent_ids of this CreateLayeredGroupRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_ids

    @agent_ids.setter
    def agent_ids(self, agent_ids):
        """Sets the agent_ids of this CreateLayeredGroupRequest.


        :param agent_ids: The agent_ids of this CreateLayeredGroupRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_ids = agent_ids

    @property
    def group_name(self):
        """Gets the group_name of this CreateLayeredGroupRequest.  # noqa: E501


        :return: The group_name of this CreateLayeredGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._group_name

    @group_name.setter
    def group_name(self, group_name):
        """Sets the group_name of this CreateLayeredGroupRequest.


        :param group_name: The group_name of this CreateLayeredGroupRequest.  # noqa: E501
        :type: str
        """

        self._group_name = group_name

    @property
    def parent_group_path(self):
        """Gets the parent_group_path of this CreateLayeredGroupRequest.  # noqa: E501


        :return: The parent_group_path of this CreateLayeredGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._parent_group_path

    @parent_group_path.setter
    def parent_group_path(self, parent_group_path):
        """Sets the parent_group_path of this CreateLayeredGroupRequest.


        :param parent_group_path: The parent_group_path of this CreateLayeredGroupRequest.  # noqa: E501
        :type: str
        """

        self._parent_group_path = parent_group_path

    @property
    def parent_id(self):
        """Gets the parent_id of this CreateLayeredGroupRequest.  # noqa: E501


        :return: The parent_id of this CreateLayeredGroupRequest.  # noqa: E501
        :rtype: str
        """
        return self._parent_id

    @parent_id.setter
    def parent_id(self, parent_id):
        """Sets the parent_id of this CreateLayeredGroupRequest.


        :param parent_id: The parent_id of this CreateLayeredGroupRequest.  # noqa: E501
        :type: str
        """

        self._parent_id = parent_id

    @property
    def reconfirm(self):
        """Gets the reconfirm of this CreateLayeredGroupRequest.  # noqa: E501


        :return: The reconfirm of this CreateLayeredGroupRequest.  # noqa: E501
        :rtype: bool
        """
        return self._reconfirm

    @reconfirm.setter
    def reconfirm(self, reconfirm):
        """Sets the reconfirm of this CreateLayeredGroupRequest.


        :param reconfirm: The reconfirm of this CreateLayeredGroupRequest.  # noqa: E501
        :type: bool
        """

        self._reconfirm = reconfirm

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateLayeredGroupRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateLayeredGroupRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateLayeredGroupRequest):
            return True

        return self.to_dict() != other.to_dict()
