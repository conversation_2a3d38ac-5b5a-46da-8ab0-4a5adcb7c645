# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TeachAssistantConfigForUpdateTeachAssistantConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'label': 'str',
        'logo': 'str'
    }

    attribute_map = {
        'label': 'Label',
        'logo': 'Logo'
    }

    def __init__(self, label=None, logo=None, _configuration=None):  # noqa: E501
        """TeachAssistantConfigForUpdateTeachAssistantConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._label = None
        self._logo = None
        self.discriminator = None

        if label is not None:
            self.label = label
        if logo is not None:
            self.logo = logo

    @property
    def label(self):
        """Gets the label of this TeachAssistantConfigForUpdateTeachAssistantConfigInput.  # noqa: E501


        :return: The label of this TeachAssistantConfigForUpdateTeachAssistantConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._label

    @label.setter
    def label(self, label):
        """Sets the label of this TeachAssistantConfigForUpdateTeachAssistantConfigInput.


        :param label: The label of this TeachAssistantConfigForUpdateTeachAssistantConfigInput.  # noqa: E501
        :type: str
        """

        self._label = label

    @property
    def logo(self):
        """Gets the logo of this TeachAssistantConfigForUpdateTeachAssistantConfigInput.  # noqa: E501


        :return: The logo of this TeachAssistantConfigForUpdateTeachAssistantConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._logo

    @logo.setter
    def logo(self, logo):
        """Sets the logo of this TeachAssistantConfigForUpdateTeachAssistantConfigInput.


        :param logo: The logo of this TeachAssistantConfigForUpdateTeachAssistantConfigInput.  # noqa: E501
        :type: str
        """

        self._logo = logo

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TeachAssistantConfigForUpdateTeachAssistantConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TeachAssistantConfigForUpdateTeachAssistantConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TeachAssistantConfigForUpdateTeachAssistantConfigInput):
            return True

        return self.to_dict() != other.to_dict()
