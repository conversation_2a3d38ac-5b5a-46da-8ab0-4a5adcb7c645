# coding: utf-8

"""
    rabbitmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyInstanceSpecRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'compute_spec': 'str',
        'instance_id': 'str',
        'storage_space': 'int'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'compute_spec': 'ComputeSpec',
        'instance_id': 'InstanceId',
        'storage_space': 'StorageSpace'
    }

    def __init__(self, client_token=None, compute_spec=None, instance_id=None, storage_space=None, _configuration=None):  # noqa: E501
        """ModifyInstanceSpecRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._compute_spec = None
        self._instance_id = None
        self._storage_space = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if compute_spec is not None:
            self.compute_spec = compute_spec
        self.instance_id = instance_id
        if storage_space is not None:
            self.storage_space = storage_space

    @property
    def client_token(self):
        """Gets the client_token of this ModifyInstanceSpecRequest.  # noqa: E501


        :return: The client_token of this ModifyInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this ModifyInstanceSpecRequest.


        :param client_token: The client_token of this ModifyInstanceSpecRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def compute_spec(self):
        """Gets the compute_spec of this ModifyInstanceSpecRequest.  # noqa: E501


        :return: The compute_spec of this ModifyInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._compute_spec

    @compute_spec.setter
    def compute_spec(self, compute_spec):
        """Sets the compute_spec of this ModifyInstanceSpecRequest.


        :param compute_spec: The compute_spec of this ModifyInstanceSpecRequest.  # noqa: E501
        :type: str
        """

        self._compute_spec = compute_spec

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyInstanceSpecRequest.  # noqa: E501


        :return: The instance_id of this ModifyInstanceSpecRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyInstanceSpecRequest.


        :param instance_id: The instance_id of this ModifyInstanceSpecRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def storage_space(self):
        """Gets the storage_space of this ModifyInstanceSpecRequest.  # noqa: E501


        :return: The storage_space of this ModifyInstanceSpecRequest.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this ModifyInstanceSpecRequest.


        :param storage_space: The storage_space of this ModifyInstanceSpecRequest.  # noqa: E501
        :type: int
        """

        self._storage_space = storage_space

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyInstanceSpecRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyInstanceSpecRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyInstanceSpecRequest):
            return True

        return self.to_dict() != other.to_dict()
