# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OtherCertConfigForDescribeCertConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cer_status': 'str',
        'domain': 'str',
        'domain_lock': 'DomainLockForDescribeCertConfigOutput',
        'status': 'str',
        'type': 'str'
    }

    attribute_map = {
        'cer_status': 'CerStatus',
        'domain': 'Domain',
        'domain_lock': 'DomainLock',
        'status': 'Status',
        'type': 'Type'
    }

    def __init__(self, cer_status=None, domain=None, domain_lock=None, status=None, type=None, _configuration=None):  # noqa: E501
        """OtherCertConfigForDescribeCertConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cer_status = None
        self._domain = None
        self._domain_lock = None
        self._status = None
        self._type = None
        self.discriminator = None

        if cer_status is not None:
            self.cer_status = cer_status
        if domain is not None:
            self.domain = domain
        if domain_lock is not None:
            self.domain_lock = domain_lock
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type

    @property
    def cer_status(self):
        """Gets the cer_status of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501


        :return: The cer_status of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._cer_status

    @cer_status.setter
    def cer_status(self, cer_status):
        """Sets the cer_status of this OtherCertConfigForDescribeCertConfigOutput.


        :param cer_status: The cer_status of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :type: str
        """

        self._cer_status = cer_status

    @property
    def domain(self):
        """Gets the domain of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501


        :return: The domain of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this OtherCertConfigForDescribeCertConfigOutput.


        :param domain: The domain of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :type: str
        """

        self._domain = domain

    @property
    def domain_lock(self):
        """Gets the domain_lock of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501


        :return: The domain_lock of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :rtype: DomainLockForDescribeCertConfigOutput
        """
        return self._domain_lock

    @domain_lock.setter
    def domain_lock(self, domain_lock):
        """Sets the domain_lock of this OtherCertConfigForDescribeCertConfigOutput.


        :param domain_lock: The domain_lock of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :type: DomainLockForDescribeCertConfigOutput
        """

        self._domain_lock = domain_lock

    @property
    def status(self):
        """Gets the status of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501


        :return: The status of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this OtherCertConfigForDescribeCertConfigOutput.


        :param status: The status of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501


        :return: The type of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this OtherCertConfigForDescribeCertConfigOutput.


        :param type: The type of this OtherCertConfigForDescribeCertConfigOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OtherCertConfigForDescribeCertConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OtherCertConfigForDescribeCertConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OtherCertConfigForDescribeCertConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
