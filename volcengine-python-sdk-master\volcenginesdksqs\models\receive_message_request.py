# coding: utf-8

"""
    sqs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ReceiveMessageRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'max_number_of_messages': 'int',
        'queue_trn': 'str',
        'wait_time_seconds': 'int'
    }

    attribute_map = {
        'max_number_of_messages': 'MaxNumberOfMessages',
        'queue_trn': 'QueueTrn',
        'wait_time_seconds': 'WaitTimeSeconds'
    }

    def __init__(self, max_number_of_messages=None, queue_trn=None, wait_time_seconds=None, _configuration=None):  # noqa: E501
        """ReceiveMessageRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._max_number_of_messages = None
        self._queue_trn = None
        self._wait_time_seconds = None
        self.discriminator = None

        if max_number_of_messages is not None:
            self.max_number_of_messages = max_number_of_messages
        self.queue_trn = queue_trn
        if wait_time_seconds is not None:
            self.wait_time_seconds = wait_time_seconds

    @property
    def max_number_of_messages(self):
        """Gets the max_number_of_messages of this ReceiveMessageRequest.  # noqa: E501


        :return: The max_number_of_messages of this ReceiveMessageRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_number_of_messages

    @max_number_of_messages.setter
    def max_number_of_messages(self, max_number_of_messages):
        """Sets the max_number_of_messages of this ReceiveMessageRequest.


        :param max_number_of_messages: The max_number_of_messages of this ReceiveMessageRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                max_number_of_messages is not None and max_number_of_messages > 10):  # noqa: E501
            raise ValueError("Invalid value for `max_number_of_messages`, must be a value less than or equal to `10`")  # noqa: E501
        if (self._configuration.client_side_validation and
                max_number_of_messages is not None and max_number_of_messages < 1):  # noqa: E501
            raise ValueError("Invalid value for `max_number_of_messages`, must be a value greater than or equal to `1`")  # noqa: E501

        self._max_number_of_messages = max_number_of_messages

    @property
    def queue_trn(self):
        """Gets the queue_trn of this ReceiveMessageRequest.  # noqa: E501


        :return: The queue_trn of this ReceiveMessageRequest.  # noqa: E501
        :rtype: str
        """
        return self._queue_trn

    @queue_trn.setter
    def queue_trn(self, queue_trn):
        """Sets the queue_trn of this ReceiveMessageRequest.


        :param queue_trn: The queue_trn of this ReceiveMessageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and queue_trn is None:
            raise ValueError("Invalid value for `queue_trn`, must not be `None`")  # noqa: E501

        self._queue_trn = queue_trn

    @property
    def wait_time_seconds(self):
        """Gets the wait_time_seconds of this ReceiveMessageRequest.  # noqa: E501


        :return: The wait_time_seconds of this ReceiveMessageRequest.  # noqa: E501
        :rtype: int
        """
        return self._wait_time_seconds

    @wait_time_seconds.setter
    def wait_time_seconds(self, wait_time_seconds):
        """Sets the wait_time_seconds of this ReceiveMessageRequest.


        :param wait_time_seconds: The wait_time_seconds of this ReceiveMessageRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                wait_time_seconds is not None and wait_time_seconds > 20):  # noqa: E501
            raise ValueError("Invalid value for `wait_time_seconds`, must be a value less than or equal to `20`")  # noqa: E501
        if (self._configuration.client_side_validation and
                wait_time_seconds is not None and wait_time_seconds < 0):  # noqa: E501
            raise ValueError("Invalid value for `wait_time_seconds`, must be a value greater than or equal to `0`")  # noqa: E501

        self._wait_time_seconds = wait_time_seconds

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReceiveMessageRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReceiveMessageRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ReceiveMessageRequest):
            return True

        return self.to_dict() != other.to_dict()
