# coding: utf-8

"""
    aiotvideo20231001

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ControlPlaybackRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cmd': 'int',
        'ntp': 'str',
        'playback_id': 'str',
        'scale': 'float'
    }

    attribute_map = {
        'cmd': 'Cmd',
        'ntp': 'Ntp',
        'playback_id': 'PlaybackID',
        'scale': 'Scale'
    }

    def __init__(self, cmd=None, ntp=None, playback_id=None, scale=None, _configuration=None):  # noqa: E501
        """ControlPlaybackRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cmd = None
        self._ntp = None
        self._playback_id = None
        self._scale = None
        self.discriminator = None

        self.cmd = cmd
        self.ntp = ntp
        self.playback_id = playback_id
        self.scale = scale

    @property
    def cmd(self):
        """Gets the cmd of this ControlPlaybackRequest.  # noqa: E501


        :return: The cmd of this ControlPlaybackRequest.  # noqa: E501
        :rtype: int
        """
        return self._cmd

    @cmd.setter
    def cmd(self, cmd):
        """Sets the cmd of this ControlPlaybackRequest.


        :param cmd: The cmd of this ControlPlaybackRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and cmd is None:
            raise ValueError("Invalid value for `cmd`, must not be `None`")  # noqa: E501

        self._cmd = cmd

    @property
    def ntp(self):
        """Gets the ntp of this ControlPlaybackRequest.  # noqa: E501


        :return: The ntp of this ControlPlaybackRequest.  # noqa: E501
        :rtype: str
        """
        return self._ntp

    @ntp.setter
    def ntp(self, ntp):
        """Sets the ntp of this ControlPlaybackRequest.


        :param ntp: The ntp of this ControlPlaybackRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ntp is None:
            raise ValueError("Invalid value for `ntp`, must not be `None`")  # noqa: E501

        self._ntp = ntp

    @property
    def playback_id(self):
        """Gets the playback_id of this ControlPlaybackRequest.  # noqa: E501


        :return: The playback_id of this ControlPlaybackRequest.  # noqa: E501
        :rtype: str
        """
        return self._playback_id

    @playback_id.setter
    def playback_id(self, playback_id):
        """Sets the playback_id of this ControlPlaybackRequest.


        :param playback_id: The playback_id of this ControlPlaybackRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and playback_id is None:
            raise ValueError("Invalid value for `playback_id`, must not be `None`")  # noqa: E501

        self._playback_id = playback_id

    @property
    def scale(self):
        """Gets the scale of this ControlPlaybackRequest.  # noqa: E501


        :return: The scale of this ControlPlaybackRequest.  # noqa: E501
        :rtype: float
        """
        return self._scale

    @scale.setter
    def scale(self, scale):
        """Sets the scale of this ControlPlaybackRequest.


        :param scale: The scale of this ControlPlaybackRequest.  # noqa: E501
        :type: float
        """
        if self._configuration.client_side_validation and scale is None:
            raise ValueError("Invalid value for `scale`, must not be `None`")  # noqa: E501

        self._scale = scale

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ControlPlaybackRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ControlPlaybackRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ControlPlaybackRequest):
            return True

        return self.to_dict() != other.to_dict()
