# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryMessageByTimestampRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'partition_id': 'int',
        'query_end_timestamp': 'int',
        'query_start_timestamp': 'int',
        'refresh': 'bool',
        'sort_order': 'str',
        'topic_name': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'partition_id': 'PartitionId',
        'query_end_timestamp': 'QueryEndTimestamp',
        'query_start_timestamp': 'QueryStartTimestamp',
        'refresh': 'Refresh',
        'sort_order': 'SortOrder',
        'topic_name': 'TopicName'
    }

    def __init__(self, instance_id=None, page_number=None, page_size=None, partition_id=None, query_end_timestamp=None, query_start_timestamp=None, refresh=None, sort_order=None, topic_name=None, _configuration=None):  # noqa: E501
        """QueryMessageByTimestampRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._page_number = None
        self._page_size = None
        self._partition_id = None
        self._query_end_timestamp = None
        self._query_start_timestamp = None
        self._refresh = None
        self._sort_order = None
        self._topic_name = None
        self.discriminator = None

        self.instance_id = instance_id
        self.page_number = page_number
        self.page_size = page_size
        self.partition_id = partition_id
        self.query_end_timestamp = query_end_timestamp
        self.query_start_timestamp = query_start_timestamp
        if refresh is not None:
            self.refresh = refresh
        if sort_order is not None:
            self.sort_order = sort_order
        self.topic_name = topic_name

    @property
    def instance_id(self):
        """Gets the instance_id of this QueryMessageByTimestampRequest.  # noqa: E501


        :return: The instance_id of this QueryMessageByTimestampRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this QueryMessageByTimestampRequest.


        :param instance_id: The instance_id of this QueryMessageByTimestampRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def page_number(self):
        """Gets the page_number of this QueryMessageByTimestampRequest.  # noqa: E501


        :return: The page_number of this QueryMessageByTimestampRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this QueryMessageByTimestampRequest.


        :param page_number: The page_number of this QueryMessageByTimestampRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this QueryMessageByTimestampRequest.  # noqa: E501


        :return: The page_size of this QueryMessageByTimestampRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this QueryMessageByTimestampRequest.


        :param page_size: The page_size of this QueryMessageByTimestampRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def partition_id(self):
        """Gets the partition_id of this QueryMessageByTimestampRequest.  # noqa: E501


        :return: The partition_id of this QueryMessageByTimestampRequest.  # noqa: E501
        :rtype: int
        """
        return self._partition_id

    @partition_id.setter
    def partition_id(self, partition_id):
        """Sets the partition_id of this QueryMessageByTimestampRequest.


        :param partition_id: The partition_id of this QueryMessageByTimestampRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and partition_id is None:
            raise ValueError("Invalid value for `partition_id`, must not be `None`")  # noqa: E501

        self._partition_id = partition_id

    @property
    def query_end_timestamp(self):
        """Gets the query_end_timestamp of this QueryMessageByTimestampRequest.  # noqa: E501


        :return: The query_end_timestamp of this QueryMessageByTimestampRequest.  # noqa: E501
        :rtype: int
        """
        return self._query_end_timestamp

    @query_end_timestamp.setter
    def query_end_timestamp(self, query_end_timestamp):
        """Sets the query_end_timestamp of this QueryMessageByTimestampRequest.


        :param query_end_timestamp: The query_end_timestamp of this QueryMessageByTimestampRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and query_end_timestamp is None:
            raise ValueError("Invalid value for `query_end_timestamp`, must not be `None`")  # noqa: E501

        self._query_end_timestamp = query_end_timestamp

    @property
    def query_start_timestamp(self):
        """Gets the query_start_timestamp of this QueryMessageByTimestampRequest.  # noqa: E501


        :return: The query_start_timestamp of this QueryMessageByTimestampRequest.  # noqa: E501
        :rtype: int
        """
        return self._query_start_timestamp

    @query_start_timestamp.setter
    def query_start_timestamp(self, query_start_timestamp):
        """Sets the query_start_timestamp of this QueryMessageByTimestampRequest.


        :param query_start_timestamp: The query_start_timestamp of this QueryMessageByTimestampRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and query_start_timestamp is None:
            raise ValueError("Invalid value for `query_start_timestamp`, must not be `None`")  # noqa: E501

        self._query_start_timestamp = query_start_timestamp

    @property
    def refresh(self):
        """Gets the refresh of this QueryMessageByTimestampRequest.  # noqa: E501


        :return: The refresh of this QueryMessageByTimestampRequest.  # noqa: E501
        :rtype: bool
        """
        return self._refresh

    @refresh.setter
    def refresh(self, refresh):
        """Sets the refresh of this QueryMessageByTimestampRequest.


        :param refresh: The refresh of this QueryMessageByTimestampRequest.  # noqa: E501
        :type: bool
        """

        self._refresh = refresh

    @property
    def sort_order(self):
        """Gets the sort_order of this QueryMessageByTimestampRequest.  # noqa: E501


        :return: The sort_order of this QueryMessageByTimestampRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this QueryMessageByTimestampRequest.


        :param sort_order: The sort_order of this QueryMessageByTimestampRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def topic_name(self):
        """Gets the topic_name of this QueryMessageByTimestampRequest.  # noqa: E501


        :return: The topic_name of this QueryMessageByTimestampRequest.  # noqa: E501
        :rtype: str
        """
        return self._topic_name

    @topic_name.setter
    def topic_name(self, topic_name):
        """Sets the topic_name of this QueryMessageByTimestampRequest.


        :param topic_name: The topic_name of this QueryMessageByTimestampRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and topic_name is None:
            raise ValueError("Invalid value for `topic_name`, must not be `None`")  # noqa: E501

        self._topic_name = topic_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryMessageByTimestampRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryMessageByTimestampRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryMessageByTimestampRequest):
            return True

        return self.to_dict() != other.to_dict()
