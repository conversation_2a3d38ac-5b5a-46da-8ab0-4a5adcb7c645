# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListUserBehaviorDataAPIV2Response(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'data': 'list[DataForListUserBehaviorDataAPIV2Output]',
        'scroll_id': 'str',
        'total_item_count': 'int'
    }

    attribute_map = {
        'data': 'Data',
        'scroll_id': 'ScrollId',
        'total_item_count': 'TotalItemCount'
    }

    def __init__(self, data=None, scroll_id=None, total_item_count=None, _configuration=None):  # noqa: E501
        """ListUserBehaviorDataAPIV2Response - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._data = None
        self._scroll_id = None
        self._total_item_count = None
        self.discriminator = None

        if data is not None:
            self.data = data
        if scroll_id is not None:
            self.scroll_id = scroll_id
        if total_item_count is not None:
            self.total_item_count = total_item_count

    @property
    def data(self):
        """Gets the data of this ListUserBehaviorDataAPIV2Response.  # noqa: E501


        :return: The data of this ListUserBehaviorDataAPIV2Response.  # noqa: E501
        :rtype: list[DataForListUserBehaviorDataAPIV2Output]
        """
        return self._data

    @data.setter
    def data(self, data):
        """Sets the data of this ListUserBehaviorDataAPIV2Response.


        :param data: The data of this ListUserBehaviorDataAPIV2Response.  # noqa: E501
        :type: list[DataForListUserBehaviorDataAPIV2Output]
        """

        self._data = data

    @property
    def scroll_id(self):
        """Gets the scroll_id of this ListUserBehaviorDataAPIV2Response.  # noqa: E501


        :return: The scroll_id of this ListUserBehaviorDataAPIV2Response.  # noqa: E501
        :rtype: str
        """
        return self._scroll_id

    @scroll_id.setter
    def scroll_id(self, scroll_id):
        """Sets the scroll_id of this ListUserBehaviorDataAPIV2Response.


        :param scroll_id: The scroll_id of this ListUserBehaviorDataAPIV2Response.  # noqa: E501
        :type: str
        """

        self._scroll_id = scroll_id

    @property
    def total_item_count(self):
        """Gets the total_item_count of this ListUserBehaviorDataAPIV2Response.  # noqa: E501


        :return: The total_item_count of this ListUserBehaviorDataAPIV2Response.  # noqa: E501
        :rtype: int
        """
        return self._total_item_count

    @total_item_count.setter
    def total_item_count(self, total_item_count):
        """Sets the total_item_count of this ListUserBehaviorDataAPIV2Response.


        :param total_item_count: The total_item_count of this ListUserBehaviorDataAPIV2Response.  # noqa: E501
        :type: int
        """

        self._total_item_count = total_item_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListUserBehaviorDataAPIV2Response, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListUserBehaviorDataAPIV2Response):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListUserBehaviorDataAPIV2Response):
            return True

        return self.to_dict() != other.to_dict()
