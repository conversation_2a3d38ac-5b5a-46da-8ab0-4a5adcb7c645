# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListNotifyPoliciesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'channel_notify_template_ids': 'list[str]',
        'contact_group_ids': 'list[str]',
        'ids': 'list[str]',
        'name': 'str'
    }

    attribute_map = {
        'channel_notify_template_ids': 'ChannelNotifyTemplateIds',
        'contact_group_ids': 'ContactGroupIds',
        'ids': 'Ids',
        'name': 'Name'
    }

    def __init__(self, channel_notify_template_ids=None, contact_group_ids=None, ids=None, name=None, _configuration=None):  # noqa: E501
        """FilterForListNotifyPoliciesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._channel_notify_template_ids = None
        self._contact_group_ids = None
        self._ids = None
        self._name = None
        self.discriminator = None

        if channel_notify_template_ids is not None:
            self.channel_notify_template_ids = channel_notify_template_ids
        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        if ids is not None:
            self.ids = ids
        if name is not None:
            self.name = name

    @property
    def channel_notify_template_ids(self):
        """Gets the channel_notify_template_ids of this FilterForListNotifyPoliciesInput.  # noqa: E501


        :return: The channel_notify_template_ids of this FilterForListNotifyPoliciesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._channel_notify_template_ids

    @channel_notify_template_ids.setter
    def channel_notify_template_ids(self, channel_notify_template_ids):
        """Sets the channel_notify_template_ids of this FilterForListNotifyPoliciesInput.


        :param channel_notify_template_ids: The channel_notify_template_ids of this FilterForListNotifyPoliciesInput.  # noqa: E501
        :type: list[str]
        """

        self._channel_notify_template_ids = channel_notify_template_ids

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this FilterForListNotifyPoliciesInput.  # noqa: E501


        :return: The contact_group_ids of this FilterForListNotifyPoliciesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this FilterForListNotifyPoliciesInput.


        :param contact_group_ids: The contact_group_ids of this FilterForListNotifyPoliciesInput.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def ids(self):
        """Gets the ids of this FilterForListNotifyPoliciesInput.  # noqa: E501


        :return: The ids of this FilterForListNotifyPoliciesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListNotifyPoliciesInput.


        :param ids: The ids of this FilterForListNotifyPoliciesInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def name(self):
        """Gets the name of this FilterForListNotifyPoliciesInput.  # noqa: E501


        :return: The name of this FilterForListNotifyPoliciesInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListNotifyPoliciesInput.


        :param name: The name of this FilterForListNotifyPoliciesInput.  # noqa: E501
        :type: str
        """

        self._name = name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListNotifyPoliciesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListNotifyPoliciesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListNotifyPoliciesInput):
            return True

        return self.to_dict() != other.to_dict()
