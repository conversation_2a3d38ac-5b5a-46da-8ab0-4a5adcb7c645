# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AIProviderForGetUpstreamOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'base_url': 'str',
        'custom_body_params': 'CustomBodyParamsForGetUpstreamOutput',
        'custom_header_params': 'CustomHeaderParamsForGetUpstreamOutput',
        'custom_model_service': 'CustomModelServiceForGetUpstreamOutput',
        'name': 'str',
        'token': 'str'
    }

    attribute_map = {
        'base_url': 'BaseUrl',
        'custom_body_params': 'CustomBodyParams',
        'custom_header_params': 'CustomHeaderParams',
        'custom_model_service': 'CustomModelService',
        'name': 'Name',
        'token': 'Token'
    }

    def __init__(self, base_url=None, custom_body_params=None, custom_header_params=None, custom_model_service=None, name=None, token=None, _configuration=None):  # noqa: E501
        """AIProviderForGetUpstreamOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._base_url = None
        self._custom_body_params = None
        self._custom_header_params = None
        self._custom_model_service = None
        self._name = None
        self._token = None
        self.discriminator = None

        if base_url is not None:
            self.base_url = base_url
        if custom_body_params is not None:
            self.custom_body_params = custom_body_params
        if custom_header_params is not None:
            self.custom_header_params = custom_header_params
        if custom_model_service is not None:
            self.custom_model_service = custom_model_service
        if name is not None:
            self.name = name
        if token is not None:
            self.token = token

    @property
    def base_url(self):
        """Gets the base_url of this AIProviderForGetUpstreamOutput.  # noqa: E501


        :return: The base_url of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._base_url

    @base_url.setter
    def base_url(self, base_url):
        """Sets the base_url of this AIProviderForGetUpstreamOutput.


        :param base_url: The base_url of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._base_url = base_url

    @property
    def custom_body_params(self):
        """Gets the custom_body_params of this AIProviderForGetUpstreamOutput.  # noqa: E501


        :return: The custom_body_params of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :rtype: CustomBodyParamsForGetUpstreamOutput
        """
        return self._custom_body_params

    @custom_body_params.setter
    def custom_body_params(self, custom_body_params):
        """Sets the custom_body_params of this AIProviderForGetUpstreamOutput.


        :param custom_body_params: The custom_body_params of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :type: CustomBodyParamsForGetUpstreamOutput
        """

        self._custom_body_params = custom_body_params

    @property
    def custom_header_params(self):
        """Gets the custom_header_params of this AIProviderForGetUpstreamOutput.  # noqa: E501


        :return: The custom_header_params of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :rtype: CustomHeaderParamsForGetUpstreamOutput
        """
        return self._custom_header_params

    @custom_header_params.setter
    def custom_header_params(self, custom_header_params):
        """Sets the custom_header_params of this AIProviderForGetUpstreamOutput.


        :param custom_header_params: The custom_header_params of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :type: CustomHeaderParamsForGetUpstreamOutput
        """

        self._custom_header_params = custom_header_params

    @property
    def custom_model_service(self):
        """Gets the custom_model_service of this AIProviderForGetUpstreamOutput.  # noqa: E501


        :return: The custom_model_service of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :rtype: CustomModelServiceForGetUpstreamOutput
        """
        return self._custom_model_service

    @custom_model_service.setter
    def custom_model_service(self, custom_model_service):
        """Sets the custom_model_service of this AIProviderForGetUpstreamOutput.


        :param custom_model_service: The custom_model_service of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :type: CustomModelServiceForGetUpstreamOutput
        """

        self._custom_model_service = custom_model_service

    @property
    def name(self):
        """Gets the name of this AIProviderForGetUpstreamOutput.  # noqa: E501


        :return: The name of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this AIProviderForGetUpstreamOutput.


        :param name: The name of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def token(self):
        """Gets the token of this AIProviderForGetUpstreamOutput.  # noqa: E501


        :return: The token of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._token

    @token.setter
    def token(self, token):
        """Sets the token of this AIProviderForGetUpstreamOutput.


        :param token: The token of this AIProviderForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._token = token

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AIProviderForGetUpstreamOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AIProviderForGetUpstreamOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AIProviderForGetUpstreamOutput):
            return True

        return self.to_dict() != other.to_dict()
