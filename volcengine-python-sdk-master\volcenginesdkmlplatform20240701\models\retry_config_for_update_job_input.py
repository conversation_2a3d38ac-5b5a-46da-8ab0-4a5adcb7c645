# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RetryConfigForUpdateJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'interval_seconds': 'int',
        'max_retry_times': 'int',
        'triggers': 'list[str]'
    }

    attribute_map = {
        'interval_seconds': 'IntervalSeconds',
        'max_retry_times': 'MaxRetryTimes',
        'triggers': 'Triggers'
    }

    def __init__(self, interval_seconds=None, max_retry_times=None, triggers=None, _configuration=None):  # noqa: E501
        """RetryConfigForUpdateJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._interval_seconds = None
        self._max_retry_times = None
        self._triggers = None
        self.discriminator = None

        if interval_seconds is not None:
            self.interval_seconds = interval_seconds
        if max_retry_times is not None:
            self.max_retry_times = max_retry_times
        if triggers is not None:
            self.triggers = triggers

    @property
    def interval_seconds(self):
        """Gets the interval_seconds of this RetryConfigForUpdateJobInput.  # noqa: E501


        :return: The interval_seconds of this RetryConfigForUpdateJobInput.  # noqa: E501
        :rtype: int
        """
        return self._interval_seconds

    @interval_seconds.setter
    def interval_seconds(self, interval_seconds):
        """Sets the interval_seconds of this RetryConfigForUpdateJobInput.


        :param interval_seconds: The interval_seconds of this RetryConfigForUpdateJobInput.  # noqa: E501
        :type: int
        """

        self._interval_seconds = interval_seconds

    @property
    def max_retry_times(self):
        """Gets the max_retry_times of this RetryConfigForUpdateJobInput.  # noqa: E501


        :return: The max_retry_times of this RetryConfigForUpdateJobInput.  # noqa: E501
        :rtype: int
        """
        return self._max_retry_times

    @max_retry_times.setter
    def max_retry_times(self, max_retry_times):
        """Sets the max_retry_times of this RetryConfigForUpdateJobInput.


        :param max_retry_times: The max_retry_times of this RetryConfigForUpdateJobInput.  # noqa: E501
        :type: int
        """

        self._max_retry_times = max_retry_times

    @property
    def triggers(self):
        """Gets the triggers of this RetryConfigForUpdateJobInput.  # noqa: E501


        :return: The triggers of this RetryConfigForUpdateJobInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._triggers

    @triggers.setter
    def triggers(self, triggers):
        """Sets the triggers of this RetryConfigForUpdateJobInput.


        :param triggers: The triggers of this RetryConfigForUpdateJobInput.  # noqa: E501
        :type: list[str]
        """

        self._triggers = triggers

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RetryConfigForUpdateJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RetryConfigForUpdateJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RetryConfigForUpdateJobInput):
            return True

        return self.to_dict() != other.to_dict()
