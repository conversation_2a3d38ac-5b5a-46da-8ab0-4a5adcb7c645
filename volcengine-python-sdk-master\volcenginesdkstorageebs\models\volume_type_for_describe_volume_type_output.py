# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VolumeTypeForDescribeVolumeTypeOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'extra_performance_types': 'list[ExtraPerformanceTypeForDescribeVolumeTypeOutput]',
        'id': 'str',
        'zones': 'list[str]'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'extra_performance_types': 'ExtraPerformanceTypes',
        'id': 'Id',
        'zones': 'Zones'
    }

    def __init__(self, created_at=None, extra_performance_types=None, id=None, zones=None, _configuration=None):  # noqa: E501
        """VolumeTypeForDescribeVolumeTypeOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._extra_performance_types = None
        self._id = None
        self._zones = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if extra_performance_types is not None:
            self.extra_performance_types = extra_performance_types
        if id is not None:
            self.id = id
        if zones is not None:
            self.zones = zones

    @property
    def created_at(self):
        """Gets the created_at of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501


        :return: The created_at of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this VolumeTypeForDescribeVolumeTypeOutput.


        :param created_at: The created_at of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def extra_performance_types(self):
        """Gets the extra_performance_types of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501


        :return: The extra_performance_types of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :rtype: list[ExtraPerformanceTypeForDescribeVolumeTypeOutput]
        """
        return self._extra_performance_types

    @extra_performance_types.setter
    def extra_performance_types(self, extra_performance_types):
        """Sets the extra_performance_types of this VolumeTypeForDescribeVolumeTypeOutput.


        :param extra_performance_types: The extra_performance_types of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :type: list[ExtraPerformanceTypeForDescribeVolumeTypeOutput]
        """

        self._extra_performance_types = extra_performance_types

    @property
    def id(self):
        """Gets the id of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501


        :return: The id of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VolumeTypeForDescribeVolumeTypeOutput.


        :param id: The id of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def zones(self):
        """Gets the zones of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501


        :return: The zones of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._zones

    @zones.setter
    def zones(self, zones):
        """Sets the zones of this VolumeTypeForDescribeVolumeTypeOutput.


        :param zones: The zones of this VolumeTypeForDescribeVolumeTypeOutput.  # noqa: E501
        :type: list[str]
        """

        self._zones = zones

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VolumeTypeForDescribeVolumeTypeOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VolumeTypeForDescribeVolumeTypeOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VolumeTypeForDescribeVolumeTypeOutput):
            return True

        return self.to_dict() != other.to_dict()
