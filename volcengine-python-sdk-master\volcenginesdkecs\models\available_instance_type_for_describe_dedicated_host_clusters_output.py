# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AvailableInstanceTypeForDescribeDedicatedHostClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'available_capacity': 'int',
        'instance_type': 'str'
    }

    attribute_map = {
        'available_capacity': 'AvailableCapacity',
        'instance_type': 'InstanceType'
    }

    def __init__(self, available_capacity=None, instance_type=None, _configuration=None):  # noqa: E501
        """AvailableInstanceTypeForDescribeDedicatedHostClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._available_capacity = None
        self._instance_type = None
        self.discriminator = None

        if available_capacity is not None:
            self.available_capacity = available_capacity
        if instance_type is not None:
            self.instance_type = instance_type

    @property
    def available_capacity(self):
        """Gets the available_capacity of this AvailableInstanceTypeForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The available_capacity of this AvailableInstanceTypeForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._available_capacity

    @available_capacity.setter
    def available_capacity(self, available_capacity):
        """Sets the available_capacity of this AvailableInstanceTypeForDescribeDedicatedHostClustersOutput.


        :param available_capacity: The available_capacity of this AvailableInstanceTypeForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: int
        """

        self._available_capacity = available_capacity

    @property
    def instance_type(self):
        """Gets the instance_type of this AvailableInstanceTypeForDescribeDedicatedHostClustersOutput.  # noqa: E501


        :return: The instance_type of this AvailableInstanceTypeForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this AvailableInstanceTypeForDescribeDedicatedHostClustersOutput.


        :param instance_type: The instance_type of this AvailableInstanceTypeForDescribeDedicatedHostClustersOutput.  # noqa: E501
        :type: str
        """

        self._instance_type = instance_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AvailableInstanceTypeForDescribeDedicatedHostClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AvailableInstanceTypeForDescribeDedicatedHostClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AvailableInstanceTypeForDescribeDedicatedHostClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
