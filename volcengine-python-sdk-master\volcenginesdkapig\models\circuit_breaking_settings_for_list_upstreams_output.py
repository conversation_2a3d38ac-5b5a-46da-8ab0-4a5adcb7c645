# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CircuitBreakingSettingsForListUpstreamsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'base_ejection_time': 'int',
        'consecutive_errors': 'int',
        'enable': 'bool',
        'interval': 'int',
        'max_ejection_percent': 'int',
        'min_health_percent': 'int'
    }

    attribute_map = {
        'base_ejection_time': 'BaseEjectionTime',
        'consecutive_errors': 'ConsecutiveErrors',
        'enable': 'Enable',
        'interval': 'Interval',
        'max_ejection_percent': 'MaxEjectionPercent',
        'min_health_percent': 'MinHealthPercent'
    }

    def __init__(self, base_ejection_time=None, consecutive_errors=None, enable=None, interval=None, max_ejection_percent=None, min_health_percent=None, _configuration=None):  # noqa: E501
        """CircuitBreakingSettingsForListUpstreamsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._base_ejection_time = None
        self._consecutive_errors = None
        self._enable = None
        self._interval = None
        self._max_ejection_percent = None
        self._min_health_percent = None
        self.discriminator = None

        if base_ejection_time is not None:
            self.base_ejection_time = base_ejection_time
        if consecutive_errors is not None:
            self.consecutive_errors = consecutive_errors
        if enable is not None:
            self.enable = enable
        if interval is not None:
            self.interval = interval
        if max_ejection_percent is not None:
            self.max_ejection_percent = max_ejection_percent
        if min_health_percent is not None:
            self.min_health_percent = min_health_percent

    @property
    def base_ejection_time(self):
        """Gets the base_ejection_time of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501


        :return: The base_ejection_time of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :rtype: int
        """
        return self._base_ejection_time

    @base_ejection_time.setter
    def base_ejection_time(self, base_ejection_time):
        """Sets the base_ejection_time of this CircuitBreakingSettingsForListUpstreamsOutput.


        :param base_ejection_time: The base_ejection_time of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :type: int
        """

        self._base_ejection_time = base_ejection_time

    @property
    def consecutive_errors(self):
        """Gets the consecutive_errors of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501


        :return: The consecutive_errors of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :rtype: int
        """
        return self._consecutive_errors

    @consecutive_errors.setter
    def consecutive_errors(self, consecutive_errors):
        """Sets the consecutive_errors of this CircuitBreakingSettingsForListUpstreamsOutput.


        :param consecutive_errors: The consecutive_errors of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :type: int
        """

        self._consecutive_errors = consecutive_errors

    @property
    def enable(self):
        """Gets the enable of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501


        :return: The enable of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this CircuitBreakingSettingsForListUpstreamsOutput.


        :param enable: The enable of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def interval(self):
        """Gets the interval of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501


        :return: The interval of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :rtype: int
        """
        return self._interval

    @interval.setter
    def interval(self, interval):
        """Sets the interval of this CircuitBreakingSettingsForListUpstreamsOutput.


        :param interval: The interval of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :type: int
        """

        self._interval = interval

    @property
    def max_ejection_percent(self):
        """Gets the max_ejection_percent of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501


        :return: The max_ejection_percent of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_ejection_percent

    @max_ejection_percent.setter
    def max_ejection_percent(self, max_ejection_percent):
        """Sets the max_ejection_percent of this CircuitBreakingSettingsForListUpstreamsOutput.


        :param max_ejection_percent: The max_ejection_percent of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :type: int
        """

        self._max_ejection_percent = max_ejection_percent

    @property
    def min_health_percent(self):
        """Gets the min_health_percent of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501


        :return: The min_health_percent of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :rtype: int
        """
        return self._min_health_percent

    @min_health_percent.setter
    def min_health_percent(self, min_health_percent):
        """Sets the min_health_percent of this CircuitBreakingSettingsForListUpstreamsOutput.


        :param min_health_percent: The min_health_percent of this CircuitBreakingSettingsForListUpstreamsOutput.  # noqa: E501
        :type: int
        """

        self._min_health_percent = min_health_percent

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CircuitBreakingSettingsForListUpstreamsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CircuitBreakingSettingsForListUpstreamsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CircuitBreakingSettingsForListUpstreamsOutput):
            return True

        return self.to_dict() != other.to_dict()
