# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'generation': 'str',
        'instance_type_family': 'str',
        'zone_ids': 'list[str]'
    }

    attribute_map = {
        'generation': 'Generation',
        'instance_type_family': 'InstanceTypeFamily',
        'zone_ids': 'ZoneIds'
    }

    def __init__(self, generation=None, instance_type_family=None, zone_ids=None, _configuration=None):  # noqa: E501
        """InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._generation = None
        self._instance_type_family = None
        self._zone_ids = None
        self.discriminator = None

        if generation is not None:
            self.generation = generation
        if instance_type_family is not None:
            self.instance_type_family = instance_type_family
        if zone_ids is not None:
            self.zone_ids = zone_ids

    @property
    def generation(self):
        """Gets the generation of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.  # noqa: E501


        :return: The generation of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.  # noqa: E501
        :rtype: str
        """
        return self._generation

    @generation.setter
    def generation(self, generation):
        """Sets the generation of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.


        :param generation: The generation of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.  # noqa: E501
        :type: str
        """

        self._generation = generation

    @property
    def instance_type_family(self):
        """Gets the instance_type_family of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.  # noqa: E501


        :return: The instance_type_family of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type_family

    @instance_type_family.setter
    def instance_type_family(self, instance_type_family):
        """Sets the instance_type_family of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.


        :param instance_type_family: The instance_type_family of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.  # noqa: E501
        :type: str
        """

        self._instance_type_family = instance_type_family

    @property
    def zone_ids(self):
        """Gets the zone_ids of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.  # noqa: E501


        :return: The zone_ids of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._zone_ids

    @zone_ids.setter
    def zone_ids(self, zone_ids):
        """Sets the zone_ids of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.


        :param zone_ids: The zone_ids of this InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput.  # noqa: E501
        :type: list[str]
        """

        self._zone_ids = zone_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceTypeFamilyForDescribeInstanceTypeFamiliesOutput):
            return True

        return self.to_dict() != other.to_dict()
