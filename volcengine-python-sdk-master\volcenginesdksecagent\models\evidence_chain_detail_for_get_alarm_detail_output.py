# coding: utf-8

"""
    sec_agent

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EvidenceChainDetailForGetAlarmDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action_advice': 'str',
        'alarm_raw_data': 'str',
        'analysis_end_time': 'int',
        'analysis_start_time': 'int',
        'critical_attribute': 'str',
        'event_summarize': 'str',
        'evidences': 'EvidencesForGetAlarmDetailOutput',
        'feedback': 'FeedbackForGetAlarmDetailOutput',
        'final_analysis_result': 'str',
        'findings': 'FindingsForGetAlarmDetailOutput',
        'first_analysis_result': 'str',
        'formatted_alarm': 'str',
        'ioc_data': 'IOCDataForGetAlarmDetailOutput',
        'kill_chain': 'str',
        'planning_survey_path': 'PlanningSurveyPathForGetAlarmDetailOutput',
        'product_name': 'str',
        'rag_detail': 'RagDetailForGetAlarmDetailOutput'
    }

    attribute_map = {
        'action_advice': 'ActionAdvice',
        'alarm_raw_data': 'AlarmRawData',
        'analysis_end_time': 'AnalysisEndTime',
        'analysis_start_time': 'AnalysisStartTime',
        'critical_attribute': 'CriticalAttribute',
        'event_summarize': 'EventSummarize',
        'evidences': 'Evidences',
        'feedback': 'Feedback',
        'final_analysis_result': 'FinalAnalysisResult',
        'findings': 'Findings',
        'first_analysis_result': 'FirstAnalysisResult',
        'formatted_alarm': 'FormattedAlarm',
        'ioc_data': 'IOCData',
        'kill_chain': 'KillChain',
        'planning_survey_path': 'PlanningSurveyPath',
        'product_name': 'ProductName',
        'rag_detail': 'RagDetail'
    }

    def __init__(self, action_advice=None, alarm_raw_data=None, analysis_end_time=None, analysis_start_time=None, critical_attribute=None, event_summarize=None, evidences=None, feedback=None, final_analysis_result=None, findings=None, first_analysis_result=None, formatted_alarm=None, ioc_data=None, kill_chain=None, planning_survey_path=None, product_name=None, rag_detail=None, _configuration=None):  # noqa: E501
        """EvidenceChainDetailForGetAlarmDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action_advice = None
        self._alarm_raw_data = None
        self._analysis_end_time = None
        self._analysis_start_time = None
        self._critical_attribute = None
        self._event_summarize = None
        self._evidences = None
        self._feedback = None
        self._final_analysis_result = None
        self._findings = None
        self._first_analysis_result = None
        self._formatted_alarm = None
        self._ioc_data = None
        self._kill_chain = None
        self._planning_survey_path = None
        self._product_name = None
        self._rag_detail = None
        self.discriminator = None

        if action_advice is not None:
            self.action_advice = action_advice
        if alarm_raw_data is not None:
            self.alarm_raw_data = alarm_raw_data
        if analysis_end_time is not None:
            self.analysis_end_time = analysis_end_time
        if analysis_start_time is not None:
            self.analysis_start_time = analysis_start_time
        if critical_attribute is not None:
            self.critical_attribute = critical_attribute
        if event_summarize is not None:
            self.event_summarize = event_summarize
        if evidences is not None:
            self.evidences = evidences
        if feedback is not None:
            self.feedback = feedback
        if final_analysis_result is not None:
            self.final_analysis_result = final_analysis_result
        if findings is not None:
            self.findings = findings
        if first_analysis_result is not None:
            self.first_analysis_result = first_analysis_result
        if formatted_alarm is not None:
            self.formatted_alarm = formatted_alarm
        if ioc_data is not None:
            self.ioc_data = ioc_data
        if kill_chain is not None:
            self.kill_chain = kill_chain
        if planning_survey_path is not None:
            self.planning_survey_path = planning_survey_path
        if product_name is not None:
            self.product_name = product_name
        if rag_detail is not None:
            self.rag_detail = rag_detail

    @property
    def action_advice(self):
        """Gets the action_advice of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The action_advice of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._action_advice

    @action_advice.setter
    def action_advice(self, action_advice):
        """Sets the action_advice of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param action_advice: The action_advice of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._action_advice = action_advice

    @property
    def alarm_raw_data(self):
        """Gets the alarm_raw_data of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The alarm_raw_data of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_raw_data

    @alarm_raw_data.setter
    def alarm_raw_data(self, alarm_raw_data):
        """Sets the alarm_raw_data of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param alarm_raw_data: The alarm_raw_data of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._alarm_raw_data = alarm_raw_data

    @property
    def analysis_end_time(self):
        """Gets the analysis_end_time of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The analysis_end_time of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._analysis_end_time

    @analysis_end_time.setter
    def analysis_end_time(self, analysis_end_time):
        """Sets the analysis_end_time of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param analysis_end_time: The analysis_end_time of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: int
        """

        self._analysis_end_time = analysis_end_time

    @property
    def analysis_start_time(self):
        """Gets the analysis_start_time of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The analysis_start_time of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._analysis_start_time

    @analysis_start_time.setter
    def analysis_start_time(self, analysis_start_time):
        """Sets the analysis_start_time of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param analysis_start_time: The analysis_start_time of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: int
        """

        self._analysis_start_time = analysis_start_time

    @property
    def critical_attribute(self):
        """Gets the critical_attribute of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The critical_attribute of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._critical_attribute

    @critical_attribute.setter
    def critical_attribute(self, critical_attribute):
        """Sets the critical_attribute of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param critical_attribute: The critical_attribute of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._critical_attribute = critical_attribute

    @property
    def event_summarize(self):
        """Gets the event_summarize of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The event_summarize of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_summarize

    @event_summarize.setter
    def event_summarize(self, event_summarize):
        """Sets the event_summarize of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param event_summarize: The event_summarize of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._event_summarize = event_summarize

    @property
    def evidences(self):
        """Gets the evidences of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The evidences of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: EvidencesForGetAlarmDetailOutput
        """
        return self._evidences

    @evidences.setter
    def evidences(self, evidences):
        """Sets the evidences of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param evidences: The evidences of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: EvidencesForGetAlarmDetailOutput
        """

        self._evidences = evidences

    @property
    def feedback(self):
        """Gets the feedback of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The feedback of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: FeedbackForGetAlarmDetailOutput
        """
        return self._feedback

    @feedback.setter
    def feedback(self, feedback):
        """Sets the feedback of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param feedback: The feedback of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: FeedbackForGetAlarmDetailOutput
        """

        self._feedback = feedback

    @property
    def final_analysis_result(self):
        """Gets the final_analysis_result of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The final_analysis_result of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._final_analysis_result

    @final_analysis_result.setter
    def final_analysis_result(self, final_analysis_result):
        """Sets the final_analysis_result of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param final_analysis_result: The final_analysis_result of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._final_analysis_result = final_analysis_result

    @property
    def findings(self):
        """Gets the findings of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The findings of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: FindingsForGetAlarmDetailOutput
        """
        return self._findings

    @findings.setter
    def findings(self, findings):
        """Sets the findings of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param findings: The findings of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: FindingsForGetAlarmDetailOutput
        """

        self._findings = findings

    @property
    def first_analysis_result(self):
        """Gets the first_analysis_result of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The first_analysis_result of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._first_analysis_result

    @first_analysis_result.setter
    def first_analysis_result(self, first_analysis_result):
        """Sets the first_analysis_result of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param first_analysis_result: The first_analysis_result of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._first_analysis_result = first_analysis_result

    @property
    def formatted_alarm(self):
        """Gets the formatted_alarm of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The formatted_alarm of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._formatted_alarm

    @formatted_alarm.setter
    def formatted_alarm(self, formatted_alarm):
        """Sets the formatted_alarm of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param formatted_alarm: The formatted_alarm of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._formatted_alarm = formatted_alarm

    @property
    def ioc_data(self):
        """Gets the ioc_data of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The ioc_data of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: IOCDataForGetAlarmDetailOutput
        """
        return self._ioc_data

    @ioc_data.setter
    def ioc_data(self, ioc_data):
        """Sets the ioc_data of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param ioc_data: The ioc_data of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: IOCDataForGetAlarmDetailOutput
        """

        self._ioc_data = ioc_data

    @property
    def kill_chain(self):
        """Gets the kill_chain of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The kill_chain of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._kill_chain

    @kill_chain.setter
    def kill_chain(self, kill_chain):
        """Sets the kill_chain of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param kill_chain: The kill_chain of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._kill_chain = kill_chain

    @property
    def planning_survey_path(self):
        """Gets the planning_survey_path of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The planning_survey_path of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: PlanningSurveyPathForGetAlarmDetailOutput
        """
        return self._planning_survey_path

    @planning_survey_path.setter
    def planning_survey_path(self, planning_survey_path):
        """Sets the planning_survey_path of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param planning_survey_path: The planning_survey_path of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: PlanningSurveyPathForGetAlarmDetailOutput
        """

        self._planning_survey_path = planning_survey_path

    @property
    def product_name(self):
        """Gets the product_name of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The product_name of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._product_name

    @product_name.setter
    def product_name(self, product_name):
        """Sets the product_name of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param product_name: The product_name of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: str
        """

        self._product_name = product_name

    @property
    def rag_detail(self):
        """Gets the rag_detail of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501


        :return: The rag_detail of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :rtype: RagDetailForGetAlarmDetailOutput
        """
        return self._rag_detail

    @rag_detail.setter
    def rag_detail(self, rag_detail):
        """Sets the rag_detail of this EvidenceChainDetailForGetAlarmDetailOutput.


        :param rag_detail: The rag_detail of this EvidenceChainDetailForGetAlarmDetailOutput.  # noqa: E501
        :type: RagDetailForGetAlarmDetailOutput
        """

        self._rag_detail = rag_detail

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EvidenceChainDetailForGetAlarmDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EvidenceChainDetailForGetAlarmDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EvidenceChainDetailForGetAlarmDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
