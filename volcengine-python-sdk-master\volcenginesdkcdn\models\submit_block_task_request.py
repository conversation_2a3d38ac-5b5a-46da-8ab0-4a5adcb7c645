# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubmitBlockTaskRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'type': 'str',
        'urls': 'str'
    }

    attribute_map = {
        'type': 'Type',
        'urls': 'Urls'
    }

    def __init__(self, type=None, urls=None, _configuration=None):  # noqa: E501
        """SubmitBlockTaskRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._type = None
        self._urls = None
        self.discriminator = None

        if type is not None:
            self.type = type
        self.urls = urls

    @property
    def type(self):
        """Gets the type of this SubmitBlockTaskRequest.  # noqa: E501


        :return: The type of this SubmitBlockTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this SubmitBlockTaskRequest.


        :param type: The type of this SubmitBlockTaskRequest.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def urls(self):
        """Gets the urls of this SubmitBlockTaskRequest.  # noqa: E501


        :return: The urls of this SubmitBlockTaskRequest.  # noqa: E501
        :rtype: str
        """
        return self._urls

    @urls.setter
    def urls(self, urls):
        """Sets the urls of this SubmitBlockTaskRequest.


        :param urls: The urls of this SubmitBlockTaskRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and urls is None:
            raise ValueError("Invalid value for `urls`, must not be `None`")  # noqa: E501

        self._urls = urls

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubmitBlockTaskRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubmitBlockTaskRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubmitBlockTaskRequest):
            return True

        return self.to_dict() != other.to_dict()
