# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteEDXPeerLinkRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'peer_link_id': 'str'
    }

    attribute_map = {
        'peer_link_id': 'PeerLinkId'
    }

    def __init__(self, peer_link_id=None, _configuration=None):  # noqa: E501
        """DeleteEDXPeerLinkRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._peer_link_id = None
        self.discriminator = None

        self.peer_link_id = peer_link_id

    @property
    def peer_link_id(self):
        """Gets the peer_link_id of this DeleteEDXPeerLinkRequest.  # noqa: E501


        :return: The peer_link_id of this DeleteEDXPeerLinkRequest.  # noqa: E501
        :rtype: str
        """
        return self._peer_link_id

    @peer_link_id.setter
    def peer_link_id(self, peer_link_id):
        """Sets the peer_link_id of this DeleteEDXPeerLinkRequest.


        :param peer_link_id: The peer_link_id of this DeleteEDXPeerLinkRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and peer_link_id is None:
            raise ValueError("Invalid value for `peer_link_id`, must not be `None`")  # noqa: E501

        self._peer_link_id = peer_link_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteEDXPeerLinkRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteEDXPeerLinkRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteEDXPeerLinkRequest):
            return True

        return self.to_dict() != other.to_dict()
