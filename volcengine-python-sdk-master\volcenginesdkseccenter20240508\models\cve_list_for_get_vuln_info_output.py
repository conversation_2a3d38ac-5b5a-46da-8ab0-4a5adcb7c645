# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CveListForGetVulnInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cve_detail_url': 'str',
        'cve_id': 'str',
        'cvss': 'str',
        'descript': 'str',
        'has_exploit': 'bool',
        'tag': 'list[str]',
        'vuln_name': 'str'
    }

    attribute_map = {
        'cve_detail_url': 'CveDetailUrl',
        'cve_id': 'CveID',
        'cvss': 'Cvss',
        'descript': 'Descript',
        'has_exploit': 'HasExploit',
        'tag': 'Tag',
        'vuln_name': 'VulnName'
    }

    def __init__(self, cve_detail_url=None, cve_id=None, cvss=None, descript=None, has_exploit=None, tag=None, vuln_name=None, _configuration=None):  # noqa: E501
        """CveListForGetVulnInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cve_detail_url = None
        self._cve_id = None
        self._cvss = None
        self._descript = None
        self._has_exploit = None
        self._tag = None
        self._vuln_name = None
        self.discriminator = None

        if cve_detail_url is not None:
            self.cve_detail_url = cve_detail_url
        if cve_id is not None:
            self.cve_id = cve_id
        if cvss is not None:
            self.cvss = cvss
        if descript is not None:
            self.descript = descript
        if has_exploit is not None:
            self.has_exploit = has_exploit
        if tag is not None:
            self.tag = tag
        if vuln_name is not None:
            self.vuln_name = vuln_name

    @property
    def cve_detail_url(self):
        """Gets the cve_detail_url of this CveListForGetVulnInfoOutput.  # noqa: E501


        :return: The cve_detail_url of this CveListForGetVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cve_detail_url

    @cve_detail_url.setter
    def cve_detail_url(self, cve_detail_url):
        """Sets the cve_detail_url of this CveListForGetVulnInfoOutput.


        :param cve_detail_url: The cve_detail_url of this CveListForGetVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._cve_detail_url = cve_detail_url

    @property
    def cve_id(self):
        """Gets the cve_id of this CveListForGetVulnInfoOutput.  # noqa: E501


        :return: The cve_id of this CveListForGetVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cve_id

    @cve_id.setter
    def cve_id(self, cve_id):
        """Sets the cve_id of this CveListForGetVulnInfoOutput.


        :param cve_id: The cve_id of this CveListForGetVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._cve_id = cve_id

    @property
    def cvss(self):
        """Gets the cvss of this CveListForGetVulnInfoOutput.  # noqa: E501


        :return: The cvss of this CveListForGetVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cvss

    @cvss.setter
    def cvss(self, cvss):
        """Sets the cvss of this CveListForGetVulnInfoOutput.


        :param cvss: The cvss of this CveListForGetVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._cvss = cvss

    @property
    def descript(self):
        """Gets the descript of this CveListForGetVulnInfoOutput.  # noqa: E501


        :return: The descript of this CveListForGetVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._descript

    @descript.setter
    def descript(self, descript):
        """Sets the descript of this CveListForGetVulnInfoOutput.


        :param descript: The descript of this CveListForGetVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._descript = descript

    @property
    def has_exploit(self):
        """Gets the has_exploit of this CveListForGetVulnInfoOutput.  # noqa: E501


        :return: The has_exploit of this CveListForGetVulnInfoOutput.  # noqa: E501
        :rtype: bool
        """
        return self._has_exploit

    @has_exploit.setter
    def has_exploit(self, has_exploit):
        """Sets the has_exploit of this CveListForGetVulnInfoOutput.


        :param has_exploit: The has_exploit of this CveListForGetVulnInfoOutput.  # noqa: E501
        :type: bool
        """

        self._has_exploit = has_exploit

    @property
    def tag(self):
        """Gets the tag of this CveListForGetVulnInfoOutput.  # noqa: E501


        :return: The tag of this CveListForGetVulnInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this CveListForGetVulnInfoOutput.


        :param tag: The tag of this CveListForGetVulnInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._tag = tag

    @property
    def vuln_name(self):
        """Gets the vuln_name of this CveListForGetVulnInfoOutput.  # noqa: E501


        :return: The vuln_name of this CveListForGetVulnInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_name

    @vuln_name.setter
    def vuln_name(self, vuln_name):
        """Sets the vuln_name of this CveListForGetVulnInfoOutput.


        :param vuln_name: The vuln_name of this CveListForGetVulnInfoOutput.  # noqa: E501
        :type: str
        """

        self._vuln_name = vuln_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CveListForGetVulnInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CveListForGetVulnInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CveListForGetVulnInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
