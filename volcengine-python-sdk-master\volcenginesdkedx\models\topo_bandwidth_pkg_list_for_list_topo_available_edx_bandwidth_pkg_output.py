# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'left_bandwidth_cap': 'int',
        'name': 'str',
        'pkg_id': 'str'
    }

    attribute_map = {
        'left_bandwidth_cap': 'LeftBandwidthCap',
        'name': 'Name',
        'pkg_id': 'PkgID'
    }

    def __init__(self, left_bandwidth_cap=None, name=None, pkg_id=None, _configuration=None):  # noqa: E501
        """TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._left_bandwidth_cap = None
        self._name = None
        self._pkg_id = None
        self.discriminator = None

        if left_bandwidth_cap is not None:
            self.left_bandwidth_cap = left_bandwidth_cap
        if name is not None:
            self.name = name
        if pkg_id is not None:
            self.pkg_id = pkg_id

    @property
    def left_bandwidth_cap(self):
        """Gets the left_bandwidth_cap of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.  # noqa: E501


        :return: The left_bandwidth_cap of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: int
        """
        return self._left_bandwidth_cap

    @left_bandwidth_cap.setter
    def left_bandwidth_cap(self, left_bandwidth_cap):
        """Sets the left_bandwidth_cap of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.


        :param left_bandwidth_cap: The left_bandwidth_cap of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.  # noqa: E501
        :type: int
        """

        self._left_bandwidth_cap = left_bandwidth_cap

    @property
    def name(self):
        """Gets the name of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.  # noqa: E501


        :return: The name of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.


        :param name: The name of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def pkg_id(self):
        """Gets the pkg_id of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.  # noqa: E501


        :return: The pkg_id of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_id

    @pkg_id.setter
    def pkg_id(self, pkg_id):
        """Sets the pkg_id of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.


        :param pkg_id: The pkg_id of this TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput.  # noqa: E501
        :type: str
        """

        self._pkg_id = pkg_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TopoBandwidthPkgListForListTopoAvailableEDXBandwidthPkgOutput):
            return True

        return self.to_dict() != other.to_dict()
