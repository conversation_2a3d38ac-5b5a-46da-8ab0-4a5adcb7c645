# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAssetCenterDevsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'alarm': 'int',
        'asset_id': 'str',
        'asset_name': 'str',
        'asset_type': 'str',
        'asset_uk': 'str',
        'baseline': 'int',
        'ip': 'str',
        'platform': 'str',
        'region': 'str',
        'status': 'str',
        'vul': 'int'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'alarm': 'Alarm',
        'asset_id': 'AssetId',
        'asset_name': 'AssetName',
        'asset_type': 'AssetType',
        'asset_uk': 'AssetUk',
        'baseline': 'Baseline',
        'ip': 'Ip',
        'platform': 'Platform',
        'region': 'Region',
        'status': 'Status',
        'vul': 'Vul'
    }

    def __init__(self, account_id=None, alarm=None, asset_id=None, asset_name=None, asset_type=None, asset_uk=None, baseline=None, ip=None, platform=None, region=None, status=None, vul=None, _configuration=None):  # noqa: E501
        """DataForListAssetCenterDevsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._alarm = None
        self._asset_id = None
        self._asset_name = None
        self._asset_type = None
        self._asset_uk = None
        self._baseline = None
        self._ip = None
        self._platform = None
        self._region = None
        self._status = None
        self._vul = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if alarm is not None:
            self.alarm = alarm
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_name is not None:
            self.asset_name = asset_name
        if asset_type is not None:
            self.asset_type = asset_type
        if asset_uk is not None:
            self.asset_uk = asset_uk
        if baseline is not None:
            self.baseline = baseline
        if ip is not None:
            self.ip = ip
        if platform is not None:
            self.platform = platform
        if region is not None:
            self.region = region
        if status is not None:
            self.status = status
        if vul is not None:
            self.vul = vul

    @property
    def account_id(self):
        """Gets the account_id of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The account_id of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForListAssetCenterDevsOutput.


        :param account_id: The account_id of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def alarm(self):
        """Gets the alarm of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The alarm of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: int
        """
        return self._alarm

    @alarm.setter
    def alarm(self, alarm):
        """Sets the alarm of this DataForListAssetCenterDevsOutput.


        :param alarm: The alarm of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: int
        """

        self._alarm = alarm

    @property
    def asset_id(self):
        """Gets the asset_id of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The asset_id of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this DataForListAssetCenterDevsOutput.


        :param asset_id: The asset_id of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_name(self):
        """Gets the asset_name of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The asset_name of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this DataForListAssetCenterDevsOutput.


        :param asset_name: The asset_name of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def asset_type(self):
        """Gets the asset_type of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The asset_type of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this DataForListAssetCenterDevsOutput.


        :param asset_type: The asset_type of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: str
        """

        self._asset_type = asset_type

    @property
    def asset_uk(self):
        """Gets the asset_uk of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The asset_uk of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_uk

    @asset_uk.setter
    def asset_uk(self, asset_uk):
        """Sets the asset_uk of this DataForListAssetCenterDevsOutput.


        :param asset_uk: The asset_uk of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: str
        """

        self._asset_uk = asset_uk

    @property
    def baseline(self):
        """Gets the baseline of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The baseline of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: int
        """
        return self._baseline

    @baseline.setter
    def baseline(self, baseline):
        """Sets the baseline of this DataForListAssetCenterDevsOutput.


        :param baseline: The baseline of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: int
        """

        self._baseline = baseline

    @property
    def ip(self):
        """Gets the ip of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The ip of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this DataForListAssetCenterDevsOutput.


        :param ip: The ip of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def platform(self):
        """Gets the platform of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The platform of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this DataForListAssetCenterDevsOutput.


        :param platform: The platform of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: str
        """

        self._platform = platform

    @property
    def region(self):
        """Gets the region of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The region of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListAssetCenterDevsOutput.


        :param region: The region of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def status(self):
        """Gets the status of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The status of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListAssetCenterDevsOutput.


        :param status: The status of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def vul(self):
        """Gets the vul of this DataForListAssetCenterDevsOutput.  # noqa: E501


        :return: The vul of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :rtype: int
        """
        return self._vul

    @vul.setter
    def vul(self, vul):
        """Sets the vul of this DataForListAssetCenterDevsOutput.


        :param vul: The vul of this DataForListAssetCenterDevsOutput.  # noqa: E501
        :type: int
        """

        self._vul = vul

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAssetCenterDevsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAssetCenterDevsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAssetCenterDevsOutput):
            return True

        return self.to_dict() != other.to_dict()
