# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VepfsForListResourceGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system_ids': 'list[str]',
        'mount_service_id': 'str',
        'prefetch_enabled': 'bool',
        'unavailable_node_count': 'int'
    }

    attribute_map = {
        'file_system_ids': 'FileSystemIds',
        'mount_service_id': 'MountServiceId',
        'prefetch_enabled': 'PrefetchEnabled',
        'unavailable_node_count': 'UnavailableNodeCount'
    }

    def __init__(self, file_system_ids=None, mount_service_id=None, prefetch_enabled=None, unavailable_node_count=None, _configuration=None):  # noqa: E501
        """VepfsForListResourceGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system_ids = None
        self._mount_service_id = None
        self._prefetch_enabled = None
        self._unavailable_node_count = None
        self.discriminator = None

        if file_system_ids is not None:
            self.file_system_ids = file_system_ids
        if mount_service_id is not None:
            self.mount_service_id = mount_service_id
        if prefetch_enabled is not None:
            self.prefetch_enabled = prefetch_enabled
        if unavailable_node_count is not None:
            self.unavailable_node_count = unavailable_node_count

    @property
    def file_system_ids(self):
        """Gets the file_system_ids of this VepfsForListResourceGroupsOutput.  # noqa: E501


        :return: The file_system_ids of this VepfsForListResourceGroupsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._file_system_ids

    @file_system_ids.setter
    def file_system_ids(self, file_system_ids):
        """Sets the file_system_ids of this VepfsForListResourceGroupsOutput.


        :param file_system_ids: The file_system_ids of this VepfsForListResourceGroupsOutput.  # noqa: E501
        :type: list[str]
        """

        self._file_system_ids = file_system_ids

    @property
    def mount_service_id(self):
        """Gets the mount_service_id of this VepfsForListResourceGroupsOutput.  # noqa: E501


        :return: The mount_service_id of this VepfsForListResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._mount_service_id

    @mount_service_id.setter
    def mount_service_id(self, mount_service_id):
        """Sets the mount_service_id of this VepfsForListResourceGroupsOutput.


        :param mount_service_id: The mount_service_id of this VepfsForListResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._mount_service_id = mount_service_id

    @property
    def prefetch_enabled(self):
        """Gets the prefetch_enabled of this VepfsForListResourceGroupsOutput.  # noqa: E501


        :return: The prefetch_enabled of this VepfsForListResourceGroupsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._prefetch_enabled

    @prefetch_enabled.setter
    def prefetch_enabled(self, prefetch_enabled):
        """Sets the prefetch_enabled of this VepfsForListResourceGroupsOutput.


        :param prefetch_enabled: The prefetch_enabled of this VepfsForListResourceGroupsOutput.  # noqa: E501
        :type: bool
        """

        self._prefetch_enabled = prefetch_enabled

    @property
    def unavailable_node_count(self):
        """Gets the unavailable_node_count of this VepfsForListResourceGroupsOutput.  # noqa: E501


        :return: The unavailable_node_count of this VepfsForListResourceGroupsOutput.  # noqa: E501
        :rtype: int
        """
        return self._unavailable_node_count

    @unavailable_node_count.setter
    def unavailable_node_count(self, unavailable_node_count):
        """Sets the unavailable_node_count of this VepfsForListResourceGroupsOutput.


        :param unavailable_node_count: The unavailable_node_count of this VepfsForListResourceGroupsOutput.  # noqa: E501
        :type: int
        """

        self._unavailable_node_count = unavailable_node_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VepfsForListResourceGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VepfsForListResourceGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VepfsForListResourceGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
