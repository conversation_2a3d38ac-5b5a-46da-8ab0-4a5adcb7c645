# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListAlertingRulesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'annotations': 'list[AnnotationForListAlertingRulesOutput]',
        'create_time': 'str',
        'description': 'str',
        'group_id': 'str',
        'id': 'str',
        'labels': 'list[LabelForListAlertingRulesOutput]',
        'levels': 'list[LevelForListAlertingRulesOutput]',
        'name': 'str',
        'notify_group_policy_id': 'str',
        'notify_policy_id': 'str',
        'query': 'QueryForListAlertingRulesOutput',
        'status': 'str',
        'type': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'annotations': 'Annotations',
        'create_time': 'CreateTime',
        'description': 'Description',
        'group_id': 'GroupId',
        'id': 'Id',
        'labels': 'Labels',
        'levels': 'Levels',
        'name': 'Name',
        'notify_group_policy_id': 'NotifyGroupPolicyId',
        'notify_policy_id': 'NotifyPolicyId',
        'query': 'Query',
        'status': 'Status',
        'type': 'Type',
        'update_time': 'UpdateTime'
    }

    def __init__(self, annotations=None, create_time=None, description=None, group_id=None, id=None, labels=None, levels=None, name=None, notify_group_policy_id=None, notify_policy_id=None, query=None, status=None, type=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListAlertingRulesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._annotations = None
        self._create_time = None
        self._description = None
        self._group_id = None
        self._id = None
        self._labels = None
        self._levels = None
        self._name = None
        self._notify_group_policy_id = None
        self._notify_policy_id = None
        self._query = None
        self._status = None
        self._type = None
        self._update_time = None
        self.discriminator = None

        if annotations is not None:
            self.annotations = annotations
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if group_id is not None:
            self.group_id = group_id
        if id is not None:
            self.id = id
        if labels is not None:
            self.labels = labels
        if levels is not None:
            self.levels = levels
        if name is not None:
            self.name = name
        if notify_group_policy_id is not None:
            self.notify_group_policy_id = notify_group_policy_id
        if notify_policy_id is not None:
            self.notify_policy_id = notify_policy_id
        if query is not None:
            self.query = query
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type
        if update_time is not None:
            self.update_time = update_time

    @property
    def annotations(self):
        """Gets the annotations of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The annotations of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: list[AnnotationForListAlertingRulesOutput]
        """
        return self._annotations

    @annotations.setter
    def annotations(self, annotations):
        """Sets the annotations of this ItemForListAlertingRulesOutput.


        :param annotations: The annotations of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: list[AnnotationForListAlertingRulesOutput]
        """

        self._annotations = annotations

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The create_time of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListAlertingRulesOutput.


        :param create_time: The create_time of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The description of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListAlertingRulesOutput.


        :param description: The description of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def group_id(self):
        """Gets the group_id of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The group_id of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this ItemForListAlertingRulesOutput.


        :param group_id: The group_id of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._group_id = group_id

    @property
    def id(self):
        """Gets the id of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The id of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListAlertingRulesOutput.


        :param id: The id of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def labels(self):
        """Gets the labels of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The labels of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: list[LabelForListAlertingRulesOutput]
        """
        return self._labels

    @labels.setter
    def labels(self, labels):
        """Sets the labels of this ItemForListAlertingRulesOutput.


        :param labels: The labels of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: list[LabelForListAlertingRulesOutput]
        """

        self._labels = labels

    @property
    def levels(self):
        """Gets the levels of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The levels of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: list[LevelForListAlertingRulesOutput]
        """
        return self._levels

    @levels.setter
    def levels(self, levels):
        """Sets the levels of this ItemForListAlertingRulesOutput.


        :param levels: The levels of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: list[LevelForListAlertingRulesOutput]
        """

        self._levels = levels

    @property
    def name(self):
        """Gets the name of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The name of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListAlertingRulesOutput.


        :param name: The name of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def notify_group_policy_id(self):
        """Gets the notify_group_policy_id of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The notify_group_policy_id of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._notify_group_policy_id

    @notify_group_policy_id.setter
    def notify_group_policy_id(self, notify_group_policy_id):
        """Sets the notify_group_policy_id of this ItemForListAlertingRulesOutput.


        :param notify_group_policy_id: The notify_group_policy_id of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._notify_group_policy_id = notify_group_policy_id

    @property
    def notify_policy_id(self):
        """Gets the notify_policy_id of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The notify_policy_id of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._notify_policy_id

    @notify_policy_id.setter
    def notify_policy_id(self, notify_policy_id):
        """Sets the notify_policy_id of this ItemForListAlertingRulesOutput.


        :param notify_policy_id: The notify_policy_id of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._notify_policy_id = notify_policy_id

    @property
    def query(self):
        """Gets the query of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The query of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: QueryForListAlertingRulesOutput
        """
        return self._query

    @query.setter
    def query(self, query):
        """Sets the query of this ItemForListAlertingRulesOutput.


        :param query: The query of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: QueryForListAlertingRulesOutput
        """

        self._query = query

    @property
    def status(self):
        """Gets the status of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The status of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListAlertingRulesOutput.


        :param status: The status of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The type of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemForListAlertingRulesOutput.


        :param type: The type of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListAlertingRulesOutput.  # noqa: E501


        :return: The update_time of this ItemForListAlertingRulesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListAlertingRulesOutput.


        :param update_time: The update_time of this ItemForListAlertingRulesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListAlertingRulesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListAlertingRulesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListAlertingRulesOutput):
            return True

        return self.to_dict() != other.to_dict()
