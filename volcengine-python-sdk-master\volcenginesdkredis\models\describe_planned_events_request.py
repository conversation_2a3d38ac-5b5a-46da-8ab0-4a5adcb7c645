# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribePlannedEventsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'max_start_time': 'str',
        'min_start_time': 'str',
        'page_number': 'int',
        'page_size': 'int'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'max_start_time': 'MaxStartTime',
        'min_start_time': 'MinStartTime',
        'page_number': 'PageNumber',
        'page_size': 'PageSize'
    }

    def __init__(self, instance_id=None, max_start_time=None, min_start_time=None, page_number=None, page_size=None, _configuration=None):  # noqa: E501
        """DescribePlannedEventsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._max_start_time = None
        self._min_start_time = None
        self._page_number = None
        self._page_size = None
        self.discriminator = None

        if instance_id is not None:
            self.instance_id = instance_id
        if max_start_time is not None:
            self.max_start_time = max_start_time
        if min_start_time is not None:
            self.min_start_time = min_start_time
        self.page_number = page_number
        self.page_size = page_size

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribePlannedEventsRequest.  # noqa: E501


        :return: The instance_id of this DescribePlannedEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribePlannedEventsRequest.


        :param instance_id: The instance_id of this DescribePlannedEventsRequest.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def max_start_time(self):
        """Gets the max_start_time of this DescribePlannedEventsRequest.  # noqa: E501


        :return: The max_start_time of this DescribePlannedEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._max_start_time

    @max_start_time.setter
    def max_start_time(self, max_start_time):
        """Sets the max_start_time of this DescribePlannedEventsRequest.


        :param max_start_time: The max_start_time of this DescribePlannedEventsRequest.  # noqa: E501
        :type: str
        """

        self._max_start_time = max_start_time

    @property
    def min_start_time(self):
        """Gets the min_start_time of this DescribePlannedEventsRequest.  # noqa: E501


        :return: The min_start_time of this DescribePlannedEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._min_start_time

    @min_start_time.setter
    def min_start_time(self, min_start_time):
        """Sets the min_start_time of this DescribePlannedEventsRequest.


        :param min_start_time: The min_start_time of this DescribePlannedEventsRequest.  # noqa: E501
        :type: str
        """

        self._min_start_time = min_start_time

    @property
    def page_number(self):
        """Gets the page_number of this DescribePlannedEventsRequest.  # noqa: E501


        :return: The page_number of this DescribePlannedEventsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribePlannedEventsRequest.


        :param page_number: The page_number of this DescribePlannedEventsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribePlannedEventsRequest.  # noqa: E501


        :return: The page_size of this DescribePlannedEventsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribePlannedEventsRequest.


        :param page_size: The page_size of this DescribePlannedEventsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribePlannedEventsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribePlannedEventsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribePlannedEventsRequest):
            return True

        return self.to_dict() != other.to_dict()
