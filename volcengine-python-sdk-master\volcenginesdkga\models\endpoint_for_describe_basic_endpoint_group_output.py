# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EndpointForDescribeBasicEndpointGroupOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'edge_node_name': 'str',
        'endpoint_address': 'str',
        'endpoint_id': 'str',
        'type': 'str'
    }

    attribute_map = {
        'edge_node_name': 'EdgeNodeName',
        'endpoint_address': 'EndpointAddress',
        'endpoint_id': 'EndpointId',
        'type': 'Type'
    }

    def __init__(self, edge_node_name=None, endpoint_address=None, endpoint_id=None, type=None, _configuration=None):  # noqa: E501
        """EndpointForDescribeBasicEndpointGroupOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._edge_node_name = None
        self._endpoint_address = None
        self._endpoint_id = None
        self._type = None
        self.discriminator = None

        if edge_node_name is not None:
            self.edge_node_name = edge_node_name
        if endpoint_address is not None:
            self.endpoint_address = endpoint_address
        if endpoint_id is not None:
            self.endpoint_id = endpoint_id
        if type is not None:
            self.type = type

    @property
    def edge_node_name(self):
        """Gets the edge_node_name of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501


        :return: The edge_node_name of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501
        :rtype: str
        """
        return self._edge_node_name

    @edge_node_name.setter
    def edge_node_name(self, edge_node_name):
        """Sets the edge_node_name of this EndpointForDescribeBasicEndpointGroupOutput.


        :param edge_node_name: The edge_node_name of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501
        :type: str
        """

        self._edge_node_name = edge_node_name

    @property
    def endpoint_address(self):
        """Gets the endpoint_address of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501


        :return: The endpoint_address of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_address

    @endpoint_address.setter
    def endpoint_address(self, endpoint_address):
        """Sets the endpoint_address of this EndpointForDescribeBasicEndpointGroupOutput.


        :param endpoint_address: The endpoint_address of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_address = endpoint_address

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501


        :return: The endpoint_id of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this EndpointForDescribeBasicEndpointGroupOutput.


        :param endpoint_id: The endpoint_id of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_id = endpoint_id

    @property
    def type(self):
        """Gets the type of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501


        :return: The type of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this EndpointForDescribeBasicEndpointGroupOutput.


        :param type: The type of this EndpointForDescribeBasicEndpointGroupOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EndpointForDescribeBasicEndpointGroupOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EndpointForDescribeBasicEndpointGroupOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EndpointForDescribeBasicEndpointGroupOutput):
            return True

        return self.to_dict() != other.to_dict()
