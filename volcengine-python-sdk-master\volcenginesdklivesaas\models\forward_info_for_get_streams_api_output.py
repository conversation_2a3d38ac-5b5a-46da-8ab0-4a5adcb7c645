# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ForwardInfoForGetStreamsAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'pull_stream_check_status': 'str',
        'pull_stream_status': 'str',
        'pull_stream_url': 'str'
    }

    attribute_map = {
        'pull_stream_check_status': 'PullStreamCheckStatus',
        'pull_stream_status': 'PullStreamStatus',
        'pull_stream_url': 'PullStreamUrl'
    }

    def __init__(self, pull_stream_check_status=None, pull_stream_status=None, pull_stream_url=None, _configuration=None):  # noqa: E501
        """ForwardInfoForGetStreamsAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._pull_stream_check_status = None
        self._pull_stream_status = None
        self._pull_stream_url = None
        self.discriminator = None

        if pull_stream_check_status is not None:
            self.pull_stream_check_status = pull_stream_check_status
        if pull_stream_status is not None:
            self.pull_stream_status = pull_stream_status
        if pull_stream_url is not None:
            self.pull_stream_url = pull_stream_url

    @property
    def pull_stream_check_status(self):
        """Gets the pull_stream_check_status of this ForwardInfoForGetStreamsAPIOutput.  # noqa: E501


        :return: The pull_stream_check_status of this ForwardInfoForGetStreamsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._pull_stream_check_status

    @pull_stream_check_status.setter
    def pull_stream_check_status(self, pull_stream_check_status):
        """Sets the pull_stream_check_status of this ForwardInfoForGetStreamsAPIOutput.


        :param pull_stream_check_status: The pull_stream_check_status of this ForwardInfoForGetStreamsAPIOutput.  # noqa: E501
        :type: str
        """

        self._pull_stream_check_status = pull_stream_check_status

    @property
    def pull_stream_status(self):
        """Gets the pull_stream_status of this ForwardInfoForGetStreamsAPIOutput.  # noqa: E501


        :return: The pull_stream_status of this ForwardInfoForGetStreamsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._pull_stream_status

    @pull_stream_status.setter
    def pull_stream_status(self, pull_stream_status):
        """Sets the pull_stream_status of this ForwardInfoForGetStreamsAPIOutput.


        :param pull_stream_status: The pull_stream_status of this ForwardInfoForGetStreamsAPIOutput.  # noqa: E501
        :type: str
        """

        self._pull_stream_status = pull_stream_status

    @property
    def pull_stream_url(self):
        """Gets the pull_stream_url of this ForwardInfoForGetStreamsAPIOutput.  # noqa: E501


        :return: The pull_stream_url of this ForwardInfoForGetStreamsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._pull_stream_url

    @pull_stream_url.setter
    def pull_stream_url(self, pull_stream_url):
        """Sets the pull_stream_url of this ForwardInfoForGetStreamsAPIOutput.


        :param pull_stream_url: The pull_stream_url of this ForwardInfoForGetStreamsAPIOutput.  # noqa: E501
        :type: str
        """

        self._pull_stream_url = pull_stream_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ForwardInfoForGetStreamsAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ForwardInfoForGetStreamsAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ForwardInfoForGetStreamsAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
