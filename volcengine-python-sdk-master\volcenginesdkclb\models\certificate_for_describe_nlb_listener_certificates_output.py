# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CertificateForDescribeNLBListenerCertificatesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate_id': 'str',
        'certificate_type': 'str',
        'domain': 'str',
        'is_default': 'bool',
        'status': 'str'
    }

    attribute_map = {
        'certificate_id': 'CertificateId',
        'certificate_type': 'CertificateType',
        'domain': 'Domain',
        'is_default': 'IsDefault',
        'status': 'Status'
    }

    def __init__(self, certificate_id=None, certificate_type=None, domain=None, is_default=None, status=None, _configuration=None):  # noqa: E501
        """CertificateForDescribeNLBListenerCertificatesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate_id = None
        self._certificate_type = None
        self._domain = None
        self._is_default = None
        self._status = None
        self.discriminator = None

        if certificate_id is not None:
            self.certificate_id = certificate_id
        if certificate_type is not None:
            self.certificate_type = certificate_type
        if domain is not None:
            self.domain = domain
        if is_default is not None:
            self.is_default = is_default
        if status is not None:
            self.status = status

    @property
    def certificate_id(self):
        """Gets the certificate_id of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501


        :return: The certificate_id of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._certificate_id

    @certificate_id.setter
    def certificate_id(self, certificate_id):
        """Sets the certificate_id of this CertificateForDescribeNLBListenerCertificatesOutput.


        :param certificate_id: The certificate_id of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._certificate_id = certificate_id

    @property
    def certificate_type(self):
        """Gets the certificate_type of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501


        :return: The certificate_type of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._certificate_type

    @certificate_type.setter
    def certificate_type(self, certificate_type):
        """Sets the certificate_type of this CertificateForDescribeNLBListenerCertificatesOutput.


        :param certificate_type: The certificate_type of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._certificate_type = certificate_type

    @property
    def domain(self):
        """Gets the domain of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501


        :return: The domain of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this CertificateForDescribeNLBListenerCertificatesOutput.


        :param domain: The domain of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._domain = domain

    @property
    def is_default(self):
        """Gets the is_default of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501


        :return: The is_default of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_default

    @is_default.setter
    def is_default(self, is_default):
        """Sets the is_default of this CertificateForDescribeNLBListenerCertificatesOutput.


        :param is_default: The is_default of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :type: bool
        """

        self._is_default = is_default

    @property
    def status(self):
        """Gets the status of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501


        :return: The status of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CertificateForDescribeNLBListenerCertificatesOutput.


        :param status: The status of this CertificateForDescribeNLBListenerCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CertificateForDescribeNLBListenerCertificatesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CertificateForDescribeNLBListenerCertificatesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CertificateForDescribeNLBListenerCertificatesOutput):
            return True

        return self.to_dict() != other.to_dict()
