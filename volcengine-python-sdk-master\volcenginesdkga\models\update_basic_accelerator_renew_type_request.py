# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateBasicAcceleratorRenewTypeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'renew_type': 'int'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'renew_type': 'RenewType'
    }

    def __init__(self, accelerator_id=None, renew_type=None, _configuration=None):  # noqa: E501
        """UpdateBasicAcceleratorRenewTypeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._renew_type = None
        self.discriminator = None

        self.accelerator_id = accelerator_id
        if renew_type is not None:
            self.renew_type = renew_type

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this UpdateBasicAcceleratorRenewTypeRequest.  # noqa: E501


        :return: The accelerator_id of this UpdateBasicAcceleratorRenewTypeRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this UpdateBasicAcceleratorRenewTypeRequest.


        :param accelerator_id: The accelerator_id of this UpdateBasicAcceleratorRenewTypeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and accelerator_id is None:
            raise ValueError("Invalid value for `accelerator_id`, must not be `None`")  # noqa: E501

        self._accelerator_id = accelerator_id

    @property
    def renew_type(self):
        """Gets the renew_type of this UpdateBasicAcceleratorRenewTypeRequest.  # noqa: E501


        :return: The renew_type of this UpdateBasicAcceleratorRenewTypeRequest.  # noqa: E501
        :rtype: int
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this UpdateBasicAcceleratorRenewTypeRequest.


        :param renew_type: The renew_type of this UpdateBasicAcceleratorRenewTypeRequest.  # noqa: E501
        :type: int
        """

        self._renew_type = renew_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateBasicAcceleratorRenewTypeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateBasicAcceleratorRenewTypeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateBasicAcceleratorRenewTypeRequest):
            return True

        return self.to_dict() != other.to_dict()
