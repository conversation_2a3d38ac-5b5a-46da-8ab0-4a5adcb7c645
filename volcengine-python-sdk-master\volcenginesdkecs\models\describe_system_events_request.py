# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSystemEventsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'category': 'str',
        'created_at_end': 'str',
        'created_at_start': 'str',
        'event_ids': 'list[str]',
        'max_results': 'str',
        'next_token': 'str',
        'resource_ids': 'list[str]',
        'status': 'list[str]',
        'types': 'list[str]'
    }

    attribute_map = {
        'category': 'Category',
        'created_at_end': 'CreatedAtEnd',
        'created_at_start': 'CreatedAtStart',
        'event_ids': 'EventIds',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'resource_ids': 'ResourceIds',
        'status': 'Status',
        'types': 'Types'
    }

    def __init__(self, category=None, created_at_end=None, created_at_start=None, event_ids=None, max_results=None, next_token=None, resource_ids=None, status=None, types=None, _configuration=None):  # noqa: E501
        """DescribeSystemEventsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._category = None
        self._created_at_end = None
        self._created_at_start = None
        self._event_ids = None
        self._max_results = None
        self._next_token = None
        self._resource_ids = None
        self._status = None
        self._types = None
        self.discriminator = None

        if category is not None:
            self.category = category
        if created_at_end is not None:
            self.created_at_end = created_at_end
        if created_at_start is not None:
            self.created_at_start = created_at_start
        if event_ids is not None:
            self.event_ids = event_ids
        if max_results is not None:
            self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if resource_ids is not None:
            self.resource_ids = resource_ids
        if status is not None:
            self.status = status
        if types is not None:
            self.types = types

    @property
    def category(self):
        """Gets the category of this DescribeSystemEventsRequest.  # noqa: E501


        :return: The category of this DescribeSystemEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._category

    @category.setter
    def category(self, category):
        """Sets the category of this DescribeSystemEventsRequest.


        :param category: The category of this DescribeSystemEventsRequest.  # noqa: E501
        :type: str
        """

        self._category = category

    @property
    def created_at_end(self):
        """Gets the created_at_end of this DescribeSystemEventsRequest.  # noqa: E501


        :return: The created_at_end of this DescribeSystemEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._created_at_end

    @created_at_end.setter
    def created_at_end(self, created_at_end):
        """Sets the created_at_end of this DescribeSystemEventsRequest.


        :param created_at_end: The created_at_end of this DescribeSystemEventsRequest.  # noqa: E501
        :type: str
        """

        self._created_at_end = created_at_end

    @property
    def created_at_start(self):
        """Gets the created_at_start of this DescribeSystemEventsRequest.  # noqa: E501


        :return: The created_at_start of this DescribeSystemEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._created_at_start

    @created_at_start.setter
    def created_at_start(self, created_at_start):
        """Sets the created_at_start of this DescribeSystemEventsRequest.


        :param created_at_start: The created_at_start of this DescribeSystemEventsRequest.  # noqa: E501
        :type: str
        """

        self._created_at_start = created_at_start

    @property
    def event_ids(self):
        """Gets the event_ids of this DescribeSystemEventsRequest.  # noqa: E501


        :return: The event_ids of this DescribeSystemEventsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._event_ids

    @event_ids.setter
    def event_ids(self, event_ids):
        """Sets the event_ids of this DescribeSystemEventsRequest.


        :param event_ids: The event_ids of this DescribeSystemEventsRequest.  # noqa: E501
        :type: list[str]
        """

        self._event_ids = event_ids

    @property
    def max_results(self):
        """Gets the max_results of this DescribeSystemEventsRequest.  # noqa: E501


        :return: The max_results of this DescribeSystemEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeSystemEventsRequest.


        :param max_results: The max_results of this DescribeSystemEventsRequest.  # noqa: E501
        :type: str
        """

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this DescribeSystemEventsRequest.  # noqa: E501


        :return: The next_token of this DescribeSystemEventsRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeSystemEventsRequest.


        :param next_token: The next_token of this DescribeSystemEventsRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def resource_ids(self):
        """Gets the resource_ids of this DescribeSystemEventsRequest.  # noqa: E501


        :return: The resource_ids of this DescribeSystemEventsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._resource_ids

    @resource_ids.setter
    def resource_ids(self, resource_ids):
        """Sets the resource_ids of this DescribeSystemEventsRequest.


        :param resource_ids: The resource_ids of this DescribeSystemEventsRequest.  # noqa: E501
        :type: list[str]
        """

        self._resource_ids = resource_ids

    @property
    def status(self):
        """Gets the status of this DescribeSystemEventsRequest.  # noqa: E501


        :return: The status of this DescribeSystemEventsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeSystemEventsRequest.


        :param status: The status of this DescribeSystemEventsRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def types(self):
        """Gets the types of this DescribeSystemEventsRequest.  # noqa: E501


        :return: The types of this DescribeSystemEventsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._types

    @types.setter
    def types(self, types):
        """Sets the types of this DescribeSystemEventsRequest.


        :param types: The types of this DescribeSystemEventsRequest.  # noqa: E501
        :type: list[str]
        """

        self._types = types

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSystemEventsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSystemEventsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSystemEventsRequest):
            return True

        return self.to_dict() != other.to_dict()
