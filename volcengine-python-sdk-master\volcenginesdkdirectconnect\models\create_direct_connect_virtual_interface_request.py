# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDirectConnectVirtualInterfaceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'bfd_detect_interval': 'int',
        'bfd_detect_multiplier': 'int',
        'description': 'str',
        'direct_connect_connection_id': 'str',
        'direct_connect_gateway_id': 'str',
        'enable_bfd': 'bool',
        'enable_nqa': 'bool',
        'local_ip': 'str',
        'local_ipv6_ip': 'str',
        'nqa_detect_interval': 'int',
        'nqa_detect_multiplier': 'int',
        'peer_ip': 'str',
        'peer_ipv6_ip': 'str',
        'route_type': 'str',
        'tags': 'list[TagForCreateDirectConnectVirtualInterfaceInput]',
        'virtual_interface_name': 'str',
        'vlan_id': 'int'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'bfd_detect_interval': 'BfdDetectInterval',
        'bfd_detect_multiplier': 'BfdDetectMultiplier',
        'description': 'Description',
        'direct_connect_connection_id': 'DirectConnectConnectionId',
        'direct_connect_gateway_id': 'DirectConnectGatewayId',
        'enable_bfd': 'EnableBfd',
        'enable_nqa': 'EnableNqa',
        'local_ip': 'LocalIp',
        'local_ipv6_ip': 'LocalIpv6Ip',
        'nqa_detect_interval': 'NqaDetectInterval',
        'nqa_detect_multiplier': 'NqaDetectMultiplier',
        'peer_ip': 'PeerIp',
        'peer_ipv6_ip': 'PeerIpv6Ip',
        'route_type': 'RouteType',
        'tags': 'Tags',
        'virtual_interface_name': 'VirtualInterfaceName',
        'vlan_id': 'VlanId'
    }

    def __init__(self, bandwidth=None, bfd_detect_interval=None, bfd_detect_multiplier=None, description=None, direct_connect_connection_id=None, direct_connect_gateway_id=None, enable_bfd=None, enable_nqa=None, local_ip=None, local_ipv6_ip=None, nqa_detect_interval=None, nqa_detect_multiplier=None, peer_ip=None, peer_ipv6_ip=None, route_type=None, tags=None, virtual_interface_name=None, vlan_id=None, _configuration=None):  # noqa: E501
        """CreateDirectConnectVirtualInterfaceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._bfd_detect_interval = None
        self._bfd_detect_multiplier = None
        self._description = None
        self._direct_connect_connection_id = None
        self._direct_connect_gateway_id = None
        self._enable_bfd = None
        self._enable_nqa = None
        self._local_ip = None
        self._local_ipv6_ip = None
        self._nqa_detect_interval = None
        self._nqa_detect_multiplier = None
        self._peer_ip = None
        self._peer_ipv6_ip = None
        self._route_type = None
        self._tags = None
        self._virtual_interface_name = None
        self._vlan_id = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if bfd_detect_interval is not None:
            self.bfd_detect_interval = bfd_detect_interval
        if bfd_detect_multiplier is not None:
            self.bfd_detect_multiplier = bfd_detect_multiplier
        if description is not None:
            self.description = description
        self.direct_connect_connection_id = direct_connect_connection_id
        self.direct_connect_gateway_id = direct_connect_gateway_id
        if enable_bfd is not None:
            self.enable_bfd = enable_bfd
        if enable_nqa is not None:
            self.enable_nqa = enable_nqa
        self.local_ip = local_ip
        if local_ipv6_ip is not None:
            self.local_ipv6_ip = local_ipv6_ip
        if nqa_detect_interval is not None:
            self.nqa_detect_interval = nqa_detect_interval
        if nqa_detect_multiplier is not None:
            self.nqa_detect_multiplier = nqa_detect_multiplier
        self.peer_ip = peer_ip
        if peer_ipv6_ip is not None:
            self.peer_ipv6_ip = peer_ipv6_ip
        if route_type is not None:
            self.route_type = route_type
        if tags is not None:
            self.tags = tags
        if virtual_interface_name is not None:
            self.virtual_interface_name = virtual_interface_name
        self.vlan_id = vlan_id

    @property
    def bandwidth(self):
        """Gets the bandwidth of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The bandwidth of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this CreateDirectConnectVirtualInterfaceRequest.


        :param bandwidth: The bandwidth of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def bfd_detect_interval(self):
        """Gets the bfd_detect_interval of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The bfd_detect_interval of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._bfd_detect_interval

    @bfd_detect_interval.setter
    def bfd_detect_interval(self, bfd_detect_interval):
        """Sets the bfd_detect_interval of this CreateDirectConnectVirtualInterfaceRequest.


        :param bfd_detect_interval: The bfd_detect_interval of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                bfd_detect_interval is not None and bfd_detect_interval > 1000):  # noqa: E501
            raise ValueError("Invalid value for `bfd_detect_interval`, must be a value less than or equal to `1000`")  # noqa: E501
        if (self._configuration.client_side_validation and
                bfd_detect_interval is not None and bfd_detect_interval < 200):  # noqa: E501
            raise ValueError("Invalid value for `bfd_detect_interval`, must be a value greater than or equal to `200`")  # noqa: E501

        self._bfd_detect_interval = bfd_detect_interval

    @property
    def bfd_detect_multiplier(self):
        """Gets the bfd_detect_multiplier of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The bfd_detect_multiplier of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._bfd_detect_multiplier

    @bfd_detect_multiplier.setter
    def bfd_detect_multiplier(self, bfd_detect_multiplier):
        """Sets the bfd_detect_multiplier of this CreateDirectConnectVirtualInterfaceRequest.


        :param bfd_detect_multiplier: The bfd_detect_multiplier of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                bfd_detect_multiplier is not None and bfd_detect_multiplier > 10):  # noqa: E501
            raise ValueError("Invalid value for `bfd_detect_multiplier`, must be a value less than or equal to `10`")  # noqa: E501
        if (self._configuration.client_side_validation and
                bfd_detect_multiplier is not None and bfd_detect_multiplier < 3):  # noqa: E501
            raise ValueError("Invalid value for `bfd_detect_multiplier`, must be a value greater than or equal to `3`")  # noqa: E501

        self._bfd_detect_multiplier = bfd_detect_multiplier

    @property
    def description(self):
        """Gets the description of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The description of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateDirectConnectVirtualInterfaceRequest.


        :param description: The description of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def direct_connect_connection_id(self):
        """Gets the direct_connect_connection_id of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The direct_connect_connection_id of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_connection_id

    @direct_connect_connection_id.setter
    def direct_connect_connection_id(self, direct_connect_connection_id):
        """Sets the direct_connect_connection_id of this CreateDirectConnectVirtualInterfaceRequest.


        :param direct_connect_connection_id: The direct_connect_connection_id of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and direct_connect_connection_id is None:
            raise ValueError("Invalid value for `direct_connect_connection_id`, must not be `None`")  # noqa: E501

        self._direct_connect_connection_id = direct_connect_connection_id

    @property
    def direct_connect_gateway_id(self):
        """Gets the direct_connect_gateway_id of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The direct_connect_gateway_id of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_gateway_id

    @direct_connect_gateway_id.setter
    def direct_connect_gateway_id(self, direct_connect_gateway_id):
        """Sets the direct_connect_gateway_id of this CreateDirectConnectVirtualInterfaceRequest.


        :param direct_connect_gateway_id: The direct_connect_gateway_id of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and direct_connect_gateway_id is None:
            raise ValueError("Invalid value for `direct_connect_gateway_id`, must not be `None`")  # noqa: E501

        self._direct_connect_gateway_id = direct_connect_gateway_id

    @property
    def enable_bfd(self):
        """Gets the enable_bfd of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The enable_bfd of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_bfd

    @enable_bfd.setter
    def enable_bfd(self, enable_bfd):
        """Sets the enable_bfd of this CreateDirectConnectVirtualInterfaceRequest.


        :param enable_bfd: The enable_bfd of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: bool
        """

        self._enable_bfd = enable_bfd

    @property
    def enable_nqa(self):
        """Gets the enable_nqa of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The enable_nqa of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_nqa

    @enable_nqa.setter
    def enable_nqa(self, enable_nqa):
        """Sets the enable_nqa of this CreateDirectConnectVirtualInterfaceRequest.


        :param enable_nqa: The enable_nqa of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: bool
        """

        self._enable_nqa = enable_nqa

    @property
    def local_ip(self):
        """Gets the local_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The local_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._local_ip

    @local_ip.setter
    def local_ip(self, local_ip):
        """Sets the local_ip of this CreateDirectConnectVirtualInterfaceRequest.


        :param local_ip: The local_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and local_ip is None:
            raise ValueError("Invalid value for `local_ip`, must not be `None`")  # noqa: E501

        self._local_ip = local_ip

    @property
    def local_ipv6_ip(self):
        """Gets the local_ipv6_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The local_ipv6_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._local_ipv6_ip

    @local_ipv6_ip.setter
    def local_ipv6_ip(self, local_ipv6_ip):
        """Sets the local_ipv6_ip of this CreateDirectConnectVirtualInterfaceRequest.


        :param local_ipv6_ip: The local_ipv6_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._local_ipv6_ip = local_ipv6_ip

    @property
    def nqa_detect_interval(self):
        """Gets the nqa_detect_interval of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The nqa_detect_interval of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._nqa_detect_interval

    @nqa_detect_interval.setter
    def nqa_detect_interval(self, nqa_detect_interval):
        """Sets the nqa_detect_interval of this CreateDirectConnectVirtualInterfaceRequest.


        :param nqa_detect_interval: The nqa_detect_interval of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                nqa_detect_interval is not None and nqa_detect_interval > 5000):  # noqa: E501
            raise ValueError("Invalid value for `nqa_detect_interval`, must be a value less than or equal to `5000`")  # noqa: E501
        if (self._configuration.client_side_validation and
                nqa_detect_interval is not None and nqa_detect_interval < 1000):  # noqa: E501
            raise ValueError("Invalid value for `nqa_detect_interval`, must be a value greater than or equal to `1000`")  # noqa: E501

        self._nqa_detect_interval = nqa_detect_interval

    @property
    def nqa_detect_multiplier(self):
        """Gets the nqa_detect_multiplier of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The nqa_detect_multiplier of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._nqa_detect_multiplier

    @nqa_detect_multiplier.setter
    def nqa_detect_multiplier(self, nqa_detect_multiplier):
        """Sets the nqa_detect_multiplier of this CreateDirectConnectVirtualInterfaceRequest.


        :param nqa_detect_multiplier: The nqa_detect_multiplier of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                nqa_detect_multiplier is not None and nqa_detect_multiplier > 8):  # noqa: E501
            raise ValueError("Invalid value for `nqa_detect_multiplier`, must be a value less than or equal to `8`")  # noqa: E501
        if (self._configuration.client_side_validation and
                nqa_detect_multiplier is not None and nqa_detect_multiplier < 3):  # noqa: E501
            raise ValueError("Invalid value for `nqa_detect_multiplier`, must be a value greater than or equal to `3`")  # noqa: E501

        self._nqa_detect_multiplier = nqa_detect_multiplier

    @property
    def peer_ip(self):
        """Gets the peer_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The peer_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._peer_ip

    @peer_ip.setter
    def peer_ip(self, peer_ip):
        """Sets the peer_ip of this CreateDirectConnectVirtualInterfaceRequest.


        :param peer_ip: The peer_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and peer_ip is None:
            raise ValueError("Invalid value for `peer_ip`, must not be `None`")  # noqa: E501

        self._peer_ip = peer_ip

    @property
    def peer_ipv6_ip(self):
        """Gets the peer_ipv6_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The peer_ipv6_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._peer_ipv6_ip

    @peer_ipv6_ip.setter
    def peer_ipv6_ip(self, peer_ipv6_ip):
        """Sets the peer_ipv6_ip of this CreateDirectConnectVirtualInterfaceRequest.


        :param peer_ipv6_ip: The peer_ipv6_ip of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """

        self._peer_ipv6_ip = peer_ipv6_ip

    @property
    def route_type(self):
        """Gets the route_type of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The route_type of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._route_type

    @route_type.setter
    def route_type(self, route_type):
        """Sets the route_type of this CreateDirectConnectVirtualInterfaceRequest.


        :param route_type: The route_type of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Static", "BGP"]  # noqa: E501
        if (self._configuration.client_side_validation and
                route_type not in allowed_values):
            raise ValueError(
                "Invalid value for `route_type` ({0}), must be one of {1}"  # noqa: E501
                .format(route_type, allowed_values)
            )

        self._route_type = route_type

    @property
    def tags(self):
        """Gets the tags of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The tags of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: list[TagForCreateDirectConnectVirtualInterfaceInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateDirectConnectVirtualInterfaceRequest.


        :param tags: The tags of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: list[TagForCreateDirectConnectVirtualInterfaceInput]
        """

        self._tags = tags

    @property
    def virtual_interface_name(self):
        """Gets the virtual_interface_name of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The virtual_interface_name of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._virtual_interface_name

    @virtual_interface_name.setter
    def virtual_interface_name(self, virtual_interface_name):
        """Sets the virtual_interface_name of this CreateDirectConnectVirtualInterfaceRequest.


        :param virtual_interface_name: The virtual_interface_name of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                virtual_interface_name is not None and len(virtual_interface_name) > 128):
            raise ValueError("Invalid value for `virtual_interface_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                virtual_interface_name is not None and len(virtual_interface_name) < 1):
            raise ValueError("Invalid value for `virtual_interface_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._virtual_interface_name = virtual_interface_name

    @property
    def vlan_id(self):
        """Gets the vlan_id of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501


        :return: The vlan_id of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :rtype: int
        """
        return self._vlan_id

    @vlan_id.setter
    def vlan_id(self, vlan_id):
        """Sets the vlan_id of this CreateDirectConnectVirtualInterfaceRequest.


        :param vlan_id: The vlan_id of this CreateDirectConnectVirtualInterfaceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and vlan_id is None:
            raise ValueError("Invalid value for `vlan_id`, must not be `None`")  # noqa: E501

        self._vlan_id = vlan_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDirectConnectVirtualInterfaceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDirectConnectVirtualInterfaceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDirectConnectVirtualInterfaceRequest):
            return True

        return self.to_dict() != other.to_dict()
