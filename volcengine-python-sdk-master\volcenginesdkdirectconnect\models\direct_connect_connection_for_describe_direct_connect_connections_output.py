# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DirectConnectConnectionForDescribeDirectConnectConnectionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'bandwidth': 'int',
        'billing_type': 'int',
        'business_status': 'str',
        'connection_type': 'str',
        'creation_time': 'str',
        'customer_contact_email': 'str',
        'customer_contact_phone': 'str',
        'customer_name': 'str',
        'deleted_time': 'str',
        'description': 'str',
        'direct_connect_access_point_id': 'str',
        'direct_connect_connection_id': 'str',
        'direct_connect_connection_name': 'str',
        'expect_bandwidth': 'int',
        'expired_time': 'str',
        'line_operator': 'str',
        'parent_connection_account_id': 'str',
        'parent_connection_id': 'str',
        'peer_location': 'str',
        'port_spec': 'str',
        'port_type': 'str',
        'project_name': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeDirectConnectConnectionsOutput]',
        'update_time': 'str',
        'vlan_id': 'int'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'bandwidth': 'Bandwidth',
        'billing_type': 'BillingType',
        'business_status': 'BusinessStatus',
        'connection_type': 'ConnectionType',
        'creation_time': 'CreationTime',
        'customer_contact_email': 'CustomerContactEmail',
        'customer_contact_phone': 'CustomerContactPhone',
        'customer_name': 'CustomerName',
        'deleted_time': 'DeletedTime',
        'description': 'Description',
        'direct_connect_access_point_id': 'DirectConnectAccessPointId',
        'direct_connect_connection_id': 'DirectConnectConnectionId',
        'direct_connect_connection_name': 'DirectConnectConnectionName',
        'expect_bandwidth': 'ExpectBandwidth',
        'expired_time': 'ExpiredTime',
        'line_operator': 'LineOperator',
        'parent_connection_account_id': 'ParentConnectionAccountId',
        'parent_connection_id': 'ParentConnectionId',
        'peer_location': 'PeerLocation',
        'port_spec': 'PortSpec',
        'port_type': 'PortType',
        'project_name': 'ProjectName',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime',
        'vlan_id': 'VlanId'
    }

    def __init__(self, account_id=None, bandwidth=None, billing_type=None, business_status=None, connection_type=None, creation_time=None, customer_contact_email=None, customer_contact_phone=None, customer_name=None, deleted_time=None, description=None, direct_connect_access_point_id=None, direct_connect_connection_id=None, direct_connect_connection_name=None, expect_bandwidth=None, expired_time=None, line_operator=None, parent_connection_account_id=None, parent_connection_id=None, peer_location=None, port_spec=None, port_type=None, project_name=None, status=None, tags=None, update_time=None, vlan_id=None, _configuration=None):  # noqa: E501
        """DirectConnectConnectionForDescribeDirectConnectConnectionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._bandwidth = None
        self._billing_type = None
        self._business_status = None
        self._connection_type = None
        self._creation_time = None
        self._customer_contact_email = None
        self._customer_contact_phone = None
        self._customer_name = None
        self._deleted_time = None
        self._description = None
        self._direct_connect_access_point_id = None
        self._direct_connect_connection_id = None
        self._direct_connect_connection_name = None
        self._expect_bandwidth = None
        self._expired_time = None
        self._line_operator = None
        self._parent_connection_account_id = None
        self._parent_connection_id = None
        self._peer_location = None
        self._port_spec = None
        self._port_type = None
        self._project_name = None
        self._status = None
        self._tags = None
        self._update_time = None
        self._vlan_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if bandwidth is not None:
            self.bandwidth = bandwidth
        if billing_type is not None:
            self.billing_type = billing_type
        if business_status is not None:
            self.business_status = business_status
        if connection_type is not None:
            self.connection_type = connection_type
        if creation_time is not None:
            self.creation_time = creation_time
        if customer_contact_email is not None:
            self.customer_contact_email = customer_contact_email
        if customer_contact_phone is not None:
            self.customer_contact_phone = customer_contact_phone
        if customer_name is not None:
            self.customer_name = customer_name
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if description is not None:
            self.description = description
        if direct_connect_access_point_id is not None:
            self.direct_connect_access_point_id = direct_connect_access_point_id
        if direct_connect_connection_id is not None:
            self.direct_connect_connection_id = direct_connect_connection_id
        if direct_connect_connection_name is not None:
            self.direct_connect_connection_name = direct_connect_connection_name
        if expect_bandwidth is not None:
            self.expect_bandwidth = expect_bandwidth
        if expired_time is not None:
            self.expired_time = expired_time
        if line_operator is not None:
            self.line_operator = line_operator
        if parent_connection_account_id is not None:
            self.parent_connection_account_id = parent_connection_account_id
        if parent_connection_id is not None:
            self.parent_connection_id = parent_connection_id
        if peer_location is not None:
            self.peer_location = peer_location
        if port_spec is not None:
            self.port_spec = port_spec
        if port_type is not None:
            self.port_type = port_type
        if project_name is not None:
            self.project_name = project_name
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time
        if vlan_id is not None:
            self.vlan_id = vlan_id

    @property
    def account_id(self):
        """Gets the account_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The account_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param account_id: The account_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def bandwidth(self):
        """Gets the bandwidth of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The bandwidth of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param bandwidth: The bandwidth of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def billing_type(self):
        """Gets the billing_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The billing_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param billing_type: The billing_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: int
        """

        self._billing_type = billing_type

    @property
    def business_status(self):
        """Gets the business_status of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The business_status of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_status

    @business_status.setter
    def business_status(self, business_status):
        """Sets the business_status of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param business_status: The business_status of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._business_status = business_status

    @property
    def connection_type(self):
        """Gets the connection_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The connection_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._connection_type

    @connection_type.setter
    def connection_type(self, connection_type):
        """Sets the connection_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param connection_type: The connection_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._connection_type = connection_type

    @property
    def creation_time(self):
        """Gets the creation_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The creation_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param creation_time: The creation_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def customer_contact_email(self):
        """Gets the customer_contact_email of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The customer_contact_email of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._customer_contact_email

    @customer_contact_email.setter
    def customer_contact_email(self, customer_contact_email):
        """Sets the customer_contact_email of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param customer_contact_email: The customer_contact_email of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._customer_contact_email = customer_contact_email

    @property
    def customer_contact_phone(self):
        """Gets the customer_contact_phone of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The customer_contact_phone of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._customer_contact_phone

    @customer_contact_phone.setter
    def customer_contact_phone(self, customer_contact_phone):
        """Sets the customer_contact_phone of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param customer_contact_phone: The customer_contact_phone of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._customer_contact_phone = customer_contact_phone

    @property
    def customer_name(self):
        """Gets the customer_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The customer_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._customer_name

    @customer_name.setter
    def customer_name(self, customer_name):
        """Sets the customer_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param customer_name: The customer_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._customer_name = customer_name

    @property
    def deleted_time(self):
        """Gets the deleted_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The deleted_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param deleted_time: The deleted_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def description(self):
        """Gets the description of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The description of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param description: The description of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def direct_connect_access_point_id(self):
        """Gets the direct_connect_access_point_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The direct_connect_access_point_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_access_point_id

    @direct_connect_access_point_id.setter
    def direct_connect_access_point_id(self, direct_connect_access_point_id):
        """Sets the direct_connect_access_point_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param direct_connect_access_point_id: The direct_connect_access_point_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._direct_connect_access_point_id = direct_connect_access_point_id

    @property
    def direct_connect_connection_id(self):
        """Gets the direct_connect_connection_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The direct_connect_connection_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_connection_id

    @direct_connect_connection_id.setter
    def direct_connect_connection_id(self, direct_connect_connection_id):
        """Sets the direct_connect_connection_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param direct_connect_connection_id: The direct_connect_connection_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._direct_connect_connection_id = direct_connect_connection_id

    @property
    def direct_connect_connection_name(self):
        """Gets the direct_connect_connection_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The direct_connect_connection_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_connection_name

    @direct_connect_connection_name.setter
    def direct_connect_connection_name(self, direct_connect_connection_name):
        """Sets the direct_connect_connection_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param direct_connect_connection_name: The direct_connect_connection_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._direct_connect_connection_name = direct_connect_connection_name

    @property
    def expect_bandwidth(self):
        """Gets the expect_bandwidth of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The expect_bandwidth of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._expect_bandwidth

    @expect_bandwidth.setter
    def expect_bandwidth(self, expect_bandwidth):
        """Sets the expect_bandwidth of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param expect_bandwidth: The expect_bandwidth of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: int
        """

        self._expect_bandwidth = expect_bandwidth

    @property
    def expired_time(self):
        """Gets the expired_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The expired_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param expired_time: The expired_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._expired_time = expired_time

    @property
    def line_operator(self):
        """Gets the line_operator of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The line_operator of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._line_operator

    @line_operator.setter
    def line_operator(self, line_operator):
        """Sets the line_operator of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param line_operator: The line_operator of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._line_operator = line_operator

    @property
    def parent_connection_account_id(self):
        """Gets the parent_connection_account_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The parent_connection_account_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._parent_connection_account_id

    @parent_connection_account_id.setter
    def parent_connection_account_id(self, parent_connection_account_id):
        """Sets the parent_connection_account_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param parent_connection_account_id: The parent_connection_account_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._parent_connection_account_id = parent_connection_account_id

    @property
    def parent_connection_id(self):
        """Gets the parent_connection_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The parent_connection_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._parent_connection_id

    @parent_connection_id.setter
    def parent_connection_id(self, parent_connection_id):
        """Sets the parent_connection_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param parent_connection_id: The parent_connection_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._parent_connection_id = parent_connection_id

    @property
    def peer_location(self):
        """Gets the peer_location of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The peer_location of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._peer_location

    @peer_location.setter
    def peer_location(self, peer_location):
        """Sets the peer_location of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param peer_location: The peer_location of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._peer_location = peer_location

    @property
    def port_spec(self):
        """Gets the port_spec of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The port_spec of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._port_spec

    @port_spec.setter
    def port_spec(self, port_spec):
        """Sets the port_spec of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param port_spec: The port_spec of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._port_spec = port_spec

    @property
    def port_type(self):
        """Gets the port_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The port_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._port_type

    @port_type.setter
    def port_type(self, port_type):
        """Sets the port_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param port_type: The port_type of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._port_type = port_type

    @property
    def project_name(self):
        """Gets the project_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The project_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param project_name: The project_name of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def status(self):
        """Gets the status of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The status of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param status: The status of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The tags of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: list[TagForDescribeDirectConnectConnectionsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param tags: The tags of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: list[TagForDescribeDirectConnectConnectionsOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The update_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param update_time: The update_time of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vlan_id(self):
        """Gets the vlan_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501


        :return: The vlan_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._vlan_id

    @vlan_id.setter
    def vlan_id(self, vlan_id):
        """Sets the vlan_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.


        :param vlan_id: The vlan_id of this DirectConnectConnectionForDescribeDirectConnectConnectionsOutput.  # noqa: E501
        :type: int
        """

        self._vlan_id = vlan_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DirectConnectConnectionForDescribeDirectConnectConnectionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DirectConnectConnectionForDescribeDirectConnectConnectionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DirectConnectConnectionForDescribeDirectConnectConnectionsOutput):
            return True

        return self.to_dict() != other.to_dict()
