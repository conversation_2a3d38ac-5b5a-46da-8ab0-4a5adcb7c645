# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeHpcClustersRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'hpc_cluster_ids': 'list[str]',
        'max_results': 'int',
        'name': 'str',
        'next_token': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'hpc_cluster_ids': 'HpcClusterIds',
        'max_results': 'MaxResults',
        'name': 'Name',
        'next_token': 'NextToken',
        'zone_id': 'ZoneId'
    }

    def __init__(self, client_token=None, hpc_cluster_ids=None, max_results=None, name=None, next_token=None, zone_id=None, _configuration=None):  # noqa: E501
        """DescribeHpcClustersRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._hpc_cluster_ids = None
        self._max_results = None
        self._name = None
        self._next_token = None
        self._zone_id = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if hpc_cluster_ids is not None:
            self.hpc_cluster_ids = hpc_cluster_ids
        if max_results is not None:
            self.max_results = max_results
        if name is not None:
            self.name = name
        if next_token is not None:
            self.next_token = next_token
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def client_token(self):
        """Gets the client_token of this DescribeHpcClustersRequest.  # noqa: E501


        :return: The client_token of this DescribeHpcClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this DescribeHpcClustersRequest.


        :param client_token: The client_token of this DescribeHpcClustersRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def hpc_cluster_ids(self):
        """Gets the hpc_cluster_ids of this DescribeHpcClustersRequest.  # noqa: E501


        :return: The hpc_cluster_ids of this DescribeHpcClustersRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._hpc_cluster_ids

    @hpc_cluster_ids.setter
    def hpc_cluster_ids(self, hpc_cluster_ids):
        """Sets the hpc_cluster_ids of this DescribeHpcClustersRequest.


        :param hpc_cluster_ids: The hpc_cluster_ids of this DescribeHpcClustersRequest.  # noqa: E501
        :type: list[str]
        """

        self._hpc_cluster_ids = hpc_cluster_ids

    @property
    def max_results(self):
        """Gets the max_results of this DescribeHpcClustersRequest.  # noqa: E501


        :return: The max_results of this DescribeHpcClustersRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this DescribeHpcClustersRequest.


        :param max_results: The max_results of this DescribeHpcClustersRequest.  # noqa: E501
        :type: int
        """

        self._max_results = max_results

    @property
    def name(self):
        """Gets the name of this DescribeHpcClustersRequest.  # noqa: E501


        :return: The name of this DescribeHpcClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DescribeHpcClustersRequest.


        :param name: The name of this DescribeHpcClustersRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def next_token(self):
        """Gets the next_token of this DescribeHpcClustersRequest.  # noqa: E501


        :return: The next_token of this DescribeHpcClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this DescribeHpcClustersRequest.


        :param next_token: The next_token of this DescribeHpcClustersRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def zone_id(self):
        """Gets the zone_id of this DescribeHpcClustersRequest.  # noqa: E501


        :return: The zone_id of this DescribeHpcClustersRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DescribeHpcClustersRequest.


        :param zone_id: The zone_id of this DescribeHpcClustersRequest.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeHpcClustersRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeHpcClustersRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeHpcClustersRequest):
            return True

        return self.to_dict() != other.to_dict()
