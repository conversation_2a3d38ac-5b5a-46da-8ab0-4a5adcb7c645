# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListSiteTagAPIV2Response(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'site_tags': 'list[SiteTagForListSiteTagAPIV2Output]',
        'text_site_tags': 'list[TextSiteTagForListSiteTagAPIV2Output]'
    }

    attribute_map = {
        'site_tags': 'SiteTags',
        'text_site_tags': 'TextSiteTags'
    }

    def __init__(self, site_tags=None, text_site_tags=None, _configuration=None):  # noqa: E501
        """ListSiteTagAPIV2Response - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._site_tags = None
        self._text_site_tags = None
        self.discriminator = None

        if site_tags is not None:
            self.site_tags = site_tags
        if text_site_tags is not None:
            self.text_site_tags = text_site_tags

    @property
    def site_tags(self):
        """Gets the site_tags of this ListSiteTagAPIV2Response.  # noqa: E501


        :return: The site_tags of this ListSiteTagAPIV2Response.  # noqa: E501
        :rtype: list[SiteTagForListSiteTagAPIV2Output]
        """
        return self._site_tags

    @site_tags.setter
    def site_tags(self, site_tags):
        """Sets the site_tags of this ListSiteTagAPIV2Response.


        :param site_tags: The site_tags of this ListSiteTagAPIV2Response.  # noqa: E501
        :type: list[SiteTagForListSiteTagAPIV2Output]
        """

        self._site_tags = site_tags

    @property
    def text_site_tags(self):
        """Gets the text_site_tags of this ListSiteTagAPIV2Response.  # noqa: E501


        :return: The text_site_tags of this ListSiteTagAPIV2Response.  # noqa: E501
        :rtype: list[TextSiteTagForListSiteTagAPIV2Output]
        """
        return self._text_site_tags

    @text_site_tags.setter
    def text_site_tags(self, text_site_tags):
        """Sets the text_site_tags of this ListSiteTagAPIV2Response.


        :param text_site_tags: The text_site_tags of this ListSiteTagAPIV2Response.  # noqa: E501
        :type: list[TextSiteTagForListSiteTagAPIV2Output]
        """

        self._text_site_tags = text_site_tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListSiteTagAPIV2Response, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListSiteTagAPIV2Response):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListSiteTagAPIV2Response):
            return True

        return self.to_dict() != other.to_dict()
