# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListRegistriesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'regions': 'list[str]',
        'volc_status': 'list[str]',
        'vpc_auth_status': 'list[int]'
    }

    attribute_map = {
        'name': 'Name',
        'regions': 'Regions',
        'volc_status': 'VolcStatus',
        'vpc_auth_status': 'VpcAuthStatus'
    }

    def __init__(self, name=None, regions=None, volc_status=None, vpc_auth_status=None, _configuration=None):  # noqa: E501
        """FilterForListRegistriesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._regions = None
        self._volc_status = None
        self._vpc_auth_status = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if regions is not None:
            self.regions = regions
        if volc_status is not None:
            self.volc_status = volc_status
        if vpc_auth_status is not None:
            self.vpc_auth_status = vpc_auth_status

    @property
    def name(self):
        """Gets the name of this FilterForListRegistriesInput.  # noqa: E501


        :return: The name of this FilterForListRegistriesInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListRegistriesInput.


        :param name: The name of this FilterForListRegistriesInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def regions(self):
        """Gets the regions of this FilterForListRegistriesInput.  # noqa: E501


        :return: The regions of this FilterForListRegistriesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._regions

    @regions.setter
    def regions(self, regions):
        """Sets the regions of this FilterForListRegistriesInput.


        :param regions: The regions of this FilterForListRegistriesInput.  # noqa: E501
        :type: list[str]
        """

        self._regions = regions

    @property
    def volc_status(self):
        """Gets the volc_status of this FilterForListRegistriesInput.  # noqa: E501


        :return: The volc_status of this FilterForListRegistriesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._volc_status

    @volc_status.setter
    def volc_status(self, volc_status):
        """Sets the volc_status of this FilterForListRegistriesInput.


        :param volc_status: The volc_status of this FilterForListRegistriesInput.  # noqa: E501
        :type: list[str]
        """

        self._volc_status = volc_status

    @property
    def vpc_auth_status(self):
        """Gets the vpc_auth_status of this FilterForListRegistriesInput.  # noqa: E501


        :return: The vpc_auth_status of this FilterForListRegistriesInput.  # noqa: E501
        :rtype: list[int]
        """
        return self._vpc_auth_status

    @vpc_auth_status.setter
    def vpc_auth_status(self, vpc_auth_status):
        """Sets the vpc_auth_status of this FilterForListRegistriesInput.


        :param vpc_auth_status: The vpc_auth_status of this FilterForListRegistriesInput.  # noqa: E501
        :type: list[int]
        """

        self._vpc_auth_status = vpc_auth_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListRegistriesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListRegistriesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListRegistriesInput):
            return True

        return self.to_dict() != other.to_dict()
