# coding: utf-8

"""
    tag

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceTypeForListResourceTypesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'resource_type': 'str',
        'resource_type_name': 'str',
        'service': 'str',
        'service_name': 'str'
    }

    attribute_map = {
        'resource_type': 'ResourceType',
        'resource_type_name': 'ResourceTypeName',
        'service': 'Service',
        'service_name': 'ServiceName'
    }

    def __init__(self, resource_type=None, resource_type_name=None, service=None, service_name=None, _configuration=None):  # noqa: E501
        """ResourceTypeForListResourceTypesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._resource_type = None
        self._resource_type_name = None
        self._service = None
        self._service_name = None
        self.discriminator = None

        if resource_type is not None:
            self.resource_type = resource_type
        if resource_type_name is not None:
            self.resource_type_name = resource_type_name
        if service is not None:
            self.service = service
        if service_name is not None:
            self.service_name = service_name

    @property
    def resource_type(self):
        """Gets the resource_type of this ResourceTypeForListResourceTypesOutput.  # noqa: E501


        :return: The resource_type of this ResourceTypeForListResourceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ResourceTypeForListResourceTypesOutput.


        :param resource_type: The resource_type of this ResourceTypeForListResourceTypesOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def resource_type_name(self):
        """Gets the resource_type_name of this ResourceTypeForListResourceTypesOutput.  # noqa: E501


        :return: The resource_type_name of this ResourceTypeForListResourceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type_name

    @resource_type_name.setter
    def resource_type_name(self, resource_type_name):
        """Sets the resource_type_name of this ResourceTypeForListResourceTypesOutput.


        :param resource_type_name: The resource_type_name of this ResourceTypeForListResourceTypesOutput.  # noqa: E501
        :type: str
        """

        self._resource_type_name = resource_type_name

    @property
    def service(self):
        """Gets the service of this ResourceTypeForListResourceTypesOutput.  # noqa: E501


        :return: The service of this ResourceTypeForListResourceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._service

    @service.setter
    def service(self, service):
        """Sets the service of this ResourceTypeForListResourceTypesOutput.


        :param service: The service of this ResourceTypeForListResourceTypesOutput.  # noqa: E501
        :type: str
        """

        self._service = service

    @property
    def service_name(self):
        """Gets the service_name of this ResourceTypeForListResourceTypesOutput.  # noqa: E501


        :return: The service_name of this ResourceTypeForListResourceTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._service_name

    @service_name.setter
    def service_name(self, service_name):
        """Sets the service_name of this ResourceTypeForListResourceTypesOutput.


        :param service_name: The service_name of this ResourceTypeForListResourceTypesOutput.  # noqa: E501
        :type: str
        """

        self._service_name = service_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceTypeForListResourceTypesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceTypeForListResourceTypesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceTypeForListResourceTypesOutput):
            return True

        return self.to_dict() != other.to_dict()
