# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConfigurationForGetMultiLevelInstitutionDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'institution_status': 'str',
        'report_content': 'list[str]',
        'report_period': 'int',
        'report_switch_open': 'bool'
    }

    attribute_map = {
        'institution_status': 'InstitutionStatus',
        'report_content': 'ReportContent',
        'report_period': 'ReportPeriod',
        'report_switch_open': 'ReportSwitchOpen'
    }

    def __init__(self, institution_status=None, report_content=None, report_period=None, report_switch_open=None, _configuration=None):  # noqa: E501
        """ConfigurationForGetMultiLevelInstitutionDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._institution_status = None
        self._report_content = None
        self._report_period = None
        self._report_switch_open = None
        self.discriminator = None

        if institution_status is not None:
            self.institution_status = institution_status
        if report_content is not None:
            self.report_content = report_content
        if report_period is not None:
            self.report_period = report_period
        if report_switch_open is not None:
            self.report_switch_open = report_switch_open

    @property
    def institution_status(self):
        """Gets the institution_status of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The institution_status of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._institution_status

    @institution_status.setter
    def institution_status(self, institution_status):
        """Sets the institution_status of this ConfigurationForGetMultiLevelInstitutionDetailOutput.


        :param institution_status: The institution_status of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: str
        """

        self._institution_status = institution_status

    @property
    def report_content(self):
        """Gets the report_content of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The report_content of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._report_content

    @report_content.setter
    def report_content(self, report_content):
        """Sets the report_content of this ConfigurationForGetMultiLevelInstitutionDetailOutput.


        :param report_content: The report_content of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: list[str]
        """

        self._report_content = report_content

    @property
    def report_period(self):
        """Gets the report_period of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The report_period of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._report_period

    @report_period.setter
    def report_period(self, report_period):
        """Sets the report_period of this ConfigurationForGetMultiLevelInstitutionDetailOutput.


        :param report_period: The report_period of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: int
        """

        self._report_period = report_period

    @property
    def report_switch_open(self):
        """Gets the report_switch_open of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501


        :return: The report_switch_open of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :rtype: bool
        """
        return self._report_switch_open

    @report_switch_open.setter
    def report_switch_open(self, report_switch_open):
        """Sets the report_switch_open of this ConfigurationForGetMultiLevelInstitutionDetailOutput.


        :param report_switch_open: The report_switch_open of this ConfigurationForGetMultiLevelInstitutionDetailOutput.  # noqa: E501
        :type: bool
        """

        self._report_switch_open = report_switch_open

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConfigurationForGetMultiLevelInstitutionDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConfigurationForGetMultiLevelInstitutionDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConfigurationForGetMultiLevelInstitutionDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
