# coding: utf-8

"""
    vepfs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OverViewForDescribeFileSystemOverviewOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'error_count': 'int',
        'other_count': 'int',
        'region_id': 'str',
        'running_count': 'int',
        'total_count': 'int'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'error_count': 'ErrorCount',
        'other_count': 'OtherCount',
        'region_id': 'RegionId',
        'running_count': 'RunningCount',
        'total_count': 'TotalCount'
    }

    def __init__(self, account_id=None, error_count=None, other_count=None, region_id=None, running_count=None, total_count=None, _configuration=None):  # noqa: E501
        """OverViewForDescribeFileSystemOverviewOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._error_count = None
        self._other_count = None
        self._region_id = None
        self._running_count = None
        self._total_count = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if error_count is not None:
            self.error_count = error_count
        if other_count is not None:
            self.other_count = other_count
        if region_id is not None:
            self.region_id = region_id
        if running_count is not None:
            self.running_count = running_count
        if total_count is not None:
            self.total_count = total_count

    @property
    def account_id(self):
        """Gets the account_id of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501


        :return: The account_id of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this OverViewForDescribeFileSystemOverviewOutput.


        :param account_id: The account_id of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def error_count(self):
        """Gets the error_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501


        :return: The error_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :rtype: int
        """
        return self._error_count

    @error_count.setter
    def error_count(self, error_count):
        """Sets the error_count of this OverViewForDescribeFileSystemOverviewOutput.


        :param error_count: The error_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :type: int
        """

        self._error_count = error_count

    @property
    def other_count(self):
        """Gets the other_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501


        :return: The other_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :rtype: int
        """
        return self._other_count

    @other_count.setter
    def other_count(self, other_count):
        """Sets the other_count of this OverViewForDescribeFileSystemOverviewOutput.


        :param other_count: The other_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :type: int
        """

        self._other_count = other_count

    @property
    def region_id(self):
        """Gets the region_id of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501


        :return: The region_id of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this OverViewForDescribeFileSystemOverviewOutput.


        :param region_id: The region_id of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def running_count(self):
        """Gets the running_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501


        :return: The running_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :rtype: int
        """
        return self._running_count

    @running_count.setter
    def running_count(self, running_count):
        """Sets the running_count of this OverViewForDescribeFileSystemOverviewOutput.


        :param running_count: The running_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :type: int
        """

        self._running_count = running_count

    @property
    def total_count(self):
        """Gets the total_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501


        :return: The total_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this OverViewForDescribeFileSystemOverviewOutput.


        :param total_count: The total_count of this OverViewForDescribeFileSystemOverviewOutput.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OverViewForDescribeFileSystemOverviewOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OverViewForDescribeFileSystemOverviewOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OverViewForDescribeFileSystemOverviewOutput):
            return True

        return self.to_dict() != other.to_dict()
