# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListUserZoneBackupsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_infos': 'list[BackupInfoForListUserZoneBackupsOutput]'
    }

    attribute_map = {
        'backup_infos': 'BackupInfos'
    }

    def __init__(self, backup_infos=None, _configuration=None):  # noqa: E501
        """ListUserZoneBackupsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_infos = None
        self.discriminator = None

        if backup_infos is not None:
            self.backup_infos = backup_infos

    @property
    def backup_infos(self):
        """Gets the backup_infos of this ListUserZoneBackupsResponse.  # noqa: E501


        :return: The backup_infos of this ListUserZoneBackupsResponse.  # noqa: E501
        :rtype: list[BackupInfoForListUserZoneBackupsOutput]
        """
        return self._backup_infos

    @backup_infos.setter
    def backup_infos(self, backup_infos):
        """Sets the backup_infos of this ListUserZoneBackupsResponse.


        :param backup_infos: The backup_infos of this ListUserZoneBackupsResponse.  # noqa: E501
        :type: list[BackupInfoForListUserZoneBackupsOutput]
        """

        self._backup_infos = backup_infos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListUserZoneBackupsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListUserZoneBackupsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListUserZoneBackupsResponse):
            return True

        return self.to_dict() != other.to_dict()
