# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAlertsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'desc': 'bool',
        'filter': 'FilterForListAlertsInput',
        'limit': 'int',
        'order_by': 'str',
        'search_after': 'str'
    }

    attribute_map = {
        'desc': 'Desc',
        'filter': 'Filter',
        'limit': 'Limit',
        'order_by': 'OrderBy',
        'search_after': 'SearchAfter'
    }

    def __init__(self, desc=None, filter=None, limit=None, order_by=None, search_after=None, _configuration=None):  # noqa: E501
        """ListAlertsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._desc = None
        self._filter = None
        self._limit = None
        self._order_by = None
        self._search_after = None
        self.discriminator = None

        if desc is not None:
            self.desc = desc
        if filter is not None:
            self.filter = filter
        if limit is not None:
            self.limit = limit
        if order_by is not None:
            self.order_by = order_by
        if search_after is not None:
            self.search_after = search_after

    @property
    def desc(self):
        """Gets the desc of this ListAlertsRequest.  # noqa: E501


        :return: The desc of this ListAlertsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._desc

    @desc.setter
    def desc(self, desc):
        """Sets the desc of this ListAlertsRequest.


        :param desc: The desc of this ListAlertsRequest.  # noqa: E501
        :type: bool
        """

        self._desc = desc

    @property
    def filter(self):
        """Gets the filter of this ListAlertsRequest.  # noqa: E501


        :return: The filter of this ListAlertsRequest.  # noqa: E501
        :rtype: FilterForListAlertsInput
        """
        return self._filter

    @filter.setter
    def filter(self, filter):
        """Sets the filter of this ListAlertsRequest.


        :param filter: The filter of this ListAlertsRequest.  # noqa: E501
        :type: FilterForListAlertsInput
        """

        self._filter = filter

    @property
    def limit(self):
        """Gets the limit of this ListAlertsRequest.  # noqa: E501


        :return: The limit of this ListAlertsRequest.  # noqa: E501
        :rtype: int
        """
        return self._limit

    @limit.setter
    def limit(self, limit):
        """Sets the limit of this ListAlertsRequest.


        :param limit: The limit of this ListAlertsRequest.  # noqa: E501
        :type: int
        """

        self._limit = limit

    @property
    def order_by(self):
        """Gets the order_by of this ListAlertsRequest.  # noqa: E501


        :return: The order_by of this ListAlertsRequest.  # noqa: E501
        :rtype: str
        """
        return self._order_by

    @order_by.setter
    def order_by(self, order_by):
        """Sets the order_by of this ListAlertsRequest.


        :param order_by: The order_by of this ListAlertsRequest.  # noqa: E501
        :type: str
        """

        self._order_by = order_by

    @property
    def search_after(self):
        """Gets the search_after of this ListAlertsRequest.  # noqa: E501


        :return: The search_after of this ListAlertsRequest.  # noqa: E501
        :rtype: str
        """
        return self._search_after

    @search_after.setter
    def search_after(self, search_after):
        """Sets the search_after of this ListAlertsRequest.


        :param search_after: The search_after of this ListAlertsRequest.  # noqa: E501
        :type: str
        """

        self._search_after = search_after

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAlertsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAlertsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAlertsRequest):
            return True

        return self.to_dict() != other.to_dict()
