# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListLiveChannelConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'cover_image': 'str',
        'line_id': 'int',
        'line_index': 'int',
        'line_language': 'str',
        'line_name': 'str',
        'lls_type': 'int',
        'main_backup_type': 'int',
        'mobile_default_resolution': 'str',
        'pc_default_resolution': 'str',
        'resolution': 'list[str]',
        'time_shift_type': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'cover_image': 'CoverImage',
        'line_id': 'LineId',
        'line_index': 'LineIndex',
        'line_language': 'LineLanguage',
        'line_name': 'LineName',
        'lls_type': 'LlsType',
        'main_backup_type': 'MainBackupType',
        'mobile_default_resolution': 'MobileDefaultResolution',
        'pc_default_resolution': 'PCDefaultResolution',
        'resolution': 'Resolution',
        'time_shift_type': 'TimeShiftType'
    }

    def __init__(self, activity_id=None, cover_image=None, line_id=None, line_index=None, line_language=None, line_name=None, lls_type=None, main_backup_type=None, mobile_default_resolution=None, pc_default_resolution=None, resolution=None, time_shift_type=None, _configuration=None):  # noqa: E501
        """ListLiveChannelConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._cover_image = None
        self._line_id = None
        self._line_index = None
        self._line_language = None
        self._line_name = None
        self._lls_type = None
        self._main_backup_type = None
        self._mobile_default_resolution = None
        self._pc_default_resolution = None
        self._resolution = None
        self._time_shift_type = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if cover_image is not None:
            self.cover_image = cover_image
        if line_id is not None:
            self.line_id = line_id
        if line_index is not None:
            self.line_index = line_index
        if line_language is not None:
            self.line_language = line_language
        if line_name is not None:
            self.line_name = line_name
        if lls_type is not None:
            self.lls_type = lls_type
        if main_backup_type is not None:
            self.main_backup_type = main_backup_type
        if mobile_default_resolution is not None:
            self.mobile_default_resolution = mobile_default_resolution
        if pc_default_resolution is not None:
            self.pc_default_resolution = pc_default_resolution
        if resolution is not None:
            self.resolution = resolution
        if time_shift_type is not None:
            self.time_shift_type = time_shift_type

    @property
    def activity_id(self):
        """Gets the activity_id of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The activity_id of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ListLiveChannelConfigResponse.


        :param activity_id: The activity_id of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def cover_image(self):
        """Gets the cover_image of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The cover_image of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._cover_image

    @cover_image.setter
    def cover_image(self, cover_image):
        """Sets the cover_image of this ListLiveChannelConfigResponse.


        :param cover_image: The cover_image of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: str
        """

        self._cover_image = cover_image

    @property
    def line_id(self):
        """Gets the line_id of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The line_id of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._line_id

    @line_id.setter
    def line_id(self, line_id):
        """Sets the line_id of this ListLiveChannelConfigResponse.


        :param line_id: The line_id of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: int
        """

        self._line_id = line_id

    @property
    def line_index(self):
        """Gets the line_index of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The line_index of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._line_index

    @line_index.setter
    def line_index(self, line_index):
        """Sets the line_index of this ListLiveChannelConfigResponse.


        :param line_index: The line_index of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: int
        """

        self._line_index = line_index

    @property
    def line_language(self):
        """Gets the line_language of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The line_language of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._line_language

    @line_language.setter
    def line_language(self, line_language):
        """Sets the line_language of this ListLiveChannelConfigResponse.


        :param line_language: The line_language of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: str
        """

        self._line_language = line_language

    @property
    def line_name(self):
        """Gets the line_name of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The line_name of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._line_name

    @line_name.setter
    def line_name(self, line_name):
        """Sets the line_name of this ListLiveChannelConfigResponse.


        :param line_name: The line_name of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: str
        """

        self._line_name = line_name

    @property
    def lls_type(self):
        """Gets the lls_type of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The lls_type of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._lls_type

    @lls_type.setter
    def lls_type(self, lls_type):
        """Sets the lls_type of this ListLiveChannelConfigResponse.


        :param lls_type: The lls_type of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: int
        """

        self._lls_type = lls_type

    @property
    def main_backup_type(self):
        """Gets the main_backup_type of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The main_backup_type of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._main_backup_type

    @main_backup_type.setter
    def main_backup_type(self, main_backup_type):
        """Sets the main_backup_type of this ListLiveChannelConfigResponse.


        :param main_backup_type: The main_backup_type of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: int
        """

        self._main_backup_type = main_backup_type

    @property
    def mobile_default_resolution(self):
        """Gets the mobile_default_resolution of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The mobile_default_resolution of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._mobile_default_resolution

    @mobile_default_resolution.setter
    def mobile_default_resolution(self, mobile_default_resolution):
        """Sets the mobile_default_resolution of this ListLiveChannelConfigResponse.


        :param mobile_default_resolution: The mobile_default_resolution of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: str
        """

        self._mobile_default_resolution = mobile_default_resolution

    @property
    def pc_default_resolution(self):
        """Gets the pc_default_resolution of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The pc_default_resolution of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._pc_default_resolution

    @pc_default_resolution.setter
    def pc_default_resolution(self, pc_default_resolution):
        """Sets the pc_default_resolution of this ListLiveChannelConfigResponse.


        :param pc_default_resolution: The pc_default_resolution of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: str
        """

        self._pc_default_resolution = pc_default_resolution

    @property
    def resolution(self):
        """Gets the resolution of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The resolution of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._resolution

    @resolution.setter
    def resolution(self, resolution):
        """Sets the resolution of this ListLiveChannelConfigResponse.


        :param resolution: The resolution of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: list[str]
        """

        self._resolution = resolution

    @property
    def time_shift_type(self):
        """Gets the time_shift_type of this ListLiveChannelConfigResponse.  # noqa: E501


        :return: The time_shift_type of this ListLiveChannelConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._time_shift_type

    @time_shift_type.setter
    def time_shift_type(self, time_shift_type):
        """Sets the time_shift_type of this ListLiveChannelConfigResponse.


        :param time_shift_type: The time_shift_type of this ListLiveChannelConfigResponse.  # noqa: E501
        :type: int
        """

        self._time_shift_type = time_shift_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListLiveChannelConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListLiveChannelConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListLiveChannelConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
