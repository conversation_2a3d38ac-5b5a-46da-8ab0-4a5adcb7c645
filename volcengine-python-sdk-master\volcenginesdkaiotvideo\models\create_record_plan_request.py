# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateRecordPlanRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bind_channels': 'BindChannelsForCreateRecordPlanInput',
        'bind_streams': 'list[str]',
        'bind_template': 'str',
        'description': 'str',
        'main_streams': 'list[str]',
        'plan_name': 'str',
        'resolution': 'str',
        'status': 'str',
        'streaming_index': 'int'
    }

    attribute_map = {
        'bind_channels': 'BindChannels',
        'bind_streams': 'BindStreams',
        'bind_template': 'BindTemplate',
        'description': 'Description',
        'main_streams': 'MainStreams',
        'plan_name': 'PlanName',
        'resolution': 'Resolution',
        'status': 'Status',
        'streaming_index': 'StreamingIndex'
    }

    def __init__(self, bind_channels=None, bind_streams=None, bind_template=None, description=None, main_streams=None, plan_name=None, resolution=None, status=None, streaming_index=None, _configuration=None):  # noqa: E501
        """CreateRecordPlanRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bind_channels = None
        self._bind_streams = None
        self._bind_template = None
        self._description = None
        self._main_streams = None
        self._plan_name = None
        self._resolution = None
        self._status = None
        self._streaming_index = None
        self.discriminator = None

        if bind_channels is not None:
            self.bind_channels = bind_channels
        if bind_streams is not None:
            self.bind_streams = bind_streams
        self.bind_template = bind_template
        if description is not None:
            self.description = description
        if main_streams is not None:
            self.main_streams = main_streams
        self.plan_name = plan_name
        if resolution is not None:
            self.resolution = resolution
        self.status = status
        if streaming_index is not None:
            self.streaming_index = streaming_index

    @property
    def bind_channels(self):
        """Gets the bind_channels of this CreateRecordPlanRequest.  # noqa: E501


        :return: The bind_channels of this CreateRecordPlanRequest.  # noqa: E501
        :rtype: BindChannelsForCreateRecordPlanInput
        """
        return self._bind_channels

    @bind_channels.setter
    def bind_channels(self, bind_channels):
        """Sets the bind_channels of this CreateRecordPlanRequest.


        :param bind_channels: The bind_channels of this CreateRecordPlanRequest.  # noqa: E501
        :type: BindChannelsForCreateRecordPlanInput
        """

        self._bind_channels = bind_channels

    @property
    def bind_streams(self):
        """Gets the bind_streams of this CreateRecordPlanRequest.  # noqa: E501


        :return: The bind_streams of this CreateRecordPlanRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._bind_streams

    @bind_streams.setter
    def bind_streams(self, bind_streams):
        """Sets the bind_streams of this CreateRecordPlanRequest.


        :param bind_streams: The bind_streams of this CreateRecordPlanRequest.  # noqa: E501
        :type: list[str]
        """

        self._bind_streams = bind_streams

    @property
    def bind_template(self):
        """Gets the bind_template of this CreateRecordPlanRequest.  # noqa: E501


        :return: The bind_template of this CreateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._bind_template

    @bind_template.setter
    def bind_template(self, bind_template):
        """Sets the bind_template of this CreateRecordPlanRequest.


        :param bind_template: The bind_template of this CreateRecordPlanRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and bind_template is None:
            raise ValueError("Invalid value for `bind_template`, must not be `None`")  # noqa: E501

        self._bind_template = bind_template

    @property
    def description(self):
        """Gets the description of this CreateRecordPlanRequest.  # noqa: E501


        :return: The description of this CreateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateRecordPlanRequest.


        :param description: The description of this CreateRecordPlanRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def main_streams(self):
        """Gets the main_streams of this CreateRecordPlanRequest.  # noqa: E501


        :return: The main_streams of this CreateRecordPlanRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._main_streams

    @main_streams.setter
    def main_streams(self, main_streams):
        """Sets the main_streams of this CreateRecordPlanRequest.


        :param main_streams: The main_streams of this CreateRecordPlanRequest.  # noqa: E501
        :type: list[str]
        """

        self._main_streams = main_streams

    @property
    def plan_name(self):
        """Gets the plan_name of this CreateRecordPlanRequest.  # noqa: E501


        :return: The plan_name of this CreateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._plan_name

    @plan_name.setter
    def plan_name(self, plan_name):
        """Sets the plan_name of this CreateRecordPlanRequest.


        :param plan_name: The plan_name of this CreateRecordPlanRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and plan_name is None:
            raise ValueError("Invalid value for `plan_name`, must not be `None`")  # noqa: E501

        self._plan_name = plan_name

    @property
    def resolution(self):
        """Gets the resolution of this CreateRecordPlanRequest.  # noqa: E501


        :return: The resolution of this CreateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._resolution

    @resolution.setter
    def resolution(self, resolution):
        """Sets the resolution of this CreateRecordPlanRequest.


        :param resolution: The resolution of this CreateRecordPlanRequest.  # noqa: E501
        :type: str
        """

        self._resolution = resolution

    @property
    def status(self):
        """Gets the status of this CreateRecordPlanRequest.  # noqa: E501


        :return: The status of this CreateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CreateRecordPlanRequest.


        :param status: The status of this CreateRecordPlanRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and status is None:
            raise ValueError("Invalid value for `status`, must not be `None`")  # noqa: E501

        self._status = status

    @property
    def streaming_index(self):
        """Gets the streaming_index of this CreateRecordPlanRequest.  # noqa: E501


        :return: The streaming_index of this CreateRecordPlanRequest.  # noqa: E501
        :rtype: int
        """
        return self._streaming_index

    @streaming_index.setter
    def streaming_index(self, streaming_index):
        """Sets the streaming_index of this CreateRecordPlanRequest.


        :param streaming_index: The streaming_index of this CreateRecordPlanRequest.  # noqa: E501
        :type: int
        """

        self._streaming_index = streaming_index

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateRecordPlanRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateRecordPlanRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateRecordPlanRequest):
            return True

        return self.to_dict() != other.to_dict()
