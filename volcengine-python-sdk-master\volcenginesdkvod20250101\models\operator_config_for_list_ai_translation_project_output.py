# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OperatorConfigForListAITranslationProjectOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'subtitle_recognition_config': 'SubtitleRecognitionConfigForListAITranslationProjectOutput'
    }

    attribute_map = {
        'subtitle_recognition_config': 'SubtitleRecognitionConfig'
    }

    def __init__(self, subtitle_recognition_config=None, _configuration=None):  # noqa: E501
        """OperatorConfigForListAITranslationProjectOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._subtitle_recognition_config = None
        self.discriminator = None

        if subtitle_recognition_config is not None:
            self.subtitle_recognition_config = subtitle_recognition_config

    @property
    def subtitle_recognition_config(self):
        """Gets the subtitle_recognition_config of this OperatorConfigForListAITranslationProjectOutput.  # noqa: E501


        :return: The subtitle_recognition_config of this OperatorConfigForListAITranslationProjectOutput.  # noqa: E501
        :rtype: SubtitleRecognitionConfigForListAITranslationProjectOutput
        """
        return self._subtitle_recognition_config

    @subtitle_recognition_config.setter
    def subtitle_recognition_config(self, subtitle_recognition_config):
        """Sets the subtitle_recognition_config of this OperatorConfigForListAITranslationProjectOutput.


        :param subtitle_recognition_config: The subtitle_recognition_config of this OperatorConfigForListAITranslationProjectOutput.  # noqa: E501
        :type: SubtitleRecognitionConfigForListAITranslationProjectOutput
        """

        self._subtitle_recognition_config = subtitle_recognition_config

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OperatorConfigForListAITranslationProjectOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OperatorConfigForListAITranslationProjectOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OperatorConfigForListAITranslationProjectOutput):
            return True

        return self.to_dict() != other.to_dict()
