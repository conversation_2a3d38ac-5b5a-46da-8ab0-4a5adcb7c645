# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListVirusAlarmsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_group': 'str',
        'agent_group_list': 'list[str]',
        'agent_id': 'str',
        'agent_id_list': 'list[str]',
        'agent_tags': 'list[str]',
        'alarm_handle_result_list': 'list[int]',
        'alarm_id': 'str',
        'alert_tags': 'list[str]',
        'cloud_providers': 'list[str]',
        'cluster_id': 'str',
        'cluster_name': 'str',
        'cluster_region': 'str',
        'cluster_tags': 'list[str]',
        'container_id': 'str',
        'container_name': 'str',
        'data_type': 'str',
        'event_id': 'str',
        'event_name': 'str',
        'event_reason': 'str',
        'exe': 'str',
        'file_hash': 'str',
        'file_path': 'str',
        'hostname': 'str',
        'ip': 'str',
        'image_name': 'str',
        'leaf_group_ids': 'list[str]',
        'level_list': 'list[str]',
        'mlp_instance_id': 'str',
        'name': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'probe_hook': 'str',
        'rasp_argv': 'str',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'list[int]',
        'task_id': 'str',
        'time_end': 'int',
        'time_start': 'int',
        'top_group_id': 'str',
        'type': 'list[str]',
        'virus_type': 'str',
        'white_list_id': 'str',
        'white_list_name': 'str'
    }

    attribute_map = {
        'agent_group': 'AgentGroup',
        'agent_group_list': 'AgentGroupList',
        'agent_id': 'AgentID',
        'agent_id_list': 'AgentIDList',
        'agent_tags': 'AgentTags',
        'alarm_handle_result_list': 'AlarmHandleResultList',
        'alarm_id': 'AlarmID',
        'alert_tags': 'AlertTags',
        'cloud_providers': 'CloudProviders',
        'cluster_id': 'ClusterID',
        'cluster_name': 'ClusterName',
        'cluster_region': 'ClusterRegion',
        'cluster_tags': 'ClusterTags',
        'container_id': 'ContainerID',
        'container_name': 'ContainerName',
        'data_type': 'DataType',
        'event_id': 'EventID',
        'event_name': 'EventName',
        'event_reason': 'EventReason',
        'exe': 'Exe',
        'file_hash': 'FileHash',
        'file_path': 'FilePath',
        'hostname': 'Hostname',
        'ip': 'IP',
        'image_name': 'ImageName',
        'leaf_group_ids': 'LeafGroupIDs',
        'level_list': 'LevelList',
        'mlp_instance_id': 'MlpInstanceID',
        'name': 'Name',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'probe_hook': 'ProbeHook',
        'rasp_argv': 'RaspArgv',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status',
        'task_id': 'TaskID',
        'time_end': 'TimeEnd',
        'time_start': 'TimeStart',
        'top_group_id': 'TopGroupID',
        'type': 'Type',
        'virus_type': 'VirusType',
        'white_list_id': 'WhiteListID',
        'white_list_name': 'WhiteListName'
    }

    def __init__(self, agent_group=None, agent_group_list=None, agent_id=None, agent_id_list=None, agent_tags=None, alarm_handle_result_list=None, alarm_id=None, alert_tags=None, cloud_providers=None, cluster_id=None, cluster_name=None, cluster_region=None, cluster_tags=None, container_id=None, container_name=None, data_type=None, event_id=None, event_name=None, event_reason=None, exe=None, file_hash=None, file_path=None, hostname=None, ip=None, image_name=None, leaf_group_ids=None, level_list=None, mlp_instance_id=None, name=None, page_number=None, page_size=None, probe_hook=None, rasp_argv=None, sort_by=None, sort_order=None, status=None, task_id=None, time_end=None, time_start=None, top_group_id=None, type=None, virus_type=None, white_list_id=None, white_list_name=None, _configuration=None):  # noqa: E501
        """ListVirusAlarmsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_group = None
        self._agent_group_list = None
        self._agent_id = None
        self._agent_id_list = None
        self._agent_tags = None
        self._alarm_handle_result_list = None
        self._alarm_id = None
        self._alert_tags = None
        self._cloud_providers = None
        self._cluster_id = None
        self._cluster_name = None
        self._cluster_region = None
        self._cluster_tags = None
        self._container_id = None
        self._container_name = None
        self._data_type = None
        self._event_id = None
        self._event_name = None
        self._event_reason = None
        self._exe = None
        self._file_hash = None
        self._file_path = None
        self._hostname = None
        self._ip = None
        self._image_name = None
        self._leaf_group_ids = None
        self._level_list = None
        self._mlp_instance_id = None
        self._name = None
        self._page_number = None
        self._page_size = None
        self._probe_hook = None
        self._rasp_argv = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self._task_id = None
        self._time_end = None
        self._time_start = None
        self._top_group_id = None
        self._type = None
        self._virus_type = None
        self._white_list_id = None
        self._white_list_name = None
        self.discriminator = None

        if agent_group is not None:
            self.agent_group = agent_group
        if agent_group_list is not None:
            self.agent_group_list = agent_group_list
        if agent_id is not None:
            self.agent_id = agent_id
        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if alarm_handle_result_list is not None:
            self.alarm_handle_result_list = alarm_handle_result_list
        if alarm_id is not None:
            self.alarm_id = alarm_id
        if alert_tags is not None:
            self.alert_tags = alert_tags
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if cluster_region is not None:
            self.cluster_region = cluster_region
        if cluster_tags is not None:
            self.cluster_tags = cluster_tags
        if container_id is not None:
            self.container_id = container_id
        if container_name is not None:
            self.container_name = container_name
        if data_type is not None:
            self.data_type = data_type
        if event_id is not None:
            self.event_id = event_id
        if event_name is not None:
            self.event_name = event_name
        if event_reason is not None:
            self.event_reason = event_reason
        if exe is not None:
            self.exe = exe
        if file_hash is not None:
            self.file_hash = file_hash
        if file_path is not None:
            self.file_path = file_path
        if hostname is not None:
            self.hostname = hostname
        if ip is not None:
            self.ip = ip
        if image_name is not None:
            self.image_name = image_name
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if level_list is not None:
            self.level_list = level_list
        if mlp_instance_id is not None:
            self.mlp_instance_id = mlp_instance_id
        if name is not None:
            self.name = name
        self.page_number = page_number
        self.page_size = page_size
        if probe_hook is not None:
            self.probe_hook = probe_hook
        if rasp_argv is not None:
            self.rasp_argv = rasp_argv
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status
        if task_id is not None:
            self.task_id = task_id
        if time_end is not None:
            self.time_end = time_end
        if time_start is not None:
            self.time_start = time_start
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if type is not None:
            self.type = type
        if virus_type is not None:
            self.virus_type = virus_type
        if white_list_id is not None:
            self.white_list_id = white_list_id
        if white_list_name is not None:
            self.white_list_name = white_list_name

    @property
    def agent_group(self):
        """Gets the agent_group of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The agent_group of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_group

    @agent_group.setter
    def agent_group(self, agent_group):
        """Sets the agent_group of this ListVirusAlarmsRequest.


        :param agent_group: The agent_group of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._agent_group = agent_group

    @property
    def agent_group_list(self):
        """Gets the agent_group_list of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The agent_group_list of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_group_list

    @agent_group_list.setter
    def agent_group_list(self, agent_group_list):
        """Sets the agent_group_list of this ListVirusAlarmsRequest.


        :param agent_group_list: The agent_group_list of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_group_list = agent_group_list

    @property
    def agent_id(self):
        """Gets the agent_id of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The agent_id of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ListVirusAlarmsRequest.


        :param agent_id: The agent_id of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The agent_id_list of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this ListVirusAlarmsRequest.


        :param agent_id_list: The agent_id_list of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def agent_tags(self):
        """Gets the agent_tags of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The agent_tags of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this ListVirusAlarmsRequest.


        :param agent_tags: The agent_tags of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def alarm_handle_result_list(self):
        """Gets the alarm_handle_result_list of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The alarm_handle_result_list of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._alarm_handle_result_list

    @alarm_handle_result_list.setter
    def alarm_handle_result_list(self, alarm_handle_result_list):
        """Sets the alarm_handle_result_list of this ListVirusAlarmsRequest.


        :param alarm_handle_result_list: The alarm_handle_result_list of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[int]
        """

        self._alarm_handle_result_list = alarm_handle_result_list

    @property
    def alarm_id(self):
        """Gets the alarm_id of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The alarm_id of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._alarm_id

    @alarm_id.setter
    def alarm_id(self, alarm_id):
        """Sets the alarm_id of this ListVirusAlarmsRequest.


        :param alarm_id: The alarm_id of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._alarm_id = alarm_id

    @property
    def alert_tags(self):
        """Gets the alert_tags of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The alert_tags of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._alert_tags

    @alert_tags.setter
    def alert_tags(self, alert_tags):
        """Sets the alert_tags of this ListVirusAlarmsRequest.


        :param alert_tags: The alert_tags of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[str]
        """

        self._alert_tags = alert_tags

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The cloud_providers of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ListVirusAlarmsRequest.


        :param cloud_providers: The cloud_providers of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The cluster_id of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ListVirusAlarmsRequest.


        :param cluster_id: The cluster_id of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The cluster_name of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this ListVirusAlarmsRequest.


        :param cluster_name: The cluster_name of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def cluster_region(self):
        """Gets the cluster_region of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The cluster_region of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_region

    @cluster_region.setter
    def cluster_region(self, cluster_region):
        """Sets the cluster_region of this ListVirusAlarmsRequest.


        :param cluster_region: The cluster_region of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._cluster_region = cluster_region

    @property
    def cluster_tags(self):
        """Gets the cluster_tags of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The cluster_tags of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_tags

    @cluster_tags.setter
    def cluster_tags(self, cluster_tags):
        """Sets the cluster_tags of this ListVirusAlarmsRequest.


        :param cluster_tags: The cluster_tags of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[str]
        """

        self._cluster_tags = cluster_tags

    @property
    def container_id(self):
        """Gets the container_id of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The container_id of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._container_id

    @container_id.setter
    def container_id(self, container_id):
        """Sets the container_id of this ListVirusAlarmsRequest.


        :param container_id: The container_id of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._container_id = container_id

    @property
    def container_name(self):
        """Gets the container_name of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The container_name of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._container_name

    @container_name.setter
    def container_name(self, container_name):
        """Sets the container_name of this ListVirusAlarmsRequest.


        :param container_name: The container_name of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._container_name = container_name

    @property
    def data_type(self):
        """Gets the data_type of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The data_type of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_type

    @data_type.setter
    def data_type(self, data_type):
        """Sets the data_type of this ListVirusAlarmsRequest.


        :param data_type: The data_type of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._data_type = data_type

    @property
    def event_id(self):
        """Gets the event_id of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The event_id of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._event_id

    @event_id.setter
    def event_id(self, event_id):
        """Sets the event_id of this ListVirusAlarmsRequest.


        :param event_id: The event_id of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._event_id = event_id

    @property
    def event_name(self):
        """Gets the event_name of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The event_name of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._event_name

    @event_name.setter
    def event_name(self, event_name):
        """Sets the event_name of this ListVirusAlarmsRequest.


        :param event_name: The event_name of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._event_name = event_name

    @property
    def event_reason(self):
        """Gets the event_reason of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The event_reason of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._event_reason

    @event_reason.setter
    def event_reason(self, event_reason):
        """Sets the event_reason of this ListVirusAlarmsRequest.


        :param event_reason: The event_reason of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._event_reason = event_reason

    @property
    def exe(self):
        """Gets the exe of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The exe of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this ListVirusAlarmsRequest.


        :param exe: The exe of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def file_hash(self):
        """Gets the file_hash of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The file_hash of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_hash

    @file_hash.setter
    def file_hash(self, file_hash):
        """Sets the file_hash of this ListVirusAlarmsRequest.


        :param file_hash: The file_hash of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._file_hash = file_hash

    @property
    def file_path(self):
        """Gets the file_path of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The file_path of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._file_path

    @file_path.setter
    def file_path(self, file_path):
        """Sets the file_path of this ListVirusAlarmsRequest.


        :param file_path: The file_path of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._file_path = file_path

    @property
    def hostname(self):
        """Gets the hostname of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The hostname of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this ListVirusAlarmsRequest.


        :param hostname: The hostname of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def ip(self):
        """Gets the ip of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The ip of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ListVirusAlarmsRequest.


        :param ip: The ip of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def image_name(self):
        """Gets the image_name of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The image_name of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_name

    @image_name.setter
    def image_name(self, image_name):
        """Sets the image_name of this ListVirusAlarmsRequest.


        :param image_name: The image_name of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._image_name = image_name

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The leaf_group_ids of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ListVirusAlarmsRequest.


        :param leaf_group_ids: The leaf_group_ids of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def level_list(self):
        """Gets the level_list of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The level_list of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._level_list

    @level_list.setter
    def level_list(self, level_list):
        """Sets the level_list of this ListVirusAlarmsRequest.


        :param level_list: The level_list of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[str]
        """

        self._level_list = level_list

    @property
    def mlp_instance_id(self):
        """Gets the mlp_instance_id of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The mlp_instance_id of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._mlp_instance_id

    @mlp_instance_id.setter
    def mlp_instance_id(self, mlp_instance_id):
        """Sets the mlp_instance_id of this ListVirusAlarmsRequest.


        :param mlp_instance_id: The mlp_instance_id of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._mlp_instance_id = mlp_instance_id

    @property
    def name(self):
        """Gets the name of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The name of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ListVirusAlarmsRequest.


        :param name: The name of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def page_number(self):
        """Gets the page_number of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The page_number of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListVirusAlarmsRequest.


        :param page_number: The page_number of this ListVirusAlarmsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The page_size of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListVirusAlarmsRequest.


        :param page_size: The page_size of this ListVirusAlarmsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def probe_hook(self):
        """Gets the probe_hook of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The probe_hook of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._probe_hook

    @probe_hook.setter
    def probe_hook(self, probe_hook):
        """Sets the probe_hook of this ListVirusAlarmsRequest.


        :param probe_hook: The probe_hook of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._probe_hook = probe_hook

    @property
    def rasp_argv(self):
        """Gets the rasp_argv of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The rasp_argv of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._rasp_argv

    @rasp_argv.setter
    def rasp_argv(self, rasp_argv):
        """Sets the rasp_argv of this ListVirusAlarmsRequest.


        :param rasp_argv: The rasp_argv of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._rasp_argv = rasp_argv

    @property
    def sort_by(self):
        """Gets the sort_by of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The sort_by of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListVirusAlarmsRequest.


        :param sort_by: The sort_by of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The sort_order of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListVirusAlarmsRequest.


        :param sort_order: The sort_order of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The status of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListVirusAlarmsRequest.


        :param status: The status of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[int]
        """

        self._status = status

    @property
    def task_id(self):
        """Gets the task_id of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The task_id of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._task_id

    @task_id.setter
    def task_id(self, task_id):
        """Sets the task_id of this ListVirusAlarmsRequest.


        :param task_id: The task_id of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._task_id = task_id

    @property
    def time_end(self):
        """Gets the time_end of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The time_end of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: int
        """
        return self._time_end

    @time_end.setter
    def time_end(self, time_end):
        """Sets the time_end of this ListVirusAlarmsRequest.


        :param time_end: The time_end of this ListVirusAlarmsRequest.  # noqa: E501
        :type: int
        """

        self._time_end = time_end

    @property
    def time_start(self):
        """Gets the time_start of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The time_start of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: int
        """
        return self._time_start

    @time_start.setter
    def time_start(self, time_start):
        """Sets the time_start of this ListVirusAlarmsRequest.


        :param time_start: The time_start of this ListVirusAlarmsRequest.  # noqa: E501
        :type: int
        """

        self._time_start = time_start

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The top_group_id of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListVirusAlarmsRequest.


        :param top_group_id: The top_group_id of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def type(self):
        """Gets the type of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The type of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ListVirusAlarmsRequest.


        :param type: The type of this ListVirusAlarmsRequest.  # noqa: E501
        :type: list[str]
        """

        self._type = type

    @property
    def virus_type(self):
        """Gets the virus_type of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The virus_type of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._virus_type

    @virus_type.setter
    def virus_type(self, virus_type):
        """Sets the virus_type of this ListVirusAlarmsRequest.


        :param virus_type: The virus_type of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._virus_type = virus_type

    @property
    def white_list_id(self):
        """Gets the white_list_id of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The white_list_id of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._white_list_id

    @white_list_id.setter
    def white_list_id(self, white_list_id):
        """Sets the white_list_id of this ListVirusAlarmsRequest.


        :param white_list_id: The white_list_id of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._white_list_id = white_list_id

    @property
    def white_list_name(self):
        """Gets the white_list_name of this ListVirusAlarmsRequest.  # noqa: E501


        :return: The white_list_name of this ListVirusAlarmsRequest.  # noqa: E501
        :rtype: str
        """
        return self._white_list_name

    @white_list_name.setter
    def white_list_name(self, white_list_name):
        """Sets the white_list_name of this ListVirusAlarmsRequest.


        :param white_list_name: The white_list_name of this ListVirusAlarmsRequest.  # noqa: E501
        :type: str
        """

        self._white_list_name = white_list_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListVirusAlarmsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListVirusAlarmsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListVirusAlarmsRequest):
            return True

        return self.to_dict() != other.to_dict()
