# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAcceleratorsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'billing_spec': 'str',
        'charge_type': 'str',
        'page_num': 'int',
        'page_size': 'int',
        'project_name': 'str',
        'resource_tag_filter': 'ResourceTagFilterForListAcceleratorsInput',
        'state': 'str',
        'tags': 'list[TagForListAcceleratorsInput]'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'billing_spec': 'BillingSpec',
        'charge_type': 'ChargeType',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'project_name': 'ProjectName',
        'resource_tag_filter': 'ResourceTagFilter',
        'state': 'State',
        'tags': 'Tags'
    }

    def __init__(self, accelerator_id=None, billing_spec=None, charge_type=None, page_num=None, page_size=None, project_name=None, resource_tag_filter=None, state=None, tags=None, _configuration=None):  # noqa: E501
        """ListAcceleratorsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._billing_spec = None
        self._charge_type = None
        self._page_num = None
        self._page_size = None
        self._project_name = None
        self._resource_tag_filter = None
        self._state = None
        self._tags = None
        self.discriminator = None

        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if billing_spec is not None:
            self.billing_spec = billing_spec
        if charge_type is not None:
            self.charge_type = charge_type
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if project_name is not None:
            self.project_name = project_name
        if resource_tag_filter is not None:
            self.resource_tag_filter = resource_tag_filter
        if state is not None:
            self.state = state
        if tags is not None:
            self.tags = tags

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this ListAcceleratorsRequest.  # noqa: E501


        :return: The accelerator_id of this ListAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this ListAcceleratorsRequest.


        :param accelerator_id: The accelerator_id of this ListAcceleratorsRequest.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def billing_spec(self):
        """Gets the billing_spec of this ListAcceleratorsRequest.  # noqa: E501


        :return: The billing_spec of this ListAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._billing_spec

    @billing_spec.setter
    def billing_spec(self, billing_spec):
        """Sets the billing_spec of this ListAcceleratorsRequest.


        :param billing_spec: The billing_spec of this ListAcceleratorsRequest.  # noqa: E501
        :type: str
        """

        self._billing_spec = billing_spec

    @property
    def charge_type(self):
        """Gets the charge_type of this ListAcceleratorsRequest.  # noqa: E501


        :return: The charge_type of this ListAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ListAcceleratorsRequest.


        :param charge_type: The charge_type of this ListAcceleratorsRequest.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def page_num(self):
        """Gets the page_num of this ListAcceleratorsRequest.  # noqa: E501


        :return: The page_num of this ListAcceleratorsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListAcceleratorsRequest.


        :param page_num: The page_num of this ListAcceleratorsRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListAcceleratorsRequest.  # noqa: E501


        :return: The page_size of this ListAcceleratorsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListAcceleratorsRequest.


        :param page_size: The page_size of this ListAcceleratorsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project_name(self):
        """Gets the project_name of this ListAcceleratorsRequest.  # noqa: E501


        :return: The project_name of this ListAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this ListAcceleratorsRequest.


        :param project_name: The project_name of this ListAcceleratorsRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resource_tag_filter(self):
        """Gets the resource_tag_filter of this ListAcceleratorsRequest.  # noqa: E501


        :return: The resource_tag_filter of this ListAcceleratorsRequest.  # noqa: E501
        :rtype: ResourceTagFilterForListAcceleratorsInput
        """
        return self._resource_tag_filter

    @resource_tag_filter.setter
    def resource_tag_filter(self, resource_tag_filter):
        """Sets the resource_tag_filter of this ListAcceleratorsRequest.


        :param resource_tag_filter: The resource_tag_filter of this ListAcceleratorsRequest.  # noqa: E501
        :type: ResourceTagFilterForListAcceleratorsInput
        """

        self._resource_tag_filter = resource_tag_filter

    @property
    def state(self):
        """Gets the state of this ListAcceleratorsRequest.  # noqa: E501


        :return: The state of this ListAcceleratorsRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ListAcceleratorsRequest.


        :param state: The state of this ListAcceleratorsRequest.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def tags(self):
        """Gets the tags of this ListAcceleratorsRequest.  # noqa: E501


        :return: The tags of this ListAcceleratorsRequest.  # noqa: E501
        :rtype: list[TagForListAcceleratorsInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ListAcceleratorsRequest.


        :param tags: The tags of this ListAcceleratorsRequest.  # noqa: E501
        :type: list[TagForListAcceleratorsInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAcceleratorsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAcceleratorsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAcceleratorsRequest):
            return True

        return self.to_dict() != other.to_dict()
