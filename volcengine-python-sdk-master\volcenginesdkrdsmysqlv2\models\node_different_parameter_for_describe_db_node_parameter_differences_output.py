# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'node_parameter_value_details': 'list[NodeParameterValueDetailForDescribeDBNodeParameterDifferencesOutput]',
        'parameter_name': 'str'
    }

    attribute_map = {
        'node_parameter_value_details': 'NodeParameterValueDetails',
        'parameter_name': 'ParameterName'
    }

    def __init__(self, node_parameter_value_details=None, parameter_name=None, _configuration=None):  # noqa: E501
        """NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._node_parameter_value_details = None
        self._parameter_name = None
        self.discriminator = None

        if node_parameter_value_details is not None:
            self.node_parameter_value_details = node_parameter_value_details
        if parameter_name is not None:
            self.parameter_name = parameter_name

    @property
    def node_parameter_value_details(self):
        """Gets the node_parameter_value_details of this NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput.  # noqa: E501


        :return: The node_parameter_value_details of this NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput.  # noqa: E501
        :rtype: list[NodeParameterValueDetailForDescribeDBNodeParameterDifferencesOutput]
        """
        return self._node_parameter_value_details

    @node_parameter_value_details.setter
    def node_parameter_value_details(self, node_parameter_value_details):
        """Sets the node_parameter_value_details of this NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput.


        :param node_parameter_value_details: The node_parameter_value_details of this NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput.  # noqa: E501
        :type: list[NodeParameterValueDetailForDescribeDBNodeParameterDifferencesOutput]
        """

        self._node_parameter_value_details = node_parameter_value_details

    @property
    def parameter_name(self):
        """Gets the parameter_name of this NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput.  # noqa: E501


        :return: The parameter_name of this NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput.  # noqa: E501
        :rtype: str
        """
        return self._parameter_name

    @parameter_name.setter
    def parameter_name(self, parameter_name):
        """Sets the parameter_name of this NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput.


        :param parameter_name: The parameter_name of this NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput.  # noqa: E501
        :type: str
        """

        self._parameter_name = parameter_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeDifferentParameterForDescribeDBNodeParameterDifferencesOutput):
            return True

        return self.to_dict() != other.to_dict()
