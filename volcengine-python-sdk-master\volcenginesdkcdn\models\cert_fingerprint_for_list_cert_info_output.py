# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CertFingerprintForListCertInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sha1': 'str',
        'sha256': 'str'
    }

    attribute_map = {
        'sha1': 'Sha1',
        'sha256': 'Sha256'
    }

    def __init__(self, sha1=None, sha256=None, _configuration=None):  # noqa: E501
        """CertFingerprintForListCertInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._sha1 = None
        self._sha256 = None
        self.discriminator = None

        if sha1 is not None:
            self.sha1 = sha1
        if sha256 is not None:
            self.sha256 = sha256

    @property
    def sha1(self):
        """Gets the sha1 of this CertFingerprintForListCertInfoOutput.  # noqa: E501


        :return: The sha1 of this CertFingerprintForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._sha1

    @sha1.setter
    def sha1(self, sha1):
        """Sets the sha1 of this CertFingerprintForListCertInfoOutput.


        :param sha1: The sha1 of this CertFingerprintForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._sha1 = sha1

    @property
    def sha256(self):
        """Gets the sha256 of this CertFingerprintForListCertInfoOutput.  # noqa: E501


        :return: The sha256 of this CertFingerprintForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._sha256

    @sha256.setter
    def sha256(self, sha256):
        """Sets the sha256 of this CertFingerprintForListCertInfoOutput.


        :param sha256: The sha256 of this CertFingerprintForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._sha256 = sha256

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CertFingerprintForListCertInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CertFingerprintForListCertInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CertFingerprintForListCertInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
