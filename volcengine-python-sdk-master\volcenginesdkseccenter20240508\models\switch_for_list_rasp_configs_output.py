# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SwitchForListRaspConfigsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'classupload': 'bool',
        'file': 'bool',
        'load': 'bool',
        'memshell': 'bool',
        'net': 'bool',
        'process': 'bool',
        'reflect': 'bool'
    }

    attribute_map = {
        'classupload': 'classupload',
        'file': 'file',
        'load': 'load',
        'memshell': 'memshell',
        'net': 'net',
        'process': 'process',
        'reflect': 'reflect'
    }

    def __init__(self, classupload=None, file=None, load=None, memshell=None, net=None, process=None, reflect=None, _configuration=None):  # noqa: E501
        """SwitchForListRaspConfigsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._classupload = None
        self._file = None
        self._load = None
        self._memshell = None
        self._net = None
        self._process = None
        self._reflect = None
        self.discriminator = None

        if classupload is not None:
            self.classupload = classupload
        if file is not None:
            self.file = file
        if load is not None:
            self.load = load
        if memshell is not None:
            self.memshell = memshell
        if net is not None:
            self.net = net
        if process is not None:
            self.process = process
        if reflect is not None:
            self.reflect = reflect

    @property
    def classupload(self):
        """Gets the classupload of this SwitchForListRaspConfigsOutput.  # noqa: E501


        :return: The classupload of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._classupload

    @classupload.setter
    def classupload(self, classupload):
        """Sets the classupload of this SwitchForListRaspConfigsOutput.


        :param classupload: The classupload of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :type: bool
        """

        self._classupload = classupload

    @property
    def file(self):
        """Gets the file of this SwitchForListRaspConfigsOutput.  # noqa: E501


        :return: The file of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._file

    @file.setter
    def file(self, file):
        """Sets the file of this SwitchForListRaspConfigsOutput.


        :param file: The file of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :type: bool
        """

        self._file = file

    @property
    def load(self):
        """Gets the load of this SwitchForListRaspConfigsOutput.  # noqa: E501


        :return: The load of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._load

    @load.setter
    def load(self, load):
        """Sets the load of this SwitchForListRaspConfigsOutput.


        :param load: The load of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :type: bool
        """

        self._load = load

    @property
    def memshell(self):
        """Gets the memshell of this SwitchForListRaspConfigsOutput.  # noqa: E501


        :return: The memshell of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._memshell

    @memshell.setter
    def memshell(self, memshell):
        """Sets the memshell of this SwitchForListRaspConfigsOutput.


        :param memshell: The memshell of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :type: bool
        """

        self._memshell = memshell

    @property
    def net(self):
        """Gets the net of this SwitchForListRaspConfigsOutput.  # noqa: E501


        :return: The net of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._net

    @net.setter
    def net(self, net):
        """Sets the net of this SwitchForListRaspConfigsOutput.


        :param net: The net of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :type: bool
        """

        self._net = net

    @property
    def process(self):
        """Gets the process of this SwitchForListRaspConfigsOutput.  # noqa: E501


        :return: The process of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._process

    @process.setter
    def process(self, process):
        """Sets the process of this SwitchForListRaspConfigsOutput.


        :param process: The process of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :type: bool
        """

        self._process = process

    @property
    def reflect(self):
        """Gets the reflect of this SwitchForListRaspConfigsOutput.  # noqa: E501


        :return: The reflect of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._reflect

    @reflect.setter
    def reflect(self, reflect):
        """Sets the reflect of this SwitchForListRaspConfigsOutput.


        :param reflect: The reflect of this SwitchForListRaspConfigsOutput.  # noqa: E501
        :type: bool
        """

        self._reflect = reflect

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SwitchForListRaspConfigsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SwitchForListRaspConfigsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SwitchForListRaspConfigsOutput):
            return True

        return self.to_dict() != other.to_dict()
