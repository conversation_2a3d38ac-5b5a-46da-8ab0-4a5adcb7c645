# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetRaspAlarmSummaryInfoResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_content': 'AlarmContentForGetRaspAlarmSummaryInfoOutput',
        'alarm_desc': 'AlarmDescForGetRaspAlarmSummaryInfoOutput',
        'asset_info': 'AssetInfoForGetRaspAlarmSummaryInfoOutput',
        'container_info': 'ContainerInfoForGetRaspAlarmSummaryInfoOutput',
        'endpoint_trace': 'EndpointTraceForGetRaspAlarmSummaryInfoOutput',
        'handling_records': 'list[HandlingRecordForGetRaspAlarmSummaryInfoOutput]',
        'raw_data': 'RawDataForGetRaspAlarmSummaryInfoOutput'
    }

    attribute_map = {
        'alarm_content': 'AlarmContent',
        'alarm_desc': 'AlarmDesc',
        'asset_info': 'AssetInfo',
        'container_info': 'ContainerInfo',
        'endpoint_trace': 'EndpointTrace',
        'handling_records': 'HandlingRecords',
        'raw_data': 'RawData'
    }

    def __init__(self, alarm_content=None, alarm_desc=None, asset_info=None, container_info=None, endpoint_trace=None, handling_records=None, raw_data=None, _configuration=None):  # noqa: E501
        """GetRaspAlarmSummaryInfoResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_content = None
        self._alarm_desc = None
        self._asset_info = None
        self._container_info = None
        self._endpoint_trace = None
        self._handling_records = None
        self._raw_data = None
        self.discriminator = None

        if alarm_content is not None:
            self.alarm_content = alarm_content
        if alarm_desc is not None:
            self.alarm_desc = alarm_desc
        if asset_info is not None:
            self.asset_info = asset_info
        if container_info is not None:
            self.container_info = container_info
        if endpoint_trace is not None:
            self.endpoint_trace = endpoint_trace
        if handling_records is not None:
            self.handling_records = handling_records
        if raw_data is not None:
            self.raw_data = raw_data

    @property
    def alarm_content(self):
        """Gets the alarm_content of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501


        :return: The alarm_content of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :rtype: AlarmContentForGetRaspAlarmSummaryInfoOutput
        """
        return self._alarm_content

    @alarm_content.setter
    def alarm_content(self, alarm_content):
        """Sets the alarm_content of this GetRaspAlarmSummaryInfoResponse.


        :param alarm_content: The alarm_content of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :type: AlarmContentForGetRaspAlarmSummaryInfoOutput
        """

        self._alarm_content = alarm_content

    @property
    def alarm_desc(self):
        """Gets the alarm_desc of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501


        :return: The alarm_desc of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :rtype: AlarmDescForGetRaspAlarmSummaryInfoOutput
        """
        return self._alarm_desc

    @alarm_desc.setter
    def alarm_desc(self, alarm_desc):
        """Sets the alarm_desc of this GetRaspAlarmSummaryInfoResponse.


        :param alarm_desc: The alarm_desc of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :type: AlarmDescForGetRaspAlarmSummaryInfoOutput
        """

        self._alarm_desc = alarm_desc

    @property
    def asset_info(self):
        """Gets the asset_info of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501


        :return: The asset_info of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :rtype: AssetInfoForGetRaspAlarmSummaryInfoOutput
        """
        return self._asset_info

    @asset_info.setter
    def asset_info(self, asset_info):
        """Sets the asset_info of this GetRaspAlarmSummaryInfoResponse.


        :param asset_info: The asset_info of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :type: AssetInfoForGetRaspAlarmSummaryInfoOutput
        """

        self._asset_info = asset_info

    @property
    def container_info(self):
        """Gets the container_info of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501


        :return: The container_info of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :rtype: ContainerInfoForGetRaspAlarmSummaryInfoOutput
        """
        return self._container_info

    @container_info.setter
    def container_info(self, container_info):
        """Sets the container_info of this GetRaspAlarmSummaryInfoResponse.


        :param container_info: The container_info of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :type: ContainerInfoForGetRaspAlarmSummaryInfoOutput
        """

        self._container_info = container_info

    @property
    def endpoint_trace(self):
        """Gets the endpoint_trace of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501


        :return: The endpoint_trace of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :rtype: EndpointTraceForGetRaspAlarmSummaryInfoOutput
        """
        return self._endpoint_trace

    @endpoint_trace.setter
    def endpoint_trace(self, endpoint_trace):
        """Sets the endpoint_trace of this GetRaspAlarmSummaryInfoResponse.


        :param endpoint_trace: The endpoint_trace of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :type: EndpointTraceForGetRaspAlarmSummaryInfoOutput
        """

        self._endpoint_trace = endpoint_trace

    @property
    def handling_records(self):
        """Gets the handling_records of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501


        :return: The handling_records of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :rtype: list[HandlingRecordForGetRaspAlarmSummaryInfoOutput]
        """
        return self._handling_records

    @handling_records.setter
    def handling_records(self, handling_records):
        """Sets the handling_records of this GetRaspAlarmSummaryInfoResponse.


        :param handling_records: The handling_records of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :type: list[HandlingRecordForGetRaspAlarmSummaryInfoOutput]
        """

        self._handling_records = handling_records

    @property
    def raw_data(self):
        """Gets the raw_data of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501


        :return: The raw_data of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :rtype: RawDataForGetRaspAlarmSummaryInfoOutput
        """
        return self._raw_data

    @raw_data.setter
    def raw_data(self, raw_data):
        """Sets the raw_data of this GetRaspAlarmSummaryInfoResponse.


        :param raw_data: The raw_data of this GetRaspAlarmSummaryInfoResponse.  # noqa: E501
        :type: RawDataForGetRaspAlarmSummaryInfoOutput
        """

        self._raw_data = raw_data

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetRaspAlarmSummaryInfoResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetRaspAlarmSummaryInfoResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetRaspAlarmSummaryInfoResponse):
            return True

        return self.to_dict() != other.to_dict()
