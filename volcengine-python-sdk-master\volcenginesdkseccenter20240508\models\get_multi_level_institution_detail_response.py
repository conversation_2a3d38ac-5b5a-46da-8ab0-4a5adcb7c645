# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetMultiLevelInstitutionDetailResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'auth_info': 'AuthInfoForGetMultiLevelInstitutionDetailOutput',
        'base_info': 'BaseInfoForGetMultiLevelInstitutionDetailOutput',
        'id': 'str',
        'configuration': 'ConfigurationForGetMultiLevelInstitutionDetailOutput',
        'host_overview': 'HostOverviewForGetMultiLevelInstitutionDetailOutput',
        'threaten': 'ThreatenForGetMultiLevelInstitutionDetailOutput'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'auth_info': 'AuthInfo',
        'base_info': 'BaseInfo',
        'id': 'ID',
        'configuration': 'configuration',
        'host_overview': 'hostOverview',
        'threaten': 'threaten'
    }

    def __init__(self, account_id=None, auth_info=None, base_info=None, id=None, configuration=None, host_overview=None, threaten=None, _configuration=None):  # noqa: E501
        """GetMultiLevelInstitutionDetailResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._auth_info = None
        self._base_info = None
        self._id = None
        self._configuration = None
        self._host_overview = None
        self._threaten = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if auth_info is not None:
            self.auth_info = auth_info
        if base_info is not None:
            self.base_info = base_info
        if id is not None:
            self.id = id
        if configuration is not None:
            self.configuration = configuration
        if host_overview is not None:
            self.host_overview = host_overview
        if threaten is not None:
            self.threaten = threaten

    @property
    def account_id(self):
        """Gets the account_id of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501


        :return: The account_id of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this GetMultiLevelInstitutionDetailResponse.


        :param account_id: The account_id of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def auth_info(self):
        """Gets the auth_info of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501


        :return: The auth_info of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :rtype: AuthInfoForGetMultiLevelInstitutionDetailOutput
        """
        return self._auth_info

    @auth_info.setter
    def auth_info(self, auth_info):
        """Sets the auth_info of this GetMultiLevelInstitutionDetailResponse.


        :param auth_info: The auth_info of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :type: AuthInfoForGetMultiLevelInstitutionDetailOutput
        """

        self._auth_info = auth_info

    @property
    def base_info(self):
        """Gets the base_info of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501


        :return: The base_info of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :rtype: BaseInfoForGetMultiLevelInstitutionDetailOutput
        """
        return self._base_info

    @base_info.setter
    def base_info(self, base_info):
        """Sets the base_info of this GetMultiLevelInstitutionDetailResponse.


        :param base_info: The base_info of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :type: BaseInfoForGetMultiLevelInstitutionDetailOutput
        """

        self._base_info = base_info

    @property
    def id(self):
        """Gets the id of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501


        :return: The id of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetMultiLevelInstitutionDetailResponse.


        :param id: The id of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def configuration(self):
        """Gets the configuration of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501


        :return: The configuration of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :rtype: ConfigurationForGetMultiLevelInstitutionDetailOutput
        """
        return self._configuration

    @configuration.setter
    def configuration(self, configuration):
        """Sets the configuration of this GetMultiLevelInstitutionDetailResponse.


        :param configuration: The configuration of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :type: ConfigurationForGetMultiLevelInstitutionDetailOutput
        """

        self._configuration = configuration

    @property
    def host_overview(self):
        """Gets the host_overview of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501


        :return: The host_overview of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :rtype: HostOverviewForGetMultiLevelInstitutionDetailOutput
        """
        return self._host_overview

    @host_overview.setter
    def host_overview(self, host_overview):
        """Sets the host_overview of this GetMultiLevelInstitutionDetailResponse.


        :param host_overview: The host_overview of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :type: HostOverviewForGetMultiLevelInstitutionDetailOutput
        """

        self._host_overview = host_overview

    @property
    def threaten(self):
        """Gets the threaten of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501


        :return: The threaten of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :rtype: ThreatenForGetMultiLevelInstitutionDetailOutput
        """
        return self._threaten

    @threaten.setter
    def threaten(self, threaten):
        """Sets the threaten of this GetMultiLevelInstitutionDetailResponse.


        :param threaten: The threaten of this GetMultiLevelInstitutionDetailResponse.  # noqa: E501
        :type: ThreatenForGetMultiLevelInstitutionDetailOutput
        """

        self._threaten = threaten

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetMultiLevelInstitutionDetailResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetMultiLevelInstitutionDetailResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetMultiLevelInstitutionDetailResponse):
            return True

        return self.to_dict() != other.to_dict()
