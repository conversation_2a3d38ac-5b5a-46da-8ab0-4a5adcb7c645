# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VKEConfigForListClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'storage_class': 'str',
        'vci_enabled': 'bool'
    }

    attribute_map = {
        'cluster_id': 'ClusterID',
        'storage_class': 'StorageClass',
        'vci_enabled': 'VCIEnabled'
    }

    def __init__(self, cluster_id=None, storage_class=None, vci_enabled=None, _configuration=None):  # noqa: E501
        """VKEConfigForListClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._storage_class = None
        self._vci_enabled = None
        self.discriminator = None

        if cluster_id is not None:
            self.cluster_id = cluster_id
        if storage_class is not None:
            self.storage_class = storage_class
        if vci_enabled is not None:
            self.vci_enabled = vci_enabled

    @property
    def cluster_id(self):
        """Gets the cluster_id of this VKEConfigForListClustersOutput.  # noqa: E501


        :return: The cluster_id of this VKEConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this VKEConfigForListClustersOutput.


        :param cluster_id: The cluster_id of this VKEConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def storage_class(self):
        """Gets the storage_class of this VKEConfigForListClustersOutput.  # noqa: E501


        :return: The storage_class of this VKEConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._storage_class

    @storage_class.setter
    def storage_class(self, storage_class):
        """Sets the storage_class of this VKEConfigForListClustersOutput.


        :param storage_class: The storage_class of this VKEConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._storage_class = storage_class

    @property
    def vci_enabled(self):
        """Gets the vci_enabled of this VKEConfigForListClustersOutput.  # noqa: E501


        :return: The vci_enabled of this VKEConfigForListClustersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._vci_enabled

    @vci_enabled.setter
    def vci_enabled(self, vci_enabled):
        """Sets the vci_enabled of this VKEConfigForListClustersOutput.


        :param vci_enabled: The vci_enabled of this VKEConfigForListClustersOutput.  # noqa: E501
        :type: bool
        """

        self._vci_enabled = vci_enabled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VKEConfigForListClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VKEConfigForListClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VKEConfigForListClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
