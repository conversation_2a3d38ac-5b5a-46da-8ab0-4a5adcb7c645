# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatusCodeActionForAddCdnDomainInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'default_action': 'str',
        'fail_code': 'str',
        'success_code': 'str'
    }

    attribute_map = {
        'default_action': 'DefaultAction',
        'fail_code': 'FailCode',
        'success_code': 'SuccessCode'
    }

    def __init__(self, default_action=None, fail_code=None, success_code=None, _configuration=None):  # noqa: E501
        """StatusCodeActionForAddCdnDomainInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._default_action = None
        self._fail_code = None
        self._success_code = None
        self.discriminator = None

        if default_action is not None:
            self.default_action = default_action
        if fail_code is not None:
            self.fail_code = fail_code
        if success_code is not None:
            self.success_code = success_code

    @property
    def default_action(self):
        """Gets the default_action of this StatusCodeActionForAddCdnDomainInput.  # noqa: E501


        :return: The default_action of this StatusCodeActionForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._default_action

    @default_action.setter
    def default_action(self, default_action):
        """Sets the default_action of this StatusCodeActionForAddCdnDomainInput.


        :param default_action: The default_action of this StatusCodeActionForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._default_action = default_action

    @property
    def fail_code(self):
        """Gets the fail_code of this StatusCodeActionForAddCdnDomainInput.  # noqa: E501


        :return: The fail_code of this StatusCodeActionForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._fail_code

    @fail_code.setter
    def fail_code(self, fail_code):
        """Sets the fail_code of this StatusCodeActionForAddCdnDomainInput.


        :param fail_code: The fail_code of this StatusCodeActionForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._fail_code = fail_code

    @property
    def success_code(self):
        """Gets the success_code of this StatusCodeActionForAddCdnDomainInput.  # noqa: E501


        :return: The success_code of this StatusCodeActionForAddCdnDomainInput.  # noqa: E501
        :rtype: str
        """
        return self._success_code

    @success_code.setter
    def success_code(self, success_code):
        """Sets the success_code of this StatusCodeActionForAddCdnDomainInput.


        :param success_code: The success_code of this StatusCodeActionForAddCdnDomainInput.  # noqa: E501
        :type: str
        """

        self._success_code = success_code

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatusCodeActionForAddCdnDomainInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatusCodeActionForAddCdnDomainInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatusCodeActionForAddCdnDomainInput):
            return True

        return self.to_dict() != other.to_dict()
