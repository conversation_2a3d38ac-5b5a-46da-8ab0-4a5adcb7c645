# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MediaCutRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'end_time': 'int',
        'media_name': 'str',
        'start_time': 'int',
        'vid': 'str'
    }

    attribute_map = {
        'end_time': 'EndTime',
        'media_name': 'MediaName',
        'start_time': 'StartTime',
        'vid': 'Vid'
    }

    def __init__(self, end_time=None, media_name=None, start_time=None, vid=None, _configuration=None):  # noqa: E501
        """MediaCutRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._end_time = None
        self._media_name = None
        self._start_time = None
        self._vid = None
        self.discriminator = None

        if end_time is not None:
            self.end_time = end_time
        if media_name is not None:
            self.media_name = media_name
        if start_time is not None:
            self.start_time = start_time
        self.vid = vid

    @property
    def end_time(self):
        """Gets the end_time of this MediaCutRequest.  # noqa: E501


        :return: The end_time of this MediaCutRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this MediaCutRequest.


        :param end_time: The end_time of this MediaCutRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def media_name(self):
        """Gets the media_name of this MediaCutRequest.  # noqa: E501


        :return: The media_name of this MediaCutRequest.  # noqa: E501
        :rtype: str
        """
        return self._media_name

    @media_name.setter
    def media_name(self, media_name):
        """Sets the media_name of this MediaCutRequest.


        :param media_name: The media_name of this MediaCutRequest.  # noqa: E501
        :type: str
        """

        self._media_name = media_name

    @property
    def start_time(self):
        """Gets the start_time of this MediaCutRequest.  # noqa: E501


        :return: The start_time of this MediaCutRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this MediaCutRequest.


        :param start_time: The start_time of this MediaCutRequest.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def vid(self):
        """Gets the vid of this MediaCutRequest.  # noqa: E501


        :return: The vid of this MediaCutRequest.  # noqa: E501
        :rtype: str
        """
        return self._vid

    @vid.setter
    def vid(self, vid):
        """Sets the vid of this MediaCutRequest.


        :param vid: The vid of this MediaCutRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vid is None:
            raise ValueError("Invalid value for `vid`, must not be `None`")  # noqa: E501

        self._vid = vid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MediaCutRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MediaCutRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MediaCutRequest):
            return True

        return self.to_dict() != other.to_dict()
