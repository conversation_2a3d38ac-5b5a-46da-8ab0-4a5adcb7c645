# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateResolverRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'endpoint_id': 'int',
        'endpoint_trn': 'str',
        'forward_ips': 'list[ForwardIPForCreateResolverRuleInput]',
        'line': 'str',
        'name': 'str',
        'project_name': 'str',
        'tags': 'list[TagForCreateResolverRuleInput]',
        'type': 'str',
        'vpc_trns': 'list[str]',
        'vpcs': 'list[VpcForCreateResolverRuleInput]',
        'zone_name': 'str'
    }

    attribute_map = {
        'endpoint_id': 'EndpointID',
        'endpoint_trn': 'EndpointTrn',
        'forward_ips': 'ForwardIPs',
        'line': 'Line',
        'name': 'Name',
        'project_name': 'ProjectName',
        'tags': 'Tags',
        'type': 'Type',
        'vpc_trns': 'VpcTrns',
        'vpcs': 'Vpcs',
        'zone_name': 'ZoneName'
    }

    def __init__(self, endpoint_id=None, endpoint_trn=None, forward_ips=None, line=None, name=None, project_name=None, tags=None, type=None, vpc_trns=None, vpcs=None, zone_name=None, _configuration=None):  # noqa: E501
        """CreateResolverRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._endpoint_id = None
        self._endpoint_trn = None
        self._forward_ips = None
        self._line = None
        self._name = None
        self._project_name = None
        self._tags = None
        self._type = None
        self._vpc_trns = None
        self._vpcs = None
        self._zone_name = None
        self.discriminator = None

        if endpoint_id is not None:
            self.endpoint_id = endpoint_id
        if endpoint_trn is not None:
            self.endpoint_trn = endpoint_trn
        if forward_ips is not None:
            self.forward_ips = forward_ips
        if line is not None:
            self.line = line
        self.name = name
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags
        self.type = type
        if vpc_trns is not None:
            self.vpc_trns = vpc_trns
        if vpcs is not None:
            self.vpcs = vpcs
        if zone_name is not None:
            self.zone_name = zone_name

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this CreateResolverRuleRequest.  # noqa: E501


        :return: The endpoint_id of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this CreateResolverRuleRequest.


        :param endpoint_id: The endpoint_id of this CreateResolverRuleRequest.  # noqa: E501
        :type: int
        """

        self._endpoint_id = endpoint_id

    @property
    def endpoint_trn(self):
        """Gets the endpoint_trn of this CreateResolverRuleRequest.  # noqa: E501


        :return: The endpoint_trn of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_trn

    @endpoint_trn.setter
    def endpoint_trn(self, endpoint_trn):
        """Sets the endpoint_trn of this CreateResolverRuleRequest.


        :param endpoint_trn: The endpoint_trn of this CreateResolverRuleRequest.  # noqa: E501
        :type: str
        """

        self._endpoint_trn = endpoint_trn

    @property
    def forward_ips(self):
        """Gets the forward_ips of this CreateResolverRuleRequest.  # noqa: E501


        :return: The forward_ips of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: list[ForwardIPForCreateResolverRuleInput]
        """
        return self._forward_ips

    @forward_ips.setter
    def forward_ips(self, forward_ips):
        """Sets the forward_ips of this CreateResolverRuleRequest.


        :param forward_ips: The forward_ips of this CreateResolverRuleRequest.  # noqa: E501
        :type: list[ForwardIPForCreateResolverRuleInput]
        """

        self._forward_ips = forward_ips

    @property
    def line(self):
        """Gets the line of this CreateResolverRuleRequest.  # noqa: E501


        :return: The line of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._line

    @line.setter
    def line(self, line):
        """Sets the line of this CreateResolverRuleRequest.


        :param line: The line of this CreateResolverRuleRequest.  # noqa: E501
        :type: str
        """

        self._line = line

    @property
    def name(self):
        """Gets the name of this CreateResolverRuleRequest.  # noqa: E501


        :return: The name of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateResolverRuleRequest.


        :param name: The name of this CreateResolverRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this CreateResolverRuleRequest.  # noqa: E501


        :return: The project_name of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateResolverRuleRequest.


        :param project_name: The project_name of this CreateResolverRuleRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateResolverRuleRequest.  # noqa: E501


        :return: The tags of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: list[TagForCreateResolverRuleInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateResolverRuleRequest.


        :param tags: The tags of this CreateResolverRuleRequest.  # noqa: E501
        :type: list[TagForCreateResolverRuleInput]
        """

        self._tags = tags

    @property
    def type(self):
        """Gets the type of this CreateResolverRuleRequest.  # noqa: E501


        :return: The type of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this CreateResolverRuleRequest.


        :param type: The type of this CreateResolverRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    @property
    def vpc_trns(self):
        """Gets the vpc_trns of this CreateResolverRuleRequest.  # noqa: E501


        :return: The vpc_trns of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_trns

    @vpc_trns.setter
    def vpc_trns(self, vpc_trns):
        """Sets the vpc_trns of this CreateResolverRuleRequest.


        :param vpc_trns: The vpc_trns of this CreateResolverRuleRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpc_trns = vpc_trns

    @property
    def vpcs(self):
        """Gets the vpcs of this CreateResolverRuleRequest.  # noqa: E501


        :return: The vpcs of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: list[VpcForCreateResolverRuleInput]
        """
        return self._vpcs

    @vpcs.setter
    def vpcs(self, vpcs):
        """Sets the vpcs of this CreateResolverRuleRequest.


        :param vpcs: The vpcs of this CreateResolverRuleRequest.  # noqa: E501
        :type: list[VpcForCreateResolverRuleInput]
        """

        self._vpcs = vpcs

    @property
    def zone_name(self):
        """Gets the zone_name of this CreateResolverRuleRequest.  # noqa: E501


        :return: The zone_name of this CreateResolverRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this CreateResolverRuleRequest.


        :param zone_name: The zone_name of this CreateResolverRuleRequest.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateResolverRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateResolverRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateResolverRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
