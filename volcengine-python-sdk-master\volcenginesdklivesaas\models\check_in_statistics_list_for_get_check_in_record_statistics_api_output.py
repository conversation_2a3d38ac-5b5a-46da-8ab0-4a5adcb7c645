# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'check_in_id': 'str',
        'check_in_time': 'int',
        'email': 'str',
        'external_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'login_tel': 'str',
        'person_info': 'str',
        'user_agent': 'str',
        'user_id': 'int',
        'user_name': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'check_in_id': 'CheckInID',
        'check_in_time': 'CheckInTime',
        'email': 'Email',
        'external_id': 'ExternalId',
        'extra': 'Extra',
        'ip': 'Ip',
        'login_tel': 'LoginTel',
        'person_info': 'PersonInfo',
        'user_agent': 'UserAgent',
        'user_id': 'UserID',
        'user_name': 'UserName'
    }

    def __init__(self, activity_id=None, check_in_id=None, check_in_time=None, email=None, external_id=None, extra=None, ip=None, login_tel=None, person_info=None, user_agent=None, user_id=None, user_name=None, _configuration=None):  # noqa: E501
        """CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._check_in_id = None
        self._check_in_time = None
        self._email = None
        self._external_id = None
        self._extra = None
        self._ip = None
        self._login_tel = None
        self._person_info = None
        self._user_agent = None
        self._user_id = None
        self._user_name = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if check_in_id is not None:
            self.check_in_id = check_in_id
        if check_in_time is not None:
            self.check_in_time = check_in_time
        if email is not None:
            self.email = email
        if external_id is not None:
            self.external_id = external_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if login_tel is not None:
            self.login_tel = login_tel
        if person_info is not None:
            self.person_info = person_info
        if user_agent is not None:
            self.user_agent = user_agent
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name

    @property
    def activity_id(self):
        """Gets the activity_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The activity_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param activity_id: The activity_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def check_in_id(self):
        """Gets the check_in_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The check_in_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_in_id

    @check_in_id.setter
    def check_in_id(self, check_in_id):
        """Sets the check_in_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param check_in_id: The check_in_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._check_in_id = check_in_id

    @property
    def check_in_time(self):
        """Gets the check_in_time of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The check_in_time of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._check_in_time

    @check_in_time.setter
    def check_in_time(self, check_in_time):
        """Sets the check_in_time of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param check_in_time: The check_in_time of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._check_in_time = check_in_time

    @property
    def email(self):
        """Gets the email of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The email of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param email: The email of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def external_id(self):
        """Gets the external_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The external_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param external_id: The external_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def extra(self):
        """Gets the extra of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The extra of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param extra: The extra of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The ip of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param ip: The ip of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def login_tel(self):
        """Gets the login_tel of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The login_tel of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._login_tel

    @login_tel.setter
    def login_tel(self, login_tel):
        """Sets the login_tel of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param login_tel: The login_tel of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._login_tel = login_tel

    @property
    def person_info(self):
        """Gets the person_info of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The person_info of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._person_info

    @person_info.setter
    def person_info(self, person_info):
        """Sets the person_info of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param person_info: The person_info of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._person_info = person_info

    @property
    def user_agent(self):
        """Gets the user_agent of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The user_agent of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_agent

    @user_agent.setter
    def user_agent(self, user_agent):
        """Sets the user_agent of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param user_agent: The user_agent of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_agent = user_agent

    @property
    def user_id(self):
        """Gets the user_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The user_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param user_id: The user_id of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501


        :return: The user_name of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.


        :param user_name: The user_name of this CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckInStatisticsListForGetCheckInRecordStatisticsAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
