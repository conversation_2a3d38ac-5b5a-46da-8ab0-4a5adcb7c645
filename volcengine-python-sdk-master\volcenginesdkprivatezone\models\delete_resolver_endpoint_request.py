# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteResolverEndpointRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'endpoint_id': 'int',
        'endpoint_trn': 'str'
    }

    attribute_map = {
        'endpoint_id': 'EndpointID',
        'endpoint_trn': 'EndpointTrn'
    }

    def __init__(self, endpoint_id=None, endpoint_trn=None, _configuration=None):  # noqa: E501
        """DeleteResolverEndpointRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._endpoint_id = None
        self._endpoint_trn = None
        self.discriminator = None

        self.endpoint_id = endpoint_id
        if endpoint_trn is not None:
            self.endpoint_trn = endpoint_trn

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this DeleteResolverEndpointRequest.  # noqa: E501


        :return: The endpoint_id of this DeleteResolverEndpointRequest.  # noqa: E501
        :rtype: int
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this DeleteResolverEndpointRequest.


        :param endpoint_id: The endpoint_id of this DeleteResolverEndpointRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and endpoint_id is None:
            raise ValueError("Invalid value for `endpoint_id`, must not be `None`")  # noqa: E501

        self._endpoint_id = endpoint_id

    @property
    def endpoint_trn(self):
        """Gets the endpoint_trn of this DeleteResolverEndpointRequest.  # noqa: E501


        :return: The endpoint_trn of this DeleteResolverEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_trn

    @endpoint_trn.setter
    def endpoint_trn(self, endpoint_trn):
        """Sets the endpoint_trn of this DeleteResolverEndpointRequest.


        :param endpoint_trn: The endpoint_trn of this DeleteResolverEndpointRequest.  # noqa: E501
        :type: str
        """

        self._endpoint_trn = endpoint_trn

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteResolverEndpointRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteResolverEndpointRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteResolverEndpointRequest):
            return True

        return self.to_dict() != other.to_dict()
