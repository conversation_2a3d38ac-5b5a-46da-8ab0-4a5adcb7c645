# coding: utf-8

"""
    vefaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetKafkaTriggerResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'batch_flush_duration_milliseconds': 'int',
        'batch_size': 'int',
        'consumer_group': 'str',
        'creation_time': 'str',
        'description': 'str',
        'enabled': 'bool',
        'function_id': 'str',
        'id': 'str',
        'last_update_time': 'str',
        'maximum_retry_attempts': 'int',
        'mq_instance_id': 'str',
        'name': 'str',
        'starting_position': 'str',
        'status': 'str',
        'topic_name': 'str'
    }

    attribute_map = {
        'batch_flush_duration_milliseconds': 'BatchFlushDurationMilliseconds',
        'batch_size': 'BatchSize',
        'consumer_group': 'ConsumerGroup',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'enabled': 'Enabled',
        'function_id': 'FunctionId',
        'id': 'Id',
        'last_update_time': 'LastUpdateTime',
        'maximum_retry_attempts': 'MaximumRetryAttempts',
        'mq_instance_id': 'MqInstanceId',
        'name': 'Name',
        'starting_position': 'StartingPosition',
        'status': 'Status',
        'topic_name': 'TopicName'
    }

    def __init__(self, batch_flush_duration_milliseconds=None, batch_size=None, consumer_group=None, creation_time=None, description=None, enabled=None, function_id=None, id=None, last_update_time=None, maximum_retry_attempts=None, mq_instance_id=None, name=None, starting_position=None, status=None, topic_name=None, _configuration=None):  # noqa: E501
        """GetKafkaTriggerResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._batch_flush_duration_milliseconds = None
        self._batch_size = None
        self._consumer_group = None
        self._creation_time = None
        self._description = None
        self._enabled = None
        self._function_id = None
        self._id = None
        self._last_update_time = None
        self._maximum_retry_attempts = None
        self._mq_instance_id = None
        self._name = None
        self._starting_position = None
        self._status = None
        self._topic_name = None
        self.discriminator = None

        if batch_flush_duration_milliseconds is not None:
            self.batch_flush_duration_milliseconds = batch_flush_duration_milliseconds
        if batch_size is not None:
            self.batch_size = batch_size
        if consumer_group is not None:
            self.consumer_group = consumer_group
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if enabled is not None:
            self.enabled = enabled
        if function_id is not None:
            self.function_id = function_id
        if id is not None:
            self.id = id
        if last_update_time is not None:
            self.last_update_time = last_update_time
        if maximum_retry_attempts is not None:
            self.maximum_retry_attempts = maximum_retry_attempts
        if mq_instance_id is not None:
            self.mq_instance_id = mq_instance_id
        if name is not None:
            self.name = name
        if starting_position is not None:
            self.starting_position = starting_position
        if status is not None:
            self.status = status
        if topic_name is not None:
            self.topic_name = topic_name

    @property
    def batch_flush_duration_milliseconds(self):
        """Gets the batch_flush_duration_milliseconds of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The batch_flush_duration_milliseconds of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: int
        """
        return self._batch_flush_duration_milliseconds

    @batch_flush_duration_milliseconds.setter
    def batch_flush_duration_milliseconds(self, batch_flush_duration_milliseconds):
        """Sets the batch_flush_duration_milliseconds of this GetKafkaTriggerResponse.


        :param batch_flush_duration_milliseconds: The batch_flush_duration_milliseconds of this GetKafkaTriggerResponse.  # noqa: E501
        :type: int
        """

        self._batch_flush_duration_milliseconds = batch_flush_duration_milliseconds

    @property
    def batch_size(self):
        """Gets the batch_size of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The batch_size of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: int
        """
        return self._batch_size

    @batch_size.setter
    def batch_size(self, batch_size):
        """Sets the batch_size of this GetKafkaTriggerResponse.


        :param batch_size: The batch_size of this GetKafkaTriggerResponse.  # noqa: E501
        :type: int
        """

        self._batch_size = batch_size

    @property
    def consumer_group(self):
        """Gets the consumer_group of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The consumer_group of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._consumer_group

    @consumer_group.setter
    def consumer_group(self, consumer_group):
        """Sets the consumer_group of this GetKafkaTriggerResponse.


        :param consumer_group: The consumer_group of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._consumer_group = consumer_group

    @property
    def creation_time(self):
        """Gets the creation_time of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The creation_time of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this GetKafkaTriggerResponse.


        :param creation_time: The creation_time of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The description of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetKafkaTriggerResponse.


        :param description: The description of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def enabled(self):
        """Gets the enabled of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The enabled of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this GetKafkaTriggerResponse.


        :param enabled: The enabled of this GetKafkaTriggerResponse.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def function_id(self):
        """Gets the function_id of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The function_id of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._function_id

    @function_id.setter
    def function_id(self, function_id):
        """Sets the function_id of this GetKafkaTriggerResponse.


        :param function_id: The function_id of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._function_id = function_id

    @property
    def id(self):
        """Gets the id of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The id of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetKafkaTriggerResponse.


        :param id: The id of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def last_update_time(self):
        """Gets the last_update_time of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The last_update_time of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._last_update_time

    @last_update_time.setter
    def last_update_time(self, last_update_time):
        """Sets the last_update_time of this GetKafkaTriggerResponse.


        :param last_update_time: The last_update_time of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._last_update_time = last_update_time

    @property
    def maximum_retry_attempts(self):
        """Gets the maximum_retry_attempts of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The maximum_retry_attempts of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: int
        """
        return self._maximum_retry_attempts

    @maximum_retry_attempts.setter
    def maximum_retry_attempts(self, maximum_retry_attempts):
        """Sets the maximum_retry_attempts of this GetKafkaTriggerResponse.


        :param maximum_retry_attempts: The maximum_retry_attempts of this GetKafkaTriggerResponse.  # noqa: E501
        :type: int
        """

        self._maximum_retry_attempts = maximum_retry_attempts

    @property
    def mq_instance_id(self):
        """Gets the mq_instance_id of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The mq_instance_id of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._mq_instance_id

    @mq_instance_id.setter
    def mq_instance_id(self, mq_instance_id):
        """Sets the mq_instance_id of this GetKafkaTriggerResponse.


        :param mq_instance_id: The mq_instance_id of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._mq_instance_id = mq_instance_id

    @property
    def name(self):
        """Gets the name of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The name of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetKafkaTriggerResponse.


        :param name: The name of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def starting_position(self):
        """Gets the starting_position of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The starting_position of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._starting_position

    @starting_position.setter
    def starting_position(self, starting_position):
        """Sets the starting_position of this GetKafkaTriggerResponse.


        :param starting_position: The starting_position of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._starting_position = starting_position

    @property
    def status(self):
        """Gets the status of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The status of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetKafkaTriggerResponse.


        :param status: The status of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def topic_name(self):
        """Gets the topic_name of this GetKafkaTriggerResponse.  # noqa: E501


        :return: The topic_name of this GetKafkaTriggerResponse.  # noqa: E501
        :rtype: str
        """
        return self._topic_name

    @topic_name.setter
    def topic_name(self, topic_name):
        """Sets the topic_name of this GetKafkaTriggerResponse.


        :param topic_name: The topic_name of this GetKafkaTriggerResponse.  # noqa: E501
        :type: str
        """

        self._topic_name = topic_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetKafkaTriggerResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetKafkaTriggerResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetKafkaTriggerResponse):
            return True

        return self.to_dict() != other.to_dict()
