# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateWorkspaceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'delete_protection_enabled': 'bool',
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'password': 'str',
        'quota': 'QuotaForUpdateWorkspaceInput',
        'username': 'str'
    }

    attribute_map = {
        'delete_protection_enabled': 'DeleteProtectionEnabled',
        'description': 'Description',
        'id': 'Id',
        'name': 'Name',
        'password': 'Password',
        'quota': 'Quota',
        'username': 'Username'
    }

    def __init__(self, delete_protection_enabled=None, description=None, id=None, name=None, password=None, quota=None, username=None, _configuration=None):  # noqa: E501
        """UpdateWorkspaceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._delete_protection_enabled = None
        self._description = None
        self._id = None
        self._name = None
        self._password = None
        self._quota = None
        self._username = None
        self.discriminator = None

        if delete_protection_enabled is not None:
            self.delete_protection_enabled = delete_protection_enabled
        if description is not None:
            self.description = description
        self.id = id
        if name is not None:
            self.name = name
        if password is not None:
            self.password = password
        if quota is not None:
            self.quota = quota
        if username is not None:
            self.username = username

    @property
    def delete_protection_enabled(self):
        """Gets the delete_protection_enabled of this UpdateWorkspaceRequest.  # noqa: E501


        :return: The delete_protection_enabled of this UpdateWorkspaceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._delete_protection_enabled

    @delete_protection_enabled.setter
    def delete_protection_enabled(self, delete_protection_enabled):
        """Sets the delete_protection_enabled of this UpdateWorkspaceRequest.


        :param delete_protection_enabled: The delete_protection_enabled of this UpdateWorkspaceRequest.  # noqa: E501
        :type: bool
        """

        self._delete_protection_enabled = delete_protection_enabled

    @property
    def description(self):
        """Gets the description of this UpdateWorkspaceRequest.  # noqa: E501


        :return: The description of this UpdateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateWorkspaceRequest.


        :param description: The description of this UpdateWorkspaceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this UpdateWorkspaceRequest.  # noqa: E501


        :return: The id of this UpdateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this UpdateWorkspaceRequest.


        :param id: The id of this UpdateWorkspaceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and id is None:
            raise ValueError("Invalid value for `id`, must not be `None`")  # noqa: E501

        self._id = id

    @property
    def name(self):
        """Gets the name of this UpdateWorkspaceRequest.  # noqa: E501


        :return: The name of this UpdateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this UpdateWorkspaceRequest.


        :param name: The name of this UpdateWorkspaceRequest.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def password(self):
        """Gets the password of this UpdateWorkspaceRequest.  # noqa: E501


        :return: The password of this UpdateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._password

    @password.setter
    def password(self, password):
        """Sets the password of this UpdateWorkspaceRequest.


        :param password: The password of this UpdateWorkspaceRequest.  # noqa: E501
        :type: str
        """

        self._password = password

    @property
    def quota(self):
        """Gets the quota of this UpdateWorkspaceRequest.  # noqa: E501


        :return: The quota of this UpdateWorkspaceRequest.  # noqa: E501
        :rtype: QuotaForUpdateWorkspaceInput
        """
        return self._quota

    @quota.setter
    def quota(self, quota):
        """Sets the quota of this UpdateWorkspaceRequest.


        :param quota: The quota of this UpdateWorkspaceRequest.  # noqa: E501
        :type: QuotaForUpdateWorkspaceInput
        """

        self._quota = quota

    @property
    def username(self):
        """Gets the username of this UpdateWorkspaceRequest.  # noqa: E501


        :return: The username of this UpdateWorkspaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this UpdateWorkspaceRequest.


        :param username: The username of this UpdateWorkspaceRequest.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateWorkspaceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateWorkspaceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateWorkspaceRequest):
            return True

        return self.to_dict() != other.to_dict()
