# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeContentQuotaResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'block_limit': 'int',
        'block_quota': 'int',
        'block_remain': 'int',
        'preload_limit': 'int',
        'preload_quota': 'int',
        'preload_remain': 'int',
        'refresh_dir_limit': 'int',
        'refresh_dir_quota': 'int',
        'refresh_dir_remain': 'int',
        'refresh_quota': 'int',
        'refresh_quota_limit': 'int',
        'refresh_regex_limit': 'int',
        'refresh_regex_quota': 'int',
        'refresh_regex_remain': 'int',
        'refresh_remain': 'int',
        'unblock_limit': 'int',
        'unblock_quota': 'int',
        'unblock_remain': 'int'
    }

    attribute_map = {
        'block_limit': 'BlockLimit',
        'block_quota': 'BlockQuota',
        'block_remain': 'BlockRemain',
        'preload_limit': 'PreloadLimit',
        'preload_quota': 'PreloadQuota',
        'preload_remain': 'PreloadRemain',
        'refresh_dir_limit': 'RefreshDirLimit',
        'refresh_dir_quota': 'RefreshDirQuota',
        'refresh_dir_remain': 'RefreshDirRemain',
        'refresh_quota': 'RefreshQuota',
        'refresh_quota_limit': 'RefreshQuotaLimit',
        'refresh_regex_limit': 'RefreshRegexLimit',
        'refresh_regex_quota': 'RefreshRegexQuota',
        'refresh_regex_remain': 'RefreshRegexRemain',
        'refresh_remain': 'RefreshRemain',
        'unblock_limit': 'UnblockLimit',
        'unblock_quota': 'UnblockQuota',
        'unblock_remain': 'UnblockRemain'
    }

    def __init__(self, block_limit=None, block_quota=None, block_remain=None, preload_limit=None, preload_quota=None, preload_remain=None, refresh_dir_limit=None, refresh_dir_quota=None, refresh_dir_remain=None, refresh_quota=None, refresh_quota_limit=None, refresh_regex_limit=None, refresh_regex_quota=None, refresh_regex_remain=None, refresh_remain=None, unblock_limit=None, unblock_quota=None, unblock_remain=None, _configuration=None):  # noqa: E501
        """DescribeContentQuotaResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._block_limit = None
        self._block_quota = None
        self._block_remain = None
        self._preload_limit = None
        self._preload_quota = None
        self._preload_remain = None
        self._refresh_dir_limit = None
        self._refresh_dir_quota = None
        self._refresh_dir_remain = None
        self._refresh_quota = None
        self._refresh_quota_limit = None
        self._refresh_regex_limit = None
        self._refresh_regex_quota = None
        self._refresh_regex_remain = None
        self._refresh_remain = None
        self._unblock_limit = None
        self._unblock_quota = None
        self._unblock_remain = None
        self.discriminator = None

        if block_limit is not None:
            self.block_limit = block_limit
        if block_quota is not None:
            self.block_quota = block_quota
        if block_remain is not None:
            self.block_remain = block_remain
        if preload_limit is not None:
            self.preload_limit = preload_limit
        if preload_quota is not None:
            self.preload_quota = preload_quota
        if preload_remain is not None:
            self.preload_remain = preload_remain
        if refresh_dir_limit is not None:
            self.refresh_dir_limit = refresh_dir_limit
        if refresh_dir_quota is not None:
            self.refresh_dir_quota = refresh_dir_quota
        if refresh_dir_remain is not None:
            self.refresh_dir_remain = refresh_dir_remain
        if refresh_quota is not None:
            self.refresh_quota = refresh_quota
        if refresh_quota_limit is not None:
            self.refresh_quota_limit = refresh_quota_limit
        if refresh_regex_limit is not None:
            self.refresh_regex_limit = refresh_regex_limit
        if refresh_regex_quota is not None:
            self.refresh_regex_quota = refresh_regex_quota
        if refresh_regex_remain is not None:
            self.refresh_regex_remain = refresh_regex_remain
        if refresh_remain is not None:
            self.refresh_remain = refresh_remain
        if unblock_limit is not None:
            self.unblock_limit = unblock_limit
        if unblock_quota is not None:
            self.unblock_quota = unblock_quota
        if unblock_remain is not None:
            self.unblock_remain = unblock_remain

    @property
    def block_limit(self):
        """Gets the block_limit of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The block_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._block_limit

    @block_limit.setter
    def block_limit(self, block_limit):
        """Sets the block_limit of this DescribeContentQuotaResponse.


        :param block_limit: The block_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._block_limit = block_limit

    @property
    def block_quota(self):
        """Gets the block_quota of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The block_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._block_quota

    @block_quota.setter
    def block_quota(self, block_quota):
        """Sets the block_quota of this DescribeContentQuotaResponse.


        :param block_quota: The block_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._block_quota = block_quota

    @property
    def block_remain(self):
        """Gets the block_remain of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The block_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._block_remain

    @block_remain.setter
    def block_remain(self, block_remain):
        """Sets the block_remain of this DescribeContentQuotaResponse.


        :param block_remain: The block_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._block_remain = block_remain

    @property
    def preload_limit(self):
        """Gets the preload_limit of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The preload_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._preload_limit

    @preload_limit.setter
    def preload_limit(self, preload_limit):
        """Sets the preload_limit of this DescribeContentQuotaResponse.


        :param preload_limit: The preload_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._preload_limit = preload_limit

    @property
    def preload_quota(self):
        """Gets the preload_quota of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The preload_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._preload_quota

    @preload_quota.setter
    def preload_quota(self, preload_quota):
        """Sets the preload_quota of this DescribeContentQuotaResponse.


        :param preload_quota: The preload_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._preload_quota = preload_quota

    @property
    def preload_remain(self):
        """Gets the preload_remain of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The preload_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._preload_remain

    @preload_remain.setter
    def preload_remain(self, preload_remain):
        """Sets the preload_remain of this DescribeContentQuotaResponse.


        :param preload_remain: The preload_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._preload_remain = preload_remain

    @property
    def refresh_dir_limit(self):
        """Gets the refresh_dir_limit of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The refresh_dir_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._refresh_dir_limit

    @refresh_dir_limit.setter
    def refresh_dir_limit(self, refresh_dir_limit):
        """Sets the refresh_dir_limit of this DescribeContentQuotaResponse.


        :param refresh_dir_limit: The refresh_dir_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._refresh_dir_limit = refresh_dir_limit

    @property
    def refresh_dir_quota(self):
        """Gets the refresh_dir_quota of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The refresh_dir_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._refresh_dir_quota

    @refresh_dir_quota.setter
    def refresh_dir_quota(self, refresh_dir_quota):
        """Sets the refresh_dir_quota of this DescribeContentQuotaResponse.


        :param refresh_dir_quota: The refresh_dir_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._refresh_dir_quota = refresh_dir_quota

    @property
    def refresh_dir_remain(self):
        """Gets the refresh_dir_remain of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The refresh_dir_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._refresh_dir_remain

    @refresh_dir_remain.setter
    def refresh_dir_remain(self, refresh_dir_remain):
        """Sets the refresh_dir_remain of this DescribeContentQuotaResponse.


        :param refresh_dir_remain: The refresh_dir_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._refresh_dir_remain = refresh_dir_remain

    @property
    def refresh_quota(self):
        """Gets the refresh_quota of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The refresh_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._refresh_quota

    @refresh_quota.setter
    def refresh_quota(self, refresh_quota):
        """Sets the refresh_quota of this DescribeContentQuotaResponse.


        :param refresh_quota: The refresh_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._refresh_quota = refresh_quota

    @property
    def refresh_quota_limit(self):
        """Gets the refresh_quota_limit of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The refresh_quota_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._refresh_quota_limit

    @refresh_quota_limit.setter
    def refresh_quota_limit(self, refresh_quota_limit):
        """Sets the refresh_quota_limit of this DescribeContentQuotaResponse.


        :param refresh_quota_limit: The refresh_quota_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._refresh_quota_limit = refresh_quota_limit

    @property
    def refresh_regex_limit(self):
        """Gets the refresh_regex_limit of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The refresh_regex_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._refresh_regex_limit

    @refresh_regex_limit.setter
    def refresh_regex_limit(self, refresh_regex_limit):
        """Sets the refresh_regex_limit of this DescribeContentQuotaResponse.


        :param refresh_regex_limit: The refresh_regex_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._refresh_regex_limit = refresh_regex_limit

    @property
    def refresh_regex_quota(self):
        """Gets the refresh_regex_quota of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The refresh_regex_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._refresh_regex_quota

    @refresh_regex_quota.setter
    def refresh_regex_quota(self, refresh_regex_quota):
        """Sets the refresh_regex_quota of this DescribeContentQuotaResponse.


        :param refresh_regex_quota: The refresh_regex_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._refresh_regex_quota = refresh_regex_quota

    @property
    def refresh_regex_remain(self):
        """Gets the refresh_regex_remain of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The refresh_regex_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._refresh_regex_remain

    @refresh_regex_remain.setter
    def refresh_regex_remain(self, refresh_regex_remain):
        """Sets the refresh_regex_remain of this DescribeContentQuotaResponse.


        :param refresh_regex_remain: The refresh_regex_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._refresh_regex_remain = refresh_regex_remain

    @property
    def refresh_remain(self):
        """Gets the refresh_remain of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The refresh_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._refresh_remain

    @refresh_remain.setter
    def refresh_remain(self, refresh_remain):
        """Sets the refresh_remain of this DescribeContentQuotaResponse.


        :param refresh_remain: The refresh_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._refresh_remain = refresh_remain

    @property
    def unblock_limit(self):
        """Gets the unblock_limit of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The unblock_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._unblock_limit

    @unblock_limit.setter
    def unblock_limit(self, unblock_limit):
        """Sets the unblock_limit of this DescribeContentQuotaResponse.


        :param unblock_limit: The unblock_limit of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._unblock_limit = unblock_limit

    @property
    def unblock_quota(self):
        """Gets the unblock_quota of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The unblock_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._unblock_quota

    @unblock_quota.setter
    def unblock_quota(self, unblock_quota):
        """Sets the unblock_quota of this DescribeContentQuotaResponse.


        :param unblock_quota: The unblock_quota of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._unblock_quota = unblock_quota

    @property
    def unblock_remain(self):
        """Gets the unblock_remain of this DescribeContentQuotaResponse.  # noqa: E501


        :return: The unblock_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :rtype: int
        """
        return self._unblock_remain

    @unblock_remain.setter
    def unblock_remain(self, unblock_remain):
        """Sets the unblock_remain of this DescribeContentQuotaResponse.


        :param unblock_remain: The unblock_remain of this DescribeContentQuotaResponse.  # noqa: E501
        :type: int
        """

        self._unblock_remain = unblock_remain

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeContentQuotaResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeContentQuotaResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeContentQuotaResponse):
            return True

        return self.to_dict() != other.to_dict()
