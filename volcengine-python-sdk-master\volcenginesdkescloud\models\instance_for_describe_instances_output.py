# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceForDescribeInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cerebro_enabled': 'bool',
        'cerebro_private_domain': 'str',
        'cerebro_public_domain': 'str',
        'charge_enabled': 'bool',
        'cluster_id': 'str',
        'create_time': 'str',
        'deletion_protection': 'bool',
        'es_eip': 'str',
        'es_eip_id': 'str',
        'es_private_endpoint': 'str',
        'es_private_ip_allow_list': 'str',
        'es_private_ip_whitelist': 'str',
        'es_public_endpoint': 'str',
        'es_public_ip_allow_list': 'str',
        'es_public_ip_whitelist': 'str',
        'enable_es_private_domain_public': 'bool',
        'enable_es_private_network': 'bool',
        'enable_es_public_network': 'bool',
        'enable_kibana_private_domain_public': 'bool',
        'enable_kibana_private_network': 'bool',
        'enable_kibana_public_network': 'bool',
        'expire_date': 'str',
        'instance_configuration': 'InstanceConfigurationForDescribeInstancesOutput',
        'instance_id': 'str',
        'kibana_config': 'KibanaConfigForDescribeInstancesOutput',
        'kibana_eip': 'str',
        'kibana_eip_id': 'str',
        'kibana_private_domain': 'str',
        'kibana_private_ip_allow_list': 'str',
        'kibana_private_ip_whitelist': 'str',
        'kibana_public_domain': 'str',
        'kibana_public_ip_allow_list': 'str',
        'kibana_public_ip_whitelist': 'str',
        'main_zone_id': 'str',
        'maintenance_day': 'list[str]',
        'maintenance_time': 'str',
        'resource_tags': 'list[ResourceTagForDescribeInstancesOutput]',
        'status': 'str',
        'sub_instance_enable': 'str',
        'sub_instances': 'list[SubInstanceForDescribeInstancesOutput]',
        'support_cold_node': 'bool',
        'total_nodes': 'int',
        'transfer_info': 'TransferInfoForDescribeInstancesOutput',
        'user_id': 'str'
    }

    attribute_map = {
        'cerebro_enabled': 'CerebroEnabled',
        'cerebro_private_domain': 'CerebroPrivateDomain',
        'cerebro_public_domain': 'CerebroPublicDomain',
        'charge_enabled': 'ChargeEnabled',
        'cluster_id': 'ClusterId',
        'create_time': 'CreateTime',
        'deletion_protection': 'DeletionProtection',
        'es_eip': 'ESEip',
        'es_eip_id': 'ESEipId',
        'es_private_endpoint': 'ESPrivateEndpoint',
        'es_private_ip_allow_list': 'ESPrivateIpAllowList',
        'es_private_ip_whitelist': 'ESPrivateIpWhitelist',
        'es_public_endpoint': 'ESPublicEndpoint',
        'es_public_ip_allow_list': 'ESPublicIpAllowList',
        'es_public_ip_whitelist': 'ESPublicIpWhitelist',
        'enable_es_private_domain_public': 'EnableESPrivateDomainPublic',
        'enable_es_private_network': 'EnableESPrivateNetwork',
        'enable_es_public_network': 'EnableESPublicNetwork',
        'enable_kibana_private_domain_public': 'EnableKibanaPrivateDomainPublic',
        'enable_kibana_private_network': 'EnableKibanaPrivateNetwork',
        'enable_kibana_public_network': 'EnableKibanaPublicNetwork',
        'expire_date': 'ExpireDate',
        'instance_configuration': 'InstanceConfiguration',
        'instance_id': 'InstanceId',
        'kibana_config': 'KibanaConfig',
        'kibana_eip': 'KibanaEip',
        'kibana_eip_id': 'KibanaEipId',
        'kibana_private_domain': 'KibanaPrivateDomain',
        'kibana_private_ip_allow_list': 'KibanaPrivateIpAllowList',
        'kibana_private_ip_whitelist': 'KibanaPrivateIpWhitelist',
        'kibana_public_domain': 'KibanaPublicDomain',
        'kibana_public_ip_allow_list': 'KibanaPublicIpAllowList',
        'kibana_public_ip_whitelist': 'KibanaPublicIpWhitelist',
        'main_zone_id': 'MainZoneId',
        'maintenance_day': 'MaintenanceDay',
        'maintenance_time': 'MaintenanceTime',
        'resource_tags': 'ResourceTags',
        'status': 'Status',
        'sub_instance_enable': 'SubInstanceEnable',
        'sub_instances': 'SubInstances',
        'support_cold_node': 'SupportColdNode',
        'total_nodes': 'TotalNodes',
        'transfer_info': 'TransferInfo',
        'user_id': 'UserId'
    }

    def __init__(self, cerebro_enabled=None, cerebro_private_domain=None, cerebro_public_domain=None, charge_enabled=None, cluster_id=None, create_time=None, deletion_protection=None, es_eip=None, es_eip_id=None, es_private_endpoint=None, es_private_ip_allow_list=None, es_private_ip_whitelist=None, es_public_endpoint=None, es_public_ip_allow_list=None, es_public_ip_whitelist=None, enable_es_private_domain_public=None, enable_es_private_network=None, enable_es_public_network=None, enable_kibana_private_domain_public=None, enable_kibana_private_network=None, enable_kibana_public_network=None, expire_date=None, instance_configuration=None, instance_id=None, kibana_config=None, kibana_eip=None, kibana_eip_id=None, kibana_private_domain=None, kibana_private_ip_allow_list=None, kibana_private_ip_whitelist=None, kibana_public_domain=None, kibana_public_ip_allow_list=None, kibana_public_ip_whitelist=None, main_zone_id=None, maintenance_day=None, maintenance_time=None, resource_tags=None, status=None, sub_instance_enable=None, sub_instances=None, support_cold_node=None, total_nodes=None, transfer_info=None, user_id=None, _configuration=None):  # noqa: E501
        """InstanceForDescribeInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cerebro_enabled = None
        self._cerebro_private_domain = None
        self._cerebro_public_domain = None
        self._charge_enabled = None
        self._cluster_id = None
        self._create_time = None
        self._deletion_protection = None
        self._es_eip = None
        self._es_eip_id = None
        self._es_private_endpoint = None
        self._es_private_ip_allow_list = None
        self._es_private_ip_whitelist = None
        self._es_public_endpoint = None
        self._es_public_ip_allow_list = None
        self._es_public_ip_whitelist = None
        self._enable_es_private_domain_public = None
        self._enable_es_private_network = None
        self._enable_es_public_network = None
        self._enable_kibana_private_domain_public = None
        self._enable_kibana_private_network = None
        self._enable_kibana_public_network = None
        self._expire_date = None
        self._instance_configuration = None
        self._instance_id = None
        self._kibana_config = None
        self._kibana_eip = None
        self._kibana_eip_id = None
        self._kibana_private_domain = None
        self._kibana_private_ip_allow_list = None
        self._kibana_private_ip_whitelist = None
        self._kibana_public_domain = None
        self._kibana_public_ip_allow_list = None
        self._kibana_public_ip_whitelist = None
        self._main_zone_id = None
        self._maintenance_day = None
        self._maintenance_time = None
        self._resource_tags = None
        self._status = None
        self._sub_instance_enable = None
        self._sub_instances = None
        self._support_cold_node = None
        self._total_nodes = None
        self._transfer_info = None
        self._user_id = None
        self.discriminator = None

        if cerebro_enabled is not None:
            self.cerebro_enabled = cerebro_enabled
        if cerebro_private_domain is not None:
            self.cerebro_private_domain = cerebro_private_domain
        if cerebro_public_domain is not None:
            self.cerebro_public_domain = cerebro_public_domain
        if charge_enabled is not None:
            self.charge_enabled = charge_enabled
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if create_time is not None:
            self.create_time = create_time
        if deletion_protection is not None:
            self.deletion_protection = deletion_protection
        if es_eip is not None:
            self.es_eip = es_eip
        if es_eip_id is not None:
            self.es_eip_id = es_eip_id
        if es_private_endpoint is not None:
            self.es_private_endpoint = es_private_endpoint
        if es_private_ip_allow_list is not None:
            self.es_private_ip_allow_list = es_private_ip_allow_list
        if es_private_ip_whitelist is not None:
            self.es_private_ip_whitelist = es_private_ip_whitelist
        if es_public_endpoint is not None:
            self.es_public_endpoint = es_public_endpoint
        if es_public_ip_allow_list is not None:
            self.es_public_ip_allow_list = es_public_ip_allow_list
        if es_public_ip_whitelist is not None:
            self.es_public_ip_whitelist = es_public_ip_whitelist
        if enable_es_private_domain_public is not None:
            self.enable_es_private_domain_public = enable_es_private_domain_public
        if enable_es_private_network is not None:
            self.enable_es_private_network = enable_es_private_network
        if enable_es_public_network is not None:
            self.enable_es_public_network = enable_es_public_network
        if enable_kibana_private_domain_public is not None:
            self.enable_kibana_private_domain_public = enable_kibana_private_domain_public
        if enable_kibana_private_network is not None:
            self.enable_kibana_private_network = enable_kibana_private_network
        if enable_kibana_public_network is not None:
            self.enable_kibana_public_network = enable_kibana_public_network
        if expire_date is not None:
            self.expire_date = expire_date
        if instance_configuration is not None:
            self.instance_configuration = instance_configuration
        if instance_id is not None:
            self.instance_id = instance_id
        if kibana_config is not None:
            self.kibana_config = kibana_config
        if kibana_eip is not None:
            self.kibana_eip = kibana_eip
        if kibana_eip_id is not None:
            self.kibana_eip_id = kibana_eip_id
        if kibana_private_domain is not None:
            self.kibana_private_domain = kibana_private_domain
        if kibana_private_ip_allow_list is not None:
            self.kibana_private_ip_allow_list = kibana_private_ip_allow_list
        if kibana_private_ip_whitelist is not None:
            self.kibana_private_ip_whitelist = kibana_private_ip_whitelist
        if kibana_public_domain is not None:
            self.kibana_public_domain = kibana_public_domain
        if kibana_public_ip_allow_list is not None:
            self.kibana_public_ip_allow_list = kibana_public_ip_allow_list
        if kibana_public_ip_whitelist is not None:
            self.kibana_public_ip_whitelist = kibana_public_ip_whitelist
        if main_zone_id is not None:
            self.main_zone_id = main_zone_id
        if maintenance_day is not None:
            self.maintenance_day = maintenance_day
        if maintenance_time is not None:
            self.maintenance_time = maintenance_time
        if resource_tags is not None:
            self.resource_tags = resource_tags
        if status is not None:
            self.status = status
        if sub_instance_enable is not None:
            self.sub_instance_enable = sub_instance_enable
        if sub_instances is not None:
            self.sub_instances = sub_instances
        if support_cold_node is not None:
            self.support_cold_node = support_cold_node
        if total_nodes is not None:
            self.total_nodes = total_nodes
        if transfer_info is not None:
            self.transfer_info = transfer_info
        if user_id is not None:
            self.user_id = user_id

    @property
    def cerebro_enabled(self):
        """Gets the cerebro_enabled of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The cerebro_enabled of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._cerebro_enabled

    @cerebro_enabled.setter
    def cerebro_enabled(self, cerebro_enabled):
        """Sets the cerebro_enabled of this InstanceForDescribeInstancesOutput.


        :param cerebro_enabled: The cerebro_enabled of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._cerebro_enabled = cerebro_enabled

    @property
    def cerebro_private_domain(self):
        """Gets the cerebro_private_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The cerebro_private_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._cerebro_private_domain

    @cerebro_private_domain.setter
    def cerebro_private_domain(self, cerebro_private_domain):
        """Sets the cerebro_private_domain of this InstanceForDescribeInstancesOutput.


        :param cerebro_private_domain: The cerebro_private_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._cerebro_private_domain = cerebro_private_domain

    @property
    def cerebro_public_domain(self):
        """Gets the cerebro_public_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The cerebro_public_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._cerebro_public_domain

    @cerebro_public_domain.setter
    def cerebro_public_domain(self, cerebro_public_domain):
        """Sets the cerebro_public_domain of this InstanceForDescribeInstancesOutput.


        :param cerebro_public_domain: The cerebro_public_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._cerebro_public_domain = cerebro_public_domain

    @property
    def charge_enabled(self):
        """Gets the charge_enabled of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The charge_enabled of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._charge_enabled

    @charge_enabled.setter
    def charge_enabled(self, charge_enabled):
        """Sets the charge_enabled of this InstanceForDescribeInstancesOutput.


        :param charge_enabled: The charge_enabled of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._charge_enabled = charge_enabled

    @property
    def cluster_id(self):
        """Gets the cluster_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The cluster_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this InstanceForDescribeInstancesOutput.


        :param cluster_id: The cluster_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def create_time(self):
        """Gets the create_time of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The create_time of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this InstanceForDescribeInstancesOutput.


        :param create_time: The create_time of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def deletion_protection(self):
        """Gets the deletion_protection of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The deletion_protection of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._deletion_protection

    @deletion_protection.setter
    def deletion_protection(self, deletion_protection):
        """Sets the deletion_protection of this InstanceForDescribeInstancesOutput.


        :param deletion_protection: The deletion_protection of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._deletion_protection = deletion_protection

    @property
    def es_eip(self):
        """Gets the es_eip of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The es_eip of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._es_eip

    @es_eip.setter
    def es_eip(self, es_eip):
        """Sets the es_eip of this InstanceForDescribeInstancesOutput.


        :param es_eip: The es_eip of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._es_eip = es_eip

    @property
    def es_eip_id(self):
        """Gets the es_eip_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The es_eip_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._es_eip_id

    @es_eip_id.setter
    def es_eip_id(self, es_eip_id):
        """Sets the es_eip_id of this InstanceForDescribeInstancesOutput.


        :param es_eip_id: The es_eip_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._es_eip_id = es_eip_id

    @property
    def es_private_endpoint(self):
        """Gets the es_private_endpoint of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The es_private_endpoint of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._es_private_endpoint

    @es_private_endpoint.setter
    def es_private_endpoint(self, es_private_endpoint):
        """Sets the es_private_endpoint of this InstanceForDescribeInstancesOutput.


        :param es_private_endpoint: The es_private_endpoint of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._es_private_endpoint = es_private_endpoint

    @property
    def es_private_ip_allow_list(self):
        """Gets the es_private_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The es_private_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._es_private_ip_allow_list

    @es_private_ip_allow_list.setter
    def es_private_ip_allow_list(self, es_private_ip_allow_list):
        """Sets the es_private_ip_allow_list of this InstanceForDescribeInstancesOutput.


        :param es_private_ip_allow_list: The es_private_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._es_private_ip_allow_list = es_private_ip_allow_list

    @property
    def es_private_ip_whitelist(self):
        """Gets the es_private_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The es_private_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._es_private_ip_whitelist

    @es_private_ip_whitelist.setter
    def es_private_ip_whitelist(self, es_private_ip_whitelist):
        """Sets the es_private_ip_whitelist of this InstanceForDescribeInstancesOutput.


        :param es_private_ip_whitelist: The es_private_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._es_private_ip_whitelist = es_private_ip_whitelist

    @property
    def es_public_endpoint(self):
        """Gets the es_public_endpoint of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The es_public_endpoint of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._es_public_endpoint

    @es_public_endpoint.setter
    def es_public_endpoint(self, es_public_endpoint):
        """Sets the es_public_endpoint of this InstanceForDescribeInstancesOutput.


        :param es_public_endpoint: The es_public_endpoint of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._es_public_endpoint = es_public_endpoint

    @property
    def es_public_ip_allow_list(self):
        """Gets the es_public_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The es_public_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._es_public_ip_allow_list

    @es_public_ip_allow_list.setter
    def es_public_ip_allow_list(self, es_public_ip_allow_list):
        """Sets the es_public_ip_allow_list of this InstanceForDescribeInstancesOutput.


        :param es_public_ip_allow_list: The es_public_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._es_public_ip_allow_list = es_public_ip_allow_list

    @property
    def es_public_ip_whitelist(self):
        """Gets the es_public_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The es_public_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._es_public_ip_whitelist

    @es_public_ip_whitelist.setter
    def es_public_ip_whitelist(self, es_public_ip_whitelist):
        """Sets the es_public_ip_whitelist of this InstanceForDescribeInstancesOutput.


        :param es_public_ip_whitelist: The es_public_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._es_public_ip_whitelist = es_public_ip_whitelist

    @property
    def enable_es_private_domain_public(self):
        """Gets the enable_es_private_domain_public of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The enable_es_private_domain_public of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_es_private_domain_public

    @enable_es_private_domain_public.setter
    def enable_es_private_domain_public(self, enable_es_private_domain_public):
        """Sets the enable_es_private_domain_public of this InstanceForDescribeInstancesOutput.


        :param enable_es_private_domain_public: The enable_es_private_domain_public of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._enable_es_private_domain_public = enable_es_private_domain_public

    @property
    def enable_es_private_network(self):
        """Gets the enable_es_private_network of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The enable_es_private_network of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_es_private_network

    @enable_es_private_network.setter
    def enable_es_private_network(self, enable_es_private_network):
        """Sets the enable_es_private_network of this InstanceForDescribeInstancesOutput.


        :param enable_es_private_network: The enable_es_private_network of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._enable_es_private_network = enable_es_private_network

    @property
    def enable_es_public_network(self):
        """Gets the enable_es_public_network of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The enable_es_public_network of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_es_public_network

    @enable_es_public_network.setter
    def enable_es_public_network(self, enable_es_public_network):
        """Sets the enable_es_public_network of this InstanceForDescribeInstancesOutput.


        :param enable_es_public_network: The enable_es_public_network of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._enable_es_public_network = enable_es_public_network

    @property
    def enable_kibana_private_domain_public(self):
        """Gets the enable_kibana_private_domain_public of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The enable_kibana_private_domain_public of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_kibana_private_domain_public

    @enable_kibana_private_domain_public.setter
    def enable_kibana_private_domain_public(self, enable_kibana_private_domain_public):
        """Sets the enable_kibana_private_domain_public of this InstanceForDescribeInstancesOutput.


        :param enable_kibana_private_domain_public: The enable_kibana_private_domain_public of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._enable_kibana_private_domain_public = enable_kibana_private_domain_public

    @property
    def enable_kibana_private_network(self):
        """Gets the enable_kibana_private_network of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The enable_kibana_private_network of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_kibana_private_network

    @enable_kibana_private_network.setter
    def enable_kibana_private_network(self, enable_kibana_private_network):
        """Sets the enable_kibana_private_network of this InstanceForDescribeInstancesOutput.


        :param enable_kibana_private_network: The enable_kibana_private_network of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._enable_kibana_private_network = enable_kibana_private_network

    @property
    def enable_kibana_public_network(self):
        """Gets the enable_kibana_public_network of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The enable_kibana_public_network of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_kibana_public_network

    @enable_kibana_public_network.setter
    def enable_kibana_public_network(self, enable_kibana_public_network):
        """Sets the enable_kibana_public_network of this InstanceForDescribeInstancesOutput.


        :param enable_kibana_public_network: The enable_kibana_public_network of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._enable_kibana_public_network = enable_kibana_public_network

    @property
    def expire_date(self):
        """Gets the expire_date of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The expire_date of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._expire_date

    @expire_date.setter
    def expire_date(self, expire_date):
        """Sets the expire_date of this InstanceForDescribeInstancesOutput.


        :param expire_date: The expire_date of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._expire_date = expire_date

    @property
    def instance_configuration(self):
        """Gets the instance_configuration of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_configuration of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: InstanceConfigurationForDescribeInstancesOutput
        """
        return self._instance_configuration

    @instance_configuration.setter
    def instance_configuration(self, instance_configuration):
        """Sets the instance_configuration of this InstanceForDescribeInstancesOutput.


        :param instance_configuration: The instance_configuration of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: InstanceConfigurationForDescribeInstancesOutput
        """

        self._instance_configuration = instance_configuration

    @property
    def instance_id(self):
        """Gets the instance_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The instance_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this InstanceForDescribeInstancesOutput.


        :param instance_id: The instance_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def kibana_config(self):
        """Gets the kibana_config of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The kibana_config of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: KibanaConfigForDescribeInstancesOutput
        """
        return self._kibana_config

    @kibana_config.setter
    def kibana_config(self, kibana_config):
        """Sets the kibana_config of this InstanceForDescribeInstancesOutput.


        :param kibana_config: The kibana_config of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: KibanaConfigForDescribeInstancesOutput
        """

        self._kibana_config = kibana_config

    @property
    def kibana_eip(self):
        """Gets the kibana_eip of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The kibana_eip of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kibana_eip

    @kibana_eip.setter
    def kibana_eip(self, kibana_eip):
        """Sets the kibana_eip of this InstanceForDescribeInstancesOutput.


        :param kibana_eip: The kibana_eip of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._kibana_eip = kibana_eip

    @property
    def kibana_eip_id(self):
        """Gets the kibana_eip_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The kibana_eip_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kibana_eip_id

    @kibana_eip_id.setter
    def kibana_eip_id(self, kibana_eip_id):
        """Sets the kibana_eip_id of this InstanceForDescribeInstancesOutput.


        :param kibana_eip_id: The kibana_eip_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._kibana_eip_id = kibana_eip_id

    @property
    def kibana_private_domain(self):
        """Gets the kibana_private_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The kibana_private_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kibana_private_domain

    @kibana_private_domain.setter
    def kibana_private_domain(self, kibana_private_domain):
        """Sets the kibana_private_domain of this InstanceForDescribeInstancesOutput.


        :param kibana_private_domain: The kibana_private_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._kibana_private_domain = kibana_private_domain

    @property
    def kibana_private_ip_allow_list(self):
        """Gets the kibana_private_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The kibana_private_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kibana_private_ip_allow_list

    @kibana_private_ip_allow_list.setter
    def kibana_private_ip_allow_list(self, kibana_private_ip_allow_list):
        """Sets the kibana_private_ip_allow_list of this InstanceForDescribeInstancesOutput.


        :param kibana_private_ip_allow_list: The kibana_private_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._kibana_private_ip_allow_list = kibana_private_ip_allow_list

    @property
    def kibana_private_ip_whitelist(self):
        """Gets the kibana_private_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The kibana_private_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kibana_private_ip_whitelist

    @kibana_private_ip_whitelist.setter
    def kibana_private_ip_whitelist(self, kibana_private_ip_whitelist):
        """Sets the kibana_private_ip_whitelist of this InstanceForDescribeInstancesOutput.


        :param kibana_private_ip_whitelist: The kibana_private_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._kibana_private_ip_whitelist = kibana_private_ip_whitelist

    @property
    def kibana_public_domain(self):
        """Gets the kibana_public_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The kibana_public_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kibana_public_domain

    @kibana_public_domain.setter
    def kibana_public_domain(self, kibana_public_domain):
        """Sets the kibana_public_domain of this InstanceForDescribeInstancesOutput.


        :param kibana_public_domain: The kibana_public_domain of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._kibana_public_domain = kibana_public_domain

    @property
    def kibana_public_ip_allow_list(self):
        """Gets the kibana_public_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The kibana_public_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kibana_public_ip_allow_list

    @kibana_public_ip_allow_list.setter
    def kibana_public_ip_allow_list(self, kibana_public_ip_allow_list):
        """Sets the kibana_public_ip_allow_list of this InstanceForDescribeInstancesOutput.


        :param kibana_public_ip_allow_list: The kibana_public_ip_allow_list of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._kibana_public_ip_allow_list = kibana_public_ip_allow_list

    @property
    def kibana_public_ip_whitelist(self):
        """Gets the kibana_public_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The kibana_public_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._kibana_public_ip_whitelist

    @kibana_public_ip_whitelist.setter
    def kibana_public_ip_whitelist(self, kibana_public_ip_whitelist):
        """Sets the kibana_public_ip_whitelist of this InstanceForDescribeInstancesOutput.


        :param kibana_public_ip_whitelist: The kibana_public_ip_whitelist of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._kibana_public_ip_whitelist = kibana_public_ip_whitelist

    @property
    def main_zone_id(self):
        """Gets the main_zone_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The main_zone_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._main_zone_id

    @main_zone_id.setter
    def main_zone_id(self, main_zone_id):
        """Sets the main_zone_id of this InstanceForDescribeInstancesOutput.


        :param main_zone_id: The main_zone_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._main_zone_id = main_zone_id

    @property
    def maintenance_day(self):
        """Gets the maintenance_day of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The maintenance_day of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._maintenance_day

    @maintenance_day.setter
    def maintenance_day(self, maintenance_day):
        """Sets the maintenance_day of this InstanceForDescribeInstancesOutput.


        :param maintenance_day: The maintenance_day of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: list[str]
        """

        self._maintenance_day = maintenance_day

    @property
    def maintenance_time(self):
        """Gets the maintenance_time of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The maintenance_time of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._maintenance_time

    @maintenance_time.setter
    def maintenance_time(self, maintenance_time):
        """Sets the maintenance_time of this InstanceForDescribeInstancesOutput.


        :param maintenance_time: The maintenance_time of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._maintenance_time = maintenance_time

    @property
    def resource_tags(self):
        """Gets the resource_tags of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The resource_tags of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: list[ResourceTagForDescribeInstancesOutput]
        """
        return self._resource_tags

    @resource_tags.setter
    def resource_tags(self, resource_tags):
        """Sets the resource_tags of this InstanceForDescribeInstancesOutput.


        :param resource_tags: The resource_tags of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: list[ResourceTagForDescribeInstancesOutput]
        """

        self._resource_tags = resource_tags

    @property
    def status(self):
        """Gets the status of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The status of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this InstanceForDescribeInstancesOutput.


        :param status: The status of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def sub_instance_enable(self):
        """Gets the sub_instance_enable of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The sub_instance_enable of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_instance_enable

    @sub_instance_enable.setter
    def sub_instance_enable(self, sub_instance_enable):
        """Sets the sub_instance_enable of this InstanceForDescribeInstancesOutput.


        :param sub_instance_enable: The sub_instance_enable of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._sub_instance_enable = sub_instance_enable

    @property
    def sub_instances(self):
        """Gets the sub_instances of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The sub_instances of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: list[SubInstanceForDescribeInstancesOutput]
        """
        return self._sub_instances

    @sub_instances.setter
    def sub_instances(self, sub_instances):
        """Sets the sub_instances of this InstanceForDescribeInstancesOutput.


        :param sub_instances: The sub_instances of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: list[SubInstanceForDescribeInstancesOutput]
        """

        self._sub_instances = sub_instances

    @property
    def support_cold_node(self):
        """Gets the support_cold_node of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The support_cold_node of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._support_cold_node

    @support_cold_node.setter
    def support_cold_node(self, support_cold_node):
        """Sets the support_cold_node of this InstanceForDescribeInstancesOutput.


        :param support_cold_node: The support_cold_node of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: bool
        """

        self._support_cold_node = support_cold_node

    @property
    def total_nodes(self):
        """Gets the total_nodes of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The total_nodes of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_nodes

    @total_nodes.setter
    def total_nodes(self, total_nodes):
        """Sets the total_nodes of this InstanceForDescribeInstancesOutput.


        :param total_nodes: The total_nodes of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: int
        """

        self._total_nodes = total_nodes

    @property
    def transfer_info(self):
        """Gets the transfer_info of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The transfer_info of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: TransferInfoForDescribeInstancesOutput
        """
        return self._transfer_info

    @transfer_info.setter
    def transfer_info(self, transfer_info):
        """Sets the transfer_info of this InstanceForDescribeInstancesOutput.


        :param transfer_info: The transfer_info of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: TransferInfoForDescribeInstancesOutput
        """

        self._transfer_info = transfer_info

    @property
    def user_id(self):
        """Gets the user_id of this InstanceForDescribeInstancesOutput.  # noqa: E501


        :return: The user_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this InstanceForDescribeInstancesOutput.


        :param user_id: The user_id of this InstanceForDescribeInstancesOutput.  # noqa: E501
        :type: str
        """

        self._user_id = user_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceForDescribeInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceForDescribeInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceForDescribeInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
