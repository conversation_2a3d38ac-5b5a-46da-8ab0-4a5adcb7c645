# coding: utf-8

"""
    spark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeApplicationInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'id': 'int',
        'instance_id': 'int',
        'job_type': 'str',
        'project_id': 'str',
        'unique_key': 'str',
        'user_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'id': 'Id',
        'instance_id': 'InstanceId',
        'job_type': 'JobType',
        'project_id': 'ProjectId',
        'unique_key': 'UniqueKey',
        'user_id': 'UserId'
    }

    def __init__(self, account_id=None, id=None, instance_id=None, job_type=None, project_id=None, unique_key=None, user_id=None, _configuration=None):  # noqa: E501
        """DescribeApplicationInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._id = None
        self._instance_id = None
        self._job_type = None
        self._project_id = None
        self._unique_key = None
        self._user_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if id is not None:
            self.id = id
        if instance_id is not None:
            self.instance_id = instance_id
        if job_type is not None:
            self.job_type = job_type
        if project_id is not None:
            self.project_id = project_id
        if unique_key is not None:
            self.unique_key = unique_key
        if user_id is not None:
            self.user_id = user_id

    @property
    def account_id(self):
        """Gets the account_id of this DescribeApplicationInstanceRequest.  # noqa: E501


        :return: The account_id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeApplicationInstanceRequest.


        :param account_id: The account_id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def id(self):
        """Gets the id of this DescribeApplicationInstanceRequest.  # noqa: E501


        :return: The id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DescribeApplicationInstanceRequest.


        :param id: The id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeApplicationInstanceRequest.  # noqa: E501


        :return: The instance_id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeApplicationInstanceRequest.


        :param instance_id: The instance_id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :type: int
        """

        self._instance_id = instance_id

    @property
    def job_type(self):
        """Gets the job_type of this DescribeApplicationInstanceRequest.  # noqa: E501


        :return: The job_type of this DescribeApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._job_type

    @job_type.setter
    def job_type(self, job_type):
        """Sets the job_type of this DescribeApplicationInstanceRequest.


        :param job_type: The job_type of this DescribeApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._job_type = job_type

    @property
    def project_id(self):
        """Gets the project_id of this DescribeApplicationInstanceRequest.  # noqa: E501


        :return: The project_id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this DescribeApplicationInstanceRequest.


        :param project_id: The project_id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def unique_key(self):
        """Gets the unique_key of this DescribeApplicationInstanceRequest.  # noqa: E501


        :return: The unique_key of this DescribeApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._unique_key

    @unique_key.setter
    def unique_key(self, unique_key):
        """Sets the unique_key of this DescribeApplicationInstanceRequest.


        :param unique_key: The unique_key of this DescribeApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._unique_key = unique_key

    @property
    def user_id(self):
        """Gets the user_id of this DescribeApplicationInstanceRequest.  # noqa: E501


        :return: The user_id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this DescribeApplicationInstanceRequest.


        :param user_id: The user_id of this DescribeApplicationInstanceRequest.  # noqa: E501
        :type: str
        """

        self._user_id = user_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeApplicationInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeApplicationInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeApplicationInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
