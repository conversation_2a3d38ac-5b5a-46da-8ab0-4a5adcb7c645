# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_progress_bar_enable': 'int',
        'is_resolution_enable': 'int',
        'is_speed_control_enable': 'int'
    }

    attribute_map = {
        'is_progress_bar_enable': 'IsProgressBarEnable',
        'is_resolution_enable': 'IsResolutionEnable',
        'is_speed_control_enable': 'IsSpeedControlEnable'
    }

    def __init__(self, is_progress_bar_enable=None, is_resolution_enable=None, is_speed_control_enable=None, _configuration=None):  # noqa: E501
        """ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_progress_bar_enable = None
        self._is_resolution_enable = None
        self._is_speed_control_enable = None
        self.discriminator = None

        if is_progress_bar_enable is not None:
            self.is_progress_bar_enable = is_progress_bar_enable
        if is_resolution_enable is not None:
            self.is_resolution_enable = is_resolution_enable
        if is_speed_control_enable is not None:
            self.is_speed_control_enable = is_speed_control_enable

    @property
    def is_progress_bar_enable(self):
        """Gets the is_progress_bar_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.  # noqa: E501


        :return: The is_progress_bar_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_progress_bar_enable

    @is_progress_bar_enable.setter
    def is_progress_bar_enable(self, is_progress_bar_enable):
        """Sets the is_progress_bar_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.


        :param is_progress_bar_enable: The is_progress_bar_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.  # noqa: E501
        :type: int
        """

        self._is_progress_bar_enable = is_progress_bar_enable

    @property
    def is_resolution_enable(self):
        """Gets the is_resolution_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.  # noqa: E501


        :return: The is_resolution_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_resolution_enable

    @is_resolution_enable.setter
    def is_resolution_enable(self, is_resolution_enable):
        """Sets the is_resolution_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.


        :param is_resolution_enable: The is_resolution_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.  # noqa: E501
        :type: int
        """

        self._is_resolution_enable = is_resolution_enable

    @property
    def is_speed_control_enable(self):
        """Gets the is_speed_control_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.  # noqa: E501


        :return: The is_speed_control_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_speed_control_enable

    @is_speed_control_enable.setter
    def is_speed_control_enable(self, is_speed_control_enable):
        """Sets the is_speed_control_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.


        :param is_speed_control_enable: The is_speed_control_enable of this ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput.  # noqa: E501
        :type: int
        """

        self._is_speed_control_enable = is_speed_control_enable

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ReplayPlayerConfigForUpdateActivityReplayPlayerConfigInput):
            return True

        return self.to_dict() != other.to_dict()
