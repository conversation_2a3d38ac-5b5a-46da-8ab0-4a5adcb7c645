# coding: utf-8

"""
    apig20221112

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MirrorPolicyForGetRouteOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'percent': 'PercentForGetRouteOutput',
        'upstream': 'UpstreamForGetRouteOutput'
    }

    attribute_map = {
        'percent': 'Percent',
        'upstream': 'Upstream'
    }

    def __init__(self, percent=None, upstream=None, _configuration=None):  # noqa: E501
        """MirrorPolicyForGetRouteOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._percent = None
        self._upstream = None
        self.discriminator = None

        if percent is not None:
            self.percent = percent
        if upstream is not None:
            self.upstream = upstream

    @property
    def percent(self):
        """Gets the percent of this MirrorPolicyForGetRouteOutput.  # noqa: E501


        :return: The percent of this MirrorPolicyForGetRouteOutput.  # noqa: E501
        :rtype: PercentForGetRouteOutput
        """
        return self._percent

    @percent.setter
    def percent(self, percent):
        """Sets the percent of this MirrorPolicyForGetRouteOutput.


        :param percent: The percent of this MirrorPolicyForGetRouteOutput.  # noqa: E501
        :type: PercentForGetRouteOutput
        """

        self._percent = percent

    @property
    def upstream(self):
        """Gets the upstream of this MirrorPolicyForGetRouteOutput.  # noqa: E501


        :return: The upstream of this MirrorPolicyForGetRouteOutput.  # noqa: E501
        :rtype: UpstreamForGetRouteOutput
        """
        return self._upstream

    @upstream.setter
    def upstream(self, upstream):
        """Sets the upstream of this MirrorPolicyForGetRouteOutput.


        :param upstream: The upstream of this MirrorPolicyForGetRouteOutput.  # noqa: E501
        :type: UpstreamForGetRouteOutput
        """

        self._upstream = upstream

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MirrorPolicyForGetRouteOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MirrorPolicyForGetRouteOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MirrorPolicyForGetRouteOutput):
            return True

        return self.to_dict() != other.to_dict()
