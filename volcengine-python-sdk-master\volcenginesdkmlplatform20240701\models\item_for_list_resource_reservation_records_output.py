# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListResourceReservationRecordsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activation_time': 'str',
        'allocated_compute_resource': 'AllocatedComputeResourceForListResourceReservationRecordsOutput',
        'delivered_compute_resource': 'DeliveredComputeResourceForListResourceReservationRecordsOutput',
        'desired_compute_resource': 'DesiredComputeResourceForListResourceReservationRecordsOutput',
        'id': 'str',
        'resource_end_time': 'str',
        'resource_start_time': 'str',
        'resource_zone_id': 'str',
        'status': 'StatusForListResourceReservationRecordsOutput'
    }

    attribute_map = {
        'activation_time': 'ActivationTime',
        'allocated_compute_resource': 'AllocatedComputeResource',
        'delivered_compute_resource': 'DeliveredComputeResource',
        'desired_compute_resource': 'DesiredComputeResource',
        'id': 'Id',
        'resource_end_time': 'ResourceEndTime',
        'resource_start_time': 'ResourceStartTime',
        'resource_zone_id': 'ResourceZoneId',
        'status': 'Status'
    }

    def __init__(self, activation_time=None, allocated_compute_resource=None, delivered_compute_resource=None, desired_compute_resource=None, id=None, resource_end_time=None, resource_start_time=None, resource_zone_id=None, status=None, _configuration=None):  # noqa: E501
        """ItemForListResourceReservationRecordsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activation_time = None
        self._allocated_compute_resource = None
        self._delivered_compute_resource = None
        self._desired_compute_resource = None
        self._id = None
        self._resource_end_time = None
        self._resource_start_time = None
        self._resource_zone_id = None
        self._status = None
        self.discriminator = None

        if activation_time is not None:
            self.activation_time = activation_time
        if allocated_compute_resource is not None:
            self.allocated_compute_resource = allocated_compute_resource
        if delivered_compute_resource is not None:
            self.delivered_compute_resource = delivered_compute_resource
        if desired_compute_resource is not None:
            self.desired_compute_resource = desired_compute_resource
        if id is not None:
            self.id = id
        if resource_end_time is not None:
            self.resource_end_time = resource_end_time
        if resource_start_time is not None:
            self.resource_start_time = resource_start_time
        if resource_zone_id is not None:
            self.resource_zone_id = resource_zone_id
        if status is not None:
            self.status = status

    @property
    def activation_time(self):
        """Gets the activation_time of this ItemForListResourceReservationRecordsOutput.  # noqa: E501


        :return: The activation_time of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :rtype: str
        """
        return self._activation_time

    @activation_time.setter
    def activation_time(self, activation_time):
        """Sets the activation_time of this ItemForListResourceReservationRecordsOutput.


        :param activation_time: The activation_time of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :type: str
        """

        self._activation_time = activation_time

    @property
    def allocated_compute_resource(self):
        """Gets the allocated_compute_resource of this ItemForListResourceReservationRecordsOutput.  # noqa: E501


        :return: The allocated_compute_resource of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :rtype: AllocatedComputeResourceForListResourceReservationRecordsOutput
        """
        return self._allocated_compute_resource

    @allocated_compute_resource.setter
    def allocated_compute_resource(self, allocated_compute_resource):
        """Sets the allocated_compute_resource of this ItemForListResourceReservationRecordsOutput.


        :param allocated_compute_resource: The allocated_compute_resource of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :type: AllocatedComputeResourceForListResourceReservationRecordsOutput
        """

        self._allocated_compute_resource = allocated_compute_resource

    @property
    def delivered_compute_resource(self):
        """Gets the delivered_compute_resource of this ItemForListResourceReservationRecordsOutput.  # noqa: E501


        :return: The delivered_compute_resource of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :rtype: DeliveredComputeResourceForListResourceReservationRecordsOutput
        """
        return self._delivered_compute_resource

    @delivered_compute_resource.setter
    def delivered_compute_resource(self, delivered_compute_resource):
        """Sets the delivered_compute_resource of this ItemForListResourceReservationRecordsOutput.


        :param delivered_compute_resource: The delivered_compute_resource of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :type: DeliveredComputeResourceForListResourceReservationRecordsOutput
        """

        self._delivered_compute_resource = delivered_compute_resource

    @property
    def desired_compute_resource(self):
        """Gets the desired_compute_resource of this ItemForListResourceReservationRecordsOutput.  # noqa: E501


        :return: The desired_compute_resource of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :rtype: DesiredComputeResourceForListResourceReservationRecordsOutput
        """
        return self._desired_compute_resource

    @desired_compute_resource.setter
    def desired_compute_resource(self, desired_compute_resource):
        """Sets the desired_compute_resource of this ItemForListResourceReservationRecordsOutput.


        :param desired_compute_resource: The desired_compute_resource of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :type: DesiredComputeResourceForListResourceReservationRecordsOutput
        """

        self._desired_compute_resource = desired_compute_resource

    @property
    def id(self):
        """Gets the id of this ItemForListResourceReservationRecordsOutput.  # noqa: E501


        :return: The id of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListResourceReservationRecordsOutput.


        :param id: The id of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def resource_end_time(self):
        """Gets the resource_end_time of this ItemForListResourceReservationRecordsOutput.  # noqa: E501


        :return: The resource_end_time of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_end_time

    @resource_end_time.setter
    def resource_end_time(self, resource_end_time):
        """Sets the resource_end_time of this ItemForListResourceReservationRecordsOutput.


        :param resource_end_time: The resource_end_time of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :type: str
        """

        self._resource_end_time = resource_end_time

    @property
    def resource_start_time(self):
        """Gets the resource_start_time of this ItemForListResourceReservationRecordsOutput.  # noqa: E501


        :return: The resource_start_time of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_start_time

    @resource_start_time.setter
    def resource_start_time(self, resource_start_time):
        """Sets the resource_start_time of this ItemForListResourceReservationRecordsOutput.


        :param resource_start_time: The resource_start_time of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :type: str
        """

        self._resource_start_time = resource_start_time

    @property
    def resource_zone_id(self):
        """Gets the resource_zone_id of this ItemForListResourceReservationRecordsOutput.  # noqa: E501


        :return: The resource_zone_id of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_zone_id

    @resource_zone_id.setter
    def resource_zone_id(self, resource_zone_id):
        """Sets the resource_zone_id of this ItemForListResourceReservationRecordsOutput.


        :param resource_zone_id: The resource_zone_id of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :type: str
        """

        self._resource_zone_id = resource_zone_id

    @property
    def status(self):
        """Gets the status of this ItemForListResourceReservationRecordsOutput.  # noqa: E501


        :return: The status of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :rtype: StatusForListResourceReservationRecordsOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListResourceReservationRecordsOutput.


        :param status: The status of this ItemForListResourceReservationRecordsOutput.  # noqa: E501
        :type: StatusForListResourceReservationRecordsOutput
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListResourceReservationRecordsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListResourceReservationRecordsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListResourceReservationRecordsOutput):
            return True

        return self.to_dict() != other.to_dict()
