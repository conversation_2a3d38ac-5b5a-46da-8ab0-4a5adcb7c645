# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDevAssetOverviewResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm': 'int',
        'baseline': 'int',
        'has_risk': 'int',
        'running': 'int',
        'stopped': 'int',
        'total': 'int',
        'vul': 'int'
    }

    attribute_map = {
        'alarm': 'Alarm',
        'baseline': 'Baseline',
        'has_risk': 'HasRisk',
        'running': 'Running',
        'stopped': 'Stopped',
        'total': 'Total',
        'vul': 'Vul'
    }

    def __init__(self, alarm=None, baseline=None, has_risk=None, running=None, stopped=None, total=None, vul=None, _configuration=None):  # noqa: E501
        """GetDevAssetOverviewResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm = None
        self._baseline = None
        self._has_risk = None
        self._running = None
        self._stopped = None
        self._total = None
        self._vul = None
        self.discriminator = None

        if alarm is not None:
            self.alarm = alarm
        if baseline is not None:
            self.baseline = baseline
        if has_risk is not None:
            self.has_risk = has_risk
        if running is not None:
            self.running = running
        if stopped is not None:
            self.stopped = stopped
        if total is not None:
            self.total = total
        if vul is not None:
            self.vul = vul

    @property
    def alarm(self):
        """Gets the alarm of this GetDevAssetOverviewResponse.  # noqa: E501


        :return: The alarm of this GetDevAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._alarm

    @alarm.setter
    def alarm(self, alarm):
        """Sets the alarm of this GetDevAssetOverviewResponse.


        :param alarm: The alarm of this GetDevAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._alarm = alarm

    @property
    def baseline(self):
        """Gets the baseline of this GetDevAssetOverviewResponse.  # noqa: E501


        :return: The baseline of this GetDevAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._baseline

    @baseline.setter
    def baseline(self, baseline):
        """Sets the baseline of this GetDevAssetOverviewResponse.


        :param baseline: The baseline of this GetDevAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._baseline = baseline

    @property
    def has_risk(self):
        """Gets the has_risk of this GetDevAssetOverviewResponse.  # noqa: E501


        :return: The has_risk of this GetDevAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._has_risk

    @has_risk.setter
    def has_risk(self, has_risk):
        """Sets the has_risk of this GetDevAssetOverviewResponse.


        :param has_risk: The has_risk of this GetDevAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._has_risk = has_risk

    @property
    def running(self):
        """Gets the running of this GetDevAssetOverviewResponse.  # noqa: E501


        :return: The running of this GetDevAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._running

    @running.setter
    def running(self, running):
        """Sets the running of this GetDevAssetOverviewResponse.


        :param running: The running of this GetDevAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._running = running

    @property
    def stopped(self):
        """Gets the stopped of this GetDevAssetOverviewResponse.  # noqa: E501


        :return: The stopped of this GetDevAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._stopped

    @stopped.setter
    def stopped(self, stopped):
        """Sets the stopped of this GetDevAssetOverviewResponse.


        :param stopped: The stopped of this GetDevAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._stopped = stopped

    @property
    def total(self):
        """Gets the total of this GetDevAssetOverviewResponse.  # noqa: E501


        :return: The total of this GetDevAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this GetDevAssetOverviewResponse.


        :param total: The total of this GetDevAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    @property
    def vul(self):
        """Gets the vul of this GetDevAssetOverviewResponse.  # noqa: E501


        :return: The vul of this GetDevAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._vul

    @vul.setter
    def vul(self, vul):
        """Sets the vul of this GetDevAssetOverviewResponse.


        :param vul: The vul of this GetDevAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._vul = vul

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDevAssetOverviewResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDevAssetOverviewResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDevAssetOverviewResponse):
            return True

        return self.to_dict() != other.to_dict()
