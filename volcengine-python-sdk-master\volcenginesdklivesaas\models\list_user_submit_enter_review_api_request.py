# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListUserSubmitEnterReviewAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'content': 'str',
        'end_time': 'int',
        'extra': 'str',
        'page_count': 'int',
        'page_no': 'int',
        'review_status': 'int',
        'sort_order': 'int',
        'start_time': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityID',
        'content': 'Content',
        'end_time': 'EndTime',
        'extra': 'Extra',
        'page_count': 'PageCount',
        'page_no': 'PageNo',
        'review_status': 'ReviewStatus',
        'sort_order': 'SortOrder',
        'start_time': 'StartTime'
    }

    def __init__(self, activity_id=None, content=None, end_time=None, extra=None, page_count=None, page_no=None, review_status=None, sort_order=None, start_time=None, _configuration=None):  # noqa: E501
        """ListUserSubmitEnterReviewAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._content = None
        self._end_time = None
        self._extra = None
        self._page_count = None
        self._page_no = None
        self._review_status = None
        self._sort_order = None
        self._start_time = None
        self.discriminator = None

        self.activity_id = activity_id
        if content is not None:
            self.content = content
        if end_time is not None:
            self.end_time = end_time
        if extra is not None:
            self.extra = extra
        if page_count is not None:
            self.page_count = page_count
        if page_no is not None:
            self.page_no = page_no
        if review_status is not None:
            self.review_status = review_status
        self.sort_order = sort_order
        if start_time is not None:
            self.start_time = start_time

    @property
    def activity_id(self):
        """Gets the activity_id of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501


        :return: The activity_id of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ListUserSubmitEnterReviewAPIRequest.


        :param activity_id: The activity_id of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def content(self):
        """Gets the content of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501


        :return: The content of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._content

    @content.setter
    def content(self, content):
        """Sets the content of this ListUserSubmitEnterReviewAPIRequest.


        :param content: The content of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :type: str
        """

        self._content = content

    @property
    def end_time(self):
        """Gets the end_time of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501


        :return: The end_time of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this ListUserSubmitEnterReviewAPIRequest.


        :param end_time: The end_time of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def extra(self):
        """Gets the extra of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501


        :return: The extra of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this ListUserSubmitEnterReviewAPIRequest.


        :param extra: The extra of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def page_count(self):
        """Gets the page_count of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501


        :return: The page_count of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_count

    @page_count.setter
    def page_count(self, page_count):
        """Sets the page_count of this ListUserSubmitEnterReviewAPIRequest.


        :param page_count: The page_count of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_count = page_count

    @property
    def page_no(self):
        """Gets the page_no of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501


        :return: The page_no of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_no

    @page_no.setter
    def page_no(self, page_no):
        """Sets the page_no of this ListUserSubmitEnterReviewAPIRequest.


        :param page_no: The page_no of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_no = page_no

    @property
    def review_status(self):
        """Gets the review_status of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501


        :return: The review_status of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._review_status

    @review_status.setter
    def review_status(self, review_status):
        """Sets the review_status of this ListUserSubmitEnterReviewAPIRequest.


        :param review_status: The review_status of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :type: int
        """

        self._review_status = review_status

    @property
    def sort_order(self):
        """Gets the sort_order of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501


        :return: The sort_order of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListUserSubmitEnterReviewAPIRequest.


        :param sort_order: The sort_order of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and sort_order is None:
            raise ValueError("Invalid value for `sort_order`, must not be `None`")  # noqa: E501

        self._sort_order = sort_order

    @property
    def start_time(self):
        """Gets the start_time of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501


        :return: The start_time of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this ListUserSubmitEnterReviewAPIRequest.


        :param start_time: The start_time of this ListUserSubmitEnterReviewAPIRequest.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListUserSubmitEnterReviewAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListUserSubmitEnterReviewAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListUserSubmitEnterReviewAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
