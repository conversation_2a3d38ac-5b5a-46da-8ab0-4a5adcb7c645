# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateMlpAlarmStatusRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_id_list': 'list[str]',
        'conditions': 'ConditionsForUpdateMlpAlarmStatusInput',
        'custom_tag_list': 'list[str]',
        'handle_method': 'int',
        'type': 'str'
    }

    attribute_map = {
        'alarm_id_list': 'AlarmIDList',
        'conditions': 'Conditions',
        'custom_tag_list': 'CustomTagList',
        'handle_method': 'HandleMethod',
        'type': 'Type'
    }

    def __init__(self, alarm_id_list=None, conditions=None, custom_tag_list=None, handle_method=None, type=None, _configuration=None):  # noqa: E501
        """UpdateMlpAlarmStatusRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_id_list = None
        self._conditions = None
        self._custom_tag_list = None
        self._handle_method = None
        self._type = None
        self.discriminator = None

        if alarm_id_list is not None:
            self.alarm_id_list = alarm_id_list
        if conditions is not None:
            self.conditions = conditions
        if custom_tag_list is not None:
            self.custom_tag_list = custom_tag_list
        if handle_method is not None:
            self.handle_method = handle_method
        self.type = type

    @property
    def alarm_id_list(self):
        """Gets the alarm_id_list of this UpdateMlpAlarmStatusRequest.  # noqa: E501


        :return: The alarm_id_list of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._alarm_id_list

    @alarm_id_list.setter
    def alarm_id_list(self, alarm_id_list):
        """Sets the alarm_id_list of this UpdateMlpAlarmStatusRequest.


        :param alarm_id_list: The alarm_id_list of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :type: list[str]
        """

        self._alarm_id_list = alarm_id_list

    @property
    def conditions(self):
        """Gets the conditions of this UpdateMlpAlarmStatusRequest.  # noqa: E501


        :return: The conditions of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :rtype: ConditionsForUpdateMlpAlarmStatusInput
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this UpdateMlpAlarmStatusRequest.


        :param conditions: The conditions of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :type: ConditionsForUpdateMlpAlarmStatusInput
        """

        self._conditions = conditions

    @property
    def custom_tag_list(self):
        """Gets the custom_tag_list of this UpdateMlpAlarmStatusRequest.  # noqa: E501


        :return: The custom_tag_list of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._custom_tag_list

    @custom_tag_list.setter
    def custom_tag_list(self, custom_tag_list):
        """Sets the custom_tag_list of this UpdateMlpAlarmStatusRequest.


        :param custom_tag_list: The custom_tag_list of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :type: list[str]
        """

        self._custom_tag_list = custom_tag_list

    @property
    def handle_method(self):
        """Gets the handle_method of this UpdateMlpAlarmStatusRequest.  # noqa: E501


        :return: The handle_method of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :rtype: int
        """
        return self._handle_method

    @handle_method.setter
    def handle_method(self, handle_method):
        """Sets the handle_method of this UpdateMlpAlarmStatusRequest.


        :param handle_method: The handle_method of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :type: int
        """

        self._handle_method = handle_method

    @property
    def type(self):
        """Gets the type of this UpdateMlpAlarmStatusRequest.  # noqa: E501


        :return: The type of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this UpdateMlpAlarmStatusRequest.


        :param type: The type of this UpdateMlpAlarmStatusRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and type is None:
            raise ValueError("Invalid value for `type`, must not be `None`")  # noqa: E501

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateMlpAlarmStatusRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateMlpAlarmStatusRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateMlpAlarmStatusRequest):
            return True

        return self.to_dict() != other.to_dict()
