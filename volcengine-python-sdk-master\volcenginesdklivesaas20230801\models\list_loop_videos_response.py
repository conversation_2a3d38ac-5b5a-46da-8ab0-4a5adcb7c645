# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListLoopVideosResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'auto_end_time': 'int',
        'auto_start_time': 'int',
        'is_show_program': 'int',
        'line_id': 'int',
        'live_mode': 'int',
        'live_type': 'int',
        'loop_number': 'int',
        'loop_video_status': 'int',
        'loop_videos': 'list[LoopVideoForListLoopVideosOutput]',
        'play_type': 'int',
        'program_name': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'auto_end_time': 'AutoEndTime',
        'auto_start_time': 'AutoStartTime',
        'is_show_program': 'IsShowProgram',
        'line_id': 'LineId',
        'live_mode': 'LiveMode',
        'live_type': 'LiveType',
        'loop_number': 'LoopNumber',
        'loop_video_status': 'LoopVideoStatus',
        'loop_videos': 'LoopVideos',
        'play_type': 'PlayType',
        'program_name': 'ProgramName'
    }

    def __init__(self, activity_id=None, auto_end_time=None, auto_start_time=None, is_show_program=None, line_id=None, live_mode=None, live_type=None, loop_number=None, loop_video_status=None, loop_videos=None, play_type=None, program_name=None, _configuration=None):  # noqa: E501
        """ListLoopVideosResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._auto_end_time = None
        self._auto_start_time = None
        self._is_show_program = None
        self._line_id = None
        self._live_mode = None
        self._live_type = None
        self._loop_number = None
        self._loop_video_status = None
        self._loop_videos = None
        self._play_type = None
        self._program_name = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if auto_end_time is not None:
            self.auto_end_time = auto_end_time
        if auto_start_time is not None:
            self.auto_start_time = auto_start_time
        if is_show_program is not None:
            self.is_show_program = is_show_program
        if line_id is not None:
            self.line_id = line_id
        if live_mode is not None:
            self.live_mode = live_mode
        if live_type is not None:
            self.live_type = live_type
        if loop_number is not None:
            self.loop_number = loop_number
        if loop_video_status is not None:
            self.loop_video_status = loop_video_status
        if loop_videos is not None:
            self.loop_videos = loop_videos
        if play_type is not None:
            self.play_type = play_type
        if program_name is not None:
            self.program_name = program_name

    @property
    def activity_id(self):
        """Gets the activity_id of this ListLoopVideosResponse.  # noqa: E501


        :return: The activity_id of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ListLoopVideosResponse.


        :param activity_id: The activity_id of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def auto_end_time(self):
        """Gets the auto_end_time of this ListLoopVideosResponse.  # noqa: E501


        :return: The auto_end_time of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._auto_end_time

    @auto_end_time.setter
    def auto_end_time(self, auto_end_time):
        """Sets the auto_end_time of this ListLoopVideosResponse.


        :param auto_end_time: The auto_end_time of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._auto_end_time = auto_end_time

    @property
    def auto_start_time(self):
        """Gets the auto_start_time of this ListLoopVideosResponse.  # noqa: E501


        :return: The auto_start_time of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._auto_start_time

    @auto_start_time.setter
    def auto_start_time(self, auto_start_time):
        """Sets the auto_start_time of this ListLoopVideosResponse.


        :param auto_start_time: The auto_start_time of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._auto_start_time = auto_start_time

    @property
    def is_show_program(self):
        """Gets the is_show_program of this ListLoopVideosResponse.  # noqa: E501


        :return: The is_show_program of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._is_show_program

    @is_show_program.setter
    def is_show_program(self, is_show_program):
        """Sets the is_show_program of this ListLoopVideosResponse.


        :param is_show_program: The is_show_program of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._is_show_program = is_show_program

    @property
    def line_id(self):
        """Gets the line_id of this ListLoopVideosResponse.  # noqa: E501


        :return: The line_id of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._line_id

    @line_id.setter
    def line_id(self, line_id):
        """Sets the line_id of this ListLoopVideosResponse.


        :param line_id: The line_id of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._line_id = line_id

    @property
    def live_mode(self):
        """Gets the live_mode of this ListLoopVideosResponse.  # noqa: E501


        :return: The live_mode of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_mode

    @live_mode.setter
    def live_mode(self, live_mode):
        """Sets the live_mode of this ListLoopVideosResponse.


        :param live_mode: The live_mode of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._live_mode = live_mode

    @property
    def live_type(self):
        """Gets the live_type of this ListLoopVideosResponse.  # noqa: E501


        :return: The live_type of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._live_type

    @live_type.setter
    def live_type(self, live_type):
        """Sets the live_type of this ListLoopVideosResponse.


        :param live_type: The live_type of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._live_type = live_type

    @property
    def loop_number(self):
        """Gets the loop_number of this ListLoopVideosResponse.  # noqa: E501


        :return: The loop_number of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._loop_number

    @loop_number.setter
    def loop_number(self, loop_number):
        """Sets the loop_number of this ListLoopVideosResponse.


        :param loop_number: The loop_number of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._loop_number = loop_number

    @property
    def loop_video_status(self):
        """Gets the loop_video_status of this ListLoopVideosResponse.  # noqa: E501


        :return: The loop_video_status of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._loop_video_status

    @loop_video_status.setter
    def loop_video_status(self, loop_video_status):
        """Sets the loop_video_status of this ListLoopVideosResponse.


        :param loop_video_status: The loop_video_status of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._loop_video_status = loop_video_status

    @property
    def loop_videos(self):
        """Gets the loop_videos of this ListLoopVideosResponse.  # noqa: E501


        :return: The loop_videos of this ListLoopVideosResponse.  # noqa: E501
        :rtype: list[LoopVideoForListLoopVideosOutput]
        """
        return self._loop_videos

    @loop_videos.setter
    def loop_videos(self, loop_videos):
        """Sets the loop_videos of this ListLoopVideosResponse.


        :param loop_videos: The loop_videos of this ListLoopVideosResponse.  # noqa: E501
        :type: list[LoopVideoForListLoopVideosOutput]
        """

        self._loop_videos = loop_videos

    @property
    def play_type(self):
        """Gets the play_type of this ListLoopVideosResponse.  # noqa: E501


        :return: The play_type of this ListLoopVideosResponse.  # noqa: E501
        :rtype: int
        """
        return self._play_type

    @play_type.setter
    def play_type(self, play_type):
        """Sets the play_type of this ListLoopVideosResponse.


        :param play_type: The play_type of this ListLoopVideosResponse.  # noqa: E501
        :type: int
        """

        self._play_type = play_type

    @property
    def program_name(self):
        """Gets the program_name of this ListLoopVideosResponse.  # noqa: E501


        :return: The program_name of this ListLoopVideosResponse.  # noqa: E501
        :rtype: str
        """
        return self._program_name

    @program_name.setter
    def program_name(self, program_name):
        """Sets the program_name of this ListLoopVideosResponse.


        :param program_name: The program_name of this ListLoopVideosResponse.  # noqa: E501
        :type: str
        """

        self._program_name = program_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListLoopVideosResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListLoopVideosResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListLoopVideosResponse):
            return True

        return self.to_dict() != other.to_dict()
