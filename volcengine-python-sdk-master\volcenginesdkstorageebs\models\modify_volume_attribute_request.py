# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyVolumeAttributeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'delete_with_instance': 'bool',
        'description': 'str',
        'volume_id': 'str',
        'volume_name': 'str'
    }

    attribute_map = {
        'delete_with_instance': 'DeleteWithInstance',
        'description': 'Description',
        'volume_id': 'VolumeId',
        'volume_name': 'VolumeName'
    }

    def __init__(self, delete_with_instance=None, description=None, volume_id=None, volume_name=None, _configuration=None):  # noqa: E501
        """ModifyVolumeAttributeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._delete_with_instance = None
        self._description = None
        self._volume_id = None
        self._volume_name = None
        self.discriminator = None

        if delete_with_instance is not None:
            self.delete_with_instance = delete_with_instance
        if description is not None:
            self.description = description
        self.volume_id = volume_id
        self.volume_name = volume_name

    @property
    def delete_with_instance(self):
        """Gets the delete_with_instance of this ModifyVolumeAttributeRequest.  # noqa: E501


        :return: The delete_with_instance of this ModifyVolumeAttributeRequest.  # noqa: E501
        :rtype: bool
        """
        return self._delete_with_instance

    @delete_with_instance.setter
    def delete_with_instance(self, delete_with_instance):
        """Sets the delete_with_instance of this ModifyVolumeAttributeRequest.


        :param delete_with_instance: The delete_with_instance of this ModifyVolumeAttributeRequest.  # noqa: E501
        :type: bool
        """

        self._delete_with_instance = delete_with_instance

    @property
    def description(self):
        """Gets the description of this ModifyVolumeAttributeRequest.  # noqa: E501


        :return: The description of this ModifyVolumeAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyVolumeAttributeRequest.


        :param description: The description of this ModifyVolumeAttributeRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def volume_id(self):
        """Gets the volume_id of this ModifyVolumeAttributeRequest.  # noqa: E501


        :return: The volume_id of this ModifyVolumeAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._volume_id

    @volume_id.setter
    def volume_id(self, volume_id):
        """Sets the volume_id of this ModifyVolumeAttributeRequest.


        :param volume_id: The volume_id of this ModifyVolumeAttributeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and volume_id is None:
            raise ValueError("Invalid value for `volume_id`, must not be `None`")  # noqa: E501

        self._volume_id = volume_id

    @property
    def volume_name(self):
        """Gets the volume_name of this ModifyVolumeAttributeRequest.  # noqa: E501


        :return: The volume_name of this ModifyVolumeAttributeRequest.  # noqa: E501
        :rtype: str
        """
        return self._volume_name

    @volume_name.setter
    def volume_name(self, volume_name):
        """Sets the volume_name of this ModifyVolumeAttributeRequest.


        :param volume_name: The volume_name of this ModifyVolumeAttributeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and volume_name is None:
            raise ValueError("Invalid value for `volume_name`, must not be `None`")  # noqa: E501

        self._volume_name = volume_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyVolumeAttributeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyVolumeAttributeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyVolumeAttributeRequest):
            return True

        return self.to_dict() != other.to_dict()
