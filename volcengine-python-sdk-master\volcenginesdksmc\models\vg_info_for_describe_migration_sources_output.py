# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VgInfoForDescribeMigrationSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'format': 'str',
        'lv_infos': 'list[LvInfoForDescribeMigrationSourcesOutput]',
        'pv_infos': 'list[PvInfoForDescribeMigrationSourcesOutput]',
        'vg_name': 'str',
        'vg_size': 'int',
        'vg_uuid': 'str'
    }

    attribute_map = {
        'format': 'Format',
        'lv_infos': 'LvInfos',
        'pv_infos': 'PvInfos',
        'vg_name': 'VgName',
        'vg_size': 'VgSize',
        'vg_uuid': 'VgUUID'
    }

    def __init__(self, format=None, lv_infos=None, pv_infos=None, vg_name=None, vg_size=None, vg_uuid=None, _configuration=None):  # noqa: E501
        """VgInfoForDescribeMigrationSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._format = None
        self._lv_infos = None
        self._pv_infos = None
        self._vg_name = None
        self._vg_size = None
        self._vg_uuid = None
        self.discriminator = None

        if format is not None:
            self.format = format
        if lv_infos is not None:
            self.lv_infos = lv_infos
        if pv_infos is not None:
            self.pv_infos = pv_infos
        if vg_name is not None:
            self.vg_name = vg_name
        if vg_size is not None:
            self.vg_size = vg_size
        if vg_uuid is not None:
            self.vg_uuid = vg_uuid

    @property
    def format(self):
        """Gets the format of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The format of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._format

    @format.setter
    def format(self, format):
        """Sets the format of this VgInfoForDescribeMigrationSourcesOutput.


        :param format: The format of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._format = format

    @property
    def lv_infos(self):
        """Gets the lv_infos of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The lv_infos of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: list[LvInfoForDescribeMigrationSourcesOutput]
        """
        return self._lv_infos

    @lv_infos.setter
    def lv_infos(self, lv_infos):
        """Sets the lv_infos of this VgInfoForDescribeMigrationSourcesOutput.


        :param lv_infos: The lv_infos of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: list[LvInfoForDescribeMigrationSourcesOutput]
        """

        self._lv_infos = lv_infos

    @property
    def pv_infos(self):
        """Gets the pv_infos of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The pv_infos of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: list[PvInfoForDescribeMigrationSourcesOutput]
        """
        return self._pv_infos

    @pv_infos.setter
    def pv_infos(self, pv_infos):
        """Sets the pv_infos of this VgInfoForDescribeMigrationSourcesOutput.


        :param pv_infos: The pv_infos of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: list[PvInfoForDescribeMigrationSourcesOutput]
        """

        self._pv_infos = pv_infos

    @property
    def vg_name(self):
        """Gets the vg_name of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The vg_name of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._vg_name

    @vg_name.setter
    def vg_name(self, vg_name):
        """Sets the vg_name of this VgInfoForDescribeMigrationSourcesOutput.


        :param vg_name: The vg_name of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._vg_name = vg_name

    @property
    def vg_size(self):
        """Gets the vg_size of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The vg_size of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._vg_size

    @vg_size.setter
    def vg_size(self, vg_size):
        """Sets the vg_size of this VgInfoForDescribeMigrationSourcesOutput.


        :param vg_size: The vg_size of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._vg_size = vg_size

    @property
    def vg_uuid(self):
        """Gets the vg_uuid of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The vg_uuid of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._vg_uuid

    @vg_uuid.setter
    def vg_uuid(self, vg_uuid):
        """Sets the vg_uuid of this VgInfoForDescribeMigrationSourcesOutput.


        :param vg_uuid: The vg_uuid of this VgInfoForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._vg_uuid = vg_uuid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VgInfoForDescribeMigrationSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VgInfoForDescribeMigrationSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VgInfoForDescribeMigrationSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
