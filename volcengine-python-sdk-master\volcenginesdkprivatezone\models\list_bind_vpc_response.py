# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListBindVPCResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bind_vpcs': 'list[BindVPCForListBindVPCOutput]',
        'total': 'int'
    }

    attribute_map = {
        'bind_vpcs': 'BindVPCs',
        'total': 'Total'
    }

    def __init__(self, bind_vpcs=None, total=None, _configuration=None):  # noqa: E501
        """ListBindVPCResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bind_vpcs = None
        self._total = None
        self.discriminator = None

        if bind_vpcs is not None:
            self.bind_vpcs = bind_vpcs
        if total is not None:
            self.total = total

    @property
    def bind_vpcs(self):
        """Gets the bind_vpcs of this ListBindVPCResponse.  # noqa: E501


        :return: The bind_vpcs of this ListBindVPCResponse.  # noqa: E501
        :rtype: list[BindVPCForListBindVPCOutput]
        """
        return self._bind_vpcs

    @bind_vpcs.setter
    def bind_vpcs(self, bind_vpcs):
        """Sets the bind_vpcs of this ListBindVPCResponse.


        :param bind_vpcs: The bind_vpcs of this ListBindVPCResponse.  # noqa: E501
        :type: list[BindVPCForListBindVPCOutput]
        """

        self._bind_vpcs = bind_vpcs

    @property
    def total(self):
        """Gets the total of this ListBindVPCResponse.  # noqa: E501


        :return: The total of this ListBindVPCResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this ListBindVPCResponse.


        :param total: The total of this ListBindVPCResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListBindVPCResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListBindVPCResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListBindVPCResponse):
            return True

        return self.to_dict() != other.to_dict()
