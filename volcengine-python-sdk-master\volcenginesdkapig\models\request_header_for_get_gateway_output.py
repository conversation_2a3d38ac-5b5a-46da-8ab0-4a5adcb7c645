# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RequestHeaderForGetGatewayOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aliases_in_log': 'str',
        'key': 'str'
    }

    attribute_map = {
        'aliases_in_log': 'AliasesInLog',
        'key': 'Key'
    }

    def __init__(self, aliases_in_log=None, key=None, _configuration=None):  # noqa: E501
        """RequestHeaderForGetGatewayOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aliases_in_log = None
        self._key = None
        self.discriminator = None

        if aliases_in_log is not None:
            self.aliases_in_log = aliases_in_log
        if key is not None:
            self.key = key

    @property
    def aliases_in_log(self):
        """Gets the aliases_in_log of this RequestHeaderForGetGatewayOutput.  # noqa: E501


        :return: The aliases_in_log of this RequestHeaderForGetGatewayOutput.  # noqa: E501
        :rtype: str
        """
        return self._aliases_in_log

    @aliases_in_log.setter
    def aliases_in_log(self, aliases_in_log):
        """Sets the aliases_in_log of this RequestHeaderForGetGatewayOutput.


        :param aliases_in_log: The aliases_in_log of this RequestHeaderForGetGatewayOutput.  # noqa: E501
        :type: str
        """

        self._aliases_in_log = aliases_in_log

    @property
    def key(self):
        """Gets the key of this RequestHeaderForGetGatewayOutput.  # noqa: E501


        :return: The key of this RequestHeaderForGetGatewayOutput.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this RequestHeaderForGetGatewayOutput.


        :param key: The key of this RequestHeaderForGetGatewayOutput.  # noqa: E501
        :type: str
        """

        self._key = key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RequestHeaderForGetGatewayOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RequestHeaderForGetGatewayOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RequestHeaderForGetGatewayOutput):
            return True

        return self.to_dict() != other.to_dict()
