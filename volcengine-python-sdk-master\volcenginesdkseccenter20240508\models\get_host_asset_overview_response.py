# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetHostAssetOverviewResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm': 'int',
        'baseline': 'int',
        'cpu': 'int',
        'host_num': 'int',
        'offline': 'int',
        'protecting': 'int',
        'protection_exception': 'int',
        'risk': 'int',
        'unprotected': 'int',
        'unprotected_cpu': 'int',
        'virus': 'int',
        'vuln': 'int'
    }

    attribute_map = {
        'alarm': 'Alarm',
        'baseline': 'Baseline',
        'cpu': 'Cpu',
        'host_num': 'HostNum',
        'offline': 'Offline',
        'protecting': 'Protecting',
        'protection_exception': 'ProtectionException',
        'risk': 'Risk',
        'unprotected': 'Unprotected',
        'unprotected_cpu': 'UnprotectedCpu',
        'virus': 'Virus',
        'vuln': 'Vuln'
    }

    def __init__(self, alarm=None, baseline=None, cpu=None, host_num=None, offline=None, protecting=None, protection_exception=None, risk=None, unprotected=None, unprotected_cpu=None, virus=None, vuln=None, _configuration=None):  # noqa: E501
        """GetHostAssetOverviewResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm = None
        self._baseline = None
        self._cpu = None
        self._host_num = None
        self._offline = None
        self._protecting = None
        self._protection_exception = None
        self._risk = None
        self._unprotected = None
        self._unprotected_cpu = None
        self._virus = None
        self._vuln = None
        self.discriminator = None

        if alarm is not None:
            self.alarm = alarm
        if baseline is not None:
            self.baseline = baseline
        if cpu is not None:
            self.cpu = cpu
        if host_num is not None:
            self.host_num = host_num
        if offline is not None:
            self.offline = offline
        if protecting is not None:
            self.protecting = protecting
        if protection_exception is not None:
            self.protection_exception = protection_exception
        if risk is not None:
            self.risk = risk
        if unprotected is not None:
            self.unprotected = unprotected
        if unprotected_cpu is not None:
            self.unprotected_cpu = unprotected_cpu
        if virus is not None:
            self.virus = virus
        if vuln is not None:
            self.vuln = vuln

    @property
    def alarm(self):
        """Gets the alarm of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The alarm of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._alarm

    @alarm.setter
    def alarm(self, alarm):
        """Sets the alarm of this GetHostAssetOverviewResponse.


        :param alarm: The alarm of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._alarm = alarm

    @property
    def baseline(self):
        """Gets the baseline of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The baseline of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._baseline

    @baseline.setter
    def baseline(self, baseline):
        """Sets the baseline of this GetHostAssetOverviewResponse.


        :param baseline: The baseline of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._baseline = baseline

    @property
    def cpu(self):
        """Gets the cpu of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The cpu of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._cpu

    @cpu.setter
    def cpu(self, cpu):
        """Sets the cpu of this GetHostAssetOverviewResponse.


        :param cpu: The cpu of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._cpu = cpu

    @property
    def host_num(self):
        """Gets the host_num of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The host_num of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._host_num

    @host_num.setter
    def host_num(self, host_num):
        """Sets the host_num of this GetHostAssetOverviewResponse.


        :param host_num: The host_num of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._host_num = host_num

    @property
    def offline(self):
        """Gets the offline of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The offline of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._offline

    @offline.setter
    def offline(self, offline):
        """Sets the offline of this GetHostAssetOverviewResponse.


        :param offline: The offline of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._offline = offline

    @property
    def protecting(self):
        """Gets the protecting of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The protecting of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._protecting

    @protecting.setter
    def protecting(self, protecting):
        """Sets the protecting of this GetHostAssetOverviewResponse.


        :param protecting: The protecting of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._protecting = protecting

    @property
    def protection_exception(self):
        """Gets the protection_exception of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The protection_exception of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._protection_exception

    @protection_exception.setter
    def protection_exception(self, protection_exception):
        """Sets the protection_exception of this GetHostAssetOverviewResponse.


        :param protection_exception: The protection_exception of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._protection_exception = protection_exception

    @property
    def risk(self):
        """Gets the risk of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The risk of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this GetHostAssetOverviewResponse.


        :param risk: The risk of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._risk = risk

    @property
    def unprotected(self):
        """Gets the unprotected of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The unprotected of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._unprotected

    @unprotected.setter
    def unprotected(self, unprotected):
        """Sets the unprotected of this GetHostAssetOverviewResponse.


        :param unprotected: The unprotected of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._unprotected = unprotected

    @property
    def unprotected_cpu(self):
        """Gets the unprotected_cpu of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The unprotected_cpu of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._unprotected_cpu

    @unprotected_cpu.setter
    def unprotected_cpu(self, unprotected_cpu):
        """Sets the unprotected_cpu of this GetHostAssetOverviewResponse.


        :param unprotected_cpu: The unprotected_cpu of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._unprotected_cpu = unprotected_cpu

    @property
    def virus(self):
        """Gets the virus of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The virus of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._virus

    @virus.setter
    def virus(self, virus):
        """Sets the virus of this GetHostAssetOverviewResponse.


        :param virus: The virus of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._virus = virus

    @property
    def vuln(self):
        """Gets the vuln of this GetHostAssetOverviewResponse.  # noqa: E501


        :return: The vuln of this GetHostAssetOverviewResponse.  # noqa: E501
        :rtype: int
        """
        return self._vuln

    @vuln.setter
    def vuln(self, vuln):
        """Sets the vuln of this GetHostAssetOverviewResponse.


        :param vuln: The vuln of this GetHostAssetOverviewResponse.  # noqa: E501
        :type: int
        """

        self._vuln = vuln

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetHostAssetOverviewResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetHostAssetOverviewResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetHostAssetOverviewResponse):
            return True

        return self.to_dict() != other.to_dict()
