# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListUserBehaviorDataAPIV2Output(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_time': 'str',
        'channel_name': 'str',
        'external_user_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'invite_code': 'str',
        'join_at': 'list[int]',
        'leave_at': 'list[int]',
        'region': 'str',
        'silence_status': 'int',
        'user_device': 'list[str]',
        'user_id': 'int',
        'user_name': 'str',
        'user_tel': 'str',
        'watch_time': 'int'
    }

    attribute_map = {
        'access_time': 'AccessTime',
        'channel_name': 'ChannelName',
        'external_user_id': 'ExternalUserId',
        'extra': 'Extra',
        'ip': 'IP',
        'invite_code': 'InviteCode',
        'join_at': 'JoinAt',
        'leave_at': 'LeaveAt',
        'region': 'Region',
        'silence_status': 'SilenceStatus',
        'user_device': 'UserDevice',
        'user_id': 'UserId',
        'user_name': 'UserName',
        'user_tel': 'UserTel',
        'watch_time': 'WatchTime'
    }

    def __init__(self, access_time=None, channel_name=None, external_user_id=None, extra=None, ip=None, invite_code=None, join_at=None, leave_at=None, region=None, silence_status=None, user_device=None, user_id=None, user_name=None, user_tel=None, watch_time=None, _configuration=None):  # noqa: E501
        """DataForListUserBehaviorDataAPIV2Output - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_time = None
        self._channel_name = None
        self._external_user_id = None
        self._extra = None
        self._ip = None
        self._invite_code = None
        self._join_at = None
        self._leave_at = None
        self._region = None
        self._silence_status = None
        self._user_device = None
        self._user_id = None
        self._user_name = None
        self._user_tel = None
        self._watch_time = None
        self.discriminator = None

        if access_time is not None:
            self.access_time = access_time
        if channel_name is not None:
            self.channel_name = channel_name
        if external_user_id is not None:
            self.external_user_id = external_user_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if invite_code is not None:
            self.invite_code = invite_code
        if join_at is not None:
            self.join_at = join_at
        if leave_at is not None:
            self.leave_at = leave_at
        if region is not None:
            self.region = region
        if silence_status is not None:
            self.silence_status = silence_status
        if user_device is not None:
            self.user_device = user_device
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name
        if user_tel is not None:
            self.user_tel = user_tel
        if watch_time is not None:
            self.watch_time = watch_time

    @property
    def access_time(self):
        """Gets the access_time of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The access_time of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._access_time

    @access_time.setter
    def access_time(self, access_time):
        """Sets the access_time of this DataForListUserBehaviorDataAPIV2Output.


        :param access_time: The access_time of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._access_time = access_time

    @property
    def channel_name(self):
        """Gets the channel_name of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The channel_name of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._channel_name

    @channel_name.setter
    def channel_name(self, channel_name):
        """Sets the channel_name of this DataForListUserBehaviorDataAPIV2Output.


        :param channel_name: The channel_name of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._channel_name = channel_name

    @property
    def external_user_id(self):
        """Gets the external_user_id of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The external_user_id of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._external_user_id

    @external_user_id.setter
    def external_user_id(self, external_user_id):
        """Sets the external_user_id of this DataForListUserBehaviorDataAPIV2Output.


        :param external_user_id: The external_user_id of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._external_user_id = external_user_id

    @property
    def extra(self):
        """Gets the extra of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The extra of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this DataForListUserBehaviorDataAPIV2Output.


        :param extra: The extra of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The ip of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this DataForListUserBehaviorDataAPIV2Output.


        :param ip: The ip of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def invite_code(self):
        """Gets the invite_code of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The invite_code of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._invite_code

    @invite_code.setter
    def invite_code(self, invite_code):
        """Sets the invite_code of this DataForListUserBehaviorDataAPIV2Output.


        :param invite_code: The invite_code of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._invite_code = invite_code

    @property
    def join_at(self):
        """Gets the join_at of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The join_at of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: list[int]
        """
        return self._join_at

    @join_at.setter
    def join_at(self, join_at):
        """Sets the join_at of this DataForListUserBehaviorDataAPIV2Output.


        :param join_at: The join_at of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: list[int]
        """

        self._join_at = join_at

    @property
    def leave_at(self):
        """Gets the leave_at of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The leave_at of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: list[int]
        """
        return self._leave_at

    @leave_at.setter
    def leave_at(self, leave_at):
        """Sets the leave_at of this DataForListUserBehaviorDataAPIV2Output.


        :param leave_at: The leave_at of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: list[int]
        """

        self._leave_at = leave_at

    @property
    def region(self):
        """Gets the region of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The region of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListUserBehaviorDataAPIV2Output.


        :param region: The region of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def silence_status(self):
        """Gets the silence_status of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The silence_status of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._silence_status

    @silence_status.setter
    def silence_status(self, silence_status):
        """Sets the silence_status of this DataForListUserBehaviorDataAPIV2Output.


        :param silence_status: The silence_status of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._silence_status = silence_status

    @property
    def user_device(self):
        """Gets the user_device of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The user_device of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: list[str]
        """
        return self._user_device

    @user_device.setter
    def user_device(self, user_device):
        """Sets the user_device of this DataForListUserBehaviorDataAPIV2Output.


        :param user_device: The user_device of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: list[str]
        """

        self._user_device = user_device

    @property
    def user_id(self):
        """Gets the user_id of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The user_id of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this DataForListUserBehaviorDataAPIV2Output.


        :param user_id: The user_id of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The user_name of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this DataForListUserBehaviorDataAPIV2Output.


        :param user_name: The user_name of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def user_tel(self):
        """Gets the user_tel of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The user_tel of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: str
        """
        return self._user_tel

    @user_tel.setter
    def user_tel(self, user_tel):
        """Sets the user_tel of this DataForListUserBehaviorDataAPIV2Output.


        :param user_tel: The user_tel of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: str
        """

        self._user_tel = user_tel

    @property
    def watch_time(self):
        """Gets the watch_time of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501


        :return: The watch_time of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :rtype: int
        """
        return self._watch_time

    @watch_time.setter
    def watch_time(self, watch_time):
        """Sets the watch_time of this DataForListUserBehaviorDataAPIV2Output.


        :param watch_time: The watch_time of this DataForListUserBehaviorDataAPIV2Output.  # noqa: E501
        :type: int
        """

        self._watch_time = watch_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListUserBehaviorDataAPIV2Output, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListUserBehaviorDataAPIV2Output):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListUserBehaviorDataAPIV2Output):
            return True

        return self.to_dict() != other.to_dict()
