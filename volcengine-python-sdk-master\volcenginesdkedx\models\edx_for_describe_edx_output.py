# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EDXForDescribeEDXOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'as_number': 'str',
        'area': 'str',
        'create_time': 'str',
        'description': 'str',
        'instance_id': 'str',
        'name': 'str',
        'state': 'str'
    }

    attribute_map = {
        'as_number': 'ASNumber',
        'area': 'Area',
        'create_time': 'CreateTime',
        'description': 'Description',
        'instance_id': 'InstanceId',
        'name': 'Name',
        'state': 'State'
    }

    def __init__(self, as_number=None, area=None, create_time=None, description=None, instance_id=None, name=None, state=None, _configuration=None):  # noqa: E501
        """EDXForDescribeEDXOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._as_number = None
        self._area = None
        self._create_time = None
        self._description = None
        self._instance_id = None
        self._name = None
        self._state = None
        self.discriminator = None

        if as_number is not None:
            self.as_number = as_number
        if area is not None:
            self.area = area
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if instance_id is not None:
            self.instance_id = instance_id
        if name is not None:
            self.name = name
        if state is not None:
            self.state = state

    @property
    def as_number(self):
        """Gets the as_number of this EDXForDescribeEDXOutput.  # noqa: E501


        :return: The as_number of this EDXForDescribeEDXOutput.  # noqa: E501
        :rtype: str
        """
        return self._as_number

    @as_number.setter
    def as_number(self, as_number):
        """Sets the as_number of this EDXForDescribeEDXOutput.


        :param as_number: The as_number of this EDXForDescribeEDXOutput.  # noqa: E501
        :type: str
        """

        self._as_number = as_number

    @property
    def area(self):
        """Gets the area of this EDXForDescribeEDXOutput.  # noqa: E501


        :return: The area of this EDXForDescribeEDXOutput.  # noqa: E501
        :rtype: str
        """
        return self._area

    @area.setter
    def area(self, area):
        """Sets the area of this EDXForDescribeEDXOutput.


        :param area: The area of this EDXForDescribeEDXOutput.  # noqa: E501
        :type: str
        """

        self._area = area

    @property
    def create_time(self):
        """Gets the create_time of this EDXForDescribeEDXOutput.  # noqa: E501


        :return: The create_time of this EDXForDescribeEDXOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this EDXForDescribeEDXOutput.


        :param create_time: The create_time of this EDXForDescribeEDXOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this EDXForDescribeEDXOutput.  # noqa: E501


        :return: The description of this EDXForDescribeEDXOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this EDXForDescribeEDXOutput.


        :param description: The description of this EDXForDescribeEDXOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def instance_id(self):
        """Gets the instance_id of this EDXForDescribeEDXOutput.  # noqa: E501


        :return: The instance_id of this EDXForDescribeEDXOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this EDXForDescribeEDXOutput.


        :param instance_id: The instance_id of this EDXForDescribeEDXOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def name(self):
        """Gets the name of this EDXForDescribeEDXOutput.  # noqa: E501


        :return: The name of this EDXForDescribeEDXOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this EDXForDescribeEDXOutput.


        :param name: The name of this EDXForDescribeEDXOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def state(self):
        """Gets the state of this EDXForDescribeEDXOutput.  # noqa: E501


        :return: The state of this EDXForDescribeEDXOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this EDXForDescribeEDXOutput.


        :param state: The state of this EDXForDescribeEDXOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EDXForDescribeEDXOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EDXForDescribeEDXOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EDXForDescribeEDXOutput):
            return True

        return self.to_dict() != other.to_dict()
