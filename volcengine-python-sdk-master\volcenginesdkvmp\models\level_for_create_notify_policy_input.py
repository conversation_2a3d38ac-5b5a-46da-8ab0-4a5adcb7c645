# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LevelForCreateNotifyPolicyInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'channels': 'list[str]',
        'contact_group_ids': 'list[str]',
        'level': 'str',
        'resolved_channels': 'list[str]'
    }

    attribute_map = {
        'channels': 'Channels',
        'contact_group_ids': 'ContactGroupIds',
        'level': 'Level',
        'resolved_channels': 'ResolvedChannels'
    }

    def __init__(self, channels=None, contact_group_ids=None, level=None, resolved_channels=None, _configuration=None):  # noqa: E501
        """LevelForCreateNotifyPolicyInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._channels = None
        self._contact_group_ids = None
        self._level = None
        self._resolved_channels = None
        self.discriminator = None

        if channels is not None:
            self.channels = channels
        if contact_group_ids is not None:
            self.contact_group_ids = contact_group_ids
        if level is not None:
            self.level = level
        if resolved_channels is not None:
            self.resolved_channels = resolved_channels

    @property
    def channels(self):
        """Gets the channels of this LevelForCreateNotifyPolicyInput.  # noqa: E501


        :return: The channels of this LevelForCreateNotifyPolicyInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._channels

    @channels.setter
    def channels(self, channels):
        """Sets the channels of this LevelForCreateNotifyPolicyInput.


        :param channels: The channels of this LevelForCreateNotifyPolicyInput.  # noqa: E501
        :type: list[str]
        """

        self._channels = channels

    @property
    def contact_group_ids(self):
        """Gets the contact_group_ids of this LevelForCreateNotifyPolicyInput.  # noqa: E501


        :return: The contact_group_ids of this LevelForCreateNotifyPolicyInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._contact_group_ids

    @contact_group_ids.setter
    def contact_group_ids(self, contact_group_ids):
        """Sets the contact_group_ids of this LevelForCreateNotifyPolicyInput.


        :param contact_group_ids: The contact_group_ids of this LevelForCreateNotifyPolicyInput.  # noqa: E501
        :type: list[str]
        """

        self._contact_group_ids = contact_group_ids

    @property
    def level(self):
        """Gets the level of this LevelForCreateNotifyPolicyInput.  # noqa: E501


        :return: The level of this LevelForCreateNotifyPolicyInput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this LevelForCreateNotifyPolicyInput.


        :param level: The level of this LevelForCreateNotifyPolicyInput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def resolved_channels(self):
        """Gets the resolved_channels of this LevelForCreateNotifyPolicyInput.  # noqa: E501


        :return: The resolved_channels of this LevelForCreateNotifyPolicyInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._resolved_channels

    @resolved_channels.setter
    def resolved_channels(self, resolved_channels):
        """Sets the resolved_channels of this LevelForCreateNotifyPolicyInput.


        :param resolved_channels: The resolved_channels of this LevelForCreateNotifyPolicyInput.  # noqa: E501
        :type: list[str]
        """

        self._resolved_channels = resolved_channels

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LevelForCreateNotifyPolicyInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LevelForCreateNotifyPolicyInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LevelForCreateNotifyPolicyInput):
            return True

        return self.to_dict() != other.to_dict()
