# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListViewsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'countries': 'list[CountryForListViewsOutput]',
        'version': 'str'
    }

    attribute_map = {
        'countries': 'Countries',
        'version': 'Version'
    }

    def __init__(self, countries=None, version=None, _configuration=None):  # noqa: E501
        """ListViewsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._countries = None
        self._version = None
        self.discriminator = None

        if countries is not None:
            self.countries = countries
        if version is not None:
            self.version = version

    @property
    def countries(self):
        """Gets the countries of this ListViewsResponse.  # noqa: E501


        :return: The countries of this ListViewsResponse.  # noqa: E501
        :rtype: list[CountryForListViewsOutput]
        """
        return self._countries

    @countries.setter
    def countries(self, countries):
        """Sets the countries of this ListViewsResponse.


        :param countries: The countries of this ListViewsResponse.  # noqa: E501
        :type: list[CountryForListViewsOutput]
        """

        self._countries = countries

    @property
    def version(self):
        """Gets the version of this ListViewsResponse.  # noqa: E501


        :return: The version of this ListViewsResponse.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this ListViewsResponse.


        :param version: The version of this ListViewsResponse.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListViewsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListViewsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListViewsResponse):
            return True

        return self.to_dict() != other.to_dict()
