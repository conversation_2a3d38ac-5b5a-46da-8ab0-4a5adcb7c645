# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeGroupAttributeForCreateClusterInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'charge_type': 'str',
        'data_disks': 'list[DataDiskForCreateClusterInput]',
        'ecs_instance_types': 'list[str]',
        'ecs_key_pair_name': 'str',
        'ecs_password': 'str',
        'node_count': 'int',
        'node_group_name': 'str',
        'node_group_type': 'str',
        'subnet_ids': 'list[str]',
        'system_disk': 'SystemDiskForCreateClusterInput',
        'with_public_ip': 'bool',
        'zone_id': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'charge_type': 'ChargeType',
        'data_disks': 'DataDisks',
        'ecs_instance_types': 'EcsInstanceTypes',
        'ecs_key_pair_name': 'EcsKeyPairName',
        'ecs_password': 'EcsPassword',
        'node_count': 'NodeCount',
        'node_group_name': 'NodeGroupName',
        'node_group_type': 'NodeGroupType',
        'subnet_ids': 'SubnetIds',
        'system_disk': 'SystemDisk',
        'with_public_ip': 'WithPublicIp',
        'zone_id': 'ZoneId'
    }

    def __init__(self, bandwidth=None, charge_type=None, data_disks=None, ecs_instance_types=None, ecs_key_pair_name=None, ecs_password=None, node_count=None, node_group_name=None, node_group_type=None, subnet_ids=None, system_disk=None, with_public_ip=None, zone_id=None, _configuration=None):  # noqa: E501
        """NodeGroupAttributeForCreateClusterInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._charge_type = None
        self._data_disks = None
        self._ecs_instance_types = None
        self._ecs_key_pair_name = None
        self._ecs_password = None
        self._node_count = None
        self._node_group_name = None
        self._node_group_type = None
        self._subnet_ids = None
        self._system_disk = None
        self._with_public_ip = None
        self._zone_id = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if charge_type is not None:
            self.charge_type = charge_type
        if data_disks is not None:
            self.data_disks = data_disks
        if ecs_instance_types is not None:
            self.ecs_instance_types = ecs_instance_types
        if ecs_key_pair_name is not None:
            self.ecs_key_pair_name = ecs_key_pair_name
        if ecs_password is not None:
            self.ecs_password = ecs_password
        if node_count is not None:
            self.node_count = node_count
        if node_group_name is not None:
            self.node_group_name = node_group_name
        if node_group_type is not None:
            self.node_group_type = node_group_type
        if subnet_ids is not None:
            self.subnet_ids = subnet_ids
        if system_disk is not None:
            self.system_disk = system_disk
        if with_public_ip is not None:
            self.with_public_ip = with_public_ip
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def bandwidth(self):
        """Gets the bandwidth of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The bandwidth of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this NodeGroupAttributeForCreateClusterInput.


        :param bandwidth: The bandwidth of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def charge_type(self):
        """Gets the charge_type of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The charge_type of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this NodeGroupAttributeForCreateClusterInput.


        :param charge_type: The charge_type of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["PRE", "POST"]  # noqa: E501
        if (self._configuration.client_side_validation and
                charge_type not in allowed_values):
            raise ValueError(
                "Invalid value for `charge_type` ({0}), must be one of {1}"  # noqa: E501
                .format(charge_type, allowed_values)
            )

        self._charge_type = charge_type

    @property
    def data_disks(self):
        """Gets the data_disks of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The data_disks of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: list[DataDiskForCreateClusterInput]
        """
        return self._data_disks

    @data_disks.setter
    def data_disks(self, data_disks):
        """Sets the data_disks of this NodeGroupAttributeForCreateClusterInput.


        :param data_disks: The data_disks of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: list[DataDiskForCreateClusterInput]
        """

        self._data_disks = data_disks

    @property
    def ecs_instance_types(self):
        """Gets the ecs_instance_types of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The ecs_instance_types of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ecs_instance_types

    @ecs_instance_types.setter
    def ecs_instance_types(self, ecs_instance_types):
        """Sets the ecs_instance_types of this NodeGroupAttributeForCreateClusterInput.


        :param ecs_instance_types: The ecs_instance_types of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: list[str]
        """

        self._ecs_instance_types = ecs_instance_types

    @property
    def ecs_key_pair_name(self):
        """Gets the ecs_key_pair_name of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The ecs_key_pair_name of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_key_pair_name

    @ecs_key_pair_name.setter
    def ecs_key_pair_name(self, ecs_key_pair_name):
        """Sets the ecs_key_pair_name of this NodeGroupAttributeForCreateClusterInput.


        :param ecs_key_pair_name: The ecs_key_pair_name of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._ecs_key_pair_name = ecs_key_pair_name

    @property
    def ecs_password(self):
        """Gets the ecs_password of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The ecs_password of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_password

    @ecs_password.setter
    def ecs_password(self, ecs_password):
        """Sets the ecs_password of this NodeGroupAttributeForCreateClusterInput.


        :param ecs_password: The ecs_password of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._ecs_password = ecs_password

    @property
    def node_count(self):
        """Gets the node_count of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The node_count of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: int
        """
        return self._node_count

    @node_count.setter
    def node_count(self, node_count):
        """Sets the node_count of this NodeGroupAttributeForCreateClusterInput.


        :param node_count: The node_count of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: int
        """

        self._node_count = node_count

    @property
    def node_group_name(self):
        """Gets the node_group_name of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The node_group_name of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._node_group_name

    @node_group_name.setter
    def node_group_name(self, node_group_name):
        """Sets the node_group_name of this NodeGroupAttributeForCreateClusterInput.


        :param node_group_name: The node_group_name of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._node_group_name = node_group_name

    @property
    def node_group_type(self):
        """Gets the node_group_type of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The node_group_type of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._node_group_type

    @node_group_type.setter
    def node_group_type(self, node_group_type):
        """Sets the node_group_type of this NodeGroupAttributeForCreateClusterInput.


        :param node_group_type: The node_group_type of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["MASTER", "CORE", "TASK"]  # noqa: E501
        if (self._configuration.client_side_validation and
                node_group_type not in allowed_values):
            raise ValueError(
                "Invalid value for `node_group_type` ({0}), must be one of {1}"  # noqa: E501
                .format(node_group_type, allowed_values)
            )

        self._node_group_type = node_group_type

    @property
    def subnet_ids(self):
        """Gets the subnet_ids of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The subnet_ids of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._subnet_ids

    @subnet_ids.setter
    def subnet_ids(self, subnet_ids):
        """Sets the subnet_ids of this NodeGroupAttributeForCreateClusterInput.


        :param subnet_ids: The subnet_ids of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: list[str]
        """

        self._subnet_ids = subnet_ids

    @property
    def system_disk(self):
        """Gets the system_disk of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The system_disk of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: SystemDiskForCreateClusterInput
        """
        return self._system_disk

    @system_disk.setter
    def system_disk(self, system_disk):
        """Sets the system_disk of this NodeGroupAttributeForCreateClusterInput.


        :param system_disk: The system_disk of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: SystemDiskForCreateClusterInput
        """

        self._system_disk = system_disk

    @property
    def with_public_ip(self):
        """Gets the with_public_ip of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The with_public_ip of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: bool
        """
        return self._with_public_ip

    @with_public_ip.setter
    def with_public_ip(self, with_public_ip):
        """Sets the with_public_ip of this NodeGroupAttributeForCreateClusterInput.


        :param with_public_ip: The with_public_ip of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: bool
        """

        self._with_public_ip = with_public_ip

    @property
    def zone_id(self):
        """Gets the zone_id of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501


        :return: The zone_id of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this NodeGroupAttributeForCreateClusterInput.


        :param zone_id: The zone_id of this NodeGroupAttributeForCreateClusterInput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeGroupAttributeForCreateClusterInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeGroupAttributeForCreateClusterInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeGroupAttributeForCreateClusterInput):
            return True

        return self.to_dict() != other.to_dict()
