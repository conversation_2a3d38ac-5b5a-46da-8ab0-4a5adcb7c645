# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetDevFingerprintProcessOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'asset_id': 'str',
        'asset_name': 'str',
        'checksum': 'str',
        'cmdline': 'str',
        'comm': 'str',
        'exe': 'str',
        'id': 'str',
        'pid': 'str',
        'ppid': 'str',
        'private_ip': 'str',
        'public_ip': 'str',
        'start_time': 'int',
        'state': 'str',
        'status': 'str',
        'uid': 'str',
        'update_time': 'int',
        'username': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'asset_id': 'AssetId',
        'asset_name': 'AssetName',
        'checksum': 'Checksum',
        'cmdline': 'Cmdline',
        'comm': 'Comm',
        'exe': 'Exe',
        'id': 'ID',
        'pid': 'Pid',
        'ppid': 'Ppid',
        'private_ip': 'PrivateIP',
        'public_ip': 'PublicIP',
        'start_time': 'StartTime',
        'state': 'State',
        'status': 'Status',
        'uid': 'Uid',
        'update_time': 'UpdateTime',
        'username': 'Username'
    }

    def __init__(self, account_id=None, asset_id=None, asset_name=None, checksum=None, cmdline=None, comm=None, exe=None, id=None, pid=None, ppid=None, private_ip=None, public_ip=None, start_time=None, state=None, status=None, uid=None, update_time=None, username=None, _configuration=None):  # noqa: E501
        """DataForGetDevFingerprintProcessOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._asset_id = None
        self._asset_name = None
        self._checksum = None
        self._cmdline = None
        self._comm = None
        self._exe = None
        self._id = None
        self._pid = None
        self._ppid = None
        self._private_ip = None
        self._public_ip = None
        self._start_time = None
        self._state = None
        self._status = None
        self._uid = None
        self._update_time = None
        self._username = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_name is not None:
            self.asset_name = asset_name
        if checksum is not None:
            self.checksum = checksum
        if cmdline is not None:
            self.cmdline = cmdline
        if comm is not None:
            self.comm = comm
        if exe is not None:
            self.exe = exe
        if id is not None:
            self.id = id
        if pid is not None:
            self.pid = pid
        if ppid is not None:
            self.ppid = ppid
        if private_ip is not None:
            self.private_ip = private_ip
        if public_ip is not None:
            self.public_ip = public_ip
        if start_time is not None:
            self.start_time = start_time
        if state is not None:
            self.state = state
        if status is not None:
            self.status = status
        if uid is not None:
            self.uid = uid
        if update_time is not None:
            self.update_time = update_time
        if username is not None:
            self.username = username

    @property
    def account_id(self):
        """Gets the account_id of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The account_id of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForGetDevFingerprintProcessOutput.


        :param account_id: The account_id of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def asset_id(self):
        """Gets the asset_id of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The asset_id of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this DataForGetDevFingerprintProcessOutput.


        :param asset_id: The asset_id of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_name(self):
        """Gets the asset_name of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The asset_name of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this DataForGetDevFingerprintProcessOutput.


        :param asset_name: The asset_name of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def checksum(self):
        """Gets the checksum of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The checksum of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._checksum

    @checksum.setter
    def checksum(self, checksum):
        """Sets the checksum of this DataForGetDevFingerprintProcessOutput.


        :param checksum: The checksum of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._checksum = checksum

    @property
    def cmdline(self):
        """Gets the cmdline of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The cmdline of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this DataForGetDevFingerprintProcessOutput.


        :param cmdline: The cmdline of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def comm(self):
        """Gets the comm of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The comm of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._comm

    @comm.setter
    def comm(self, comm):
        """Sets the comm of this DataForGetDevFingerprintProcessOutput.


        :param comm: The comm of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._comm = comm

    @property
    def exe(self):
        """Gets the exe of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The exe of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this DataForGetDevFingerprintProcessOutput.


        :param exe: The exe of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def id(self):
        """Gets the id of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The id of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForGetDevFingerprintProcessOutput.


        :param id: The id of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def pid(self):
        """Gets the pid of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The pid of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this DataForGetDevFingerprintProcessOutput.


        :param pid: The pid of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def ppid(self):
        """Gets the ppid of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The ppid of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._ppid

    @ppid.setter
    def ppid(self, ppid):
        """Sets the ppid of this DataForGetDevFingerprintProcessOutput.


        :param ppid: The ppid of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._ppid = ppid

    @property
    def private_ip(self):
        """Gets the private_ip of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The private_ip of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ip

    @private_ip.setter
    def private_ip(self, private_ip):
        """Sets the private_ip of this DataForGetDevFingerprintProcessOutput.


        :param private_ip: The private_ip of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._private_ip = private_ip

    @property
    def public_ip(self):
        """Gets the public_ip of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The public_ip of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_ip

    @public_ip.setter
    def public_ip(self, public_ip):
        """Sets the public_ip of this DataForGetDevFingerprintProcessOutput.


        :param public_ip: The public_ip of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._public_ip = public_ip

    @property
    def start_time(self):
        """Gets the start_time of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The start_time of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForGetDevFingerprintProcessOutput.


        :param start_time: The start_time of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def state(self):
        """Gets the state of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The state of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this DataForGetDevFingerprintProcessOutput.


        :param state: The state of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def status(self):
        """Gets the status of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The status of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForGetDevFingerprintProcessOutput.


        :param status: The status of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def uid(self):
        """Gets the uid of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The uid of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._uid

    @uid.setter
    def uid(self, uid):
        """Sets the uid of this DataForGetDevFingerprintProcessOutput.


        :param uid: The uid of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._uid = uid

    @property
    def update_time(self):
        """Gets the update_time of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The update_time of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForGetDevFingerprintProcessOutput.


        :param update_time: The update_time of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def username(self):
        """Gets the username of this DataForGetDevFingerprintProcessOutput.  # noqa: E501


        :return: The username of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this DataForGetDevFingerprintProcessOutput.


        :param username: The username of this DataForGetDevFingerprintProcessOutput.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetDevFingerprintProcessOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetDevFingerprintProcessOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetDevFingerprintProcessOutput):
            return True

        return self.to_dict() != other.to_dict()
