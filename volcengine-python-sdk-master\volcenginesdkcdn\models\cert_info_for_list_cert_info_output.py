# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CertInfoForListCertInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cert_fingerprint': 'CertFingerprintForListCertInfoOutput',
        'cert_id': 'str',
        'cert_name': 'str',
        'cert_type': 'str',
        'configured_domain': 'str',
        'configured_domain_detail': 'list[ConfiguredDomainDetailForListCertInfoOutput]',
        'desc': 'str',
        'dns_name': 'str',
        'effective_time': 'int',
        'encry_type': 'str',
        'expire_time': 'int',
        'source': 'str',
        'status': 'str'
    }

    attribute_map = {
        'cert_fingerprint': 'CertFingerprint',
        'cert_id': 'CertId',
        'cert_name': 'CertName',
        'cert_type': 'CertType',
        'configured_domain': 'ConfiguredDomain',
        'configured_domain_detail': 'ConfiguredDomainDetail',
        'desc': 'Desc',
        'dns_name': 'DnsName',
        'effective_time': 'EffectiveTime',
        'encry_type': 'EncryType',
        'expire_time': 'ExpireTime',
        'source': 'Source',
        'status': 'Status'
    }

    def __init__(self, cert_fingerprint=None, cert_id=None, cert_name=None, cert_type=None, configured_domain=None, configured_domain_detail=None, desc=None, dns_name=None, effective_time=None, encry_type=None, expire_time=None, source=None, status=None, _configuration=None):  # noqa: E501
        """CertInfoForListCertInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cert_fingerprint = None
        self._cert_id = None
        self._cert_name = None
        self._cert_type = None
        self._configured_domain = None
        self._configured_domain_detail = None
        self._desc = None
        self._dns_name = None
        self._effective_time = None
        self._encry_type = None
        self._expire_time = None
        self._source = None
        self._status = None
        self.discriminator = None

        if cert_fingerprint is not None:
            self.cert_fingerprint = cert_fingerprint
        if cert_id is not None:
            self.cert_id = cert_id
        if cert_name is not None:
            self.cert_name = cert_name
        if cert_type is not None:
            self.cert_type = cert_type
        if configured_domain is not None:
            self.configured_domain = configured_domain
        if configured_domain_detail is not None:
            self.configured_domain_detail = configured_domain_detail
        if desc is not None:
            self.desc = desc
        if dns_name is not None:
            self.dns_name = dns_name
        if effective_time is not None:
            self.effective_time = effective_time
        if encry_type is not None:
            self.encry_type = encry_type
        if expire_time is not None:
            self.expire_time = expire_time
        if source is not None:
            self.source = source
        if status is not None:
            self.status = status

    @property
    def cert_fingerprint(self):
        """Gets the cert_fingerprint of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The cert_fingerprint of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: CertFingerprintForListCertInfoOutput
        """
        return self._cert_fingerprint

    @cert_fingerprint.setter
    def cert_fingerprint(self, cert_fingerprint):
        """Sets the cert_fingerprint of this CertInfoForListCertInfoOutput.


        :param cert_fingerprint: The cert_fingerprint of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: CertFingerprintForListCertInfoOutput
        """

        self._cert_fingerprint = cert_fingerprint

    @property
    def cert_id(self):
        """Gets the cert_id of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The cert_id of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cert_id

    @cert_id.setter
    def cert_id(self, cert_id):
        """Sets the cert_id of this CertInfoForListCertInfoOutput.


        :param cert_id: The cert_id of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._cert_id = cert_id

    @property
    def cert_name(self):
        """Gets the cert_name of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The cert_name of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cert_name

    @cert_name.setter
    def cert_name(self, cert_name):
        """Sets the cert_name of this CertInfoForListCertInfoOutput.


        :param cert_name: The cert_name of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._cert_name = cert_name

    @property
    def cert_type(self):
        """Gets the cert_type of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The cert_type of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cert_type

    @cert_type.setter
    def cert_type(self, cert_type):
        """Sets the cert_type of this CertInfoForListCertInfoOutput.


        :param cert_type: The cert_type of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._cert_type = cert_type

    @property
    def configured_domain(self):
        """Gets the configured_domain of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The configured_domain of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._configured_domain

    @configured_domain.setter
    def configured_domain(self, configured_domain):
        """Sets the configured_domain of this CertInfoForListCertInfoOutput.


        :param configured_domain: The configured_domain of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._configured_domain = configured_domain

    @property
    def configured_domain_detail(self):
        """Gets the configured_domain_detail of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The configured_domain_detail of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: list[ConfiguredDomainDetailForListCertInfoOutput]
        """
        return self._configured_domain_detail

    @configured_domain_detail.setter
    def configured_domain_detail(self, configured_domain_detail):
        """Sets the configured_domain_detail of this CertInfoForListCertInfoOutput.


        :param configured_domain_detail: The configured_domain_detail of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: list[ConfiguredDomainDetailForListCertInfoOutput]
        """

        self._configured_domain_detail = configured_domain_detail

    @property
    def desc(self):
        """Gets the desc of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The desc of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._desc

    @desc.setter
    def desc(self, desc):
        """Sets the desc of this CertInfoForListCertInfoOutput.


        :param desc: The desc of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._desc = desc

    @property
    def dns_name(self):
        """Gets the dns_name of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The dns_name of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._dns_name

    @dns_name.setter
    def dns_name(self, dns_name):
        """Sets the dns_name of this CertInfoForListCertInfoOutput.


        :param dns_name: The dns_name of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._dns_name = dns_name

    @property
    def effective_time(self):
        """Gets the effective_time of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The effective_time of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._effective_time

    @effective_time.setter
    def effective_time(self, effective_time):
        """Sets the effective_time of this CertInfoForListCertInfoOutput.


        :param effective_time: The effective_time of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: int
        """

        self._effective_time = effective_time

    @property
    def encry_type(self):
        """Gets the encry_type of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The encry_type of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._encry_type

    @encry_type.setter
    def encry_type(self, encry_type):
        """Sets the encry_type of this CertInfoForListCertInfoOutput.


        :param encry_type: The encry_type of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._encry_type = encry_type

    @property
    def expire_time(self):
        """Gets the expire_time of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The expire_time of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this CertInfoForListCertInfoOutput.


        :param expire_time: The expire_time of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: int
        """

        self._expire_time = expire_time

    @property
    def source(self):
        """Gets the source of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The source of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._source

    @source.setter
    def source(self, source):
        """Sets the source of this CertInfoForListCertInfoOutput.


        :param source: The source of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._source = source

    @property
    def status(self):
        """Gets the status of this CertInfoForListCertInfoOutput.  # noqa: E501


        :return: The status of this CertInfoForListCertInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this CertInfoForListCertInfoOutput.


        :param status: The status of this CertInfoForListCertInfoOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CertInfoForListCertInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CertInfoForListCertInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CertInfoForListCertInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
