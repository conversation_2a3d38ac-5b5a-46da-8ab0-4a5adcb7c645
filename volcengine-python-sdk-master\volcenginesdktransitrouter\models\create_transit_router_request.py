# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateTransitRouterRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asn': 'int',
        'client_token': 'str',
        'description': 'str',
        'project_name': 'str',
        'tags': 'list[TagForCreateTransitRouterInput]',
        'transit_router_name': 'str'
    }

    attribute_map = {
        'asn': 'Asn',
        'client_token': 'ClientToken',
        'description': 'Description',
        'project_name': 'ProjectName',
        'tags': 'Tags',
        'transit_router_name': 'TransitRouterName'
    }

    def __init__(self, asn=None, client_token=None, description=None, project_name=None, tags=None, transit_router_name=None, _configuration=None):  # noqa: E501
        """CreateTransitRouterRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asn = None
        self._client_token = None
        self._description = None
        self._project_name = None
        self._tags = None
        self._transit_router_name = None
        self.discriminator = None

        if asn is not None:
            self.asn = asn
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if project_name is not None:
            self.project_name = project_name
        if tags is not None:
            self.tags = tags
        if transit_router_name is not None:
            self.transit_router_name = transit_router_name

    @property
    def asn(self):
        """Gets the asn of this CreateTransitRouterRequest.  # noqa: E501


        :return: The asn of this CreateTransitRouterRequest.  # noqa: E501
        :rtype: int
        """
        return self._asn

    @asn.setter
    def asn(self, asn):
        """Sets the asn of this CreateTransitRouterRequest.


        :param asn: The asn of this CreateTransitRouterRequest.  # noqa: E501
        :type: int
        """

        self._asn = asn

    @property
    def client_token(self):
        """Gets the client_token of this CreateTransitRouterRequest.  # noqa: E501


        :return: The client_token of this CreateTransitRouterRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateTransitRouterRequest.


        :param client_token: The client_token of this CreateTransitRouterRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateTransitRouterRequest.  # noqa: E501


        :return: The description of this CreateTransitRouterRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateTransitRouterRequest.


        :param description: The description of this CreateTransitRouterRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def project_name(self):
        """Gets the project_name of this CreateTransitRouterRequest.  # noqa: E501


        :return: The project_name of this CreateTransitRouterRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateTransitRouterRequest.


        :param project_name: The project_name of this CreateTransitRouterRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def tags(self):
        """Gets the tags of this CreateTransitRouterRequest.  # noqa: E501


        :return: The tags of this CreateTransitRouterRequest.  # noqa: E501
        :rtype: list[TagForCreateTransitRouterInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateTransitRouterRequest.


        :param tags: The tags of this CreateTransitRouterRequest.  # noqa: E501
        :type: list[TagForCreateTransitRouterInput]
        """

        self._tags = tags

    @property
    def transit_router_name(self):
        """Gets the transit_router_name of this CreateTransitRouterRequest.  # noqa: E501


        :return: The transit_router_name of this CreateTransitRouterRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_name

    @transit_router_name.setter
    def transit_router_name(self, transit_router_name):
        """Sets the transit_router_name of this CreateTransitRouterRequest.


        :param transit_router_name: The transit_router_name of this CreateTransitRouterRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_name = transit_router_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateTransitRouterRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateTransitRouterRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateTransitRouterRequest):
            return True

        return self.to_dict() != other.to_dict()
