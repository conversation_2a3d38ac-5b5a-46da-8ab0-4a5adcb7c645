# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DBEndpointForDescribeDBEndpointOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'db_addresses': 'list[DBAddressForDescribeDBEndpointOutput]',
        'endpoint_id': 'str',
        'endpoint_str': 'str',
        'endpoint_type': 'str',
        'network_type': 'str',
        'object_id': 'str',
        'subnet_id': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'db_addresses': 'DBAddresses',
        'endpoint_id': 'EndpointId',
        'endpoint_str': 'EndpointStr',
        'endpoint_type': 'EndpointType',
        'network_type': 'NetworkType',
        'object_id': 'ObjectId',
        'subnet_id': 'SubnetId',
        'vpc_id': 'VpcId'
    }

    def __init__(self, db_addresses=None, endpoint_id=None, endpoint_str=None, endpoint_type=None, network_type=None, object_id=None, subnet_id=None, vpc_id=None, _configuration=None):  # noqa: E501
        """DBEndpointForDescribeDBEndpointOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._db_addresses = None
        self._endpoint_id = None
        self._endpoint_str = None
        self._endpoint_type = None
        self._network_type = None
        self._object_id = None
        self._subnet_id = None
        self._vpc_id = None
        self.discriminator = None

        if db_addresses is not None:
            self.db_addresses = db_addresses
        if endpoint_id is not None:
            self.endpoint_id = endpoint_id
        if endpoint_str is not None:
            self.endpoint_str = endpoint_str
        if endpoint_type is not None:
            self.endpoint_type = endpoint_type
        if network_type is not None:
            self.network_type = network_type
        if object_id is not None:
            self.object_id = object_id
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def db_addresses(self):
        """Gets the db_addresses of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501


        :return: The db_addresses of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :rtype: list[DBAddressForDescribeDBEndpointOutput]
        """
        return self._db_addresses

    @db_addresses.setter
    def db_addresses(self, db_addresses):
        """Sets the db_addresses of this DBEndpointForDescribeDBEndpointOutput.


        :param db_addresses: The db_addresses of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :type: list[DBAddressForDescribeDBEndpointOutput]
        """

        self._db_addresses = db_addresses

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501


        :return: The endpoint_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this DBEndpointForDescribeDBEndpointOutput.


        :param endpoint_id: The endpoint_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_id = endpoint_id

    @property
    def endpoint_str(self):
        """Gets the endpoint_str of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501


        :return: The endpoint_str of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_str

    @endpoint_str.setter
    def endpoint_str(self, endpoint_str):
        """Sets the endpoint_str of this DBEndpointForDescribeDBEndpointOutput.


        :param endpoint_str: The endpoint_str of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_str = endpoint_str

    @property
    def endpoint_type(self):
        """Gets the endpoint_type of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501


        :return: The endpoint_type of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_type

    @endpoint_type.setter
    def endpoint_type(self, endpoint_type):
        """Sets the endpoint_type of this DBEndpointForDescribeDBEndpointOutput.


        :param endpoint_type: The endpoint_type of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :type: str
        """

        self._endpoint_type = endpoint_type

    @property
    def network_type(self):
        """Gets the network_type of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501


        :return: The network_type of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._network_type

    @network_type.setter
    def network_type(self, network_type):
        """Sets the network_type of this DBEndpointForDescribeDBEndpointOutput.


        :param network_type: The network_type of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :type: str
        """

        self._network_type = network_type

    @property
    def object_id(self):
        """Gets the object_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501


        :return: The object_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._object_id

    @object_id.setter
    def object_id(self, object_id):
        """Sets the object_id of this DBEndpointForDescribeDBEndpointOutput.


        :param object_id: The object_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :type: str
        """

        self._object_id = object_id

    @property
    def subnet_id(self):
        """Gets the subnet_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501


        :return: The subnet_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this DBEndpointForDescribeDBEndpointOutput.


        :param subnet_id: The subnet_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501


        :return: The vpc_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DBEndpointForDescribeDBEndpointOutput.


        :param vpc_id: The vpc_id of this DBEndpointForDescribeDBEndpointOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DBEndpointForDescribeDBEndpointOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DBEndpointForDescribeDBEndpointOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DBEndpointForDescribeDBEndpointOutput):
            return True

        return self.to_dict() != other.to_dict()
