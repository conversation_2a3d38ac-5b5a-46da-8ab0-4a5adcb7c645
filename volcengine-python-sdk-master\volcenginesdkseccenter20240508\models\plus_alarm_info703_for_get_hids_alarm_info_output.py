# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlusAlarmInfo703ForGetHidsAlarmInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'interrupt_number': 'str',
        'module_name': 'str'
    }

    attribute_map = {
        'interrupt_number': 'InterruptNumber',
        'module_name': 'ModuleName'
    }

    def __init__(self, interrupt_number=None, module_name=None, _configuration=None):  # noqa: E501
        """PlusAlarmInfo703ForGetHidsAlarmInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._interrupt_number = None
        self._module_name = None
        self.discriminator = None

        if interrupt_number is not None:
            self.interrupt_number = interrupt_number
        if module_name is not None:
            self.module_name = module_name

    @property
    def interrupt_number(self):
        """Gets the interrupt_number of this PlusAlarmInfo703ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The interrupt_number of this PlusAlarmInfo703ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._interrupt_number

    @interrupt_number.setter
    def interrupt_number(self, interrupt_number):
        """Sets the interrupt_number of this PlusAlarmInfo703ForGetHidsAlarmInfoOutput.


        :param interrupt_number: The interrupt_number of this PlusAlarmInfo703ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._interrupt_number = interrupt_number

    @property
    def module_name(self):
        """Gets the module_name of this PlusAlarmInfo703ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The module_name of this PlusAlarmInfo703ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._module_name

    @module_name.setter
    def module_name(self, module_name):
        """Sets the module_name of this PlusAlarmInfo703ForGetHidsAlarmInfoOutput.


        :param module_name: The module_name of this PlusAlarmInfo703ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._module_name = module_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlusAlarmInfo703ForGetHidsAlarmInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlusAlarmInfo703ForGetHidsAlarmInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlusAlarmInfo703ForGetHidsAlarmInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
