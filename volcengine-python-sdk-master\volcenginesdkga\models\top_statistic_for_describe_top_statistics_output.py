# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TopStatisticForDescribeTopStatisticsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'max_bandwidth': 'float',
        'max_bandwidth95': 'float',
        'max_connection_num': 'float',
        'name': 'str',
        'rank': 'int',
        'total_traffic': 'float'
    }

    attribute_map = {
        'id': 'ID',
        'max_bandwidth': 'MaxBandwidth',
        'max_bandwidth95': 'MaxBandwidth95',
        'max_connection_num': 'MaxConnectionNum',
        'name': 'Name',
        'rank': 'Rank',
        'total_traffic': 'TotalTraffic'
    }

    def __init__(self, id=None, max_bandwidth=None, max_bandwidth95=None, max_connection_num=None, name=None, rank=None, total_traffic=None, _configuration=None):  # noqa: E501
        """TopStatisticForDescribeTopStatisticsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._max_bandwidth = None
        self._max_bandwidth95 = None
        self._max_connection_num = None
        self._name = None
        self._rank = None
        self._total_traffic = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if max_bandwidth is not None:
            self.max_bandwidth = max_bandwidth
        if max_bandwidth95 is not None:
            self.max_bandwidth95 = max_bandwidth95
        if max_connection_num is not None:
            self.max_connection_num = max_connection_num
        if name is not None:
            self.name = name
        if rank is not None:
            self.rank = rank
        if total_traffic is not None:
            self.total_traffic = total_traffic

    @property
    def id(self):
        """Gets the id of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501


        :return: The id of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this TopStatisticForDescribeTopStatisticsOutput.


        :param id: The id of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def max_bandwidth(self):
        """Gets the max_bandwidth of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501


        :return: The max_bandwidth of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :rtype: float
        """
        return self._max_bandwidth

    @max_bandwidth.setter
    def max_bandwidth(self, max_bandwidth):
        """Sets the max_bandwidth of this TopStatisticForDescribeTopStatisticsOutput.


        :param max_bandwidth: The max_bandwidth of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :type: float
        """

        self._max_bandwidth = max_bandwidth

    @property
    def max_bandwidth95(self):
        """Gets the max_bandwidth95 of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501


        :return: The max_bandwidth95 of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :rtype: float
        """
        return self._max_bandwidth95

    @max_bandwidth95.setter
    def max_bandwidth95(self, max_bandwidth95):
        """Sets the max_bandwidth95 of this TopStatisticForDescribeTopStatisticsOutput.


        :param max_bandwidth95: The max_bandwidth95 of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :type: float
        """

        self._max_bandwidth95 = max_bandwidth95

    @property
    def max_connection_num(self):
        """Gets the max_connection_num of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501


        :return: The max_connection_num of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :rtype: float
        """
        return self._max_connection_num

    @max_connection_num.setter
    def max_connection_num(self, max_connection_num):
        """Sets the max_connection_num of this TopStatisticForDescribeTopStatisticsOutput.


        :param max_connection_num: The max_connection_num of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :type: float
        """

        self._max_connection_num = max_connection_num

    @property
    def name(self):
        """Gets the name of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501


        :return: The name of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this TopStatisticForDescribeTopStatisticsOutput.


        :param name: The name of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def rank(self):
        """Gets the rank of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501


        :return: The rank of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._rank

    @rank.setter
    def rank(self, rank):
        """Sets the rank of this TopStatisticForDescribeTopStatisticsOutput.


        :param rank: The rank of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._rank = rank

    @property
    def total_traffic(self):
        """Gets the total_traffic of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501


        :return: The total_traffic of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :rtype: float
        """
        return self._total_traffic

    @total_traffic.setter
    def total_traffic(self, total_traffic):
        """Sets the total_traffic of this TopStatisticForDescribeTopStatisticsOutput.


        :param total_traffic: The total_traffic of this TopStatisticForDescribeTopStatisticsOutput.  # noqa: E501
        :type: float
        """

        self._total_traffic = total_traffic

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TopStatisticForDescribeTopStatisticsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TopStatisticForDescribeTopStatisticsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TopStatisticForDescribeTopStatisticsOutput):
            return True

        return self.to_dict() != other.to_dict()
