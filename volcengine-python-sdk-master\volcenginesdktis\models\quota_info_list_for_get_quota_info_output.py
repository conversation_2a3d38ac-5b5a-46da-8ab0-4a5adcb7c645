# coding: utf-8

"""
    tis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuotaInfoListForGetQuotaInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'available_amount': 'int',
        'configuration_code': 'str',
        'package_name': 'str',
        'total_amount': 'int'
    }

    attribute_map = {
        'available_amount': 'availableAmount',
        'configuration_code': 'configurationCode',
        'package_name': 'packageName',
        'total_amount': 'totalAmount'
    }

    def __init__(self, available_amount=None, configuration_code=None, package_name=None, total_amount=None, _configuration=None):  # noqa: E501
        """QuotaInfoListForGetQuotaInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._available_amount = None
        self._configuration_code = None
        self._package_name = None
        self._total_amount = None
        self.discriminator = None

        if available_amount is not None:
            self.available_amount = available_amount
        if configuration_code is not None:
            self.configuration_code = configuration_code
        if package_name is not None:
            self.package_name = package_name
        if total_amount is not None:
            self.total_amount = total_amount

    @property
    def available_amount(self):
        """Gets the available_amount of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501


        :return: The available_amount of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._available_amount

    @available_amount.setter
    def available_amount(self, available_amount):
        """Sets the available_amount of this QuotaInfoListForGetQuotaInfoOutput.


        :param available_amount: The available_amount of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501
        :type: int
        """

        self._available_amount = available_amount

    @property
    def configuration_code(self):
        """Gets the configuration_code of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501


        :return: The configuration_code of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._configuration_code

    @configuration_code.setter
    def configuration_code(self, configuration_code):
        """Sets the configuration_code of this QuotaInfoListForGetQuotaInfoOutput.


        :param configuration_code: The configuration_code of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501
        :type: str
        """

        self._configuration_code = configuration_code

    @property
    def package_name(self):
        """Gets the package_name of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501


        :return: The package_name of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._package_name

    @package_name.setter
    def package_name(self, package_name):
        """Sets the package_name of this QuotaInfoListForGetQuotaInfoOutput.


        :param package_name: The package_name of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501
        :type: str
        """

        self._package_name = package_name

    @property
    def total_amount(self):
        """Gets the total_amount of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501


        :return: The total_amount of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_amount

    @total_amount.setter
    def total_amount(self, total_amount):
        """Sets the total_amount of this QuotaInfoListForGetQuotaInfoOutput.


        :param total_amount: The total_amount of this QuotaInfoListForGetQuotaInfoOutput.  # noqa: E501
        :type: int
        """

        self._total_amount = total_amount

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuotaInfoListForGetQuotaInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuotaInfoListForGetQuotaInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuotaInfoListForGetQuotaInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
