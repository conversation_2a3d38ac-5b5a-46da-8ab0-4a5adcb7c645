# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SourceClipForGetAITranslationProjectOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'clip_audio': 'PreviewVideoMutedForGetAITranslationProjectOutput',
        'clip_video': 'PreviewVideoMutedForGetAITranslationProjectOutput',
        'id': 'str',
        'track_id': 'str',
        'translation_type': 'str',
        'trim': 'TrimForGetAITranslationProjectOutput',
        'volume': 'int'
    }

    attribute_map = {
        'clip_audio': 'ClipAudio',
        'clip_video': 'ClipVideo',
        'id': 'Id',
        'track_id': 'TrackId',
        'translation_type': 'TranslationType',
        'trim': 'Trim',
        'volume': 'Volume'
    }

    def __init__(self, clip_audio=None, clip_video=None, id=None, track_id=None, translation_type=None, trim=None, volume=None, _configuration=None):  # noqa: E501
        """SourceClipForGetAITranslationProjectOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._clip_audio = None
        self._clip_video = None
        self._id = None
        self._track_id = None
        self._translation_type = None
        self._trim = None
        self._volume = None
        self.discriminator = None

        if clip_audio is not None:
            self.clip_audio = clip_audio
        if clip_video is not None:
            self.clip_video = clip_video
        if id is not None:
            self.id = id
        if track_id is not None:
            self.track_id = track_id
        if translation_type is not None:
            self.translation_type = translation_type
        if trim is not None:
            self.trim = trim
        if volume is not None:
            self.volume = volume

    @property
    def clip_audio(self):
        """Gets the clip_audio of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The clip_audio of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: PreviewVideoMutedForGetAITranslationProjectOutput
        """
        return self._clip_audio

    @clip_audio.setter
    def clip_audio(self, clip_audio):
        """Sets the clip_audio of this SourceClipForGetAITranslationProjectOutput.


        :param clip_audio: The clip_audio of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: PreviewVideoMutedForGetAITranslationProjectOutput
        """

        self._clip_audio = clip_audio

    @property
    def clip_video(self):
        """Gets the clip_video of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The clip_video of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: PreviewVideoMutedForGetAITranslationProjectOutput
        """
        return self._clip_video

    @clip_video.setter
    def clip_video(self, clip_video):
        """Sets the clip_video of this SourceClipForGetAITranslationProjectOutput.


        :param clip_video: The clip_video of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: PreviewVideoMutedForGetAITranslationProjectOutput
        """

        self._clip_video = clip_video

    @property
    def id(self):
        """Gets the id of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The id of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this SourceClipForGetAITranslationProjectOutput.


        :param id: The id of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def track_id(self):
        """Gets the track_id of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The track_id of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._track_id

    @track_id.setter
    def track_id(self, track_id):
        """Sets the track_id of this SourceClipForGetAITranslationProjectOutput.


        :param track_id: The track_id of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._track_id = track_id

    @property
    def translation_type(self):
        """Gets the translation_type of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The translation_type of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: str
        """
        return self._translation_type

    @translation_type.setter
    def translation_type(self, translation_type):
        """Sets the translation_type of this SourceClipForGetAITranslationProjectOutput.


        :param translation_type: The translation_type of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: str
        """

        self._translation_type = translation_type

    @property
    def trim(self):
        """Gets the trim of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The trim of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: TrimForGetAITranslationProjectOutput
        """
        return self._trim

    @trim.setter
    def trim(self, trim):
        """Sets the trim of this SourceClipForGetAITranslationProjectOutput.


        :param trim: The trim of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: TrimForGetAITranslationProjectOutput
        """

        self._trim = trim

    @property
    def volume(self):
        """Gets the volume of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501


        :return: The volume of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :rtype: int
        """
        return self._volume

    @volume.setter
    def volume(self, volume):
        """Sets the volume of this SourceClipForGetAITranslationProjectOutput.


        :param volume: The volume of this SourceClipForGetAITranslationProjectOutput.  # noqa: E501
        :type: int
        """

        self._volume = volume

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SourceClipForGetAITranslationProjectOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SourceClipForGetAITranslationProjectOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SourceClipForGetAITranslationProjectOutput):
            return True

        return self.to_dict() != other.to_dict()
