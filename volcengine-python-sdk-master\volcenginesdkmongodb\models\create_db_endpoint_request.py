# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDBEndpointRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'eip_ids': 'list[str]',
        'instance_id': 'str',
        'mongos_node_ids': 'list[str]',
        'network_type': 'str',
        'object_id': 'str'
    }

    attribute_map = {
        'eip_ids': 'EipIds',
        'instance_id': 'InstanceId',
        'mongos_node_ids': 'MongosNodeIds',
        'network_type': 'NetworkType',
        'object_id': 'ObjectId'
    }

    def __init__(self, eip_ids=None, instance_id=None, mongos_node_ids=None, network_type=None, object_id=None, _configuration=None):  # noqa: E501
        """CreateDBEndpointRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._eip_ids = None
        self._instance_id = None
        self._mongos_node_ids = None
        self._network_type = None
        self._object_id = None
        self.discriminator = None

        if eip_ids is not None:
            self.eip_ids = eip_ids
        self.instance_id = instance_id
        if mongos_node_ids is not None:
            self.mongos_node_ids = mongos_node_ids
        if network_type is not None:
            self.network_type = network_type
        if object_id is not None:
            self.object_id = object_id

    @property
    def eip_ids(self):
        """Gets the eip_ids of this CreateDBEndpointRequest.  # noqa: E501


        :return: The eip_ids of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._eip_ids

    @eip_ids.setter
    def eip_ids(self, eip_ids):
        """Sets the eip_ids of this CreateDBEndpointRequest.


        :param eip_ids: The eip_ids of this CreateDBEndpointRequest.  # noqa: E501
        :type: list[str]
        """

        self._eip_ids = eip_ids

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateDBEndpointRequest.  # noqa: E501


        :return: The instance_id of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateDBEndpointRequest.


        :param instance_id: The instance_id of this CreateDBEndpointRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def mongos_node_ids(self):
        """Gets the mongos_node_ids of this CreateDBEndpointRequest.  # noqa: E501


        :return: The mongos_node_ids of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._mongos_node_ids

    @mongos_node_ids.setter
    def mongos_node_ids(self, mongos_node_ids):
        """Sets the mongos_node_ids of this CreateDBEndpointRequest.


        :param mongos_node_ids: The mongos_node_ids of this CreateDBEndpointRequest.  # noqa: E501
        :type: list[str]
        """

        self._mongos_node_ids = mongos_node_ids

    @property
    def network_type(self):
        """Gets the network_type of this CreateDBEndpointRequest.  # noqa: E501


        :return: The network_type of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._network_type

    @network_type.setter
    def network_type(self, network_type):
        """Sets the network_type of this CreateDBEndpointRequest.


        :param network_type: The network_type of this CreateDBEndpointRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Private", "Public", "InnerPLB", "StorageInner"]  # noqa: E501
        if (self._configuration.client_side_validation and
                network_type not in allowed_values):
            raise ValueError(
                "Invalid value for `network_type` ({0}), must be one of {1}"  # noqa: E501
                .format(network_type, allowed_values)
            )

        self._network_type = network_type

    @property
    def object_id(self):
        """Gets the object_id of this CreateDBEndpointRequest.  # noqa: E501


        :return: The object_id of this CreateDBEndpointRequest.  # noqa: E501
        :rtype: str
        """
        return self._object_id

    @object_id.setter
    def object_id(self, object_id):
        """Sets the object_id of this CreateDBEndpointRequest.


        :param object_id: The object_id of this CreateDBEndpointRequest.  # noqa: E501
        :type: str
        """

        self._object_id = object_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDBEndpointRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDBEndpointRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDBEndpointRequest):
            return True

        return self.to_dict() != other.to_dict()
