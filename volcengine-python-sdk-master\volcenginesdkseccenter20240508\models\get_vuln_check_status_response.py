# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetVulnCheckStatusResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'last_check_time': 'int',
        'progress': 'int',
        'status': 'str'
    }

    attribute_map = {
        'last_check_time': 'LastCheckTime',
        'progress': 'Progress',
        'status': 'Status'
    }

    def __init__(self, last_check_time=None, progress=None, status=None, _configuration=None):  # noqa: E501
        """GetVulnCheckStatusResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._last_check_time = None
        self._progress = None
        self._status = None
        self.discriminator = None

        if last_check_time is not None:
            self.last_check_time = last_check_time
        if progress is not None:
            self.progress = progress
        if status is not None:
            self.status = status

    @property
    def last_check_time(self):
        """Gets the last_check_time of this GetVulnCheckStatusResponse.  # noqa: E501


        :return: The last_check_time of this GetVulnCheckStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._last_check_time

    @last_check_time.setter
    def last_check_time(self, last_check_time):
        """Sets the last_check_time of this GetVulnCheckStatusResponse.


        :param last_check_time: The last_check_time of this GetVulnCheckStatusResponse.  # noqa: E501
        :type: int
        """

        self._last_check_time = last_check_time

    @property
    def progress(self):
        """Gets the progress of this GetVulnCheckStatusResponse.  # noqa: E501


        :return: The progress of this GetVulnCheckStatusResponse.  # noqa: E501
        :rtype: int
        """
        return self._progress

    @progress.setter
    def progress(self, progress):
        """Sets the progress of this GetVulnCheckStatusResponse.


        :param progress: The progress of this GetVulnCheckStatusResponse.  # noqa: E501
        :type: int
        """

        self._progress = progress

    @property
    def status(self):
        """Gets the status of this GetVulnCheckStatusResponse.  # noqa: E501


        :return: The status of this GetVulnCheckStatusResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetVulnCheckStatusResponse.


        :param status: The status of this GetVulnCheckStatusResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetVulnCheckStatusResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetVulnCheckStatusResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetVulnCheckStatusResponse):
            return True

        return self.to_dict() != other.to_dict()
