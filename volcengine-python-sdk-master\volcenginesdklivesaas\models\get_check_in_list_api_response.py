# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetCheckInListAPIResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'check_in_list': 'list[CheckInListForGetCheckInListAPIOutput]',
        'page_count': 'int',
        'page_no': 'int'
    }

    attribute_map = {
        'check_in_list': 'CheckInList',
        'page_count': 'PageCount',
        'page_no': 'PageNo'
    }

    def __init__(self, check_in_list=None, page_count=None, page_no=None, _configuration=None):  # noqa: E501
        """GetCheckInListAPIResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._check_in_list = None
        self._page_count = None
        self._page_no = None
        self.discriminator = None

        if check_in_list is not None:
            self.check_in_list = check_in_list
        if page_count is not None:
            self.page_count = page_count
        if page_no is not None:
            self.page_no = page_no

    @property
    def check_in_list(self):
        """Gets the check_in_list of this GetCheckInListAPIResponse.  # noqa: E501


        :return: The check_in_list of this GetCheckInListAPIResponse.  # noqa: E501
        :rtype: list[CheckInListForGetCheckInListAPIOutput]
        """
        return self._check_in_list

    @check_in_list.setter
    def check_in_list(self, check_in_list):
        """Sets the check_in_list of this GetCheckInListAPIResponse.


        :param check_in_list: The check_in_list of this GetCheckInListAPIResponse.  # noqa: E501
        :type: list[CheckInListForGetCheckInListAPIOutput]
        """

        self._check_in_list = check_in_list

    @property
    def page_count(self):
        """Gets the page_count of this GetCheckInListAPIResponse.  # noqa: E501


        :return: The page_count of this GetCheckInListAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_count

    @page_count.setter
    def page_count(self, page_count):
        """Sets the page_count of this GetCheckInListAPIResponse.


        :param page_count: The page_count of this GetCheckInListAPIResponse.  # noqa: E501
        :type: int
        """

        self._page_count = page_count

    @property
    def page_no(self):
        """Gets the page_no of this GetCheckInListAPIResponse.  # noqa: E501


        :return: The page_no of this GetCheckInListAPIResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_no

    @page_no.setter
    def page_no(self, page_no):
        """Sets the page_no of this GetCheckInListAPIResponse.


        :param page_no: The page_no of this GetCheckInListAPIResponse.  # noqa: E501
        :type: int
        """

        self._page_no = page_no

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetCheckInListAPIResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetCheckInListAPIResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetCheckInListAPIResponse):
            return True

        return self.to_dict() != other.to_dict()
