# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HpcClusterForDescribeHpcClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_at': 'str',
        'description': 'str',
        'hpc_cluster_id': 'str',
        'name': 'str',
        'updated_at': 'str',
        'vpc_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'created_at': 'CreatedAt',
        'description': 'Description',
        'hpc_cluster_id': 'HpcClusterId',
        'name': 'Name',
        'updated_at': 'UpdatedAt',
        'vpc_id': 'VpcId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, created_at=None, description=None, hpc_cluster_id=None, name=None, updated_at=None, vpc_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """HpcClusterForDescribeHpcClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_at = None
        self._description = None
        self._hpc_cluster_id = None
        self._name = None
        self._updated_at = None
        self._vpc_id = None
        self._zone_id = None
        self.discriminator = None

        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if hpc_cluster_id is not None:
            self.hpc_cluster_id = hpc_cluster_id
        if name is not None:
            self.name = name
        if updated_at is not None:
            self.updated_at = updated_at
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def created_at(self):
        """Gets the created_at of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501


        :return: The created_at of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this HpcClusterForDescribeHpcClustersOutput.


        :param created_at: The created_at of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501


        :return: The description of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this HpcClusterForDescribeHpcClustersOutput.


        :param description: The description of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def hpc_cluster_id(self):
        """Gets the hpc_cluster_id of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501


        :return: The hpc_cluster_id of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._hpc_cluster_id

    @hpc_cluster_id.setter
    def hpc_cluster_id(self, hpc_cluster_id):
        """Sets the hpc_cluster_id of this HpcClusterForDescribeHpcClustersOutput.


        :param hpc_cluster_id: The hpc_cluster_id of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :type: str
        """

        self._hpc_cluster_id = hpc_cluster_id

    @property
    def name(self):
        """Gets the name of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501


        :return: The name of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this HpcClusterForDescribeHpcClustersOutput.


        :param name: The name of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def updated_at(self):
        """Gets the updated_at of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501


        :return: The updated_at of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this HpcClusterForDescribeHpcClustersOutput.


        :param updated_at: The updated_at of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    @property
    def vpc_id(self):
        """Gets the vpc_id of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501


        :return: The vpc_id of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this HpcClusterForDescribeHpcClustersOutput.


        :param vpc_id: The vpc_id of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_id(self):
        """Gets the zone_id of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501


        :return: The zone_id of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this HpcClusterForDescribeHpcClustersOutput.


        :param zone_id: The zone_id of this HpcClusterForDescribeHpcClustersOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HpcClusterForDescribeHpcClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HpcClusterForDescribeHpcClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HpcClusterForDescribeHpcClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
