# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribePublicBandwidthPackageResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'public_bandwidth_package': 'PublicBandwidthPackageForDescribePublicBandwidthPackageOutput'
    }

    attribute_map = {
        'public_bandwidth_package': 'PublicBandwidthPackage'
    }

    def __init__(self, public_bandwidth_package=None, _configuration=None):  # noqa: E501
        """DescribePublicBandwidthPackageResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._public_bandwidth_package = None
        self.discriminator = None

        if public_bandwidth_package is not None:
            self.public_bandwidth_package = public_bandwidth_package

    @property
    def public_bandwidth_package(self):
        """Gets the public_bandwidth_package of this DescribePublicBandwidthPackageResponse.  # noqa: E501


        :return: The public_bandwidth_package of this DescribePublicBandwidthPackageResponse.  # noqa: E501
        :rtype: PublicBandwidthPackageForDescribePublicBandwidthPackageOutput
        """
        return self._public_bandwidth_package

    @public_bandwidth_package.setter
    def public_bandwidth_package(self, public_bandwidth_package):
        """Sets the public_bandwidth_package of this DescribePublicBandwidthPackageResponse.


        :param public_bandwidth_package: The public_bandwidth_package of this DescribePublicBandwidthPackageResponse.  # noqa: E501
        :type: PublicBandwidthPackageForDescribePublicBandwidthPackageOutput
        """

        self._public_bandwidth_package = public_bandwidth_package

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribePublicBandwidthPackageResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribePublicBandwidthPackageResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribePublicBandwidthPackageResponse):
            return True

        return self.to_dict() != other.to_dict()
