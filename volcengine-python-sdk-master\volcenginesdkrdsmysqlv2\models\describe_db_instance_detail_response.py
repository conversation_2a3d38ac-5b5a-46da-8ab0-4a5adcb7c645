# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstanceDetailResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'basic_info': 'BasicInfoForDescribeDBInstanceDetailOutput',
        'charge_detail': 'ChargeDetailForDescribeDBInstanceDetailOutput',
        'disaster_recovery_instances': 'list[DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput]',
        'endpoints': 'list[EndpointForDescribeDBInstanceDetailOutput]',
        'nodes': 'list[NodeForDescribeDBInstanceDetailOutput]',
        'proxy_detail': 'ProxyDetailForDescribeDBInstanceDetailOutput'
    }

    attribute_map = {
        'basic_info': 'BasicInfo',
        'charge_detail': 'ChargeDetail',
        'disaster_recovery_instances': 'DisasterRecoveryInstances',
        'endpoints': 'Endpoints',
        'nodes': 'Nodes',
        'proxy_detail': 'ProxyDetail'
    }

    def __init__(self, basic_info=None, charge_detail=None, disaster_recovery_instances=None, endpoints=None, nodes=None, proxy_detail=None, _configuration=None):  # noqa: E501
        """DescribeDBInstanceDetailResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._basic_info = None
        self._charge_detail = None
        self._disaster_recovery_instances = None
        self._endpoints = None
        self._nodes = None
        self._proxy_detail = None
        self.discriminator = None

        if basic_info is not None:
            self.basic_info = basic_info
        if charge_detail is not None:
            self.charge_detail = charge_detail
        if disaster_recovery_instances is not None:
            self.disaster_recovery_instances = disaster_recovery_instances
        if endpoints is not None:
            self.endpoints = endpoints
        if nodes is not None:
            self.nodes = nodes
        if proxy_detail is not None:
            self.proxy_detail = proxy_detail

    @property
    def basic_info(self):
        """Gets the basic_info of this DescribeDBInstanceDetailResponse.  # noqa: E501


        :return: The basic_info of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :rtype: BasicInfoForDescribeDBInstanceDetailOutput
        """
        return self._basic_info

    @basic_info.setter
    def basic_info(self, basic_info):
        """Sets the basic_info of this DescribeDBInstanceDetailResponse.


        :param basic_info: The basic_info of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :type: BasicInfoForDescribeDBInstanceDetailOutput
        """

        self._basic_info = basic_info

    @property
    def charge_detail(self):
        """Gets the charge_detail of this DescribeDBInstanceDetailResponse.  # noqa: E501


        :return: The charge_detail of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :rtype: ChargeDetailForDescribeDBInstanceDetailOutput
        """
        return self._charge_detail

    @charge_detail.setter
    def charge_detail(self, charge_detail):
        """Sets the charge_detail of this DescribeDBInstanceDetailResponse.


        :param charge_detail: The charge_detail of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :type: ChargeDetailForDescribeDBInstanceDetailOutput
        """

        self._charge_detail = charge_detail

    @property
    def disaster_recovery_instances(self):
        """Gets the disaster_recovery_instances of this DescribeDBInstanceDetailResponse.  # noqa: E501


        :return: The disaster_recovery_instances of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :rtype: list[DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput]
        """
        return self._disaster_recovery_instances

    @disaster_recovery_instances.setter
    def disaster_recovery_instances(self, disaster_recovery_instances):
        """Sets the disaster_recovery_instances of this DescribeDBInstanceDetailResponse.


        :param disaster_recovery_instances: The disaster_recovery_instances of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :type: list[DisasterRecoveryInstanceForDescribeDBInstanceDetailOutput]
        """

        self._disaster_recovery_instances = disaster_recovery_instances

    @property
    def endpoints(self):
        """Gets the endpoints of this DescribeDBInstanceDetailResponse.  # noqa: E501


        :return: The endpoints of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :rtype: list[EndpointForDescribeDBInstanceDetailOutput]
        """
        return self._endpoints

    @endpoints.setter
    def endpoints(self, endpoints):
        """Sets the endpoints of this DescribeDBInstanceDetailResponse.


        :param endpoints: The endpoints of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :type: list[EndpointForDescribeDBInstanceDetailOutput]
        """

        self._endpoints = endpoints

    @property
    def nodes(self):
        """Gets the nodes of this DescribeDBInstanceDetailResponse.  # noqa: E501


        :return: The nodes of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :rtype: list[NodeForDescribeDBInstanceDetailOutput]
        """
        return self._nodes

    @nodes.setter
    def nodes(self, nodes):
        """Sets the nodes of this DescribeDBInstanceDetailResponse.


        :param nodes: The nodes of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :type: list[NodeForDescribeDBInstanceDetailOutput]
        """

        self._nodes = nodes

    @property
    def proxy_detail(self):
        """Gets the proxy_detail of this DescribeDBInstanceDetailResponse.  # noqa: E501


        :return: The proxy_detail of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :rtype: ProxyDetailForDescribeDBInstanceDetailOutput
        """
        return self._proxy_detail

    @proxy_detail.setter
    def proxy_detail(self, proxy_detail):
        """Sets the proxy_detail of this DescribeDBInstanceDetailResponse.


        :param proxy_detail: The proxy_detail of this DescribeDBInstanceDetailResponse.  # noqa: E501
        :type: ProxyDetailForDescribeDBInstanceDetailOutput
        """

        self._proxy_detail = proxy_detail

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstanceDetailResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstanceDetailResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstanceDetailResponse):
            return True

        return self.to_dict() != other.to_dict()
