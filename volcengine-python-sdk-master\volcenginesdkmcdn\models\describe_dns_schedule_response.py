# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDnsScheduleResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'dns_schedule_info': 'DnsScheduleInfoForDescribeDnsScheduleOutput',
        'weight_infos': 'list[WeightInfoForDescribeDnsScheduleOutput]'
    }

    attribute_map = {
        'dns_schedule_info': 'DnsScheduleInfo',
        'weight_infos': 'WeightInfos'
    }

    def __init__(self, dns_schedule_info=None, weight_infos=None, _configuration=None):  # noqa: E501
        """DescribeDnsScheduleResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._dns_schedule_info = None
        self._weight_infos = None
        self.discriminator = None

        if dns_schedule_info is not None:
            self.dns_schedule_info = dns_schedule_info
        if weight_infos is not None:
            self.weight_infos = weight_infos

    @property
    def dns_schedule_info(self):
        """Gets the dns_schedule_info of this DescribeDnsScheduleResponse.  # noqa: E501


        :return: The dns_schedule_info of this DescribeDnsScheduleResponse.  # noqa: E501
        :rtype: DnsScheduleInfoForDescribeDnsScheduleOutput
        """
        return self._dns_schedule_info

    @dns_schedule_info.setter
    def dns_schedule_info(self, dns_schedule_info):
        """Sets the dns_schedule_info of this DescribeDnsScheduleResponse.


        :param dns_schedule_info: The dns_schedule_info of this DescribeDnsScheduleResponse.  # noqa: E501
        :type: DnsScheduleInfoForDescribeDnsScheduleOutput
        """

        self._dns_schedule_info = dns_schedule_info

    @property
    def weight_infos(self):
        """Gets the weight_infos of this DescribeDnsScheduleResponse.  # noqa: E501


        :return: The weight_infos of this DescribeDnsScheduleResponse.  # noqa: E501
        :rtype: list[WeightInfoForDescribeDnsScheduleOutput]
        """
        return self._weight_infos

    @weight_infos.setter
    def weight_infos(self, weight_infos):
        """Sets the weight_infos of this DescribeDnsScheduleResponse.


        :param weight_infos: The weight_infos of this DescribeDnsScheduleResponse.  # noqa: E501
        :type: list[WeightInfoForDescribeDnsScheduleOutput]
        """

        self._weight_infos = weight_infos

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDnsScheduleResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDnsScheduleResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDnsScheduleResponse):
            return True

        return self.to_dict() != other.to_dict()
