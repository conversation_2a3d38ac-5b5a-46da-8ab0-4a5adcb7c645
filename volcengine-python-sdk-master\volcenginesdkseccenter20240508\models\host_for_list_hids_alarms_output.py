# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HostForListHidsAlarmsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_tags': 'list[str]',
        'hostname': 'str',
        'inner_ip_list': 'list[str]',
        'outer_ip_list': 'list[str]'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_tags': 'AgentTags',
        'hostname': 'Hostname',
        'inner_ip_list': 'InnerIPList',
        'outer_ip_list': 'OuterIPList'
    }

    def __init__(self, agent_id=None, agent_tags=None, hostname=None, inner_ip_list=None, outer_ip_list=None, _configuration=None):  # noqa: E501
        """HostForListHidsAlarmsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_tags = None
        self._hostname = None
        self._inner_ip_list = None
        self._outer_ip_list = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if hostname is not None:
            self.hostname = hostname
        if inner_ip_list is not None:
            self.inner_ip_list = inner_ip_list
        if outer_ip_list is not None:
            self.outer_ip_list = outer_ip_list

    @property
    def agent_id(self):
        """Gets the agent_id of this HostForListHidsAlarmsOutput.  # noqa: E501


        :return: The agent_id of this HostForListHidsAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this HostForListHidsAlarmsOutput.


        :param agent_id: The agent_id of this HostForListHidsAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this HostForListHidsAlarmsOutput.  # noqa: E501


        :return: The agent_tags of this HostForListHidsAlarmsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this HostForListHidsAlarmsOutput.


        :param agent_tags: The agent_tags of this HostForListHidsAlarmsOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def hostname(self):
        """Gets the hostname of this HostForListHidsAlarmsOutput.  # noqa: E501


        :return: The hostname of this HostForListHidsAlarmsOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this HostForListHidsAlarmsOutput.


        :param hostname: The hostname of this HostForListHidsAlarmsOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def inner_ip_list(self):
        """Gets the inner_ip_list of this HostForListHidsAlarmsOutput.  # noqa: E501


        :return: The inner_ip_list of this HostForListHidsAlarmsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._inner_ip_list

    @inner_ip_list.setter
    def inner_ip_list(self, inner_ip_list):
        """Sets the inner_ip_list of this HostForListHidsAlarmsOutput.


        :param inner_ip_list: The inner_ip_list of this HostForListHidsAlarmsOutput.  # noqa: E501
        :type: list[str]
        """

        self._inner_ip_list = inner_ip_list

    @property
    def outer_ip_list(self):
        """Gets the outer_ip_list of this HostForListHidsAlarmsOutput.  # noqa: E501


        :return: The outer_ip_list of this HostForListHidsAlarmsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._outer_ip_list

    @outer_ip_list.setter
    def outer_ip_list(self, outer_ip_list):
        """Sets the outer_ip_list of this HostForListHidsAlarmsOutput.


        :param outer_ip_list: The outer_ip_list of this HostForListHidsAlarmsOutput.  # noqa: E501
        :type: list[str]
        """

        self._outer_ip_list = outer_ip_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HostForListHidsAlarmsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HostForListHidsAlarmsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HostForListHidsAlarmsOutput):
            return True

        return self.to_dict() != other.to_dict()
