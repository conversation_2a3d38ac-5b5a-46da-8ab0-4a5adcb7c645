# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TemporaryResourceForDescribeMigrationJobsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'associated_resource': 'list[AssociatedResourceForDescribeMigrationJobsOutput]',
        'project_name': 'str',
        'resource_id': 'str',
        'resource_name': 'str',
        'resource_status': 'str',
        'resource_type': 'str'
    }

    attribute_map = {
        'associated_resource': 'AssociatedResource',
        'project_name': 'ProjectName',
        'resource_id': 'ResourceId',
        'resource_name': 'ResourceName',
        'resource_status': 'ResourceStatus',
        'resource_type': 'ResourceType'
    }

    def __init__(self, associated_resource=None, project_name=None, resource_id=None, resource_name=None, resource_status=None, resource_type=None, _configuration=None):  # noqa: E501
        """TemporaryResourceForDescribeMigrationJobsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._associated_resource = None
        self._project_name = None
        self._resource_id = None
        self._resource_name = None
        self._resource_status = None
        self._resource_type = None
        self.discriminator = None

        if associated_resource is not None:
            self.associated_resource = associated_resource
        if project_name is not None:
            self.project_name = project_name
        if resource_id is not None:
            self.resource_id = resource_id
        if resource_name is not None:
            self.resource_name = resource_name
        if resource_status is not None:
            self.resource_status = resource_status
        if resource_type is not None:
            self.resource_type = resource_type

    @property
    def associated_resource(self):
        """Gets the associated_resource of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The associated_resource of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: list[AssociatedResourceForDescribeMigrationJobsOutput]
        """
        return self._associated_resource

    @associated_resource.setter
    def associated_resource(self, associated_resource):
        """Sets the associated_resource of this TemporaryResourceForDescribeMigrationJobsOutput.


        :param associated_resource: The associated_resource of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :type: list[AssociatedResourceForDescribeMigrationJobsOutput]
        """

        self._associated_resource = associated_resource

    @property
    def project_name(self):
        """Gets the project_name of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The project_name of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this TemporaryResourceForDescribeMigrationJobsOutput.


        :param project_name: The project_name of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def resource_id(self):
        """Gets the resource_id of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The resource_id of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_id

    @resource_id.setter
    def resource_id(self, resource_id):
        """Sets the resource_id of this TemporaryResourceForDescribeMigrationJobsOutput.


        :param resource_id: The resource_id of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._resource_id = resource_id

    @property
    def resource_name(self):
        """Gets the resource_name of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The resource_name of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_name

    @resource_name.setter
    def resource_name(self, resource_name):
        """Sets the resource_name of this TemporaryResourceForDescribeMigrationJobsOutput.


        :param resource_name: The resource_name of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._resource_name = resource_name

    @property
    def resource_status(self):
        """Gets the resource_status of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The resource_status of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_status

    @resource_status.setter
    def resource_status(self, resource_status):
        """Sets the resource_status of this TemporaryResourceForDescribeMigrationJobsOutput.


        :param resource_status: The resource_status of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._resource_status = resource_status

    @property
    def resource_type(self):
        """Gets the resource_type of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The resource_type of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this TemporaryResourceForDescribeMigrationJobsOutput.


        :param resource_type: The resource_type of this TemporaryResourceForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TemporaryResourceForDescribeMigrationJobsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TemporaryResourceForDescribeMigrationJobsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TemporaryResourceForDescribeMigrationJobsOutput):
            return True

        return self.to_dict() != other.to_dict()
