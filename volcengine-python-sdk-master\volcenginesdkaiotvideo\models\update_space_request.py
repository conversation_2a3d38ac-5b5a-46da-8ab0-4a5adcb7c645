# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateSpaceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'callback_url': 'str',
        'description': 'str',
        'gb': 'GBForUpdateSpaceInput',
        'hls_low_latency': 'bool',
        'region': 'str',
        'space_id': 'str',
        'space_name': 'str'
    }

    attribute_map = {
        'callback_url': 'CallbackURL',
        'description': 'Description',
        'gb': 'GB',
        'hls_low_latency': 'HLSLowLatency',
        'region': 'Region',
        'space_id': 'SpaceID',
        'space_name': 'SpaceName'
    }

    def __init__(self, callback_url=None, description=None, gb=None, hls_low_latency=None, region=None, space_id=None, space_name=None, _configuration=None):  # noqa: E501
        """UpdateSpaceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._callback_url = None
        self._description = None
        self._gb = None
        self._hls_low_latency = None
        self._region = None
        self._space_id = None
        self._space_name = None
        self.discriminator = None

        if callback_url is not None:
            self.callback_url = callback_url
        if description is not None:
            self.description = description
        if gb is not None:
            self.gb = gb
        if hls_low_latency is not None:
            self.hls_low_latency = hls_low_latency
        if region is not None:
            self.region = region
        self.space_id = space_id
        if space_name is not None:
            self.space_name = space_name

    @property
    def callback_url(self):
        """Gets the callback_url of this UpdateSpaceRequest.  # noqa: E501


        :return: The callback_url of this UpdateSpaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._callback_url

    @callback_url.setter
    def callback_url(self, callback_url):
        """Sets the callback_url of this UpdateSpaceRequest.


        :param callback_url: The callback_url of this UpdateSpaceRequest.  # noqa: E501
        :type: str
        """

        self._callback_url = callback_url

    @property
    def description(self):
        """Gets the description of this UpdateSpaceRequest.  # noqa: E501


        :return: The description of this UpdateSpaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateSpaceRequest.


        :param description: The description of this UpdateSpaceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def gb(self):
        """Gets the gb of this UpdateSpaceRequest.  # noqa: E501


        :return: The gb of this UpdateSpaceRequest.  # noqa: E501
        :rtype: GBForUpdateSpaceInput
        """
        return self._gb

    @gb.setter
    def gb(self, gb):
        """Sets the gb of this UpdateSpaceRequest.


        :param gb: The gb of this UpdateSpaceRequest.  # noqa: E501
        :type: GBForUpdateSpaceInput
        """

        self._gb = gb

    @property
    def hls_low_latency(self):
        """Gets the hls_low_latency of this UpdateSpaceRequest.  # noqa: E501


        :return: The hls_low_latency of this UpdateSpaceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._hls_low_latency

    @hls_low_latency.setter
    def hls_low_latency(self, hls_low_latency):
        """Sets the hls_low_latency of this UpdateSpaceRequest.


        :param hls_low_latency: The hls_low_latency of this UpdateSpaceRequest.  # noqa: E501
        :type: bool
        """

        self._hls_low_latency = hls_low_latency

    @property
    def region(self):
        """Gets the region of this UpdateSpaceRequest.  # noqa: E501


        :return: The region of this UpdateSpaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this UpdateSpaceRequest.


        :param region: The region of this UpdateSpaceRequest.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def space_id(self):
        """Gets the space_id of this UpdateSpaceRequest.  # noqa: E501


        :return: The space_id of this UpdateSpaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_id

    @space_id.setter
    def space_id(self, space_id):
        """Sets the space_id of this UpdateSpaceRequest.


        :param space_id: The space_id of this UpdateSpaceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and space_id is None:
            raise ValueError("Invalid value for `space_id`, must not be `None`")  # noqa: E501

        self._space_id = space_id

    @property
    def space_name(self):
        """Gets the space_name of this UpdateSpaceRequest.  # noqa: E501


        :return: The space_name of this UpdateSpaceRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_name

    @space_name.setter
    def space_name(self, space_name):
        """Sets the space_name of this UpdateSpaceRequest.


        :param space_name: The space_name of this UpdateSpaceRequest.  # noqa: E501
        :type: str
        """

        self._space_name = space_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateSpaceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateSpaceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateSpaceRequest):
            return True

        return self.to_dict() != other.to_dict()
