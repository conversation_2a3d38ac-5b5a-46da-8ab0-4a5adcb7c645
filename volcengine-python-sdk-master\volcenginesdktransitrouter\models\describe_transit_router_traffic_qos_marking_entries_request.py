# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTransitRouterTrafficQosMarkingEntriesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'destination_cidr_block': 'str',
        'match_dscp': 'int',
        'page_number': 'int',
        'page_size': 'int',
        'protocol': 'str',
        'remarking_dscp': 'int',
        'source_cidr_block': 'str',
        'transit_router_traffic_qos_marking_entry_ids': 'list[str]',
        'transit_router_traffic_qos_marking_entry_name': 'str',
        'transit_router_traffic_qos_marking_policy_id': 'str'
    }

    attribute_map = {
        'destination_cidr_block': 'DestinationCidrBlock',
        'match_dscp': 'MatchDscp',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'protocol': 'Protocol',
        'remarking_dscp': 'RemarkingDscp',
        'source_cidr_block': 'SourceCidrBlock',
        'transit_router_traffic_qos_marking_entry_ids': 'TransitRouterTrafficQosMarkingEntryIds',
        'transit_router_traffic_qos_marking_entry_name': 'TransitRouterTrafficQosMarkingEntryName',
        'transit_router_traffic_qos_marking_policy_id': 'TransitRouterTrafficQosMarkingPolicyId'
    }

    def __init__(self, destination_cidr_block=None, match_dscp=None, page_number=None, page_size=None, protocol=None, remarking_dscp=None, source_cidr_block=None, transit_router_traffic_qos_marking_entry_ids=None, transit_router_traffic_qos_marking_entry_name=None, transit_router_traffic_qos_marking_policy_id=None, _configuration=None):  # noqa: E501
        """DescribeTransitRouterTrafficQosMarkingEntriesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._destination_cidr_block = None
        self._match_dscp = None
        self._page_number = None
        self._page_size = None
        self._protocol = None
        self._remarking_dscp = None
        self._source_cidr_block = None
        self._transit_router_traffic_qos_marking_entry_ids = None
        self._transit_router_traffic_qos_marking_entry_name = None
        self._transit_router_traffic_qos_marking_policy_id = None
        self.discriminator = None

        if destination_cidr_block is not None:
            self.destination_cidr_block = destination_cidr_block
        if match_dscp is not None:
            self.match_dscp = match_dscp
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if protocol is not None:
            self.protocol = protocol
        if remarking_dscp is not None:
            self.remarking_dscp = remarking_dscp
        if source_cidr_block is not None:
            self.source_cidr_block = source_cidr_block
        if transit_router_traffic_qos_marking_entry_ids is not None:
            self.transit_router_traffic_qos_marking_entry_ids = transit_router_traffic_qos_marking_entry_ids
        if transit_router_traffic_qos_marking_entry_name is not None:
            self.transit_router_traffic_qos_marking_entry_name = transit_router_traffic_qos_marking_entry_name
        self.transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id

    @property
    def destination_cidr_block(self):
        """Gets the destination_cidr_block of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The destination_cidr_block of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._destination_cidr_block

    @destination_cidr_block.setter
    def destination_cidr_block(self, destination_cidr_block):
        """Sets the destination_cidr_block of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param destination_cidr_block: The destination_cidr_block of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: str
        """

        self._destination_cidr_block = destination_cidr_block

    @property
    def match_dscp(self):
        """Gets the match_dscp of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The match_dscp of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: int
        """
        return self._match_dscp

    @match_dscp.setter
    def match_dscp(self, match_dscp):
        """Sets the match_dscp of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param match_dscp: The match_dscp of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: int
        """

        self._match_dscp = match_dscp

    @property
    def page_number(self):
        """Gets the page_number of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The page_number of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param page_number: The page_number of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The page_size of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param page_size: The page_size of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def protocol(self):
        """Gets the protocol of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The protocol of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._protocol

    @protocol.setter
    def protocol(self, protocol):
        """Sets the protocol of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param protocol: The protocol of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: str
        """

        self._protocol = protocol

    @property
    def remarking_dscp(self):
        """Gets the remarking_dscp of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The remarking_dscp of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: int
        """
        return self._remarking_dscp

    @remarking_dscp.setter
    def remarking_dscp(self, remarking_dscp):
        """Sets the remarking_dscp of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param remarking_dscp: The remarking_dscp of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: int
        """

        self._remarking_dscp = remarking_dscp

    @property
    def source_cidr_block(self):
        """Gets the source_cidr_block of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The source_cidr_block of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._source_cidr_block

    @source_cidr_block.setter
    def source_cidr_block(self, source_cidr_block):
        """Sets the source_cidr_block of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param source_cidr_block: The source_cidr_block of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: str
        """

        self._source_cidr_block = source_cidr_block

    @property
    def transit_router_traffic_qos_marking_entry_ids(self):
        """Gets the transit_router_traffic_qos_marking_entry_ids of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_entry_ids of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._transit_router_traffic_qos_marking_entry_ids

    @transit_router_traffic_qos_marking_entry_ids.setter
    def transit_router_traffic_qos_marking_entry_ids(self, transit_router_traffic_qos_marking_entry_ids):
        """Sets the transit_router_traffic_qos_marking_entry_ids of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param transit_router_traffic_qos_marking_entry_ids: The transit_router_traffic_qos_marking_entry_ids of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: list[str]
        """

        self._transit_router_traffic_qos_marking_entry_ids = transit_router_traffic_qos_marking_entry_ids

    @property
    def transit_router_traffic_qos_marking_entry_name(self):
        """Gets the transit_router_traffic_qos_marking_entry_name of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_entry_name of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_entry_name

    @transit_router_traffic_qos_marking_entry_name.setter
    def transit_router_traffic_qos_marking_entry_name(self, transit_router_traffic_qos_marking_entry_name):
        """Sets the transit_router_traffic_qos_marking_entry_name of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param transit_router_traffic_qos_marking_entry_name: The transit_router_traffic_qos_marking_entry_name of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: str
        """

        self._transit_router_traffic_qos_marking_entry_name = transit_router_traffic_qos_marking_entry_name

    @property
    def transit_router_traffic_qos_marking_policy_id(self):
        """Gets the transit_router_traffic_qos_marking_policy_id of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501


        :return: The transit_router_traffic_qos_marking_policy_id of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_traffic_qos_marking_policy_id

    @transit_router_traffic_qos_marking_policy_id.setter
    def transit_router_traffic_qos_marking_policy_id(self, transit_router_traffic_qos_marking_policy_id):
        """Sets the transit_router_traffic_qos_marking_policy_id of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.


        :param transit_router_traffic_qos_marking_policy_id: The transit_router_traffic_qos_marking_policy_id of this DescribeTransitRouterTrafficQosMarkingEntriesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and transit_router_traffic_qos_marking_policy_id is None:
            raise ValueError("Invalid value for `transit_router_traffic_qos_marking_policy_id`, must not be `None`")  # noqa: E501

        self._transit_router_traffic_qos_marking_policy_id = transit_router_traffic_qos_marking_policy_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTransitRouterTrafficQosMarkingEntriesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTransitRouterTrafficQosMarkingEntriesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTransitRouterTrafficQosMarkingEntriesRequest):
            return True

        return self.to_dict() != other.to_dict()
