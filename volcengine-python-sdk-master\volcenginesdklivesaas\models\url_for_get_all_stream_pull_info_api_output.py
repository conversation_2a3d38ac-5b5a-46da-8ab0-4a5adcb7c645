# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UrlForGetAllStreamPullInfoAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'flv_url': 'str',
        'hls_url': 'str',
        'lls_url': 'str',
        'rtmp_url': 'str'
    }

    attribute_map = {
        'flv_url': 'FlvUrl',
        'hls_url': 'HlsUrl',
        'lls_url': 'LLSUrl',
        'rtmp_url': 'RtmpUrl'
    }

    def __init__(self, flv_url=None, hls_url=None, lls_url=None, rtmp_url=None, _configuration=None):  # noqa: E501
        """UrlForGetAllStreamPullInfoAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._flv_url = None
        self._hls_url = None
        self._lls_url = None
        self._rtmp_url = None
        self.discriminator = None

        if flv_url is not None:
            self.flv_url = flv_url
        if hls_url is not None:
            self.hls_url = hls_url
        if lls_url is not None:
            self.lls_url = lls_url
        if rtmp_url is not None:
            self.rtmp_url = rtmp_url

    @property
    def flv_url(self):
        """Gets the flv_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501


        :return: The flv_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._flv_url

    @flv_url.setter
    def flv_url(self, flv_url):
        """Sets the flv_url of this UrlForGetAllStreamPullInfoAPIOutput.


        :param flv_url: The flv_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :type: str
        """

        self._flv_url = flv_url

    @property
    def hls_url(self):
        """Gets the hls_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501


        :return: The hls_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._hls_url

    @hls_url.setter
    def hls_url(self, hls_url):
        """Sets the hls_url of this UrlForGetAllStreamPullInfoAPIOutput.


        :param hls_url: The hls_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :type: str
        """

        self._hls_url = hls_url

    @property
    def lls_url(self):
        """Gets the lls_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501


        :return: The lls_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._lls_url

    @lls_url.setter
    def lls_url(self, lls_url):
        """Sets the lls_url of this UrlForGetAllStreamPullInfoAPIOutput.


        :param lls_url: The lls_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :type: str
        """

        self._lls_url = lls_url

    @property
    def rtmp_url(self):
        """Gets the rtmp_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501


        :return: The rtmp_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._rtmp_url

    @rtmp_url.setter
    def rtmp_url(self, rtmp_url):
        """Sets the rtmp_url of this UrlForGetAllStreamPullInfoAPIOutput.


        :param rtmp_url: The rtmp_url of this UrlForGetAllStreamPullInfoAPIOutput.  # noqa: E501
        :type: str
        """

        self._rtmp_url = rtmp_url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UrlForGetAllStreamPullInfoAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UrlForGetAllStreamPullInfoAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UrlForGetAllStreamPullInfoAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
