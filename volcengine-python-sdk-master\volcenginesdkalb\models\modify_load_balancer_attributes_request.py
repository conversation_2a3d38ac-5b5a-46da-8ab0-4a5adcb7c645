# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyLoadBalancerAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'delete_protection': 'str',
        'description': 'str',
        'global_accelerator': 'GlobalAcceleratorForModifyLoadBalancerAttributesInput',
        'load_balancer_id': 'str',
        'load_balancer_name': 'str',
        'modification_protection_reason': 'str',
        'modification_protection_status': 'str',
        'waf_instance_id': 'str',
        'waf_protected_domain': 'str',
        'waf_protection_enabled': 'str'
    }

    attribute_map = {
        'delete_protection': 'DeleteProtection',
        'description': 'Description',
        'global_accelerator': 'GlobalAccelerator',
        'load_balancer_id': 'LoadBalancerId',
        'load_balancer_name': 'LoadBalancerName',
        'modification_protection_reason': 'ModificationProtectionReason',
        'modification_protection_status': 'ModificationProtectionStatus',
        'waf_instance_id': 'WafInstanceId',
        'waf_protected_domain': 'WafProtectedDomain',
        'waf_protection_enabled': 'WafProtectionEnabled'
    }

    def __init__(self, delete_protection=None, description=None, global_accelerator=None, load_balancer_id=None, load_balancer_name=None, modification_protection_reason=None, modification_protection_status=None, waf_instance_id=None, waf_protected_domain=None, waf_protection_enabled=None, _configuration=None):  # noqa: E501
        """ModifyLoadBalancerAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._delete_protection = None
        self._description = None
        self._global_accelerator = None
        self._load_balancer_id = None
        self._load_balancer_name = None
        self._modification_protection_reason = None
        self._modification_protection_status = None
        self._waf_instance_id = None
        self._waf_protected_domain = None
        self._waf_protection_enabled = None
        self.discriminator = None

        if delete_protection is not None:
            self.delete_protection = delete_protection
        if description is not None:
            self.description = description
        if global_accelerator is not None:
            self.global_accelerator = global_accelerator
        self.load_balancer_id = load_balancer_id
        if load_balancer_name is not None:
            self.load_balancer_name = load_balancer_name
        if modification_protection_reason is not None:
            self.modification_protection_reason = modification_protection_reason
        if modification_protection_status is not None:
            self.modification_protection_status = modification_protection_status
        if waf_instance_id is not None:
            self.waf_instance_id = waf_instance_id
        if waf_protected_domain is not None:
            self.waf_protected_domain = waf_protected_domain
        if waf_protection_enabled is not None:
            self.waf_protection_enabled = waf_protection_enabled

    @property
    def delete_protection(self):
        """Gets the delete_protection of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The delete_protection of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._delete_protection

    @delete_protection.setter
    def delete_protection(self, delete_protection):
        """Sets the delete_protection of this ModifyLoadBalancerAttributesRequest.


        :param delete_protection: The delete_protection of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._delete_protection = delete_protection

    @property
    def description(self):
        """Gets the description of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The description of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ModifyLoadBalancerAttributesRequest.


        :param description: The description of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def global_accelerator(self):
        """Gets the global_accelerator of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The global_accelerator of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: GlobalAcceleratorForModifyLoadBalancerAttributesInput
        """
        return self._global_accelerator

    @global_accelerator.setter
    def global_accelerator(self, global_accelerator):
        """Sets the global_accelerator of this ModifyLoadBalancerAttributesRequest.


        :param global_accelerator: The global_accelerator of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: GlobalAcceleratorForModifyLoadBalancerAttributesInput
        """

        self._global_accelerator = global_accelerator

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The load_balancer_id of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this ModifyLoadBalancerAttributesRequest.


        :param load_balancer_id: The load_balancer_id of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and load_balancer_id is None:
            raise ValueError("Invalid value for `load_balancer_id`, must not be `None`")  # noqa: E501

        self._load_balancer_id = load_balancer_id

    @property
    def load_balancer_name(self):
        """Gets the load_balancer_name of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The load_balancer_name of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_name

    @load_balancer_name.setter
    def load_balancer_name(self, load_balancer_name):
        """Sets the load_balancer_name of this ModifyLoadBalancerAttributesRequest.


        :param load_balancer_name: The load_balancer_name of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._load_balancer_name = load_balancer_name

    @property
    def modification_protection_reason(self):
        """Gets the modification_protection_reason of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The modification_protection_reason of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_reason

    @modification_protection_reason.setter
    def modification_protection_reason(self, modification_protection_reason):
        """Sets the modification_protection_reason of this ModifyLoadBalancerAttributesRequest.


        :param modification_protection_reason: The modification_protection_reason of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._modification_protection_reason = modification_protection_reason

    @property
    def modification_protection_status(self):
        """Gets the modification_protection_status of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The modification_protection_status of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_status

    @modification_protection_status.setter
    def modification_protection_status(self, modification_protection_status):
        """Sets the modification_protection_status of this ModifyLoadBalancerAttributesRequest.


        :param modification_protection_status: The modification_protection_status of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._modification_protection_status = modification_protection_status

    @property
    def waf_instance_id(self):
        """Gets the waf_instance_id of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The waf_instance_id of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._waf_instance_id

    @waf_instance_id.setter
    def waf_instance_id(self, waf_instance_id):
        """Sets the waf_instance_id of this ModifyLoadBalancerAttributesRequest.


        :param waf_instance_id: The waf_instance_id of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._waf_instance_id = waf_instance_id

    @property
    def waf_protected_domain(self):
        """Gets the waf_protected_domain of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The waf_protected_domain of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._waf_protected_domain

    @waf_protected_domain.setter
    def waf_protected_domain(self, waf_protected_domain):
        """Sets the waf_protected_domain of this ModifyLoadBalancerAttributesRequest.


        :param waf_protected_domain: The waf_protected_domain of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._waf_protected_domain = waf_protected_domain

    @property
    def waf_protection_enabled(self):
        """Gets the waf_protection_enabled of this ModifyLoadBalancerAttributesRequest.  # noqa: E501


        :return: The waf_protection_enabled of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._waf_protection_enabled

    @waf_protection_enabled.setter
    def waf_protection_enabled(self, waf_protection_enabled):
        """Sets the waf_protection_enabled of this ModifyLoadBalancerAttributesRequest.


        :param waf_protection_enabled: The waf_protection_enabled of this ModifyLoadBalancerAttributesRequest.  # noqa: E501
        :type: str
        """

        self._waf_protection_enabled = waf_protection_enabled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyLoadBalancerAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyLoadBalancerAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyLoadBalancerAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
