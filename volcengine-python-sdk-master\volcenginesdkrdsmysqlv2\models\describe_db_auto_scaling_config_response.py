# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBAutoScalingConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'storage_config': 'StorageConfigForDescribeDBAutoScalingConfigOutput',
        'storage_max_capacity': 'int',
        'storage_max_trigger_threshold': 'int',
        'storage_min_capacity': 'int',
        'storage_min_trigger_threshold': 'int'
    }

    attribute_map = {
        'storage_config': 'StorageConfig',
        'storage_max_capacity': 'StorageMaxCapacity',
        'storage_max_trigger_threshold': 'StorageMaxTriggerThreshold',
        'storage_min_capacity': 'StorageMinCapacity',
        'storage_min_trigger_threshold': 'StorageMinTriggerThreshold'
    }

    def __init__(self, storage_config=None, storage_max_capacity=None, storage_max_trigger_threshold=None, storage_min_capacity=None, storage_min_trigger_threshold=None, _configuration=None):  # noqa: E501
        """DescribeDBAutoScalingConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._storage_config = None
        self._storage_max_capacity = None
        self._storage_max_trigger_threshold = None
        self._storage_min_capacity = None
        self._storage_min_trigger_threshold = None
        self.discriminator = None

        if storage_config is not None:
            self.storage_config = storage_config
        if storage_max_capacity is not None:
            self.storage_max_capacity = storage_max_capacity
        if storage_max_trigger_threshold is not None:
            self.storage_max_trigger_threshold = storage_max_trigger_threshold
        if storage_min_capacity is not None:
            self.storage_min_capacity = storage_min_capacity
        if storage_min_trigger_threshold is not None:
            self.storage_min_trigger_threshold = storage_min_trigger_threshold

    @property
    def storage_config(self):
        """Gets the storage_config of this DescribeDBAutoScalingConfigResponse.  # noqa: E501


        :return: The storage_config of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :rtype: StorageConfigForDescribeDBAutoScalingConfigOutput
        """
        return self._storage_config

    @storage_config.setter
    def storage_config(self, storage_config):
        """Sets the storage_config of this DescribeDBAutoScalingConfigResponse.


        :param storage_config: The storage_config of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :type: StorageConfigForDescribeDBAutoScalingConfigOutput
        """

        self._storage_config = storage_config

    @property
    def storage_max_capacity(self):
        """Gets the storage_max_capacity of this DescribeDBAutoScalingConfigResponse.  # noqa: E501


        :return: The storage_max_capacity of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._storage_max_capacity

    @storage_max_capacity.setter
    def storage_max_capacity(self, storage_max_capacity):
        """Sets the storage_max_capacity of this DescribeDBAutoScalingConfigResponse.


        :param storage_max_capacity: The storage_max_capacity of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :type: int
        """

        self._storage_max_capacity = storage_max_capacity

    @property
    def storage_max_trigger_threshold(self):
        """Gets the storage_max_trigger_threshold of this DescribeDBAutoScalingConfigResponse.  # noqa: E501


        :return: The storage_max_trigger_threshold of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._storage_max_trigger_threshold

    @storage_max_trigger_threshold.setter
    def storage_max_trigger_threshold(self, storage_max_trigger_threshold):
        """Sets the storage_max_trigger_threshold of this DescribeDBAutoScalingConfigResponse.


        :param storage_max_trigger_threshold: The storage_max_trigger_threshold of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :type: int
        """

        self._storage_max_trigger_threshold = storage_max_trigger_threshold

    @property
    def storage_min_capacity(self):
        """Gets the storage_min_capacity of this DescribeDBAutoScalingConfigResponse.  # noqa: E501


        :return: The storage_min_capacity of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._storage_min_capacity

    @storage_min_capacity.setter
    def storage_min_capacity(self, storage_min_capacity):
        """Sets the storage_min_capacity of this DescribeDBAutoScalingConfigResponse.


        :param storage_min_capacity: The storage_min_capacity of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :type: int
        """

        self._storage_min_capacity = storage_min_capacity

    @property
    def storage_min_trigger_threshold(self):
        """Gets the storage_min_trigger_threshold of this DescribeDBAutoScalingConfigResponse.  # noqa: E501


        :return: The storage_min_trigger_threshold of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._storage_min_trigger_threshold

    @storage_min_trigger_threshold.setter
    def storage_min_trigger_threshold(self, storage_min_trigger_threshold):
        """Sets the storage_min_trigger_threshold of this DescribeDBAutoScalingConfigResponse.


        :param storage_min_trigger_threshold: The storage_min_trigger_threshold of this DescribeDBAutoScalingConfigResponse.  # noqa: E501
        :type: int
        """

        self._storage_min_trigger_threshold = storage_min_trigger_threshold

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBAutoScalingConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBAutoScalingConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBAutoScalingConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
