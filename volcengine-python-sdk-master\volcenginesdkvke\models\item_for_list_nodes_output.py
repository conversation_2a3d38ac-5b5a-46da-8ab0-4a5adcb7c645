# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListNodesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'additional_container_storage_enabled': 'bool',
        'cluster_id': 'str',
        'container_storage_path': 'str',
        'create_client_token': 'str',
        'create_time': 'str',
        'id': 'str',
        'image_id': 'str',
        'initialize_script': 'str',
        'instance_id': 'str',
        'is_virtual': 'bool',
        'kubernetes_config': 'KubernetesConfigForListNodesOutput',
        'name': 'str',
        'node_pool_id': 'str',
        'roles': 'list[str]',
        'status': 'StatusForListNodesOutput',
        'update_time': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'additional_container_storage_enabled': 'AdditionalContainerStorageEnabled',
        'cluster_id': 'ClusterId',
        'container_storage_path': 'ContainerStoragePath',
        'create_client_token': 'CreateClientToken',
        'create_time': 'CreateTime',
        'id': 'Id',
        'image_id': 'ImageId',
        'initialize_script': 'InitializeScript',
        'instance_id': 'InstanceId',
        'is_virtual': 'IsVirtual',
        'kubernetes_config': 'KubernetesConfig',
        'name': 'Name',
        'node_pool_id': 'NodePoolId',
        'roles': 'Roles',
        'status': 'Status',
        'update_time': 'UpdateTime',
        'zone_id': 'ZoneId'
    }

    def __init__(self, additional_container_storage_enabled=None, cluster_id=None, container_storage_path=None, create_client_token=None, create_time=None, id=None, image_id=None, initialize_script=None, instance_id=None, is_virtual=None, kubernetes_config=None, name=None, node_pool_id=None, roles=None, status=None, update_time=None, zone_id=None, _configuration=None):  # noqa: E501
        """ItemForListNodesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._additional_container_storage_enabled = None
        self._cluster_id = None
        self._container_storage_path = None
        self._create_client_token = None
        self._create_time = None
        self._id = None
        self._image_id = None
        self._initialize_script = None
        self._instance_id = None
        self._is_virtual = None
        self._kubernetes_config = None
        self._name = None
        self._node_pool_id = None
        self._roles = None
        self._status = None
        self._update_time = None
        self._zone_id = None
        self.discriminator = None

        if additional_container_storage_enabled is not None:
            self.additional_container_storage_enabled = additional_container_storage_enabled
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if container_storage_path is not None:
            self.container_storage_path = container_storage_path
        if create_client_token is not None:
            self.create_client_token = create_client_token
        if create_time is not None:
            self.create_time = create_time
        if id is not None:
            self.id = id
        if image_id is not None:
            self.image_id = image_id
        if initialize_script is not None:
            self.initialize_script = initialize_script
        if instance_id is not None:
            self.instance_id = instance_id
        if is_virtual is not None:
            self.is_virtual = is_virtual
        if kubernetes_config is not None:
            self.kubernetes_config = kubernetes_config
        if name is not None:
            self.name = name
        if node_pool_id is not None:
            self.node_pool_id = node_pool_id
        if roles is not None:
            self.roles = roles
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def additional_container_storage_enabled(self):
        """Gets the additional_container_storage_enabled of this ItemForListNodesOutput.  # noqa: E501


        :return: The additional_container_storage_enabled of this ItemForListNodesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._additional_container_storage_enabled

    @additional_container_storage_enabled.setter
    def additional_container_storage_enabled(self, additional_container_storage_enabled):
        """Sets the additional_container_storage_enabled of this ItemForListNodesOutput.


        :param additional_container_storage_enabled: The additional_container_storage_enabled of this ItemForListNodesOutput.  # noqa: E501
        :type: bool
        """

        self._additional_container_storage_enabled = additional_container_storage_enabled

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ItemForListNodesOutput.  # noqa: E501


        :return: The cluster_id of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ItemForListNodesOutput.


        :param cluster_id: The cluster_id of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def container_storage_path(self):
        """Gets the container_storage_path of this ItemForListNodesOutput.  # noqa: E501


        :return: The container_storage_path of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._container_storage_path

    @container_storage_path.setter
    def container_storage_path(self, container_storage_path):
        """Sets the container_storage_path of this ItemForListNodesOutput.


        :param container_storage_path: The container_storage_path of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._container_storage_path = container_storage_path

    @property
    def create_client_token(self):
        """Gets the create_client_token of this ItemForListNodesOutput.  # noqa: E501


        :return: The create_client_token of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_client_token

    @create_client_token.setter
    def create_client_token(self, create_client_token):
        """Sets the create_client_token of this ItemForListNodesOutput.


        :param create_client_token: The create_client_token of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._create_client_token = create_client_token

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListNodesOutput.  # noqa: E501


        :return: The create_time of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListNodesOutput.


        :param create_time: The create_time of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def id(self):
        """Gets the id of this ItemForListNodesOutput.  # noqa: E501


        :return: The id of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListNodesOutput.


        :param id: The id of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def image_id(self):
        """Gets the image_id of this ItemForListNodesOutput.  # noqa: E501


        :return: The image_id of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this ItemForListNodesOutput.


        :param image_id: The image_id of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def initialize_script(self):
        """Gets the initialize_script of this ItemForListNodesOutput.  # noqa: E501


        :return: The initialize_script of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._initialize_script

    @initialize_script.setter
    def initialize_script(self, initialize_script):
        """Sets the initialize_script of this ItemForListNodesOutput.


        :param initialize_script: The initialize_script of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._initialize_script = initialize_script

    @property
    def instance_id(self):
        """Gets the instance_id of this ItemForListNodesOutput.  # noqa: E501


        :return: The instance_id of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ItemForListNodesOutput.


        :param instance_id: The instance_id of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def is_virtual(self):
        """Gets the is_virtual of this ItemForListNodesOutput.  # noqa: E501


        :return: The is_virtual of this ItemForListNodesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_virtual

    @is_virtual.setter
    def is_virtual(self, is_virtual):
        """Sets the is_virtual of this ItemForListNodesOutput.


        :param is_virtual: The is_virtual of this ItemForListNodesOutput.  # noqa: E501
        :type: bool
        """

        self._is_virtual = is_virtual

    @property
    def kubernetes_config(self):
        """Gets the kubernetes_config of this ItemForListNodesOutput.  # noqa: E501


        :return: The kubernetes_config of this ItemForListNodesOutput.  # noqa: E501
        :rtype: KubernetesConfigForListNodesOutput
        """
        return self._kubernetes_config

    @kubernetes_config.setter
    def kubernetes_config(self, kubernetes_config):
        """Sets the kubernetes_config of this ItemForListNodesOutput.


        :param kubernetes_config: The kubernetes_config of this ItemForListNodesOutput.  # noqa: E501
        :type: KubernetesConfigForListNodesOutput
        """

        self._kubernetes_config = kubernetes_config

    @property
    def name(self):
        """Gets the name of this ItemForListNodesOutput.  # noqa: E501


        :return: The name of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListNodesOutput.


        :param name: The name of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def node_pool_id(self):
        """Gets the node_pool_id of this ItemForListNodesOutput.  # noqa: E501


        :return: The node_pool_id of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_pool_id

    @node_pool_id.setter
    def node_pool_id(self, node_pool_id):
        """Sets the node_pool_id of this ItemForListNodesOutput.


        :param node_pool_id: The node_pool_id of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._node_pool_id = node_pool_id

    @property
    def roles(self):
        """Gets the roles of this ItemForListNodesOutput.  # noqa: E501


        :return: The roles of this ItemForListNodesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._roles

    @roles.setter
    def roles(self, roles):
        """Sets the roles of this ItemForListNodesOutput.


        :param roles: The roles of this ItemForListNodesOutput.  # noqa: E501
        :type: list[str]
        """

        self._roles = roles

    @property
    def status(self):
        """Gets the status of this ItemForListNodesOutput.  # noqa: E501


        :return: The status of this ItemForListNodesOutput.  # noqa: E501
        :rtype: StatusForListNodesOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListNodesOutput.


        :param status: The status of this ItemForListNodesOutput.  # noqa: E501
        :type: StatusForListNodesOutput
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListNodesOutput.  # noqa: E501


        :return: The update_time of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListNodesOutput.


        :param update_time: The update_time of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def zone_id(self):
        """Gets the zone_id of this ItemForListNodesOutput.  # noqa: E501


        :return: The zone_id of this ItemForListNodesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ItemForListNodesOutput.


        :param zone_id: The zone_id of this ItemForListNodesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListNodesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListNodesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListNodesOutput):
            return True

        return self.to_dict() != other.to_dict()
