# coding: utf-8

"""
    rabbitmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PluginForModifyPluginInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enabled': 'bool',
        'plugin_name': 'str'
    }

    attribute_map = {
        'enabled': 'Enabled',
        'plugin_name': 'PluginName'
    }

    def __init__(self, enabled=None, plugin_name=None, _configuration=None):  # noqa: E501
        """PluginForModifyPluginInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enabled = None
        self._plugin_name = None
        self.discriminator = None

        if enabled is not None:
            self.enabled = enabled
        if plugin_name is not None:
            self.plugin_name = plugin_name

    @property
    def enabled(self):
        """Gets the enabled of this PluginForModifyPluginInput.  # noqa: E501


        :return: The enabled of this PluginForModifyPluginInput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this PluginForModifyPluginInput.


        :param enabled: The enabled of this PluginForModifyPluginInput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def plugin_name(self):
        """Gets the plugin_name of this PluginForModifyPluginInput.  # noqa: E501


        :return: The plugin_name of this PluginForModifyPluginInput.  # noqa: E501
        :rtype: str
        """
        return self._plugin_name

    @plugin_name.setter
    def plugin_name(self, plugin_name):
        """Sets the plugin_name of this PluginForModifyPluginInput.


        :param plugin_name: The plugin_name of this PluginForModifyPluginInput.  # noqa: E501
        :type: str
        """

        self._plugin_name = plugin_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PluginForModifyPluginInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PluginForModifyPluginInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PluginForModifyPluginInput):
            return True

        return self.to_dict() != other.to_dict()
