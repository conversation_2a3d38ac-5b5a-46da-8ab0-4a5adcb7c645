# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetDownloadLiveClientRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'login_type': 'int',
        'user_name': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'login_type': 'LoginType',
        'user_name': 'UserName'
    }

    def __init__(self, activity_id=None, login_type=None, user_name=None, _configuration=None):  # noqa: E501
        """GetDownloadLiveClientRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._login_type = None
        self._user_name = None
        self.discriminator = None

        self.activity_id = activity_id
        if login_type is not None:
            self.login_type = login_type
        if user_name is not None:
            self.user_name = user_name

    @property
    def activity_id(self):
        """Gets the activity_id of this GetDownloadLiveClientRequest.  # noqa: E501


        :return: The activity_id of this GetDownloadLiveClientRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetDownloadLiveClientRequest.


        :param activity_id: The activity_id of this GetDownloadLiveClientRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def login_type(self):
        """Gets the login_type of this GetDownloadLiveClientRequest.  # noqa: E501


        :return: The login_type of this GetDownloadLiveClientRequest.  # noqa: E501
        :rtype: int
        """
        return self._login_type

    @login_type.setter
    def login_type(self, login_type):
        """Sets the login_type of this GetDownloadLiveClientRequest.


        :param login_type: The login_type of this GetDownloadLiveClientRequest.  # noqa: E501
        :type: int
        """

        self._login_type = login_type

    @property
    def user_name(self):
        """Gets the user_name of this GetDownloadLiveClientRequest.  # noqa: E501


        :return: The user_name of this GetDownloadLiveClientRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this GetDownloadLiveClientRequest.


        :param user_name: The user_name of this GetDownloadLiveClientRequest.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetDownloadLiveClientRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetDownloadLiveClientRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetDownloadLiveClientRequest):
            return True

        return self.to_dict() != other.to_dict()
