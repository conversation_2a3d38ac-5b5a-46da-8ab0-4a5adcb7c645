# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NacosServiceForGetUpstreamOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'group': 'str',
        'namespace': 'str',
        'namespace_id': 'str',
        'service': 'str',
        'upstream_source_id': 'str'
    }

    attribute_map = {
        'group': 'Group',
        'namespace': 'Namespace',
        'namespace_id': 'NamespaceId',
        'service': 'Service',
        'upstream_source_id': 'UpstreamSourceId'
    }

    def __init__(self, group=None, namespace=None, namespace_id=None, service=None, upstream_source_id=None, _configuration=None):  # noqa: E501
        """NacosServiceForGetUpstreamOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._group = None
        self._namespace = None
        self._namespace_id = None
        self._service = None
        self._upstream_source_id = None
        self.discriminator = None

        if group is not None:
            self.group = group
        if namespace is not None:
            self.namespace = namespace
        if namespace_id is not None:
            self.namespace_id = namespace_id
        if service is not None:
            self.service = service
        if upstream_source_id is not None:
            self.upstream_source_id = upstream_source_id

    @property
    def group(self):
        """Gets the group of this NacosServiceForGetUpstreamOutput.  # noqa: E501


        :return: The group of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._group

    @group.setter
    def group(self, group):
        """Sets the group of this NacosServiceForGetUpstreamOutput.


        :param group: The group of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._group = group

    @property
    def namespace(self):
        """Gets the namespace of this NacosServiceForGetUpstreamOutput.  # noqa: E501


        :return: The namespace of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this NacosServiceForGetUpstreamOutput.


        :param namespace: The namespace of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def namespace_id(self):
        """Gets the namespace_id of this NacosServiceForGetUpstreamOutput.  # noqa: E501


        :return: The namespace_id of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace_id

    @namespace_id.setter
    def namespace_id(self, namespace_id):
        """Sets the namespace_id of this NacosServiceForGetUpstreamOutput.


        :param namespace_id: The namespace_id of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._namespace_id = namespace_id

    @property
    def service(self):
        """Gets the service of this NacosServiceForGetUpstreamOutput.  # noqa: E501


        :return: The service of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._service

    @service.setter
    def service(self, service):
        """Sets the service of this NacosServiceForGetUpstreamOutput.


        :param service: The service of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._service = service

    @property
    def upstream_source_id(self):
        """Gets the upstream_source_id of this NacosServiceForGetUpstreamOutput.  # noqa: E501


        :return: The upstream_source_id of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._upstream_source_id

    @upstream_source_id.setter
    def upstream_source_id(self, upstream_source_id):
        """Sets the upstream_source_id of this NacosServiceForGetUpstreamOutput.


        :param upstream_source_id: The upstream_source_id of this NacosServiceForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._upstream_source_id = upstream_source_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NacosServiceForGetUpstreamOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NacosServiceForGetUpstreamOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NacosServiceForGetUpstreamOutput):
            return True

        return self.to_dict() != other.to_dict()
