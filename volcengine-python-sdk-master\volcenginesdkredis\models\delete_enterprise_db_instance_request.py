# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteEnterpriseDBInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'backup_point_name': 'str',
        'client_token': 'str',
        'create_backup': 'bool',
        'instance_id': 'str'
    }

    attribute_map = {
        'backup_point_name': 'BackupPointName',
        'client_token': 'ClientToken',
        'create_backup': 'CreateBackup',
        'instance_id': 'InstanceId'
    }

    def __init__(self, backup_point_name=None, client_token=None, create_backup=None, instance_id=None, _configuration=None):  # noqa: E501
        """DeleteEnterpriseDBInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._backup_point_name = None
        self._client_token = None
        self._create_backup = None
        self._instance_id = None
        self.discriminator = None

        if backup_point_name is not None:
            self.backup_point_name = backup_point_name
        if client_token is not None:
            self.client_token = client_token
        if create_backup is not None:
            self.create_backup = create_backup
        self.instance_id = instance_id

    @property
    def backup_point_name(self):
        """Gets the backup_point_name of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501


        :return: The backup_point_name of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_point_name

    @backup_point_name.setter
    def backup_point_name(self, backup_point_name):
        """Sets the backup_point_name of this DeleteEnterpriseDBInstanceRequest.


        :param backup_point_name: The backup_point_name of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._backup_point_name = backup_point_name

    @property
    def client_token(self):
        """Gets the client_token of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501


        :return: The client_token of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this DeleteEnterpriseDBInstanceRequest.


        :param client_token: The client_token of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def create_backup(self):
        """Gets the create_backup of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501


        :return: The create_backup of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501
        :rtype: bool
        """
        return self._create_backup

    @create_backup.setter
    def create_backup(self, create_backup):
        """Sets the create_backup of this DeleteEnterpriseDBInstanceRequest.


        :param create_backup: The create_backup of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501
        :type: bool
        """

        self._create_backup = create_backup

    @property
    def instance_id(self):
        """Gets the instance_id of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501


        :return: The instance_id of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DeleteEnterpriseDBInstanceRequest.


        :param instance_id: The instance_id of this DeleteEnterpriseDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteEnterpriseDBInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteEnterpriseDBInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteEnterpriseDBInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
