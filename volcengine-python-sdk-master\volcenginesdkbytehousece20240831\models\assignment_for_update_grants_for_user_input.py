# coding: utf-8

"""
    bytehouse_ce20240831

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AssignmentForUpdateGrantsForUserInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'admin_option': 'bool',
        'cluster_id': 'int',
        'grant_to': 'str',
        'immutable': 'bool',
        'immutable_reason': 'str',
        'on_cluster': 'str',
        'raw_sql': 'str',
        'role_id': 'int',
        'role_is_default': 'bool',
        'role_name': 'str'
    }

    attribute_map = {
        'admin_option': 'AdminOption',
        'cluster_id': 'ClusterID',
        'grant_to': 'GrantTo',
        'immutable': 'Immutable',
        'immutable_reason': 'ImmutableReason',
        'on_cluster': 'OnCluster',
        'raw_sql': 'RawSql',
        'role_id': 'RoleID',
        'role_is_default': 'RoleIsDefault',
        'role_name': 'RoleName'
    }

    def __init__(self, admin_option=None, cluster_id=None, grant_to=None, immutable=None, immutable_reason=None, on_cluster=None, raw_sql=None, role_id=None, role_is_default=None, role_name=None, _configuration=None):  # noqa: E501
        """AssignmentForUpdateGrantsForUserInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._admin_option = None
        self._cluster_id = None
        self._grant_to = None
        self._immutable = None
        self._immutable_reason = None
        self._on_cluster = None
        self._raw_sql = None
        self._role_id = None
        self._role_is_default = None
        self._role_name = None
        self.discriminator = None

        if admin_option is not None:
            self.admin_option = admin_option
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if grant_to is not None:
            self.grant_to = grant_to
        if immutable is not None:
            self.immutable = immutable
        if immutable_reason is not None:
            self.immutable_reason = immutable_reason
        if on_cluster is not None:
            self.on_cluster = on_cluster
        if raw_sql is not None:
            self.raw_sql = raw_sql
        if role_id is not None:
            self.role_id = role_id
        if role_is_default is not None:
            self.role_is_default = role_is_default
        if role_name is not None:
            self.role_name = role_name

    @property
    def admin_option(self):
        """Gets the admin_option of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The admin_option of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: bool
        """
        return self._admin_option

    @admin_option.setter
    def admin_option(self, admin_option):
        """Sets the admin_option of this AssignmentForUpdateGrantsForUserInput.


        :param admin_option: The admin_option of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: bool
        """

        self._admin_option = admin_option

    @property
    def cluster_id(self):
        """Gets the cluster_id of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The cluster_id of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: int
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this AssignmentForUpdateGrantsForUserInput.


        :param cluster_id: The cluster_id of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: int
        """

        self._cluster_id = cluster_id

    @property
    def grant_to(self):
        """Gets the grant_to of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The grant_to of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: str
        """
        return self._grant_to

    @grant_to.setter
    def grant_to(self, grant_to):
        """Sets the grant_to of this AssignmentForUpdateGrantsForUserInput.


        :param grant_to: The grant_to of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: str
        """

        self._grant_to = grant_to

    @property
    def immutable(self):
        """Gets the immutable of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The immutable of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: bool
        """
        return self._immutable

    @immutable.setter
    def immutable(self, immutable):
        """Sets the immutable of this AssignmentForUpdateGrantsForUserInput.


        :param immutable: The immutable of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: bool
        """

        self._immutable = immutable

    @property
    def immutable_reason(self):
        """Gets the immutable_reason of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The immutable_reason of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: str
        """
        return self._immutable_reason

    @immutable_reason.setter
    def immutable_reason(self, immutable_reason):
        """Sets the immutable_reason of this AssignmentForUpdateGrantsForUserInput.


        :param immutable_reason: The immutable_reason of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: str
        """

        self._immutable_reason = immutable_reason

    @property
    def on_cluster(self):
        """Gets the on_cluster of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The on_cluster of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: str
        """
        return self._on_cluster

    @on_cluster.setter
    def on_cluster(self, on_cluster):
        """Sets the on_cluster of this AssignmentForUpdateGrantsForUserInput.


        :param on_cluster: The on_cluster of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: str
        """

        self._on_cluster = on_cluster

    @property
    def raw_sql(self):
        """Gets the raw_sql of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The raw_sql of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: str
        """
        return self._raw_sql

    @raw_sql.setter
    def raw_sql(self, raw_sql):
        """Sets the raw_sql of this AssignmentForUpdateGrantsForUserInput.


        :param raw_sql: The raw_sql of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: str
        """

        self._raw_sql = raw_sql

    @property
    def role_id(self):
        """Gets the role_id of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The role_id of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: int
        """
        return self._role_id

    @role_id.setter
    def role_id(self, role_id):
        """Sets the role_id of this AssignmentForUpdateGrantsForUserInput.


        :param role_id: The role_id of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: int
        """

        self._role_id = role_id

    @property
    def role_is_default(self):
        """Gets the role_is_default of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The role_is_default of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: bool
        """
        return self._role_is_default

    @role_is_default.setter
    def role_is_default(self, role_is_default):
        """Sets the role_is_default of this AssignmentForUpdateGrantsForUserInput.


        :param role_is_default: The role_is_default of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: bool
        """

        self._role_is_default = role_is_default

    @property
    def role_name(self):
        """Gets the role_name of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501


        :return: The role_name of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :rtype: str
        """
        return self._role_name

    @role_name.setter
    def role_name(self, role_name):
        """Sets the role_name of this AssignmentForUpdateGrantsForUserInput.


        :param role_name: The role_name of this AssignmentForUpdateGrantsForUserInput.  # noqa: E501
        :type: str
        """

        self._role_name = role_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AssignmentForUpdateGrantsForUserInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AssignmentForUpdateGrantsForUserInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AssignmentForUpdateGrantsForUserInput):
            return True

        return self.to_dict() != other.to_dict()
