# coding: utf-8

"""
    natgateway

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateNatGatewayRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'billing_type': 'int',
        'client_token': 'str',
        'description': 'str',
        'nat_gateway_name': 'str',
        'period': 'int',
        'period_unit': 'str',
        'project_name': 'str',
        'spec': 'str',
        'subnet_id': 'str',
        'tags': 'list[TagForCreateNatGatewayInput]',
        'vpc_id': 'str'
    }

    attribute_map = {
        'billing_type': 'BillingType',
        'client_token': 'ClientToken',
        'description': 'Description',
        'nat_gateway_name': 'NatGatewayName',
        'period': 'Period',
        'period_unit': 'PeriodUnit',
        'project_name': 'ProjectName',
        'spec': 'Spec',
        'subnet_id': 'SubnetId',
        'tags': 'Tags',
        'vpc_id': 'VpcId'
    }

    def __init__(self, billing_type=None, client_token=None, description=None, nat_gateway_name=None, period=None, period_unit=None, project_name=None, spec=None, subnet_id=None, tags=None, vpc_id=None, _configuration=None):  # noqa: E501
        """CreateNatGatewayRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._billing_type = None
        self._client_token = None
        self._description = None
        self._nat_gateway_name = None
        self._period = None
        self._period_unit = None
        self._project_name = None
        self._spec = None
        self._subnet_id = None
        self._tags = None
        self._vpc_id = None
        self.discriminator = None

        if billing_type is not None:
            self.billing_type = billing_type
        if client_token is not None:
            self.client_token = client_token
        if description is not None:
            self.description = description
        if nat_gateway_name is not None:
            self.nat_gateway_name = nat_gateway_name
        if period is not None:
            self.period = period
        if period_unit is not None:
            self.period_unit = period_unit
        if project_name is not None:
            self.project_name = project_name
        if spec is not None:
            self.spec = spec
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if tags is not None:
            self.tags = tags
        self.vpc_id = vpc_id

    @property
    def billing_type(self):
        """Gets the billing_type of this CreateNatGatewayRequest.  # noqa: E501


        :return: The billing_type of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this CreateNatGatewayRequest.


        :param billing_type: The billing_type of this CreateNatGatewayRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                billing_type is not None and billing_type > 3):  # noqa: E501
            raise ValueError("Invalid value for `billing_type`, must be a value less than or equal to `3`")  # noqa: E501
        if (self._configuration.client_side_validation and
                billing_type is not None and billing_type < 1):  # noqa: E501
            raise ValueError("Invalid value for `billing_type`, must be a value greater than or equal to `1`")  # noqa: E501

        self._billing_type = billing_type

    @property
    def client_token(self):
        """Gets the client_token of this CreateNatGatewayRequest.  # noqa: E501


        :return: The client_token of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateNatGatewayRequest.


        :param client_token: The client_token of this CreateNatGatewayRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def description(self):
        """Gets the description of this CreateNatGatewayRequest.  # noqa: E501


        :return: The description of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateNatGatewayRequest.


        :param description: The description of this CreateNatGatewayRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 255):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `255`")  # noqa: E501
        if (self._configuration.client_side_validation and
                description is not None and len(description) < 1):
            raise ValueError("Invalid value for `description`, length must be greater than or equal to `1`")  # noqa: E501

        self._description = description

    @property
    def nat_gateway_name(self):
        """Gets the nat_gateway_name of this CreateNatGatewayRequest.  # noqa: E501


        :return: The nat_gateway_name of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._nat_gateway_name

    @nat_gateway_name.setter
    def nat_gateway_name(self, nat_gateway_name):
        """Sets the nat_gateway_name of this CreateNatGatewayRequest.


        :param nat_gateway_name: The nat_gateway_name of this CreateNatGatewayRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                nat_gateway_name is not None and len(nat_gateway_name) > 128):
            raise ValueError("Invalid value for `nat_gateway_name`, length must be less than or equal to `128`")  # noqa: E501
        if (self._configuration.client_side_validation and
                nat_gateway_name is not None and len(nat_gateway_name) < 1):
            raise ValueError("Invalid value for `nat_gateway_name`, length must be greater than or equal to `1`")  # noqa: E501

        self._nat_gateway_name = nat_gateway_name

    @property
    def period(self):
        """Gets the period of this CreateNatGatewayRequest.  # noqa: E501


        :return: The period of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: int
        """
        return self._period

    @period.setter
    def period(self, period):
        """Sets the period of this CreateNatGatewayRequest.


        :param period: The period of this CreateNatGatewayRequest.  # noqa: E501
        :type: int
        """

        self._period = period

    @property
    def period_unit(self):
        """Gets the period_unit of this CreateNatGatewayRequest.  # noqa: E501


        :return: The period_unit of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this CreateNatGatewayRequest.


        :param period_unit: The period_unit of this CreateNatGatewayRequest.  # noqa: E501
        :type: str
        """

        self._period_unit = period_unit

    @property
    def project_name(self):
        """Gets the project_name of this CreateNatGatewayRequest.  # noqa: E501


        :return: The project_name of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateNatGatewayRequest.


        :param project_name: The project_name of this CreateNatGatewayRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def spec(self):
        """Gets the spec of this CreateNatGatewayRequest.  # noqa: E501


        :return: The spec of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._spec

    @spec.setter
    def spec(self, spec):
        """Sets the spec of this CreateNatGatewayRequest.


        :param spec: The spec of this CreateNatGatewayRequest.  # noqa: E501
        :type: str
        """

        self._spec = spec

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateNatGatewayRequest.  # noqa: E501


        :return: The subnet_id of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateNatGatewayRequest.


        :param subnet_id: The subnet_id of this CreateNatGatewayRequest.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def tags(self):
        """Gets the tags of this CreateNatGatewayRequest.  # noqa: E501


        :return: The tags of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: list[TagForCreateNatGatewayInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateNatGatewayRequest.


        :param tags: The tags of this CreateNatGatewayRequest.  # noqa: E501
        :type: list[TagForCreateNatGatewayInput]
        """

        self._tags = tags

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateNatGatewayRequest.  # noqa: E501


        :return: The vpc_id of this CreateNatGatewayRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateNatGatewayRequest.


        :param vpc_id: The vpc_id of this CreateNatGatewayRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateNatGatewayRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateNatGatewayRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateNatGatewayRequest):
            return True

        return self.to_dict() != other.to_dict()
