# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RobotCommentConfigForUpdateActivityRobotCommentConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'is_robot_enable': 'int',
        'robot_comment_repository_id': 'int',
        'robot_comment_repository_name': 'str',
        'robot_send_num': 'int',
        'robot_send_unit': 'int'
    }

    attribute_map = {
        'is_robot_enable': 'IsRobotEnable',
        'robot_comment_repository_id': 'RobotCommentRepositoryId',
        'robot_comment_repository_name': 'RobotCommentRepositoryName',
        'robot_send_num': 'RobotSendNum',
        'robot_send_unit': 'RobotSendUnit'
    }

    def __init__(self, is_robot_enable=None, robot_comment_repository_id=None, robot_comment_repository_name=None, robot_send_num=None, robot_send_unit=None, _configuration=None):  # noqa: E501
        """RobotCommentConfigForUpdateActivityRobotCommentConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._is_robot_enable = None
        self._robot_comment_repository_id = None
        self._robot_comment_repository_name = None
        self._robot_send_num = None
        self._robot_send_unit = None
        self.discriminator = None

        if is_robot_enable is not None:
            self.is_robot_enable = is_robot_enable
        if robot_comment_repository_id is not None:
            self.robot_comment_repository_id = robot_comment_repository_id
        if robot_comment_repository_name is not None:
            self.robot_comment_repository_name = robot_comment_repository_name
        if robot_send_num is not None:
            self.robot_send_num = robot_send_num
        if robot_send_unit is not None:
            self.robot_send_unit = robot_send_unit

    @property
    def is_robot_enable(self):
        """Gets the is_robot_enable of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501


        :return: The is_robot_enable of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._is_robot_enable

    @is_robot_enable.setter
    def is_robot_enable(self, is_robot_enable):
        """Sets the is_robot_enable of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.


        :param is_robot_enable: The is_robot_enable of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._is_robot_enable = is_robot_enable

    @property
    def robot_comment_repository_id(self):
        """Gets the robot_comment_repository_id of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501


        :return: The robot_comment_repository_id of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._robot_comment_repository_id

    @robot_comment_repository_id.setter
    def robot_comment_repository_id(self, robot_comment_repository_id):
        """Sets the robot_comment_repository_id of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.


        :param robot_comment_repository_id: The robot_comment_repository_id of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._robot_comment_repository_id = robot_comment_repository_id

    @property
    def robot_comment_repository_name(self):
        """Gets the robot_comment_repository_name of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501


        :return: The robot_comment_repository_name of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._robot_comment_repository_name

    @robot_comment_repository_name.setter
    def robot_comment_repository_name(self, robot_comment_repository_name):
        """Sets the robot_comment_repository_name of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.


        :param robot_comment_repository_name: The robot_comment_repository_name of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :type: str
        """

        self._robot_comment_repository_name = robot_comment_repository_name

    @property
    def robot_send_num(self):
        """Gets the robot_send_num of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501


        :return: The robot_send_num of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._robot_send_num

    @robot_send_num.setter
    def robot_send_num(self, robot_send_num):
        """Sets the robot_send_num of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.


        :param robot_send_num: The robot_send_num of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._robot_send_num = robot_send_num

    @property
    def robot_send_unit(self):
        """Gets the robot_send_unit of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501


        :return: The robot_send_unit of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._robot_send_unit

    @robot_send_unit.setter
    def robot_send_unit(self, robot_send_unit):
        """Sets the robot_send_unit of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.


        :param robot_send_unit: The robot_send_unit of this RobotCommentConfigForUpdateActivityRobotCommentConfigInput.  # noqa: E501
        :type: int
        """

        self._robot_send_unit = robot_send_unit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RobotCommentConfigForUpdateActivityRobotCommentConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RobotCommentConfigForUpdateActivityRobotCommentConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RobotCommentConfigForUpdateActivityRobotCommentConfigInput):
            return True

        return self.to_dict() != other.to_dict()
