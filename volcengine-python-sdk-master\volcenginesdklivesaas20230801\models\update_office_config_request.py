# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateOfficeConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allowed_size': 'list[str]',
        'enable_extranet_url': 'bool',
        'lb_strategy': 'str',
        'office_id': 'str',
        'office_ip': 'OfficeIpForUpdateOfficeConfigInput',
        'office_name': 'str',
        'office_status': 'str',
        'recommended_size': 'str'
    }

    attribute_map = {
        'allowed_size': 'AllowedSize',
        'enable_extranet_url': 'EnableExtranetUrl',
        'lb_strategy': 'LBStrategy',
        'office_id': 'OfficeId',
        'office_ip': 'OfficeIp',
        'office_name': 'OfficeName',
        'office_status': 'OfficeStatus',
        'recommended_size': 'RecommendedSize'
    }

    def __init__(self, allowed_size=None, enable_extranet_url=None, lb_strategy=None, office_id=None, office_ip=None, office_name=None, office_status=None, recommended_size=None, _configuration=None):  # noqa: E501
        """UpdateOfficeConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allowed_size = None
        self._enable_extranet_url = None
        self._lb_strategy = None
        self._office_id = None
        self._office_ip = None
        self._office_name = None
        self._office_status = None
        self._recommended_size = None
        self.discriminator = None

        if allowed_size is not None:
            self.allowed_size = allowed_size
        if enable_extranet_url is not None:
            self.enable_extranet_url = enable_extranet_url
        if lb_strategy is not None:
            self.lb_strategy = lb_strategy
        self.office_id = office_id
        if office_ip is not None:
            self.office_ip = office_ip
        if office_name is not None:
            self.office_name = office_name
        if office_status is not None:
            self.office_status = office_status
        if recommended_size is not None:
            self.recommended_size = recommended_size

    @property
    def allowed_size(self):
        """Gets the allowed_size of this UpdateOfficeConfigRequest.  # noqa: E501


        :return: The allowed_size of this UpdateOfficeConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._allowed_size

    @allowed_size.setter
    def allowed_size(self, allowed_size):
        """Sets the allowed_size of this UpdateOfficeConfigRequest.


        :param allowed_size: The allowed_size of this UpdateOfficeConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._allowed_size = allowed_size

    @property
    def enable_extranet_url(self):
        """Gets the enable_extranet_url of this UpdateOfficeConfigRequest.  # noqa: E501


        :return: The enable_extranet_url of this UpdateOfficeConfigRequest.  # noqa: E501
        :rtype: bool
        """
        return self._enable_extranet_url

    @enable_extranet_url.setter
    def enable_extranet_url(self, enable_extranet_url):
        """Sets the enable_extranet_url of this UpdateOfficeConfigRequest.


        :param enable_extranet_url: The enable_extranet_url of this UpdateOfficeConfigRequest.  # noqa: E501
        :type: bool
        """

        self._enable_extranet_url = enable_extranet_url

    @property
    def lb_strategy(self):
        """Gets the lb_strategy of this UpdateOfficeConfigRequest.  # noqa: E501


        :return: The lb_strategy of this UpdateOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._lb_strategy

    @lb_strategy.setter
    def lb_strategy(self, lb_strategy):
        """Sets the lb_strategy of this UpdateOfficeConfigRequest.


        :param lb_strategy: The lb_strategy of this UpdateOfficeConfigRequest.  # noqa: E501
        :type: str
        """

        self._lb_strategy = lb_strategy

    @property
    def office_id(self):
        """Gets the office_id of this UpdateOfficeConfigRequest.  # noqa: E501


        :return: The office_id of this UpdateOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._office_id

    @office_id.setter
    def office_id(self, office_id):
        """Sets the office_id of this UpdateOfficeConfigRequest.


        :param office_id: The office_id of this UpdateOfficeConfigRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and office_id is None:
            raise ValueError("Invalid value for `office_id`, must not be `None`")  # noqa: E501

        self._office_id = office_id

    @property
    def office_ip(self):
        """Gets the office_ip of this UpdateOfficeConfigRequest.  # noqa: E501


        :return: The office_ip of this UpdateOfficeConfigRequest.  # noqa: E501
        :rtype: OfficeIpForUpdateOfficeConfigInput
        """
        return self._office_ip

    @office_ip.setter
    def office_ip(self, office_ip):
        """Sets the office_ip of this UpdateOfficeConfigRequest.


        :param office_ip: The office_ip of this UpdateOfficeConfigRequest.  # noqa: E501
        :type: OfficeIpForUpdateOfficeConfigInput
        """

        self._office_ip = office_ip

    @property
    def office_name(self):
        """Gets the office_name of this UpdateOfficeConfigRequest.  # noqa: E501


        :return: The office_name of this UpdateOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._office_name

    @office_name.setter
    def office_name(self, office_name):
        """Sets the office_name of this UpdateOfficeConfigRequest.


        :param office_name: The office_name of this UpdateOfficeConfigRequest.  # noqa: E501
        :type: str
        """

        self._office_name = office_name

    @property
    def office_status(self):
        """Gets the office_status of this UpdateOfficeConfigRequest.  # noqa: E501


        :return: The office_status of this UpdateOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._office_status

    @office_status.setter
    def office_status(self, office_status):
        """Sets the office_status of this UpdateOfficeConfigRequest.


        :param office_status: The office_status of this UpdateOfficeConfigRequest.  # noqa: E501
        :type: str
        """

        self._office_status = office_status

    @property
    def recommended_size(self):
        """Gets the recommended_size of this UpdateOfficeConfigRequest.  # noqa: E501


        :return: The recommended_size of this UpdateOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._recommended_size

    @recommended_size.setter
    def recommended_size(self, recommended_size):
        """Sets the recommended_size of this UpdateOfficeConfigRequest.


        :param recommended_size: The recommended_size of this UpdateOfficeConfigRequest.  # noqa: E501
        :type: str
        """

        self._recommended_size = recommended_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateOfficeConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateOfficeConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateOfficeConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
