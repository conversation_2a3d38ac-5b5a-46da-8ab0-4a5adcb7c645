# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VirusHitDataListForGetRaspAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'hit_len': 'int',
        'offset': 'int',
        'rule_id': 'str'
    }

    attribute_map = {
        'hit_len': 'HitLen',
        'offset': 'Offset',
        'rule_id': 'RuleID'
    }

    def __init__(self, hit_len=None, offset=None, rule_id=None, _configuration=None):  # noqa: E501
        """VirusHitDataListForGetRaspAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._hit_len = None
        self._offset = None
        self._rule_id = None
        self.discriminator = None

        if hit_len is not None:
            self.hit_len = hit_len
        if offset is not None:
            self.offset = offset
        if rule_id is not None:
            self.rule_id = rule_id

    @property
    def hit_len(self):
        """Gets the hit_len of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501


        :return: The hit_len of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._hit_len

    @hit_len.setter
    def hit_len(self, hit_len):
        """Sets the hit_len of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.


        :param hit_len: The hit_len of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :type: int
        """

        self._hit_len = hit_len

    @property
    def offset(self):
        """Gets the offset of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501


        :return: The offset of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.


        :param offset: The offset of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def rule_id(self):
        """Gets the rule_id of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501


        :return: The rule_id of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.


        :param rule_id: The rule_id of this VirusHitDataListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._rule_id = rule_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VirusHitDataListForGetRaspAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VirusHitDataListForGetRaspAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VirusHitDataListForGetRaspAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
