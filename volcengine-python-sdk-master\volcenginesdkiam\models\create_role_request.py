# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateRoleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'display_name': 'str',
        'max_session_duration': 'int',
        'role_name': 'str',
        'tags': 'list[TagForCreateRoleInput]',
        'trust_policy_document': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'display_name': 'DisplayName',
        'max_session_duration': 'MaxSessionDuration',
        'role_name': 'RoleName',
        'tags': 'Tags',
        'trust_policy_document': 'TrustPolicyDocument'
    }

    def __init__(self, description=None, display_name=None, max_session_duration=None, role_name=None, tags=None, trust_policy_document=None, _configuration=None):  # noqa: E501
        """CreateRoleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._display_name = None
        self._max_session_duration = None
        self._role_name = None
        self._tags = None
        self._trust_policy_document = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if display_name is not None:
            self.display_name = display_name
        if max_session_duration is not None:
            self.max_session_duration = max_session_duration
        self.role_name = role_name
        if tags is not None:
            self.tags = tags
        if trust_policy_document is not None:
            self.trust_policy_document = trust_policy_document

    @property
    def description(self):
        """Gets the description of this CreateRoleRequest.  # noqa: E501


        :return: The description of this CreateRoleRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateRoleRequest.


        :param description: The description of this CreateRoleRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def display_name(self):
        """Gets the display_name of this CreateRoleRequest.  # noqa: E501


        :return: The display_name of this CreateRoleRequest.  # noqa: E501
        :rtype: str
        """
        return self._display_name

    @display_name.setter
    def display_name(self, display_name):
        """Sets the display_name of this CreateRoleRequest.


        :param display_name: The display_name of this CreateRoleRequest.  # noqa: E501
        :type: str
        """

        self._display_name = display_name

    @property
    def max_session_duration(self):
        """Gets the max_session_duration of this CreateRoleRequest.  # noqa: E501


        :return: The max_session_duration of this CreateRoleRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_session_duration

    @max_session_duration.setter
    def max_session_duration(self, max_session_duration):
        """Sets the max_session_duration of this CreateRoleRequest.


        :param max_session_duration: The max_session_duration of this CreateRoleRequest.  # noqa: E501
        :type: int
        """

        self._max_session_duration = max_session_duration

    @property
    def role_name(self):
        """Gets the role_name of this CreateRoleRequest.  # noqa: E501


        :return: The role_name of this CreateRoleRequest.  # noqa: E501
        :rtype: str
        """
        return self._role_name

    @role_name.setter
    def role_name(self, role_name):
        """Sets the role_name of this CreateRoleRequest.


        :param role_name: The role_name of this CreateRoleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and role_name is None:
            raise ValueError("Invalid value for `role_name`, must not be `None`")  # noqa: E501

        self._role_name = role_name

    @property
    def tags(self):
        """Gets the tags of this CreateRoleRequest.  # noqa: E501


        :return: The tags of this CreateRoleRequest.  # noqa: E501
        :rtype: list[TagForCreateRoleInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateRoleRequest.


        :param tags: The tags of this CreateRoleRequest.  # noqa: E501
        :type: list[TagForCreateRoleInput]
        """

        self._tags = tags

    @property
    def trust_policy_document(self):
        """Gets the trust_policy_document of this CreateRoleRequest.  # noqa: E501


        :return: The trust_policy_document of this CreateRoleRequest.  # noqa: E501
        :rtype: str
        """
        return self._trust_policy_document

    @trust_policy_document.setter
    def trust_policy_document(self, trust_policy_document):
        """Sets the trust_policy_document of this CreateRoleRequest.


        :param trust_policy_document: The trust_policy_document of this CreateRoleRequest.  # noqa: E501
        :type: str
        """

        self._trust_policy_document = trust_policy_document

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateRoleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateRoleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateRoleRequest):
            return True

        return self.to_dict() != other.to_dict()
