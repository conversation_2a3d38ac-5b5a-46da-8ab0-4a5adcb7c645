# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListWorkspaceExtraBucketsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bind_time': 'int',
        'bucket': 'str',
        'exist': 'bool',
        'id': 'str',
        'message': 'str',
        'type': 'str'
    }

    attribute_map = {
        'bind_time': 'BindTime',
        'bucket': 'Bucket',
        'exist': 'Exist',
        'id': 'ID',
        'message': 'Message',
        'type': 'Type'
    }

    def __init__(self, bind_time=None, bucket=None, exist=None, id=None, message=None, type=None, _configuration=None):  # noqa: E501
        """ItemForListWorkspaceExtraBucketsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bind_time = None
        self._bucket = None
        self._exist = None
        self._id = None
        self._message = None
        self._type = None
        self.discriminator = None

        if bind_time is not None:
            self.bind_time = bind_time
        if bucket is not None:
            self.bucket = bucket
        if exist is not None:
            self.exist = exist
        if id is not None:
            self.id = id
        if message is not None:
            self.message = message
        if type is not None:
            self.type = type

    @property
    def bind_time(self):
        """Gets the bind_time of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501


        :return: The bind_time of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :rtype: int
        """
        return self._bind_time

    @bind_time.setter
    def bind_time(self, bind_time):
        """Sets the bind_time of this ItemForListWorkspaceExtraBucketsOutput.


        :param bind_time: The bind_time of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :type: int
        """

        self._bind_time = bind_time

    @property
    def bucket(self):
        """Gets the bucket of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501


        :return: The bucket of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :rtype: str
        """
        return self._bucket

    @bucket.setter
    def bucket(self, bucket):
        """Sets the bucket of this ItemForListWorkspaceExtraBucketsOutput.


        :param bucket: The bucket of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :type: str
        """

        self._bucket = bucket

    @property
    def exist(self):
        """Gets the exist of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501


        :return: The exist of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._exist

    @exist.setter
    def exist(self, exist):
        """Sets the exist of this ItemForListWorkspaceExtraBucketsOutput.


        :param exist: The exist of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :type: bool
        """

        self._exist = exist

    @property
    def id(self):
        """Gets the id of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501


        :return: The id of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListWorkspaceExtraBucketsOutput.


        :param id: The id of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def message(self):
        """Gets the message of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501


        :return: The message of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this ItemForListWorkspaceExtraBucketsOutput.


        :param message: The message of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def type(self):
        """Gets the type of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501


        :return: The type of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ItemForListWorkspaceExtraBucketsOutput.


        :param type: The type of this ItemForListWorkspaceExtraBucketsOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListWorkspaceExtraBucketsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListWorkspaceExtraBucketsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListWorkspaceExtraBucketsOutput):
            return True

        return self.to_dict() != other.to_dict()
