# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetFingerprintIntegrityOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_tags': 'list[str]',
        'digest': 'str',
        'eip_address': 'str',
        'exe': 'str',
        'hostname': 'str',
        'id': 'str',
        'modify_time': 'int',
        'name': 'str',
        'origin_digest': 'str',
        'primary_ip_address': 'str',
        'start_time': 'int',
        'update_time': 'int',
        'version': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_tags': 'AgentTags',
        'digest': 'Digest',
        'eip_address': 'EipAddress',
        'exe': 'Exe',
        'hostname': 'Hostname',
        'id': 'ID',
        'modify_time': 'ModifyTime',
        'name': 'Name',
        'origin_digest': 'OriginDigest',
        'primary_ip_address': 'PrimaryIpAddress',
        'start_time': 'StartTime',
        'update_time': 'UpdateTime',
        'version': 'Version'
    }

    def __init__(self, agent_id=None, agent_tags=None, digest=None, eip_address=None, exe=None, hostname=None, id=None, modify_time=None, name=None, origin_digest=None, primary_ip_address=None, start_time=None, update_time=None, version=None, _configuration=None):  # noqa: E501
        """DataForGetFingerprintIntegrityOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_tags = None
        self._digest = None
        self._eip_address = None
        self._exe = None
        self._hostname = None
        self._id = None
        self._modify_time = None
        self._name = None
        self._origin_digest = None
        self._primary_ip_address = None
        self._start_time = None
        self._update_time = None
        self._version = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if digest is not None:
            self.digest = digest
        if eip_address is not None:
            self.eip_address = eip_address
        if exe is not None:
            self.exe = exe
        if hostname is not None:
            self.hostname = hostname
        if id is not None:
            self.id = id
        if modify_time is not None:
            self.modify_time = modify_time
        if name is not None:
            self.name = name
        if origin_digest is not None:
            self.origin_digest = origin_digest
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if start_time is not None:
            self.start_time = start_time
        if update_time is not None:
            self.update_time = update_time
        if version is not None:
            self.version = version

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The agent_id of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForGetFingerprintIntegrityOutput.


        :param agent_id: The agent_id of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The agent_tags of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this DataForGetFingerprintIntegrityOutput.


        :param agent_tags: The agent_tags of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def digest(self):
        """Gets the digest of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The digest of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._digest

    @digest.setter
    def digest(self, digest):
        """Sets the digest of this DataForGetFingerprintIntegrityOutput.


        :param digest: The digest of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._digest = digest

    @property
    def eip_address(self):
        """Gets the eip_address of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The eip_address of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this DataForGetFingerprintIntegrityOutput.


        :param eip_address: The eip_address of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def exe(self):
        """Gets the exe of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The exe of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._exe

    @exe.setter
    def exe(self, exe):
        """Sets the exe of this DataForGetFingerprintIntegrityOutput.


        :param exe: The exe of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._exe = exe

    @property
    def hostname(self):
        """Gets the hostname of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The hostname of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this DataForGetFingerprintIntegrityOutput.


        :param hostname: The hostname of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def id(self):
        """Gets the id of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The id of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForGetFingerprintIntegrityOutput.


        :param id: The id of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def modify_time(self):
        """Gets the modify_time of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The modify_time of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: int
        """
        return self._modify_time

    @modify_time.setter
    def modify_time(self, modify_time):
        """Sets the modify_time of this DataForGetFingerprintIntegrityOutput.


        :param modify_time: The modify_time of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: int
        """

        self._modify_time = modify_time

    @property
    def name(self):
        """Gets the name of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The name of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForGetFingerprintIntegrityOutput.


        :param name: The name of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def origin_digest(self):
        """Gets the origin_digest of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The origin_digest of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._origin_digest

    @origin_digest.setter
    def origin_digest(self, origin_digest):
        """Sets the origin_digest of this DataForGetFingerprintIntegrityOutput.


        :param origin_digest: The origin_digest of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._origin_digest = origin_digest

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The primary_ip_address of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this DataForGetFingerprintIntegrityOutput.


        :param primary_ip_address: The primary_ip_address of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def start_time(self):
        """Gets the start_time of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The start_time of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataForGetFingerprintIntegrityOutput.


        :param start_time: The start_time of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    @property
    def update_time(self):
        """Gets the update_time of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The update_time of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForGetFingerprintIntegrityOutput.


        :param update_time: The update_time of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    @property
    def version(self):
        """Gets the version of this DataForGetFingerprintIntegrityOutput.  # noqa: E501


        :return: The version of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this DataForGetFingerprintIntegrityOutput.


        :param version: The version of this DataForGetFingerprintIntegrityOutput.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetFingerprintIntegrityOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetFingerprintIntegrityOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetFingerprintIntegrityOutput):
            return True

        return self.to_dict() != other.to_dict()
