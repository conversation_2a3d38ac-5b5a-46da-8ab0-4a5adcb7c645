# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateNotifyTemplateRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'active': 'ActiveForCreateNotifyTemplateInput',
        'channel': 'str',
        'description': 'str',
        'name': 'str',
        'resolved': 'ResolvedForCreateNotifyTemplateInput'
    }

    attribute_map = {
        'active': 'Active',
        'channel': 'Channel',
        'description': 'Description',
        'name': 'Name',
        'resolved': 'Resolved'
    }

    def __init__(self, active=None, channel=None, description=None, name=None, resolved=None, _configuration=None):  # noqa: E501
        """CreateNotifyTemplateRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._active = None
        self._channel = None
        self._description = None
        self._name = None
        self._resolved = None
        self.discriminator = None

        if active is not None:
            self.active = active
        self.channel = channel
        if description is not None:
            self.description = description
        self.name = name
        if resolved is not None:
            self.resolved = resolved

    @property
    def active(self):
        """Gets the active of this CreateNotifyTemplateRequest.  # noqa: E501


        :return: The active of this CreateNotifyTemplateRequest.  # noqa: E501
        :rtype: ActiveForCreateNotifyTemplateInput
        """
        return self._active

    @active.setter
    def active(self, active):
        """Sets the active of this CreateNotifyTemplateRequest.


        :param active: The active of this CreateNotifyTemplateRequest.  # noqa: E501
        :type: ActiveForCreateNotifyTemplateInput
        """

        self._active = active

    @property
    def channel(self):
        """Gets the channel of this CreateNotifyTemplateRequest.  # noqa: E501


        :return: The channel of this CreateNotifyTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._channel

    @channel.setter
    def channel(self, channel):
        """Sets the channel of this CreateNotifyTemplateRequest.


        :param channel: The channel of this CreateNotifyTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and channel is None:
            raise ValueError("Invalid value for `channel`, must not be `None`")  # noqa: E501

        self._channel = channel

    @property
    def description(self):
        """Gets the description of this CreateNotifyTemplateRequest.  # noqa: E501


        :return: The description of this CreateNotifyTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateNotifyTemplateRequest.


        :param description: The description of this CreateNotifyTemplateRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def name(self):
        """Gets the name of this CreateNotifyTemplateRequest.  # noqa: E501


        :return: The name of this CreateNotifyTemplateRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateNotifyTemplateRequest.


        :param name: The name of this CreateNotifyTemplateRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def resolved(self):
        """Gets the resolved of this CreateNotifyTemplateRequest.  # noqa: E501


        :return: The resolved of this CreateNotifyTemplateRequest.  # noqa: E501
        :rtype: ResolvedForCreateNotifyTemplateInput
        """
        return self._resolved

    @resolved.setter
    def resolved(self, resolved):
        """Sets the resolved of this CreateNotifyTemplateRequest.


        :param resolved: The resolved of this CreateNotifyTemplateRequest.  # noqa: E501
        :type: ResolvedForCreateNotifyTemplateInput
        """

        self._resolved = resolved

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateNotifyTemplateRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateNotifyTemplateRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateNotifyTemplateRequest):
            return True

        return self.to_dict() != other.to_dict()
