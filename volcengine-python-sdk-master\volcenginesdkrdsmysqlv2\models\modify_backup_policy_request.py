# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyBackupPolicyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'binlog_backup_all_retention': 'bool',
        'binlog_backup_enabled': 'bool',
        'binlog_backup_encryption_enabled': 'bool',
        'binlog_file_counts_enable': 'bool',
        'binlog_limit_count': 'int',
        'binlog_local_retention_hour': 'int',
        'binlog_space_limit_enable': 'bool',
        'binlog_storage_percentage': 'int',
        'data_backup_all_retention': 'bool',
        'data_backup_encryption_enabled': 'bool',
        'data_backup_retention_day': 'int',
        'data_full_backup_periods': 'list[str]',
        'data_full_backup_start_utc_hour': 'int',
        'data_full_backup_time': 'str',
        'data_incr_backup_periods': 'list[str]',
        'data_keep_days_after_released': 'int',
        'data_keep_policy_after_released': 'str',
        'hourly_incr_backup_enable': 'bool',
        'incr_backup_hour_period': 'int',
        'instance_id': 'str',
        'lock_ddl_time': 'int',
        'log_backup_retention_day': 'int',
        'retention_policy_synced': 'bool'
    }

    attribute_map = {
        'binlog_backup_all_retention': 'BinlogBackupAllRetention',
        'binlog_backup_enabled': 'BinlogBackupEnabled',
        'binlog_backup_encryption_enabled': 'BinlogBackupEncryptionEnabled',
        'binlog_file_counts_enable': 'BinlogFileCountsEnable',
        'binlog_limit_count': 'BinlogLimitCount',
        'binlog_local_retention_hour': 'BinlogLocalRetentionHour',
        'binlog_space_limit_enable': 'BinlogSpaceLimitEnable',
        'binlog_storage_percentage': 'BinlogStoragePercentage',
        'data_backup_all_retention': 'DataBackupAllRetention',
        'data_backup_encryption_enabled': 'DataBackupEncryptionEnabled',
        'data_backup_retention_day': 'DataBackupRetentionDay',
        'data_full_backup_periods': 'DataFullBackupPeriods',
        'data_full_backup_start_utc_hour': 'DataFullBackupStartUTCHour',
        'data_full_backup_time': 'DataFullBackupTime',
        'data_incr_backup_periods': 'DataIncrBackupPeriods',
        'data_keep_days_after_released': 'DataKeepDaysAfterReleased',
        'data_keep_policy_after_released': 'DataKeepPolicyAfterReleased',
        'hourly_incr_backup_enable': 'HourlyIncrBackupEnable',
        'incr_backup_hour_period': 'IncrBackupHourPeriod',
        'instance_id': 'InstanceId',
        'lock_ddl_time': 'LockDDLTime',
        'log_backup_retention_day': 'LogBackupRetentionDay',
        'retention_policy_synced': 'RetentionPolicySynced'
    }

    def __init__(self, binlog_backup_all_retention=None, binlog_backup_enabled=None, binlog_backup_encryption_enabled=None, binlog_file_counts_enable=None, binlog_limit_count=None, binlog_local_retention_hour=None, binlog_space_limit_enable=None, binlog_storage_percentage=None, data_backup_all_retention=None, data_backup_encryption_enabled=None, data_backup_retention_day=None, data_full_backup_periods=None, data_full_backup_start_utc_hour=None, data_full_backup_time=None, data_incr_backup_periods=None, data_keep_days_after_released=None, data_keep_policy_after_released=None, hourly_incr_backup_enable=None, incr_backup_hour_period=None, instance_id=None, lock_ddl_time=None, log_backup_retention_day=None, retention_policy_synced=None, _configuration=None):  # noqa: E501
        """ModifyBackupPolicyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._binlog_backup_all_retention = None
        self._binlog_backup_enabled = None
        self._binlog_backup_encryption_enabled = None
        self._binlog_file_counts_enable = None
        self._binlog_limit_count = None
        self._binlog_local_retention_hour = None
        self._binlog_space_limit_enable = None
        self._binlog_storage_percentage = None
        self._data_backup_all_retention = None
        self._data_backup_encryption_enabled = None
        self._data_backup_retention_day = None
        self._data_full_backup_periods = None
        self._data_full_backup_start_utc_hour = None
        self._data_full_backup_time = None
        self._data_incr_backup_periods = None
        self._data_keep_days_after_released = None
        self._data_keep_policy_after_released = None
        self._hourly_incr_backup_enable = None
        self._incr_backup_hour_period = None
        self._instance_id = None
        self._lock_ddl_time = None
        self._log_backup_retention_day = None
        self._retention_policy_synced = None
        self.discriminator = None

        if binlog_backup_all_retention is not None:
            self.binlog_backup_all_retention = binlog_backup_all_retention
        if binlog_backup_enabled is not None:
            self.binlog_backup_enabled = binlog_backup_enabled
        if binlog_backup_encryption_enabled is not None:
            self.binlog_backup_encryption_enabled = binlog_backup_encryption_enabled
        if binlog_file_counts_enable is not None:
            self.binlog_file_counts_enable = binlog_file_counts_enable
        if binlog_limit_count is not None:
            self.binlog_limit_count = binlog_limit_count
        if binlog_local_retention_hour is not None:
            self.binlog_local_retention_hour = binlog_local_retention_hour
        if binlog_space_limit_enable is not None:
            self.binlog_space_limit_enable = binlog_space_limit_enable
        if binlog_storage_percentage is not None:
            self.binlog_storage_percentage = binlog_storage_percentage
        if data_backup_all_retention is not None:
            self.data_backup_all_retention = data_backup_all_retention
        if data_backup_encryption_enabled is not None:
            self.data_backup_encryption_enabled = data_backup_encryption_enabled
        if data_backup_retention_day is not None:
            self.data_backup_retention_day = data_backup_retention_day
        if data_full_backup_periods is not None:
            self.data_full_backup_periods = data_full_backup_periods
        if data_full_backup_start_utc_hour is not None:
            self.data_full_backup_start_utc_hour = data_full_backup_start_utc_hour
        if data_full_backup_time is not None:
            self.data_full_backup_time = data_full_backup_time
        if data_incr_backup_periods is not None:
            self.data_incr_backup_periods = data_incr_backup_periods
        if data_keep_days_after_released is not None:
            self.data_keep_days_after_released = data_keep_days_after_released
        if data_keep_policy_after_released is not None:
            self.data_keep_policy_after_released = data_keep_policy_after_released
        if hourly_incr_backup_enable is not None:
            self.hourly_incr_backup_enable = hourly_incr_backup_enable
        if incr_backup_hour_period is not None:
            self.incr_backup_hour_period = incr_backup_hour_period
        self.instance_id = instance_id
        if lock_ddl_time is not None:
            self.lock_ddl_time = lock_ddl_time
        if log_backup_retention_day is not None:
            self.log_backup_retention_day = log_backup_retention_day
        if retention_policy_synced is not None:
            self.retention_policy_synced = retention_policy_synced

    @property
    def binlog_backup_all_retention(self):
        """Gets the binlog_backup_all_retention of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The binlog_backup_all_retention of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._binlog_backup_all_retention

    @binlog_backup_all_retention.setter
    def binlog_backup_all_retention(self, binlog_backup_all_retention):
        """Sets the binlog_backup_all_retention of this ModifyBackupPolicyRequest.


        :param binlog_backup_all_retention: The binlog_backup_all_retention of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: bool
        """

        self._binlog_backup_all_retention = binlog_backup_all_retention

    @property
    def binlog_backup_enabled(self):
        """Gets the binlog_backup_enabled of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The binlog_backup_enabled of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._binlog_backup_enabled

    @binlog_backup_enabled.setter
    def binlog_backup_enabled(self, binlog_backup_enabled):
        """Sets the binlog_backup_enabled of this ModifyBackupPolicyRequest.


        :param binlog_backup_enabled: The binlog_backup_enabled of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: bool
        """

        self._binlog_backup_enabled = binlog_backup_enabled

    @property
    def binlog_backup_encryption_enabled(self):
        """Gets the binlog_backup_encryption_enabled of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The binlog_backup_encryption_enabled of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._binlog_backup_encryption_enabled

    @binlog_backup_encryption_enabled.setter
    def binlog_backup_encryption_enabled(self, binlog_backup_encryption_enabled):
        """Sets the binlog_backup_encryption_enabled of this ModifyBackupPolicyRequest.


        :param binlog_backup_encryption_enabled: The binlog_backup_encryption_enabled of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: bool
        """

        self._binlog_backup_encryption_enabled = binlog_backup_encryption_enabled

    @property
    def binlog_file_counts_enable(self):
        """Gets the binlog_file_counts_enable of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The binlog_file_counts_enable of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._binlog_file_counts_enable

    @binlog_file_counts_enable.setter
    def binlog_file_counts_enable(self, binlog_file_counts_enable):
        """Sets the binlog_file_counts_enable of this ModifyBackupPolicyRequest.


        :param binlog_file_counts_enable: The binlog_file_counts_enable of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: bool
        """

        self._binlog_file_counts_enable = binlog_file_counts_enable

    @property
    def binlog_limit_count(self):
        """Gets the binlog_limit_count of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The binlog_limit_count of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._binlog_limit_count

    @binlog_limit_count.setter
    def binlog_limit_count(self, binlog_limit_count):
        """Sets the binlog_limit_count of this ModifyBackupPolicyRequest.


        :param binlog_limit_count: The binlog_limit_count of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: int
        """

        self._binlog_limit_count = binlog_limit_count

    @property
    def binlog_local_retention_hour(self):
        """Gets the binlog_local_retention_hour of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The binlog_local_retention_hour of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._binlog_local_retention_hour

    @binlog_local_retention_hour.setter
    def binlog_local_retention_hour(self, binlog_local_retention_hour):
        """Sets the binlog_local_retention_hour of this ModifyBackupPolicyRequest.


        :param binlog_local_retention_hour: The binlog_local_retention_hour of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: int
        """

        self._binlog_local_retention_hour = binlog_local_retention_hour

    @property
    def binlog_space_limit_enable(self):
        """Gets the binlog_space_limit_enable of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The binlog_space_limit_enable of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._binlog_space_limit_enable

    @binlog_space_limit_enable.setter
    def binlog_space_limit_enable(self, binlog_space_limit_enable):
        """Sets the binlog_space_limit_enable of this ModifyBackupPolicyRequest.


        :param binlog_space_limit_enable: The binlog_space_limit_enable of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: bool
        """

        self._binlog_space_limit_enable = binlog_space_limit_enable

    @property
    def binlog_storage_percentage(self):
        """Gets the binlog_storage_percentage of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The binlog_storage_percentage of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._binlog_storage_percentage

    @binlog_storage_percentage.setter
    def binlog_storage_percentage(self, binlog_storage_percentage):
        """Sets the binlog_storage_percentage of this ModifyBackupPolicyRequest.


        :param binlog_storage_percentage: The binlog_storage_percentage of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: int
        """

        self._binlog_storage_percentage = binlog_storage_percentage

    @property
    def data_backup_all_retention(self):
        """Gets the data_backup_all_retention of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The data_backup_all_retention of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._data_backup_all_retention

    @data_backup_all_retention.setter
    def data_backup_all_retention(self, data_backup_all_retention):
        """Sets the data_backup_all_retention of this ModifyBackupPolicyRequest.


        :param data_backup_all_retention: The data_backup_all_retention of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: bool
        """

        self._data_backup_all_retention = data_backup_all_retention

    @property
    def data_backup_encryption_enabled(self):
        """Gets the data_backup_encryption_enabled of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The data_backup_encryption_enabled of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._data_backup_encryption_enabled

    @data_backup_encryption_enabled.setter
    def data_backup_encryption_enabled(self, data_backup_encryption_enabled):
        """Sets the data_backup_encryption_enabled of this ModifyBackupPolicyRequest.


        :param data_backup_encryption_enabled: The data_backup_encryption_enabled of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: bool
        """

        self._data_backup_encryption_enabled = data_backup_encryption_enabled

    @property
    def data_backup_retention_day(self):
        """Gets the data_backup_retention_day of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The data_backup_retention_day of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._data_backup_retention_day

    @data_backup_retention_day.setter
    def data_backup_retention_day(self, data_backup_retention_day):
        """Sets the data_backup_retention_day of this ModifyBackupPolicyRequest.


        :param data_backup_retention_day: The data_backup_retention_day of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: int
        """

        self._data_backup_retention_day = data_backup_retention_day

    @property
    def data_full_backup_periods(self):
        """Gets the data_full_backup_periods of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The data_full_backup_periods of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._data_full_backup_periods

    @data_full_backup_periods.setter
    def data_full_backup_periods(self, data_full_backup_periods):
        """Sets the data_full_backup_periods of this ModifyBackupPolicyRequest.


        :param data_full_backup_periods: The data_full_backup_periods of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._data_full_backup_periods = data_full_backup_periods

    @property
    def data_full_backup_start_utc_hour(self):
        """Gets the data_full_backup_start_utc_hour of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The data_full_backup_start_utc_hour of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._data_full_backup_start_utc_hour

    @data_full_backup_start_utc_hour.setter
    def data_full_backup_start_utc_hour(self, data_full_backup_start_utc_hour):
        """Sets the data_full_backup_start_utc_hour of this ModifyBackupPolicyRequest.


        :param data_full_backup_start_utc_hour: The data_full_backup_start_utc_hour of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: int
        """

        self._data_full_backup_start_utc_hour = data_full_backup_start_utc_hour

    @property
    def data_full_backup_time(self):
        """Gets the data_full_backup_time of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The data_full_backup_time of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_full_backup_time

    @data_full_backup_time.setter
    def data_full_backup_time(self, data_full_backup_time):
        """Sets the data_full_backup_time of this ModifyBackupPolicyRequest.


        :param data_full_backup_time: The data_full_backup_time of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: str
        """

        self._data_full_backup_time = data_full_backup_time

    @property
    def data_incr_backup_periods(self):
        """Gets the data_incr_backup_periods of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The data_incr_backup_periods of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._data_incr_backup_periods

    @data_incr_backup_periods.setter
    def data_incr_backup_periods(self, data_incr_backup_periods):
        """Sets the data_incr_backup_periods of this ModifyBackupPolicyRequest.


        :param data_incr_backup_periods: The data_incr_backup_periods of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: list[str]
        """

        self._data_incr_backup_periods = data_incr_backup_periods

    @property
    def data_keep_days_after_released(self):
        """Gets the data_keep_days_after_released of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The data_keep_days_after_released of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._data_keep_days_after_released

    @data_keep_days_after_released.setter
    def data_keep_days_after_released(self, data_keep_days_after_released):
        """Sets the data_keep_days_after_released of this ModifyBackupPolicyRequest.


        :param data_keep_days_after_released: The data_keep_days_after_released of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: int
        """

        self._data_keep_days_after_released = data_keep_days_after_released

    @property
    def data_keep_policy_after_released(self):
        """Gets the data_keep_policy_after_released of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The data_keep_policy_after_released of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_keep_policy_after_released

    @data_keep_policy_after_released.setter
    def data_keep_policy_after_released(self, data_keep_policy_after_released):
        """Sets the data_keep_policy_after_released of this ModifyBackupPolicyRequest.


        :param data_keep_policy_after_released: The data_keep_policy_after_released of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: str
        """

        self._data_keep_policy_after_released = data_keep_policy_after_released

    @property
    def hourly_incr_backup_enable(self):
        """Gets the hourly_incr_backup_enable of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The hourly_incr_backup_enable of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._hourly_incr_backup_enable

    @hourly_incr_backup_enable.setter
    def hourly_incr_backup_enable(self, hourly_incr_backup_enable):
        """Sets the hourly_incr_backup_enable of this ModifyBackupPolicyRequest.


        :param hourly_incr_backup_enable: The hourly_incr_backup_enable of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: bool
        """

        self._hourly_incr_backup_enable = hourly_incr_backup_enable

    @property
    def incr_backup_hour_period(self):
        """Gets the incr_backup_hour_period of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The incr_backup_hour_period of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._incr_backup_hour_period

    @incr_backup_hour_period.setter
    def incr_backup_hour_period(self, incr_backup_hour_period):
        """Sets the incr_backup_hour_period of this ModifyBackupPolicyRequest.


        :param incr_backup_hour_period: The incr_backup_hour_period of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: int
        """

        self._incr_backup_hour_period = incr_backup_hour_period

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The instance_id of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyBackupPolicyRequest.


        :param instance_id: The instance_id of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def lock_ddl_time(self):
        """Gets the lock_ddl_time of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The lock_ddl_time of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._lock_ddl_time

    @lock_ddl_time.setter
    def lock_ddl_time(self, lock_ddl_time):
        """Sets the lock_ddl_time of this ModifyBackupPolicyRequest.


        :param lock_ddl_time: The lock_ddl_time of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: int
        """

        self._lock_ddl_time = lock_ddl_time

    @property
    def log_backup_retention_day(self):
        """Gets the log_backup_retention_day of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The log_backup_retention_day of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: int
        """
        return self._log_backup_retention_day

    @log_backup_retention_day.setter
    def log_backup_retention_day(self, log_backup_retention_day):
        """Sets the log_backup_retention_day of this ModifyBackupPolicyRequest.


        :param log_backup_retention_day: The log_backup_retention_day of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: int
        """

        self._log_backup_retention_day = log_backup_retention_day

    @property
    def retention_policy_synced(self):
        """Gets the retention_policy_synced of this ModifyBackupPolicyRequest.  # noqa: E501


        :return: The retention_policy_synced of this ModifyBackupPolicyRequest.  # noqa: E501
        :rtype: bool
        """
        return self._retention_policy_synced

    @retention_policy_synced.setter
    def retention_policy_synced(self, retention_policy_synced):
        """Sets the retention_policy_synced of this ModifyBackupPolicyRequest.


        :param retention_policy_synced: The retention_policy_synced of this ModifyBackupPolicyRequest.  # noqa: E501
        :type: bool
        """

        self._retention_policy_synced = retention_policy_synced

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyBackupPolicyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyBackupPolicyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyBackupPolicyRequest):
            return True

        return self.to_dict() != other.to_dict()
