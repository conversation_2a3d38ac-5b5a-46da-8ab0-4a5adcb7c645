# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SourceForDescribeMigrationSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_version': 'str',
        'created_at': 'str',
        'disk_info': 'list[DiskInfoForDescribeMigrationSourcesOutput]',
        'hostname': 'str',
        'image_info': 'ImageInfoForDescribeMigrationSourcesOutput',
        'instance_info': 'InstanceInfoForDescribeMigrationSourcesOutput',
        'last_migration_job_info': 'LastMigrationJobInfoForDescribeMigrationSourcesOutput',
        'lvm_info': 'LvmInfoForDescribeMigrationSourcesOutput',
        'migration_source_description': 'str',
        'private_ip_address': 'str',
        'project_name': 'str',
        'public_ip_address': 'str',
        'source_id': 'str',
        'source_name': 'str',
        'source_state': 'str',
        'source_type': 'str',
        'suggest_sync_type': 'str',
        'sync_type_support_statuses': 'list[SyncTypeSupportStatusForDescribeMigrationSourcesOutput]',
        'system_info': 'SystemInfoForDescribeMigrationSourcesOutput',
        'tags': 'list[TagForDescribeMigrationSourcesOutput]'
    }

    attribute_map = {
        'agent_version': 'AgentVersion',
        'created_at': 'CreatedAt',
        'disk_info': 'DiskInfo',
        'hostname': 'Hostname',
        'image_info': 'ImageInfo',
        'instance_info': 'InstanceInfo',
        'last_migration_job_info': 'LastMigrationJobInfo',
        'lvm_info': 'LvmInfo',
        'migration_source_description': 'MigrationSourceDescription',
        'private_ip_address': 'PrivateIpAddress',
        'project_name': 'ProjectName',
        'public_ip_address': 'PublicIpAddress',
        'source_id': 'SourceId',
        'source_name': 'SourceName',
        'source_state': 'SourceState',
        'source_type': 'SourceType',
        'suggest_sync_type': 'SuggestSyncType',
        'sync_type_support_statuses': 'SyncTypeSupportStatuses',
        'system_info': 'SystemInfo',
        'tags': 'Tags'
    }

    def __init__(self, agent_version=None, created_at=None, disk_info=None, hostname=None, image_info=None, instance_info=None, last_migration_job_info=None, lvm_info=None, migration_source_description=None, private_ip_address=None, project_name=None, public_ip_address=None, source_id=None, source_name=None, source_state=None, source_type=None, suggest_sync_type=None, sync_type_support_statuses=None, system_info=None, tags=None, _configuration=None):  # noqa: E501
        """SourceForDescribeMigrationSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_version = None
        self._created_at = None
        self._disk_info = None
        self._hostname = None
        self._image_info = None
        self._instance_info = None
        self._last_migration_job_info = None
        self._lvm_info = None
        self._migration_source_description = None
        self._private_ip_address = None
        self._project_name = None
        self._public_ip_address = None
        self._source_id = None
        self._source_name = None
        self._source_state = None
        self._source_type = None
        self._suggest_sync_type = None
        self._sync_type_support_statuses = None
        self._system_info = None
        self._tags = None
        self.discriminator = None

        if agent_version is not None:
            self.agent_version = agent_version
        if created_at is not None:
            self.created_at = created_at
        if disk_info is not None:
            self.disk_info = disk_info
        if hostname is not None:
            self.hostname = hostname
        if image_info is not None:
            self.image_info = image_info
        if instance_info is not None:
            self.instance_info = instance_info
        if last_migration_job_info is not None:
            self.last_migration_job_info = last_migration_job_info
        if lvm_info is not None:
            self.lvm_info = lvm_info
        if migration_source_description is not None:
            self.migration_source_description = migration_source_description
        if private_ip_address is not None:
            self.private_ip_address = private_ip_address
        if project_name is not None:
            self.project_name = project_name
        if public_ip_address is not None:
            self.public_ip_address = public_ip_address
        if source_id is not None:
            self.source_id = source_id
        if source_name is not None:
            self.source_name = source_name
        if source_state is not None:
            self.source_state = source_state
        if source_type is not None:
            self.source_type = source_type
        if suggest_sync_type is not None:
            self.suggest_sync_type = suggest_sync_type
        if sync_type_support_statuses is not None:
            self.sync_type_support_statuses = sync_type_support_statuses
        if system_info is not None:
            self.system_info = system_info
        if tags is not None:
            self.tags = tags

    @property
    def agent_version(self):
        """Gets the agent_version of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The agent_version of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_version

    @agent_version.setter
    def agent_version(self, agent_version):
        """Sets the agent_version of this SourceForDescribeMigrationSourcesOutput.


        :param agent_version: The agent_version of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._agent_version = agent_version

    @property
    def created_at(self):
        """Gets the created_at of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The created_at of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this SourceForDescribeMigrationSourcesOutput.


        :param created_at: The created_at of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._created_at = created_at

    @property
    def disk_info(self):
        """Gets the disk_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The disk_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: list[DiskInfoForDescribeMigrationSourcesOutput]
        """
        return self._disk_info

    @disk_info.setter
    def disk_info(self, disk_info):
        """Sets the disk_info of this SourceForDescribeMigrationSourcesOutput.


        :param disk_info: The disk_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: list[DiskInfoForDescribeMigrationSourcesOutput]
        """

        self._disk_info = disk_info

    @property
    def hostname(self):
        """Gets the hostname of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The hostname of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this SourceForDescribeMigrationSourcesOutput.


        :param hostname: The hostname of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def image_info(self):
        """Gets the image_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The image_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: ImageInfoForDescribeMigrationSourcesOutput
        """
        return self._image_info

    @image_info.setter
    def image_info(self, image_info):
        """Sets the image_info of this SourceForDescribeMigrationSourcesOutput.


        :param image_info: The image_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: ImageInfoForDescribeMigrationSourcesOutput
        """

        self._image_info = image_info

    @property
    def instance_info(self):
        """Gets the instance_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The instance_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: InstanceInfoForDescribeMigrationSourcesOutput
        """
        return self._instance_info

    @instance_info.setter
    def instance_info(self, instance_info):
        """Sets the instance_info of this SourceForDescribeMigrationSourcesOutput.


        :param instance_info: The instance_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: InstanceInfoForDescribeMigrationSourcesOutput
        """

        self._instance_info = instance_info

    @property
    def last_migration_job_info(self):
        """Gets the last_migration_job_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The last_migration_job_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: LastMigrationJobInfoForDescribeMigrationSourcesOutput
        """
        return self._last_migration_job_info

    @last_migration_job_info.setter
    def last_migration_job_info(self, last_migration_job_info):
        """Sets the last_migration_job_info of this SourceForDescribeMigrationSourcesOutput.


        :param last_migration_job_info: The last_migration_job_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: LastMigrationJobInfoForDescribeMigrationSourcesOutput
        """

        self._last_migration_job_info = last_migration_job_info

    @property
    def lvm_info(self):
        """Gets the lvm_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The lvm_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: LvmInfoForDescribeMigrationSourcesOutput
        """
        return self._lvm_info

    @lvm_info.setter
    def lvm_info(self, lvm_info):
        """Sets the lvm_info of this SourceForDescribeMigrationSourcesOutput.


        :param lvm_info: The lvm_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: LvmInfoForDescribeMigrationSourcesOutput
        """

        self._lvm_info = lvm_info

    @property
    def migration_source_description(self):
        """Gets the migration_source_description of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The migration_source_description of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._migration_source_description

    @migration_source_description.setter
    def migration_source_description(self, migration_source_description):
        """Sets the migration_source_description of this SourceForDescribeMigrationSourcesOutput.


        :param migration_source_description: The migration_source_description of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._migration_source_description = migration_source_description

    @property
    def private_ip_address(self):
        """Gets the private_ip_address of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The private_ip_address of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._private_ip_address

    @private_ip_address.setter
    def private_ip_address(self, private_ip_address):
        """Sets the private_ip_address of this SourceForDescribeMigrationSourcesOutput.


        :param private_ip_address: The private_ip_address of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._private_ip_address = private_ip_address

    @property
    def project_name(self):
        """Gets the project_name of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The project_name of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this SourceForDescribeMigrationSourcesOutput.


        :param project_name: The project_name of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def public_ip_address(self):
        """Gets the public_ip_address of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The public_ip_address of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._public_ip_address

    @public_ip_address.setter
    def public_ip_address(self, public_ip_address):
        """Sets the public_ip_address of this SourceForDescribeMigrationSourcesOutput.


        :param public_ip_address: The public_ip_address of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._public_ip_address = public_ip_address

    @property
    def source_id(self):
        """Gets the source_id of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The source_id of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_id

    @source_id.setter
    def source_id(self, source_id):
        """Sets the source_id of this SourceForDescribeMigrationSourcesOutput.


        :param source_id: The source_id of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._source_id = source_id

    @property
    def source_name(self):
        """Gets the source_name of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The source_name of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_name

    @source_name.setter
    def source_name(self, source_name):
        """Sets the source_name of this SourceForDescribeMigrationSourcesOutput.


        :param source_name: The source_name of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._source_name = source_name

    @property
    def source_state(self):
        """Gets the source_state of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The source_state of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_state

    @source_state.setter
    def source_state(self, source_state):
        """Sets the source_state of this SourceForDescribeMigrationSourcesOutput.


        :param source_state: The source_state of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._source_state = source_state

    @property
    def source_type(self):
        """Gets the source_type of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The source_type of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._source_type

    @source_type.setter
    def source_type(self, source_type):
        """Sets the source_type of this SourceForDescribeMigrationSourcesOutput.


        :param source_type: The source_type of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._source_type = source_type

    @property
    def suggest_sync_type(self):
        """Gets the suggest_sync_type of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The suggest_sync_type of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._suggest_sync_type

    @suggest_sync_type.setter
    def suggest_sync_type(self, suggest_sync_type):
        """Sets the suggest_sync_type of this SourceForDescribeMigrationSourcesOutput.


        :param suggest_sync_type: The suggest_sync_type of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._suggest_sync_type = suggest_sync_type

    @property
    def sync_type_support_statuses(self):
        """Gets the sync_type_support_statuses of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The sync_type_support_statuses of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: list[SyncTypeSupportStatusForDescribeMigrationSourcesOutput]
        """
        return self._sync_type_support_statuses

    @sync_type_support_statuses.setter
    def sync_type_support_statuses(self, sync_type_support_statuses):
        """Sets the sync_type_support_statuses of this SourceForDescribeMigrationSourcesOutput.


        :param sync_type_support_statuses: The sync_type_support_statuses of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: list[SyncTypeSupportStatusForDescribeMigrationSourcesOutput]
        """

        self._sync_type_support_statuses = sync_type_support_statuses

    @property
    def system_info(self):
        """Gets the system_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The system_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: SystemInfoForDescribeMigrationSourcesOutput
        """
        return self._system_info

    @system_info.setter
    def system_info(self, system_info):
        """Sets the system_info of this SourceForDescribeMigrationSourcesOutput.


        :param system_info: The system_info of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: SystemInfoForDescribeMigrationSourcesOutput
        """

        self._system_info = system_info

    @property
    def tags(self):
        """Gets the tags of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The tags of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: list[TagForDescribeMigrationSourcesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this SourceForDescribeMigrationSourcesOutput.


        :param tags: The tags of this SourceForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: list[TagForDescribeMigrationSourcesOutput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SourceForDescribeMigrationSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SourceForDescribeMigrationSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SourceForDescribeMigrationSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
