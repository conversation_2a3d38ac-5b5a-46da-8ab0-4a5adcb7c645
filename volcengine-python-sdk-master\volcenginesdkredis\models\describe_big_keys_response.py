# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeBigKeysResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'big_key': 'list[BigKeyForDescribeBigKeysOutput]',
        'instance_id': 'str',
        'total': 'int'
    }

    attribute_map = {
        'big_key': 'BigKey',
        'instance_id': 'InstanceId',
        'total': 'Total'
    }

    def __init__(self, big_key=None, instance_id=None, total=None, _configuration=None):  # noqa: E501
        """DescribeBigKeysResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._big_key = None
        self._instance_id = None
        self._total = None
        self.discriminator = None

        if big_key is not None:
            self.big_key = big_key
        if instance_id is not None:
            self.instance_id = instance_id
        if total is not None:
            self.total = total

    @property
    def big_key(self):
        """Gets the big_key of this DescribeBigKeysResponse.  # noqa: E501


        :return: The big_key of this DescribeBigKeysResponse.  # noqa: E501
        :rtype: list[BigKeyForDescribeBigKeysOutput]
        """
        return self._big_key

    @big_key.setter
    def big_key(self, big_key):
        """Sets the big_key of this DescribeBigKeysResponse.


        :param big_key: The big_key of this DescribeBigKeysResponse.  # noqa: E501
        :type: list[BigKeyForDescribeBigKeysOutput]
        """

        self._big_key = big_key

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeBigKeysResponse.  # noqa: E501


        :return: The instance_id of this DescribeBigKeysResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeBigKeysResponse.


        :param instance_id: The instance_id of this DescribeBigKeysResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def total(self):
        """Gets the total of this DescribeBigKeysResponse.  # noqa: E501


        :return: The total of this DescribeBigKeysResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this DescribeBigKeysResponse.


        :param total: The total of this DescribeBigKeysResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeBigKeysResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeBigKeysResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeBigKeysResponse):
            return True

        return self.to_dict() != other.to_dict()
