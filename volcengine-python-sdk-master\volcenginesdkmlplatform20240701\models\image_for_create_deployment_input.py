# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ImageForCreateDeploymentInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'credential': 'ConvertCredentialForCreateDeploymentInput',
        'type': 'str',
        'url': 'str'
    }

    attribute_map = {
        'credential': 'Credential',
        'type': 'Type',
        'url': 'Url'
    }

    def __init__(self, credential=None, type=None, url=None, _configuration=None):  # noqa: E501
        """ImageForCreateDeploymentInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._credential = None
        self._type = None
        self._url = None
        self.discriminator = None

        if credential is not None:
            self.credential = credential
        if type is not None:
            self.type = type
        if url is not None:
            self.url = url

    @property
    def credential(self):
        """Gets the credential of this ImageForCreateDeploymentInput.  # noqa: E501


        :return: The credential of this ImageForCreateDeploymentInput.  # noqa: E501
        :rtype: ConvertCredentialForCreateDeploymentInput
        """
        return self._credential

    @credential.setter
    def credential(self, credential):
        """Sets the credential of this ImageForCreateDeploymentInput.


        :param credential: The credential of this ImageForCreateDeploymentInput.  # noqa: E501
        :type: ConvertCredentialForCreateDeploymentInput
        """

        self._credential = credential

    @property
    def type(self):
        """Gets the type of this ImageForCreateDeploymentInput.  # noqa: E501


        :return: The type of this ImageForCreateDeploymentInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ImageForCreateDeploymentInput.


        :param type: The type of this ImageForCreateDeploymentInput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def url(self):
        """Gets the url of this ImageForCreateDeploymentInput.  # noqa: E501


        :return: The url of this ImageForCreateDeploymentInput.  # noqa: E501
        :rtype: str
        """
        return self._url

    @url.setter
    def url(self, url):
        """Sets the url of this ImageForCreateDeploymentInput.


        :param url: The url of this ImageForCreateDeploymentInput.  # noqa: E501
        :type: str
        """

        self._url = url

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ImageForCreateDeploymentInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ImageForCreateDeploymentInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ImageForCreateDeploymentInput):
            return True

        return self.to_dict() != other.to_dict()
