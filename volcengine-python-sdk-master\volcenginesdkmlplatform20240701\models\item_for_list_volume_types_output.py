# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListVolumeTypesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'maximum_bandwidth_mbps': 'int',
        'maximum_iops': 'int',
        'name': 'str',
        'price_by_day': 'float',
        'price_by_hour': 'float',
        'price_by_month': 'float'
    }

    attribute_map = {
        'id': 'Id',
        'maximum_bandwidth_mbps': 'MaximumBandwidthMbps',
        'maximum_iops': 'MaximumIops',
        'name': 'Name',
        'price_by_day': 'PriceByDay',
        'price_by_hour': 'PriceByHour',
        'price_by_month': 'PriceByMonth'
    }

    def __init__(self, id=None, maximum_bandwidth_mbps=None, maximum_iops=None, name=None, price_by_day=None, price_by_hour=None, price_by_month=None, _configuration=None):  # noqa: E501
        """ItemForListVolumeTypesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._maximum_bandwidth_mbps = None
        self._maximum_iops = None
        self._name = None
        self._price_by_day = None
        self._price_by_hour = None
        self._price_by_month = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if maximum_bandwidth_mbps is not None:
            self.maximum_bandwidth_mbps = maximum_bandwidth_mbps
        if maximum_iops is not None:
            self.maximum_iops = maximum_iops
        if name is not None:
            self.name = name
        if price_by_day is not None:
            self.price_by_day = price_by_day
        if price_by_hour is not None:
            self.price_by_hour = price_by_hour
        if price_by_month is not None:
            self.price_by_month = price_by_month

    @property
    def id(self):
        """Gets the id of this ItemForListVolumeTypesOutput.  # noqa: E501


        :return: The id of this ItemForListVolumeTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListVolumeTypesOutput.


        :param id: The id of this ItemForListVolumeTypesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def maximum_bandwidth_mbps(self):
        """Gets the maximum_bandwidth_mbps of this ItemForListVolumeTypesOutput.  # noqa: E501


        :return: The maximum_bandwidth_mbps of this ItemForListVolumeTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._maximum_bandwidth_mbps

    @maximum_bandwidth_mbps.setter
    def maximum_bandwidth_mbps(self, maximum_bandwidth_mbps):
        """Sets the maximum_bandwidth_mbps of this ItemForListVolumeTypesOutput.


        :param maximum_bandwidth_mbps: The maximum_bandwidth_mbps of this ItemForListVolumeTypesOutput.  # noqa: E501
        :type: int
        """

        self._maximum_bandwidth_mbps = maximum_bandwidth_mbps

    @property
    def maximum_iops(self):
        """Gets the maximum_iops of this ItemForListVolumeTypesOutput.  # noqa: E501


        :return: The maximum_iops of this ItemForListVolumeTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._maximum_iops

    @maximum_iops.setter
    def maximum_iops(self, maximum_iops):
        """Sets the maximum_iops of this ItemForListVolumeTypesOutput.


        :param maximum_iops: The maximum_iops of this ItemForListVolumeTypesOutput.  # noqa: E501
        :type: int
        """

        self._maximum_iops = maximum_iops

    @property
    def name(self):
        """Gets the name of this ItemForListVolumeTypesOutput.  # noqa: E501


        :return: The name of this ItemForListVolumeTypesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListVolumeTypesOutput.


        :param name: The name of this ItemForListVolumeTypesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def price_by_day(self):
        """Gets the price_by_day of this ItemForListVolumeTypesOutput.  # noqa: E501


        :return: The price_by_day of this ItemForListVolumeTypesOutput.  # noqa: E501
        :rtype: float
        """
        return self._price_by_day

    @price_by_day.setter
    def price_by_day(self, price_by_day):
        """Sets the price_by_day of this ItemForListVolumeTypesOutput.


        :param price_by_day: The price_by_day of this ItemForListVolumeTypesOutput.  # noqa: E501
        :type: float
        """

        self._price_by_day = price_by_day

    @property
    def price_by_hour(self):
        """Gets the price_by_hour of this ItemForListVolumeTypesOutput.  # noqa: E501


        :return: The price_by_hour of this ItemForListVolumeTypesOutput.  # noqa: E501
        :rtype: float
        """
        return self._price_by_hour

    @price_by_hour.setter
    def price_by_hour(self, price_by_hour):
        """Sets the price_by_hour of this ItemForListVolumeTypesOutput.


        :param price_by_hour: The price_by_hour of this ItemForListVolumeTypesOutput.  # noqa: E501
        :type: float
        """

        self._price_by_hour = price_by_hour

    @property
    def price_by_month(self):
        """Gets the price_by_month of this ItemForListVolumeTypesOutput.  # noqa: E501


        :return: The price_by_month of this ItemForListVolumeTypesOutput.  # noqa: E501
        :rtype: float
        """
        return self._price_by_month

    @price_by_month.setter
    def price_by_month(self, price_by_month):
        """Sets the price_by_month of this ItemForListVolumeTypesOutput.


        :param price_by_month: The price_by_month of this ItemForListVolumeTypesOutput.  # noqa: E501
        :type: float
        """

        self._price_by_month = price_by_month

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListVolumeTypesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListVolumeTypesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListVolumeTypesOutput):
            return True

        return self.to_dict() != other.to_dict()
