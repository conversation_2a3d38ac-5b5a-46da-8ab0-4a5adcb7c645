# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RestoreToCrossRegionInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_list_ids': 'list[str]',
        'auto_storage_scaling_config': 'AutoStorageScalingConfigForRestoreToCrossRegionInstanceInput',
        'backup_id': 'str',
        'charge_info': 'ChargeInfoForRestoreToCrossRegionInstanceInput',
        'db_param_group_id': 'str',
        'deletion_protection': 'str',
        'dst_region_id': 'str',
        'instance_name': 'str',
        'instance_tags': 'list[InstanceTagForRestoreToCrossRegionInstanceInput]',
        'node_info': 'list[NodeInfoForRestoreToCrossRegionInstanceInput]',
        'port': 'int',
        'project_name': 'str',
        'restore_time': 'str',
        'src_region_id': 'str',
        'src_region_instance_id': 'str',
        'storage_space': 'int',
        'storage_type': 'str',
        'subnet_id': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'allow_list_ids': 'AllowListIds',
        'auto_storage_scaling_config': 'AutoStorageScalingConfig',
        'backup_id': 'BackupId',
        'charge_info': 'ChargeInfo',
        'db_param_group_id': 'DBParamGroupId',
        'deletion_protection': 'DeletionProtection',
        'dst_region_id': 'DstRegionId',
        'instance_name': 'InstanceName',
        'instance_tags': 'InstanceTags',
        'node_info': 'NodeInfo',
        'port': 'Port',
        'project_name': 'ProjectName',
        'restore_time': 'RestoreTime',
        'src_region_id': 'SrcRegionId',
        'src_region_instance_id': 'SrcRegionInstanceId',
        'storage_space': 'StorageSpace',
        'storage_type': 'StorageType',
        'subnet_id': 'SubnetId',
        'vpc_id': 'VpcId'
    }

    def __init__(self, allow_list_ids=None, auto_storage_scaling_config=None, backup_id=None, charge_info=None, db_param_group_id=None, deletion_protection=None, dst_region_id=None, instance_name=None, instance_tags=None, node_info=None, port=None, project_name=None, restore_time=None, src_region_id=None, src_region_instance_id=None, storage_space=None, storage_type=None, subnet_id=None, vpc_id=None, _configuration=None):  # noqa: E501
        """RestoreToCrossRegionInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_list_ids = None
        self._auto_storage_scaling_config = None
        self._backup_id = None
        self._charge_info = None
        self._db_param_group_id = None
        self._deletion_protection = None
        self._dst_region_id = None
        self._instance_name = None
        self._instance_tags = None
        self._node_info = None
        self._port = None
        self._project_name = None
        self._restore_time = None
        self._src_region_id = None
        self._src_region_instance_id = None
        self._storage_space = None
        self._storage_type = None
        self._subnet_id = None
        self._vpc_id = None
        self.discriminator = None

        if allow_list_ids is not None:
            self.allow_list_ids = allow_list_ids
        if auto_storage_scaling_config is not None:
            self.auto_storage_scaling_config = auto_storage_scaling_config
        if backup_id is not None:
            self.backup_id = backup_id
        if charge_info is not None:
            self.charge_info = charge_info
        if db_param_group_id is not None:
            self.db_param_group_id = db_param_group_id
        if deletion_protection is not None:
            self.deletion_protection = deletion_protection
        self.dst_region_id = dst_region_id
        if instance_name is not None:
            self.instance_name = instance_name
        if instance_tags is not None:
            self.instance_tags = instance_tags
        if node_info is not None:
            self.node_info = node_info
        if port is not None:
            self.port = port
        if project_name is not None:
            self.project_name = project_name
        if restore_time is not None:
            self.restore_time = restore_time
        self.src_region_id = src_region_id
        self.src_region_instance_id = src_region_instance_id
        if storage_space is not None:
            self.storage_space = storage_space
        self.storage_type = storage_type
        self.subnet_id = subnet_id
        self.vpc_id = vpc_id

    @property
    def allow_list_ids(self):
        """Gets the allow_list_ids of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The allow_list_ids of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._allow_list_ids

    @allow_list_ids.setter
    def allow_list_ids(self, allow_list_ids):
        """Sets the allow_list_ids of this RestoreToCrossRegionInstanceRequest.


        :param allow_list_ids: The allow_list_ids of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: list[str]
        """

        self._allow_list_ids = allow_list_ids

    @property
    def auto_storage_scaling_config(self):
        """Gets the auto_storage_scaling_config of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The auto_storage_scaling_config of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: AutoStorageScalingConfigForRestoreToCrossRegionInstanceInput
        """
        return self._auto_storage_scaling_config

    @auto_storage_scaling_config.setter
    def auto_storage_scaling_config(self, auto_storage_scaling_config):
        """Sets the auto_storage_scaling_config of this RestoreToCrossRegionInstanceRequest.


        :param auto_storage_scaling_config: The auto_storage_scaling_config of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: AutoStorageScalingConfigForRestoreToCrossRegionInstanceInput
        """

        self._auto_storage_scaling_config = auto_storage_scaling_config

    @property
    def backup_id(self):
        """Gets the backup_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The backup_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._backup_id

    @backup_id.setter
    def backup_id(self, backup_id):
        """Sets the backup_id of this RestoreToCrossRegionInstanceRequest.


        :param backup_id: The backup_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """

        self._backup_id = backup_id

    @property
    def charge_info(self):
        """Gets the charge_info of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The charge_info of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: ChargeInfoForRestoreToCrossRegionInstanceInput
        """
        return self._charge_info

    @charge_info.setter
    def charge_info(self, charge_info):
        """Sets the charge_info of this RestoreToCrossRegionInstanceRequest.


        :param charge_info: The charge_info of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: ChargeInfoForRestoreToCrossRegionInstanceInput
        """

        self._charge_info = charge_info

    @property
    def db_param_group_id(self):
        """Gets the db_param_group_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The db_param_group_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_param_group_id

    @db_param_group_id.setter
    def db_param_group_id(self, db_param_group_id):
        """Sets the db_param_group_id of this RestoreToCrossRegionInstanceRequest.


        :param db_param_group_id: The db_param_group_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """

        self._db_param_group_id = db_param_group_id

    @property
    def deletion_protection(self):
        """Gets the deletion_protection of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The deletion_protection of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._deletion_protection

    @deletion_protection.setter
    def deletion_protection(self, deletion_protection):
        """Sets the deletion_protection of this RestoreToCrossRegionInstanceRequest.


        :param deletion_protection: The deletion_protection of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """

        self._deletion_protection = deletion_protection

    @property
    def dst_region_id(self):
        """Gets the dst_region_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The dst_region_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._dst_region_id

    @dst_region_id.setter
    def dst_region_id(self, dst_region_id):
        """Sets the dst_region_id of this RestoreToCrossRegionInstanceRequest.


        :param dst_region_id: The dst_region_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and dst_region_id is None:
            raise ValueError("Invalid value for `dst_region_id`, must not be `None`")  # noqa: E501

        self._dst_region_id = dst_region_id

    @property
    def instance_name(self):
        """Gets the instance_name of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The instance_name of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this RestoreToCrossRegionInstanceRequest.


        :param instance_name: The instance_name of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def instance_tags(self):
        """Gets the instance_tags of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The instance_tags of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: list[InstanceTagForRestoreToCrossRegionInstanceInput]
        """
        return self._instance_tags

    @instance_tags.setter
    def instance_tags(self, instance_tags):
        """Sets the instance_tags of this RestoreToCrossRegionInstanceRequest.


        :param instance_tags: The instance_tags of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: list[InstanceTagForRestoreToCrossRegionInstanceInput]
        """

        self._instance_tags = instance_tags

    @property
    def node_info(self):
        """Gets the node_info of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The node_info of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: list[NodeInfoForRestoreToCrossRegionInstanceInput]
        """
        return self._node_info

    @node_info.setter
    def node_info(self, node_info):
        """Sets the node_info of this RestoreToCrossRegionInstanceRequest.


        :param node_info: The node_info of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: list[NodeInfoForRestoreToCrossRegionInstanceInput]
        """

        self._node_info = node_info

    @property
    def port(self):
        """Gets the port of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The port of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this RestoreToCrossRegionInstanceRequest.


        :param port: The port of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def project_name(self):
        """Gets the project_name of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The project_name of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this RestoreToCrossRegionInstanceRequest.


        :param project_name: The project_name of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def restore_time(self):
        """Gets the restore_time of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The restore_time of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._restore_time

    @restore_time.setter
    def restore_time(self, restore_time):
        """Sets the restore_time of this RestoreToCrossRegionInstanceRequest.


        :param restore_time: The restore_time of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """

        self._restore_time = restore_time

    @property
    def src_region_id(self):
        """Gets the src_region_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The src_region_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._src_region_id

    @src_region_id.setter
    def src_region_id(self, src_region_id):
        """Sets the src_region_id of this RestoreToCrossRegionInstanceRequest.


        :param src_region_id: The src_region_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and src_region_id is None:
            raise ValueError("Invalid value for `src_region_id`, must not be `None`")  # noqa: E501

        self._src_region_id = src_region_id

    @property
    def src_region_instance_id(self):
        """Gets the src_region_instance_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The src_region_instance_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._src_region_instance_id

    @src_region_instance_id.setter
    def src_region_instance_id(self, src_region_instance_id):
        """Sets the src_region_instance_id of this RestoreToCrossRegionInstanceRequest.


        :param src_region_instance_id: The src_region_instance_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and src_region_instance_id is None:
            raise ValueError("Invalid value for `src_region_instance_id`, must not be `None`")  # noqa: E501

        self._src_region_instance_id = src_region_instance_id

    @property
    def storage_space(self):
        """Gets the storage_space of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The storage_space of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this RestoreToCrossRegionInstanceRequest.


        :param storage_space: The storage_space of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: int
        """

        self._storage_space = storage_space

    @property
    def storage_type(self):
        """Gets the storage_type of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The storage_type of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._storage_type

    @storage_type.setter
    def storage_type(self, storage_type):
        """Sets the storage_type of this RestoreToCrossRegionInstanceRequest.


        :param storage_type: The storage_type of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and storage_type is None:
            raise ValueError("Invalid value for `storage_type`, must not be `None`")  # noqa: E501

        self._storage_type = storage_type

    @property
    def subnet_id(self):
        """Gets the subnet_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The subnet_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this RestoreToCrossRegionInstanceRequest.


        :param subnet_id: The subnet_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def vpc_id(self):
        """Gets the vpc_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501


        :return: The vpc_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this RestoreToCrossRegionInstanceRequest.


        :param vpc_id: The vpc_id of this RestoreToCrossRegionInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RestoreToCrossRegionInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RestoreToCrossRegionInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RestoreToCrossRegionInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
