# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetRaspProcessDetailResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'cmdline': 'str',
        'environ_list': 'list[EnvironListForGetRaspProcessDetailOutput]',
        'exe_name': 'str',
        'exe_path': 'str',
        'inspect_port': 'int',
        'pid': 'str',
        'process_state': 'str',
        'protect_time': 'int',
        'reason': 'str',
        'relate_rasp_config_name': 'str',
        'related_config_id': 'str',
        'runtime': 'str',
        'runtime_version': 'str',
        'start_time_unix': 'int',
        'status': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'cmdline': 'Cmdline',
        'environ_list': 'EnvironList',
        'exe_name': 'ExeName',
        'exe_path': 'ExePath',
        'inspect_port': 'InspectPort',
        'pid': 'Pid',
        'process_state': 'ProcessState',
        'protect_time': 'ProtectTime',
        'reason': 'Reason',
        'relate_rasp_config_name': 'RelateRaspConfigName',
        'related_config_id': 'RelatedConfigID',
        'runtime': 'Runtime',
        'runtime_version': 'RuntimeVersion',
        'start_time_unix': 'StartTimeUnix',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, agent_id=None, cmdline=None, environ_list=None, exe_name=None, exe_path=None, inspect_port=None, pid=None, process_state=None, protect_time=None, reason=None, relate_rasp_config_name=None, related_config_id=None, runtime=None, runtime_version=None, start_time_unix=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """GetRaspProcessDetailResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._cmdline = None
        self._environ_list = None
        self._exe_name = None
        self._exe_path = None
        self._inspect_port = None
        self._pid = None
        self._process_state = None
        self._protect_time = None
        self._reason = None
        self._relate_rasp_config_name = None
        self._related_config_id = None
        self._runtime = None
        self._runtime_version = None
        self._start_time_unix = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if cmdline is not None:
            self.cmdline = cmdline
        if environ_list is not None:
            self.environ_list = environ_list
        if exe_name is not None:
            self.exe_name = exe_name
        if exe_path is not None:
            self.exe_path = exe_path
        if inspect_port is not None:
            self.inspect_port = inspect_port
        if pid is not None:
            self.pid = pid
        if process_state is not None:
            self.process_state = process_state
        if protect_time is not None:
            self.protect_time = protect_time
        if reason is not None:
            self.reason = reason
        if relate_rasp_config_name is not None:
            self.relate_rasp_config_name = relate_rasp_config_name
        if related_config_id is not None:
            self.related_config_id = related_config_id
        if runtime is not None:
            self.runtime = runtime
        if runtime_version is not None:
            self.runtime_version = runtime_version
        if start_time_unix is not None:
            self.start_time_unix = start_time_unix
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def agent_id(self):
        """Gets the agent_id of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The agent_id of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this GetRaspProcessDetailResponse.


        :param agent_id: The agent_id of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def cmdline(self):
        """Gets the cmdline of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The cmdline of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this GetRaspProcessDetailResponse.


        :param cmdline: The cmdline of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def environ_list(self):
        """Gets the environ_list of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The environ_list of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: list[EnvironListForGetRaspProcessDetailOutput]
        """
        return self._environ_list

    @environ_list.setter
    def environ_list(self, environ_list):
        """Sets the environ_list of this GetRaspProcessDetailResponse.


        :param environ_list: The environ_list of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: list[EnvironListForGetRaspProcessDetailOutput]
        """

        self._environ_list = environ_list

    @property
    def exe_name(self):
        """Gets the exe_name of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The exe_name of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._exe_name

    @exe_name.setter
    def exe_name(self, exe_name):
        """Sets the exe_name of this GetRaspProcessDetailResponse.


        :param exe_name: The exe_name of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._exe_name = exe_name

    @property
    def exe_path(self):
        """Gets the exe_path of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The exe_path of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._exe_path

    @exe_path.setter
    def exe_path(self, exe_path):
        """Sets the exe_path of this GetRaspProcessDetailResponse.


        :param exe_path: The exe_path of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._exe_path = exe_path

    @property
    def inspect_port(self):
        """Gets the inspect_port of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The inspect_port of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: int
        """
        return self._inspect_port

    @inspect_port.setter
    def inspect_port(self, inspect_port):
        """Sets the inspect_port of this GetRaspProcessDetailResponse.


        :param inspect_port: The inspect_port of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: int
        """

        self._inspect_port = inspect_port

    @property
    def pid(self):
        """Gets the pid of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The pid of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this GetRaspProcessDetailResponse.


        :param pid: The pid of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def process_state(self):
        """Gets the process_state of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The process_state of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._process_state

    @process_state.setter
    def process_state(self, process_state):
        """Sets the process_state of this GetRaspProcessDetailResponse.


        :param process_state: The process_state of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._process_state = process_state

    @property
    def protect_time(self):
        """Gets the protect_time of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The protect_time of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: int
        """
        return self._protect_time

    @protect_time.setter
    def protect_time(self, protect_time):
        """Sets the protect_time of this GetRaspProcessDetailResponse.


        :param protect_time: The protect_time of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: int
        """

        self._protect_time = protect_time

    @property
    def reason(self):
        """Gets the reason of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The reason of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this GetRaspProcessDetailResponse.


        :param reason: The reason of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._reason = reason

    @property
    def relate_rasp_config_name(self):
        """Gets the relate_rasp_config_name of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The relate_rasp_config_name of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._relate_rasp_config_name

    @relate_rasp_config_name.setter
    def relate_rasp_config_name(self, relate_rasp_config_name):
        """Sets the relate_rasp_config_name of this GetRaspProcessDetailResponse.


        :param relate_rasp_config_name: The relate_rasp_config_name of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._relate_rasp_config_name = relate_rasp_config_name

    @property
    def related_config_id(self):
        """Gets the related_config_id of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The related_config_id of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._related_config_id

    @related_config_id.setter
    def related_config_id(self, related_config_id):
        """Sets the related_config_id of this GetRaspProcessDetailResponse.


        :param related_config_id: The related_config_id of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._related_config_id = related_config_id

    @property
    def runtime(self):
        """Gets the runtime of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The runtime of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._runtime

    @runtime.setter
    def runtime(self, runtime):
        """Sets the runtime of this GetRaspProcessDetailResponse.


        :param runtime: The runtime of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._runtime = runtime

    @property
    def runtime_version(self):
        """Gets the runtime_version of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The runtime_version of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._runtime_version

    @runtime_version.setter
    def runtime_version(self, runtime_version):
        """Sets the runtime_version of this GetRaspProcessDetailResponse.


        :param runtime_version: The runtime_version of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._runtime_version = runtime_version

    @property
    def start_time_unix(self):
        """Gets the start_time_unix of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The start_time_unix of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: int
        """
        return self._start_time_unix

    @start_time_unix.setter
    def start_time_unix(self, start_time_unix):
        """Sets the start_time_unix of this GetRaspProcessDetailResponse.


        :param start_time_unix: The start_time_unix of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: int
        """

        self._start_time_unix = start_time_unix

    @property
    def status(self):
        """Gets the status of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The status of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetRaspProcessDetailResponse.


        :param status: The status of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this GetRaspProcessDetailResponse.  # noqa: E501


        :return: The update_time of this GetRaspProcessDetailResponse.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetRaspProcessDetailResponse.


        :param update_time: The update_time of this GetRaspProcessDetailResponse.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetRaspProcessDetailResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetRaspProcessDetailResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetRaspProcessDetailResponse):
            return True

        return self.to_dict() != other.to_dict()
