# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDrDBInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_storage_scaling_config': 'AutoStorageScalingConfigForCreateDrDBInstanceInput',
        'deletion_protection': 'str',
        'instance_name': 'str',
        'master_instance_id': 'str',
        'master_region': 'str',
        'node_info': 'list[NodeInfoForCreateDrDBInstanceInput]',
        'storage_space': 'int',
        'subnet_id': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'auto_storage_scaling_config': 'AutoStorageScalingConfig',
        'deletion_protection': 'DeletionProtection',
        'instance_name': 'InstanceName',
        'master_instance_id': 'MasterInstanceId',
        'master_region': 'MasterRegion',
        'node_info': 'NodeInfo',
        'storage_space': 'StorageSpace',
        'subnet_id': 'SubnetId',
        'vpc_id': 'VpcId'
    }

    def __init__(self, auto_storage_scaling_config=None, deletion_protection=None, instance_name=None, master_instance_id=None, master_region=None, node_info=None, storage_space=None, subnet_id=None, vpc_id=None, _configuration=None):  # noqa: E501
        """CreateDrDBInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_storage_scaling_config = None
        self._deletion_protection = None
        self._instance_name = None
        self._master_instance_id = None
        self._master_region = None
        self._node_info = None
        self._storage_space = None
        self._subnet_id = None
        self._vpc_id = None
        self.discriminator = None

        if auto_storage_scaling_config is not None:
            self.auto_storage_scaling_config = auto_storage_scaling_config
        if deletion_protection is not None:
            self.deletion_protection = deletion_protection
        if instance_name is not None:
            self.instance_name = instance_name
        self.master_instance_id = master_instance_id
        self.master_region = master_region
        if node_info is not None:
            self.node_info = node_info
        self.storage_space = storage_space
        self.subnet_id = subnet_id
        self.vpc_id = vpc_id

    @property
    def auto_storage_scaling_config(self):
        """Gets the auto_storage_scaling_config of this CreateDrDBInstanceRequest.  # noqa: E501


        :return: The auto_storage_scaling_config of this CreateDrDBInstanceRequest.  # noqa: E501
        :rtype: AutoStorageScalingConfigForCreateDrDBInstanceInput
        """
        return self._auto_storage_scaling_config

    @auto_storage_scaling_config.setter
    def auto_storage_scaling_config(self, auto_storage_scaling_config):
        """Sets the auto_storage_scaling_config of this CreateDrDBInstanceRequest.


        :param auto_storage_scaling_config: The auto_storage_scaling_config of this CreateDrDBInstanceRequest.  # noqa: E501
        :type: AutoStorageScalingConfigForCreateDrDBInstanceInput
        """

        self._auto_storage_scaling_config = auto_storage_scaling_config

    @property
    def deletion_protection(self):
        """Gets the deletion_protection of this CreateDrDBInstanceRequest.  # noqa: E501


        :return: The deletion_protection of this CreateDrDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._deletion_protection

    @deletion_protection.setter
    def deletion_protection(self, deletion_protection):
        """Sets the deletion_protection of this CreateDrDBInstanceRequest.


        :param deletion_protection: The deletion_protection of this CreateDrDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._deletion_protection = deletion_protection

    @property
    def instance_name(self):
        """Gets the instance_name of this CreateDrDBInstanceRequest.  # noqa: E501


        :return: The instance_name of this CreateDrDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this CreateDrDBInstanceRequest.


        :param instance_name: The instance_name of this CreateDrDBInstanceRequest.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def master_instance_id(self):
        """Gets the master_instance_id of this CreateDrDBInstanceRequest.  # noqa: E501


        :return: The master_instance_id of this CreateDrDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._master_instance_id

    @master_instance_id.setter
    def master_instance_id(self, master_instance_id):
        """Sets the master_instance_id of this CreateDrDBInstanceRequest.


        :param master_instance_id: The master_instance_id of this CreateDrDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and master_instance_id is None:
            raise ValueError("Invalid value for `master_instance_id`, must not be `None`")  # noqa: E501

        self._master_instance_id = master_instance_id

    @property
    def master_region(self):
        """Gets the master_region of this CreateDrDBInstanceRequest.  # noqa: E501


        :return: The master_region of this CreateDrDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._master_region

    @master_region.setter
    def master_region(self, master_region):
        """Sets the master_region of this CreateDrDBInstanceRequest.


        :param master_region: The master_region of this CreateDrDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and master_region is None:
            raise ValueError("Invalid value for `master_region`, must not be `None`")  # noqa: E501

        self._master_region = master_region

    @property
    def node_info(self):
        """Gets the node_info of this CreateDrDBInstanceRequest.  # noqa: E501


        :return: The node_info of this CreateDrDBInstanceRequest.  # noqa: E501
        :rtype: list[NodeInfoForCreateDrDBInstanceInput]
        """
        return self._node_info

    @node_info.setter
    def node_info(self, node_info):
        """Sets the node_info of this CreateDrDBInstanceRequest.


        :param node_info: The node_info of this CreateDrDBInstanceRequest.  # noqa: E501
        :type: list[NodeInfoForCreateDrDBInstanceInput]
        """

        self._node_info = node_info

    @property
    def storage_space(self):
        """Gets the storage_space of this CreateDrDBInstanceRequest.  # noqa: E501


        :return: The storage_space of this CreateDrDBInstanceRequest.  # noqa: E501
        :rtype: int
        """
        return self._storage_space

    @storage_space.setter
    def storage_space(self, storage_space):
        """Sets the storage_space of this CreateDrDBInstanceRequest.


        :param storage_space: The storage_space of this CreateDrDBInstanceRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and storage_space is None:
            raise ValueError("Invalid value for `storage_space`, must not be `None`")  # noqa: E501

        self._storage_space = storage_space

    @property
    def subnet_id(self):
        """Gets the subnet_id of this CreateDrDBInstanceRequest.  # noqa: E501


        :return: The subnet_id of this CreateDrDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this CreateDrDBInstanceRequest.


        :param subnet_id: The subnet_id of this CreateDrDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and subnet_id is None:
            raise ValueError("Invalid value for `subnet_id`, must not be `None`")  # noqa: E501

        self._subnet_id = subnet_id

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateDrDBInstanceRequest.  # noqa: E501


        :return: The vpc_id of this CreateDrDBInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateDrDBInstanceRequest.


        :param vpc_id: The vpc_id of this CreateDrDBInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDrDBInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDrDBInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDrDBInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
