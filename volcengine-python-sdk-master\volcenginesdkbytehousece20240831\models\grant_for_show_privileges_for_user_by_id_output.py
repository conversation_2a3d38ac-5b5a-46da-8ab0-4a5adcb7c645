# coding: utf-8

"""
    bytehouse_ce20240831

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GrantForShowPrivilegesForUserByIDOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'action': 'str',
        'cluster_id': 'int',
        'cluster_name': 'str',
        'database_name': 'str',
        'privileges': 'list[PrivilegeForShowPrivilegesForUserByIDOutput]',
        'table_name': 'str'
    }

    attribute_map = {
        'action': 'Action',
        'cluster_id': 'ClusterID',
        'cluster_name': 'ClusterName',
        'database_name': 'DatabaseName',
        'privileges': 'Privileges',
        'table_name': 'TableName'
    }

    def __init__(self, action=None, cluster_id=None, cluster_name=None, database_name=None, privileges=None, table_name=None, _configuration=None):  # noqa: E501
        """GrantForShowPrivilegesForUserByIDOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._action = None
        self._cluster_id = None
        self._cluster_name = None
        self._database_name = None
        self._privileges = None
        self._table_name = None
        self.discriminator = None

        if action is not None:
            self.action = action
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if database_name is not None:
            self.database_name = database_name
        if privileges is not None:
            self.privileges = privileges
        if table_name is not None:
            self.table_name = table_name

    @property
    def action(self):
        """Gets the action of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501


        :return: The action of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :rtype: str
        """
        return self._action

    @action.setter
    def action(self, action):
        """Sets the action of this GrantForShowPrivilegesForUserByIDOutput.


        :param action: The action of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :type: str
        """

        self._action = action

    @property
    def cluster_id(self):
        """Gets the cluster_id of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501


        :return: The cluster_id of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :rtype: int
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this GrantForShowPrivilegesForUserByIDOutput.


        :param cluster_id: The cluster_id of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :type: int
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501


        :return: The cluster_name of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this GrantForShowPrivilegesForUserByIDOutput.


        :param cluster_name: The cluster_name of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def database_name(self):
        """Gets the database_name of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501


        :return: The database_name of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :rtype: str
        """
        return self._database_name

    @database_name.setter
    def database_name(self, database_name):
        """Sets the database_name of this GrantForShowPrivilegesForUserByIDOutput.


        :param database_name: The database_name of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :type: str
        """

        self._database_name = database_name

    @property
    def privileges(self):
        """Gets the privileges of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501


        :return: The privileges of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :rtype: list[PrivilegeForShowPrivilegesForUserByIDOutput]
        """
        return self._privileges

    @privileges.setter
    def privileges(self, privileges):
        """Sets the privileges of this GrantForShowPrivilegesForUserByIDOutput.


        :param privileges: The privileges of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :type: list[PrivilegeForShowPrivilegesForUserByIDOutput]
        """

        self._privileges = privileges

    @property
    def table_name(self):
        """Gets the table_name of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501


        :return: The table_name of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :rtype: str
        """
        return self._table_name

    @table_name.setter
    def table_name(self, table_name):
        """Sets the table_name of this GrantForShowPrivilegesForUserByIDOutput.


        :param table_name: The table_name of this GrantForShowPrivilegesForUserByIDOutput.  # noqa: E501
        :type: str
        """

        self._table_name = table_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GrantForShowPrivilegesForUserByIDOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GrantForShowPrivilegesForUserByIDOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GrantForShowPrivilegesForUserByIDOutput):
            return True

        return self.to_dict() != other.to_dict()
