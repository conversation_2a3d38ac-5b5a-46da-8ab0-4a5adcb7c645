# coding: utf-8

"""
    advdefence

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescAtkAlarmThresholdResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'band_width': 'int',
        'default_band_width': 'str',
        'default_pps': 'str',
        'pps': 'int',
        'thres_type': 'int'
    }

    attribute_map = {
        'band_width': 'BandWidth',
        'default_band_width': 'DefaultBandWidth',
        'default_pps': 'DefaultPps',
        'pps': 'Pps',
        'thres_type': 'ThresType'
    }

    def __init__(self, band_width=None, default_band_width=None, default_pps=None, pps=None, thres_type=None, _configuration=None):  # noqa: E501
        """DescAtkAlarmThresholdResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._band_width = None
        self._default_band_width = None
        self._default_pps = None
        self._pps = None
        self._thres_type = None
        self.discriminator = None

        if band_width is not None:
            self.band_width = band_width
        if default_band_width is not None:
            self.default_band_width = default_band_width
        if default_pps is not None:
            self.default_pps = default_pps
        if pps is not None:
            self.pps = pps
        if thres_type is not None:
            self.thres_type = thres_type

    @property
    def band_width(self):
        """Gets the band_width of this DescAtkAlarmThresholdResponse.  # noqa: E501


        :return: The band_width of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :rtype: int
        """
        return self._band_width

    @band_width.setter
    def band_width(self, band_width):
        """Sets the band_width of this DescAtkAlarmThresholdResponse.


        :param band_width: The band_width of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :type: int
        """

        self._band_width = band_width

    @property
    def default_band_width(self):
        """Gets the default_band_width of this DescAtkAlarmThresholdResponse.  # noqa: E501


        :return: The default_band_width of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :rtype: str
        """
        return self._default_band_width

    @default_band_width.setter
    def default_band_width(self, default_band_width):
        """Sets the default_band_width of this DescAtkAlarmThresholdResponse.


        :param default_band_width: The default_band_width of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :type: str
        """

        self._default_band_width = default_band_width

    @property
    def default_pps(self):
        """Gets the default_pps of this DescAtkAlarmThresholdResponse.  # noqa: E501


        :return: The default_pps of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :rtype: str
        """
        return self._default_pps

    @default_pps.setter
    def default_pps(self, default_pps):
        """Sets the default_pps of this DescAtkAlarmThresholdResponse.


        :param default_pps: The default_pps of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :type: str
        """

        self._default_pps = default_pps

    @property
    def pps(self):
        """Gets the pps of this DescAtkAlarmThresholdResponse.  # noqa: E501


        :return: The pps of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :rtype: int
        """
        return self._pps

    @pps.setter
    def pps(self, pps):
        """Sets the pps of this DescAtkAlarmThresholdResponse.


        :param pps: The pps of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :type: int
        """

        self._pps = pps

    @property
    def thres_type(self):
        """Gets the thres_type of this DescAtkAlarmThresholdResponse.  # noqa: E501


        :return: The thres_type of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :rtype: int
        """
        return self._thres_type

    @thres_type.setter
    def thres_type(self, thres_type):
        """Sets the thres_type of this DescAtkAlarmThresholdResponse.


        :param thres_type: The thres_type of this DescAtkAlarmThresholdResponse.  # noqa: E501
        :type: int
        """

        self._thres_type = thres_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescAtkAlarmThresholdResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescAtkAlarmThresholdResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescAtkAlarmThresholdResponse):
            return True

        return self.to_dict() != other.to_dict()
