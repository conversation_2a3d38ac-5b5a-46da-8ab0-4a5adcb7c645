# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateEDXPeerLinkResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'peer_link_id': 'str',
        'support_forced': 'float',
        'vgw_route_conflicts': 'list[VGWRouteConflictForCreateEDXPeerLinkOutput]'
    }

    attribute_map = {
        'peer_link_id': 'PeerLinkId',
        'support_forced': 'SupportForced',
        'vgw_route_conflicts': 'VGWRouteConflicts'
    }

    def __init__(self, peer_link_id=None, support_forced=None, vgw_route_conflicts=None, _configuration=None):  # noqa: E501
        """CreateEDXPeerLinkResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._peer_link_id = None
        self._support_forced = None
        self._vgw_route_conflicts = None
        self.discriminator = None

        if peer_link_id is not None:
            self.peer_link_id = peer_link_id
        if support_forced is not None:
            self.support_forced = support_forced
        if vgw_route_conflicts is not None:
            self.vgw_route_conflicts = vgw_route_conflicts

    @property
    def peer_link_id(self):
        """Gets the peer_link_id of this CreateEDXPeerLinkResponse.  # noqa: E501


        :return: The peer_link_id of this CreateEDXPeerLinkResponse.  # noqa: E501
        :rtype: str
        """
        return self._peer_link_id

    @peer_link_id.setter
    def peer_link_id(self, peer_link_id):
        """Sets the peer_link_id of this CreateEDXPeerLinkResponse.


        :param peer_link_id: The peer_link_id of this CreateEDXPeerLinkResponse.  # noqa: E501
        :type: str
        """

        self._peer_link_id = peer_link_id

    @property
    def support_forced(self):
        """Gets the support_forced of this CreateEDXPeerLinkResponse.  # noqa: E501


        :return: The support_forced of this CreateEDXPeerLinkResponse.  # noqa: E501
        :rtype: float
        """
        return self._support_forced

    @support_forced.setter
    def support_forced(self, support_forced):
        """Sets the support_forced of this CreateEDXPeerLinkResponse.


        :param support_forced: The support_forced of this CreateEDXPeerLinkResponse.  # noqa: E501
        :type: float
        """

        self._support_forced = support_forced

    @property
    def vgw_route_conflicts(self):
        """Gets the vgw_route_conflicts of this CreateEDXPeerLinkResponse.  # noqa: E501


        :return: The vgw_route_conflicts of this CreateEDXPeerLinkResponse.  # noqa: E501
        :rtype: list[VGWRouteConflictForCreateEDXPeerLinkOutput]
        """
        return self._vgw_route_conflicts

    @vgw_route_conflicts.setter
    def vgw_route_conflicts(self, vgw_route_conflicts):
        """Sets the vgw_route_conflicts of this CreateEDXPeerLinkResponse.


        :param vgw_route_conflicts: The vgw_route_conflicts of this CreateEDXPeerLinkResponse.  # noqa: E501
        :type: list[VGWRouteConflictForCreateEDXPeerLinkOutput]
        """

        self._vgw_route_conflicts = vgw_route_conflicts

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateEDXPeerLinkResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateEDXPeerLinkResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateEDXPeerLinkResponse):
            return True

        return self.to_dict() != other.to_dict()
