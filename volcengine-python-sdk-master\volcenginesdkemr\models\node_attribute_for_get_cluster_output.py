# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeAttributeForGetClusterOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'ecs_iam_role': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'ecs_iam_role': 'EcsIamRole',
        'zone_id': 'ZoneId'
    }

    def __init__(self, ecs_iam_role=None, zone_id=None, _configuration=None):  # noqa: E501
        """NodeAttributeForGetClusterOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._ecs_iam_role = None
        self._zone_id = None
        self.discriminator = None

        if ecs_iam_role is not None:
            self.ecs_iam_role = ecs_iam_role
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def ecs_iam_role(self):
        """Gets the ecs_iam_role of this NodeAttributeForGetClusterOutput.  # noqa: E501


        :return: The ecs_iam_role of this NodeAttributeForGetClusterOutput.  # noqa: E501
        :rtype: str
        """
        return self._ecs_iam_role

    @ecs_iam_role.setter
    def ecs_iam_role(self, ecs_iam_role):
        """Sets the ecs_iam_role of this NodeAttributeForGetClusterOutput.


        :param ecs_iam_role: The ecs_iam_role of this NodeAttributeForGetClusterOutput.  # noqa: E501
        :type: str
        """

        self._ecs_iam_role = ecs_iam_role

    @property
    def zone_id(self):
        """Gets the zone_id of this NodeAttributeForGetClusterOutput.  # noqa: E501


        :return: The zone_id of this NodeAttributeForGetClusterOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this NodeAttributeForGetClusterOutput.


        :param zone_id: The zone_id of this NodeAttributeForGetClusterOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeAttributeForGetClusterOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeAttributeForGetClusterOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeAttributeForGetClusterOutput):
            return True

        return self.to_dict() != other.to_dict()
