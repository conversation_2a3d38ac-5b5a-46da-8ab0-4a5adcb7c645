# coding: utf-8

"""
    resourcecenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetResourceCountsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'filter': 'list[FilterForGetResourceCountsInput]',
        'group_by_key': 'str'
    }

    attribute_map = {
        'filter': 'Filter',
        'group_by_key': 'GroupByKey'
    }

    def __init__(self, filter=None, group_by_key=None, _configuration=None):  # noqa: E501
        """GetResourceCountsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._filter = None
        self._group_by_key = None
        self.discriminator = None

        if filter is not None:
            self.filter = filter
        self.group_by_key = group_by_key

    @property
    def filter(self):
        """Gets the filter of this GetResourceCountsRequest.  # noqa: E501


        :return: The filter of this GetResourceCountsRequest.  # noqa: E501
        :rtype: list[FilterForGetResourceCountsInput]
        """
        return self._filter

    @filter.setter
    def filter(self, filter):
        """Sets the filter of this GetResourceCountsRequest.


        :param filter: The filter of this GetResourceCountsRequest.  # noqa: E501
        :type: list[FilterForGetResourceCountsInput]
        """

        self._filter = filter

    @property
    def group_by_key(self):
        """Gets the group_by_key of this GetResourceCountsRequest.  # noqa: E501


        :return: The group_by_key of this GetResourceCountsRequest.  # noqa: E501
        :rtype: str
        """
        return self._group_by_key

    @group_by_key.setter
    def group_by_key(self, group_by_key):
        """Sets the group_by_key of this GetResourceCountsRequest.


        :param group_by_key: The group_by_key of this GetResourceCountsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and group_by_key is None:
            raise ValueError("Invalid value for `group_by_key`, must not be `None`")  # noqa: E501
        allowed_values = ["ResourceType", "Region"]  # noqa: E501
        if (self._configuration.client_side_validation and
                group_by_key not in allowed_values):
            raise ValueError(
                "Invalid value for `group_by_key` ({0}), must be one of {1}"  # noqa: E501
                .format(group_by_key, allowed_values)
            )

        self._group_by_key = group_by_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetResourceCountsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetResourceCountsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetResourceCountsRequest):
            return True

        return self.to_dict() != other.to_dict()
