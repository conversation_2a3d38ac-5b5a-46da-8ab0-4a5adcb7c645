# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SecurityPolicyForDescribeNLBSecurityPoliciesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'ciphers': 'list[str]',
        'create_time': 'str',
        'project_name': 'str',
        'related_listener_ids': 'list[str]',
        'security_policy_id': 'str',
        'security_policy_name': 'str',
        'status': 'str',
        'tags': 'list[TagForDescribeNLBSecurityPoliciesOutput]',
        'tls_versions': 'list[str]',
        'update_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'ciphers': 'Ciphers',
        'create_time': 'CreateTime',
        'project_name': 'ProjectName',
        'related_listener_ids': 'RelatedListenerIds',
        'security_policy_id': 'SecurityPolicyId',
        'security_policy_name': 'SecurityPolicyName',
        'status': 'Status',
        'tags': 'Tags',
        'tls_versions': 'TlsVersions',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, ciphers=None, create_time=None, project_name=None, related_listener_ids=None, security_policy_id=None, security_policy_name=None, status=None, tags=None, tls_versions=None, update_time=None, _configuration=None):  # noqa: E501
        """SecurityPolicyForDescribeNLBSecurityPoliciesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._ciphers = None
        self._create_time = None
        self._project_name = None
        self._related_listener_ids = None
        self._security_policy_id = None
        self._security_policy_name = None
        self._status = None
        self._tags = None
        self._tls_versions = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if ciphers is not None:
            self.ciphers = ciphers
        if create_time is not None:
            self.create_time = create_time
        if project_name is not None:
            self.project_name = project_name
        if related_listener_ids is not None:
            self.related_listener_ids = related_listener_ids
        if security_policy_id is not None:
            self.security_policy_id = security_policy_id
        if security_policy_name is not None:
            self.security_policy_name = security_policy_name
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if tls_versions is not None:
            self.tls_versions = tls_versions
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The account_id of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param account_id: The account_id of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def ciphers(self):
        """Gets the ciphers of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The ciphers of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ciphers

    @ciphers.setter
    def ciphers(self, ciphers):
        """Sets the ciphers of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param ciphers: The ciphers of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: list[str]
        """

        self._ciphers = ciphers

    @property
    def create_time(self):
        """Gets the create_time of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The create_time of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param create_time: The create_time of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def project_name(self):
        """Gets the project_name of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The project_name of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param project_name: The project_name of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def related_listener_ids(self):
        """Gets the related_listener_ids of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The related_listener_ids of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._related_listener_ids

    @related_listener_ids.setter
    def related_listener_ids(self, related_listener_ids):
        """Sets the related_listener_ids of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param related_listener_ids: The related_listener_ids of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: list[str]
        """

        self._related_listener_ids = related_listener_ids

    @property
    def security_policy_id(self):
        """Gets the security_policy_id of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The security_policy_id of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._security_policy_id

    @security_policy_id.setter
    def security_policy_id(self, security_policy_id):
        """Sets the security_policy_id of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param security_policy_id: The security_policy_id of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._security_policy_id = security_policy_id

    @property
    def security_policy_name(self):
        """Gets the security_policy_name of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The security_policy_name of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._security_policy_name

    @security_policy_name.setter
    def security_policy_name(self, security_policy_name):
        """Sets the security_policy_name of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param security_policy_name: The security_policy_name of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._security_policy_name = security_policy_name

    @property
    def status(self):
        """Gets the status of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The status of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param status: The status of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The tags of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: list[TagForDescribeNLBSecurityPoliciesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param tags: The tags of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: list[TagForDescribeNLBSecurityPoliciesOutput]
        """

        self._tags = tags

    @property
    def tls_versions(self):
        """Gets the tls_versions of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The tls_versions of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tls_versions

    @tls_versions.setter
    def tls_versions(self, tls_versions):
        """Sets the tls_versions of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param tls_versions: The tls_versions of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: list[str]
        """

        self._tls_versions = tls_versions

    @property
    def update_time(self):
        """Gets the update_time of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501


        :return: The update_time of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.


        :param update_time: The update_time of this SecurityPolicyForDescribeNLBSecurityPoliciesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SecurityPolicyForDescribeNLBSecurityPoliciesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SecurityPolicyForDescribeNLBSecurityPoliciesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SecurityPolicyForDescribeNLBSecurityPoliciesOutput):
            return True

        return self.to_dict() != other.to_dict()
