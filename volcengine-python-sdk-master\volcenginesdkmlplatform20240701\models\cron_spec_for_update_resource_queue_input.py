# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CronSpecForUpdateResourceQueueInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affected_workload_type': 'str',
        'schedule': 'str',
        'time_zone': 'str'
    }

    attribute_map = {
        'affected_workload_type': 'AffectedWorkloadType',
        'schedule': 'Schedule',
        'time_zone': 'TimeZone'
    }

    def __init__(self, affected_workload_type=None, schedule=None, time_zone=None, _configuration=None):  # noqa: E501
        """CronSpecForUpdateResourceQueueInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affected_workload_type = None
        self._schedule = None
        self._time_zone = None
        self.discriminator = None

        if affected_workload_type is not None:
            self.affected_workload_type = affected_workload_type
        if schedule is not None:
            self.schedule = schedule
        if time_zone is not None:
            self.time_zone = time_zone

    @property
    def affected_workload_type(self):
        """Gets the affected_workload_type of this CronSpecForUpdateResourceQueueInput.  # noqa: E501


        :return: The affected_workload_type of this CronSpecForUpdateResourceQueueInput.  # noqa: E501
        :rtype: str
        """
        return self._affected_workload_type

    @affected_workload_type.setter
    def affected_workload_type(self, affected_workload_type):
        """Sets the affected_workload_type of this CronSpecForUpdateResourceQueueInput.


        :param affected_workload_type: The affected_workload_type of this CronSpecForUpdateResourceQueueInput.  # noqa: E501
        :type: str
        """

        self._affected_workload_type = affected_workload_type

    @property
    def schedule(self):
        """Gets the schedule of this CronSpecForUpdateResourceQueueInput.  # noqa: E501


        :return: The schedule of this CronSpecForUpdateResourceQueueInput.  # noqa: E501
        :rtype: str
        """
        return self._schedule

    @schedule.setter
    def schedule(self, schedule):
        """Sets the schedule of this CronSpecForUpdateResourceQueueInput.


        :param schedule: The schedule of this CronSpecForUpdateResourceQueueInput.  # noqa: E501
        :type: str
        """

        self._schedule = schedule

    @property
    def time_zone(self):
        """Gets the time_zone of this CronSpecForUpdateResourceQueueInput.  # noqa: E501


        :return: The time_zone of this CronSpecForUpdateResourceQueueInput.  # noqa: E501
        :rtype: str
        """
        return self._time_zone

    @time_zone.setter
    def time_zone(self, time_zone):
        """Sets the time_zone of this CronSpecForUpdateResourceQueueInput.


        :param time_zone: The time_zone of this CronSpecForUpdateResourceQueueInput.  # noqa: E501
        :type: str
        """

        self._time_zone = time_zone

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CronSpecForUpdateResourceQueueInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CronSpecForUpdateResourceQueueInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CronSpecForUpdateResourceQueueInput):
            return True

        return self.to_dict() != other.to_dict()
