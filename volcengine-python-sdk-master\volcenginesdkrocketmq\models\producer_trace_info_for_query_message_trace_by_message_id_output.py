# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProducerTraceInfoForQueryMessageTraceByMessageIdOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'message_born_host': 'str',
        'message_born_timestamp': 'int',
        'send_cost_time_ms': 'int',
        'send_status': 'str'
    }

    attribute_map = {
        'message_born_host': 'MessageBornHost',
        'message_born_timestamp': 'MessageBornTimestamp',
        'send_cost_time_ms': 'SendCostTimeMs',
        'send_status': 'SendStatus'
    }

    def __init__(self, message_born_host=None, message_born_timestamp=None, send_cost_time_ms=None, send_status=None, _configuration=None):  # noqa: E501
        """ProducerTraceInfoForQueryMessageTraceByMessageIdOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._message_born_host = None
        self._message_born_timestamp = None
        self._send_cost_time_ms = None
        self._send_status = None
        self.discriminator = None

        if message_born_host is not None:
            self.message_born_host = message_born_host
        if message_born_timestamp is not None:
            self.message_born_timestamp = message_born_timestamp
        if send_cost_time_ms is not None:
            self.send_cost_time_ms = send_cost_time_ms
        if send_status is not None:
            self.send_status = send_status

    @property
    def message_born_host(self):
        """Gets the message_born_host of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The message_born_host of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: str
        """
        return self._message_born_host

    @message_born_host.setter
    def message_born_host(self, message_born_host):
        """Sets the message_born_host of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param message_born_host: The message_born_host of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: str
        """

        self._message_born_host = message_born_host

    @property
    def message_born_timestamp(self):
        """Gets the message_born_timestamp of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The message_born_timestamp of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: int
        """
        return self._message_born_timestamp

    @message_born_timestamp.setter
    def message_born_timestamp(self, message_born_timestamp):
        """Sets the message_born_timestamp of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param message_born_timestamp: The message_born_timestamp of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: int
        """

        self._message_born_timestamp = message_born_timestamp

    @property
    def send_cost_time_ms(self):
        """Gets the send_cost_time_ms of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The send_cost_time_ms of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_cost_time_ms

    @send_cost_time_ms.setter
    def send_cost_time_ms(self, send_cost_time_ms):
        """Sets the send_cost_time_ms of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param send_cost_time_ms: The send_cost_time_ms of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: int
        """

        self._send_cost_time_ms = send_cost_time_ms

    @property
    def send_status(self):
        """Gets the send_status of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501


        :return: The send_status of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :rtype: str
        """
        return self._send_status

    @send_status.setter
    def send_status(self, send_status):
        """Sets the send_status of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.


        :param send_status: The send_status of this ProducerTraceInfoForQueryMessageTraceByMessageIdOutput.  # noqa: E501
        :type: str
        """

        self._send_status = send_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProducerTraceInfoForQueryMessageTraceByMessageIdOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProducerTraceInfoForQueryMessageTraceByMessageIdOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProducerTraceInfoForQueryMessageTraceByMessageIdOutput):
            return True

        return self.to_dict() != other.to_dict()
