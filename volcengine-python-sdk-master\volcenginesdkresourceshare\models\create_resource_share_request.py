# coding: utf-8

"""
    resource_share

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateResourceShareRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_share_type': 'str',
        'name': 'str',
        'principals': 'str',
        'resource_trns': 'str'
    }

    attribute_map = {
        'allow_share_type': 'AllowShareType',
        'name': 'Name',
        'principals': 'Principals',
        'resource_trns': 'ResourceTrns'
    }

    def __init__(self, allow_share_type=None, name=None, principals=None, resource_trns=None, _configuration=None):  # noqa: E501
        """CreateResourceShareRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_share_type = None
        self._name = None
        self._principals = None
        self._resource_trns = None
        self.discriminator = None

        if allow_share_type is not None:
            self.allow_share_type = allow_share_type
        self.name = name
        if principals is not None:
            self.principals = principals
        if resource_trns is not None:
            self.resource_trns = resource_trns

    @property
    def allow_share_type(self):
        """Gets the allow_share_type of this CreateResourceShareRequest.  # noqa: E501


        :return: The allow_share_type of this CreateResourceShareRequest.  # noqa: E501
        :rtype: str
        """
        return self._allow_share_type

    @allow_share_type.setter
    def allow_share_type(self, allow_share_type):
        """Sets the allow_share_type of this CreateResourceShareRequest.


        :param allow_share_type: The allow_share_type of this CreateResourceShareRequest.  # noqa: E501
        :type: str
        """

        self._allow_share_type = allow_share_type

    @property
    def name(self):
        """Gets the name of this CreateResourceShareRequest.  # noqa: E501


        :return: The name of this CreateResourceShareRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateResourceShareRequest.


        :param name: The name of this CreateResourceShareRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def principals(self):
        """Gets the principals of this CreateResourceShareRequest.  # noqa: E501


        :return: The principals of this CreateResourceShareRequest.  # noqa: E501
        :rtype: str
        """
        return self._principals

    @principals.setter
    def principals(self, principals):
        """Sets the principals of this CreateResourceShareRequest.


        :param principals: The principals of this CreateResourceShareRequest.  # noqa: E501
        :type: str
        """

        self._principals = principals

    @property
    def resource_trns(self):
        """Gets the resource_trns of this CreateResourceShareRequest.  # noqa: E501


        :return: The resource_trns of this CreateResourceShareRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_trns

    @resource_trns.setter
    def resource_trns(self, resource_trns):
        """Sets the resource_trns of this CreateResourceShareRequest.


        :param resource_trns: The resource_trns of this CreateResourceShareRequest.  # noqa: E501
        :type: str
        """

        self._resource_trns = resource_trns

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateResourceShareRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateResourceShareRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateResourceShareRequest):
            return True

        return self.to_dict() != other.to_dict()
