# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListResourceQueuesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_type': 'str',
        'ids': 'list[str]',
        'name_contains': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'resource_group_ids': 'list[str]',
        'shareable': 'bool',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'list[str]',
        'workload_types': 'list[str]',
        'zone_ids': 'list[str]'
    }

    attribute_map = {
        'charge_type': 'ChargeType',
        'ids': 'Ids',
        'name_contains': 'NameContains',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'resource_group_ids': 'ResourceGroupIds',
        'shareable': 'Shareable',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status',
        'workload_types': 'WorkloadTypes',
        'zone_ids': 'ZoneIds'
    }

    def __init__(self, charge_type=None, ids=None, name_contains=None, page_number=None, page_size=None, resource_group_ids=None, shareable=None, sort_by=None, sort_order=None, status=None, workload_types=None, zone_ids=None, _configuration=None):  # noqa: E501
        """ListResourceQueuesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_type = None
        self._ids = None
        self._name_contains = None
        self._page_number = None
        self._page_size = None
        self._resource_group_ids = None
        self._shareable = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self._workload_types = None
        self._zone_ids = None
        self.discriminator = None

        if charge_type is not None:
            self.charge_type = charge_type
        if ids is not None:
            self.ids = ids
        if name_contains is not None:
            self.name_contains = name_contains
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if resource_group_ids is not None:
            self.resource_group_ids = resource_group_ids
        if shareable is not None:
            self.shareable = shareable
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status
        if workload_types is not None:
            self.workload_types = workload_types
        if zone_ids is not None:
            self.zone_ids = zone_ids

    @property
    def charge_type(self):
        """Gets the charge_type of this ListResourceQueuesRequest.  # noqa: E501


        :return: The charge_type of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ListResourceQueuesRequest.


        :param charge_type: The charge_type of this ListResourceQueuesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Paused", "Running", "Closed", "Closing"]  # noqa: E501
        if (self._configuration.client_side_validation and
                charge_type not in allowed_values):
            raise ValueError(
                "Invalid value for `charge_type` ({0}), must be one of {1}"  # noqa: E501
                .format(charge_type, allowed_values)
            )

        self._charge_type = charge_type

    @property
    def ids(self):
        """Gets the ids of this ListResourceQueuesRequest.  # noqa: E501


        :return: The ids of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this ListResourceQueuesRequest.


        :param ids: The ids of this ListResourceQueuesRequest.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def name_contains(self):
        """Gets the name_contains of this ListResourceQueuesRequest.  # noqa: E501


        :return: The name_contains of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: str
        """
        return self._name_contains

    @name_contains.setter
    def name_contains(self, name_contains):
        """Sets the name_contains of this ListResourceQueuesRequest.


        :param name_contains: The name_contains of this ListResourceQueuesRequest.  # noqa: E501
        :type: str
        """

        self._name_contains = name_contains

    @property
    def page_number(self):
        """Gets the page_number of this ListResourceQueuesRequest.  # noqa: E501


        :return: The page_number of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListResourceQueuesRequest.


        :param page_number: The page_number of this ListResourceQueuesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListResourceQueuesRequest.  # noqa: E501


        :return: The page_size of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListResourceQueuesRequest.


        :param page_size: The page_size of this ListResourceQueuesRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 100):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                page_size is not None and page_size < 10):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value greater than or equal to `10`")  # noqa: E501

        self._page_size = page_size

    @property
    def resource_group_ids(self):
        """Gets the resource_group_ids of this ListResourceQueuesRequest.  # noqa: E501


        :return: The resource_group_ids of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._resource_group_ids

    @resource_group_ids.setter
    def resource_group_ids(self, resource_group_ids):
        """Sets the resource_group_ids of this ListResourceQueuesRequest.


        :param resource_group_ids: The resource_group_ids of this ListResourceQueuesRequest.  # noqa: E501
        :type: list[str]
        """

        self._resource_group_ids = resource_group_ids

    @property
    def shareable(self):
        """Gets the shareable of this ListResourceQueuesRequest.  # noqa: E501


        :return: The shareable of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._shareable

    @shareable.setter
    def shareable(self, shareable):
        """Sets the shareable of this ListResourceQueuesRequest.


        :param shareable: The shareable of this ListResourceQueuesRequest.  # noqa: E501
        :type: bool
        """

        self._shareable = shareable

    @property
    def sort_by(self):
        """Gets the sort_by of this ListResourceQueuesRequest.  # noqa: E501


        :return: The sort_by of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListResourceQueuesRequest.


        :param sort_by: The sort_by of this ListResourceQueuesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["CreateTime"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_by not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_by` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_by, allowed_values)
            )

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListResourceQueuesRequest.  # noqa: E501


        :return: The sort_order of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListResourceQueuesRequest.


        :param sort_order: The sort_order of this ListResourceQueuesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Ascend", "Descend"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_order not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_order` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_order, allowed_values)
            )

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListResourceQueuesRequest.  # noqa: E501


        :return: The status of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListResourceQueuesRequest.


        :param status: The status of this ListResourceQueuesRequest.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["PrePaid", "PostPaid"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(status).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `status` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(status) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._status = status

    @property
    def workload_types(self):
        """Gets the workload_types of this ListResourceQueuesRequest.  # noqa: E501


        :return: The workload_types of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._workload_types

    @workload_types.setter
    def workload_types(self, workload_types):
        """Sets the workload_types of this ListResourceQueuesRequest.


        :param workload_types: The workload_types of this ListResourceQueuesRequest.  # noqa: E501
        :type: list[str]
        """

        self._workload_types = workload_types

    @property
    def zone_ids(self):
        """Gets the zone_ids of this ListResourceQueuesRequest.  # noqa: E501


        :return: The zone_ids of this ListResourceQueuesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._zone_ids

    @zone_ids.setter
    def zone_ids(self, zone_ids):
        """Sets the zone_ids of this ListResourceQueuesRequest.


        :param zone_ids: The zone_ids of this ListResourceQueuesRequest.  # noqa: E501
        :type: list[str]
        """

        self._zone_ids = zone_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListResourceQueuesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListResourceQueuesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListResourceQueuesRequest):
            return True

        return self.to_dict() != other.to_dict()
