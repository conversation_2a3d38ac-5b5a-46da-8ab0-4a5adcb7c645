# coding: utf-8

"""
    cloud_trail20180101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTrailsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'include_organization_trail': 'int',
        'trail_names': 'list[str]'
    }

    attribute_map = {
        'include_organization_trail': 'IncludeOrganizationTrail',
        'trail_names': 'TrailNames'
    }

    def __init__(self, include_organization_trail=None, trail_names=None, _configuration=None):  # noqa: E501
        """DescribeTrailsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._include_organization_trail = None
        self._trail_names = None
        self.discriminator = None

        if include_organization_trail is not None:
            self.include_organization_trail = include_organization_trail
        if trail_names is not None:
            self.trail_names = trail_names

    @property
    def include_organization_trail(self):
        """Gets the include_organization_trail of this DescribeTrailsRequest.  # noqa: E501


        :return: The include_organization_trail of this DescribeTrailsRequest.  # noqa: E501
        :rtype: int
        """
        return self._include_organization_trail

    @include_organization_trail.setter
    def include_organization_trail(self, include_organization_trail):
        """Sets the include_organization_trail of this DescribeTrailsRequest.


        :param include_organization_trail: The include_organization_trail of this DescribeTrailsRequest.  # noqa: E501
        :type: int
        """

        self._include_organization_trail = include_organization_trail

    @property
    def trail_names(self):
        """Gets the trail_names of this DescribeTrailsRequest.  # noqa: E501


        :return: The trail_names of this DescribeTrailsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._trail_names

    @trail_names.setter
    def trail_names(self, trail_names):
        """Sets the trail_names of this DescribeTrailsRequest.


        :param trail_names: The trail_names of this DescribeTrailsRequest.  # noqa: E501
        :type: list[str]
        """

        self._trail_names = trail_names

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTrailsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTrailsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTrailsRequest):
            return True

        return self.to_dict() != other.to_dict()
