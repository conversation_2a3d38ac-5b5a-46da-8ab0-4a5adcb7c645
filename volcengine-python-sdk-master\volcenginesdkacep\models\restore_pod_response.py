# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RestorePodResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'task_id_list': 'list[str]'
    }

    attribute_map = {
        'task_id_list': 'TaskIdList'
    }

    def __init__(self, task_id_list=None, _configuration=None):  # noqa: E501
        """RestorePodResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._task_id_list = None
        self.discriminator = None

        if task_id_list is not None:
            self.task_id_list = task_id_list

    @property
    def task_id_list(self):
        """Gets the task_id_list of this RestorePodResponse.  # noqa: E501


        :return: The task_id_list of this RestorePodResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._task_id_list

    @task_id_list.setter
    def task_id_list(self, task_id_list):
        """Sets the task_id_list of this RestorePodResponse.


        :param task_id_list: The task_id_list of this RestorePodResponse.  # noqa: E501
        :type: list[str]
        """

        self._task_id_list = task_id_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RestorePodResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RestorePodResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RestorePodResponse):
            return True

        return self.to_dict() != other.to_dict()
