# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCdnEdgeIpResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cdn_ipv4': 'list[str]',
        'cdn_ipv6': 'list[str]'
    }

    attribute_map = {
        'cdn_ipv4': 'CdnIpv4',
        'cdn_ipv6': 'CdnIpv6'
    }

    def __init__(self, cdn_ipv4=None, cdn_ipv6=None, _configuration=None):  # noqa: E501
        """DescribeCdnEdgeIpResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cdn_ipv4 = None
        self._cdn_ipv6 = None
        self.discriminator = None

        if cdn_ipv4 is not None:
            self.cdn_ipv4 = cdn_ipv4
        if cdn_ipv6 is not None:
            self.cdn_ipv6 = cdn_ipv6

    @property
    def cdn_ipv4(self):
        """Gets the cdn_ipv4 of this DescribeCdnEdgeIpResponse.  # noqa: E501


        :return: The cdn_ipv4 of this DescribeCdnEdgeIpResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._cdn_ipv4

    @cdn_ipv4.setter
    def cdn_ipv4(self, cdn_ipv4):
        """Sets the cdn_ipv4 of this DescribeCdnEdgeIpResponse.


        :param cdn_ipv4: The cdn_ipv4 of this DescribeCdnEdgeIpResponse.  # noqa: E501
        :type: list[str]
        """

        self._cdn_ipv4 = cdn_ipv4

    @property
    def cdn_ipv6(self):
        """Gets the cdn_ipv6 of this DescribeCdnEdgeIpResponse.  # noqa: E501


        :return: The cdn_ipv6 of this DescribeCdnEdgeIpResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._cdn_ipv6

    @cdn_ipv6.setter
    def cdn_ipv6(self, cdn_ipv6):
        """Sets the cdn_ipv6 of this DescribeCdnEdgeIpResponse.


        :param cdn_ipv6: The cdn_ipv6 of this DescribeCdnEdgeIpResponse.  # noqa: E501
        :type: list[str]
        """

        self._cdn_ipv6 = cdn_ipv6

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCdnEdgeIpResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCdnEdgeIpResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCdnEdgeIpResponse):
            return True

        return self.to_dict() != other.to_dict()
