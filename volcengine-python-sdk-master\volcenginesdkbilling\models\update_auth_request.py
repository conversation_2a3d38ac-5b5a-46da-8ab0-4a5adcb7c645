# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateAuthRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auth_list_str': 'str',
        'relation_id': 'str'
    }

    attribute_map = {
        'auth_list_str': 'AuthListStr',
        'relation_id': 'RelationID'
    }

    def __init__(self, auth_list_str=None, relation_id=None, _configuration=None):  # noqa: E501
        """UpdateAuthRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auth_list_str = None
        self._relation_id = None
        self.discriminator = None

        if auth_list_str is not None:
            self.auth_list_str = auth_list_str
        self.relation_id = relation_id

    @property
    def auth_list_str(self):
        """Gets the auth_list_str of this UpdateAuthRequest.  # noqa: E501


        :return: The auth_list_str of this UpdateAuthRequest.  # noqa: E501
        :rtype: str
        """
        return self._auth_list_str

    @auth_list_str.setter
    def auth_list_str(self, auth_list_str):
        """Sets the auth_list_str of this UpdateAuthRequest.


        :param auth_list_str: The auth_list_str of this UpdateAuthRequest.  # noqa: E501
        :type: str
        """

        self._auth_list_str = auth_list_str

    @property
    def relation_id(self):
        """Gets the relation_id of this UpdateAuthRequest.  # noqa: E501


        :return: The relation_id of this UpdateAuthRequest.  # noqa: E501
        :rtype: str
        """
        return self._relation_id

    @relation_id.setter
    def relation_id(self, relation_id):
        """Sets the relation_id of this UpdateAuthRequest.


        :param relation_id: The relation_id of this UpdateAuthRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and relation_id is None:
            raise ValueError("Invalid value for `relation_id`, must not be `None`")  # noqa: E501

        self._relation_id = relation_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateAuthRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateAuthRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateAuthRequest):
            return True

        return self.to_dict() != other.to_dict()
