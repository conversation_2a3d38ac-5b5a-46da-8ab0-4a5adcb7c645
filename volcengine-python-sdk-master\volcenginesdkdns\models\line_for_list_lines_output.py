# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LineForListLinesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'father_value': 'str',
        'id': 'str',
        'level': 'int',
        'name': 'str',
        'part_name': 'str',
        'type': 'int',
        'value': 'str'
    }

    attribute_map = {
        'father_value': 'FatherValue',
        'id': 'ID',
        'level': 'Level',
        'name': 'Name',
        'part_name': 'PartName',
        'type': 'Type',
        'value': 'Value'
    }

    def __init__(self, father_value=None, id=None, level=None, name=None, part_name=None, type=None, value=None, _configuration=None):  # noqa: E501
        """LineForListLinesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._father_value = None
        self._id = None
        self._level = None
        self._name = None
        self._part_name = None
        self._type = None
        self._value = None
        self.discriminator = None

        if father_value is not None:
            self.father_value = father_value
        if id is not None:
            self.id = id
        if level is not None:
            self.level = level
        if name is not None:
            self.name = name
        if part_name is not None:
            self.part_name = part_name
        if type is not None:
            self.type = type
        if value is not None:
            self.value = value

    @property
    def father_value(self):
        """Gets the father_value of this LineForListLinesOutput.  # noqa: E501


        :return: The father_value of this LineForListLinesOutput.  # noqa: E501
        :rtype: str
        """
        return self._father_value

    @father_value.setter
    def father_value(self, father_value):
        """Sets the father_value of this LineForListLinesOutput.


        :param father_value: The father_value of this LineForListLinesOutput.  # noqa: E501
        :type: str
        """

        self._father_value = father_value

    @property
    def id(self):
        """Gets the id of this LineForListLinesOutput.  # noqa: E501


        :return: The id of this LineForListLinesOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this LineForListLinesOutput.


        :param id: The id of this LineForListLinesOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def level(self):
        """Gets the level of this LineForListLinesOutput.  # noqa: E501


        :return: The level of this LineForListLinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this LineForListLinesOutput.


        :param level: The level of this LineForListLinesOutput.  # noqa: E501
        :type: int
        """

        self._level = level

    @property
    def name(self):
        """Gets the name of this LineForListLinesOutput.  # noqa: E501


        :return: The name of this LineForListLinesOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this LineForListLinesOutput.


        :param name: The name of this LineForListLinesOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def part_name(self):
        """Gets the part_name of this LineForListLinesOutput.  # noqa: E501


        :return: The part_name of this LineForListLinesOutput.  # noqa: E501
        :rtype: str
        """
        return self._part_name

    @part_name.setter
    def part_name(self, part_name):
        """Sets the part_name of this LineForListLinesOutput.


        :param part_name: The part_name of this LineForListLinesOutput.  # noqa: E501
        :type: str
        """

        self._part_name = part_name

    @property
    def type(self):
        """Gets the type of this LineForListLinesOutput.  # noqa: E501


        :return: The type of this LineForListLinesOutput.  # noqa: E501
        :rtype: int
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this LineForListLinesOutput.


        :param type: The type of this LineForListLinesOutput.  # noqa: E501
        :type: int
        """

        self._type = type

    @property
    def value(self):
        """Gets the value of this LineForListLinesOutput.  # noqa: E501


        :return: The value of this LineForListLinesOutput.  # noqa: E501
        :rtype: str
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this LineForListLinesOutput.


        :param value: The value of this LineForListLinesOutput.  # noqa: E501
        :type: str
        """

        self._value = value

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LineForListLinesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LineForListLinesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LineForListLinesOutput):
            return True

        return self.to_dict() != other.to_dict()
