# coding: utf-8

"""
    dms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskReportForQueryDataMigrateTaskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'failed_list_name': 'str',
        'report_name': 'str',
        'success_list_name': 'str'
    }

    attribute_map = {
        'failed_list_name': 'FailedListName',
        'report_name': 'ReportName',
        'success_list_name': 'SuccessListName'
    }

    def __init__(self, failed_list_name=None, report_name=None, success_list_name=None, _configuration=None):  # noqa: E501
        """TaskReportForQueryDataMigrateTaskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._failed_list_name = None
        self._report_name = None
        self._success_list_name = None
        self.discriminator = None

        if failed_list_name is not None:
            self.failed_list_name = failed_list_name
        if report_name is not None:
            self.report_name = report_name
        if success_list_name is not None:
            self.success_list_name = success_list_name

    @property
    def failed_list_name(self):
        """Gets the failed_list_name of this TaskReportForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The failed_list_name of this TaskReportForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._failed_list_name

    @failed_list_name.setter
    def failed_list_name(self, failed_list_name):
        """Sets the failed_list_name of this TaskReportForQueryDataMigrateTaskOutput.


        :param failed_list_name: The failed_list_name of this TaskReportForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._failed_list_name = failed_list_name

    @property
    def report_name(self):
        """Gets the report_name of this TaskReportForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The report_name of this TaskReportForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._report_name

    @report_name.setter
    def report_name(self, report_name):
        """Sets the report_name of this TaskReportForQueryDataMigrateTaskOutput.


        :param report_name: The report_name of this TaskReportForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._report_name = report_name

    @property
    def success_list_name(self):
        """Gets the success_list_name of this TaskReportForQueryDataMigrateTaskOutput.  # noqa: E501


        :return: The success_list_name of this TaskReportForQueryDataMigrateTaskOutput.  # noqa: E501
        :rtype: str
        """
        return self._success_list_name

    @success_list_name.setter
    def success_list_name(self, success_list_name):
        """Sets the success_list_name of this TaskReportForQueryDataMigrateTaskOutput.


        :param success_list_name: The success_list_name of this TaskReportForQueryDataMigrateTaskOutput.  # noqa: E501
        :type: str
        """

        self._success_list_name = success_list_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskReportForQueryDataMigrateTaskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskReportForQueryDataMigrateTaskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskReportForQueryDataMigrateTaskOutput):
            return True

        return self.to_dict() != other.to_dict()
