# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuizStatisticsListForListQuizStatisticsAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'deadline': 'int',
        'is_delete': 'bool',
        'quiz_id': 'int',
        'quiz_title': 'str',
        'quiz_title_type': 'int',
        'quiz_type': 'int',
        'right_count': 'int',
        'send_time': 'int',
        'total_count': 'int'
    }

    attribute_map = {
        'deadline': 'Deadline',
        'is_delete': 'IsDelete',
        'quiz_id': 'QuizID',
        'quiz_title': 'QuizTitle',
        'quiz_title_type': 'QuizTitleType',
        'quiz_type': 'QuizType',
        'right_count': 'RightCount',
        'send_time': 'SendTime',
        'total_count': 'TotalCount'
    }

    def __init__(self, deadline=None, is_delete=None, quiz_id=None, quiz_title=None, quiz_title_type=None, quiz_type=None, right_count=None, send_time=None, total_count=None, _configuration=None):  # noqa: E501
        """QuizStatisticsListForListQuizStatisticsAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._deadline = None
        self._is_delete = None
        self._quiz_id = None
        self._quiz_title = None
        self._quiz_title_type = None
        self._quiz_type = None
        self._right_count = None
        self._send_time = None
        self._total_count = None
        self.discriminator = None

        if deadline is not None:
            self.deadline = deadline
        if is_delete is not None:
            self.is_delete = is_delete
        if quiz_id is not None:
            self.quiz_id = quiz_id
        if quiz_title is not None:
            self.quiz_title = quiz_title
        if quiz_title_type is not None:
            self.quiz_title_type = quiz_title_type
        if quiz_type is not None:
            self.quiz_type = quiz_type
        if right_count is not None:
            self.right_count = right_count
        if send_time is not None:
            self.send_time = send_time
        if total_count is not None:
            self.total_count = total_count

    @property
    def deadline(self):
        """Gets the deadline of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501


        :return: The deadline of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._deadline

    @deadline.setter
    def deadline(self, deadline):
        """Sets the deadline of this QuizStatisticsListForListQuizStatisticsAPIOutput.


        :param deadline: The deadline of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._deadline = deadline

    @property
    def is_delete(self):
        """Gets the is_delete of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501


        :return: The is_delete of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_delete

    @is_delete.setter
    def is_delete(self, is_delete):
        """Sets the is_delete of this QuizStatisticsListForListQuizStatisticsAPIOutput.


        :param is_delete: The is_delete of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :type: bool
        """

        self._is_delete = is_delete

    @property
    def quiz_id(self):
        """Gets the quiz_id of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501


        :return: The quiz_id of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._quiz_id

    @quiz_id.setter
    def quiz_id(self, quiz_id):
        """Sets the quiz_id of this QuizStatisticsListForListQuizStatisticsAPIOutput.


        :param quiz_id: The quiz_id of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._quiz_id = quiz_id

    @property
    def quiz_title(self):
        """Gets the quiz_title of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501


        :return: The quiz_title of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._quiz_title

    @quiz_title.setter
    def quiz_title(self, quiz_title):
        """Sets the quiz_title of this QuizStatisticsListForListQuizStatisticsAPIOutput.


        :param quiz_title: The quiz_title of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._quiz_title = quiz_title

    @property
    def quiz_title_type(self):
        """Gets the quiz_title_type of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501


        :return: The quiz_title_type of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._quiz_title_type

    @quiz_title_type.setter
    def quiz_title_type(self, quiz_title_type):
        """Sets the quiz_title_type of this QuizStatisticsListForListQuizStatisticsAPIOutput.


        :param quiz_title_type: The quiz_title_type of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._quiz_title_type = quiz_title_type

    @property
    def quiz_type(self):
        """Gets the quiz_type of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501


        :return: The quiz_type of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._quiz_type

    @quiz_type.setter
    def quiz_type(self, quiz_type):
        """Sets the quiz_type of this QuizStatisticsListForListQuizStatisticsAPIOutput.


        :param quiz_type: The quiz_type of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._quiz_type = quiz_type

    @property
    def right_count(self):
        """Gets the right_count of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501


        :return: The right_count of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._right_count

    @right_count.setter
    def right_count(self, right_count):
        """Sets the right_count of this QuizStatisticsListForListQuizStatisticsAPIOutput.


        :param right_count: The right_count of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._right_count = right_count

    @property
    def send_time(self):
        """Gets the send_time of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501


        :return: The send_time of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this QuizStatisticsListForListQuizStatisticsAPIOutput.


        :param send_time: The send_time of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    @property
    def total_count(self):
        """Gets the total_count of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501


        :return: The total_count of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this QuizStatisticsListForListQuizStatisticsAPIOutput.


        :param total_count: The total_count of this QuizStatisticsListForListQuizStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuizStatisticsListForListQuizStatisticsAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuizStatisticsListForListQuizStatisticsAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuizStatisticsListForListQuizStatisticsAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
