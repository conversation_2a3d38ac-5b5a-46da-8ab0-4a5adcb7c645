# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MenuForModifyActivityMenusOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'location': 'int',
        'name': 'str',
        'tool_id': 'int',
        'type': 'int'
    }

    attribute_map = {
        'location': 'Location',
        'name': 'Name',
        'tool_id': 'ToolId',
        'type': 'Type'
    }

    def __init__(self, location=None, name=None, tool_id=None, type=None, _configuration=None):  # noqa: E501
        """MenuForModifyActivityMenusOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._location = None
        self._name = None
        self._tool_id = None
        self._type = None
        self.discriminator = None

        if location is not None:
            self.location = location
        if name is not None:
            self.name = name
        if tool_id is not None:
            self.tool_id = tool_id
        if type is not None:
            self.type = type

    @property
    def location(self):
        """Gets the location of this MenuForModifyActivityMenusOutput.  # noqa: E501


        :return: The location of this MenuForModifyActivityMenusOutput.  # noqa: E501
        :rtype: int
        """
        return self._location

    @location.setter
    def location(self, location):
        """Sets the location of this MenuForModifyActivityMenusOutput.


        :param location: The location of this MenuForModifyActivityMenusOutput.  # noqa: E501
        :type: int
        """

        self._location = location

    @property
    def name(self):
        """Gets the name of this MenuForModifyActivityMenusOutput.  # noqa: E501


        :return: The name of this MenuForModifyActivityMenusOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this MenuForModifyActivityMenusOutput.


        :param name: The name of this MenuForModifyActivityMenusOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def tool_id(self):
        """Gets the tool_id of this MenuForModifyActivityMenusOutput.  # noqa: E501


        :return: The tool_id of this MenuForModifyActivityMenusOutput.  # noqa: E501
        :rtype: int
        """
        return self._tool_id

    @tool_id.setter
    def tool_id(self, tool_id):
        """Sets the tool_id of this MenuForModifyActivityMenusOutput.


        :param tool_id: The tool_id of this MenuForModifyActivityMenusOutput.  # noqa: E501
        :type: int
        """

        self._tool_id = tool_id

    @property
    def type(self):
        """Gets the type of this MenuForModifyActivityMenusOutput.  # noqa: E501


        :return: The type of this MenuForModifyActivityMenusOutput.  # noqa: E501
        :rtype: int
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this MenuForModifyActivityMenusOutput.


        :param type: The type of this MenuForModifyActivityMenusOutput.  # noqa: E501
        :type: int
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MenuForModifyActivityMenusOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MenuForModifyActivityMenusOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MenuForModifyActivityMenusOutput):
            return True

        return self.to_dict() != other.to_dict()
