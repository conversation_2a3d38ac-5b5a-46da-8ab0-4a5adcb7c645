# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeNetworkLoadBalancerAttributesResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'billing_status': 'str',
        'billing_type': 'int',
        'create_time': 'str',
        'cross_zone_enabled': 'bool',
        'dns_name': 'str',
        'description': 'str',
        'expected_overdue_time': 'str',
        'ip_address_version': 'str',
        'ipv4_bandwidth_package_id': 'str',
        'ipv4_network_type': 'str',
        'ipv6_bandwidth_package_id': 'str',
        'ipv6_network_type': 'str',
        'load_balancer_id': 'str',
        'load_balancer_name': 'str',
        'managed_security_group_id': 'str',
        'modification_protection_status': 'str',
        'overdue_time': 'str',
        'project_name': 'str',
        'reclaimed_time': 'str',
        'request_id': 'str',
        'security_group_ids': 'list[str]',
        'status': 'str',
        'tags': 'list[TagForDescribeNetworkLoadBalancerAttributesOutput]',
        'update_time': 'str',
        'vpc_id': 'str',
        'zone_mappings': 'list[ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput]'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'billing_status': 'BillingStatus',
        'billing_type': 'BillingType',
        'create_time': 'CreateTime',
        'cross_zone_enabled': 'CrossZoneEnabled',
        'dns_name': 'DNSName',
        'description': 'Description',
        'expected_overdue_time': 'ExpectedOverdueTime',
        'ip_address_version': 'IpAddressVersion',
        'ipv4_bandwidth_package_id': 'Ipv4BandwidthPackageId',
        'ipv4_network_type': 'Ipv4NetworkType',
        'ipv6_bandwidth_package_id': 'Ipv6BandwidthPackageId',
        'ipv6_network_type': 'Ipv6NetworkType',
        'load_balancer_id': 'LoadBalancerId',
        'load_balancer_name': 'LoadBalancerName',
        'managed_security_group_id': 'ManagedSecurityGroupId',
        'modification_protection_status': 'ModificationProtectionStatus',
        'overdue_time': 'OverdueTime',
        'project_name': 'ProjectName',
        'reclaimed_time': 'ReclaimedTime',
        'request_id': 'RequestId',
        'security_group_ids': 'SecurityGroupIds',
        'status': 'Status',
        'tags': 'Tags',
        'update_time': 'UpdateTime',
        'vpc_id': 'VpcId',
        'zone_mappings': 'ZoneMappings'
    }

    def __init__(self, account_id=None, billing_status=None, billing_type=None, create_time=None, cross_zone_enabled=None, dns_name=None, description=None, expected_overdue_time=None, ip_address_version=None, ipv4_bandwidth_package_id=None, ipv4_network_type=None, ipv6_bandwidth_package_id=None, ipv6_network_type=None, load_balancer_id=None, load_balancer_name=None, managed_security_group_id=None, modification_protection_status=None, overdue_time=None, project_name=None, reclaimed_time=None, request_id=None, security_group_ids=None, status=None, tags=None, update_time=None, vpc_id=None, zone_mappings=None, _configuration=None):  # noqa: E501
        """DescribeNetworkLoadBalancerAttributesResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._billing_status = None
        self._billing_type = None
        self._create_time = None
        self._cross_zone_enabled = None
        self._dns_name = None
        self._description = None
        self._expected_overdue_time = None
        self._ip_address_version = None
        self._ipv4_bandwidth_package_id = None
        self._ipv4_network_type = None
        self._ipv6_bandwidth_package_id = None
        self._ipv6_network_type = None
        self._load_balancer_id = None
        self._load_balancer_name = None
        self._managed_security_group_id = None
        self._modification_protection_status = None
        self._overdue_time = None
        self._project_name = None
        self._reclaimed_time = None
        self._request_id = None
        self._security_group_ids = None
        self._status = None
        self._tags = None
        self._update_time = None
        self._vpc_id = None
        self._zone_mappings = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if billing_status is not None:
            self.billing_status = billing_status
        if billing_type is not None:
            self.billing_type = billing_type
        if create_time is not None:
            self.create_time = create_time
        if cross_zone_enabled is not None:
            self.cross_zone_enabled = cross_zone_enabled
        if dns_name is not None:
            self.dns_name = dns_name
        if description is not None:
            self.description = description
        if expected_overdue_time is not None:
            self.expected_overdue_time = expected_overdue_time
        if ip_address_version is not None:
            self.ip_address_version = ip_address_version
        if ipv4_bandwidth_package_id is not None:
            self.ipv4_bandwidth_package_id = ipv4_bandwidth_package_id
        if ipv4_network_type is not None:
            self.ipv4_network_type = ipv4_network_type
        if ipv6_bandwidth_package_id is not None:
            self.ipv6_bandwidth_package_id = ipv6_bandwidth_package_id
        if ipv6_network_type is not None:
            self.ipv6_network_type = ipv6_network_type
        if load_balancer_id is not None:
            self.load_balancer_id = load_balancer_id
        if load_balancer_name is not None:
            self.load_balancer_name = load_balancer_name
        if managed_security_group_id is not None:
            self.managed_security_group_id = managed_security_group_id
        if modification_protection_status is not None:
            self.modification_protection_status = modification_protection_status
        if overdue_time is not None:
            self.overdue_time = overdue_time
        if project_name is not None:
            self.project_name = project_name
        if reclaimed_time is not None:
            self.reclaimed_time = reclaimed_time
        if request_id is not None:
            self.request_id = request_id
        if security_group_ids is not None:
            self.security_group_ids = security_group_ids
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_time is not None:
            self.update_time = update_time
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if zone_mappings is not None:
            self.zone_mappings = zone_mappings

    @property
    def account_id(self):
        """Gets the account_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The account_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeNetworkLoadBalancerAttributesResponse.


        :param account_id: The account_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def billing_status(self):
        """Gets the billing_status of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The billing_status of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._billing_status

    @billing_status.setter
    def billing_status(self, billing_status):
        """Sets the billing_status of this DescribeNetworkLoadBalancerAttributesResponse.


        :param billing_status: The billing_status of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._billing_status = billing_status

    @property
    def billing_type(self):
        """Gets the billing_type of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The billing_type of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: int
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this DescribeNetworkLoadBalancerAttributesResponse.


        :param billing_type: The billing_type of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: int
        """

        self._billing_type = billing_type

    @property
    def create_time(self):
        """Gets the create_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The create_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeNetworkLoadBalancerAttributesResponse.


        :param create_time: The create_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def cross_zone_enabled(self):
        """Gets the cross_zone_enabled of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The cross_zone_enabled of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: bool
        """
        return self._cross_zone_enabled

    @cross_zone_enabled.setter
    def cross_zone_enabled(self, cross_zone_enabled):
        """Sets the cross_zone_enabled of this DescribeNetworkLoadBalancerAttributesResponse.


        :param cross_zone_enabled: The cross_zone_enabled of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: bool
        """

        self._cross_zone_enabled = cross_zone_enabled

    @property
    def dns_name(self):
        """Gets the dns_name of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The dns_name of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._dns_name

    @dns_name.setter
    def dns_name(self, dns_name):
        """Sets the dns_name of this DescribeNetworkLoadBalancerAttributesResponse.


        :param dns_name: The dns_name of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._dns_name = dns_name

    @property
    def description(self):
        """Gets the description of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The description of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DescribeNetworkLoadBalancerAttributesResponse.


        :param description: The description of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def expected_overdue_time(self):
        """Gets the expected_overdue_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The expected_overdue_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._expected_overdue_time

    @expected_overdue_time.setter
    def expected_overdue_time(self, expected_overdue_time):
        """Sets the expected_overdue_time of this DescribeNetworkLoadBalancerAttributesResponse.


        :param expected_overdue_time: The expected_overdue_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._expected_overdue_time = expected_overdue_time

    @property
    def ip_address_version(self):
        """Gets the ip_address_version of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The ip_address_version of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._ip_address_version

    @ip_address_version.setter
    def ip_address_version(self, ip_address_version):
        """Sets the ip_address_version of this DescribeNetworkLoadBalancerAttributesResponse.


        :param ip_address_version: The ip_address_version of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._ip_address_version = ip_address_version

    @property
    def ipv4_bandwidth_package_id(self):
        """Gets the ipv4_bandwidth_package_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The ipv4_bandwidth_package_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._ipv4_bandwidth_package_id

    @ipv4_bandwidth_package_id.setter
    def ipv4_bandwidth_package_id(self, ipv4_bandwidth_package_id):
        """Sets the ipv4_bandwidth_package_id of this DescribeNetworkLoadBalancerAttributesResponse.


        :param ipv4_bandwidth_package_id: The ipv4_bandwidth_package_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._ipv4_bandwidth_package_id = ipv4_bandwidth_package_id

    @property
    def ipv4_network_type(self):
        """Gets the ipv4_network_type of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The ipv4_network_type of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._ipv4_network_type

    @ipv4_network_type.setter
    def ipv4_network_type(self, ipv4_network_type):
        """Sets the ipv4_network_type of this DescribeNetworkLoadBalancerAttributesResponse.


        :param ipv4_network_type: The ipv4_network_type of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._ipv4_network_type = ipv4_network_type

    @property
    def ipv6_bandwidth_package_id(self):
        """Gets the ipv6_bandwidth_package_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The ipv6_bandwidth_package_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_bandwidth_package_id

    @ipv6_bandwidth_package_id.setter
    def ipv6_bandwidth_package_id(self, ipv6_bandwidth_package_id):
        """Sets the ipv6_bandwidth_package_id of this DescribeNetworkLoadBalancerAttributesResponse.


        :param ipv6_bandwidth_package_id: The ipv6_bandwidth_package_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._ipv6_bandwidth_package_id = ipv6_bandwidth_package_id

    @property
    def ipv6_network_type(self):
        """Gets the ipv6_network_type of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The ipv6_network_type of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_network_type

    @ipv6_network_type.setter
    def ipv6_network_type(self, ipv6_network_type):
        """Sets the ipv6_network_type of this DescribeNetworkLoadBalancerAttributesResponse.


        :param ipv6_network_type: The ipv6_network_type of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._ipv6_network_type = ipv6_network_type

    @property
    def load_balancer_id(self):
        """Gets the load_balancer_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The load_balancer_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_id

    @load_balancer_id.setter
    def load_balancer_id(self, load_balancer_id):
        """Sets the load_balancer_id of this DescribeNetworkLoadBalancerAttributesResponse.


        :param load_balancer_id: The load_balancer_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._load_balancer_id = load_balancer_id

    @property
    def load_balancer_name(self):
        """Gets the load_balancer_name of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The load_balancer_name of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._load_balancer_name

    @load_balancer_name.setter
    def load_balancer_name(self, load_balancer_name):
        """Sets the load_balancer_name of this DescribeNetworkLoadBalancerAttributesResponse.


        :param load_balancer_name: The load_balancer_name of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._load_balancer_name = load_balancer_name

    @property
    def managed_security_group_id(self):
        """Gets the managed_security_group_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The managed_security_group_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._managed_security_group_id

    @managed_security_group_id.setter
    def managed_security_group_id(self, managed_security_group_id):
        """Sets the managed_security_group_id of this DescribeNetworkLoadBalancerAttributesResponse.


        :param managed_security_group_id: The managed_security_group_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._managed_security_group_id = managed_security_group_id

    @property
    def modification_protection_status(self):
        """Gets the modification_protection_status of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The modification_protection_status of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._modification_protection_status

    @modification_protection_status.setter
    def modification_protection_status(self, modification_protection_status):
        """Sets the modification_protection_status of this DescribeNetworkLoadBalancerAttributesResponse.


        :param modification_protection_status: The modification_protection_status of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._modification_protection_status = modification_protection_status

    @property
    def overdue_time(self):
        """Gets the overdue_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The overdue_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._overdue_time

    @overdue_time.setter
    def overdue_time(self, overdue_time):
        """Sets the overdue_time of this DescribeNetworkLoadBalancerAttributesResponse.


        :param overdue_time: The overdue_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._overdue_time = overdue_time

    @property
    def project_name(self):
        """Gets the project_name of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The project_name of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeNetworkLoadBalancerAttributesResponse.


        :param project_name: The project_name of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def reclaimed_time(self):
        """Gets the reclaimed_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The reclaimed_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._reclaimed_time

    @reclaimed_time.setter
    def reclaimed_time(self, reclaimed_time):
        """Sets the reclaimed_time of this DescribeNetworkLoadBalancerAttributesResponse.


        :param reclaimed_time: The reclaimed_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._reclaimed_time = reclaimed_time

    @property
    def request_id(self):
        """Gets the request_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The request_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._request_id

    @request_id.setter
    def request_id(self, request_id):
        """Sets the request_id of this DescribeNetworkLoadBalancerAttributesResponse.


        :param request_id: The request_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._request_id = request_id

    @property
    def security_group_ids(self):
        """Gets the security_group_ids of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The security_group_ids of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._security_group_ids

    @security_group_ids.setter
    def security_group_ids(self, security_group_ids):
        """Sets the security_group_ids of this DescribeNetworkLoadBalancerAttributesResponse.


        :param security_group_ids: The security_group_ids of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: list[str]
        """

        self._security_group_ids = security_group_ids

    @property
    def status(self):
        """Gets the status of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The status of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DescribeNetworkLoadBalancerAttributesResponse.


        :param status: The status of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The tags of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: list[TagForDescribeNetworkLoadBalancerAttributesOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DescribeNetworkLoadBalancerAttributesResponse.


        :param tags: The tags of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: list[TagForDescribeNetworkLoadBalancerAttributesOutput]
        """

        self._tags = tags

    @property
    def update_time(self):
        """Gets the update_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The update_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DescribeNetworkLoadBalancerAttributesResponse.


        :param update_time: The update_time of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The vpc_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DescribeNetworkLoadBalancerAttributesResponse.


        :param vpc_id: The vpc_id of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def zone_mappings(self):
        """Gets the zone_mappings of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501


        :return: The zone_mappings of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :rtype: list[ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput]
        """
        return self._zone_mappings

    @zone_mappings.setter
    def zone_mappings(self, zone_mappings):
        """Sets the zone_mappings of this DescribeNetworkLoadBalancerAttributesResponse.


        :param zone_mappings: The zone_mappings of this DescribeNetworkLoadBalancerAttributesResponse.  # noqa: E501
        :type: list[ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput]
        """

        self._zone_mappings = zone_mappings

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeNetworkLoadBalancerAttributesResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeNetworkLoadBalancerAttributesResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeNetworkLoadBalancerAttributesResponse):
            return True

        return self.to_dict() != other.to_dict()
