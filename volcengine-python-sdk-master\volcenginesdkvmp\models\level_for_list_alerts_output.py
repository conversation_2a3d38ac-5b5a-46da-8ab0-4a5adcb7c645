# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LevelForListAlertsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'comparator': 'str',
        '_for': 'str',
        'level': 'str',
        'threshold': 'float'
    }

    attribute_map = {
        'comparator': 'Comparator',
        '_for': 'For',
        'level': 'Level',
        'threshold': 'Threshold'
    }

    def __init__(self, comparator=None, _for=None, level=None, threshold=None, _configuration=None):  # noqa: E501
        """LevelForListAlertsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._comparator = None
        self.__for = None
        self._level = None
        self._threshold = None
        self.discriminator = None

        if comparator is not None:
            self.comparator = comparator
        if _for is not None:
            self._for = _for
        if level is not None:
            self.level = level
        if threshold is not None:
            self.threshold = threshold

    @property
    def comparator(self):
        """Gets the comparator of this LevelForListAlertsOutput.  # noqa: E501


        :return: The comparator of this LevelForListAlertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._comparator

    @comparator.setter
    def comparator(self, comparator):
        """Sets the comparator of this LevelForListAlertsOutput.


        :param comparator: The comparator of this LevelForListAlertsOutput.  # noqa: E501
        :type: str
        """

        self._comparator = comparator

    @property
    def _for(self):
        """Gets the _for of this LevelForListAlertsOutput.  # noqa: E501


        :return: The _for of this LevelForListAlertsOutput.  # noqa: E501
        :rtype: str
        """
        return self.__for

    @_for.setter
    def _for(self, _for):
        """Sets the _for of this LevelForListAlertsOutput.


        :param _for: The _for of this LevelForListAlertsOutput.  # noqa: E501
        :type: str
        """

        self.__for = _for

    @property
    def level(self):
        """Gets the level of this LevelForListAlertsOutput.  # noqa: E501


        :return: The level of this LevelForListAlertsOutput.  # noqa: E501
        :rtype: str
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this LevelForListAlertsOutput.


        :param level: The level of this LevelForListAlertsOutput.  # noqa: E501
        :type: str
        """

        self._level = level

    @property
    def threshold(self):
        """Gets the threshold of this LevelForListAlertsOutput.  # noqa: E501


        :return: The threshold of this LevelForListAlertsOutput.  # noqa: E501
        :rtype: float
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this LevelForListAlertsOutput.


        :param threshold: The threshold of this LevelForListAlertsOutput.  # noqa: E501
        :type: float
        """

        self._threshold = threshold

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LevelForListAlertsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LevelForListAlertsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LevelForListAlertsOutput):
            return True

        return self.to_dict() != other.to_dict()
