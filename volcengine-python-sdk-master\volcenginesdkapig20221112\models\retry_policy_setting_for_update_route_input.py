# coding: utf-8

"""
    apig20221112

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RetryPolicySettingForUpdateRouteInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attempts': 'int',
        'enable': 'bool',
        'http_codes': 'list[str]',
        'per_try_timeout': 'int',
        'retry_on': 'list[str]'
    }

    attribute_map = {
        'attempts': 'Attempts',
        'enable': 'Enable',
        'http_codes': 'HttpCodes',
        'per_try_timeout': 'PerTryTimeout',
        'retry_on': 'RetryOn'
    }

    def __init__(self, attempts=None, enable=None, http_codes=None, per_try_timeout=None, retry_on=None, _configuration=None):  # noqa: E501
        """RetryPolicySettingForUpdateRouteInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attempts = None
        self._enable = None
        self._http_codes = None
        self._per_try_timeout = None
        self._retry_on = None
        self.discriminator = None

        if attempts is not None:
            self.attempts = attempts
        if enable is not None:
            self.enable = enable
        if http_codes is not None:
            self.http_codes = http_codes
        if per_try_timeout is not None:
            self.per_try_timeout = per_try_timeout
        if retry_on is not None:
            self.retry_on = retry_on

    @property
    def attempts(self):
        """Gets the attempts of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501


        :return: The attempts of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :rtype: int
        """
        return self._attempts

    @attempts.setter
    def attempts(self, attempts):
        """Sets the attempts of this RetryPolicySettingForUpdateRouteInput.


        :param attempts: The attempts of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :type: int
        """

        self._attempts = attempts

    @property
    def enable(self):
        """Gets the enable of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501


        :return: The enable of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :rtype: bool
        """
        return self._enable

    @enable.setter
    def enable(self, enable):
        """Sets the enable of this RetryPolicySettingForUpdateRouteInput.


        :param enable: The enable of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :type: bool
        """

        self._enable = enable

    @property
    def http_codes(self):
        """Gets the http_codes of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501


        :return: The http_codes of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._http_codes

    @http_codes.setter
    def http_codes(self, http_codes):
        """Sets the http_codes of this RetryPolicySettingForUpdateRouteInput.


        :param http_codes: The http_codes of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :type: list[str]
        """

        self._http_codes = http_codes

    @property
    def per_try_timeout(self):
        """Gets the per_try_timeout of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501


        :return: The per_try_timeout of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :rtype: int
        """
        return self._per_try_timeout

    @per_try_timeout.setter
    def per_try_timeout(self, per_try_timeout):
        """Sets the per_try_timeout of this RetryPolicySettingForUpdateRouteInput.


        :param per_try_timeout: The per_try_timeout of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :type: int
        """

        self._per_try_timeout = per_try_timeout

    @property
    def retry_on(self):
        """Gets the retry_on of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501


        :return: The retry_on of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._retry_on

    @retry_on.setter
    def retry_on(self, retry_on):
        """Sets the retry_on of this RetryPolicySettingForUpdateRouteInput.


        :param retry_on: The retry_on of this RetryPolicySettingForUpdateRouteInput.  # noqa: E501
        :type: list[str]
        """

        self._retry_on = retry_on

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RetryPolicySettingForUpdateRouteInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RetryPolicySettingForUpdateRouteInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RetryPolicySettingForUpdateRouteInput):
            return True

        return self.to_dict() != other.to_dict()
