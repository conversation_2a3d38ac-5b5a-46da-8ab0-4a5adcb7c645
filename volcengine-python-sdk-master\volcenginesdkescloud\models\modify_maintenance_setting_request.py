# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyMaintenanceSettingRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'maintenance_day': 'list[str]',
        'maintenance_time': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'maintenance_day': 'MaintenanceDay',
        'maintenance_time': 'MaintenanceTime'
    }

    def __init__(self, instance_id=None, maintenance_day=None, maintenance_time=None, _configuration=None):  # noqa: E501
        """ModifyMaintenanceSettingRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._maintenance_day = None
        self._maintenance_time = None
        self.discriminator = None

        self.instance_id = instance_id
        if maintenance_day is not None:
            self.maintenance_day = maintenance_day
        self.maintenance_time = maintenance_time

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyMaintenanceSettingRequest.  # noqa: E501


        :return: The instance_id of this ModifyMaintenanceSettingRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyMaintenanceSettingRequest.


        :param instance_id: The instance_id of this ModifyMaintenanceSettingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def maintenance_day(self):
        """Gets the maintenance_day of this ModifyMaintenanceSettingRequest.  # noqa: E501


        :return: The maintenance_day of this ModifyMaintenanceSettingRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._maintenance_day

    @maintenance_day.setter
    def maintenance_day(self, maintenance_day):
        """Sets the maintenance_day of this ModifyMaintenanceSettingRequest.


        :param maintenance_day: The maintenance_day of this ModifyMaintenanceSettingRequest.  # noqa: E501
        :type: list[str]
        """

        self._maintenance_day = maintenance_day

    @property
    def maintenance_time(self):
        """Gets the maintenance_time of this ModifyMaintenanceSettingRequest.  # noqa: E501


        :return: The maintenance_time of this ModifyMaintenanceSettingRequest.  # noqa: E501
        :rtype: str
        """
        return self._maintenance_time

    @maintenance_time.setter
    def maintenance_time(self, maintenance_time):
        """Sets the maintenance_time of this ModifyMaintenanceSettingRequest.


        :param maintenance_time: The maintenance_time of this ModifyMaintenanceSettingRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and maintenance_time is None:
            raise ValueError("Invalid value for `maintenance_time`, must not be `None`")  # noqa: E501

        self._maintenance_time = maintenance_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyMaintenanceSettingRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyMaintenanceSettingRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyMaintenanceSettingRequest):
            return True

        return self.to_dict() != other.to_dict()
