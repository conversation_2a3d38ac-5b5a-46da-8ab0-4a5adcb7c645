# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListResourceGroupsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_type': 'str',
        'description': 'str',
        'expire_time': 'str',
        'id': 'str',
        'name': 'str',
        'period_unit': 'str',
        'resource_allocated': 'ResourceAllocatedForListResourceGroupsOutput',
        'resource_capability': 'ResourceCapabilityForListResourceGroupsOutput',
        'status': 'StatusForListResourceGroupsOutput',
        'storage_config': 'StorageConfigForListResourceGroupsOutput',
        'workload_network_config': 'WorkloadNetworkConfigForListResourceGroupsOutput',
        'workload_network_mode': 'str',
        'zone_ids': 'list[str]'
    }

    attribute_map = {
        'charge_type': 'ChargeType',
        'description': 'Description',
        'expire_time': 'ExpireTime',
        'id': 'Id',
        'name': 'Name',
        'period_unit': 'PeriodUnit',
        'resource_allocated': 'ResourceAllocated',
        'resource_capability': 'ResourceCapability',
        'status': 'Status',
        'storage_config': 'StorageConfig',
        'workload_network_config': 'WorkloadNetworkConfig',
        'workload_network_mode': 'WorkloadNetworkMode',
        'zone_ids': 'ZoneIds'
    }

    def __init__(self, charge_type=None, description=None, expire_time=None, id=None, name=None, period_unit=None, resource_allocated=None, resource_capability=None, status=None, storage_config=None, workload_network_config=None, workload_network_mode=None, zone_ids=None, _configuration=None):  # noqa: E501
        """ItemForListResourceGroupsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_type = None
        self._description = None
        self._expire_time = None
        self._id = None
        self._name = None
        self._period_unit = None
        self._resource_allocated = None
        self._resource_capability = None
        self._status = None
        self._storage_config = None
        self._workload_network_config = None
        self._workload_network_mode = None
        self._zone_ids = None
        self.discriminator = None

        if charge_type is not None:
            self.charge_type = charge_type
        if description is not None:
            self.description = description
        if expire_time is not None:
            self.expire_time = expire_time
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if period_unit is not None:
            self.period_unit = period_unit
        if resource_allocated is not None:
            self.resource_allocated = resource_allocated
        if resource_capability is not None:
            self.resource_capability = resource_capability
        if status is not None:
            self.status = status
        if storage_config is not None:
            self.storage_config = storage_config
        if workload_network_config is not None:
            self.workload_network_config = workload_network_config
        if workload_network_mode is not None:
            self.workload_network_mode = workload_network_mode
        if zone_ids is not None:
            self.zone_ids = zone_ids

    @property
    def charge_type(self):
        """Gets the charge_type of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The charge_type of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ItemForListResourceGroupsOutput.


        :param charge_type: The charge_type of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def description(self):
        """Gets the description of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The description of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ItemForListResourceGroupsOutput.


        :param description: The description of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def expire_time(self):
        """Gets the expire_time of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The expire_time of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this ItemForListResourceGroupsOutput.


        :param expire_time: The expire_time of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._expire_time = expire_time

    @property
    def id(self):
        """Gets the id of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The id of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListResourceGroupsOutput.


        :param id: The id of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The name of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListResourceGroupsOutput.


        :param name: The name of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def period_unit(self):
        """Gets the period_unit of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The period_unit of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this ItemForListResourceGroupsOutput.


        :param period_unit: The period_unit of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._period_unit = period_unit

    @property
    def resource_allocated(self):
        """Gets the resource_allocated of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The resource_allocated of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: ResourceAllocatedForListResourceGroupsOutput
        """
        return self._resource_allocated

    @resource_allocated.setter
    def resource_allocated(self, resource_allocated):
        """Sets the resource_allocated of this ItemForListResourceGroupsOutput.


        :param resource_allocated: The resource_allocated of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: ResourceAllocatedForListResourceGroupsOutput
        """

        self._resource_allocated = resource_allocated

    @property
    def resource_capability(self):
        """Gets the resource_capability of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The resource_capability of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: ResourceCapabilityForListResourceGroupsOutput
        """
        return self._resource_capability

    @resource_capability.setter
    def resource_capability(self, resource_capability):
        """Sets the resource_capability of this ItemForListResourceGroupsOutput.


        :param resource_capability: The resource_capability of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: ResourceCapabilityForListResourceGroupsOutput
        """

        self._resource_capability = resource_capability

    @property
    def status(self):
        """Gets the status of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The status of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: StatusForListResourceGroupsOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListResourceGroupsOutput.


        :param status: The status of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: StatusForListResourceGroupsOutput
        """

        self._status = status

    @property
    def storage_config(self):
        """Gets the storage_config of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The storage_config of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: StorageConfigForListResourceGroupsOutput
        """
        return self._storage_config

    @storage_config.setter
    def storage_config(self, storage_config):
        """Sets the storage_config of this ItemForListResourceGroupsOutput.


        :param storage_config: The storage_config of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: StorageConfigForListResourceGroupsOutput
        """

        self._storage_config = storage_config

    @property
    def workload_network_config(self):
        """Gets the workload_network_config of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The workload_network_config of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: WorkloadNetworkConfigForListResourceGroupsOutput
        """
        return self._workload_network_config

    @workload_network_config.setter
    def workload_network_config(self, workload_network_config):
        """Sets the workload_network_config of this ItemForListResourceGroupsOutput.


        :param workload_network_config: The workload_network_config of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: WorkloadNetworkConfigForListResourceGroupsOutput
        """

        self._workload_network_config = workload_network_config

    @property
    def workload_network_mode(self):
        """Gets the workload_network_mode of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The workload_network_mode of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: str
        """
        return self._workload_network_mode

    @workload_network_mode.setter
    def workload_network_mode(self, workload_network_mode):
        """Sets the workload_network_mode of this ItemForListResourceGroupsOutput.


        :param workload_network_mode: The workload_network_mode of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: str
        """

        self._workload_network_mode = workload_network_mode

    @property
    def zone_ids(self):
        """Gets the zone_ids of this ItemForListResourceGroupsOutput.  # noqa: E501


        :return: The zone_ids of this ItemForListResourceGroupsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._zone_ids

    @zone_ids.setter
    def zone_ids(self, zone_ids):
        """Sets the zone_ids of this ItemForListResourceGroupsOutput.


        :param zone_ids: The zone_ids of this ItemForListResourceGroupsOutput.  # noqa: E501
        :type: list[str]
        """

        self._zone_ids = zone_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListResourceGroupsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListResourceGroupsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListResourceGroupsOutput):
            return True

        return self.to_dict() != other.to_dict()
