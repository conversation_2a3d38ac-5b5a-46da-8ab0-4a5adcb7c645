# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RedPacketListForListActivityRedPacketOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'award_condition': 'AwardConditionForListActivityRedPacketOutput',
        'barrage_pwd': 'str',
        'black_user_info_id': 'int',
        'claim_failure_text': 'str',
        'claim_success_text': 'str',
        'custom_redirect_url': 'str',
        'dead_line': 'int',
        'enable_custom_redirect': 'bool',
        'enable_mini_app': 'bool',
        'is_open_sms_notification': 'int',
        'mini_app_id': 'str',
        'mini_app_redirect_url': 'str',
        'open_red_packet_type': 'int',
        'range_status': 'int',
        'red_packet_cover_image': 'str',
        'red_packet_id': 'int',
        'red_packet_number': 'int',
        'red_packet_status': 'int',
        'red_packet_type': 'int',
        'repeated_award_limit': 'int',
        'rewards_points_config': 'RewardsPointsConfigForListActivityRedPacketOutput',
        'total_amount': 'int',
        'vip_user_info_id': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityID',
        'award_condition': 'AwardCondition',
        'barrage_pwd': 'BarragePwd',
        'black_user_info_id': 'BlackUserInfoId',
        'claim_failure_text': 'ClaimFailureText',
        'claim_success_text': 'ClaimSuccessText',
        'custom_redirect_url': 'CustomRedirectUrl',
        'dead_line': 'DeadLine',
        'enable_custom_redirect': 'EnableCustomRedirect',
        'enable_mini_app': 'EnableMiniApp',
        'is_open_sms_notification': 'IsOpenSmsNotification',
        'mini_app_id': 'MiniAppId',
        'mini_app_redirect_url': 'MiniAppRedirectUrl',
        'open_red_packet_type': 'OpenRedPacketType',
        'range_status': 'RangeStatus',
        'red_packet_cover_image': 'RedPacketCoverImage',
        'red_packet_id': 'RedPacketID',
        'red_packet_number': 'RedPacketNumber',
        'red_packet_status': 'RedPacketStatus',
        'red_packet_type': 'RedPacketType',
        'repeated_award_limit': 'RepeatedAwardLimit',
        'rewards_points_config': 'RewardsPointsConfig',
        'total_amount': 'TotalAmount',
        'vip_user_info_id': 'VipUserInfoId'
    }

    def __init__(self, activity_id=None, award_condition=None, barrage_pwd=None, black_user_info_id=None, claim_failure_text=None, claim_success_text=None, custom_redirect_url=None, dead_line=None, enable_custom_redirect=None, enable_mini_app=None, is_open_sms_notification=None, mini_app_id=None, mini_app_redirect_url=None, open_red_packet_type=None, range_status=None, red_packet_cover_image=None, red_packet_id=None, red_packet_number=None, red_packet_status=None, red_packet_type=None, repeated_award_limit=None, rewards_points_config=None, total_amount=None, vip_user_info_id=None, _configuration=None):  # noqa: E501
        """RedPacketListForListActivityRedPacketOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._award_condition = None
        self._barrage_pwd = None
        self._black_user_info_id = None
        self._claim_failure_text = None
        self._claim_success_text = None
        self._custom_redirect_url = None
        self._dead_line = None
        self._enable_custom_redirect = None
        self._enable_mini_app = None
        self._is_open_sms_notification = None
        self._mini_app_id = None
        self._mini_app_redirect_url = None
        self._open_red_packet_type = None
        self._range_status = None
        self._red_packet_cover_image = None
        self._red_packet_id = None
        self._red_packet_number = None
        self._red_packet_status = None
        self._red_packet_type = None
        self._repeated_award_limit = None
        self._rewards_points_config = None
        self._total_amount = None
        self._vip_user_info_id = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if award_condition is not None:
            self.award_condition = award_condition
        if barrage_pwd is not None:
            self.barrage_pwd = barrage_pwd
        if black_user_info_id is not None:
            self.black_user_info_id = black_user_info_id
        if claim_failure_text is not None:
            self.claim_failure_text = claim_failure_text
        if claim_success_text is not None:
            self.claim_success_text = claim_success_text
        if custom_redirect_url is not None:
            self.custom_redirect_url = custom_redirect_url
        if dead_line is not None:
            self.dead_line = dead_line
        if enable_custom_redirect is not None:
            self.enable_custom_redirect = enable_custom_redirect
        if enable_mini_app is not None:
            self.enable_mini_app = enable_mini_app
        if is_open_sms_notification is not None:
            self.is_open_sms_notification = is_open_sms_notification
        if mini_app_id is not None:
            self.mini_app_id = mini_app_id
        if mini_app_redirect_url is not None:
            self.mini_app_redirect_url = mini_app_redirect_url
        if open_red_packet_type is not None:
            self.open_red_packet_type = open_red_packet_type
        if range_status is not None:
            self.range_status = range_status
        if red_packet_cover_image is not None:
            self.red_packet_cover_image = red_packet_cover_image
        if red_packet_id is not None:
            self.red_packet_id = red_packet_id
        if red_packet_number is not None:
            self.red_packet_number = red_packet_number
        if red_packet_status is not None:
            self.red_packet_status = red_packet_status
        if red_packet_type is not None:
            self.red_packet_type = red_packet_type
        if repeated_award_limit is not None:
            self.repeated_award_limit = repeated_award_limit
        if rewards_points_config is not None:
            self.rewards_points_config = rewards_points_config
        if total_amount is not None:
            self.total_amount = total_amount
        if vip_user_info_id is not None:
            self.vip_user_info_id = vip_user_info_id

    @property
    def activity_id(self):
        """Gets the activity_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The activity_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this RedPacketListForListActivityRedPacketOutput.


        :param activity_id: The activity_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def award_condition(self):
        """Gets the award_condition of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The award_condition of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: AwardConditionForListActivityRedPacketOutput
        """
        return self._award_condition

    @award_condition.setter
    def award_condition(self, award_condition):
        """Sets the award_condition of this RedPacketListForListActivityRedPacketOutput.


        :param award_condition: The award_condition of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: AwardConditionForListActivityRedPacketOutput
        """

        self._award_condition = award_condition

    @property
    def barrage_pwd(self):
        """Gets the barrage_pwd of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The barrage_pwd of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: str
        """
        return self._barrage_pwd

    @barrage_pwd.setter
    def barrage_pwd(self, barrage_pwd):
        """Sets the barrage_pwd of this RedPacketListForListActivityRedPacketOutput.


        :param barrage_pwd: The barrage_pwd of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: str
        """

        self._barrage_pwd = barrage_pwd

    @property
    def black_user_info_id(self):
        """Gets the black_user_info_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The black_user_info_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._black_user_info_id

    @black_user_info_id.setter
    def black_user_info_id(self, black_user_info_id):
        """Sets the black_user_info_id of this RedPacketListForListActivityRedPacketOutput.


        :param black_user_info_id: The black_user_info_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._black_user_info_id = black_user_info_id

    @property
    def claim_failure_text(self):
        """Gets the claim_failure_text of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The claim_failure_text of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: str
        """
        return self._claim_failure_text

    @claim_failure_text.setter
    def claim_failure_text(self, claim_failure_text):
        """Sets the claim_failure_text of this RedPacketListForListActivityRedPacketOutput.


        :param claim_failure_text: The claim_failure_text of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: str
        """

        self._claim_failure_text = claim_failure_text

    @property
    def claim_success_text(self):
        """Gets the claim_success_text of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The claim_success_text of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: str
        """
        return self._claim_success_text

    @claim_success_text.setter
    def claim_success_text(self, claim_success_text):
        """Sets the claim_success_text of this RedPacketListForListActivityRedPacketOutput.


        :param claim_success_text: The claim_success_text of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: str
        """

        self._claim_success_text = claim_success_text

    @property
    def custom_redirect_url(self):
        """Gets the custom_redirect_url of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The custom_redirect_url of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: str
        """
        return self._custom_redirect_url

    @custom_redirect_url.setter
    def custom_redirect_url(self, custom_redirect_url):
        """Sets the custom_redirect_url of this RedPacketListForListActivityRedPacketOutput.


        :param custom_redirect_url: The custom_redirect_url of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: str
        """

        self._custom_redirect_url = custom_redirect_url

    @property
    def dead_line(self):
        """Gets the dead_line of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The dead_line of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._dead_line

    @dead_line.setter
    def dead_line(self, dead_line):
        """Sets the dead_line of this RedPacketListForListActivityRedPacketOutput.


        :param dead_line: The dead_line of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._dead_line = dead_line

    @property
    def enable_custom_redirect(self):
        """Gets the enable_custom_redirect of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The enable_custom_redirect of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_custom_redirect

    @enable_custom_redirect.setter
    def enable_custom_redirect(self, enable_custom_redirect):
        """Sets the enable_custom_redirect of this RedPacketListForListActivityRedPacketOutput.


        :param enable_custom_redirect: The enable_custom_redirect of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: bool
        """

        self._enable_custom_redirect = enable_custom_redirect

    @property
    def enable_mini_app(self):
        """Gets the enable_mini_app of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The enable_mini_app of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enable_mini_app

    @enable_mini_app.setter
    def enable_mini_app(self, enable_mini_app):
        """Sets the enable_mini_app of this RedPacketListForListActivityRedPacketOutput.


        :param enable_mini_app: The enable_mini_app of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: bool
        """

        self._enable_mini_app = enable_mini_app

    @property
    def is_open_sms_notification(self):
        """Gets the is_open_sms_notification of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The is_open_sms_notification of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_open_sms_notification

    @is_open_sms_notification.setter
    def is_open_sms_notification(self, is_open_sms_notification):
        """Sets the is_open_sms_notification of this RedPacketListForListActivityRedPacketOutput.


        :param is_open_sms_notification: The is_open_sms_notification of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._is_open_sms_notification = is_open_sms_notification

    @property
    def mini_app_id(self):
        """Gets the mini_app_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The mini_app_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: str
        """
        return self._mini_app_id

    @mini_app_id.setter
    def mini_app_id(self, mini_app_id):
        """Sets the mini_app_id of this RedPacketListForListActivityRedPacketOutput.


        :param mini_app_id: The mini_app_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: str
        """

        self._mini_app_id = mini_app_id

    @property
    def mini_app_redirect_url(self):
        """Gets the mini_app_redirect_url of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The mini_app_redirect_url of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: str
        """
        return self._mini_app_redirect_url

    @mini_app_redirect_url.setter
    def mini_app_redirect_url(self, mini_app_redirect_url):
        """Sets the mini_app_redirect_url of this RedPacketListForListActivityRedPacketOutput.


        :param mini_app_redirect_url: The mini_app_redirect_url of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: str
        """

        self._mini_app_redirect_url = mini_app_redirect_url

    @property
    def open_red_packet_type(self):
        """Gets the open_red_packet_type of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The open_red_packet_type of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._open_red_packet_type

    @open_red_packet_type.setter
    def open_red_packet_type(self, open_red_packet_type):
        """Sets the open_red_packet_type of this RedPacketListForListActivityRedPacketOutput.


        :param open_red_packet_type: The open_red_packet_type of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._open_red_packet_type = open_red_packet_type

    @property
    def range_status(self):
        """Gets the range_status of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The range_status of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._range_status

    @range_status.setter
    def range_status(self, range_status):
        """Sets the range_status of this RedPacketListForListActivityRedPacketOutput.


        :param range_status: The range_status of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._range_status = range_status

    @property
    def red_packet_cover_image(self):
        """Gets the red_packet_cover_image of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The red_packet_cover_image of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: str
        """
        return self._red_packet_cover_image

    @red_packet_cover_image.setter
    def red_packet_cover_image(self, red_packet_cover_image):
        """Sets the red_packet_cover_image of this RedPacketListForListActivityRedPacketOutput.


        :param red_packet_cover_image: The red_packet_cover_image of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: str
        """

        self._red_packet_cover_image = red_packet_cover_image

    @property
    def red_packet_id(self):
        """Gets the red_packet_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The red_packet_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._red_packet_id

    @red_packet_id.setter
    def red_packet_id(self, red_packet_id):
        """Sets the red_packet_id of this RedPacketListForListActivityRedPacketOutput.


        :param red_packet_id: The red_packet_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._red_packet_id = red_packet_id

    @property
    def red_packet_number(self):
        """Gets the red_packet_number of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The red_packet_number of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._red_packet_number

    @red_packet_number.setter
    def red_packet_number(self, red_packet_number):
        """Sets the red_packet_number of this RedPacketListForListActivityRedPacketOutput.


        :param red_packet_number: The red_packet_number of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._red_packet_number = red_packet_number

    @property
    def red_packet_status(self):
        """Gets the red_packet_status of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The red_packet_status of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._red_packet_status

    @red_packet_status.setter
    def red_packet_status(self, red_packet_status):
        """Sets the red_packet_status of this RedPacketListForListActivityRedPacketOutput.


        :param red_packet_status: The red_packet_status of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._red_packet_status = red_packet_status

    @property
    def red_packet_type(self):
        """Gets the red_packet_type of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The red_packet_type of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._red_packet_type

    @red_packet_type.setter
    def red_packet_type(self, red_packet_type):
        """Sets the red_packet_type of this RedPacketListForListActivityRedPacketOutput.


        :param red_packet_type: The red_packet_type of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._red_packet_type = red_packet_type

    @property
    def repeated_award_limit(self):
        """Gets the repeated_award_limit of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The repeated_award_limit of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._repeated_award_limit

    @repeated_award_limit.setter
    def repeated_award_limit(self, repeated_award_limit):
        """Sets the repeated_award_limit of this RedPacketListForListActivityRedPacketOutput.


        :param repeated_award_limit: The repeated_award_limit of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._repeated_award_limit = repeated_award_limit

    @property
    def rewards_points_config(self):
        """Gets the rewards_points_config of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The rewards_points_config of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: RewardsPointsConfigForListActivityRedPacketOutput
        """
        return self._rewards_points_config

    @rewards_points_config.setter
    def rewards_points_config(self, rewards_points_config):
        """Sets the rewards_points_config of this RedPacketListForListActivityRedPacketOutput.


        :param rewards_points_config: The rewards_points_config of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: RewardsPointsConfigForListActivityRedPacketOutput
        """

        self._rewards_points_config = rewards_points_config

    @property
    def total_amount(self):
        """Gets the total_amount of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The total_amount of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_amount

    @total_amount.setter
    def total_amount(self, total_amount):
        """Sets the total_amount of this RedPacketListForListActivityRedPacketOutput.


        :param total_amount: The total_amount of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._total_amount = total_amount

    @property
    def vip_user_info_id(self):
        """Gets the vip_user_info_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501


        :return: The vip_user_info_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :rtype: int
        """
        return self._vip_user_info_id

    @vip_user_info_id.setter
    def vip_user_info_id(self, vip_user_info_id):
        """Sets the vip_user_info_id of this RedPacketListForListActivityRedPacketOutput.


        :param vip_user_info_id: The vip_user_info_id of this RedPacketListForListActivityRedPacketOutput.  # noqa: E501
        :type: int
        """

        self._vip_user_info_id = vip_user_info_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RedPacketListForListActivityRedPacketOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RedPacketListForListActivityRedPacketOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RedPacketListForListActivityRedPacketOutput):
            return True

        return self.to_dict() != other.to_dict()
