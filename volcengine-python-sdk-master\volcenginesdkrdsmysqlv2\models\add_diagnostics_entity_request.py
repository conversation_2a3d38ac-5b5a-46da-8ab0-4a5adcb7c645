# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AddDiagnosticsEntityRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'diagnostics_type': 'str',
        'ecs_instance_ids': 'list[str]',
        'endpoint_id': 'str',
        'instance_id': 'str',
        'public_ip_addresses': 'list[str]'
    }

    attribute_map = {
        'diagnostics_type': 'DiagnosticsType',
        'ecs_instance_ids': 'EcsInstanceIds',
        'endpoint_id': 'EndpointId',
        'instance_id': 'InstanceId',
        'public_ip_addresses': 'PublicIpAddresses'
    }

    def __init__(self, diagnostics_type=None, ecs_instance_ids=None, endpoint_id=None, instance_id=None, public_ip_addresses=None, _configuration=None):  # noqa: E501
        """AddDiagnosticsEntityRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._diagnostics_type = None
        self._ecs_instance_ids = None
        self._endpoint_id = None
        self._instance_id = None
        self._public_ip_addresses = None
        self.discriminator = None

        if diagnostics_type is not None:
            self.diagnostics_type = diagnostics_type
        if ecs_instance_ids is not None:
            self.ecs_instance_ids = ecs_instance_ids
        self.endpoint_id = endpoint_id
        self.instance_id = instance_id
        if public_ip_addresses is not None:
            self.public_ip_addresses = public_ip_addresses

    @property
    def diagnostics_type(self):
        """Gets the diagnostics_type of this AddDiagnosticsEntityRequest.  # noqa: E501


        :return: The diagnostics_type of this AddDiagnosticsEntityRequest.  # noqa: E501
        :rtype: str
        """
        return self._diagnostics_type

    @diagnostics_type.setter
    def diagnostics_type(self, diagnostics_type):
        """Sets the diagnostics_type of this AddDiagnosticsEntityRequest.


        :param diagnostics_type: The diagnostics_type of this AddDiagnosticsEntityRequest.  # noqa: E501
        :type: str
        """

        self._diagnostics_type = diagnostics_type

    @property
    def ecs_instance_ids(self):
        """Gets the ecs_instance_ids of this AddDiagnosticsEntityRequest.  # noqa: E501


        :return: The ecs_instance_ids of this AddDiagnosticsEntityRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._ecs_instance_ids

    @ecs_instance_ids.setter
    def ecs_instance_ids(self, ecs_instance_ids):
        """Sets the ecs_instance_ids of this AddDiagnosticsEntityRequest.


        :param ecs_instance_ids: The ecs_instance_ids of this AddDiagnosticsEntityRequest.  # noqa: E501
        :type: list[str]
        """

        self._ecs_instance_ids = ecs_instance_ids

    @property
    def endpoint_id(self):
        """Gets the endpoint_id of this AddDiagnosticsEntityRequest.  # noqa: E501


        :return: The endpoint_id of this AddDiagnosticsEntityRequest.  # noqa: E501
        :rtype: str
        """
        return self._endpoint_id

    @endpoint_id.setter
    def endpoint_id(self, endpoint_id):
        """Sets the endpoint_id of this AddDiagnosticsEntityRequest.


        :param endpoint_id: The endpoint_id of this AddDiagnosticsEntityRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and endpoint_id is None:
            raise ValueError("Invalid value for `endpoint_id`, must not be `None`")  # noqa: E501

        self._endpoint_id = endpoint_id

    @property
    def instance_id(self):
        """Gets the instance_id of this AddDiagnosticsEntityRequest.  # noqa: E501


        :return: The instance_id of this AddDiagnosticsEntityRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this AddDiagnosticsEntityRequest.


        :param instance_id: The instance_id of this AddDiagnosticsEntityRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def public_ip_addresses(self):
        """Gets the public_ip_addresses of this AddDiagnosticsEntityRequest.  # noqa: E501


        :return: The public_ip_addresses of this AddDiagnosticsEntityRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._public_ip_addresses

    @public_ip_addresses.setter
    def public_ip_addresses(self, public_ip_addresses):
        """Sets the public_ip_addresses of this AddDiagnosticsEntityRequest.


        :param public_ip_addresses: The public_ip_addresses of this AddDiagnosticsEntityRequest.  # noqa: E501
        :type: list[str]
        """

        self._public_ip_addresses = public_ip_addresses

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddDiagnosticsEntityRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddDiagnosticsEntityRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AddDiagnosticsEntityRequest):
            return True

        return self.to_dict() != other.to_dict()
