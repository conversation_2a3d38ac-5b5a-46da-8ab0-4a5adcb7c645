# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FailedHostForGetBaselineDetectProgressDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'failed_reason': 'str',
        'host_name': 'str'
    }

    attribute_map = {
        'failed_reason': 'FailedReason',
        'host_name': 'HostName'
    }

    def __init__(self, failed_reason=None, host_name=None, _configuration=None):  # noqa: E501
        """FailedHostForGetBaselineDetectProgressDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._failed_reason = None
        self._host_name = None
        self.discriminator = None

        if failed_reason is not None:
            self.failed_reason = failed_reason
        if host_name is not None:
            self.host_name = host_name

    @property
    def failed_reason(self):
        """Gets the failed_reason of this FailedHostForGetBaselineDetectProgressDetailOutput.  # noqa: E501


        :return: The failed_reason of this FailedHostForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._failed_reason

    @failed_reason.setter
    def failed_reason(self, failed_reason):
        """Sets the failed_reason of this FailedHostForGetBaselineDetectProgressDetailOutput.


        :param failed_reason: The failed_reason of this FailedHostForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :type: str
        """

        self._failed_reason = failed_reason

    @property
    def host_name(self):
        """Gets the host_name of this FailedHostForGetBaselineDetectProgressDetailOutput.  # noqa: E501


        :return: The host_name of this FailedHostForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._host_name

    @host_name.setter
    def host_name(self, host_name):
        """Sets the host_name of this FailedHostForGetBaselineDetectProgressDetailOutput.


        :param host_name: The host_name of this FailedHostForGetBaselineDetectProgressDetailOutput.  # noqa: E501
        :type: str
        """

        self._host_name = host_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FailedHostForGetBaselineDetectProgressDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FailedHostForGetBaselineDetectProgressDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FailedHostForGetBaselineDetectProgressDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
