# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAdvertisementDataAPIV2Response(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_clicks': 'AccountClicksForGetAdvertisementDataAPIV2Output',
        'activity_id': 'int',
        'actual_thumb_up_number': 'int',
        'ad_info': 'list[AdInfoForGetAdvertisementDataAPIV2Output]',
        'advertising_clicks': 'AdvertisingClicksForGetAdvertisementDataAPIV2Output',
        'show_thumb_up_number': 'int'
    }

    attribute_map = {
        'account_clicks': 'AccountClicks',
        'activity_id': 'ActivityId',
        'actual_thumb_up_number': 'ActualThumbUpNumber',
        'ad_info': 'AdInfo',
        'advertising_clicks': 'AdvertisingClicks',
        'show_thumb_up_number': 'ShowThumbUpNumber'
    }

    def __init__(self, account_clicks=None, activity_id=None, actual_thumb_up_number=None, ad_info=None, advertising_clicks=None, show_thumb_up_number=None, _configuration=None):  # noqa: E501
        """GetAdvertisementDataAPIV2Response - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_clicks = None
        self._activity_id = None
        self._actual_thumb_up_number = None
        self._ad_info = None
        self._advertising_clicks = None
        self._show_thumb_up_number = None
        self.discriminator = None

        if account_clicks is not None:
            self.account_clicks = account_clicks
        if activity_id is not None:
            self.activity_id = activity_id
        if actual_thumb_up_number is not None:
            self.actual_thumb_up_number = actual_thumb_up_number
        if ad_info is not None:
            self.ad_info = ad_info
        if advertising_clicks is not None:
            self.advertising_clicks = advertising_clicks
        if show_thumb_up_number is not None:
            self.show_thumb_up_number = show_thumb_up_number

    @property
    def account_clicks(self):
        """Gets the account_clicks of this GetAdvertisementDataAPIV2Response.  # noqa: E501


        :return: The account_clicks of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :rtype: AccountClicksForGetAdvertisementDataAPIV2Output
        """
        return self._account_clicks

    @account_clicks.setter
    def account_clicks(self, account_clicks):
        """Sets the account_clicks of this GetAdvertisementDataAPIV2Response.


        :param account_clicks: The account_clicks of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :type: AccountClicksForGetAdvertisementDataAPIV2Output
        """

        self._account_clicks = account_clicks

    @property
    def activity_id(self):
        """Gets the activity_id of this GetAdvertisementDataAPIV2Response.  # noqa: E501


        :return: The activity_id of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetAdvertisementDataAPIV2Response.


        :param activity_id: The activity_id of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def actual_thumb_up_number(self):
        """Gets the actual_thumb_up_number of this GetAdvertisementDataAPIV2Response.  # noqa: E501


        :return: The actual_thumb_up_number of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :rtype: int
        """
        return self._actual_thumb_up_number

    @actual_thumb_up_number.setter
    def actual_thumb_up_number(self, actual_thumb_up_number):
        """Sets the actual_thumb_up_number of this GetAdvertisementDataAPIV2Response.


        :param actual_thumb_up_number: The actual_thumb_up_number of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :type: int
        """

        self._actual_thumb_up_number = actual_thumb_up_number

    @property
    def ad_info(self):
        """Gets the ad_info of this GetAdvertisementDataAPIV2Response.  # noqa: E501


        :return: The ad_info of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :rtype: list[AdInfoForGetAdvertisementDataAPIV2Output]
        """
        return self._ad_info

    @ad_info.setter
    def ad_info(self, ad_info):
        """Sets the ad_info of this GetAdvertisementDataAPIV2Response.


        :param ad_info: The ad_info of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :type: list[AdInfoForGetAdvertisementDataAPIV2Output]
        """

        self._ad_info = ad_info

    @property
    def advertising_clicks(self):
        """Gets the advertising_clicks of this GetAdvertisementDataAPIV2Response.  # noqa: E501


        :return: The advertising_clicks of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :rtype: AdvertisingClicksForGetAdvertisementDataAPIV2Output
        """
        return self._advertising_clicks

    @advertising_clicks.setter
    def advertising_clicks(self, advertising_clicks):
        """Sets the advertising_clicks of this GetAdvertisementDataAPIV2Response.


        :param advertising_clicks: The advertising_clicks of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :type: AdvertisingClicksForGetAdvertisementDataAPIV2Output
        """

        self._advertising_clicks = advertising_clicks

    @property
    def show_thumb_up_number(self):
        """Gets the show_thumb_up_number of this GetAdvertisementDataAPIV2Response.  # noqa: E501


        :return: The show_thumb_up_number of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :rtype: int
        """
        return self._show_thumb_up_number

    @show_thumb_up_number.setter
    def show_thumb_up_number(self, show_thumb_up_number):
        """Sets the show_thumb_up_number of this GetAdvertisementDataAPIV2Response.


        :param show_thumb_up_number: The show_thumb_up_number of this GetAdvertisementDataAPIV2Response.  # noqa: E501
        :type: int
        """

        self._show_thumb_up_number = show_thumb_up_number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAdvertisementDataAPIV2Response, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAdvertisementDataAPIV2Response):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAdvertisementDataAPIV2Response):
            return True

        return self.to_dict() != other.to_dict()
