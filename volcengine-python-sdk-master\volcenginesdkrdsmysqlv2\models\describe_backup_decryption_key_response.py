# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeBackupDecryptionKeyResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'decryption_key': 'str',
        'iv': 'str'
    }

    attribute_map = {
        'decryption_key': 'DecryptionKey',
        'iv': 'Iv'
    }

    def __init__(self, decryption_key=None, iv=None, _configuration=None):  # noqa: E501
        """DescribeBackupDecryptionKeyResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._decryption_key = None
        self._iv = None
        self.discriminator = None

        if decryption_key is not None:
            self.decryption_key = decryption_key
        if iv is not None:
            self.iv = iv

    @property
    def decryption_key(self):
        """Gets the decryption_key of this DescribeBackupDecryptionKeyResponse.  # noqa: E501


        :return: The decryption_key of this DescribeBackupDecryptionKeyResponse.  # noqa: E501
        :rtype: str
        """
        return self._decryption_key

    @decryption_key.setter
    def decryption_key(self, decryption_key):
        """Sets the decryption_key of this DescribeBackupDecryptionKeyResponse.


        :param decryption_key: The decryption_key of this DescribeBackupDecryptionKeyResponse.  # noqa: E501
        :type: str
        """

        self._decryption_key = decryption_key

    @property
    def iv(self):
        """Gets the iv of this DescribeBackupDecryptionKeyResponse.  # noqa: E501


        :return: The iv of this DescribeBackupDecryptionKeyResponse.  # noqa: E501
        :rtype: str
        """
        return self._iv

    @iv.setter
    def iv(self, iv):
        """Sets the iv of this DescribeBackupDecryptionKeyResponse.


        :param iv: The iv of this DescribeBackupDecryptionKeyResponse.  # noqa: E501
        :type: str
        """

        self._iv = iv

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeBackupDecryptionKeyResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeBackupDecryptionKeyResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeBackupDecryptionKeyResponse):
            return True

        return self.to_dict() != other.to_dict()
