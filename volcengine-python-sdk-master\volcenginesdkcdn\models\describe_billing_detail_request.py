# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeBillingDetailRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'billing_region': 'str',
        'domain': 'str',
        'end_time': 'int',
        'interval': 'str',
        'metric': 'str',
        'project': 'str',
        'start_time': 'int',
        'time_zone': 'str'
    }

    attribute_map = {
        'billing_region': 'BillingRegion',
        'domain': 'Domain',
        'end_time': 'EndTime',
        'interval': 'Interval',
        'metric': 'Metric',
        'project': 'Project',
        'start_time': 'StartTime',
        'time_zone': 'TimeZone'
    }

    def __init__(self, billing_region=None, domain=None, end_time=None, interval=None, metric=None, project=None, start_time=None, time_zone=None, _configuration=None):  # noqa: E501
        """DescribeBillingDetailRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._billing_region = None
        self._domain = None
        self._end_time = None
        self._interval = None
        self._metric = None
        self._project = None
        self._start_time = None
        self._time_zone = None
        self.discriminator = None

        if billing_region is not None:
            self.billing_region = billing_region
        if domain is not None:
            self.domain = domain
        self.end_time = end_time
        if interval is not None:
            self.interval = interval
        self.metric = metric
        if project is not None:
            self.project = project
        self.start_time = start_time
        if time_zone is not None:
            self.time_zone = time_zone

    @property
    def billing_region(self):
        """Gets the billing_region of this DescribeBillingDetailRequest.  # noqa: E501


        :return: The billing_region of this DescribeBillingDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._billing_region

    @billing_region.setter
    def billing_region(self, billing_region):
        """Sets the billing_region of this DescribeBillingDetailRequest.


        :param billing_region: The billing_region of this DescribeBillingDetailRequest.  # noqa: E501
        :type: str
        """

        self._billing_region = billing_region

    @property
    def domain(self):
        """Gets the domain of this DescribeBillingDetailRequest.  # noqa: E501


        :return: The domain of this DescribeBillingDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._domain

    @domain.setter
    def domain(self, domain):
        """Sets the domain of this DescribeBillingDetailRequest.


        :param domain: The domain of this DescribeBillingDetailRequest.  # noqa: E501
        :type: str
        """

        self._domain = domain

    @property
    def end_time(self):
        """Gets the end_time of this DescribeBillingDetailRequest.  # noqa: E501


        :return: The end_time of this DescribeBillingDetailRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeBillingDetailRequest.


        :param end_time: The end_time of this DescribeBillingDetailRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def interval(self):
        """Gets the interval of this DescribeBillingDetailRequest.  # noqa: E501


        :return: The interval of this DescribeBillingDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._interval

    @interval.setter
    def interval(self, interval):
        """Sets the interval of this DescribeBillingDetailRequest.


        :param interval: The interval of this DescribeBillingDetailRequest.  # noqa: E501
        :type: str
        """

        self._interval = interval

    @property
    def metric(self):
        """Gets the metric of this DescribeBillingDetailRequest.  # noqa: E501


        :return: The metric of this DescribeBillingDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._metric

    @metric.setter
    def metric(self, metric):
        """Sets the metric of this DescribeBillingDetailRequest.


        :param metric: The metric of this DescribeBillingDetailRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and metric is None:
            raise ValueError("Invalid value for `metric`, must not be `None`")  # noqa: E501

        self._metric = metric

    @property
    def project(self):
        """Gets the project of this DescribeBillingDetailRequest.  # noqa: E501


        :return: The project of this DescribeBillingDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this DescribeBillingDetailRequest.


        :param project: The project of this DescribeBillingDetailRequest.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def start_time(self):
        """Gets the start_time of this DescribeBillingDetailRequest.  # noqa: E501


        :return: The start_time of this DescribeBillingDetailRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DescribeBillingDetailRequest.


        :param start_time: The start_time of this DescribeBillingDetailRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and start_time is None:
            raise ValueError("Invalid value for `start_time`, must not be `None`")  # noqa: E501

        self._start_time = start_time

    @property
    def time_zone(self):
        """Gets the time_zone of this DescribeBillingDetailRequest.  # noqa: E501


        :return: The time_zone of this DescribeBillingDetailRequest.  # noqa: E501
        :rtype: str
        """
        return self._time_zone

    @time_zone.setter
    def time_zone(self, time_zone):
        """Sets the time_zone of this DescribeBillingDetailRequest.


        :param time_zone: The time_zone of this DescribeBillingDetailRequest.  # noqa: E501
        :type: str
        """

        self._time_zone = time_zone

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeBillingDetailRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeBillingDetailRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeBillingDetailRequest):
            return True

        return self.to_dict() != other.to_dict()
