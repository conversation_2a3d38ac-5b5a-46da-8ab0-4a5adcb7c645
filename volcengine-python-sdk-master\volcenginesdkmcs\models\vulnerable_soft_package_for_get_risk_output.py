# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VulnerableSoftPackageForGetRiskOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'container_env': 'ContainerEnvForGetRiskOutput',
        'img_id': 'str',
        'img_name': 'str',
        'os_proc': 'OSProcForGetRiskOutput',
        'vuln_pkg_name': 'str',
        'vuln_pkg_path': 'str',
        'vuln_pkg_ver': 'str'
    }

    attribute_map = {
        'container_env': 'ContainerEnv',
        'img_id': 'ImgID',
        'img_name': 'ImgName',
        'os_proc': 'OSProc',
        'vuln_pkg_name': 'VulnPkgName',
        'vuln_pkg_path': 'VulnPkgPath',
        'vuln_pkg_ver': 'VulnPkgVer'
    }

    def __init__(self, container_env=None, img_id=None, img_name=None, os_proc=None, vuln_pkg_name=None, vuln_pkg_path=None, vuln_pkg_ver=None, _configuration=None):  # noqa: E501
        """VulnerableSoftPackageForGetRiskOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._container_env = None
        self._img_id = None
        self._img_name = None
        self._os_proc = None
        self._vuln_pkg_name = None
        self._vuln_pkg_path = None
        self._vuln_pkg_ver = None
        self.discriminator = None

        if container_env is not None:
            self.container_env = container_env
        if img_id is not None:
            self.img_id = img_id
        if img_name is not None:
            self.img_name = img_name
        if os_proc is not None:
            self.os_proc = os_proc
        if vuln_pkg_name is not None:
            self.vuln_pkg_name = vuln_pkg_name
        if vuln_pkg_path is not None:
            self.vuln_pkg_path = vuln_pkg_path
        if vuln_pkg_ver is not None:
            self.vuln_pkg_ver = vuln_pkg_ver

    @property
    def container_env(self):
        """Gets the container_env of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501


        :return: The container_env of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :rtype: ContainerEnvForGetRiskOutput
        """
        return self._container_env

    @container_env.setter
    def container_env(self, container_env):
        """Sets the container_env of this VulnerableSoftPackageForGetRiskOutput.


        :param container_env: The container_env of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :type: ContainerEnvForGetRiskOutput
        """

        self._container_env = container_env

    @property
    def img_id(self):
        """Gets the img_id of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501


        :return: The img_id of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._img_id

    @img_id.setter
    def img_id(self, img_id):
        """Sets the img_id of this VulnerableSoftPackageForGetRiskOutput.


        :param img_id: The img_id of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._img_id = img_id

    @property
    def img_name(self):
        """Gets the img_name of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501


        :return: The img_name of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._img_name

    @img_name.setter
    def img_name(self, img_name):
        """Sets the img_name of this VulnerableSoftPackageForGetRiskOutput.


        :param img_name: The img_name of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._img_name = img_name

    @property
    def os_proc(self):
        """Gets the os_proc of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501


        :return: The os_proc of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :rtype: OSProcForGetRiskOutput
        """
        return self._os_proc

    @os_proc.setter
    def os_proc(self, os_proc):
        """Sets the os_proc of this VulnerableSoftPackageForGetRiskOutput.


        :param os_proc: The os_proc of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :type: OSProcForGetRiskOutput
        """

        self._os_proc = os_proc

    @property
    def vuln_pkg_name(self):
        """Gets the vuln_pkg_name of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501


        :return: The vuln_pkg_name of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_pkg_name

    @vuln_pkg_name.setter
    def vuln_pkg_name(self, vuln_pkg_name):
        """Sets the vuln_pkg_name of this VulnerableSoftPackageForGetRiskOutput.


        :param vuln_pkg_name: The vuln_pkg_name of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._vuln_pkg_name = vuln_pkg_name

    @property
    def vuln_pkg_path(self):
        """Gets the vuln_pkg_path of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501


        :return: The vuln_pkg_path of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_pkg_path

    @vuln_pkg_path.setter
    def vuln_pkg_path(self, vuln_pkg_path):
        """Sets the vuln_pkg_path of this VulnerableSoftPackageForGetRiskOutput.


        :param vuln_pkg_path: The vuln_pkg_path of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._vuln_pkg_path = vuln_pkg_path

    @property
    def vuln_pkg_ver(self):
        """Gets the vuln_pkg_ver of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501


        :return: The vuln_pkg_ver of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :rtype: str
        """
        return self._vuln_pkg_ver

    @vuln_pkg_ver.setter
    def vuln_pkg_ver(self, vuln_pkg_ver):
        """Sets the vuln_pkg_ver of this VulnerableSoftPackageForGetRiskOutput.


        :param vuln_pkg_ver: The vuln_pkg_ver of this VulnerableSoftPackageForGetRiskOutput.  # noqa: E501
        :type: str
        """

        self._vuln_pkg_ver = vuln_pkg_ver

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VulnerableSoftPackageForGetRiskOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VulnerableSoftPackageForGetRiskOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VulnerableSoftPackageForGetRiskOutput):
            return True

        return self.to_dict() != other.to_dict()
