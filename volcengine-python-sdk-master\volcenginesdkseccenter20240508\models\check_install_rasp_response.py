# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CheckInstallRaspResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'chosen_count': 'int',
        'hit_limit': 'bool',
        'left_authorization_count': 'int',
        'open_count': 'int'
    }

    attribute_map = {
        'chosen_count': 'ChosenCount',
        'hit_limit': 'HitLimit',
        'left_authorization_count': 'LeftAuthorizationCount',
        'open_count': 'OpenCount'
    }

    def __init__(self, chosen_count=None, hit_limit=None, left_authorization_count=None, open_count=None, _configuration=None):  # noqa: E501
        """CheckInstallRaspResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._chosen_count = None
        self._hit_limit = None
        self._left_authorization_count = None
        self._open_count = None
        self.discriminator = None

        if chosen_count is not None:
            self.chosen_count = chosen_count
        if hit_limit is not None:
            self.hit_limit = hit_limit
        if left_authorization_count is not None:
            self.left_authorization_count = left_authorization_count
        if open_count is not None:
            self.open_count = open_count

    @property
    def chosen_count(self):
        """Gets the chosen_count of this CheckInstallRaspResponse.  # noqa: E501


        :return: The chosen_count of this CheckInstallRaspResponse.  # noqa: E501
        :rtype: int
        """
        return self._chosen_count

    @chosen_count.setter
    def chosen_count(self, chosen_count):
        """Sets the chosen_count of this CheckInstallRaspResponse.


        :param chosen_count: The chosen_count of this CheckInstallRaspResponse.  # noqa: E501
        :type: int
        """

        self._chosen_count = chosen_count

    @property
    def hit_limit(self):
        """Gets the hit_limit of this CheckInstallRaspResponse.  # noqa: E501


        :return: The hit_limit of this CheckInstallRaspResponse.  # noqa: E501
        :rtype: bool
        """
        return self._hit_limit

    @hit_limit.setter
    def hit_limit(self, hit_limit):
        """Sets the hit_limit of this CheckInstallRaspResponse.


        :param hit_limit: The hit_limit of this CheckInstallRaspResponse.  # noqa: E501
        :type: bool
        """

        self._hit_limit = hit_limit

    @property
    def left_authorization_count(self):
        """Gets the left_authorization_count of this CheckInstallRaspResponse.  # noqa: E501


        :return: The left_authorization_count of this CheckInstallRaspResponse.  # noqa: E501
        :rtype: int
        """
        return self._left_authorization_count

    @left_authorization_count.setter
    def left_authorization_count(self, left_authorization_count):
        """Sets the left_authorization_count of this CheckInstallRaspResponse.


        :param left_authorization_count: The left_authorization_count of this CheckInstallRaspResponse.  # noqa: E501
        :type: int
        """

        self._left_authorization_count = left_authorization_count

    @property
    def open_count(self):
        """Gets the open_count of this CheckInstallRaspResponse.  # noqa: E501


        :return: The open_count of this CheckInstallRaspResponse.  # noqa: E501
        :rtype: int
        """
        return self._open_count

    @open_count.setter
    def open_count(self, open_count):
        """Sets the open_count of this CheckInstallRaspResponse.


        :param open_count: The open_count of this CheckInstallRaspResponse.  # noqa: E501
        :type: int
        """

        self._open_count = open_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CheckInstallRaspResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CheckInstallRaspResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CheckInstallRaspResponse):
            return True

        return self.to_dict() != other.to_dict()
