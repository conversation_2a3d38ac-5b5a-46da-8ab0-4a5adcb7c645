# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NetworkForDescribeInstanceTypesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'baseline_bandwidth_mbps': 'int',
        'maximum_bandwidth_mbps': 'int',
        'maximum_network_interfaces': 'int',
        'maximum_private_ipv4_addresses_per_network_interface': 'int',
        'maximum_queues_per_network_interface': 'int',
        'maximum_throughput_kpps': 'int'
    }

    attribute_map = {
        'baseline_bandwidth_mbps': 'BaselineBandwidthMbps',
        'maximum_bandwidth_mbps': 'MaximumBandwidthMbps',
        'maximum_network_interfaces': 'MaximumNetworkInterfaces',
        'maximum_private_ipv4_addresses_per_network_interface': 'MaximumPrivateIpv4AddressesPerNetworkInterface',
        'maximum_queues_per_network_interface': 'MaximumQueuesPerNetworkInterface',
        'maximum_throughput_kpps': 'MaximumThroughputKpps'
    }

    def __init__(self, baseline_bandwidth_mbps=None, maximum_bandwidth_mbps=None, maximum_network_interfaces=None, maximum_private_ipv4_addresses_per_network_interface=None, maximum_queues_per_network_interface=None, maximum_throughput_kpps=None, _configuration=None):  # noqa: E501
        """NetworkForDescribeInstanceTypesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._baseline_bandwidth_mbps = None
        self._maximum_bandwidth_mbps = None
        self._maximum_network_interfaces = None
        self._maximum_private_ipv4_addresses_per_network_interface = None
        self._maximum_queues_per_network_interface = None
        self._maximum_throughput_kpps = None
        self.discriminator = None

        if baseline_bandwidth_mbps is not None:
            self.baseline_bandwidth_mbps = baseline_bandwidth_mbps
        if maximum_bandwidth_mbps is not None:
            self.maximum_bandwidth_mbps = maximum_bandwidth_mbps
        if maximum_network_interfaces is not None:
            self.maximum_network_interfaces = maximum_network_interfaces
        if maximum_private_ipv4_addresses_per_network_interface is not None:
            self.maximum_private_ipv4_addresses_per_network_interface = maximum_private_ipv4_addresses_per_network_interface
        if maximum_queues_per_network_interface is not None:
            self.maximum_queues_per_network_interface = maximum_queues_per_network_interface
        if maximum_throughput_kpps is not None:
            self.maximum_throughput_kpps = maximum_throughput_kpps

    @property
    def baseline_bandwidth_mbps(self):
        """Gets the baseline_bandwidth_mbps of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The baseline_bandwidth_mbps of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._baseline_bandwidth_mbps

    @baseline_bandwidth_mbps.setter
    def baseline_bandwidth_mbps(self, baseline_bandwidth_mbps):
        """Sets the baseline_bandwidth_mbps of this NetworkForDescribeInstanceTypesOutput.


        :param baseline_bandwidth_mbps: The baseline_bandwidth_mbps of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._baseline_bandwidth_mbps = baseline_bandwidth_mbps

    @property
    def maximum_bandwidth_mbps(self):
        """Gets the maximum_bandwidth_mbps of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The maximum_bandwidth_mbps of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._maximum_bandwidth_mbps

    @maximum_bandwidth_mbps.setter
    def maximum_bandwidth_mbps(self, maximum_bandwidth_mbps):
        """Sets the maximum_bandwidth_mbps of this NetworkForDescribeInstanceTypesOutput.


        :param maximum_bandwidth_mbps: The maximum_bandwidth_mbps of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._maximum_bandwidth_mbps = maximum_bandwidth_mbps

    @property
    def maximum_network_interfaces(self):
        """Gets the maximum_network_interfaces of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The maximum_network_interfaces of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._maximum_network_interfaces

    @maximum_network_interfaces.setter
    def maximum_network_interfaces(self, maximum_network_interfaces):
        """Sets the maximum_network_interfaces of this NetworkForDescribeInstanceTypesOutput.


        :param maximum_network_interfaces: The maximum_network_interfaces of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._maximum_network_interfaces = maximum_network_interfaces

    @property
    def maximum_private_ipv4_addresses_per_network_interface(self):
        """Gets the maximum_private_ipv4_addresses_per_network_interface of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The maximum_private_ipv4_addresses_per_network_interface of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._maximum_private_ipv4_addresses_per_network_interface

    @maximum_private_ipv4_addresses_per_network_interface.setter
    def maximum_private_ipv4_addresses_per_network_interface(self, maximum_private_ipv4_addresses_per_network_interface):
        """Sets the maximum_private_ipv4_addresses_per_network_interface of this NetworkForDescribeInstanceTypesOutput.


        :param maximum_private_ipv4_addresses_per_network_interface: The maximum_private_ipv4_addresses_per_network_interface of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._maximum_private_ipv4_addresses_per_network_interface = maximum_private_ipv4_addresses_per_network_interface

    @property
    def maximum_queues_per_network_interface(self):
        """Gets the maximum_queues_per_network_interface of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The maximum_queues_per_network_interface of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._maximum_queues_per_network_interface

    @maximum_queues_per_network_interface.setter
    def maximum_queues_per_network_interface(self, maximum_queues_per_network_interface):
        """Sets the maximum_queues_per_network_interface of this NetworkForDescribeInstanceTypesOutput.


        :param maximum_queues_per_network_interface: The maximum_queues_per_network_interface of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._maximum_queues_per_network_interface = maximum_queues_per_network_interface

    @property
    def maximum_throughput_kpps(self):
        """Gets the maximum_throughput_kpps of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501


        :return: The maximum_throughput_kpps of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :rtype: int
        """
        return self._maximum_throughput_kpps

    @maximum_throughput_kpps.setter
    def maximum_throughput_kpps(self, maximum_throughput_kpps):
        """Sets the maximum_throughput_kpps of this NetworkForDescribeInstanceTypesOutput.


        :param maximum_throughput_kpps: The maximum_throughput_kpps of this NetworkForDescribeInstanceTypesOutput.  # noqa: E501
        :type: int
        """

        self._maximum_throughput_kpps = maximum_throughput_kpps

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NetworkForDescribeInstanceTypesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NetworkForDescribeInstanceTypesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NetworkForDescribeInstanceTypesOutput):
            return True

        return self.to_dict() != other.to_dict()
