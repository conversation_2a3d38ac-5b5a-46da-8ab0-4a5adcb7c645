# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class MessageSendRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'body': 'str',
        'instance_id': 'str',
        'key': 'str',
        'tag': 'str',
        'topic': 'str'
    }

    attribute_map = {
        'body': 'Body',
        'instance_id': 'InstanceId',
        'key': 'Key',
        'tag': 'Tag',
        'topic': 'Topic'
    }

    def __init__(self, body=None, instance_id=None, key=None, tag=None, topic=None, _configuration=None):  # noqa: E501
        """MessageSendRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._body = None
        self._instance_id = None
        self._key = None
        self._tag = None
        self._topic = None
        self.discriminator = None

        self.body = body
        self.instance_id = instance_id
        if key is not None:
            self.key = key
        if tag is not None:
            self.tag = tag
        self.topic = topic

    @property
    def body(self):
        """Gets the body of this MessageSendRequest.  # noqa: E501


        :return: The body of this MessageSendRequest.  # noqa: E501
        :rtype: str
        """
        return self._body

    @body.setter
    def body(self, body):
        """Sets the body of this MessageSendRequest.


        :param body: The body of this MessageSendRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and body is None:
            raise ValueError("Invalid value for `body`, must not be `None`")  # noqa: E501

        self._body = body

    @property
    def instance_id(self):
        """Gets the instance_id of this MessageSendRequest.  # noqa: E501


        :return: The instance_id of this MessageSendRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this MessageSendRequest.


        :param instance_id: The instance_id of this MessageSendRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def key(self):
        """Gets the key of this MessageSendRequest.  # noqa: E501


        :return: The key of this MessageSendRequest.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this MessageSendRequest.


        :param key: The key of this MessageSendRequest.  # noqa: E501
        :type: str
        """

        self._key = key

    @property
    def tag(self):
        """Gets the tag of this MessageSendRequest.  # noqa: E501


        :return: The tag of this MessageSendRequest.  # noqa: E501
        :rtype: str
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this MessageSendRequest.


        :param tag: The tag of this MessageSendRequest.  # noqa: E501
        :type: str
        """

        self._tag = tag

    @property
    def topic(self):
        """Gets the topic of this MessageSendRequest.  # noqa: E501


        :return: The topic of this MessageSendRequest.  # noqa: E501
        :rtype: str
        """
        return self._topic

    @topic.setter
    def topic(self, topic):
        """Sets the topic of this MessageSendRequest.


        :param topic: The topic of this MessageSendRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and topic is None:
            raise ValueError("Invalid value for `topic`, must not be `None`")  # noqa: E501

        self._topic = topic

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(MessageSendRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, MessageSendRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, MessageSendRequest):
            return True

        return self.to_dict() != other.to_dict()
