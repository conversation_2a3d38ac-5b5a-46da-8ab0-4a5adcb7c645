# coding: utf-8

# flake8: noqa
"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkprivatezone.models.batch_create_record_request import BatchCreateRecordRequest
from volcenginesdkprivatezone.models.batch_create_record_response import BatchCreateRecordResponse
from volcenginesdkprivatezone.models.batch_delete_record_request import BatchDeleteRecordRequest
from volcenginesdkprivatezone.models.batch_delete_record_response import BatchDeleteRecordResponse
from volcenginesdkprivatezone.models.batch_update_record_request import BatchUpdateRecordRequest
from volcenginesdkprivatezone.models.batch_update_record_response import BatchUpdateRecordResponse
from volcenginesdkprivatezone.models.bind_for_inc_bind_vpc_input import BindForIncBindVPCInput
from volcenginesdkprivatezone.models.bind_for_update_bind_vpc_input import BindForUpdateBindVPCInput
from volcenginesdkprivatezone.models.bind_rule_vpc_request import BindRuleVPCRequest
from volcenginesdkprivatezone.models.bind_rule_vpc_response import BindRuleVPCResponse
from volcenginesdkprivatezone.models.bind_vpc_for_list_bind_vpc_output import BindVPCForListBindVPCOutput
from volcenginesdkprivatezone.models.bind_vpc_for_query_private_zone_output import BindVPCForQueryPrivateZoneOutput
from volcenginesdkprivatezone.models.bind_vpc_for_query_resolver_rule_output import BindVPCForQueryResolverRuleOutput
from volcenginesdkprivatezone.models.bind_vpc_request import BindVPCRequest
from volcenginesdkprivatezone.models.bind_vpc_response import BindVPCResponse
from volcenginesdkprivatezone.models.create_authorized_user_request import CreateAuthorizedUserRequest
from volcenginesdkprivatezone.models.create_authorized_user_response import CreateAuthorizedUserResponse
from volcenginesdkprivatezone.models.create_private_zone_request import CreatePrivateZoneRequest
from volcenginesdkprivatezone.models.create_private_zone_response import CreatePrivateZoneResponse
from volcenginesdkprivatezone.models.create_record_request import CreateRecordRequest
from volcenginesdkprivatezone.models.create_record_response import CreateRecordResponse
from volcenginesdkprivatezone.models.create_resolver_endpoint_request import CreateResolverEndpointRequest
from volcenginesdkprivatezone.models.create_resolver_endpoint_response import CreateResolverEndpointResponse
from volcenginesdkprivatezone.models.create_resolver_rule_request import CreateResolverRuleRequest
from volcenginesdkprivatezone.models.create_resolver_rule_response import CreateResolverRuleResponse
from volcenginesdkprivatezone.models.delete_authorized_user_request import DeleteAuthorizedUserRequest
from volcenginesdkprivatezone.models.delete_authorized_user_response import DeleteAuthorizedUserResponse
from volcenginesdkprivatezone.models.delete_private_zone_request import DeletePrivateZoneRequest
from volcenginesdkprivatezone.models.delete_private_zone_response import DeletePrivateZoneResponse
from volcenginesdkprivatezone.models.delete_record_request import DeleteRecordRequest
from volcenginesdkprivatezone.models.delete_record_response import DeleteRecordResponse
from volcenginesdkprivatezone.models.delete_resolver_endpoint_request import DeleteResolverEndpointRequest
from volcenginesdkprivatezone.models.delete_resolver_endpoint_response import DeleteResolverEndpointResponse
from volcenginesdkprivatezone.models.delete_resolver_rule_request import DeleteResolverRuleRequest
from volcenginesdkprivatezone.models.delete_resolver_rule_response import DeleteResolverRuleResponse
from volcenginesdkprivatezone.models.ecs_region_for_query_ecs_host_sync_task_output import EcsRegionForQueryEcsHostSyncTaskOutput
from volcenginesdkprivatezone.models.ecs_region_for_sync_ecs_host_input import EcsRegionForSyncEcsHostInput
from volcenginesdkprivatezone.models.ecs_region_for_update_ecs_host_sync_task_input import EcsRegionForUpdateEcsHostSyncTaskInput
from volcenginesdkprivatezone.models.endpoint_for_list_resolver_endpoints_output import EndpointForListResolverEndpointsOutput
from volcenginesdkprivatezone.models.forward_ip_for_create_resolver_rule_input import ForwardIPForCreateResolverRuleInput
from volcenginesdkprivatezone.models.forward_ip_for_list_resolver_rules_output import ForwardIPForListResolverRulesOutput
from volcenginesdkprivatezone.models.forward_ip_for_query_resolver_rule_output import ForwardIPForQueryResolverRuleOutput
from volcenginesdkprivatezone.models.forward_ip_for_update_resolver_rule_input import ForwardIPForUpdateResolverRuleInput
from volcenginesdkprivatezone.models.inc_bind_vpc_request import IncBindVPCRequest
from volcenginesdkprivatezone.models.inc_bind_vpc_response import IncBindVPCResponse
from volcenginesdkprivatezone.models.ip_config_for_create_resolver_endpoint_input import IpConfigForCreateResolverEndpointInput
from volcenginesdkprivatezone.models.ip_config_for_list_resolver_endpoints_output import IpConfigForListResolverEndpointsOutput
from volcenginesdkprivatezone.models.ip_config_for_query_resolver_endpoint_output import IpConfigForQueryResolverEndpointOutput
from volcenginesdkprivatezone.models.ip_configs_for_update_resolver_endpoint_input import IpConfigsForUpdateResolverEndpointInput
from volcenginesdkprivatezone.models.list_authorized_users_request import ListAuthorizedUsersRequest
from volcenginesdkprivatezone.models.list_authorized_users_response import ListAuthorizedUsersResponse
from volcenginesdkprivatezone.models.list_bind_vpc_request import ListBindVPCRequest
from volcenginesdkprivatezone.models.list_bind_vpc_response import ListBindVPCResponse
from volcenginesdkprivatezone.models.list_private_zones_request import ListPrivateZonesRequest
from volcenginesdkprivatezone.models.list_private_zones_response import ListPrivateZonesResponse
from volcenginesdkprivatezone.models.list_record_attributes_request import ListRecordAttributesRequest
from volcenginesdkprivatezone.models.list_record_attributes_response import ListRecordAttributesResponse
from volcenginesdkprivatezone.models.list_record_sets_request import ListRecordSetsRequest
from volcenginesdkprivatezone.models.list_record_sets_response import ListRecordSetsResponse
from volcenginesdkprivatezone.models.list_records_request import ListRecordsRequest
from volcenginesdkprivatezone.models.list_records_response import ListRecordsResponse
from volcenginesdkprivatezone.models.list_regions_request import ListRegionsRequest
from volcenginesdkprivatezone.models.list_regions_response import ListRegionsResponse
from volcenginesdkprivatezone.models.list_resolver_endpoints_request import ListResolverEndpointsRequest
from volcenginesdkprivatezone.models.list_resolver_endpoints_response import ListResolverEndpointsResponse
from volcenginesdkprivatezone.models.list_resolver_rules_request import ListResolverRulesRequest
from volcenginesdkprivatezone.models.list_resolver_rules_response import ListResolverRulesResponse
from volcenginesdkprivatezone.models.list_tags_for_resources_request import ListTagsForResourcesRequest
from volcenginesdkprivatezone.models.list_tags_for_resources_response import ListTagsForResourcesResponse
from volcenginesdkprivatezone.models.list_user_vpc_request import ListUserVPCRequest
from volcenginesdkprivatezone.models.list_user_vpc_response import ListUserVPCResponse
from volcenginesdkprivatezone.models.query_ecs_host_sync_task_request import QueryEcsHostSyncTaskRequest
from volcenginesdkprivatezone.models.query_ecs_host_sync_task_response import QueryEcsHostSyncTaskResponse
from volcenginesdkprivatezone.models.query_private_zone_request import QueryPrivateZoneRequest
from volcenginesdkprivatezone.models.query_private_zone_response import QueryPrivateZoneResponse
from volcenginesdkprivatezone.models.query_record_request import QueryRecordRequest
from volcenginesdkprivatezone.models.query_record_response import QueryRecordResponse
from volcenginesdkprivatezone.models.query_resolver_endpoint_request import QueryResolverEndpointRequest
from volcenginesdkprivatezone.models.query_resolver_endpoint_response import QueryResolverEndpointResponse
from volcenginesdkprivatezone.models.query_resolver_rule_request import QueryResolverRuleRequest
from volcenginesdkprivatezone.models.query_resolver_rule_response import QueryResolverRuleResponse
from volcenginesdkprivatezone.models.record_for_batch_create_record_input import RecordForBatchCreateRecordInput
from volcenginesdkprivatezone.models.record_for_batch_update_record_input import RecordForBatchUpdateRecordInput
from volcenginesdkprivatezone.models.record_for_list_records_output import RecordForListRecordsOutput
from volcenginesdkprivatezone.models.record_set_for_list_record_sets_output import RecordSetForListRecordSetsOutput
from volcenginesdkprivatezone.models.region_for_list_regions_output import RegionForListRegionsOutput
from volcenginesdkprivatezone.models.resource_tag_for_list_tags_for_resources_output import ResourceTagForListTagsForResourcesOutput
from volcenginesdkprivatezone.models.rule_for_list_resolver_rules_output import RuleForListResolverRulesOutput
from volcenginesdkprivatezone.models.send_verify_code_request import SendVerifyCodeRequest
from volcenginesdkprivatezone.models.send_verify_code_response import SendVerifyCodeResponse
from volcenginesdkprivatezone.models.sync_ecs_host_request import SyncEcsHostRequest
from volcenginesdkprivatezone.models.sync_ecs_host_response import SyncEcsHostResponse
from volcenginesdkprivatezone.models.tag_filter_for_list_private_zones_input import TagFilterForListPrivateZonesInput
from volcenginesdkprivatezone.models.tag_filter_for_list_resolver_endpoints_input import TagFilterForListResolverEndpointsInput
from volcenginesdkprivatezone.models.tag_filter_for_list_resolver_rules_input import TagFilterForListResolverRulesInput
from volcenginesdkprivatezone.models.tag_filter_for_list_tags_for_resources_input import TagFilterForListTagsForResourcesInput
from volcenginesdkprivatezone.models.tag_for_create_private_zone_input import TagForCreatePrivateZoneInput
from volcenginesdkprivatezone.models.tag_for_create_resolver_endpoint_input import TagForCreateResolverEndpointInput
from volcenginesdkprivatezone.models.tag_for_create_resolver_rule_input import TagForCreateResolverRuleInput
from volcenginesdkprivatezone.models.tag_for_list_private_zones_output import TagForListPrivateZonesOutput
from volcenginesdkprivatezone.models.tag_for_list_resolver_endpoints_output import TagForListResolverEndpointsOutput
from volcenginesdkprivatezone.models.tag_for_list_resolver_rules_output import TagForListResolverRulesOutput
from volcenginesdkprivatezone.models.tag_for_query_private_zone_output import TagForQueryPrivateZoneOutput
from volcenginesdkprivatezone.models.tag_for_query_resolver_endpoint_output import TagForQueryResolverEndpointOutput
from volcenginesdkprivatezone.models.tag_for_query_resolver_rule_output import TagForQueryResolverRuleOutput
from volcenginesdkprivatezone.models.tag_for_tag_resources_input import TagForTagResourcesInput
from volcenginesdkprivatezone.models.tag_resources_request import TagResourcesRequest
from volcenginesdkprivatezone.models.tag_resources_response import TagResourcesResponse
from volcenginesdkprivatezone.models.unbind_for_inc_bind_vpc_input import UnbindForIncBindVPCInput
from volcenginesdkprivatezone.models.unbind_for_update_bind_vpc_input import UnbindForUpdateBindVPCInput
from volcenginesdkprivatezone.models.untag_resources_request import UntagResourcesRequest
from volcenginesdkprivatezone.models.untag_resources_response import UntagResourcesResponse
from volcenginesdkprivatezone.models.update_bind_vpc_request import UpdateBindVPCRequest
from volcenginesdkprivatezone.models.update_bind_vpc_response import UpdateBindVPCResponse
from volcenginesdkprivatezone.models.update_ecs_host_sync_task_request import UpdateEcsHostSyncTaskRequest
from volcenginesdkprivatezone.models.update_ecs_host_sync_task_response import UpdateEcsHostSyncTaskResponse
from volcenginesdkprivatezone.models.update_private_zone_request import UpdatePrivateZoneRequest
from volcenginesdkprivatezone.models.update_private_zone_response import UpdatePrivateZoneResponse
from volcenginesdkprivatezone.models.update_record_request import UpdateRecordRequest
from volcenginesdkprivatezone.models.update_record_response import UpdateRecordResponse
from volcenginesdkprivatezone.models.update_record_set_request import UpdateRecordSetRequest
from volcenginesdkprivatezone.models.update_record_set_response import UpdateRecordSetResponse
from volcenginesdkprivatezone.models.update_resolver_endpoint_request import UpdateResolverEndpointRequest
from volcenginesdkprivatezone.models.update_resolver_endpoint_response import UpdateResolverEndpointResponse
from volcenginesdkprivatezone.models.update_resolver_rule_request import UpdateResolverRuleRequest
from volcenginesdkprivatezone.models.update_resolver_rule_response import UpdateResolverRuleResponse
from volcenginesdkprivatezone.models.user_for_list_authorized_users_output import UserForListAuthorizedUsersOutput
from volcenginesdkprivatezone.models.vpc_for_list_user_vpc_output import VPCForListUserVPCOutput
from volcenginesdkprivatezone.models.vpc_for_bind_rule_vpc_input import VpcForBindRuleVPCInput
from volcenginesdkprivatezone.models.vpc_for_bind_vpc_input import VpcForBindVPCInput
from volcenginesdkprivatezone.models.vpc_for_create_private_zone_input import VpcForCreatePrivateZoneInput
from volcenginesdkprivatezone.models.vpc_for_create_resolver_rule_input import VpcForCreateResolverRuleInput
from volcenginesdkprivatezone.models.vpc_for_update_resolver_rule_input import VpcForUpdateResolverRuleInput
from volcenginesdkprivatezone.models.zone_for_list_private_zones_output import ZoneForListPrivateZonesOutput
