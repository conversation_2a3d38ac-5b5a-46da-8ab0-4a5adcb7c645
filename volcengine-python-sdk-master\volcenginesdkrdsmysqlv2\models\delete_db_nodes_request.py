# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeleteDBNodesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'db_node_ids': 'list[str]',
        'estimate_only': 'bool',
        'instance_id': 'str',
        'request_source': 'str',
        'specified_switch_end_time': 'str',
        'specified_switch_start_time': 'str',
        'switch_type': 'str'
    }

    attribute_map = {
        'db_node_ids': 'DBNodeIds',
        'estimate_only': 'EstimateOnly',
        'instance_id': 'InstanceId',
        'request_source': 'RequestSource',
        'specified_switch_end_time': 'SpecifiedSwitchEndTime',
        'specified_switch_start_time': 'SpecifiedSwitchStartTime',
        'switch_type': 'SwitchType'
    }

    def __init__(self, db_node_ids=None, estimate_only=None, instance_id=None, request_source=None, specified_switch_end_time=None, specified_switch_start_time=None, switch_type=None, _configuration=None):  # noqa: E501
        """DeleteDBNodesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._db_node_ids = None
        self._estimate_only = None
        self._instance_id = None
        self._request_source = None
        self._specified_switch_end_time = None
        self._specified_switch_start_time = None
        self._switch_type = None
        self.discriminator = None

        if db_node_ids is not None:
            self.db_node_ids = db_node_ids
        if estimate_only is not None:
            self.estimate_only = estimate_only
        self.instance_id = instance_id
        if request_source is not None:
            self.request_source = request_source
        if specified_switch_end_time is not None:
            self.specified_switch_end_time = specified_switch_end_time
        if specified_switch_start_time is not None:
            self.specified_switch_start_time = specified_switch_start_time
        if switch_type is not None:
            self.switch_type = switch_type

    @property
    def db_node_ids(self):
        """Gets the db_node_ids of this DeleteDBNodesRequest.  # noqa: E501


        :return: The db_node_ids of this DeleteDBNodesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._db_node_ids

    @db_node_ids.setter
    def db_node_ids(self, db_node_ids):
        """Sets the db_node_ids of this DeleteDBNodesRequest.


        :param db_node_ids: The db_node_ids of this DeleteDBNodesRequest.  # noqa: E501
        :type: list[str]
        """

        self._db_node_ids = db_node_ids

    @property
    def estimate_only(self):
        """Gets the estimate_only of this DeleteDBNodesRequest.  # noqa: E501


        :return: The estimate_only of this DeleteDBNodesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._estimate_only

    @estimate_only.setter
    def estimate_only(self, estimate_only):
        """Sets the estimate_only of this DeleteDBNodesRequest.


        :param estimate_only: The estimate_only of this DeleteDBNodesRequest.  # noqa: E501
        :type: bool
        """

        self._estimate_only = estimate_only

    @property
    def instance_id(self):
        """Gets the instance_id of this DeleteDBNodesRequest.  # noqa: E501


        :return: The instance_id of this DeleteDBNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DeleteDBNodesRequest.


        :param instance_id: The instance_id of this DeleteDBNodesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def request_source(self):
        """Gets the request_source of this DeleteDBNodesRequest.  # noqa: E501


        :return: The request_source of this DeleteDBNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._request_source

    @request_source.setter
    def request_source(self, request_source):
        """Sets the request_source of this DeleteDBNodesRequest.


        :param request_source: The request_source of this DeleteDBNodesRequest.  # noqa: E501
        :type: str
        """

        self._request_source = request_source

    @property
    def specified_switch_end_time(self):
        """Gets the specified_switch_end_time of this DeleteDBNodesRequest.  # noqa: E501


        :return: The specified_switch_end_time of this DeleteDBNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._specified_switch_end_time

    @specified_switch_end_time.setter
    def specified_switch_end_time(self, specified_switch_end_time):
        """Sets the specified_switch_end_time of this DeleteDBNodesRequest.


        :param specified_switch_end_time: The specified_switch_end_time of this DeleteDBNodesRequest.  # noqa: E501
        :type: str
        """

        self._specified_switch_end_time = specified_switch_end_time

    @property
    def specified_switch_start_time(self):
        """Gets the specified_switch_start_time of this DeleteDBNodesRequest.  # noqa: E501


        :return: The specified_switch_start_time of this DeleteDBNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._specified_switch_start_time

    @specified_switch_start_time.setter
    def specified_switch_start_time(self, specified_switch_start_time):
        """Sets the specified_switch_start_time of this DeleteDBNodesRequest.


        :param specified_switch_start_time: The specified_switch_start_time of this DeleteDBNodesRequest.  # noqa: E501
        :type: str
        """

        self._specified_switch_start_time = specified_switch_start_time

    @property
    def switch_type(self):
        """Gets the switch_type of this DeleteDBNodesRequest.  # noqa: E501


        :return: The switch_type of this DeleteDBNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._switch_type

    @switch_type.setter
    def switch_type(self, switch_type):
        """Sets the switch_type of this DeleteDBNodesRequest.


        :param switch_type: The switch_type of this DeleteDBNodesRequest.  # noqa: E501
        :type: str
        """

        self._switch_type = switch_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeleteDBNodesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeleteDBNodesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeleteDBNodesRequest):
            return True

        return self.to_dict() != other.to_dict()
