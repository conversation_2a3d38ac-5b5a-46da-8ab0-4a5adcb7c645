# coding: utf-8

"""
    directconnect

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'direct_connect_access_point_id': 'str',
        'direct_connect_access_point_name': 'str',
        'line_operators': 'list[str]',
        'location': 'str',
        'status': 'str'
    }

    attribute_map = {
        'description': 'Description',
        'direct_connect_access_point_id': 'DirectConnectAccessPointId',
        'direct_connect_access_point_name': 'DirectConnectAccessPointName',
        'line_operators': 'LineOperators',
        'location': 'Location',
        'status': 'Status'
    }

    def __init__(self, description=None, direct_connect_access_point_id=None, direct_connect_access_point_name=None, line_operators=None, location=None, status=None, _configuration=None):  # noqa: E501
        """DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._direct_connect_access_point_id = None
        self._direct_connect_access_point_name = None
        self._line_operators = None
        self._location = None
        self._status = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if direct_connect_access_point_id is not None:
            self.direct_connect_access_point_id = direct_connect_access_point_id
        if direct_connect_access_point_name is not None:
            self.direct_connect_access_point_name = direct_connect_access_point_name
        if line_operators is not None:
            self.line_operators = line_operators
        if location is not None:
            self.location = location
        if status is not None:
            self.status = status

    @property
    def description(self):
        """Gets the description of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501


        :return: The description of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.


        :param description: The description of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def direct_connect_access_point_id(self):
        """Gets the direct_connect_access_point_id of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501


        :return: The direct_connect_access_point_id of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_access_point_id

    @direct_connect_access_point_id.setter
    def direct_connect_access_point_id(self, direct_connect_access_point_id):
        """Sets the direct_connect_access_point_id of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.


        :param direct_connect_access_point_id: The direct_connect_access_point_id of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :type: str
        """

        self._direct_connect_access_point_id = direct_connect_access_point_id

    @property
    def direct_connect_access_point_name(self):
        """Gets the direct_connect_access_point_name of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501


        :return: The direct_connect_access_point_name of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._direct_connect_access_point_name

    @direct_connect_access_point_name.setter
    def direct_connect_access_point_name(self, direct_connect_access_point_name):
        """Sets the direct_connect_access_point_name of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.


        :param direct_connect_access_point_name: The direct_connect_access_point_name of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :type: str
        """

        self._direct_connect_access_point_name = direct_connect_access_point_name

    @property
    def line_operators(self):
        """Gets the line_operators of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501


        :return: The line_operators of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._line_operators

    @line_operators.setter
    def line_operators(self, line_operators):
        """Sets the line_operators of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.


        :param line_operators: The line_operators of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :type: list[str]
        """

        self._line_operators = line_operators

    @property
    def location(self):
        """Gets the location of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501


        :return: The location of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._location

    @location.setter
    def location(self, location):
        """Sets the location of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.


        :param location: The location of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :type: str
        """

        self._location = location

    @property
    def status(self):
        """Gets the status of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501


        :return: The status of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.


        :param status: The status of this DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DirectConnectAccessPointForDescribeDirectConnectAccessPointsOutput):
            return True

        return self.to_dict() != other.to_dict()
