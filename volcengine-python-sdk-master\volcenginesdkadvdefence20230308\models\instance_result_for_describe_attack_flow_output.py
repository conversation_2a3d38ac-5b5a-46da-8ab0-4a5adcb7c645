# coding: utf-8

"""
    advdefence20230308

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceResultForDescribeAttackFlowOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'key': 'str',
        'val': 'ValForDescribeAttackFlowOutput'
    }

    attribute_map = {
        'key': 'Key',
        'val': 'Val'
    }

    def __init__(self, key=None, val=None, _configuration=None):  # noqa: E501
        """InstanceResultForDescribeAttackFlowOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._key = None
        self._val = None
        self.discriminator = None

        if key is not None:
            self.key = key
        if val is not None:
            self.val = val

    @property
    def key(self):
        """Gets the key of this InstanceResultForDescribeAttackFlowOutput.  # noqa: E501


        :return: The key of this InstanceResultForDescribeAttackFlowOutput.  # noqa: E501
        :rtype: str
        """
        return self._key

    @key.setter
    def key(self, key):
        """Sets the key of this InstanceResultForDescribeAttackFlowOutput.


        :param key: The key of this InstanceResultForDescribeAttackFlowOutput.  # noqa: E501
        :type: str
        """

        self._key = key

    @property
    def val(self):
        """Gets the val of this InstanceResultForDescribeAttackFlowOutput.  # noqa: E501


        :return: The val of this InstanceResultForDescribeAttackFlowOutput.  # noqa: E501
        :rtype: ValForDescribeAttackFlowOutput
        """
        return self._val

    @val.setter
    def val(self, val):
        """Sets the val of this InstanceResultForDescribeAttackFlowOutput.


        :param val: The val of this InstanceResultForDescribeAttackFlowOutput.  # noqa: E501
        :type: ValForDescribeAttackFlowOutput
        """

        self._val = val

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceResultForDescribeAttackFlowOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceResultForDescribeAttackFlowOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceResultForDescribeAttackFlowOutput):
            return True

        return self.to_dict() != other.to_dict()
