# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateModelCustomizationJobRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'customization_type': 'str',
        'data': 'DataForCreateModelCustomizationJobInput',
        'description': 'str',
        'hyperparameters': 'list[HyperparameterForCreateModelCustomizationJobInput]',
        'model_reference': 'ModelReferenceForCreateModelCustomizationJobInput',
        'name': 'str',
        'project_name': 'str',
        'save_model_limit': 'int',
        'tags': 'list[TagForCreateModelCustomizationJobInput]'
    }

    attribute_map = {
        'customization_type': 'CustomizationType',
        'data': 'Data',
        'description': 'Description',
        'hyperparameters': 'Hyperparameters',
        'model_reference': 'ModelReference',
        'name': 'Name',
        'project_name': 'ProjectName',
        'save_model_limit': 'SaveModelLimit',
        'tags': 'Tags'
    }

    def __init__(self, customization_type=None, data=None, description=None, hyperparameters=None, model_reference=None, name=None, project_name=None, save_model_limit=None, tags=None, _configuration=None):  # noqa: E501
        """CreateModelCustomizationJobRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._customization_type = None
        self._data = None
        self._description = None
        self._hyperparameters = None
        self._model_reference = None
        self._name = None
        self._project_name = None
        self._save_model_limit = None
        self._tags = None
        self.discriminator = None

        self.customization_type = customization_type
        if data is not None:
            self.data = data
        if description is not None:
            self.description = description
        if hyperparameters is not None:
            self.hyperparameters = hyperparameters
        if model_reference is not None:
            self.model_reference = model_reference
        self.name = name
        if project_name is not None:
            self.project_name = project_name
        if save_model_limit is not None:
            self.save_model_limit = save_model_limit
        if tags is not None:
            self.tags = tags

    @property
    def customization_type(self):
        """Gets the customization_type of this CreateModelCustomizationJobRequest.  # noqa: E501


        :return: The customization_type of this CreateModelCustomizationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._customization_type

    @customization_type.setter
    def customization_type(self, customization_type):
        """Sets the customization_type of this CreateModelCustomizationJobRequest.


        :param customization_type: The customization_type of this CreateModelCustomizationJobRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and customization_type is None:
            raise ValueError("Invalid value for `customization_type`, must not be `None`")  # noqa: E501
        allowed_values = ["FinetuneSft", "FinetuneLoRA", "Pretrain", "DPOLoRA", "DPO"]  # noqa: E501
        if (self._configuration.client_side_validation and
                customization_type not in allowed_values):
            raise ValueError(
                "Invalid value for `customization_type` ({0}), must be one of {1}"  # noqa: E501
                .format(customization_type, allowed_values)
            )

        self._customization_type = customization_type

    @property
    def data(self):
        """Gets the data of this CreateModelCustomizationJobRequest.  # noqa: E501


        :return: The data of this CreateModelCustomizationJobRequest.  # noqa: E501
        :rtype: DataForCreateModelCustomizationJobInput
        """
        return self._data

    @data.setter
    def data(self, data):
        """Sets the data of this CreateModelCustomizationJobRequest.


        :param data: The data of this CreateModelCustomizationJobRequest.  # noqa: E501
        :type: DataForCreateModelCustomizationJobInput
        """

        self._data = data

    @property
    def description(self):
        """Gets the description of this CreateModelCustomizationJobRequest.  # noqa: E501


        :return: The description of this CreateModelCustomizationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateModelCustomizationJobRequest.


        :param description: The description of this CreateModelCustomizationJobRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def hyperparameters(self):
        """Gets the hyperparameters of this CreateModelCustomizationJobRequest.  # noqa: E501


        :return: The hyperparameters of this CreateModelCustomizationJobRequest.  # noqa: E501
        :rtype: list[HyperparameterForCreateModelCustomizationJobInput]
        """
        return self._hyperparameters

    @hyperparameters.setter
    def hyperparameters(self, hyperparameters):
        """Sets the hyperparameters of this CreateModelCustomizationJobRequest.


        :param hyperparameters: The hyperparameters of this CreateModelCustomizationJobRequest.  # noqa: E501
        :type: list[HyperparameterForCreateModelCustomizationJobInput]
        """

        self._hyperparameters = hyperparameters

    @property
    def model_reference(self):
        """Gets the model_reference of this CreateModelCustomizationJobRequest.  # noqa: E501


        :return: The model_reference of this CreateModelCustomizationJobRequest.  # noqa: E501
        :rtype: ModelReferenceForCreateModelCustomizationJobInput
        """
        return self._model_reference

    @model_reference.setter
    def model_reference(self, model_reference):
        """Sets the model_reference of this CreateModelCustomizationJobRequest.


        :param model_reference: The model_reference of this CreateModelCustomizationJobRequest.  # noqa: E501
        :type: ModelReferenceForCreateModelCustomizationJobInput
        """

        self._model_reference = model_reference

    @property
    def name(self):
        """Gets the name of this CreateModelCustomizationJobRequest.  # noqa: E501


        :return: The name of this CreateModelCustomizationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateModelCustomizationJobRequest.


        :param name: The name of this CreateModelCustomizationJobRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this CreateModelCustomizationJobRequest.  # noqa: E501


        :return: The project_name of this CreateModelCustomizationJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateModelCustomizationJobRequest.


        :param project_name: The project_name of this CreateModelCustomizationJobRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def save_model_limit(self):
        """Gets the save_model_limit of this CreateModelCustomizationJobRequest.  # noqa: E501


        :return: The save_model_limit of this CreateModelCustomizationJobRequest.  # noqa: E501
        :rtype: int
        """
        return self._save_model_limit

    @save_model_limit.setter
    def save_model_limit(self, save_model_limit):
        """Sets the save_model_limit of this CreateModelCustomizationJobRequest.


        :param save_model_limit: The save_model_limit of this CreateModelCustomizationJobRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                save_model_limit is not None and save_model_limit > 10):  # noqa: E501
            raise ValueError("Invalid value for `save_model_limit`, must be a value less than or equal to `10`")  # noqa: E501
        if (self._configuration.client_side_validation and
                save_model_limit is not None and save_model_limit < 1):  # noqa: E501
            raise ValueError("Invalid value for `save_model_limit`, must be a value greater than or equal to `1`")  # noqa: E501

        self._save_model_limit = save_model_limit

    @property
    def tags(self):
        """Gets the tags of this CreateModelCustomizationJobRequest.  # noqa: E501


        :return: The tags of this CreateModelCustomizationJobRequest.  # noqa: E501
        :rtype: list[TagForCreateModelCustomizationJobInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateModelCustomizationJobRequest.


        :param tags: The tags of this CreateModelCustomizationJobRequest.  # noqa: E501
        :type: list[TagForCreateModelCustomizationJobInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateModelCustomizationJobRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateModelCustomizationJobRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateModelCustomizationJobRequest):
            return True

        return self.to_dict() != other.to_dict()
