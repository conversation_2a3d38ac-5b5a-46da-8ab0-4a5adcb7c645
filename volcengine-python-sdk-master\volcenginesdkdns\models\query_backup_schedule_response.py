# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QueryBackupScheduleResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'count_limit': 'int',
        'schedule': 'int'
    }

    attribute_map = {
        'count_limit': 'CountLimit',
        'schedule': 'Schedule'
    }

    def __init__(self, count_limit=None, schedule=None, _configuration=None):  # noqa: E501
        """QueryBackupScheduleResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._count_limit = None
        self._schedule = None
        self.discriminator = None

        if count_limit is not None:
            self.count_limit = count_limit
        if schedule is not None:
            self.schedule = schedule

    @property
    def count_limit(self):
        """Gets the count_limit of this QueryBackupScheduleResponse.  # noqa: E501


        :return: The count_limit of this QueryBackupScheduleResponse.  # noqa: E501
        :rtype: int
        """
        return self._count_limit

    @count_limit.setter
    def count_limit(self, count_limit):
        """Sets the count_limit of this QueryBackupScheduleResponse.


        :param count_limit: The count_limit of this QueryBackupScheduleResponse.  # noqa: E501
        :type: int
        """

        self._count_limit = count_limit

    @property
    def schedule(self):
        """Gets the schedule of this QueryBackupScheduleResponse.  # noqa: E501


        :return: The schedule of this QueryBackupScheduleResponse.  # noqa: E501
        :rtype: int
        """
        return self._schedule

    @schedule.setter
    def schedule(self, schedule):
        """Sets the schedule of this QueryBackupScheduleResponse.


        :param schedule: The schedule of this QueryBackupScheduleResponse.  # noqa: E501
        :type: int
        """

        self._schedule = schedule

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QueryBackupScheduleResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QueryBackupScheduleResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QueryBackupScheduleResponse):
            return True

        return self.to_dict() != other.to_dict()
