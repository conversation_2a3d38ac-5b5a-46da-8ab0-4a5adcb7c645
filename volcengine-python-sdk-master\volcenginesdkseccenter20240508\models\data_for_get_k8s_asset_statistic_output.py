# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetK8sAssetStatisticOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'k8s_namespace': 'int',
        'k8s_workload': 'int'
    }

    attribute_map = {
        'k8s_namespace': 'K8sNamespace',
        'k8s_workload': 'K8sWorkload'
    }

    def __init__(self, k8s_namespace=None, k8s_workload=None, _configuration=None):  # noqa: E501
        """DataForGetK8sAssetStatisticOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._k8s_namespace = None
        self._k8s_workload = None
        self.discriminator = None

        if k8s_namespace is not None:
            self.k8s_namespace = k8s_namespace
        if k8s_workload is not None:
            self.k8s_workload = k8s_workload

    @property
    def k8s_namespace(self):
        """Gets the k8s_namespace of this DataForGetK8sAssetStatisticOutput.  # noqa: E501


        :return: The k8s_namespace of this DataForGetK8sAssetStatisticOutput.  # noqa: E501
        :rtype: int
        """
        return self._k8s_namespace

    @k8s_namespace.setter
    def k8s_namespace(self, k8s_namespace):
        """Sets the k8s_namespace of this DataForGetK8sAssetStatisticOutput.


        :param k8s_namespace: The k8s_namespace of this DataForGetK8sAssetStatisticOutput.  # noqa: E501
        :type: int
        """

        self._k8s_namespace = k8s_namespace

    @property
    def k8s_workload(self):
        """Gets the k8s_workload of this DataForGetK8sAssetStatisticOutput.  # noqa: E501


        :return: The k8s_workload of this DataForGetK8sAssetStatisticOutput.  # noqa: E501
        :rtype: int
        """
        return self._k8s_workload

    @k8s_workload.setter
    def k8s_workload(self, k8s_workload):
        """Sets the k8s_workload of this DataForGetK8sAssetStatisticOutput.


        :param k8s_workload: The k8s_workload of this DataForGetK8sAssetStatisticOutput.  # noqa: E501
        :type: int
        """

        self._k8s_workload = k8s_workload

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetK8sAssetStatisticOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetK8sAssetStatisticOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetK8sAssetStatisticOutput):
            return True

        return self.to_dict() != other.to_dict()
