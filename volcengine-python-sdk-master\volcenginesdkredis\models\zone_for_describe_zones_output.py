# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ZoneForDescribeZonesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'zone_id': 'str',
        'zone_name': 'str',
        'zone_status': 'int'
    }

    attribute_map = {
        'zone_id': 'ZoneId',
        'zone_name': 'ZoneName',
        'zone_status': 'ZoneStatus'
    }

    def __init__(self, zone_id=None, zone_name=None, zone_status=None, _configuration=None):  # noqa: E501
        """ZoneForDescribeZonesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._zone_id = None
        self._zone_name = None
        self._zone_status = None
        self.discriminator = None

        if zone_id is not None:
            self.zone_id = zone_id
        if zone_name is not None:
            self.zone_name = zone_name
        if zone_status is not None:
            self.zone_status = zone_status

    @property
    def zone_id(self):
        """Gets the zone_id of this ZoneForDescribeZonesOutput.  # noqa: E501


        :return: The zone_id of this ZoneForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ZoneForDescribeZonesOutput.


        :param zone_id: The zone_id of this ZoneForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    @property
    def zone_name(self):
        """Gets the zone_name of this ZoneForDescribeZonesOutput.  # noqa: E501


        :return: The zone_name of this ZoneForDescribeZonesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this ZoneForDescribeZonesOutput.


        :param zone_name: The zone_name of this ZoneForDescribeZonesOutput.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    @property
    def zone_status(self):
        """Gets the zone_status of this ZoneForDescribeZonesOutput.  # noqa: E501


        :return: The zone_status of this ZoneForDescribeZonesOutput.  # noqa: E501
        :rtype: int
        """
        return self._zone_status

    @zone_status.setter
    def zone_status(self, zone_status):
        """Sets the zone_status of this ZoneForDescribeZonesOutput.


        :param zone_status: The zone_status of this ZoneForDescribeZonesOutput.  # noqa: E501
        :type: int
        """

        self._zone_status = zone_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ZoneForDescribeZonesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ZoneForDescribeZonesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ZoneForDescribeZonesOutput):
            return True

        return self.to_dict() != other.to_dict()
