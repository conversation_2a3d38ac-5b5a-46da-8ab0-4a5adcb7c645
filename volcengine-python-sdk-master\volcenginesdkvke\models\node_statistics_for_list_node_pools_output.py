# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeStatisticsForListNodePoolsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'creating_count': 'int',
        'deleting_count': 'int',
        'failed_count': 'int',
        'running_count': 'int',
        'total_count': 'int',
        'updating_count': 'int'
    }

    attribute_map = {
        'creating_count': 'CreatingCount',
        'deleting_count': 'DeletingCount',
        'failed_count': 'FailedCount',
        'running_count': 'RunningCount',
        'total_count': 'TotalCount',
        'updating_count': 'UpdatingCount'
    }

    def __init__(self, creating_count=None, deleting_count=None, failed_count=None, running_count=None, total_count=None, updating_count=None, _configuration=None):  # noqa: E501
        """NodeStatisticsForListNodePoolsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._creating_count = None
        self._deleting_count = None
        self._failed_count = None
        self._running_count = None
        self._total_count = None
        self._updating_count = None
        self.discriminator = None

        if creating_count is not None:
            self.creating_count = creating_count
        if deleting_count is not None:
            self.deleting_count = deleting_count
        if failed_count is not None:
            self.failed_count = failed_count
        if running_count is not None:
            self.running_count = running_count
        if total_count is not None:
            self.total_count = total_count
        if updating_count is not None:
            self.updating_count = updating_count

    @property
    def creating_count(self):
        """Gets the creating_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501


        :return: The creating_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :rtype: int
        """
        return self._creating_count

    @creating_count.setter
    def creating_count(self, creating_count):
        """Sets the creating_count of this NodeStatisticsForListNodePoolsOutput.


        :param creating_count: The creating_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :type: int
        """

        self._creating_count = creating_count

    @property
    def deleting_count(self):
        """Gets the deleting_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501


        :return: The deleting_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :rtype: int
        """
        return self._deleting_count

    @deleting_count.setter
    def deleting_count(self, deleting_count):
        """Sets the deleting_count of this NodeStatisticsForListNodePoolsOutput.


        :param deleting_count: The deleting_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :type: int
        """

        self._deleting_count = deleting_count

    @property
    def failed_count(self):
        """Gets the failed_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501


        :return: The failed_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :rtype: int
        """
        return self._failed_count

    @failed_count.setter
    def failed_count(self, failed_count):
        """Sets the failed_count of this NodeStatisticsForListNodePoolsOutput.


        :param failed_count: The failed_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :type: int
        """

        self._failed_count = failed_count

    @property
    def running_count(self):
        """Gets the running_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501


        :return: The running_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :rtype: int
        """
        return self._running_count

    @running_count.setter
    def running_count(self, running_count):
        """Sets the running_count of this NodeStatisticsForListNodePoolsOutput.


        :param running_count: The running_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :type: int
        """

        self._running_count = running_count

    @property
    def total_count(self):
        """Gets the total_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501


        :return: The total_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this NodeStatisticsForListNodePoolsOutput.


        :param total_count: The total_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    @property
    def updating_count(self):
        """Gets the updating_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501


        :return: The updating_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :rtype: int
        """
        return self._updating_count

    @updating_count.setter
    def updating_count(self, updating_count):
        """Sets the updating_count of this NodeStatisticsForListNodePoolsOutput.


        :param updating_count: The updating_count of this NodeStatisticsForListNodePoolsOutput.  # noqa: E501
        :type: int
        """

        self._updating_count = updating_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeStatisticsForListNodePoolsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeStatisticsForListNodePoolsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeStatisticsForListNodePoolsOutput):
            return True

        return self.to_dict() != other.to_dict()
