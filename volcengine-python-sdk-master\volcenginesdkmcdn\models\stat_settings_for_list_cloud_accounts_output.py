# coding: utf-8

"""
    mcdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatSettingsForListCloudAccountsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aws': 'ConvertAwsForListCloudAccountsOutput',
        'offline_data_setting': 'OfflineDataSettingForListCloudAccountsOutput'
    }

    attribute_map = {
        'aws': 'Aws',
        'offline_data_setting': 'OfflineDataSetting'
    }

    def __init__(self, aws=None, offline_data_setting=None, _configuration=None):  # noqa: E501
        """StatSettingsForListCloudAccountsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aws = None
        self._offline_data_setting = None
        self.discriminator = None

        if aws is not None:
            self.aws = aws
        if offline_data_setting is not None:
            self.offline_data_setting = offline_data_setting

    @property
    def aws(self):
        """Gets the aws of this StatSettingsForListCloudAccountsOutput.  # noqa: E501


        :return: The aws of this StatSettingsForListCloudAccountsOutput.  # noqa: E501
        :rtype: ConvertAwsForListCloudAccountsOutput
        """
        return self._aws

    @aws.setter
    def aws(self, aws):
        """Sets the aws of this StatSettingsForListCloudAccountsOutput.


        :param aws: The aws of this StatSettingsForListCloudAccountsOutput.  # noqa: E501
        :type: ConvertAwsForListCloudAccountsOutput
        """

        self._aws = aws

    @property
    def offline_data_setting(self):
        """Gets the offline_data_setting of this StatSettingsForListCloudAccountsOutput.  # noqa: E501


        :return: The offline_data_setting of this StatSettingsForListCloudAccountsOutput.  # noqa: E501
        :rtype: OfflineDataSettingForListCloudAccountsOutput
        """
        return self._offline_data_setting

    @offline_data_setting.setter
    def offline_data_setting(self, offline_data_setting):
        """Sets the offline_data_setting of this StatSettingsForListCloudAccountsOutput.


        :param offline_data_setting: The offline_data_setting of this StatSettingsForListCloudAccountsOutput.  # noqa: E501
        :type: OfflineDataSettingForListCloudAccountsOutput
        """

        self._offline_data_setting = offline_data_setting

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatSettingsForListCloudAccountsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatSettingsForListCloudAccountsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatSettingsForListCloudAccountsOutput):
            return True

        return self.to_dict() != other.to_dict()
