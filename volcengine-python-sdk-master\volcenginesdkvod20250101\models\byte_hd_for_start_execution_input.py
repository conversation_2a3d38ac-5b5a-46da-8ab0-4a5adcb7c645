# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ByteHDForStartExecutionInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'template_id': 'str',
        'watermark_template_id': 'str'
    }

    attribute_map = {
        'template_id': 'TemplateId',
        'watermark_template_id': 'WatermarkTemplateId'
    }

    def __init__(self, template_id=None, watermark_template_id=None, _configuration=None):  # noqa: E501
        """ByteHDForStartExecutionInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._template_id = None
        self._watermark_template_id = None
        self.discriminator = None

        if template_id is not None:
            self.template_id = template_id
        if watermark_template_id is not None:
            self.watermark_template_id = watermark_template_id

    @property
    def template_id(self):
        """Gets the template_id of this ByteHDForStartExecutionInput.  # noqa: E501


        :return: The template_id of this ByteHDForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._template_id

    @template_id.setter
    def template_id(self, template_id):
        """Sets the template_id of this ByteHDForStartExecutionInput.


        :param template_id: The template_id of this ByteHDForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._template_id = template_id

    @property
    def watermark_template_id(self):
        """Gets the watermark_template_id of this ByteHDForStartExecutionInput.  # noqa: E501


        :return: The watermark_template_id of this ByteHDForStartExecutionInput.  # noqa: E501
        :rtype: str
        """
        return self._watermark_template_id

    @watermark_template_id.setter
    def watermark_template_id(self, watermark_template_id):
        """Sets the watermark_template_id of this ByteHDForStartExecutionInput.


        :param watermark_template_id: The watermark_template_id of this ByteHDForStartExecutionInput.  # noqa: E501
        :type: str
        """

        self._watermark_template_id = watermark_template_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ByteHDForStartExecutionInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ByteHDForStartExecutionInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ByteHDForStartExecutionInput):
            return True

        return self.to_dict() != other.to_dict()
