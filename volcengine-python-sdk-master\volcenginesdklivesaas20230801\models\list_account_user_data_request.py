# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAccountUserDataRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'ban_status': 'int',
        'key_word': 'str',
        'page_number': 'float',
        'page_size': 'int',
        'page_token': 'str',
        'play_status': 'str',
        'select_tags': 'list[SelectTagForListAccountUserDataInput]',
        'sort_field': 'str',
        'sort_mode': 'str',
        'user_ids': 'list[int]'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'ban_status': 'BanStatus',
        'key_word': 'KeyWord',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'page_token': 'PageToken',
        'play_status': 'PlayStatus',
        'select_tags': 'SelectTags',
        'sort_field': 'SortField',
        'sort_mode': 'SortMode',
        'user_ids': 'UserIds'
    }

    def __init__(self, activity_id=None, ban_status=None, key_word=None, page_number=None, page_size=None, page_token=None, play_status=None, select_tags=None, sort_field=None, sort_mode=None, user_ids=None, _configuration=None):  # noqa: E501
        """ListAccountUserDataRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._ban_status = None
        self._key_word = None
        self._page_number = None
        self._page_size = None
        self._page_token = None
        self._play_status = None
        self._select_tags = None
        self._sort_field = None
        self._sort_mode = None
        self._user_ids = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if ban_status is not None:
            self.ban_status = ban_status
        if key_word is not None:
            self.key_word = key_word
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if page_token is not None:
            self.page_token = page_token
        if play_status is not None:
            self.play_status = play_status
        if select_tags is not None:
            self.select_tags = select_tags
        if sort_field is not None:
            self.sort_field = sort_field
        if sort_mode is not None:
            self.sort_mode = sort_mode
        if user_ids is not None:
            self.user_ids = user_ids

    @property
    def activity_id(self):
        """Gets the activity_id of this ListAccountUserDataRequest.  # noqa: E501


        :return: The activity_id of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this ListAccountUserDataRequest.


        :param activity_id: The activity_id of this ListAccountUserDataRequest.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def ban_status(self):
        """Gets the ban_status of this ListAccountUserDataRequest.  # noqa: E501


        :return: The ban_status of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._ban_status

    @ban_status.setter
    def ban_status(self, ban_status):
        """Sets the ban_status of this ListAccountUserDataRequest.


        :param ban_status: The ban_status of this ListAccountUserDataRequest.  # noqa: E501
        :type: int
        """

        self._ban_status = ban_status

    @property
    def key_word(self):
        """Gets the key_word of this ListAccountUserDataRequest.  # noqa: E501


        :return: The key_word of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_word

    @key_word.setter
    def key_word(self, key_word):
        """Sets the key_word of this ListAccountUserDataRequest.


        :param key_word: The key_word of this ListAccountUserDataRequest.  # noqa: E501
        :type: str
        """

        self._key_word = key_word

    @property
    def page_number(self):
        """Gets the page_number of this ListAccountUserDataRequest.  # noqa: E501


        :return: The page_number of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: float
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListAccountUserDataRequest.


        :param page_number: The page_number of this ListAccountUserDataRequest.  # noqa: E501
        :type: float
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListAccountUserDataRequest.  # noqa: E501


        :return: The page_size of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListAccountUserDataRequest.


        :param page_size: The page_size of this ListAccountUserDataRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def page_token(self):
        """Gets the page_token of this ListAccountUserDataRequest.  # noqa: E501


        :return: The page_token of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_token

    @page_token.setter
    def page_token(self, page_token):
        """Sets the page_token of this ListAccountUserDataRequest.


        :param page_token: The page_token of this ListAccountUserDataRequest.  # noqa: E501
        :type: str
        """

        self._page_token = page_token

    @property
    def play_status(self):
        """Gets the play_status of this ListAccountUserDataRequest.  # noqa: E501


        :return: The play_status of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._play_status

    @play_status.setter
    def play_status(self, play_status):
        """Sets the play_status of this ListAccountUserDataRequest.


        :param play_status: The play_status of this ListAccountUserDataRequest.  # noqa: E501
        :type: str
        """

        self._play_status = play_status

    @property
    def select_tags(self):
        """Gets the select_tags of this ListAccountUserDataRequest.  # noqa: E501


        :return: The select_tags of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: list[SelectTagForListAccountUserDataInput]
        """
        return self._select_tags

    @select_tags.setter
    def select_tags(self, select_tags):
        """Sets the select_tags of this ListAccountUserDataRequest.


        :param select_tags: The select_tags of this ListAccountUserDataRequest.  # noqa: E501
        :type: list[SelectTagForListAccountUserDataInput]
        """

        self._select_tags = select_tags

    @property
    def sort_field(self):
        """Gets the sort_field of this ListAccountUserDataRequest.  # noqa: E501


        :return: The sort_field of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_field

    @sort_field.setter
    def sort_field(self, sort_field):
        """Sets the sort_field of this ListAccountUserDataRequest.


        :param sort_field: The sort_field of this ListAccountUserDataRequest.  # noqa: E501
        :type: str
        """

        self._sort_field = sort_field

    @property
    def sort_mode(self):
        """Gets the sort_mode of this ListAccountUserDataRequest.  # noqa: E501


        :return: The sort_mode of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_mode

    @sort_mode.setter
    def sort_mode(self, sort_mode):
        """Sets the sort_mode of this ListAccountUserDataRequest.


        :param sort_mode: The sort_mode of this ListAccountUserDataRequest.  # noqa: E501
        :type: str
        """

        self._sort_mode = sort_mode

    @property
    def user_ids(self):
        """Gets the user_ids of this ListAccountUserDataRequest.  # noqa: E501


        :return: The user_ids of this ListAccountUserDataRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._user_ids

    @user_ids.setter
    def user_ids(self, user_ids):
        """Sets the user_ids of this ListAccountUserDataRequest.


        :param user_ids: The user_ids of this ListAccountUserDataRequest.  # noqa: E501
        :type: list[int]
        """

        self._user_ids = user_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAccountUserDataRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAccountUserDataRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAccountUserDataRequest):
            return True

        return self.to_dict() != other.to_dict()
