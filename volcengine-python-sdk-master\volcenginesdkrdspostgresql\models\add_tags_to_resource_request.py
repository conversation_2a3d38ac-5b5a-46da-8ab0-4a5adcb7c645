# coding: utf-8

"""
    rds_postgresql

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AddTagsToResourceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_ids': 'list[str]',
        'tags': 'list[TagForAddTagsToResourceInput]'
    }

    attribute_map = {
        'instance_ids': 'InstanceIds',
        'tags': 'Tags'
    }

    def __init__(self, instance_ids=None, tags=None, _configuration=None):  # noqa: E501
        """AddTagsToResourceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_ids = None
        self._tags = None
        self.discriminator = None

        if instance_ids is not None:
            self.instance_ids = instance_ids
        if tags is not None:
            self.tags = tags

    @property
    def instance_ids(self):
        """Gets the instance_ids of this AddTagsToResourceRequest.  # noqa: E501


        :return: The instance_ids of this AddTagsToResourceRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ids

    @instance_ids.setter
    def instance_ids(self, instance_ids):
        """Sets the instance_ids of this AddTagsToResourceRequest.


        :param instance_ids: The instance_ids of this AddTagsToResourceRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_ids = instance_ids

    @property
    def tags(self):
        """Gets the tags of this AddTagsToResourceRequest.  # noqa: E501


        :return: The tags of this AddTagsToResourceRequest.  # noqa: E501
        :rtype: list[TagForAddTagsToResourceInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this AddTagsToResourceRequest.


        :param tags: The tags of this AddTagsToResourceRequest.  # noqa: E501
        :type: list[TagForAddTagsToResourceInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddTagsToResourceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddTagsToResourceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AddTagsToResourceRequest):
            return True

        return self.to_dict() != other.to_dict()
