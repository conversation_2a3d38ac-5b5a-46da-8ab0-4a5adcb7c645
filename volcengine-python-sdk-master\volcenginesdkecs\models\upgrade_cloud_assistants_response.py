# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpgradeCloudAssistantsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'failed_instances': 'list[FailedInstanceForUpgradeCloudAssistantsOutput]',
        'upgrading_instance_ids': 'list[str]'
    }

    attribute_map = {
        'failed_instances': 'FailedInstances',
        'upgrading_instance_ids': 'UpgradingInstanceIds'
    }

    def __init__(self, failed_instances=None, upgrading_instance_ids=None, _configuration=None):  # noqa: E501
        """UpgradeCloudAssistantsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._failed_instances = None
        self._upgrading_instance_ids = None
        self.discriminator = None

        if failed_instances is not None:
            self.failed_instances = failed_instances
        if upgrading_instance_ids is not None:
            self.upgrading_instance_ids = upgrading_instance_ids

    @property
    def failed_instances(self):
        """Gets the failed_instances of this UpgradeCloudAssistantsResponse.  # noqa: E501


        :return: The failed_instances of this UpgradeCloudAssistantsResponse.  # noqa: E501
        :rtype: list[FailedInstanceForUpgradeCloudAssistantsOutput]
        """
        return self._failed_instances

    @failed_instances.setter
    def failed_instances(self, failed_instances):
        """Sets the failed_instances of this UpgradeCloudAssistantsResponse.


        :param failed_instances: The failed_instances of this UpgradeCloudAssistantsResponse.  # noqa: E501
        :type: list[FailedInstanceForUpgradeCloudAssistantsOutput]
        """

        self._failed_instances = failed_instances

    @property
    def upgrading_instance_ids(self):
        """Gets the upgrading_instance_ids of this UpgradeCloudAssistantsResponse.  # noqa: E501


        :return: The upgrading_instance_ids of this UpgradeCloudAssistantsResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._upgrading_instance_ids

    @upgrading_instance_ids.setter
    def upgrading_instance_ids(self, upgrading_instance_ids):
        """Sets the upgrading_instance_ids of this UpgradeCloudAssistantsResponse.


        :param upgrading_instance_ids: The upgrading_instance_ids of this UpgradeCloudAssistantsResponse.  # noqa: E501
        :type: list[str]
        """

        self._upgrading_instance_ids = upgrading_instance_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpgradeCloudAssistantsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpgradeCloudAssistantsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpgradeCloudAssistantsResponse):
            return True

        return self.to_dict() != other.to_dict()
