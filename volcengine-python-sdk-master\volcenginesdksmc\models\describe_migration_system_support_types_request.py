# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeMigrationSystemSupportTypesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'os': 'str',
        'platform': 'str',
        'platform_version': 'str',
        'project_name': 'str'
    }

    attribute_map = {
        'os': 'OS',
        'platform': 'Platform',
        'platform_version': 'PlatformVersion',
        'project_name': 'ProjectName'
    }

    def __init__(self, os=None, platform=None, platform_version=None, project_name=None, _configuration=None):  # noqa: E501
        """DescribeMigrationSystemSupportTypesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._os = None
        self._platform = None
        self._platform_version = None
        self._project_name = None
        self.discriminator = None

        self.os = os
        self.platform = platform
        self.platform_version = platform_version
        if project_name is not None:
            self.project_name = project_name

    @property
    def os(self):
        """Gets the os of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501


        :return: The os of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this DescribeMigrationSystemSupportTypesRequest.


        :param os: The os of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and os is None:
            raise ValueError("Invalid value for `os`, must not be `None`")  # noqa: E501

        self._os = os

    @property
    def platform(self):
        """Gets the platform of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501


        :return: The platform of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this DescribeMigrationSystemSupportTypesRequest.


        :param platform: The platform of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and platform is None:
            raise ValueError("Invalid value for `platform`, must not be `None`")  # noqa: E501

        self._platform = platform

    @property
    def platform_version(self):
        """Gets the platform_version of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501


        :return: The platform_version of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._platform_version

    @platform_version.setter
    def platform_version(self, platform_version):
        """Sets the platform_version of this DescribeMigrationSystemSupportTypesRequest.


        :param platform_version: The platform_version of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and platform_version is None:
            raise ValueError("Invalid value for `platform_version`, must not be `None`")  # noqa: E501

        self._platform_version = platform_version

    @property
    def project_name(self):
        """Gets the project_name of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501


        :return: The project_name of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeMigrationSystemSupportTypesRequest.


        :param project_name: The project_name of this DescribeMigrationSystemSupportTypesRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeMigrationSystemSupportTypesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeMigrationSystemSupportTypesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeMigrationSystemSupportTypesRequest):
            return True

        return self.to_dict() != other.to_dict()
