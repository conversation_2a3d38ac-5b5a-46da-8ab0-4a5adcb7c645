# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConvertAsrForGetExecutionOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'confidence': 'float',
        'duration': 'float',
        'mode': 'str',
        'utterances': 'list[UtteranceForGetExecutionOutput]'
    }

    attribute_map = {
        'confidence': 'Confidence',
        'duration': 'Duration',
        'mode': 'Mode',
        'utterances': 'Utterances'
    }

    def __init__(self, confidence=None, duration=None, mode=None, utterances=None, _configuration=None):  # noqa: E501
        """ConvertAsrForGetExecutionOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._confidence = None
        self._duration = None
        self._mode = None
        self._utterances = None
        self.discriminator = None

        if confidence is not None:
            self.confidence = confidence
        if duration is not None:
            self.duration = duration
        if mode is not None:
            self.mode = mode
        if utterances is not None:
            self.utterances = utterances

    @property
    def confidence(self):
        """Gets the confidence of this ConvertAsrForGetExecutionOutput.  # noqa: E501


        :return: The confidence of this ConvertAsrForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._confidence

    @confidence.setter
    def confidence(self, confidence):
        """Sets the confidence of this ConvertAsrForGetExecutionOutput.


        :param confidence: The confidence of this ConvertAsrForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._confidence = confidence

    @property
    def duration(self):
        """Gets the duration of this ConvertAsrForGetExecutionOutput.  # noqa: E501


        :return: The duration of this ConvertAsrForGetExecutionOutput.  # noqa: E501
        :rtype: float
        """
        return self._duration

    @duration.setter
    def duration(self, duration):
        """Sets the duration of this ConvertAsrForGetExecutionOutput.


        :param duration: The duration of this ConvertAsrForGetExecutionOutput.  # noqa: E501
        :type: float
        """

        self._duration = duration

    @property
    def mode(self):
        """Gets the mode of this ConvertAsrForGetExecutionOutput.  # noqa: E501


        :return: The mode of this ConvertAsrForGetExecutionOutput.  # noqa: E501
        :rtype: str
        """
        return self._mode

    @mode.setter
    def mode(self, mode):
        """Sets the mode of this ConvertAsrForGetExecutionOutput.


        :param mode: The mode of this ConvertAsrForGetExecutionOutput.  # noqa: E501
        :type: str
        """

        self._mode = mode

    @property
    def utterances(self):
        """Gets the utterances of this ConvertAsrForGetExecutionOutput.  # noqa: E501


        :return: The utterances of this ConvertAsrForGetExecutionOutput.  # noqa: E501
        :rtype: list[UtteranceForGetExecutionOutput]
        """
        return self._utterances

    @utterances.setter
    def utterances(self, utterances):
        """Sets the utterances of this ConvertAsrForGetExecutionOutput.


        :param utterances: The utterances of this ConvertAsrForGetExecutionOutput.  # noqa: E501
        :type: list[UtteranceForGetExecutionOutput]
        """

        self._utterances = utterances

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConvertAsrForGetExecutionOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConvertAsrForGetExecutionOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConvertAsrForGetExecutionOutput):
            return True

        return self.to_dict() != other.to_dict()
