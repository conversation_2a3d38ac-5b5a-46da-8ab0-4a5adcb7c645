# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetAdvertisementDataDetailAPIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'channel': 'str',
        'page_advertise_types': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'page_token': 'str',
        'sort_field': 'str',
        'sort_mode': 'str'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'channel': 'Channel',
        'page_advertise_types': 'PageAdvertiseTypes',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'page_token': 'PageToken',
        'sort_field': 'SortField',
        'sort_mode': 'SortMode'
    }

    def __init__(self, activity_id=None, channel=None, page_advertise_types=None, page_number=None, page_size=None, page_token=None, sort_field=None, sort_mode=None, _configuration=None):  # noqa: E501
        """GetAdvertisementDataDetailAPIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._channel = None
        self._page_advertise_types = None
        self._page_number = None
        self._page_size = None
        self._page_token = None
        self._sort_field = None
        self._sort_mode = None
        self.discriminator = None

        self.activity_id = activity_id
        if channel is not None:
            self.channel = channel
        if page_advertise_types is not None:
            self.page_advertise_types = page_advertise_types
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if page_token is not None:
            self.page_token = page_token
        if sort_field is not None:
            self.sort_field = sort_field
        if sort_mode is not None:
            self.sort_mode = sort_mode

    @property
    def activity_id(self):
        """Gets the activity_id of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501


        :return: The activity_id of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this GetAdvertisementDataDetailAPIRequest.


        :param activity_id: The activity_id of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and activity_id is None:
            raise ValueError("Invalid value for `activity_id`, must not be `None`")  # noqa: E501

        self._activity_id = activity_id

    @property
    def channel(self):
        """Gets the channel of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501


        :return: The channel of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._channel

    @channel.setter
    def channel(self, channel):
        """Sets the channel of this GetAdvertisementDataDetailAPIRequest.


        :param channel: The channel of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :type: str
        """

        self._channel = channel

    @property
    def page_advertise_types(self):
        """Gets the page_advertise_types of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501


        :return: The page_advertise_types of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_advertise_types

    @page_advertise_types.setter
    def page_advertise_types(self, page_advertise_types):
        """Sets the page_advertise_types of this GetAdvertisementDataDetailAPIRequest.


        :param page_advertise_types: The page_advertise_types of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :type: str
        """

        self._page_advertise_types = page_advertise_types

    @property
    def page_number(self):
        """Gets the page_number of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501


        :return: The page_number of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this GetAdvertisementDataDetailAPIRequest.


        :param page_number: The page_number of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501


        :return: The page_size of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this GetAdvertisementDataDetailAPIRequest.


        :param page_size: The page_size of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def page_token(self):
        """Gets the page_token of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501


        :return: The page_token of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_token

    @page_token.setter
    def page_token(self, page_token):
        """Sets the page_token of this GetAdvertisementDataDetailAPIRequest.


        :param page_token: The page_token of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :type: str
        """

        self._page_token = page_token

    @property
    def sort_field(self):
        """Gets the sort_field of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501


        :return: The sort_field of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_field

    @sort_field.setter
    def sort_field(self, sort_field):
        """Sets the sort_field of this GetAdvertisementDataDetailAPIRequest.


        :param sort_field: The sort_field of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :type: str
        """

        self._sort_field = sort_field

    @property
    def sort_mode(self):
        """Gets the sort_mode of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501


        :return: The sort_mode of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_mode

    @sort_mode.setter
    def sort_mode(self, sort_mode):
        """Sets the sort_mode of this GetAdvertisementDataDetailAPIRequest.


        :param sort_mode: The sort_mode of this GetAdvertisementDataDetailAPIRequest.  # noqa: E501
        :type: str
        """

        self._sort_mode = sort_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetAdvertisementDataDetailAPIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetAdvertisementDataDetailAPIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetAdvertisementDataDetailAPIRequest):
            return True

        return self.to_dict() != other.to_dict()
