# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateNodesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'additional_container_storage_enabled': 'bool',
        'client_token': 'str',
        'cluster_id': 'str',
        'container_storage_path': 'str',
        'image_id': 'str',
        'initialize_script': 'str',
        'instance_ids': 'list[str]',
        'keep_instance_name': 'bool',
        'kubernetes_config': 'KubernetesConfigForCreateNodesInput',
        'node_pool_id': 'str'
    }

    attribute_map = {
        'additional_container_storage_enabled': 'AdditionalContainerStorageEnabled',
        'client_token': 'ClientToken',
        'cluster_id': 'ClusterId',
        'container_storage_path': 'ContainerStoragePath',
        'image_id': 'ImageId',
        'initialize_script': 'InitializeScript',
        'instance_ids': 'InstanceIds',
        'keep_instance_name': 'KeepInstanceName',
        'kubernetes_config': 'KubernetesConfig',
        'node_pool_id': 'NodePoolId'
    }

    def __init__(self, additional_container_storage_enabled=None, client_token=None, cluster_id=None, container_storage_path=None, image_id=None, initialize_script=None, instance_ids=None, keep_instance_name=None, kubernetes_config=None, node_pool_id=None, _configuration=None):  # noqa: E501
        """CreateNodesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._additional_container_storage_enabled = None
        self._client_token = None
        self._cluster_id = None
        self._container_storage_path = None
        self._image_id = None
        self._initialize_script = None
        self._instance_ids = None
        self._keep_instance_name = None
        self._kubernetes_config = None
        self._node_pool_id = None
        self.discriminator = None

        if additional_container_storage_enabled is not None:
            self.additional_container_storage_enabled = additional_container_storage_enabled
        if client_token is not None:
            self.client_token = client_token
        self.cluster_id = cluster_id
        if container_storage_path is not None:
            self.container_storage_path = container_storage_path
        if image_id is not None:
            self.image_id = image_id
        if initialize_script is not None:
            self.initialize_script = initialize_script
        if instance_ids is not None:
            self.instance_ids = instance_ids
        if keep_instance_name is not None:
            self.keep_instance_name = keep_instance_name
        if kubernetes_config is not None:
            self.kubernetes_config = kubernetes_config
        if node_pool_id is not None:
            self.node_pool_id = node_pool_id

    @property
    def additional_container_storage_enabled(self):
        """Gets the additional_container_storage_enabled of this CreateNodesRequest.  # noqa: E501


        :return: The additional_container_storage_enabled of this CreateNodesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._additional_container_storage_enabled

    @additional_container_storage_enabled.setter
    def additional_container_storage_enabled(self, additional_container_storage_enabled):
        """Sets the additional_container_storage_enabled of this CreateNodesRequest.


        :param additional_container_storage_enabled: The additional_container_storage_enabled of this CreateNodesRequest.  # noqa: E501
        :type: bool
        """

        self._additional_container_storage_enabled = additional_container_storage_enabled

    @property
    def client_token(self):
        """Gets the client_token of this CreateNodesRequest.  # noqa: E501


        :return: The client_token of this CreateNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreateNodesRequest.


        :param client_token: The client_token of this CreateNodesRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def cluster_id(self):
        """Gets the cluster_id of this CreateNodesRequest.  # noqa: E501


        :return: The cluster_id of this CreateNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this CreateNodesRequest.


        :param cluster_id: The cluster_id of this CreateNodesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def container_storage_path(self):
        """Gets the container_storage_path of this CreateNodesRequest.  # noqa: E501


        :return: The container_storage_path of this CreateNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._container_storage_path

    @container_storage_path.setter
    def container_storage_path(self, container_storage_path):
        """Sets the container_storage_path of this CreateNodesRequest.


        :param container_storage_path: The container_storage_path of this CreateNodesRequest.  # noqa: E501
        :type: str
        """

        self._container_storage_path = container_storage_path

    @property
    def image_id(self):
        """Gets the image_id of this CreateNodesRequest.  # noqa: E501


        :return: The image_id of this CreateNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._image_id

    @image_id.setter
    def image_id(self, image_id):
        """Sets the image_id of this CreateNodesRequest.


        :param image_id: The image_id of this CreateNodesRequest.  # noqa: E501
        :type: str
        """

        self._image_id = image_id

    @property
    def initialize_script(self):
        """Gets the initialize_script of this CreateNodesRequest.  # noqa: E501


        :return: The initialize_script of this CreateNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._initialize_script

    @initialize_script.setter
    def initialize_script(self, initialize_script):
        """Sets the initialize_script of this CreateNodesRequest.


        :param initialize_script: The initialize_script of this CreateNodesRequest.  # noqa: E501
        :type: str
        """

        self._initialize_script = initialize_script

    @property
    def instance_ids(self):
        """Gets the instance_ids of this CreateNodesRequest.  # noqa: E501


        :return: The instance_ids of this CreateNodesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ids

    @instance_ids.setter
    def instance_ids(self, instance_ids):
        """Sets the instance_ids of this CreateNodesRequest.


        :param instance_ids: The instance_ids of this CreateNodesRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_ids = instance_ids

    @property
    def keep_instance_name(self):
        """Gets the keep_instance_name of this CreateNodesRequest.  # noqa: E501


        :return: The keep_instance_name of this CreateNodesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._keep_instance_name

    @keep_instance_name.setter
    def keep_instance_name(self, keep_instance_name):
        """Sets the keep_instance_name of this CreateNodesRequest.


        :param keep_instance_name: The keep_instance_name of this CreateNodesRequest.  # noqa: E501
        :type: bool
        """

        self._keep_instance_name = keep_instance_name

    @property
    def kubernetes_config(self):
        """Gets the kubernetes_config of this CreateNodesRequest.  # noqa: E501


        :return: The kubernetes_config of this CreateNodesRequest.  # noqa: E501
        :rtype: KubernetesConfigForCreateNodesInput
        """
        return self._kubernetes_config

    @kubernetes_config.setter
    def kubernetes_config(self, kubernetes_config):
        """Sets the kubernetes_config of this CreateNodesRequest.


        :param kubernetes_config: The kubernetes_config of this CreateNodesRequest.  # noqa: E501
        :type: KubernetesConfigForCreateNodesInput
        """

        self._kubernetes_config = kubernetes_config

    @property
    def node_pool_id(self):
        """Gets the node_pool_id of this CreateNodesRequest.  # noqa: E501


        :return: The node_pool_id of this CreateNodesRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_pool_id

    @node_pool_id.setter
    def node_pool_id(self, node_pool_id):
        """Sets the node_pool_id of this CreateNodesRequest.


        :param node_pool_id: The node_pool_id of this CreateNodesRequest.  # noqa: E501
        :type: str
        """

        self._node_pool_id = node_pool_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateNodesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateNodesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateNodesRequest):
            return True

        return self.to_dict() != other.to_dict()
