# coding: utf-8

"""
    advdefence20230308

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAttackFlowRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'begin_time': 'int',
        'end_time': 'int',
        'instance_ips': 'list[str]',
        'tab': 'str'
    }

    attribute_map = {
        'begin_time': 'BeginTime',
        'end_time': 'EndTime',
        'instance_ips': 'InstanceIps',
        'tab': 'Tab'
    }

    def __init__(self, begin_time=None, end_time=None, instance_ips=None, tab=None, _configuration=None):  # noqa: E501
        """DescribeAttackFlowRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._begin_time = None
        self._end_time = None
        self._instance_ips = None
        self._tab = None
        self.discriminator = None

        self.begin_time = begin_time
        self.end_time = end_time
        if instance_ips is not None:
            self.instance_ips = instance_ips
        if tab is not None:
            self.tab = tab

    @property
    def begin_time(self):
        """Gets the begin_time of this DescribeAttackFlowRequest.  # noqa: E501


        :return: The begin_time of this DescribeAttackFlowRequest.  # noqa: E501
        :rtype: int
        """
        return self._begin_time

    @begin_time.setter
    def begin_time(self, begin_time):
        """Sets the begin_time of this DescribeAttackFlowRequest.


        :param begin_time: The begin_time of this DescribeAttackFlowRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and begin_time is None:
            raise ValueError("Invalid value for `begin_time`, must not be `None`")  # noqa: E501

        self._begin_time = begin_time

    @property
    def end_time(self):
        """Gets the end_time of this DescribeAttackFlowRequest.  # noqa: E501


        :return: The end_time of this DescribeAttackFlowRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DescribeAttackFlowRequest.


        :param end_time: The end_time of this DescribeAttackFlowRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and end_time is None:
            raise ValueError("Invalid value for `end_time`, must not be `None`")  # noqa: E501

        self._end_time = end_time

    @property
    def instance_ips(self):
        """Gets the instance_ips of this DescribeAttackFlowRequest.  # noqa: E501


        :return: The instance_ips of this DescribeAttackFlowRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._instance_ips

    @instance_ips.setter
    def instance_ips(self, instance_ips):
        """Sets the instance_ips of this DescribeAttackFlowRequest.


        :param instance_ips: The instance_ips of this DescribeAttackFlowRequest.  # noqa: E501
        :type: list[str]
        """

        self._instance_ips = instance_ips

    @property
    def tab(self):
        """Gets the tab of this DescribeAttackFlowRequest.  # noqa: E501


        :return: The tab of this DescribeAttackFlowRequest.  # noqa: E501
        :rtype: str
        """
        return self._tab

    @tab.setter
    def tab(self, tab):
        """Sets the tab of this DescribeAttackFlowRequest.


        :param tab: The tab of this DescribeAttackFlowRequest.  # noqa: E501
        :type: str
        """

        self._tab = tab

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAttackFlowRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAttackFlowRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAttackFlowRequest):
            return True

        return self.to_dict() != other.to_dict()
