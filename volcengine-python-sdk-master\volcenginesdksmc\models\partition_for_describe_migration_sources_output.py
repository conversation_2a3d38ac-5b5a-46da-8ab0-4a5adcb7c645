# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PartitionForDescribeMigrationSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'file_system_format': 'str',
        'mountpoint': 'str',
        'partition_index': 'int',
        'partition_size': 'int'
    }

    attribute_map = {
        'file_system_format': 'FileSystemFormat',
        'mountpoint': 'Mountpoint',
        'partition_index': 'PartitionIndex',
        'partition_size': 'PartitionSize'
    }

    def __init__(self, file_system_format=None, mountpoint=None, partition_index=None, partition_size=None, _configuration=None):  # noqa: E501
        """PartitionForDescribeMigrationSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._file_system_format = None
        self._mountpoint = None
        self._partition_index = None
        self._partition_size = None
        self.discriminator = None

        if file_system_format is not None:
            self.file_system_format = file_system_format
        if mountpoint is not None:
            self.mountpoint = mountpoint
        if partition_index is not None:
            self.partition_index = partition_index
        if partition_size is not None:
            self.partition_size = partition_size

    @property
    def file_system_format(self):
        """Gets the file_system_format of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The file_system_format of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_format

    @file_system_format.setter
    def file_system_format(self, file_system_format):
        """Sets the file_system_format of this PartitionForDescribeMigrationSourcesOutput.


        :param file_system_format: The file_system_format of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._file_system_format = file_system_format

    @property
    def mountpoint(self):
        """Gets the mountpoint of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The mountpoint of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: str
        """
        return self._mountpoint

    @mountpoint.setter
    def mountpoint(self, mountpoint):
        """Sets the mountpoint of this PartitionForDescribeMigrationSourcesOutput.


        :param mountpoint: The mountpoint of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: str
        """

        self._mountpoint = mountpoint

    @property
    def partition_index(self):
        """Gets the partition_index of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The partition_index of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._partition_index

    @partition_index.setter
    def partition_index(self, partition_index):
        """Sets the partition_index of this PartitionForDescribeMigrationSourcesOutput.


        :param partition_index: The partition_index of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._partition_index = partition_index

    @property
    def partition_size(self):
        """Gets the partition_size of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501


        :return: The partition_size of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501
        :rtype: int
        """
        return self._partition_size

    @partition_size.setter
    def partition_size(self, partition_size):
        """Sets the partition_size of this PartitionForDescribeMigrationSourcesOutput.


        :param partition_size: The partition_size of this PartitionForDescribeMigrationSourcesOutput.  # noqa: E501
        :type: int
        """

        self._partition_size = partition_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PartitionForDescribeMigrationSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PartitionForDescribeMigrationSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PartitionForDescribeMigrationSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
