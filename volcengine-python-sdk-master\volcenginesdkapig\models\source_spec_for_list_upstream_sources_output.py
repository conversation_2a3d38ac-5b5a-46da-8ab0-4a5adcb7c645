# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SourceSpecForListUpstreamSourcesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'k8_s_source': 'K8SSourceForListUpstreamSourcesOutput',
        'nacos_source': 'NacosSourceForListUpstreamSourcesOutput'
    }

    attribute_map = {
        'k8_s_source': 'K8SSource',
        'nacos_source': 'NacosSource'
    }

    def __init__(self, k8_s_source=None, nacos_source=None, _configuration=None):  # noqa: E501
        """SourceSpecForListUpstreamSourcesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._k8_s_source = None
        self._nacos_source = None
        self.discriminator = None

        if k8_s_source is not None:
            self.k8_s_source = k8_s_source
        if nacos_source is not None:
            self.nacos_source = nacos_source

    @property
    def k8_s_source(self):
        """Gets the k8_s_source of this SourceSpecForListUpstreamSourcesOutput.  # noqa: E501


        :return: The k8_s_source of this SourceSpecForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: K8SSourceForListUpstreamSourcesOutput
        """
        return self._k8_s_source

    @k8_s_source.setter
    def k8_s_source(self, k8_s_source):
        """Sets the k8_s_source of this SourceSpecForListUpstreamSourcesOutput.


        :param k8_s_source: The k8_s_source of this SourceSpecForListUpstreamSourcesOutput.  # noqa: E501
        :type: K8SSourceForListUpstreamSourcesOutput
        """

        self._k8_s_source = k8_s_source

    @property
    def nacos_source(self):
        """Gets the nacos_source of this SourceSpecForListUpstreamSourcesOutput.  # noqa: E501


        :return: The nacos_source of this SourceSpecForListUpstreamSourcesOutput.  # noqa: E501
        :rtype: NacosSourceForListUpstreamSourcesOutput
        """
        return self._nacos_source

    @nacos_source.setter
    def nacos_source(self, nacos_source):
        """Sets the nacos_source of this SourceSpecForListUpstreamSourcesOutput.


        :param nacos_source: The nacos_source of this SourceSpecForListUpstreamSourcesOutput.  # noqa: E501
        :type: NacosSourceForListUpstreamSourcesOutput
        """

        self._nacos_source = nacos_source

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SourceSpecForListUpstreamSourcesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SourceSpecForListUpstreamSourcesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SourceSpecForListUpstreamSourcesOutput):
            return True

        return self.to_dict() != other.to_dict()
