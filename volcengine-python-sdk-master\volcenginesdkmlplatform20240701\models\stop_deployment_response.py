# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StopDeploymentResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'deployment_id': 'str',
        'deployment_version_id': 'str',
        'service_id': 'str',
        'status': 'StatusForStopDeploymentOutput'
    }

    attribute_map = {
        'deployment_id': 'DeploymentId',
        'deployment_version_id': 'DeploymentVersionId',
        'service_id': 'ServiceId',
        'status': 'Status'
    }

    def __init__(self, deployment_id=None, deployment_version_id=None, service_id=None, status=None, _configuration=None):  # noqa: E501
        """StopDeploymentResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._deployment_id = None
        self._deployment_version_id = None
        self._service_id = None
        self._status = None
        self.discriminator = None

        if deployment_id is not None:
            self.deployment_id = deployment_id
        if deployment_version_id is not None:
            self.deployment_version_id = deployment_version_id
        if service_id is not None:
            self.service_id = service_id
        if status is not None:
            self.status = status

    @property
    def deployment_id(self):
        """Gets the deployment_id of this StopDeploymentResponse.  # noqa: E501


        :return: The deployment_id of this StopDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._deployment_id

    @deployment_id.setter
    def deployment_id(self, deployment_id):
        """Sets the deployment_id of this StopDeploymentResponse.


        :param deployment_id: The deployment_id of this StopDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._deployment_id = deployment_id

    @property
    def deployment_version_id(self):
        """Gets the deployment_version_id of this StopDeploymentResponse.  # noqa: E501


        :return: The deployment_version_id of this StopDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._deployment_version_id

    @deployment_version_id.setter
    def deployment_version_id(self, deployment_version_id):
        """Sets the deployment_version_id of this StopDeploymentResponse.


        :param deployment_version_id: The deployment_version_id of this StopDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._deployment_version_id = deployment_version_id

    @property
    def service_id(self):
        """Gets the service_id of this StopDeploymentResponse.  # noqa: E501


        :return: The service_id of this StopDeploymentResponse.  # noqa: E501
        :rtype: str
        """
        return self._service_id

    @service_id.setter
    def service_id(self, service_id):
        """Sets the service_id of this StopDeploymentResponse.


        :param service_id: The service_id of this StopDeploymentResponse.  # noqa: E501
        :type: str
        """

        self._service_id = service_id

    @property
    def status(self):
        """Gets the status of this StopDeploymentResponse.  # noqa: E501


        :return: The status of this StopDeploymentResponse.  # noqa: E501
        :rtype: StatusForStopDeploymentOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this StopDeploymentResponse.


        :param status: The status of this StopDeploymentResponse.  # noqa: E501
        :type: StatusForStopDeploymentOutput
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StopDeploymentResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StopDeploymentResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StopDeploymentResponse):
            return True

        return self.to_dict() != other.to_dict()
