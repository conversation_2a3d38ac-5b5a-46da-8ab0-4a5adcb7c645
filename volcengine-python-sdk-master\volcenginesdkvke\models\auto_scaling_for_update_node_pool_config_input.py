# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AutoScalingForUpdateNodePoolConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'desired_replicas': 'int',
        'enabled': 'bool',
        'max_replicas': 'int',
        'min_replicas': 'int',
        'priority': 'int',
        'subnet_policy': 'str'
    }

    attribute_map = {
        'desired_replicas': 'DesiredReplicas',
        'enabled': 'Enabled',
        'max_replicas': 'MaxReplicas',
        'min_replicas': 'MinReplicas',
        'priority': 'Priority',
        'subnet_policy': 'SubnetPolicy'
    }

    def __init__(self, desired_replicas=None, enabled=None, max_replicas=None, min_replicas=None, priority=None, subnet_policy=None, _configuration=None):  # noqa: E501
        """AutoScalingForUpdateNodePoolConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._desired_replicas = None
        self._enabled = None
        self._max_replicas = None
        self._min_replicas = None
        self._priority = None
        self._subnet_policy = None
        self.discriminator = None

        if desired_replicas is not None:
            self.desired_replicas = desired_replicas
        if enabled is not None:
            self.enabled = enabled
        if max_replicas is not None:
            self.max_replicas = max_replicas
        if min_replicas is not None:
            self.min_replicas = min_replicas
        if priority is not None:
            self.priority = priority
        if subnet_policy is not None:
            self.subnet_policy = subnet_policy

    @property
    def desired_replicas(self):
        """Gets the desired_replicas of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The desired_replicas of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._desired_replicas

    @desired_replicas.setter
    def desired_replicas(self, desired_replicas):
        """Sets the desired_replicas of this AutoScalingForUpdateNodePoolConfigInput.


        :param desired_replicas: The desired_replicas of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :type: int
        """

        self._desired_replicas = desired_replicas

    @property
    def enabled(self):
        """Gets the enabled of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The enabled of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this AutoScalingForUpdateNodePoolConfigInput.


        :param enabled: The enabled of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def max_replicas(self):
        """Gets the max_replicas of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The max_replicas of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._max_replicas

    @max_replicas.setter
    def max_replicas(self, max_replicas):
        """Sets the max_replicas of this AutoScalingForUpdateNodePoolConfigInput.


        :param max_replicas: The max_replicas of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :type: int
        """

        self._max_replicas = max_replicas

    @property
    def min_replicas(self):
        """Gets the min_replicas of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The min_replicas of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._min_replicas

    @min_replicas.setter
    def min_replicas(self, min_replicas):
        """Sets the min_replicas of this AutoScalingForUpdateNodePoolConfigInput.


        :param min_replicas: The min_replicas of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :type: int
        """

        self._min_replicas = min_replicas

    @property
    def priority(self):
        """Gets the priority of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The priority of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._priority

    @priority.setter
    def priority(self, priority):
        """Sets the priority of this AutoScalingForUpdateNodePoolConfigInput.


        :param priority: The priority of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :type: int
        """

        self._priority = priority

    @property
    def subnet_policy(self):
        """Gets the subnet_policy of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501


        :return: The subnet_policy of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_policy

    @subnet_policy.setter
    def subnet_policy(self, subnet_policy):
        """Sets the subnet_policy of this AutoScalingForUpdateNodePoolConfigInput.


        :param subnet_policy: The subnet_policy of this AutoScalingForUpdateNodePoolConfigInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["ZoneBalance", "Priority"]  # noqa: E501
        if (self._configuration.client_side_validation and
                subnet_policy not in allowed_values):
            raise ValueError(
                "Invalid value for `subnet_policy` ({0}), must be one of {1}"  # noqa: E501
                .format(subnet_policy, allowed_values)
            )

        self._subnet_policy = subnet_policy

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AutoScalingForUpdateNodePoolConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AutoScalingForUpdateNodePoolConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AutoScalingForUpdateNodePoolConfigInput):
            return True

        return self.to_dict() != other.to_dict()
