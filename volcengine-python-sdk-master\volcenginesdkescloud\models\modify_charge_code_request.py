# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyChargeCodeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_renew': 'bool',
        'include_months': 'int',
        'instance_id': 'str',
        'to_charge_type': 'str'
    }

    attribute_map = {
        'auto_renew': 'AutoRenew',
        'include_months': 'IncludeMonths',
        'instance_id': 'InstanceId',
        'to_charge_type': 'ToChargeType'
    }

    def __init__(self, auto_renew=None, include_months=None, instance_id=None, to_charge_type=None, _configuration=None):  # noqa: E501
        """ModifyChargeCodeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_renew = None
        self._include_months = None
        self._instance_id = None
        self._to_charge_type = None
        self.discriminator = None

        self.auto_renew = auto_renew
        self.include_months = include_months
        self.instance_id = instance_id
        self.to_charge_type = to_charge_type

    @property
    def auto_renew(self):
        """Gets the auto_renew of this ModifyChargeCodeRequest.  # noqa: E501


        :return: The auto_renew of this ModifyChargeCodeRequest.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this ModifyChargeCodeRequest.


        :param auto_renew: The auto_renew of this ModifyChargeCodeRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and auto_renew is None:
            raise ValueError("Invalid value for `auto_renew`, must not be `None`")  # noqa: E501

        self._auto_renew = auto_renew

    @property
    def include_months(self):
        """Gets the include_months of this ModifyChargeCodeRequest.  # noqa: E501


        :return: The include_months of this ModifyChargeCodeRequest.  # noqa: E501
        :rtype: int
        """
        return self._include_months

    @include_months.setter
    def include_months(self, include_months):
        """Sets the include_months of this ModifyChargeCodeRequest.


        :param include_months: The include_months of this ModifyChargeCodeRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and include_months is None:
            raise ValueError("Invalid value for `include_months`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                include_months is not None and include_months < 1):  # noqa: E501
            raise ValueError("Invalid value for `include_months`, must be a value greater than or equal to `1`")  # noqa: E501

        self._include_months = include_months

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyChargeCodeRequest.  # noqa: E501


        :return: The instance_id of this ModifyChargeCodeRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyChargeCodeRequest.


        :param instance_id: The instance_id of this ModifyChargeCodeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def to_charge_type(self):
        """Gets the to_charge_type of this ModifyChargeCodeRequest.  # noqa: E501


        :return: The to_charge_type of this ModifyChargeCodeRequest.  # noqa: E501
        :rtype: str
        """
        return self._to_charge_type

    @to_charge_type.setter
    def to_charge_type(self, to_charge_type):
        """Sets the to_charge_type of this ModifyChargeCodeRequest.


        :param to_charge_type: The to_charge_type of this ModifyChargeCodeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and to_charge_type is None:
            raise ValueError("Invalid value for `to_charge_type`, must not be `None`")  # noqa: E501

        self._to_charge_type = to_charge_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyChargeCodeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyChargeCodeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyChargeCodeRequest):
            return True

        return self.to_dict() != other.to_dict()
