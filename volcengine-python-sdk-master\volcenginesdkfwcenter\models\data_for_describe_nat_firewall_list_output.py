# coding: utf-8

"""
    fwcenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForDescribeNatFirewallListOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'bandwidth': 'int',
        'can_create': 'bool',
        'err_message': 'str',
        'firewall_status': 'str',
        'nat_firewall_id': 'str',
        'nat_firewall_name': 'str',
        'nat_gateway_id': 'str',
        'nat_gateway_name': 'str',
        'peak_traffic_within7_day': 'int',
        'region': 'str',
        'vpc_cidr': 'str',
        'vpc_id': 'str',
        'vpc_name': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'account_id': 'AccountId',
        'bandwidth': 'Bandwidth',
        'can_create': 'CanCreate',
        'err_message': 'ErrMessage',
        'firewall_status': 'FirewallStatus',
        'nat_firewall_id': 'NatFirewallId',
        'nat_firewall_name': 'NatFirewallName',
        'nat_gateway_id': 'NatGatewayId',
        'nat_gateway_name': 'NatGatewayName',
        'peak_traffic_within7_day': 'PeakTrafficWithin7Day',
        'region': 'Region',
        'vpc_cidr': 'VpcCidr',
        'vpc_id': 'VpcId',
        'vpc_name': 'VpcName',
        'zone_id': 'ZoneId'
    }

    def __init__(self, account_id=None, bandwidth=None, can_create=None, err_message=None, firewall_status=None, nat_firewall_id=None, nat_firewall_name=None, nat_gateway_id=None, nat_gateway_name=None, peak_traffic_within7_day=None, region=None, vpc_cidr=None, vpc_id=None, vpc_name=None, zone_id=None, _configuration=None):  # noqa: E501
        """DataForDescribeNatFirewallListOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._bandwidth = None
        self._can_create = None
        self._err_message = None
        self._firewall_status = None
        self._nat_firewall_id = None
        self._nat_firewall_name = None
        self._nat_gateway_id = None
        self._nat_gateway_name = None
        self._peak_traffic_within7_day = None
        self._region = None
        self._vpc_cidr = None
        self._vpc_id = None
        self._vpc_name = None
        self._zone_id = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if bandwidth is not None:
            self.bandwidth = bandwidth
        if can_create is not None:
            self.can_create = can_create
        if err_message is not None:
            self.err_message = err_message
        if firewall_status is not None:
            self.firewall_status = firewall_status
        if nat_firewall_id is not None:
            self.nat_firewall_id = nat_firewall_id
        if nat_firewall_name is not None:
            self.nat_firewall_name = nat_firewall_name
        if nat_gateway_id is not None:
            self.nat_gateway_id = nat_gateway_id
        if nat_gateway_name is not None:
            self.nat_gateway_name = nat_gateway_name
        if peak_traffic_within7_day is not None:
            self.peak_traffic_within7_day = peak_traffic_within7_day
        if region is not None:
            self.region = region
        if vpc_cidr is not None:
            self.vpc_cidr = vpc_cidr
        if vpc_id is not None:
            self.vpc_id = vpc_id
        if vpc_name is not None:
            self.vpc_name = vpc_name
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def account_id(self):
        """Gets the account_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The account_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForDescribeNatFirewallListOutput.


        :param account_id: The account_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def bandwidth(self):
        """Gets the bandwidth of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The bandwidth of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this DataForDescribeNatFirewallListOutput.


        :param bandwidth: The bandwidth of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def can_create(self):
        """Gets the can_create of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The can_create of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: bool
        """
        return self._can_create

    @can_create.setter
    def can_create(self, can_create):
        """Sets the can_create of this DataForDescribeNatFirewallListOutput.


        :param can_create: The can_create of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: bool
        """

        self._can_create = can_create

    @property
    def err_message(self):
        """Gets the err_message of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The err_message of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._err_message

    @err_message.setter
    def err_message(self, err_message):
        """Sets the err_message of this DataForDescribeNatFirewallListOutput.


        :param err_message: The err_message of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._err_message = err_message

    @property
    def firewall_status(self):
        """Gets the firewall_status of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The firewall_status of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._firewall_status

    @firewall_status.setter
    def firewall_status(self, firewall_status):
        """Sets the firewall_status of this DataForDescribeNatFirewallListOutput.


        :param firewall_status: The firewall_status of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._firewall_status = firewall_status

    @property
    def nat_firewall_id(self):
        """Gets the nat_firewall_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The nat_firewall_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._nat_firewall_id

    @nat_firewall_id.setter
    def nat_firewall_id(self, nat_firewall_id):
        """Sets the nat_firewall_id of this DataForDescribeNatFirewallListOutput.


        :param nat_firewall_id: The nat_firewall_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._nat_firewall_id = nat_firewall_id

    @property
    def nat_firewall_name(self):
        """Gets the nat_firewall_name of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The nat_firewall_name of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._nat_firewall_name

    @nat_firewall_name.setter
    def nat_firewall_name(self, nat_firewall_name):
        """Sets the nat_firewall_name of this DataForDescribeNatFirewallListOutput.


        :param nat_firewall_name: The nat_firewall_name of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._nat_firewall_name = nat_firewall_name

    @property
    def nat_gateway_id(self):
        """Gets the nat_gateway_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The nat_gateway_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._nat_gateway_id

    @nat_gateway_id.setter
    def nat_gateway_id(self, nat_gateway_id):
        """Sets the nat_gateway_id of this DataForDescribeNatFirewallListOutput.


        :param nat_gateway_id: The nat_gateway_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._nat_gateway_id = nat_gateway_id

    @property
    def nat_gateway_name(self):
        """Gets the nat_gateway_name of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The nat_gateway_name of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._nat_gateway_name

    @nat_gateway_name.setter
    def nat_gateway_name(self, nat_gateway_name):
        """Sets the nat_gateway_name of this DataForDescribeNatFirewallListOutput.


        :param nat_gateway_name: The nat_gateway_name of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._nat_gateway_name = nat_gateway_name

    @property
    def peak_traffic_within7_day(self):
        """Gets the peak_traffic_within7_day of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The peak_traffic_within7_day of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: int
        """
        return self._peak_traffic_within7_day

    @peak_traffic_within7_day.setter
    def peak_traffic_within7_day(self, peak_traffic_within7_day):
        """Sets the peak_traffic_within7_day of this DataForDescribeNatFirewallListOutput.


        :param peak_traffic_within7_day: The peak_traffic_within7_day of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: int
        """

        self._peak_traffic_within7_day = peak_traffic_within7_day

    @property
    def region(self):
        """Gets the region of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The region of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForDescribeNatFirewallListOutput.


        :param region: The region of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def vpc_cidr(self):
        """Gets the vpc_cidr of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The vpc_cidr of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_cidr

    @vpc_cidr.setter
    def vpc_cidr(self, vpc_cidr):
        """Sets the vpc_cidr of this DataForDescribeNatFirewallListOutput.


        :param vpc_cidr: The vpc_cidr of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._vpc_cidr = vpc_cidr

    @property
    def vpc_id(self):
        """Gets the vpc_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The vpc_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this DataForDescribeNatFirewallListOutput.


        :param vpc_id: The vpc_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    @property
    def vpc_name(self):
        """Gets the vpc_name of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The vpc_name of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_name

    @vpc_name.setter
    def vpc_name(self, vpc_name):
        """Sets the vpc_name of this DataForDescribeNatFirewallListOutput.


        :param vpc_name: The vpc_name of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._vpc_name = vpc_name

    @property
    def zone_id(self):
        """Gets the zone_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501


        :return: The zone_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this DataForDescribeNatFirewallListOutput.


        :param zone_id: The zone_id of this DataForDescribeNatFirewallListOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForDescribeNatFirewallListOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForDescribeNatFirewallListOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForDescribeNatFirewallListOutput):
            return True

        return self.to_dict() != other.to_dict()
