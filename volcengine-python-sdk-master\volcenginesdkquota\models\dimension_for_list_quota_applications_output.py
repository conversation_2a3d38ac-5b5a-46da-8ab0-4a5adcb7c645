# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DimensionForListQuotaApplicationsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'name_cn': 'str',
        'value': 'str',
        'value_cn': 'str'
    }

    attribute_map = {
        'name': 'Name',
        'name_cn': 'NameCn',
        'value': 'Value',
        'value_cn': 'ValueCn'
    }

    def __init__(self, name=None, name_cn=None, value=None, value_cn=None, _configuration=None):  # noqa: E501
        """DimensionForListQuotaApplicationsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._name_cn = None
        self._value = None
        self._value_cn = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if name_cn is not None:
            self.name_cn = name_cn
        if value is not None:
            self.value = value
        if value_cn is not None:
            self.value_cn = value_cn

    @property
    def name(self):
        """Gets the name of this DimensionForListQuotaApplicationsOutput.  # noqa: E501


        :return: The name of this DimensionForListQuotaApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DimensionForListQuotaApplicationsOutput.


        :param name: The name of this DimensionForListQuotaApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def name_cn(self):
        """Gets the name_cn of this DimensionForListQuotaApplicationsOutput.  # noqa: E501


        :return: The name_cn of this DimensionForListQuotaApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name_cn

    @name_cn.setter
    def name_cn(self, name_cn):
        """Sets the name_cn of this DimensionForListQuotaApplicationsOutput.


        :param name_cn: The name_cn of this DimensionForListQuotaApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._name_cn = name_cn

    @property
    def value(self):
        """Gets the value of this DimensionForListQuotaApplicationsOutput.  # noqa: E501


        :return: The value of this DimensionForListQuotaApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._value

    @value.setter
    def value(self, value):
        """Sets the value of this DimensionForListQuotaApplicationsOutput.


        :param value: The value of this DimensionForListQuotaApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._value = value

    @property
    def value_cn(self):
        """Gets the value_cn of this DimensionForListQuotaApplicationsOutput.  # noqa: E501


        :return: The value_cn of this DimensionForListQuotaApplicationsOutput.  # noqa: E501
        :rtype: str
        """
        return self._value_cn

    @value_cn.setter
    def value_cn(self, value_cn):
        """Sets the value_cn of this DimensionForListQuotaApplicationsOutput.


        :param value_cn: The value_cn of this DimensionForListQuotaApplicationsOutput.  # noqa: E501
        :type: str
        """

        self._value_cn = value_cn

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DimensionForListQuotaApplicationsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DimensionForListQuotaApplicationsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DimensionForListQuotaApplicationsOutput):
            return True

        return self.to_dict() != other.to_dict()
