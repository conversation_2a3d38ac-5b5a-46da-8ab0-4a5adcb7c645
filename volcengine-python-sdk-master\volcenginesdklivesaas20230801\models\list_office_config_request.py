# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListOfficeConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'area_id': 'str',
        'office_id': 'str',
        'office_name': 'str',
        'office_status': 'str',
        'page_number': 'int',
        'page_size': 'int'
    }

    attribute_map = {
        'area_id': 'AreaId',
        'office_id': 'OfficeId',
        'office_name': 'OfficeName',
        'office_status': 'OfficeStatus',
        'page_number': 'PageNumber',
        'page_size': 'PageSize'
    }

    def __init__(self, area_id=None, office_id=None, office_name=None, office_status=None, page_number=None, page_size=None, _configuration=None):  # noqa: E501
        """ListOfficeConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._area_id = None
        self._office_id = None
        self._office_name = None
        self._office_status = None
        self._page_number = None
        self._page_size = None
        self.discriminator = None

        if area_id is not None:
            self.area_id = area_id
        if office_id is not None:
            self.office_id = office_id
        if office_name is not None:
            self.office_name = office_name
        if office_status is not None:
            self.office_status = office_status
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size

    @property
    def area_id(self):
        """Gets the area_id of this ListOfficeConfigRequest.  # noqa: E501


        :return: The area_id of this ListOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._area_id

    @area_id.setter
    def area_id(self, area_id):
        """Sets the area_id of this ListOfficeConfigRequest.


        :param area_id: The area_id of this ListOfficeConfigRequest.  # noqa: E501
        :type: str
        """

        self._area_id = area_id

    @property
    def office_id(self):
        """Gets the office_id of this ListOfficeConfigRequest.  # noqa: E501


        :return: The office_id of this ListOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._office_id

    @office_id.setter
    def office_id(self, office_id):
        """Sets the office_id of this ListOfficeConfigRequest.


        :param office_id: The office_id of this ListOfficeConfigRequest.  # noqa: E501
        :type: str
        """

        self._office_id = office_id

    @property
    def office_name(self):
        """Gets the office_name of this ListOfficeConfigRequest.  # noqa: E501


        :return: The office_name of this ListOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._office_name

    @office_name.setter
    def office_name(self, office_name):
        """Sets the office_name of this ListOfficeConfigRequest.


        :param office_name: The office_name of this ListOfficeConfigRequest.  # noqa: E501
        :type: str
        """

        self._office_name = office_name

    @property
    def office_status(self):
        """Gets the office_status of this ListOfficeConfigRequest.  # noqa: E501


        :return: The office_status of this ListOfficeConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._office_status

    @office_status.setter
    def office_status(self, office_status):
        """Sets the office_status of this ListOfficeConfigRequest.


        :param office_status: The office_status of this ListOfficeConfigRequest.  # noqa: E501
        :type: str
        """

        self._office_status = office_status

    @property
    def page_number(self):
        """Gets the page_number of this ListOfficeConfigRequest.  # noqa: E501


        :return: The page_number of this ListOfficeConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListOfficeConfigRequest.


        :param page_number: The page_number of this ListOfficeConfigRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListOfficeConfigRequest.  # noqa: E501


        :return: The page_size of this ListOfficeConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListOfficeConfigRequest.


        :param page_size: The page_size of this ListOfficeConfigRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListOfficeConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListOfficeConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListOfficeConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
