# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SubUnitListForListOrganizationalUnitsForParentOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_time': 'str',
        'delete_uk': 'str',
        'deleted_time': 'str',
        'depth': 'int',
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'org_id': 'str',
        'org_type': 'int',
        'owner': 'str',
        'parent_id': 'str',
        'updated_time': 'str'
    }

    attribute_map = {
        'created_time': 'CreatedTime',
        'delete_uk': 'DeleteUk',
        'deleted_time': 'DeletedTime',
        'depth': 'Depth',
        'description': 'Description',
        'id': 'ID',
        'name': 'Name',
        'org_id': 'OrgID',
        'org_type': 'OrgType',
        'owner': 'Owner',
        'parent_id': 'ParentID',
        'updated_time': 'UpdatedTime'
    }

    def __init__(self, created_time=None, delete_uk=None, deleted_time=None, depth=None, description=None, id=None, name=None, org_id=None, org_type=None, owner=None, parent_id=None, updated_time=None, _configuration=None):  # noqa: E501
        """SubUnitListForListOrganizationalUnitsForParentOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_time = None
        self._delete_uk = None
        self._deleted_time = None
        self._depth = None
        self._description = None
        self._id = None
        self._name = None
        self._org_id = None
        self._org_type = None
        self._owner = None
        self._parent_id = None
        self._updated_time = None
        self.discriminator = None

        if created_time is not None:
            self.created_time = created_time
        if delete_uk is not None:
            self.delete_uk = delete_uk
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if depth is not None:
            self.depth = depth
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if org_id is not None:
            self.org_id = org_id
        if org_type is not None:
            self.org_type = org_type
        if owner is not None:
            self.owner = owner
        if parent_id is not None:
            self.parent_id = parent_id
        if updated_time is not None:
            self.updated_time = updated_time

    @property
    def created_time(self):
        """Gets the created_time of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The created_time of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param created_time: The created_time of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def delete_uk(self):
        """Gets the delete_uk of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The delete_uk of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._delete_uk

    @delete_uk.setter
    def delete_uk(self, delete_uk):
        """Sets the delete_uk of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param delete_uk: The delete_uk of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._delete_uk = delete_uk

    @property
    def deleted_time(self):
        """Gets the deleted_time of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The deleted_time of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param deleted_time: The deleted_time of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def depth(self):
        """Gets the depth of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The depth of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: int
        """
        return self._depth

    @depth.setter
    def depth(self, depth):
        """Sets the depth of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param depth: The depth of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: int
        """

        self._depth = depth

    @property
    def description(self):
        """Gets the description of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The description of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param description: The description of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The id of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param id: The id of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The name of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param name: The name of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def org_id(self):
        """Gets the org_id of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The org_id of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._org_id

    @org_id.setter
    def org_id(self, org_id):
        """Sets the org_id of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param org_id: The org_id of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._org_id = org_id

    @property
    def org_type(self):
        """Gets the org_type of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The org_type of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: int
        """
        return self._org_type

    @org_type.setter
    def org_type(self, org_type):
        """Sets the org_type of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param org_type: The org_type of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: int
        """

        self._org_type = org_type

    @property
    def owner(self):
        """Gets the owner of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The owner of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner

    @owner.setter
    def owner(self, owner):
        """Sets the owner of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param owner: The owner of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._owner = owner

    @property
    def parent_id(self):
        """Gets the parent_id of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The parent_id of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._parent_id

    @parent_id.setter
    def parent_id(self, parent_id):
        """Sets the parent_id of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param parent_id: The parent_id of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._parent_id = parent_id

    @property
    def updated_time(self):
        """Gets the updated_time of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501


        :return: The updated_time of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_time

    @updated_time.setter
    def updated_time(self, updated_time):
        """Sets the updated_time of this SubUnitListForListOrganizationalUnitsForParentOutput.


        :param updated_time: The updated_time of this SubUnitListForListOrganizationalUnitsForParentOutput.  # noqa: E501
        :type: str
        """

        self._updated_time = updated_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SubUnitListForListOrganizationalUnitsForParentOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SubUnitListForListOrganizationalUnitsForParentOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SubUnitListForListOrganizationalUnitsForParentOutput):
            return True

        return self.to_dict() != other.to_dict()
