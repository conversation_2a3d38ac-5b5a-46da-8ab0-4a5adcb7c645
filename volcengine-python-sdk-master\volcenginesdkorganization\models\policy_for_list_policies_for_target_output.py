# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PolicyForListPoliciesForTargetOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'attached_time': 'str',
        'description': 'str',
        'policy_id': 'str',
        'policy_name': 'str',
        'policy_type': 'str'
    }

    attribute_map = {
        'attached_time': 'AttachedTime',
        'description': 'Description',
        'policy_id': 'PolicyID',
        'policy_name': 'PolicyName',
        'policy_type': 'PolicyType'
    }

    def __init__(self, attached_time=None, description=None, policy_id=None, policy_name=None, policy_type=None, _configuration=None):  # noqa: E501
        """PolicyForListPoliciesForTargetOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._attached_time = None
        self._description = None
        self._policy_id = None
        self._policy_name = None
        self._policy_type = None
        self.discriminator = None

        if attached_time is not None:
            self.attached_time = attached_time
        if description is not None:
            self.description = description
        if policy_id is not None:
            self.policy_id = policy_id
        if policy_name is not None:
            self.policy_name = policy_name
        if policy_type is not None:
            self.policy_type = policy_type

    @property
    def attached_time(self):
        """Gets the attached_time of this PolicyForListPoliciesForTargetOutput.  # noqa: E501


        :return: The attached_time of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :rtype: str
        """
        return self._attached_time

    @attached_time.setter
    def attached_time(self, attached_time):
        """Sets the attached_time of this PolicyForListPoliciesForTargetOutput.


        :param attached_time: The attached_time of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :type: str
        """

        self._attached_time = attached_time

    @property
    def description(self):
        """Gets the description of this PolicyForListPoliciesForTargetOutput.  # noqa: E501


        :return: The description of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this PolicyForListPoliciesForTargetOutput.


        :param description: The description of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def policy_id(self):
        """Gets the policy_id of this PolicyForListPoliciesForTargetOutput.  # noqa: E501


        :return: The policy_id of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :rtype: str
        """
        return self._policy_id

    @policy_id.setter
    def policy_id(self, policy_id):
        """Sets the policy_id of this PolicyForListPoliciesForTargetOutput.


        :param policy_id: The policy_id of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :type: str
        """

        self._policy_id = policy_id

    @property
    def policy_name(self):
        """Gets the policy_name of this PolicyForListPoliciesForTargetOutput.  # noqa: E501


        :return: The policy_name of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :rtype: str
        """
        return self._policy_name

    @policy_name.setter
    def policy_name(self, policy_name):
        """Sets the policy_name of this PolicyForListPoliciesForTargetOutput.


        :param policy_name: The policy_name of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :type: str
        """

        self._policy_name = policy_name

    @property
    def policy_type(self):
        """Gets the policy_type of this PolicyForListPoliciesForTargetOutput.  # noqa: E501


        :return: The policy_type of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :rtype: str
        """
        return self._policy_type

    @policy_type.setter
    def policy_type(self, policy_type):
        """Sets the policy_type of this PolicyForListPoliciesForTargetOutput.


        :param policy_type: The policy_type of this PolicyForListPoliciesForTargetOutput.  # noqa: E501
        :type: str
        """

        self._policy_type = policy_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PolicyForListPoliciesForTargetOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PolicyForListPoliciesForTargetOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PolicyForListPoliciesForTargetOutput):
            return True

        return self.to_dict() != other.to_dict()
