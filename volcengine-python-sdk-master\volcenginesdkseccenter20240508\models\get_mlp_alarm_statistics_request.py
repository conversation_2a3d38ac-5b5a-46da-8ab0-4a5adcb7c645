# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetMlpAlarmStatisticsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'cluster_id': 'str',
        'end_time': 'int',
        'mlp_instance_id': 'str',
        'start_time': 'int'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'cluster_id': 'ClusterID',
        'end_time': 'EndTime',
        'mlp_instance_id': 'MlpInstanceID',
        'start_time': 'StartTime'
    }

    def __init__(self, agent_id=None, cluster_id=None, end_time=None, mlp_instance_id=None, start_time=None, _configuration=None):  # noqa: E501
        """GetMlpAlarmStatisticsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._cluster_id = None
        self._end_time = None
        self._mlp_instance_id = None
        self._start_time = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if end_time is not None:
            self.end_time = end_time
        if mlp_instance_id is not None:
            self.mlp_instance_id = mlp_instance_id
        if start_time is not None:
            self.start_time = start_time

    @property
    def agent_id(self):
        """Gets the agent_id of this GetMlpAlarmStatisticsRequest.  # noqa: E501


        :return: The agent_id of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this GetMlpAlarmStatisticsRequest.


        :param agent_id: The agent_id of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def cluster_id(self):
        """Gets the cluster_id of this GetMlpAlarmStatisticsRequest.  # noqa: E501


        :return: The cluster_id of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this GetMlpAlarmStatisticsRequest.


        :param cluster_id: The cluster_id of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def end_time(self):
        """Gets the end_time of this GetMlpAlarmStatisticsRequest.  # noqa: E501


        :return: The end_time of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this GetMlpAlarmStatisticsRequest.


        :param end_time: The end_time of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._end_time = end_time

    @property
    def mlp_instance_id(self):
        """Gets the mlp_instance_id of this GetMlpAlarmStatisticsRequest.  # noqa: E501


        :return: The mlp_instance_id of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :rtype: str
        """
        return self._mlp_instance_id

    @mlp_instance_id.setter
    def mlp_instance_id(self, mlp_instance_id):
        """Sets the mlp_instance_id of this GetMlpAlarmStatisticsRequest.


        :param mlp_instance_id: The mlp_instance_id of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :type: str
        """

        self._mlp_instance_id = mlp_instance_id

    @property
    def start_time(self):
        """Gets the start_time of this GetMlpAlarmStatisticsRequest.  # noqa: E501


        :return: The start_time of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :rtype: int
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this GetMlpAlarmStatisticsRequest.


        :param start_time: The start_time of this GetMlpAlarmStatisticsRequest.  # noqa: E501
        :type: int
        """

        self._start_time = start_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetMlpAlarmStatisticsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetMlpAlarmStatisticsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetMlpAlarmStatisticsRequest):
            return True

        return self.to_dict() != other.to_dict()
