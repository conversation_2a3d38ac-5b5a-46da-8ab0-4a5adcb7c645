# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRaspProcessesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'cloud_providers': 'list[str]',
        'cmdline': 'str',
        'exe_name': 'str',
        'exist_listen_port': 'bool',
        'hostname': 'str',
        'ip': 'str',
        'leaf_group_ids': 'list[str]',
        'pid': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'rasp_config_name': 'str',
        'relate_white_id': 'str',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'list[str]',
        'tags': 'list[str]',
        'top_group_id': 'str'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'cloud_providers': 'CloudProviders',
        'cmdline': 'Cmdline',
        'exe_name': 'ExeName',
        'exist_listen_port': 'ExistListenPort',
        'hostname': 'Hostname',
        'ip': 'IP',
        'leaf_group_ids': 'LeafGroupIDs',
        'pid': 'PID',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'rasp_config_name': 'RaspConfigName',
        'relate_white_id': 'RelateWhiteID',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status',
        'tags': 'Tags',
        'top_group_id': 'TopGroupID'
    }

    def __init__(self, agent_id=None, cloud_providers=None, cmdline=None, exe_name=None, exist_listen_port=None, hostname=None, ip=None, leaf_group_ids=None, pid=None, page_number=None, page_size=None, rasp_config_name=None, relate_white_id=None, sort_by=None, sort_order=None, status=None, tags=None, top_group_id=None, _configuration=None):  # noqa: E501
        """ListRaspProcessesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._cloud_providers = None
        self._cmdline = None
        self._exe_name = None
        self._exist_listen_port = None
        self._hostname = None
        self._ip = None
        self._leaf_group_ids = None
        self._pid = None
        self._page_number = None
        self._page_size = None
        self._rasp_config_name = None
        self._relate_white_id = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self._tags = None
        self._top_group_id = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if cloud_providers is not None:
            self.cloud_providers = cloud_providers
        if cmdline is not None:
            self.cmdline = cmdline
        if exe_name is not None:
            self.exe_name = exe_name
        if exist_listen_port is not None:
            self.exist_listen_port = exist_listen_port
        if hostname is not None:
            self.hostname = hostname
        if ip is not None:
            self.ip = ip
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if pid is not None:
            self.pid = pid
        self.page_number = page_number
        self.page_size = page_size
        if rasp_config_name is not None:
            self.rasp_config_name = rasp_config_name
        if relate_white_id is not None:
            self.relate_white_id = relate_white_id
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if top_group_id is not None:
            self.top_group_id = top_group_id

    @property
    def agent_id(self):
        """Gets the agent_id of this ListRaspProcessesRequest.  # noqa: E501


        :return: The agent_id of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this ListRaspProcessesRequest.


        :param agent_id: The agent_id of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def cloud_providers(self):
        """Gets the cloud_providers of this ListRaspProcessesRequest.  # noqa: E501


        :return: The cloud_providers of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_providers

    @cloud_providers.setter
    def cloud_providers(self, cloud_providers):
        """Sets the cloud_providers of this ListRaspProcessesRequest.


        :param cloud_providers: The cloud_providers of this ListRaspProcessesRequest.  # noqa: E501
        :type: list[str]
        """

        self._cloud_providers = cloud_providers

    @property
    def cmdline(self):
        """Gets the cmdline of this ListRaspProcessesRequest.  # noqa: E501


        :return: The cmdline of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._cmdline

    @cmdline.setter
    def cmdline(self, cmdline):
        """Sets the cmdline of this ListRaspProcessesRequest.


        :param cmdline: The cmdline of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._cmdline = cmdline

    @property
    def exe_name(self):
        """Gets the exe_name of this ListRaspProcessesRequest.  # noqa: E501


        :return: The exe_name of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._exe_name

    @exe_name.setter
    def exe_name(self, exe_name):
        """Sets the exe_name of this ListRaspProcessesRequest.


        :param exe_name: The exe_name of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._exe_name = exe_name

    @property
    def exist_listen_port(self):
        """Gets the exist_listen_port of this ListRaspProcessesRequest.  # noqa: E501


        :return: The exist_listen_port of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: bool
        """
        return self._exist_listen_port

    @exist_listen_port.setter
    def exist_listen_port(self, exist_listen_port):
        """Sets the exist_listen_port of this ListRaspProcessesRequest.


        :param exist_listen_port: The exist_listen_port of this ListRaspProcessesRequest.  # noqa: E501
        :type: bool
        """

        self._exist_listen_port = exist_listen_port

    @property
    def hostname(self):
        """Gets the hostname of this ListRaspProcessesRequest.  # noqa: E501


        :return: The hostname of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this ListRaspProcessesRequest.


        :param hostname: The hostname of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def ip(self):
        """Gets the ip of this ListRaspProcessesRequest.  # noqa: E501


        :return: The ip of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ListRaspProcessesRequest.


        :param ip: The ip of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this ListRaspProcessesRequest.  # noqa: E501


        :return: The leaf_group_ids of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this ListRaspProcessesRequest.


        :param leaf_group_ids: The leaf_group_ids of this ListRaspProcessesRequest.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def pid(self):
        """Gets the pid of this ListRaspProcessesRequest.  # noqa: E501


        :return: The pid of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._pid

    @pid.setter
    def pid(self, pid):
        """Sets the pid of this ListRaspProcessesRequest.


        :param pid: The pid of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._pid = pid

    @property
    def page_number(self):
        """Gets the page_number of this ListRaspProcessesRequest.  # noqa: E501


        :return: The page_number of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListRaspProcessesRequest.


        :param page_number: The page_number of this ListRaspProcessesRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListRaspProcessesRequest.  # noqa: E501


        :return: The page_size of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListRaspProcessesRequest.


        :param page_size: The page_size of this ListRaspProcessesRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def rasp_config_name(self):
        """Gets the rasp_config_name of this ListRaspProcessesRequest.  # noqa: E501


        :return: The rasp_config_name of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._rasp_config_name

    @rasp_config_name.setter
    def rasp_config_name(self, rasp_config_name):
        """Sets the rasp_config_name of this ListRaspProcessesRequest.


        :param rasp_config_name: The rasp_config_name of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._rasp_config_name = rasp_config_name

    @property
    def relate_white_id(self):
        """Gets the relate_white_id of this ListRaspProcessesRequest.  # noqa: E501


        :return: The relate_white_id of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._relate_white_id

    @relate_white_id.setter
    def relate_white_id(self, relate_white_id):
        """Sets the relate_white_id of this ListRaspProcessesRequest.


        :param relate_white_id: The relate_white_id of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._relate_white_id = relate_white_id

    @property
    def sort_by(self):
        """Gets the sort_by of this ListRaspProcessesRequest.  # noqa: E501


        :return: The sort_by of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListRaspProcessesRequest.


        :param sort_by: The sort_by of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListRaspProcessesRequest.  # noqa: E501


        :return: The sort_order of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListRaspProcessesRequest.


        :param sort_order: The sort_order of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListRaspProcessesRequest.  # noqa: E501


        :return: The status of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListRaspProcessesRequest.


        :param status: The status of this ListRaspProcessesRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this ListRaspProcessesRequest.  # noqa: E501


        :return: The tags of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ListRaspProcessesRequest.


        :param tags: The tags of this ListRaspProcessesRequest.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def top_group_id(self):
        """Gets the top_group_id of this ListRaspProcessesRequest.  # noqa: E501


        :return: The top_group_id of this ListRaspProcessesRequest.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this ListRaspProcessesRequest.


        :param top_group_id: The top_group_id of this ListRaspProcessesRequest.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRaspProcessesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRaspProcessesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRaspProcessesRequest):
            return True

        return self.to_dict() != other.to_dict()
