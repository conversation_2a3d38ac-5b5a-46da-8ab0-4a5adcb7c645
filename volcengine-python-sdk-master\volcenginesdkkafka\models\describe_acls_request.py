# coding: utf-8

"""
    kafka

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAclsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'access_policy': 'str',
        'instance_id': 'str',
        'ip': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'pattern_type': 'str',
        'resource': 'str',
        'resource_type': 'str',
        'user_name': 'str'
    }

    attribute_map = {
        'access_policy': 'AccessPolicy',
        'instance_id': 'InstanceId',
        'ip': 'Ip',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'pattern_type': 'PatternType',
        'resource': 'Resource',
        'resource_type': 'ResourceType',
        'user_name': 'UserName'
    }

    def __init__(self, access_policy=None, instance_id=None, ip=None, page_number=None, page_size=None, pattern_type=None, resource=None, resource_type=None, user_name=None, _configuration=None):  # noqa: E501
        """DescribeAclsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._access_policy = None
        self._instance_id = None
        self._ip = None
        self._page_number = None
        self._page_size = None
        self._pattern_type = None
        self._resource = None
        self._resource_type = None
        self._user_name = None
        self.discriminator = None

        if access_policy is not None:
            self.access_policy = access_policy
        self.instance_id = instance_id
        if ip is not None:
            self.ip = ip
        self.page_number = page_number
        self.page_size = page_size
        if pattern_type is not None:
            self.pattern_type = pattern_type
        if resource is not None:
            self.resource = resource
        if resource_type is not None:
            self.resource_type = resource_type
        if user_name is not None:
            self.user_name = user_name

    @property
    def access_policy(self):
        """Gets the access_policy of this DescribeAclsRequest.  # noqa: E501


        :return: The access_policy of this DescribeAclsRequest.  # noqa: E501
        :rtype: str
        """
        return self._access_policy

    @access_policy.setter
    def access_policy(self, access_policy):
        """Sets the access_policy of this DescribeAclsRequest.


        :param access_policy: The access_policy of this DescribeAclsRequest.  # noqa: E501
        :type: str
        """

        self._access_policy = access_policy

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeAclsRequest.  # noqa: E501


        :return: The instance_id of this DescribeAclsRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeAclsRequest.


        :param instance_id: The instance_id of this DescribeAclsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def ip(self):
        """Gets the ip of this DescribeAclsRequest.  # noqa: E501


        :return: The ip of this DescribeAclsRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this DescribeAclsRequest.


        :param ip: The ip of this DescribeAclsRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def page_number(self):
        """Gets the page_number of this DescribeAclsRequest.  # noqa: E501


        :return: The page_number of this DescribeAclsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeAclsRequest.


        :param page_number: The page_number of this DescribeAclsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeAclsRequest.  # noqa: E501


        :return: The page_size of this DescribeAclsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeAclsRequest.


        :param page_size: The page_size of this DescribeAclsRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def pattern_type(self):
        """Gets the pattern_type of this DescribeAclsRequest.  # noqa: E501


        :return: The pattern_type of this DescribeAclsRequest.  # noqa: E501
        :rtype: str
        """
        return self._pattern_type

    @pattern_type.setter
    def pattern_type(self, pattern_type):
        """Sets the pattern_type of this DescribeAclsRequest.


        :param pattern_type: The pattern_type of this DescribeAclsRequest.  # noqa: E501
        :type: str
        """

        self._pattern_type = pattern_type

    @property
    def resource(self):
        """Gets the resource of this DescribeAclsRequest.  # noqa: E501


        :return: The resource of this DescribeAclsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource

    @resource.setter
    def resource(self, resource):
        """Sets the resource of this DescribeAclsRequest.


        :param resource: The resource of this DescribeAclsRequest.  # noqa: E501
        :type: str
        """

        self._resource = resource

    @property
    def resource_type(self):
        """Gets the resource_type of this DescribeAclsRequest.  # noqa: E501


        :return: The resource_type of this DescribeAclsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this DescribeAclsRequest.


        :param resource_type: The resource_type of this DescribeAclsRequest.  # noqa: E501
        :type: str
        """

        self._resource_type = resource_type

    @property
    def user_name(self):
        """Gets the user_name of this DescribeAclsRequest.  # noqa: E501


        :return: The user_name of this DescribeAclsRequest.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this DescribeAclsRequest.


        :param user_name: The user_name of this DescribeAclsRequest.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAclsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAclsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAclsRequest):
            return True

        return self.to_dict() != other.to_dict()
