# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RangeForListRaspConfigsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id_list': 'list[str]',
        'all_host': 'bool',
        'leaf_group_ids': 'list[str]',
        'single_group_path': 'str',
        'single_hostname': 'str'
    }

    attribute_map = {
        'agent_id_list': 'AgentIDList',
        'all_host': 'AllHost',
        'leaf_group_ids': 'LeafGroupIDs',
        'single_group_path': 'SingleGroupPath',
        'single_hostname': 'SingleHostname'
    }

    def __init__(self, agent_id_list=None, all_host=None, leaf_group_ids=None, single_group_path=None, single_hostname=None, _configuration=None):  # noqa: E501
        """RangeForListRaspConfigsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id_list = None
        self._all_host = None
        self._leaf_group_ids = None
        self._single_group_path = None
        self._single_hostname = None
        self.discriminator = None

        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if all_host is not None:
            self.all_host = all_host
        if leaf_group_ids is not None:
            self.leaf_group_ids = leaf_group_ids
        if single_group_path is not None:
            self.single_group_path = single_group_path
        if single_hostname is not None:
            self.single_hostname = single_hostname

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this RangeForListRaspConfigsOutput.  # noqa: E501


        :return: The agent_id_list of this RangeForListRaspConfigsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this RangeForListRaspConfigsOutput.


        :param agent_id_list: The agent_id_list of this RangeForListRaspConfigsOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def all_host(self):
        """Gets the all_host of this RangeForListRaspConfigsOutput.  # noqa: E501


        :return: The all_host of this RangeForListRaspConfigsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._all_host

    @all_host.setter
    def all_host(self, all_host):
        """Sets the all_host of this RangeForListRaspConfigsOutput.


        :param all_host: The all_host of this RangeForListRaspConfigsOutput.  # noqa: E501
        :type: bool
        """

        self._all_host = all_host

    @property
    def leaf_group_ids(self):
        """Gets the leaf_group_ids of this RangeForListRaspConfigsOutput.  # noqa: E501


        :return: The leaf_group_ids of this RangeForListRaspConfigsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._leaf_group_ids

    @leaf_group_ids.setter
    def leaf_group_ids(self, leaf_group_ids):
        """Sets the leaf_group_ids of this RangeForListRaspConfigsOutput.


        :param leaf_group_ids: The leaf_group_ids of this RangeForListRaspConfigsOutput.  # noqa: E501
        :type: list[str]
        """

        self._leaf_group_ids = leaf_group_ids

    @property
    def single_group_path(self):
        """Gets the single_group_path of this RangeForListRaspConfigsOutput.  # noqa: E501


        :return: The single_group_path of this RangeForListRaspConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._single_group_path

    @single_group_path.setter
    def single_group_path(self, single_group_path):
        """Sets the single_group_path of this RangeForListRaspConfigsOutput.


        :param single_group_path: The single_group_path of this RangeForListRaspConfigsOutput.  # noqa: E501
        :type: str
        """

        self._single_group_path = single_group_path

    @property
    def single_hostname(self):
        """Gets the single_hostname of this RangeForListRaspConfigsOutput.  # noqa: E501


        :return: The single_hostname of this RangeForListRaspConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._single_hostname

    @single_hostname.setter
    def single_hostname(self, single_hostname):
        """Sets the single_hostname of this RangeForListRaspConfigsOutput.


        :param single_hostname: The single_hostname of this RangeForListRaspConfigsOutput.  # noqa: E501
        :type: str
        """

        self._single_hostname = single_hostname

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RangeForListRaspConfigsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RangeForListRaspConfigsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RangeForListRaspConfigsOutput):
            return True

        return self.to_dict() != other.to_dict()
