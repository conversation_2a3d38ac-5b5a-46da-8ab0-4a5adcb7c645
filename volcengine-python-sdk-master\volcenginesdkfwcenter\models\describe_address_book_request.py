# coding: utf-8

"""
    fwcenter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAddressBookRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'group_type': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'query': 'str'
    }

    attribute_map = {
        'group_type': 'GroupType',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'query': 'Query'
    }

    def __init__(self, group_type=None, page_number=None, page_size=None, query=None, _configuration=None):  # noqa: E501
        """DescribeAddressBookRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._group_type = None
        self._page_number = None
        self._page_size = None
        self._query = None
        self.discriminator = None

        if group_type is not None:
            self.group_type = group_type
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if query is not None:
            self.query = query

    @property
    def group_type(self):
        """Gets the group_type of this DescribeAddressBookRequest.  # noqa: E501


        :return: The group_type of this DescribeAddressBookRequest.  # noqa: E501
        :rtype: str
        """
        return self._group_type

    @group_type.setter
    def group_type(self, group_type):
        """Sets the group_type of this DescribeAddressBookRequest.


        :param group_type: The group_type of this DescribeAddressBookRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["ip", "port", "domain"]  # noqa: E501
        if (self._configuration.client_side_validation and
                group_type not in allowed_values):
            raise ValueError(
                "Invalid value for `group_type` ({0}), must be one of {1}"  # noqa: E501
                .format(group_type, allowed_values)
            )

        self._group_type = group_type

    @property
    def page_number(self):
        """Gets the page_number of this DescribeAddressBookRequest.  # noqa: E501


        :return: The page_number of this DescribeAddressBookRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeAddressBookRequest.


        :param page_number: The page_number of this DescribeAddressBookRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_number is not None and page_number > 100):  # noqa: E501
            raise ValueError("Invalid value for `page_number`, must be a value less than or equal to `100`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeAddressBookRequest.  # noqa: E501


        :return: The page_size of this DescribeAddressBookRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeAddressBookRequest.


        :param page_size: The page_size of this DescribeAddressBookRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def query(self):
        """Gets the query of this DescribeAddressBookRequest.  # noqa: E501


        :return: The query of this DescribeAddressBookRequest.  # noqa: E501
        :rtype: str
        """
        return self._query

    @query.setter
    def query(self, query):
        """Sets the query of this DescribeAddressBookRequest.


        :param query: The query of this DescribeAddressBookRequest.  # noqa: E501
        :type: str
        """

        self._query = query

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAddressBookRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAddressBookRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAddressBookRequest):
            return True

        return self.to_dict() != other.to_dict()
