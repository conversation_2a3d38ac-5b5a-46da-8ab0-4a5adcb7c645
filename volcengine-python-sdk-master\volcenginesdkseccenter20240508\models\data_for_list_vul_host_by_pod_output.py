# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListVulHostByPodOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'agent_tags': 'list[str]',
        'control_time': 'int',
        'create_time': 'int',
        'cwpp_id': 'str',
        'eip_address': 'str',
        'host_name': 'str',
        'operate_reason': 'str',
        'primary_ip_address': 'str',
        'status': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'agent_tags': 'AgentTags',
        'control_time': 'ControlTime',
        'create_time': 'CreateTime',
        'cwpp_id': 'CwppID',
        'eip_address': 'EipAddress',
        'host_name': 'HostName',
        'operate_reason': 'OperateReason',
        'primary_ip_address': 'PrimaryIpAddress',
        'status': 'Status',
        'update_time': 'UpdateTime'
    }

    def __init__(self, agent_id=None, agent_tags=None, control_time=None, create_time=None, cwpp_id=None, eip_address=None, host_name=None, operate_reason=None, primary_ip_address=None, status=None, update_time=None, _configuration=None):  # noqa: E501
        """DataForListVulHostByPodOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._agent_tags = None
        self._control_time = None
        self._create_time = None
        self._cwpp_id = None
        self._eip_address = None
        self._host_name = None
        self._operate_reason = None
        self._primary_ip_address = None
        self._status = None
        self._update_time = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if agent_tags is not None:
            self.agent_tags = agent_tags
        if control_time is not None:
            self.control_time = control_time
        if create_time is not None:
            self.create_time = create_time
        if cwpp_id is not None:
            self.cwpp_id = cwpp_id
        if eip_address is not None:
            self.eip_address = eip_address
        if host_name is not None:
            self.host_name = host_name
        if operate_reason is not None:
            self.operate_reason = operate_reason
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if status is not None:
            self.status = status
        if update_time is not None:
            self.update_time = update_time

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The agent_id of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForListVulHostByPodOutput.


        :param agent_id: The agent_id of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def agent_tags(self):
        """Gets the agent_tags of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The agent_tags of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_tags

    @agent_tags.setter
    def agent_tags(self, agent_tags):
        """Sets the agent_tags of this DataForListVulHostByPodOutput.


        :param agent_tags: The agent_tags of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: list[str]
        """

        self._agent_tags = agent_tags

    @property
    def control_time(self):
        """Gets the control_time of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The control_time of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._control_time

    @control_time.setter
    def control_time(self, control_time):
        """Sets the control_time of this DataForListVulHostByPodOutput.


        :param control_time: The control_time of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: int
        """

        self._control_time = control_time

    @property
    def create_time(self):
        """Gets the create_time of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The create_time of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForListVulHostByPodOutput.


        :param create_time: The create_time of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def cwpp_id(self):
        """Gets the cwpp_id of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The cwpp_id of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._cwpp_id

    @cwpp_id.setter
    def cwpp_id(self, cwpp_id):
        """Sets the cwpp_id of this DataForListVulHostByPodOutput.


        :param cwpp_id: The cwpp_id of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: str
        """

        self._cwpp_id = cwpp_id

    @property
    def eip_address(self):
        """Gets the eip_address of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The eip_address of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this DataForListVulHostByPodOutput.


        :param eip_address: The eip_address of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def host_name(self):
        """Gets the host_name of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The host_name of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._host_name

    @host_name.setter
    def host_name(self, host_name):
        """Sets the host_name of this DataForListVulHostByPodOutput.


        :param host_name: The host_name of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: str
        """

        self._host_name = host_name

    @property
    def operate_reason(self):
        """Gets the operate_reason of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The operate_reason of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._operate_reason

    @operate_reason.setter
    def operate_reason(self, operate_reason):
        """Sets the operate_reason of this DataForListVulHostByPodOutput.


        :param operate_reason: The operate_reason of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: str
        """

        self._operate_reason = operate_reason

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The primary_ip_address of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this DataForListVulHostByPodOutput.


        :param primary_ip_address: The primary_ip_address of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def status(self):
        """Gets the status of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The status of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListVulHostByPodOutput.


        :param status: The status of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def update_time(self):
        """Gets the update_time of this DataForListVulHostByPodOutput.  # noqa: E501


        :return: The update_time of this DataForListVulHostByPodOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForListVulHostByPodOutput.


        :param update_time: The update_time of this DataForListVulHostByPodOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListVulHostByPodOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListVulHostByPodOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListVulHostByPodOutput):
            return True

        return self.to_dict() != other.to_dict()
