# coding: utf-8

"""
    apig20221112

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListRoutesInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'name': 'str',
        'path': 'str',
        'upstream_ids': 'list[str]'
    }

    attribute_map = {
        'name': 'Name',
        'path': 'Path',
        'upstream_ids': 'UpstreamIds'
    }

    def __init__(self, name=None, path=None, upstream_ids=None, _configuration=None):  # noqa: E501
        """FilterForListRoutesInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._name = None
        self._path = None
        self._upstream_ids = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if path is not None:
            self.path = path
        if upstream_ids is not None:
            self.upstream_ids = upstream_ids

    @property
    def name(self):
        """Gets the name of this FilterForListRoutesInput.  # noqa: E501


        :return: The name of this FilterForListRoutesInput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this FilterForListRoutesInput.


        :param name: The name of this FilterForListRoutesInput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def path(self):
        """Gets the path of this FilterForListRoutesInput.  # noqa: E501


        :return: The path of this FilterForListRoutesInput.  # noqa: E501
        :rtype: str
        """
        return self._path

    @path.setter
    def path(self, path):
        """Sets the path of this FilterForListRoutesInput.


        :param path: The path of this FilterForListRoutesInput.  # noqa: E501
        :type: str
        """

        self._path = path

    @property
    def upstream_ids(self):
        """Gets the upstream_ids of this FilterForListRoutesInput.  # noqa: E501


        :return: The upstream_ids of this FilterForListRoutesInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._upstream_ids

    @upstream_ids.setter
    def upstream_ids(self, upstream_ids):
        """Sets the upstream_ids of this FilterForListRoutesInput.


        :param upstream_ids: The upstream_ids of this FilterForListRoutesInput.  # noqa: E501
        :type: list[str]
        """

        self._upstream_ids = upstream_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListRoutesInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListRoutesInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListRoutesInput):
            return True

        return self.to_dict() != other.to_dict()
