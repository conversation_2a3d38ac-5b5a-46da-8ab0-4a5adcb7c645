# coding: utf-8

"""
    vmp

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetWorkspaceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'str',
        'delete_protection_enabled': 'bool',
        'description': 'str',
        'id': 'str',
        'instance_type': 'InstanceTypeForGetWorkspaceOutput',
        'instance_type_id': 'str',
        'name': 'str',
        'overdue_reclaim_time': 'str',
        'project_name': 'str',
        'prometheus_push_intranet_endpoint': 'str',
        'prometheus_query_intranet_endpoint': 'str',
        'prometheus_write_intranet_endpoint': 'str',
        'quota': 'QuotaForGetWorkspaceOutput',
        'status': 'str',
        'tags': 'list[TagForGetWorkspaceOutput]',
        'username': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'delete_protection_enabled': 'DeleteProtectionEnabled',
        'description': 'Description',
        'id': 'Id',
        'instance_type': 'InstanceType',
        'instance_type_id': 'InstanceTypeId',
        'name': 'Name',
        'overdue_reclaim_time': 'OverdueReclaimTime',
        'project_name': 'ProjectName',
        'prometheus_push_intranet_endpoint': 'PrometheusPushIntranetEndpoint',
        'prometheus_query_intranet_endpoint': 'PrometheusQueryIntranetEndpoint',
        'prometheus_write_intranet_endpoint': 'PrometheusWriteIntranetEndpoint',
        'quota': 'Quota',
        'status': 'Status',
        'tags': 'Tags',
        'username': 'Username'
    }

    def __init__(self, create_time=None, delete_protection_enabled=None, description=None, id=None, instance_type=None, instance_type_id=None, name=None, overdue_reclaim_time=None, project_name=None, prometheus_push_intranet_endpoint=None, prometheus_query_intranet_endpoint=None, prometheus_write_intranet_endpoint=None, quota=None, status=None, tags=None, username=None, _configuration=None):  # noqa: E501
        """GetWorkspaceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._delete_protection_enabled = None
        self._description = None
        self._id = None
        self._instance_type = None
        self._instance_type_id = None
        self._name = None
        self._overdue_reclaim_time = None
        self._project_name = None
        self._prometheus_push_intranet_endpoint = None
        self._prometheus_query_intranet_endpoint = None
        self._prometheus_write_intranet_endpoint = None
        self._quota = None
        self._status = None
        self._tags = None
        self._username = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if delete_protection_enabled is not None:
            self.delete_protection_enabled = delete_protection_enabled
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if instance_type is not None:
            self.instance_type = instance_type
        if instance_type_id is not None:
            self.instance_type_id = instance_type_id
        if name is not None:
            self.name = name
        if overdue_reclaim_time is not None:
            self.overdue_reclaim_time = overdue_reclaim_time
        if project_name is not None:
            self.project_name = project_name
        if prometheus_push_intranet_endpoint is not None:
            self.prometheus_push_intranet_endpoint = prometheus_push_intranet_endpoint
        if prometheus_query_intranet_endpoint is not None:
            self.prometheus_query_intranet_endpoint = prometheus_query_intranet_endpoint
        if prometheus_write_intranet_endpoint is not None:
            self.prometheus_write_intranet_endpoint = prometheus_write_intranet_endpoint
        if quota is not None:
            self.quota = quota
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if username is not None:
            self.username = username

    @property
    def create_time(self):
        """Gets the create_time of this GetWorkspaceResponse.  # noqa: E501


        :return: The create_time of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this GetWorkspaceResponse.


        :param create_time: The create_time of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def delete_protection_enabled(self):
        """Gets the delete_protection_enabled of this GetWorkspaceResponse.  # noqa: E501


        :return: The delete_protection_enabled of this GetWorkspaceResponse.  # noqa: E501
        :rtype: bool
        """
        return self._delete_protection_enabled

    @delete_protection_enabled.setter
    def delete_protection_enabled(self, delete_protection_enabled):
        """Sets the delete_protection_enabled of this GetWorkspaceResponse.


        :param delete_protection_enabled: The delete_protection_enabled of this GetWorkspaceResponse.  # noqa: E501
        :type: bool
        """

        self._delete_protection_enabled = delete_protection_enabled

    @property
    def description(self):
        """Gets the description of this GetWorkspaceResponse.  # noqa: E501


        :return: The description of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetWorkspaceResponse.


        :param description: The description of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this GetWorkspaceResponse.  # noqa: E501


        :return: The id of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetWorkspaceResponse.


        :param id: The id of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def instance_type(self):
        """Gets the instance_type of this GetWorkspaceResponse.  # noqa: E501


        :return: The instance_type of this GetWorkspaceResponse.  # noqa: E501
        :rtype: InstanceTypeForGetWorkspaceOutput
        """
        return self._instance_type

    @instance_type.setter
    def instance_type(self, instance_type):
        """Sets the instance_type of this GetWorkspaceResponse.


        :param instance_type: The instance_type of this GetWorkspaceResponse.  # noqa: E501
        :type: InstanceTypeForGetWorkspaceOutput
        """

        self._instance_type = instance_type

    @property
    def instance_type_id(self):
        """Gets the instance_type_id of this GetWorkspaceResponse.  # noqa: E501


        :return: The instance_type_id of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_type_id

    @instance_type_id.setter
    def instance_type_id(self, instance_type_id):
        """Sets the instance_type_id of this GetWorkspaceResponse.


        :param instance_type_id: The instance_type_id of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._instance_type_id = instance_type_id

    @property
    def name(self):
        """Gets the name of this GetWorkspaceResponse.  # noqa: E501


        :return: The name of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetWorkspaceResponse.


        :param name: The name of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def overdue_reclaim_time(self):
        """Gets the overdue_reclaim_time of this GetWorkspaceResponse.  # noqa: E501


        :return: The overdue_reclaim_time of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._overdue_reclaim_time

    @overdue_reclaim_time.setter
    def overdue_reclaim_time(self, overdue_reclaim_time):
        """Sets the overdue_reclaim_time of this GetWorkspaceResponse.


        :param overdue_reclaim_time: The overdue_reclaim_time of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._overdue_reclaim_time = overdue_reclaim_time

    @property
    def project_name(self):
        """Gets the project_name of this GetWorkspaceResponse.  # noqa: E501


        :return: The project_name of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this GetWorkspaceResponse.


        :param project_name: The project_name of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def prometheus_push_intranet_endpoint(self):
        """Gets the prometheus_push_intranet_endpoint of this GetWorkspaceResponse.  # noqa: E501


        :return: The prometheus_push_intranet_endpoint of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._prometheus_push_intranet_endpoint

    @prometheus_push_intranet_endpoint.setter
    def prometheus_push_intranet_endpoint(self, prometheus_push_intranet_endpoint):
        """Sets the prometheus_push_intranet_endpoint of this GetWorkspaceResponse.


        :param prometheus_push_intranet_endpoint: The prometheus_push_intranet_endpoint of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._prometheus_push_intranet_endpoint = prometheus_push_intranet_endpoint

    @property
    def prometheus_query_intranet_endpoint(self):
        """Gets the prometheus_query_intranet_endpoint of this GetWorkspaceResponse.  # noqa: E501


        :return: The prometheus_query_intranet_endpoint of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._prometheus_query_intranet_endpoint

    @prometheus_query_intranet_endpoint.setter
    def prometheus_query_intranet_endpoint(self, prometheus_query_intranet_endpoint):
        """Sets the prometheus_query_intranet_endpoint of this GetWorkspaceResponse.


        :param prometheus_query_intranet_endpoint: The prometheus_query_intranet_endpoint of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._prometheus_query_intranet_endpoint = prometheus_query_intranet_endpoint

    @property
    def prometheus_write_intranet_endpoint(self):
        """Gets the prometheus_write_intranet_endpoint of this GetWorkspaceResponse.  # noqa: E501


        :return: The prometheus_write_intranet_endpoint of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._prometheus_write_intranet_endpoint

    @prometheus_write_intranet_endpoint.setter
    def prometheus_write_intranet_endpoint(self, prometheus_write_intranet_endpoint):
        """Sets the prometheus_write_intranet_endpoint of this GetWorkspaceResponse.


        :param prometheus_write_intranet_endpoint: The prometheus_write_intranet_endpoint of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._prometheus_write_intranet_endpoint = prometheus_write_intranet_endpoint

    @property
    def quota(self):
        """Gets the quota of this GetWorkspaceResponse.  # noqa: E501


        :return: The quota of this GetWorkspaceResponse.  # noqa: E501
        :rtype: QuotaForGetWorkspaceOutput
        """
        return self._quota

    @quota.setter
    def quota(self, quota):
        """Sets the quota of this GetWorkspaceResponse.


        :param quota: The quota of this GetWorkspaceResponse.  # noqa: E501
        :type: QuotaForGetWorkspaceOutput
        """

        self._quota = quota

    @property
    def status(self):
        """Gets the status of this GetWorkspaceResponse.  # noqa: E501


        :return: The status of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetWorkspaceResponse.


        :param status: The status of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this GetWorkspaceResponse.  # noqa: E501


        :return: The tags of this GetWorkspaceResponse.  # noqa: E501
        :rtype: list[TagForGetWorkspaceOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this GetWorkspaceResponse.


        :param tags: The tags of this GetWorkspaceResponse.  # noqa: E501
        :type: list[TagForGetWorkspaceOutput]
        """

        self._tags = tags

    @property
    def username(self):
        """Gets the username of this GetWorkspaceResponse.  # noqa: E501


        :return: The username of this GetWorkspaceResponse.  # noqa: E501
        :rtype: str
        """
        return self._username

    @username.setter
    def username(self, username):
        """Sets the username of this GetWorkspaceResponse.


        :param username: The username of this GetWorkspaceResponse.  # noqa: E501
        :type: str
        """

        self._username = username

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetWorkspaceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetWorkspaceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetWorkspaceResponse):
            return True

        return self.to_dict() != other.to_dict()
