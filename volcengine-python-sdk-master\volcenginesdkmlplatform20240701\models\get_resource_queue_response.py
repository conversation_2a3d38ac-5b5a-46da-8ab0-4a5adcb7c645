# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetResourceQueueResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'charge_type': 'str',
        'compute_resources': 'list[ComputeResourceForGetResourceQueueOutput]',
        'description': 'str',
        'id': 'str',
        'name': 'str',
        'quota_allocated': 'QuotaAllocatedForGetResourceQueueOutput',
        'quota_capability': 'QuotaCapabilityForGetResourceQueueOutput',
        'resource_group_id': 'str',
        'rules': 'RulesForGetResourceQueueOutput',
        'shareable': 'bool',
        'shared_quota_allocated': 'SharedQuotaAllocatedForGetResourceQueueOutput',
        'status': 'StatusForGetResourceQueueOutput',
        'system_quota_allocated': 'SystemQuotaAllocatedForGetResourceQueueOutput',
        'volume_resources': 'list[VolumeResourceForGetResourceQueueOutput]',
        'workload_infos': 'list[WorkloadInfoForGetResourceQueueOutput]',
        'zone_ids': 'list[str]'
    }

    attribute_map = {
        'charge_type': 'ChargeType',
        'compute_resources': 'ComputeResources',
        'description': 'Description',
        'id': 'Id',
        'name': 'Name',
        'quota_allocated': 'QuotaAllocated',
        'quota_capability': 'QuotaCapability',
        'resource_group_id': 'ResourceGroupId',
        'rules': 'Rules',
        'shareable': 'Shareable',
        'shared_quota_allocated': 'SharedQuotaAllocated',
        'status': 'Status',
        'system_quota_allocated': 'SystemQuotaAllocated',
        'volume_resources': 'VolumeResources',
        'workload_infos': 'WorkloadInfos',
        'zone_ids': 'ZoneIds'
    }

    def __init__(self, charge_type=None, compute_resources=None, description=None, id=None, name=None, quota_allocated=None, quota_capability=None, resource_group_id=None, rules=None, shareable=None, shared_quota_allocated=None, status=None, system_quota_allocated=None, volume_resources=None, workload_infos=None, zone_ids=None, _configuration=None):  # noqa: E501
        """GetResourceQueueResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._charge_type = None
        self._compute_resources = None
        self._description = None
        self._id = None
        self._name = None
        self._quota_allocated = None
        self._quota_capability = None
        self._resource_group_id = None
        self._rules = None
        self._shareable = None
        self._shared_quota_allocated = None
        self._status = None
        self._system_quota_allocated = None
        self._volume_resources = None
        self._workload_infos = None
        self._zone_ids = None
        self.discriminator = None

        if charge_type is not None:
            self.charge_type = charge_type
        if compute_resources is not None:
            self.compute_resources = compute_resources
        if description is not None:
            self.description = description
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if quota_allocated is not None:
            self.quota_allocated = quota_allocated
        if quota_capability is not None:
            self.quota_capability = quota_capability
        if resource_group_id is not None:
            self.resource_group_id = resource_group_id
        if rules is not None:
            self.rules = rules
        if shareable is not None:
            self.shareable = shareable
        if shared_quota_allocated is not None:
            self.shared_quota_allocated = shared_quota_allocated
        if status is not None:
            self.status = status
        if system_quota_allocated is not None:
            self.system_quota_allocated = system_quota_allocated
        if volume_resources is not None:
            self.volume_resources = volume_resources
        if workload_infos is not None:
            self.workload_infos = workload_infos
        if zone_ids is not None:
            self.zone_ids = zone_ids

    @property
    def charge_type(self):
        """Gets the charge_type of this GetResourceQueueResponse.  # noqa: E501


        :return: The charge_type of this GetResourceQueueResponse.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this GetResourceQueueResponse.


        :param charge_type: The charge_type of this GetResourceQueueResponse.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def compute_resources(self):
        """Gets the compute_resources of this GetResourceQueueResponse.  # noqa: E501


        :return: The compute_resources of this GetResourceQueueResponse.  # noqa: E501
        :rtype: list[ComputeResourceForGetResourceQueueOutput]
        """
        return self._compute_resources

    @compute_resources.setter
    def compute_resources(self, compute_resources):
        """Sets the compute_resources of this GetResourceQueueResponse.


        :param compute_resources: The compute_resources of this GetResourceQueueResponse.  # noqa: E501
        :type: list[ComputeResourceForGetResourceQueueOutput]
        """

        self._compute_resources = compute_resources

    @property
    def description(self):
        """Gets the description of this GetResourceQueueResponse.  # noqa: E501


        :return: The description of this GetResourceQueueResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetResourceQueueResponse.


        :param description: The description of this GetResourceQueueResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def id(self):
        """Gets the id of this GetResourceQueueResponse.  # noqa: E501


        :return: The id of this GetResourceQueueResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetResourceQueueResponse.


        :param id: The id of this GetResourceQueueResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this GetResourceQueueResponse.  # noqa: E501


        :return: The name of this GetResourceQueueResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetResourceQueueResponse.


        :param name: The name of this GetResourceQueueResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def quota_allocated(self):
        """Gets the quota_allocated of this GetResourceQueueResponse.  # noqa: E501


        :return: The quota_allocated of this GetResourceQueueResponse.  # noqa: E501
        :rtype: QuotaAllocatedForGetResourceQueueOutput
        """
        return self._quota_allocated

    @quota_allocated.setter
    def quota_allocated(self, quota_allocated):
        """Sets the quota_allocated of this GetResourceQueueResponse.


        :param quota_allocated: The quota_allocated of this GetResourceQueueResponse.  # noqa: E501
        :type: QuotaAllocatedForGetResourceQueueOutput
        """

        self._quota_allocated = quota_allocated

    @property
    def quota_capability(self):
        """Gets the quota_capability of this GetResourceQueueResponse.  # noqa: E501


        :return: The quota_capability of this GetResourceQueueResponse.  # noqa: E501
        :rtype: QuotaCapabilityForGetResourceQueueOutput
        """
        return self._quota_capability

    @quota_capability.setter
    def quota_capability(self, quota_capability):
        """Sets the quota_capability of this GetResourceQueueResponse.


        :param quota_capability: The quota_capability of this GetResourceQueueResponse.  # noqa: E501
        :type: QuotaCapabilityForGetResourceQueueOutput
        """

        self._quota_capability = quota_capability

    @property
    def resource_group_id(self):
        """Gets the resource_group_id of this GetResourceQueueResponse.  # noqa: E501


        :return: The resource_group_id of this GetResourceQueueResponse.  # noqa: E501
        :rtype: str
        """
        return self._resource_group_id

    @resource_group_id.setter
    def resource_group_id(self, resource_group_id):
        """Sets the resource_group_id of this GetResourceQueueResponse.


        :param resource_group_id: The resource_group_id of this GetResourceQueueResponse.  # noqa: E501
        :type: str
        """

        self._resource_group_id = resource_group_id

    @property
    def rules(self):
        """Gets the rules of this GetResourceQueueResponse.  # noqa: E501


        :return: The rules of this GetResourceQueueResponse.  # noqa: E501
        :rtype: RulesForGetResourceQueueOutput
        """
        return self._rules

    @rules.setter
    def rules(self, rules):
        """Sets the rules of this GetResourceQueueResponse.


        :param rules: The rules of this GetResourceQueueResponse.  # noqa: E501
        :type: RulesForGetResourceQueueOutput
        """

        self._rules = rules

    @property
    def shareable(self):
        """Gets the shareable of this GetResourceQueueResponse.  # noqa: E501


        :return: The shareable of this GetResourceQueueResponse.  # noqa: E501
        :rtype: bool
        """
        return self._shareable

    @shareable.setter
    def shareable(self, shareable):
        """Sets the shareable of this GetResourceQueueResponse.


        :param shareable: The shareable of this GetResourceQueueResponse.  # noqa: E501
        :type: bool
        """

        self._shareable = shareable

    @property
    def shared_quota_allocated(self):
        """Gets the shared_quota_allocated of this GetResourceQueueResponse.  # noqa: E501


        :return: The shared_quota_allocated of this GetResourceQueueResponse.  # noqa: E501
        :rtype: SharedQuotaAllocatedForGetResourceQueueOutput
        """
        return self._shared_quota_allocated

    @shared_quota_allocated.setter
    def shared_quota_allocated(self, shared_quota_allocated):
        """Sets the shared_quota_allocated of this GetResourceQueueResponse.


        :param shared_quota_allocated: The shared_quota_allocated of this GetResourceQueueResponse.  # noqa: E501
        :type: SharedQuotaAllocatedForGetResourceQueueOutput
        """

        self._shared_quota_allocated = shared_quota_allocated

    @property
    def status(self):
        """Gets the status of this GetResourceQueueResponse.  # noqa: E501


        :return: The status of this GetResourceQueueResponse.  # noqa: E501
        :rtype: StatusForGetResourceQueueOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetResourceQueueResponse.


        :param status: The status of this GetResourceQueueResponse.  # noqa: E501
        :type: StatusForGetResourceQueueOutput
        """

        self._status = status

    @property
    def system_quota_allocated(self):
        """Gets the system_quota_allocated of this GetResourceQueueResponse.  # noqa: E501


        :return: The system_quota_allocated of this GetResourceQueueResponse.  # noqa: E501
        :rtype: SystemQuotaAllocatedForGetResourceQueueOutput
        """
        return self._system_quota_allocated

    @system_quota_allocated.setter
    def system_quota_allocated(self, system_quota_allocated):
        """Sets the system_quota_allocated of this GetResourceQueueResponse.


        :param system_quota_allocated: The system_quota_allocated of this GetResourceQueueResponse.  # noqa: E501
        :type: SystemQuotaAllocatedForGetResourceQueueOutput
        """

        self._system_quota_allocated = system_quota_allocated

    @property
    def volume_resources(self):
        """Gets the volume_resources of this GetResourceQueueResponse.  # noqa: E501


        :return: The volume_resources of this GetResourceQueueResponse.  # noqa: E501
        :rtype: list[VolumeResourceForGetResourceQueueOutput]
        """
        return self._volume_resources

    @volume_resources.setter
    def volume_resources(self, volume_resources):
        """Sets the volume_resources of this GetResourceQueueResponse.


        :param volume_resources: The volume_resources of this GetResourceQueueResponse.  # noqa: E501
        :type: list[VolumeResourceForGetResourceQueueOutput]
        """

        self._volume_resources = volume_resources

    @property
    def workload_infos(self):
        """Gets the workload_infos of this GetResourceQueueResponse.  # noqa: E501


        :return: The workload_infos of this GetResourceQueueResponse.  # noqa: E501
        :rtype: list[WorkloadInfoForGetResourceQueueOutput]
        """
        return self._workload_infos

    @workload_infos.setter
    def workload_infos(self, workload_infos):
        """Sets the workload_infos of this GetResourceQueueResponse.


        :param workload_infos: The workload_infos of this GetResourceQueueResponse.  # noqa: E501
        :type: list[WorkloadInfoForGetResourceQueueOutput]
        """

        self._workload_infos = workload_infos

    @property
    def zone_ids(self):
        """Gets the zone_ids of this GetResourceQueueResponse.  # noqa: E501


        :return: The zone_ids of this GetResourceQueueResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._zone_ids

    @zone_ids.setter
    def zone_ids(self, zone_ids):
        """Sets the zone_ids of this GetResourceQueueResponse.


        :param zone_ids: The zone_ids of this GetResourceQueueResponse.  # noqa: E501
        :type: list[str]
        """

        self._zone_ids = zone_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetResourceQueueResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetResourceQueueResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetResourceQueueResponse):
            return True

        return self.to_dict() != other.to_dict()
