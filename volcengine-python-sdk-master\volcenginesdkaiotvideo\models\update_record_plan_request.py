# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateRecordPlanRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'add_list': 'AddListForUpdateRecordPlanInput',
        'bind_template': 'str',
        'del_list': 'DelListForUpdateRecordPlanInput',
        'description': 'str',
        'plan_id': 'str',
        'plan_name': 'str',
        'resolution': 'str',
        'status': 'str',
        'streaming_index': 'int'
    }

    attribute_map = {
        'add_list': 'AddList',
        'bind_template': 'BindTemplate',
        'del_list': 'DelList',
        'description': 'Description',
        'plan_id': 'PlanID',
        'plan_name': 'PlanName',
        'resolution': 'Resolution',
        'status': 'Status',
        'streaming_index': 'StreamingIndex'
    }

    def __init__(self, add_list=None, bind_template=None, del_list=None, description=None, plan_id=None, plan_name=None, resolution=None, status=None, streaming_index=None, _configuration=None):  # noqa: E501
        """UpdateRecordPlanRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._add_list = None
        self._bind_template = None
        self._del_list = None
        self._description = None
        self._plan_id = None
        self._plan_name = None
        self._resolution = None
        self._status = None
        self._streaming_index = None
        self.discriminator = None

        if add_list is not None:
            self.add_list = add_list
        if bind_template is not None:
            self.bind_template = bind_template
        if del_list is not None:
            self.del_list = del_list
        if description is not None:
            self.description = description
        self.plan_id = plan_id
        if plan_name is not None:
            self.plan_name = plan_name
        if resolution is not None:
            self.resolution = resolution
        if status is not None:
            self.status = status
        if streaming_index is not None:
            self.streaming_index = streaming_index

    @property
    def add_list(self):
        """Gets the add_list of this UpdateRecordPlanRequest.  # noqa: E501


        :return: The add_list of this UpdateRecordPlanRequest.  # noqa: E501
        :rtype: AddListForUpdateRecordPlanInput
        """
        return self._add_list

    @add_list.setter
    def add_list(self, add_list):
        """Sets the add_list of this UpdateRecordPlanRequest.


        :param add_list: The add_list of this UpdateRecordPlanRequest.  # noqa: E501
        :type: AddListForUpdateRecordPlanInput
        """

        self._add_list = add_list

    @property
    def bind_template(self):
        """Gets the bind_template of this UpdateRecordPlanRequest.  # noqa: E501


        :return: The bind_template of this UpdateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._bind_template

    @bind_template.setter
    def bind_template(self, bind_template):
        """Sets the bind_template of this UpdateRecordPlanRequest.


        :param bind_template: The bind_template of this UpdateRecordPlanRequest.  # noqa: E501
        :type: str
        """

        self._bind_template = bind_template

    @property
    def del_list(self):
        """Gets the del_list of this UpdateRecordPlanRequest.  # noqa: E501


        :return: The del_list of this UpdateRecordPlanRequest.  # noqa: E501
        :rtype: DelListForUpdateRecordPlanInput
        """
        return self._del_list

    @del_list.setter
    def del_list(self, del_list):
        """Sets the del_list of this UpdateRecordPlanRequest.


        :param del_list: The del_list of this UpdateRecordPlanRequest.  # noqa: E501
        :type: DelListForUpdateRecordPlanInput
        """

        self._del_list = del_list

    @property
    def description(self):
        """Gets the description of this UpdateRecordPlanRequest.  # noqa: E501


        :return: The description of this UpdateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this UpdateRecordPlanRequest.


        :param description: The description of this UpdateRecordPlanRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def plan_id(self):
        """Gets the plan_id of this UpdateRecordPlanRequest.  # noqa: E501


        :return: The plan_id of this UpdateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._plan_id

    @plan_id.setter
    def plan_id(self, plan_id):
        """Sets the plan_id of this UpdateRecordPlanRequest.


        :param plan_id: The plan_id of this UpdateRecordPlanRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and plan_id is None:
            raise ValueError("Invalid value for `plan_id`, must not be `None`")  # noqa: E501

        self._plan_id = plan_id

    @property
    def plan_name(self):
        """Gets the plan_name of this UpdateRecordPlanRequest.  # noqa: E501


        :return: The plan_name of this UpdateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._plan_name

    @plan_name.setter
    def plan_name(self, plan_name):
        """Sets the plan_name of this UpdateRecordPlanRequest.


        :param plan_name: The plan_name of this UpdateRecordPlanRequest.  # noqa: E501
        :type: str
        """

        self._plan_name = plan_name

    @property
    def resolution(self):
        """Gets the resolution of this UpdateRecordPlanRequest.  # noqa: E501


        :return: The resolution of this UpdateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._resolution

    @resolution.setter
    def resolution(self, resolution):
        """Sets the resolution of this UpdateRecordPlanRequest.


        :param resolution: The resolution of this UpdateRecordPlanRequest.  # noqa: E501
        :type: str
        """

        self._resolution = resolution

    @property
    def status(self):
        """Gets the status of this UpdateRecordPlanRequest.  # noqa: E501


        :return: The status of this UpdateRecordPlanRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this UpdateRecordPlanRequest.


        :param status: The status of this UpdateRecordPlanRequest.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def streaming_index(self):
        """Gets the streaming_index of this UpdateRecordPlanRequest.  # noqa: E501


        :return: The streaming_index of this UpdateRecordPlanRequest.  # noqa: E501
        :rtype: int
        """
        return self._streaming_index

    @streaming_index.setter
    def streaming_index(self, streaming_index):
        """Sets the streaming_index of this UpdateRecordPlanRequest.


        :param streaming_index: The streaming_index of this UpdateRecordPlanRequest.  # noqa: E501
        :type: int
        """

        self._streaming_index = streaming_index

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateRecordPlanRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateRecordPlanRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateRecordPlanRequest):
            return True

        return self.to_dict() != other.to_dict()
