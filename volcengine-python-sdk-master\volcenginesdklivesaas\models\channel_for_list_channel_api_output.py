# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ChannelForListChannelAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'channel_name': 'str',
        'channel_tag': 'str',
        'create_time': 'int',
        'pv': 'int'
    }

    attribute_map = {
        'channel_name': 'ChannelName',
        'channel_tag': 'ChannelTag',
        'create_time': 'CreateTime',
        'pv': 'PV'
    }

    def __init__(self, channel_name=None, channel_tag=None, create_time=None, pv=None, _configuration=None):  # noqa: E501
        """ChannelForListChannelAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._channel_name = None
        self._channel_tag = None
        self._create_time = None
        self._pv = None
        self.discriminator = None

        if channel_name is not None:
            self.channel_name = channel_name
        if channel_tag is not None:
            self.channel_tag = channel_tag
        if create_time is not None:
            self.create_time = create_time
        if pv is not None:
            self.pv = pv

    @property
    def channel_name(self):
        """Gets the channel_name of this ChannelForListChannelAPIOutput.  # noqa: E501


        :return: The channel_name of this ChannelForListChannelAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._channel_name

    @channel_name.setter
    def channel_name(self, channel_name):
        """Sets the channel_name of this ChannelForListChannelAPIOutput.


        :param channel_name: The channel_name of this ChannelForListChannelAPIOutput.  # noqa: E501
        :type: str
        """

        self._channel_name = channel_name

    @property
    def channel_tag(self):
        """Gets the channel_tag of this ChannelForListChannelAPIOutput.  # noqa: E501


        :return: The channel_tag of this ChannelForListChannelAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._channel_tag

    @channel_tag.setter
    def channel_tag(self, channel_tag):
        """Sets the channel_tag of this ChannelForListChannelAPIOutput.


        :param channel_tag: The channel_tag of this ChannelForListChannelAPIOutput.  # noqa: E501
        :type: str
        """

        self._channel_tag = channel_tag

    @property
    def create_time(self):
        """Gets the create_time of this ChannelForListChannelAPIOutput.  # noqa: E501


        :return: The create_time of this ChannelForListChannelAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ChannelForListChannelAPIOutput.


        :param create_time: The create_time of this ChannelForListChannelAPIOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def pv(self):
        """Gets the pv of this ChannelForListChannelAPIOutput.  # noqa: E501


        :return: The pv of this ChannelForListChannelAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._pv

    @pv.setter
    def pv(self, pv):
        """Sets the pv of this ChannelForListChannelAPIOutput.


        :param pv: The pv of this ChannelForListChannelAPIOutput.  # noqa: E501
        :type: int
        """

        self._pv = pv

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ChannelForListChannelAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ChannelForListChannelAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ChannelForListChannelAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
