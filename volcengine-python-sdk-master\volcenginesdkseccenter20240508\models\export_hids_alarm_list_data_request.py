# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ExportHidsAlarmListDataRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_id_list': 'list[str]',
        'conditions': 'ConditionsForExportHidsAlarmListDataInput'
    }

    attribute_map = {
        'alarm_id_list': 'AlarmIDList',
        'conditions': 'Conditions'
    }

    def __init__(self, alarm_id_list=None, conditions=None, _configuration=None):  # noqa: E501
        """ExportHidsAlarmListDataRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_id_list = None
        self._conditions = None
        self.discriminator = None

        if alarm_id_list is not None:
            self.alarm_id_list = alarm_id_list
        if conditions is not None:
            self.conditions = conditions

    @property
    def alarm_id_list(self):
        """Gets the alarm_id_list of this ExportHidsAlarmListDataRequest.  # noqa: E501


        :return: The alarm_id_list of this ExportHidsAlarmListDataRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._alarm_id_list

    @alarm_id_list.setter
    def alarm_id_list(self, alarm_id_list):
        """Sets the alarm_id_list of this ExportHidsAlarmListDataRequest.


        :param alarm_id_list: The alarm_id_list of this ExportHidsAlarmListDataRequest.  # noqa: E501
        :type: list[str]
        """

        self._alarm_id_list = alarm_id_list

    @property
    def conditions(self):
        """Gets the conditions of this ExportHidsAlarmListDataRequest.  # noqa: E501


        :return: The conditions of this ExportHidsAlarmListDataRequest.  # noqa: E501
        :rtype: ConditionsForExportHidsAlarmListDataInput
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this ExportHidsAlarmListDataRequest.


        :param conditions: The conditions of this ExportHidsAlarmListDataRequest.  # noqa: E501
        :type: ConditionsForExportHidsAlarmListDataInput
        """

        self._conditions = conditions

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExportHidsAlarmListDataRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExportHidsAlarmListDataRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExportHidsAlarmListDataRequest):
            return True

        return self.to_dict() != other.to_dict()
