# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListCrossAccountVIFAuthorityResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'authority_list': 'list[AuthorityListForListCrossAccountVIFAuthorityOutput]',
        'authorized_count': 'int',
        'authorizing_count': 'int',
        'canceled_count': 'int',
        'page_number': 'int',
        'page_size': 'int',
        'reject_count': 'int',
        'total_count': 'int'
    }

    attribute_map = {
        'authority_list': 'AuthorityList',
        'authorized_count': 'AuthorizedCount',
        'authorizing_count': 'AuthorizingCount',
        'canceled_count': 'CanceledCount',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'reject_count': 'RejectCount',
        'total_count': 'TotalCount'
    }

    def __init__(self, authority_list=None, authorized_count=None, authorizing_count=None, canceled_count=None, page_number=None, page_size=None, reject_count=None, total_count=None, _configuration=None):  # noqa: E501
        """ListCrossAccountVIFAuthorityResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._authority_list = None
        self._authorized_count = None
        self._authorizing_count = None
        self._canceled_count = None
        self._page_number = None
        self._page_size = None
        self._reject_count = None
        self._total_count = None
        self.discriminator = None

        if authority_list is not None:
            self.authority_list = authority_list
        if authorized_count is not None:
            self.authorized_count = authorized_count
        if authorizing_count is not None:
            self.authorizing_count = authorizing_count
        if canceled_count is not None:
            self.canceled_count = canceled_count
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if reject_count is not None:
            self.reject_count = reject_count
        if total_count is not None:
            self.total_count = total_count

    @property
    def authority_list(self):
        """Gets the authority_list of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501


        :return: The authority_list of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :rtype: list[AuthorityListForListCrossAccountVIFAuthorityOutput]
        """
        return self._authority_list

    @authority_list.setter
    def authority_list(self, authority_list):
        """Sets the authority_list of this ListCrossAccountVIFAuthorityResponse.


        :param authority_list: The authority_list of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :type: list[AuthorityListForListCrossAccountVIFAuthorityOutput]
        """

        self._authority_list = authority_list

    @property
    def authorized_count(self):
        """Gets the authorized_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501


        :return: The authorized_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :rtype: int
        """
        return self._authorized_count

    @authorized_count.setter
    def authorized_count(self, authorized_count):
        """Sets the authorized_count of this ListCrossAccountVIFAuthorityResponse.


        :param authorized_count: The authorized_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :type: int
        """

        self._authorized_count = authorized_count

    @property
    def authorizing_count(self):
        """Gets the authorizing_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501


        :return: The authorizing_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :rtype: int
        """
        return self._authorizing_count

    @authorizing_count.setter
    def authorizing_count(self, authorizing_count):
        """Sets the authorizing_count of this ListCrossAccountVIFAuthorityResponse.


        :param authorizing_count: The authorizing_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :type: int
        """

        self._authorizing_count = authorizing_count

    @property
    def canceled_count(self):
        """Gets the canceled_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501


        :return: The canceled_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :rtype: int
        """
        return self._canceled_count

    @canceled_count.setter
    def canceled_count(self, canceled_count):
        """Sets the canceled_count of this ListCrossAccountVIFAuthorityResponse.


        :param canceled_count: The canceled_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :type: int
        """

        self._canceled_count = canceled_count

    @property
    def page_number(self):
        """Gets the page_number of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501


        :return: The page_number of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListCrossAccountVIFAuthorityResponse.


        :param page_number: The page_number of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501


        :return: The page_size of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListCrossAccountVIFAuthorityResponse.


        :param page_size: The page_size of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def reject_count(self):
        """Gets the reject_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501


        :return: The reject_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :rtype: int
        """
        return self._reject_count

    @reject_count.setter
    def reject_count(self, reject_count):
        """Sets the reject_count of this ListCrossAccountVIFAuthorityResponse.


        :param reject_count: The reject_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :type: int
        """

        self._reject_count = reject_count

    @property
    def total_count(self):
        """Gets the total_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501


        :return: The total_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this ListCrossAccountVIFAuthorityResponse.


        :param total_count: The total_count of this ListCrossAccountVIFAuthorityResponse.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListCrossAccountVIFAuthorityResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListCrossAccountVIFAuthorityResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListCrossAccountVIFAuthorityResponse):
            return True

        return self.to_dict() != other.to_dict()
