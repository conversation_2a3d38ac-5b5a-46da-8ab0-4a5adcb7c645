# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GroupForListLayeredGroupsDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'children': 'list[str]',
        'group_id': 'str',
        'group_name': 'str',
        'group_path': 'str',
        'host_count': 'int',
        'level': 'int',
        'parent_id': 'str',
        'top_group_id': 'str',
        'group_id_path': 'str'
    }

    attribute_map = {
        'children': 'Children',
        'group_id': 'GroupID',
        'group_name': 'GroupName',
        'group_path': 'GroupPath',
        'host_count': 'HostCount',
        'level': 'Level',
        'parent_id': 'ParentID',
        'top_group_id': 'TopGroupID',
        'group_id_path': 'groupIdPath'
    }

    def __init__(self, children=None, group_id=None, group_name=None, group_path=None, host_count=None, level=None, parent_id=None, top_group_id=None, group_id_path=None, _configuration=None):  # noqa: E501
        """GroupForListLayeredGroupsDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._children = None
        self._group_id = None
        self._group_name = None
        self._group_path = None
        self._host_count = None
        self._level = None
        self._parent_id = None
        self._top_group_id = None
        self._group_id_path = None
        self.discriminator = None

        if children is not None:
            self.children = children
        if group_id is not None:
            self.group_id = group_id
        if group_name is not None:
            self.group_name = group_name
        if group_path is not None:
            self.group_path = group_path
        if host_count is not None:
            self.host_count = host_count
        if level is not None:
            self.level = level
        if parent_id is not None:
            self.parent_id = parent_id
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if group_id_path is not None:
            self.group_id_path = group_id_path

    @property
    def children(self):
        """Gets the children of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501


        :return: The children of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._children

    @children.setter
    def children(self, children):
        """Sets the children of this GroupForListLayeredGroupsDetailOutput.


        :param children: The children of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :type: list[str]
        """

        self._children = children

    @property
    def group_id(self):
        """Gets the group_id of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501


        :return: The group_id of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this GroupForListLayeredGroupsDetailOutput.


        :param group_id: The group_id of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :type: str
        """

        self._group_id = group_id

    @property
    def group_name(self):
        """Gets the group_name of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501


        :return: The group_name of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_name

    @group_name.setter
    def group_name(self, group_name):
        """Sets the group_name of this GroupForListLayeredGroupsDetailOutput.


        :param group_name: The group_name of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :type: str
        """

        self._group_name = group_name

    @property
    def group_path(self):
        """Gets the group_path of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501


        :return: The group_path of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_path

    @group_path.setter
    def group_path(self, group_path):
        """Sets the group_path of this GroupForListLayeredGroupsDetailOutput.


        :param group_path: The group_path of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :type: str
        """

        self._group_path = group_path

    @property
    def host_count(self):
        """Gets the host_count of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501


        :return: The host_count of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._host_count

    @host_count.setter
    def host_count(self, host_count):
        """Sets the host_count of this GroupForListLayeredGroupsDetailOutput.


        :param host_count: The host_count of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :type: int
        """

        self._host_count = host_count

    @property
    def level(self):
        """Gets the level of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501


        :return: The level of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._level

    @level.setter
    def level(self, level):
        """Sets the level of this GroupForListLayeredGroupsDetailOutput.


        :param level: The level of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :type: int
        """

        self._level = level

    @property
    def parent_id(self):
        """Gets the parent_id of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501


        :return: The parent_id of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._parent_id

    @parent_id.setter
    def parent_id(self, parent_id):
        """Sets the parent_id of this GroupForListLayeredGroupsDetailOutput.


        :param parent_id: The parent_id of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :type: str
        """

        self._parent_id = parent_id

    @property
    def top_group_id(self):
        """Gets the top_group_id of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501


        :return: The top_group_id of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this GroupForListLayeredGroupsDetailOutput.


        :param top_group_id: The top_group_id of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def group_id_path(self):
        """Gets the group_id_path of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501


        :return: The group_id_path of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_id_path

    @group_id_path.setter
    def group_id_path(self, group_id_path):
        """Sets the group_id_path of this GroupForListLayeredGroupsDetailOutput.


        :param group_id_path: The group_id_path of this GroupForListLayeredGroupsDetailOutput.  # noqa: E501
        :type: str
        """

        self._group_id_path = group_id_path

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GroupForListLayeredGroupsDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GroupForListLayeredGroupsDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GroupForListLayeredGroupsDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
