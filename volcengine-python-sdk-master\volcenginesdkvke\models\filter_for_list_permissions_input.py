# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListPermissionsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_ids': 'list[str]',
        'grantee_ids': 'list[int]',
        'grantee_type': 'str',
        'ids': 'list[str]',
        'namespaces': 'list[str]',
        'role_names': 'list[str]',
        'status': 'str'
    }

    attribute_map = {
        'cluster_ids': 'ClusterIds',
        'grantee_ids': 'GranteeIds',
        'grantee_type': 'GranteeType',
        'ids': 'Ids',
        'namespaces': 'Namespaces',
        'role_names': 'RoleNames',
        'status': 'Status'
    }

    def __init__(self, cluster_ids=None, grantee_ids=None, grantee_type=None, ids=None, namespaces=None, role_names=None, status=None, _configuration=None):  # noqa: E501
        """FilterForListPermissionsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_ids = None
        self._grantee_ids = None
        self._grantee_type = None
        self._ids = None
        self._namespaces = None
        self._role_names = None
        self._status = None
        self.discriminator = None

        if cluster_ids is not None:
            self.cluster_ids = cluster_ids
        if grantee_ids is not None:
            self.grantee_ids = grantee_ids
        if grantee_type is not None:
            self.grantee_type = grantee_type
        if ids is not None:
            self.ids = ids
        if namespaces is not None:
            self.namespaces = namespaces
        if role_names is not None:
            self.role_names = role_names
        if status is not None:
            self.status = status

    @property
    def cluster_ids(self):
        """Gets the cluster_ids of this FilterForListPermissionsInput.  # noqa: E501


        :return: The cluster_ids of this FilterForListPermissionsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_ids

    @cluster_ids.setter
    def cluster_ids(self, cluster_ids):
        """Sets the cluster_ids of this FilterForListPermissionsInput.


        :param cluster_ids: The cluster_ids of this FilterForListPermissionsInput.  # noqa: E501
        :type: list[str]
        """

        self._cluster_ids = cluster_ids

    @property
    def grantee_ids(self):
        """Gets the grantee_ids of this FilterForListPermissionsInput.  # noqa: E501


        :return: The grantee_ids of this FilterForListPermissionsInput.  # noqa: E501
        :rtype: list[int]
        """
        return self._grantee_ids

    @grantee_ids.setter
    def grantee_ids(self, grantee_ids):
        """Sets the grantee_ids of this FilterForListPermissionsInput.


        :param grantee_ids: The grantee_ids of this FilterForListPermissionsInput.  # noqa: E501
        :type: list[int]
        """

        self._grantee_ids = grantee_ids

    @property
    def grantee_type(self):
        """Gets the grantee_type of this FilterForListPermissionsInput.  # noqa: E501


        :return: The grantee_type of this FilterForListPermissionsInput.  # noqa: E501
        :rtype: str
        """
        return self._grantee_type

    @grantee_type.setter
    def grantee_type(self, grantee_type):
        """Sets the grantee_type of this FilterForListPermissionsInput.


        :param grantee_type: The grantee_type of this FilterForListPermissionsInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["User", "Role", "Account"]  # noqa: E501
        if (self._configuration.client_side_validation and
                grantee_type not in allowed_values):
            raise ValueError(
                "Invalid value for `grantee_type` ({0}), must be one of {1}"  # noqa: E501
                .format(grantee_type, allowed_values)
            )

        self._grantee_type = grantee_type

    @property
    def ids(self):
        """Gets the ids of this FilterForListPermissionsInput.  # noqa: E501


        :return: The ids of this FilterForListPermissionsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ids

    @ids.setter
    def ids(self, ids):
        """Sets the ids of this FilterForListPermissionsInput.


        :param ids: The ids of this FilterForListPermissionsInput.  # noqa: E501
        :type: list[str]
        """

        self._ids = ids

    @property
    def namespaces(self):
        """Gets the namespaces of this FilterForListPermissionsInput.  # noqa: E501


        :return: The namespaces of this FilterForListPermissionsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._namespaces

    @namespaces.setter
    def namespaces(self, namespaces):
        """Sets the namespaces of this FilterForListPermissionsInput.


        :param namespaces: The namespaces of this FilterForListPermissionsInput.  # noqa: E501
        :type: list[str]
        """

        self._namespaces = namespaces

    @property
    def role_names(self):
        """Gets the role_names of this FilterForListPermissionsInput.  # noqa: E501


        :return: The role_names of this FilterForListPermissionsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._role_names

    @role_names.setter
    def role_names(self, role_names):
        """Sets the role_names of this FilterForListPermissionsInput.


        :param role_names: The role_names of this FilterForListPermissionsInput.  # noqa: E501
        :type: list[str]
        """

        self._role_names = role_names

    @property
    def status(self):
        """Gets the status of this FilterForListPermissionsInput.  # noqa: E501


        :return: The status of this FilterForListPermissionsInput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this FilterForListPermissionsInput.


        :param status: The status of this FilterForListPermissionsInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Pending", "PartialSuccess", "Success", "Failed"]  # noqa: E501
        if (self._configuration.client_side_validation and
                status not in allowed_values):
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}"  # noqa: E501
                .format(status, allowed_values)
            )

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListPermissionsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListPermissionsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListPermissionsInput):
            return True

        return self.to_dict() != other.to_dict()
