# coding: utf-8

"""
    bytehouse_ce20240831

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateRoleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'int',
        'description': 'str',
        'name': 'str',
        'users': 'list[UserForCreateRoleInput]'
    }

    attribute_map = {
        'cluster_id': 'ClusterID',
        'description': 'Description',
        'name': 'Name',
        'users': 'Users'
    }

    def __init__(self, cluster_id=None, description=None, name=None, users=None, _configuration=None):  # noqa: E501
        """CreateRoleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._description = None
        self._name = None
        self._users = None
        self.discriminator = None

        self.cluster_id = cluster_id
        if description is not None:
            self.description = description
        self.name = name
        if users is not None:
            self.users = users

    @property
    def cluster_id(self):
        """Gets the cluster_id of this CreateRoleRequest.  # noqa: E501


        :return: The cluster_id of this CreateRoleRequest.  # noqa: E501
        :rtype: int
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this CreateRoleRequest.


        :param cluster_id: The cluster_id of this CreateRoleRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def description(self):
        """Gets the description of this CreateRoleRequest.  # noqa: E501


        :return: The description of this CreateRoleRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateRoleRequest.


        :param description: The description of this CreateRoleRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def name(self):
        """Gets the name of this CreateRoleRequest.  # noqa: E501


        :return: The name of this CreateRoleRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateRoleRequest.


        :param name: The name of this CreateRoleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def users(self):
        """Gets the users of this CreateRoleRequest.  # noqa: E501


        :return: The users of this CreateRoleRequest.  # noqa: E501
        :rtype: list[UserForCreateRoleInput]
        """
        return self._users

    @users.setter
    def users(self, users):
        """Sets the users of this CreateRoleRequest.


        :param users: The users of this CreateRoleRequest.  # noqa: E501
        :type: list[UserForCreateRoleInput]
        """

        self._users = users

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateRoleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateRoleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateRoleRequest):
            return True

        return self.to_dict() != other.to_dict()
