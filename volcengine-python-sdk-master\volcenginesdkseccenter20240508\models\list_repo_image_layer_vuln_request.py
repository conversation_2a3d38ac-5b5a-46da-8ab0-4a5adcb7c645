# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListRepoImageLayerVulnRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_id': 'str',
        'filter': 'FilterForListRepoImageLayerVulnInput',
        'layer_id': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str'
    }

    attribute_map = {
        'asset_id': 'AssetID',
        'filter': 'Filter',
        'layer_id': 'LayerID',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder'
    }

    def __init__(self, asset_id=None, filter=None, layer_id=None, page_number=None, page_size=None, sort_by=None, sort_order=None, _configuration=None):  # noqa: E501
        """ListRepoImageLayerVulnRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_id = None
        self._filter = None
        self._layer_id = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self.discriminator = None

        self.asset_id = asset_id
        if filter is not None:
            self.filter = filter
        self.layer_id = layer_id
        self.page_number = page_number
        self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order

    @property
    def asset_id(self):
        """Gets the asset_id of this ListRepoImageLayerVulnRequest.  # noqa: E501


        :return: The asset_id of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this ListRepoImageLayerVulnRequest.


        :param asset_id: The asset_id of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and asset_id is None:
            raise ValueError("Invalid value for `asset_id`, must not be `None`")  # noqa: E501

        self._asset_id = asset_id

    @property
    def filter(self):
        """Gets the filter of this ListRepoImageLayerVulnRequest.  # noqa: E501


        :return: The filter of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :rtype: FilterForListRepoImageLayerVulnInput
        """
        return self._filter

    @filter.setter
    def filter(self, filter):
        """Sets the filter of this ListRepoImageLayerVulnRequest.


        :param filter: The filter of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :type: FilterForListRepoImageLayerVulnInput
        """

        self._filter = filter

    @property
    def layer_id(self):
        """Gets the layer_id of this ListRepoImageLayerVulnRequest.  # noqa: E501


        :return: The layer_id of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :rtype: str
        """
        return self._layer_id

    @layer_id.setter
    def layer_id(self, layer_id):
        """Sets the layer_id of this ListRepoImageLayerVulnRequest.


        :param layer_id: The layer_id of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and layer_id is None:
            raise ValueError("Invalid value for `layer_id`, must not be `None`")  # noqa: E501

        self._layer_id = layer_id

    @property
    def page_number(self):
        """Gets the page_number of this ListRepoImageLayerVulnRequest.  # noqa: E501


        :return: The page_number of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListRepoImageLayerVulnRequest.


        :param page_number: The page_number of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListRepoImageLayerVulnRequest.  # noqa: E501


        :return: The page_size of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListRepoImageLayerVulnRequest.


        :param page_size: The page_size of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListRepoImageLayerVulnRequest.  # noqa: E501


        :return: The sort_by of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListRepoImageLayerVulnRequest.


        :param sort_by: The sort_by of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListRepoImageLayerVulnRequest.  # noqa: E501


        :return: The sort_order of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListRepoImageLayerVulnRequest.


        :param sort_order: The sort_order of this ListRepoImageLayerVulnRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListRepoImageLayerVulnRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListRepoImageLayerVulnRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListRepoImageLayerVulnRequest):
            return True

        return self.to_dict() != other.to_dict()
