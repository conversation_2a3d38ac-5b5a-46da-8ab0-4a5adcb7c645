# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VulnListForGetRaspAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cve': 'str',
        'title_cn': 'str'
    }

    attribute_map = {
        'cve': 'Cve',
        'title_cn': 'TitleCn'
    }

    def __init__(self, cve=None, title_cn=None, _configuration=None):  # noqa: E501
        """VulnListForGetRaspAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cve = None
        self._title_cn = None
        self.discriminator = None

        if cve is not None:
            self.cve = cve
        if title_cn is not None:
            self.title_cn = title_cn

    @property
    def cve(self):
        """Gets the cve of this VulnListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501


        :return: The cve of this VulnListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._cve

    @cve.setter
    def cve(self, cve):
        """Sets the cve of this VulnListForGetRaspAlarmSummaryInfoOutput.


        :param cve: The cve of this VulnListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._cve = cve

    @property
    def title_cn(self):
        """Gets the title_cn of this VulnListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501


        :return: The title_cn of this VulnListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._title_cn

    @title_cn.setter
    def title_cn(self, title_cn):
        """Sets the title_cn of this VulnListForGetRaspAlarmSummaryInfoOutput.


        :param title_cn: The title_cn of this VulnListForGetRaspAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._title_cn = title_cn

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VulnListForGetRaspAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VulnListForGetRaspAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VulnListForGetRaspAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
