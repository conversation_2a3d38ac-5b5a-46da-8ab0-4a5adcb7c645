# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatisticForDescribeFileSystemStatisticsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'common_capacity': 'CommonCapacityForDescribeFileSystemStatisticsOutput',
        'common_file_system_count': 'int',
        'extreme_capacity': 'ExtremeCapacityForDescribeFileSystemStatisticsOutput',
        'extreme_file_system_count': 'int',
        'region_id': 'str',
        'zone_id': 'str',
        'zone_name': 'str'
    }

    attribute_map = {
        'common_capacity': 'CommonCapacity',
        'common_file_system_count': 'CommonFileSystemCount',
        'extreme_capacity': 'ExtremeCapacity',
        'extreme_file_system_count': 'ExtremeFileSystemCount',
        'region_id': 'RegionId',
        'zone_id': 'ZoneId',
        'zone_name': 'ZoneName'
    }

    def __init__(self, common_capacity=None, common_file_system_count=None, extreme_capacity=None, extreme_file_system_count=None, region_id=None, zone_id=None, zone_name=None, _configuration=None):  # noqa: E501
        """StatisticForDescribeFileSystemStatisticsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._common_capacity = None
        self._common_file_system_count = None
        self._extreme_capacity = None
        self._extreme_file_system_count = None
        self._region_id = None
        self._zone_id = None
        self._zone_name = None
        self.discriminator = None

        if common_capacity is not None:
            self.common_capacity = common_capacity
        if common_file_system_count is not None:
            self.common_file_system_count = common_file_system_count
        if extreme_capacity is not None:
            self.extreme_capacity = extreme_capacity
        if extreme_file_system_count is not None:
            self.extreme_file_system_count = extreme_file_system_count
        if region_id is not None:
            self.region_id = region_id
        if zone_id is not None:
            self.zone_id = zone_id
        if zone_name is not None:
            self.zone_name = zone_name

    @property
    def common_capacity(self):
        """Gets the common_capacity of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The common_capacity of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: CommonCapacityForDescribeFileSystemStatisticsOutput
        """
        return self._common_capacity

    @common_capacity.setter
    def common_capacity(self, common_capacity):
        """Sets the common_capacity of this StatisticForDescribeFileSystemStatisticsOutput.


        :param common_capacity: The common_capacity of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: CommonCapacityForDescribeFileSystemStatisticsOutput
        """

        self._common_capacity = common_capacity

    @property
    def common_file_system_count(self):
        """Gets the common_file_system_count of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The common_file_system_count of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._common_file_system_count

    @common_file_system_count.setter
    def common_file_system_count(self, common_file_system_count):
        """Sets the common_file_system_count of this StatisticForDescribeFileSystemStatisticsOutput.


        :param common_file_system_count: The common_file_system_count of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._common_file_system_count = common_file_system_count

    @property
    def extreme_capacity(self):
        """Gets the extreme_capacity of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The extreme_capacity of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: ExtremeCapacityForDescribeFileSystemStatisticsOutput
        """
        return self._extreme_capacity

    @extreme_capacity.setter
    def extreme_capacity(self, extreme_capacity):
        """Sets the extreme_capacity of this StatisticForDescribeFileSystemStatisticsOutput.


        :param extreme_capacity: The extreme_capacity of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: ExtremeCapacityForDescribeFileSystemStatisticsOutput
        """

        self._extreme_capacity = extreme_capacity

    @property
    def extreme_file_system_count(self):
        """Gets the extreme_file_system_count of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The extreme_file_system_count of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: int
        """
        return self._extreme_file_system_count

    @extreme_file_system_count.setter
    def extreme_file_system_count(self, extreme_file_system_count):
        """Sets the extreme_file_system_count of this StatisticForDescribeFileSystemStatisticsOutput.


        :param extreme_file_system_count: The extreme_file_system_count of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: int
        """

        self._extreme_file_system_count = extreme_file_system_count

    @property
    def region_id(self):
        """Gets the region_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The region_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_id

    @region_id.setter
    def region_id(self, region_id):
        """Sets the region_id of this StatisticForDescribeFileSystemStatisticsOutput.


        :param region_id: The region_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._region_id = region_id

    @property
    def zone_id(self):
        """Gets the zone_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The zone_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this StatisticForDescribeFileSystemStatisticsOutput.


        :param zone_id: The zone_id of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    @property
    def zone_name(self):
        """Gets the zone_name of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501


        :return: The zone_name of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this StatisticForDescribeFileSystemStatisticsOutput.


        :param zone_name: The zone_name of this StatisticForDescribeFileSystemStatisticsOutput.  # noqa: E501
        :type: str
        """

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatisticForDescribeFileSystemStatisticsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatisticForDescribeFileSystemStatisticsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatisticForDescribeFileSystemStatisticsOutput):
            return True

        return self.to_dict() != other.to_dict()
