# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VirusFileForGetSecurityOverviewOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'risk_count': 'int',
        'subject_count': 'int'
    }

    attribute_map = {
        'risk_count': 'RiskCount',
        'subject_count': 'SubjectCount'
    }

    def __init__(self, risk_count=None, subject_count=None, _configuration=None):  # noqa: E501
        """VirusFileForGetSecurityOverviewOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._risk_count = None
        self._subject_count = None
        self.discriminator = None

        if risk_count is not None:
            self.risk_count = risk_count
        if subject_count is not None:
            self.subject_count = subject_count

    @property
    def risk_count(self):
        """Gets the risk_count of this VirusFileForGetSecurityOverviewOutput.  # noqa: E501


        :return: The risk_count of this VirusFileForGetSecurityOverviewOutput.  # noqa: E501
        :rtype: int
        """
        return self._risk_count

    @risk_count.setter
    def risk_count(self, risk_count):
        """Sets the risk_count of this VirusFileForGetSecurityOverviewOutput.


        :param risk_count: The risk_count of this VirusFileForGetSecurityOverviewOutput.  # noqa: E501
        :type: int
        """

        self._risk_count = risk_count

    @property
    def subject_count(self):
        """Gets the subject_count of this VirusFileForGetSecurityOverviewOutput.  # noqa: E501


        :return: The subject_count of this VirusFileForGetSecurityOverviewOutput.  # noqa: E501
        :rtype: int
        """
        return self._subject_count

    @subject_count.setter
    def subject_count(self, subject_count):
        """Sets the subject_count of this VirusFileForGetSecurityOverviewOutput.


        :param subject_count: The subject_count of this VirusFileForGetSecurityOverviewOutput.  # noqa: E501
        :type: int
        """

        self._subject_count = subject_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VirusFileForGetSecurityOverviewOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VirusFileForGetSecurityOverviewOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VirusFileForGetSecurityOverviewOutput):
            return True

        return self.to_dict() != other.to_dict()
