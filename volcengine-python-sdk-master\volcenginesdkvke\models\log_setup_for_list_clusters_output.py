# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LogSetupForListClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enabled': 'bool',
        'log_topic_id': 'str',
        'log_ttl': 'int',
        'log_type': 'str'
    }

    attribute_map = {
        'enabled': 'Enabled',
        'log_topic_id': 'LogTopicId',
        'log_ttl': 'LogTtl',
        'log_type': 'LogType'
    }

    def __init__(self, enabled=None, log_topic_id=None, log_ttl=None, log_type=None, _configuration=None):  # noqa: E501
        """LogSetupForListClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enabled = None
        self._log_topic_id = None
        self._log_ttl = None
        self._log_type = None
        self.discriminator = None

        if enabled is not None:
            self.enabled = enabled
        if log_topic_id is not None:
            self.log_topic_id = log_topic_id
        if log_ttl is not None:
            self.log_ttl = log_ttl
        if log_type is not None:
            self.log_type = log_type

    @property
    def enabled(self):
        """Gets the enabled of this LogSetupForListClustersOutput.  # noqa: E501


        :return: The enabled of this LogSetupForListClustersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this LogSetupForListClustersOutput.


        :param enabled: The enabled of this LogSetupForListClustersOutput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def log_topic_id(self):
        """Gets the log_topic_id of this LogSetupForListClustersOutput.  # noqa: E501


        :return: The log_topic_id of this LogSetupForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_topic_id

    @log_topic_id.setter
    def log_topic_id(self, log_topic_id):
        """Sets the log_topic_id of this LogSetupForListClustersOutput.


        :param log_topic_id: The log_topic_id of this LogSetupForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._log_topic_id = log_topic_id

    @property
    def log_ttl(self):
        """Gets the log_ttl of this LogSetupForListClustersOutput.  # noqa: E501


        :return: The log_ttl of this LogSetupForListClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._log_ttl

    @log_ttl.setter
    def log_ttl(self, log_ttl):
        """Sets the log_ttl of this LogSetupForListClustersOutput.


        :param log_ttl: The log_ttl of this LogSetupForListClustersOutput.  # noqa: E501
        :type: int
        """

        self._log_ttl = log_ttl

    @property
    def log_type(self):
        """Gets the log_type of this LogSetupForListClustersOutput.  # noqa: E501


        :return: The log_type of this LogSetupForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._log_type

    @log_type.setter
    def log_type(self, log_type):
        """Sets the log_type of this LogSetupForListClustersOutput.


        :param log_type: The log_type of this LogSetupForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._log_type = log_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LogSetupForListClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LogSetupForListClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LogSetupForListClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
