# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ConfigDataForListSharedConfigOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'config_name': 'str',
        'config_type': 'str',
        'domain_count': 'int',
        'project': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'config_name': 'ConfigName',
        'config_type': 'ConfigType',
        'domain_count': 'DomainCount',
        'project': 'Project',
        'update_time': 'UpdateTime'
    }

    def __init__(self, config_name=None, config_type=None, domain_count=None, project=None, update_time=None, _configuration=None):  # noqa: E501
        """ConfigDataForListSharedConfigOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._config_name = None
        self._config_type = None
        self._domain_count = None
        self._project = None
        self._update_time = None
        self.discriminator = None

        if config_name is not None:
            self.config_name = config_name
        if config_type is not None:
            self.config_type = config_type
        if domain_count is not None:
            self.domain_count = domain_count
        if project is not None:
            self.project = project
        if update_time is not None:
            self.update_time = update_time

    @property
    def config_name(self):
        """Gets the config_name of this ConfigDataForListSharedConfigOutput.  # noqa: E501


        :return: The config_name of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_name

    @config_name.setter
    def config_name(self, config_name):
        """Sets the config_name of this ConfigDataForListSharedConfigOutput.


        :param config_name: The config_name of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :type: str
        """

        self._config_name = config_name

    @property
    def config_type(self):
        """Gets the config_type of this ConfigDataForListSharedConfigOutput.  # noqa: E501


        :return: The config_type of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._config_type

    @config_type.setter
    def config_type(self, config_type):
        """Sets the config_type of this ConfigDataForListSharedConfigOutput.


        :param config_type: The config_type of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :type: str
        """

        self._config_type = config_type

    @property
    def domain_count(self):
        """Gets the domain_count of this ConfigDataForListSharedConfigOutput.  # noqa: E501


        :return: The domain_count of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._domain_count

    @domain_count.setter
    def domain_count(self, domain_count):
        """Sets the domain_count of this ConfigDataForListSharedConfigOutput.


        :param domain_count: The domain_count of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :type: int
        """

        self._domain_count = domain_count

    @property
    def project(self):
        """Gets the project of this ConfigDataForListSharedConfigOutput.  # noqa: E501


        :return: The project of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this ConfigDataForListSharedConfigOutput.


        :param project: The project of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :type: str
        """

        self._project = project

    @property
    def update_time(self):
        """Gets the update_time of this ConfigDataForListSharedConfigOutput.  # noqa: E501


        :return: The update_time of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ConfigDataForListSharedConfigOutput.


        :param update_time: The update_time of this ConfigDataForListSharedConfigOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ConfigDataForListSharedConfigOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ConfigDataForListSharedConfigOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ConfigDataForListSharedConfigOutput):
            return True

        return self.to_dict() != other.to_dict()
