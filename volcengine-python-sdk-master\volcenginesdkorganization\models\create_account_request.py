# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateAccountRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_name': 'str',
        'description': 'str',
        'inherit_profile': 'bool',
        'org_unit_id': 'str',
        'show_name': 'str',
        'verification_relation_id': 'str'
    }

    attribute_map = {
        'account_name': 'AccountName',
        'description': 'Description',
        'inherit_profile': 'InheritProfile',
        'org_unit_id': 'OrgUnitId',
        'show_name': 'ShowName',
        'verification_relation_id': 'VerificationRelationId'
    }

    def __init__(self, account_name=None, description=None, inherit_profile=None, org_unit_id=None, show_name=None, verification_relation_id=None, _configuration=None):  # noqa: E501
        """CreateAccountRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_name = None
        self._description = None
        self._inherit_profile = None
        self._org_unit_id = None
        self._show_name = None
        self._verification_relation_id = None
        self.discriminator = None

        self.account_name = account_name
        if description is not None:
            self.description = description
        if inherit_profile is not None:
            self.inherit_profile = inherit_profile
        if org_unit_id is not None:
            self.org_unit_id = org_unit_id
        self.show_name = show_name
        if verification_relation_id is not None:
            self.verification_relation_id = verification_relation_id

    @property
    def account_name(self):
        """Gets the account_name of this CreateAccountRequest.  # noqa: E501


        :return: The account_name of this CreateAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this CreateAccountRequest.


        :param account_name: The account_name of this CreateAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and account_name is None:
            raise ValueError("Invalid value for `account_name`, must not be `None`")  # noqa: E501

        self._account_name = account_name

    @property
    def description(self):
        """Gets the description of this CreateAccountRequest.  # noqa: E501


        :return: The description of this CreateAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateAccountRequest.


        :param description: The description of this CreateAccountRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def inherit_profile(self):
        """Gets the inherit_profile of this CreateAccountRequest.  # noqa: E501


        :return: The inherit_profile of this CreateAccountRequest.  # noqa: E501
        :rtype: bool
        """
        return self._inherit_profile

    @inherit_profile.setter
    def inherit_profile(self, inherit_profile):
        """Sets the inherit_profile of this CreateAccountRequest.


        :param inherit_profile: The inherit_profile of this CreateAccountRequest.  # noqa: E501
        :type: bool
        """

        self._inherit_profile = inherit_profile

    @property
    def org_unit_id(self):
        """Gets the org_unit_id of this CreateAccountRequest.  # noqa: E501


        :return: The org_unit_id of this CreateAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._org_unit_id

    @org_unit_id.setter
    def org_unit_id(self, org_unit_id):
        """Sets the org_unit_id of this CreateAccountRequest.


        :param org_unit_id: The org_unit_id of this CreateAccountRequest.  # noqa: E501
        :type: str
        """

        self._org_unit_id = org_unit_id

    @property
    def show_name(self):
        """Gets the show_name of this CreateAccountRequest.  # noqa: E501


        :return: The show_name of this CreateAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._show_name

    @show_name.setter
    def show_name(self, show_name):
        """Sets the show_name of this CreateAccountRequest.


        :param show_name: The show_name of this CreateAccountRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and show_name is None:
            raise ValueError("Invalid value for `show_name`, must not be `None`")  # noqa: E501

        self._show_name = show_name

    @property
    def verification_relation_id(self):
        """Gets the verification_relation_id of this CreateAccountRequest.  # noqa: E501


        :return: The verification_relation_id of this CreateAccountRequest.  # noqa: E501
        :rtype: str
        """
        return self._verification_relation_id

    @verification_relation_id.setter
    def verification_relation_id(self, verification_relation_id):
        """Sets the verification_relation_id of this CreateAccountRequest.


        :param verification_relation_id: The verification_relation_id of this CreateAccountRequest.  # noqa: E501
        :type: str
        """

        self._verification_relation_id = verification_relation_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateAccountRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateAccountRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateAccountRequest):
            return True

        return self.to_dict() != other.to_dict()
