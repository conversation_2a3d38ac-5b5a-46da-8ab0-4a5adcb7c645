# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeAcceleratorResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'account_id': 'str',
        'bandwidth_package_ids': 'list[str]',
        'billing_spec': 'str',
        'billing_spec_effective_time': 'int',
        'billing_type': 'str',
        'cname': 'str',
        'charge_type': 'str',
        'create_time': 'int',
        'cross_domain_bandwidth_ids': 'list[str]',
        'expired_time': 'int',
        'full_port_switch': 'bool',
        'listener_count': 'int',
        'name': 'str',
        'project_name': 'str',
        'region_count': 'int',
        'renew_type': 'int',
        'resource_tags': 'ResourceTagsForDescribeAcceleratorOutput',
        'state': 'str'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'account_id': 'AccountID',
        'bandwidth_package_ids': 'BandwidthPackageIds',
        'billing_spec': 'BillingSpec',
        'billing_spec_effective_time': 'BillingSpecEffectiveTime',
        'billing_type': 'BillingType',
        'cname': 'CNAME',
        'charge_type': 'ChargeType',
        'create_time': 'CreateTime',
        'cross_domain_bandwidth_ids': 'CrossDomainBandwidthIds',
        'expired_time': 'ExpiredTime',
        'full_port_switch': 'FullPortSwitch',
        'listener_count': 'ListenerCount',
        'name': 'Name',
        'project_name': 'ProjectName',
        'region_count': 'RegionCount',
        'renew_type': 'RenewType',
        'resource_tags': 'ResourceTags',
        'state': 'State'
    }

    def __init__(self, accelerator_id=None, account_id=None, bandwidth_package_ids=None, billing_spec=None, billing_spec_effective_time=None, billing_type=None, cname=None, charge_type=None, create_time=None, cross_domain_bandwidth_ids=None, expired_time=None, full_port_switch=None, listener_count=None, name=None, project_name=None, region_count=None, renew_type=None, resource_tags=None, state=None, _configuration=None):  # noqa: E501
        """DescribeAcceleratorResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._account_id = None
        self._bandwidth_package_ids = None
        self._billing_spec = None
        self._billing_spec_effective_time = None
        self._billing_type = None
        self._cname = None
        self._charge_type = None
        self._create_time = None
        self._cross_domain_bandwidth_ids = None
        self._expired_time = None
        self._full_port_switch = None
        self._listener_count = None
        self._name = None
        self._project_name = None
        self._region_count = None
        self._renew_type = None
        self._resource_tags = None
        self._state = None
        self.discriminator = None

        if accelerator_id is not None:
            self.accelerator_id = accelerator_id
        if account_id is not None:
            self.account_id = account_id
        if bandwidth_package_ids is not None:
            self.bandwidth_package_ids = bandwidth_package_ids
        if billing_spec is not None:
            self.billing_spec = billing_spec
        if billing_spec_effective_time is not None:
            self.billing_spec_effective_time = billing_spec_effective_time
        if billing_type is not None:
            self.billing_type = billing_type
        if cname is not None:
            self.cname = cname
        if charge_type is not None:
            self.charge_type = charge_type
        if create_time is not None:
            self.create_time = create_time
        if cross_domain_bandwidth_ids is not None:
            self.cross_domain_bandwidth_ids = cross_domain_bandwidth_ids
        if expired_time is not None:
            self.expired_time = expired_time
        if full_port_switch is not None:
            self.full_port_switch = full_port_switch
        if listener_count is not None:
            self.listener_count = listener_count
        if name is not None:
            self.name = name
        if project_name is not None:
            self.project_name = project_name
        if region_count is not None:
            self.region_count = region_count
        if renew_type is not None:
            self.renew_type = renew_type
        if resource_tags is not None:
            self.resource_tags = resource_tags
        if state is not None:
            self.state = state

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The accelerator_id of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this DescribeAcceleratorResponse.


        :param accelerator_id: The accelerator_id of this DescribeAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._accelerator_id = accelerator_id

    @property
    def account_id(self):
        """Gets the account_id of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The account_id of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DescribeAcceleratorResponse.


        :param account_id: The account_id of this DescribeAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def bandwidth_package_ids(self):
        """Gets the bandwidth_package_ids of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The bandwidth_package_ids of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._bandwidth_package_ids

    @bandwidth_package_ids.setter
    def bandwidth_package_ids(self, bandwidth_package_ids):
        """Sets the bandwidth_package_ids of this DescribeAcceleratorResponse.


        :param bandwidth_package_ids: The bandwidth_package_ids of this DescribeAcceleratorResponse.  # noqa: E501
        :type: list[str]
        """

        self._bandwidth_package_ids = bandwidth_package_ids

    @property
    def billing_spec(self):
        """Gets the billing_spec of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The billing_spec of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._billing_spec

    @billing_spec.setter
    def billing_spec(self, billing_spec):
        """Sets the billing_spec of this DescribeAcceleratorResponse.


        :param billing_spec: The billing_spec of this DescribeAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._billing_spec = billing_spec

    @property
    def billing_spec_effective_time(self):
        """Gets the billing_spec_effective_time of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The billing_spec_effective_time of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._billing_spec_effective_time

    @billing_spec_effective_time.setter
    def billing_spec_effective_time(self, billing_spec_effective_time):
        """Sets the billing_spec_effective_time of this DescribeAcceleratorResponse.


        :param billing_spec_effective_time: The billing_spec_effective_time of this DescribeAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._billing_spec_effective_time = billing_spec_effective_time

    @property
    def billing_type(self):
        """Gets the billing_type of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The billing_type of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._billing_type

    @billing_type.setter
    def billing_type(self, billing_type):
        """Sets the billing_type of this DescribeAcceleratorResponse.


        :param billing_type: The billing_type of this DescribeAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._billing_type = billing_type

    @property
    def cname(self):
        """Gets the cname of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The cname of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._cname

    @cname.setter
    def cname(self, cname):
        """Sets the cname of this DescribeAcceleratorResponse.


        :param cname: The cname of this DescribeAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._cname = cname

    @property
    def charge_type(self):
        """Gets the charge_type of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The charge_type of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this DescribeAcceleratorResponse.


        :param charge_type: The charge_type of this DescribeAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def create_time(self):
        """Gets the create_time of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The create_time of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DescribeAcceleratorResponse.


        :param create_time: The create_time of this DescribeAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def cross_domain_bandwidth_ids(self):
        """Gets the cross_domain_bandwidth_ids of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The cross_domain_bandwidth_ids of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._cross_domain_bandwidth_ids

    @cross_domain_bandwidth_ids.setter
    def cross_domain_bandwidth_ids(self, cross_domain_bandwidth_ids):
        """Sets the cross_domain_bandwidth_ids of this DescribeAcceleratorResponse.


        :param cross_domain_bandwidth_ids: The cross_domain_bandwidth_ids of this DescribeAcceleratorResponse.  # noqa: E501
        :type: list[str]
        """

        self._cross_domain_bandwidth_ids = cross_domain_bandwidth_ids

    @property
    def expired_time(self):
        """Gets the expired_time of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The expired_time of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._expired_time

    @expired_time.setter
    def expired_time(self, expired_time):
        """Sets the expired_time of this DescribeAcceleratorResponse.


        :param expired_time: The expired_time of this DescribeAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._expired_time = expired_time

    @property
    def full_port_switch(self):
        """Gets the full_port_switch of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The full_port_switch of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: bool
        """
        return self._full_port_switch

    @full_port_switch.setter
    def full_port_switch(self, full_port_switch):
        """Sets the full_port_switch of this DescribeAcceleratorResponse.


        :param full_port_switch: The full_port_switch of this DescribeAcceleratorResponse.  # noqa: E501
        :type: bool
        """

        self._full_port_switch = full_port_switch

    @property
    def listener_count(self):
        """Gets the listener_count of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The listener_count of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._listener_count

    @listener_count.setter
    def listener_count(self, listener_count):
        """Sets the listener_count of this DescribeAcceleratorResponse.


        :param listener_count: The listener_count of this DescribeAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._listener_count = listener_count

    @property
    def name(self):
        """Gets the name of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The name of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DescribeAcceleratorResponse.


        :param name: The name of this DescribeAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def project_name(self):
        """Gets the project_name of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The project_name of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this DescribeAcceleratorResponse.


        :param project_name: The project_name of this DescribeAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def region_count(self):
        """Gets the region_count of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The region_count of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._region_count

    @region_count.setter
    def region_count(self, region_count):
        """Sets the region_count of this DescribeAcceleratorResponse.


        :param region_count: The region_count of this DescribeAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._region_count = region_count

    @property
    def renew_type(self):
        """Gets the renew_type of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The renew_type of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: int
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this DescribeAcceleratorResponse.


        :param renew_type: The renew_type of this DescribeAcceleratorResponse.  # noqa: E501
        :type: int
        """

        self._renew_type = renew_type

    @property
    def resource_tags(self):
        """Gets the resource_tags of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The resource_tags of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: ResourceTagsForDescribeAcceleratorOutput
        """
        return self._resource_tags

    @resource_tags.setter
    def resource_tags(self, resource_tags):
        """Sets the resource_tags of this DescribeAcceleratorResponse.


        :param resource_tags: The resource_tags of this DescribeAcceleratorResponse.  # noqa: E501
        :type: ResourceTagsForDescribeAcceleratorOutput
        """

        self._resource_tags = resource_tags

    @property
    def state(self):
        """Gets the state of this DescribeAcceleratorResponse.  # noqa: E501


        :return: The state of this DescribeAcceleratorResponse.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this DescribeAcceleratorResponse.


        :param state: The state of this DescribeAcceleratorResponse.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeAcceleratorResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeAcceleratorResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeAcceleratorResponse):
            return True

        return self.to_dict() != other.to_dict()
