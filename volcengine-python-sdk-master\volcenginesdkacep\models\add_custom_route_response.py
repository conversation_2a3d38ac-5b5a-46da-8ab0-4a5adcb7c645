# coding: utf-8

"""
    acep

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AddCustomRouteResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'custom_route_id': 'str',
        'dst_ip': 'str'
    }

    attribute_map = {
        'custom_route_id': 'CustomRouteId',
        'dst_ip': 'DstIP'
    }

    def __init__(self, custom_route_id=None, dst_ip=None, _configuration=None):  # noqa: E501
        """AddCustomRouteResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._custom_route_id = None
        self._dst_ip = None
        self.discriminator = None

        if custom_route_id is not None:
            self.custom_route_id = custom_route_id
        if dst_ip is not None:
            self.dst_ip = dst_ip

    @property
    def custom_route_id(self):
        """Gets the custom_route_id of this AddCustomRouteResponse.  # noqa: E501


        :return: The custom_route_id of this AddCustomRouteResponse.  # noqa: E501
        :rtype: str
        """
        return self._custom_route_id

    @custom_route_id.setter
    def custom_route_id(self, custom_route_id):
        """Sets the custom_route_id of this AddCustomRouteResponse.


        :param custom_route_id: The custom_route_id of this AddCustomRouteResponse.  # noqa: E501
        :type: str
        """

        self._custom_route_id = custom_route_id

    @property
    def dst_ip(self):
        """Gets the dst_ip of this AddCustomRouteResponse.  # noqa: E501


        :return: The dst_ip of this AddCustomRouteResponse.  # noqa: E501
        :rtype: str
        """
        return self._dst_ip

    @dst_ip.setter
    def dst_ip(self, dst_ip):
        """Sets the dst_ip of this AddCustomRouteResponse.


        :param dst_ip: The dst_ip of this AddCustomRouteResponse.  # noqa: E501
        :type: str
        """

        self._dst_ip = dst_ip

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AddCustomRouteResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AddCustomRouteResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AddCustomRouteResponse):
            return True

        return self.to_dict() != other.to_dict()
