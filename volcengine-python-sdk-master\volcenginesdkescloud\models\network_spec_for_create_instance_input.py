# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NetworkSpecForCreateInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bandwidth': 'int',
        'is_open': 'bool',
        'spec_name': 'str',
        'type': 'str'
    }

    attribute_map = {
        'bandwidth': 'Bandwidth',
        'is_open': 'IsOpen',
        'spec_name': 'SpecName',
        'type': 'Type'
    }

    def __init__(self, bandwidth=None, is_open=None, spec_name=None, type=None, _configuration=None):  # noqa: E501
        """NetworkSpecForCreateInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bandwidth = None
        self._is_open = None
        self._spec_name = None
        self._type = None
        self.discriminator = None

        if bandwidth is not None:
            self.bandwidth = bandwidth
        if is_open is not None:
            self.is_open = is_open
        if spec_name is not None:
            self.spec_name = spec_name
        if type is not None:
            self.type = type

    @property
    def bandwidth(self):
        """Gets the bandwidth of this NetworkSpecForCreateInstanceInput.  # noqa: E501


        :return: The bandwidth of this NetworkSpecForCreateInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._bandwidth

    @bandwidth.setter
    def bandwidth(self, bandwidth):
        """Sets the bandwidth of this NetworkSpecForCreateInstanceInput.


        :param bandwidth: The bandwidth of this NetworkSpecForCreateInstanceInput.  # noqa: E501
        :type: int
        """

        self._bandwidth = bandwidth

    @property
    def is_open(self):
        """Gets the is_open of this NetworkSpecForCreateInstanceInput.  # noqa: E501


        :return: The is_open of this NetworkSpecForCreateInstanceInput.  # noqa: E501
        :rtype: bool
        """
        return self._is_open

    @is_open.setter
    def is_open(self, is_open):
        """Sets the is_open of this NetworkSpecForCreateInstanceInput.


        :param is_open: The is_open of this NetworkSpecForCreateInstanceInput.  # noqa: E501
        :type: bool
        """

        self._is_open = is_open

    @property
    def spec_name(self):
        """Gets the spec_name of this NetworkSpecForCreateInstanceInput.  # noqa: E501


        :return: The spec_name of this NetworkSpecForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._spec_name

    @spec_name.setter
    def spec_name(self, spec_name):
        """Sets the spec_name of this NetworkSpecForCreateInstanceInput.


        :param spec_name: The spec_name of this NetworkSpecForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._spec_name = spec_name

    @property
    def type(self):
        """Gets the type of this NetworkSpecForCreateInstanceInput.  # noqa: E501


        :return: The type of this NetworkSpecForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this NetworkSpecForCreateInstanceInput.


        :param type: The type of this NetworkSpecForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NetworkSpecForCreateInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NetworkSpecForCreateInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NetworkSpecForCreateInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
