# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListAssetClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_desc': 'str',
        'create_time': 'int',
        'external_cluster_id': 'str',
        'has_audit_risk': 'bool',
        'has_cve_risk': 'bool',
        'id': 'str',
        'kubernetes_version': 'str',
        'name': 'str',
        'node_count': 'int',
        'region': 'str',
        'region_name': 'str',
        'running_node_count': 'int',
        'status': 'str',
        'sync_time': 'int',
        'type': 'str'
    }

    attribute_map = {
        'cluster_desc': 'ClusterDesc',
        'create_time': 'CreateTime',
        'external_cluster_id': 'ExternalClusterId',
        'has_audit_risk': 'HasAuditRisk',
        'has_cve_risk': 'HasCveRisk',
        'id': 'Id',
        'kubernetes_version': 'KubernetesVersion',
        'name': 'Name',
        'node_count': 'NodeCount',
        'region': 'Region',
        'region_name': 'RegionName',
        'running_node_count': 'RunningNodeCount',
        'status': 'Status',
        'sync_time': 'SyncTime',
        'type': 'Type'
    }

    def __init__(self, cluster_desc=None, create_time=None, external_cluster_id=None, has_audit_risk=None, has_cve_risk=None, id=None, kubernetes_version=None, name=None, node_count=None, region=None, region_name=None, running_node_count=None, status=None, sync_time=None, type=None, _configuration=None):  # noqa: E501
        """DataForListAssetClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_desc = None
        self._create_time = None
        self._external_cluster_id = None
        self._has_audit_risk = None
        self._has_cve_risk = None
        self._id = None
        self._kubernetes_version = None
        self._name = None
        self._node_count = None
        self._region = None
        self._region_name = None
        self._running_node_count = None
        self._status = None
        self._sync_time = None
        self._type = None
        self.discriminator = None

        if cluster_desc is not None:
            self.cluster_desc = cluster_desc
        if create_time is not None:
            self.create_time = create_time
        if external_cluster_id is not None:
            self.external_cluster_id = external_cluster_id
        if has_audit_risk is not None:
            self.has_audit_risk = has_audit_risk
        if has_cve_risk is not None:
            self.has_cve_risk = has_cve_risk
        if id is not None:
            self.id = id
        if kubernetes_version is not None:
            self.kubernetes_version = kubernetes_version
        if name is not None:
            self.name = name
        if node_count is not None:
            self.node_count = node_count
        if region is not None:
            self.region = region
        if region_name is not None:
            self.region_name = region_name
        if running_node_count is not None:
            self.running_node_count = running_node_count
        if status is not None:
            self.status = status
        if sync_time is not None:
            self.sync_time = sync_time
        if type is not None:
            self.type = type

    @property
    def cluster_desc(self):
        """Gets the cluster_desc of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The cluster_desc of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_desc

    @cluster_desc.setter
    def cluster_desc(self, cluster_desc):
        """Sets the cluster_desc of this DataForListAssetClustersOutput.


        :param cluster_desc: The cluster_desc of this DataForListAssetClustersOutput.  # noqa: E501
        :type: str
        """

        self._cluster_desc = cluster_desc

    @property
    def create_time(self):
        """Gets the create_time of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The create_time of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataForListAssetClustersOutput.


        :param create_time: The create_time of this DataForListAssetClustersOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def external_cluster_id(self):
        """Gets the external_cluster_id of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The external_cluster_id of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_cluster_id

    @external_cluster_id.setter
    def external_cluster_id(self, external_cluster_id):
        """Sets the external_cluster_id of this DataForListAssetClustersOutput.


        :param external_cluster_id: The external_cluster_id of this DataForListAssetClustersOutput.  # noqa: E501
        :type: str
        """

        self._external_cluster_id = external_cluster_id

    @property
    def has_audit_risk(self):
        """Gets the has_audit_risk of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The has_audit_risk of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._has_audit_risk

    @has_audit_risk.setter
    def has_audit_risk(self, has_audit_risk):
        """Sets the has_audit_risk of this DataForListAssetClustersOutput.


        :param has_audit_risk: The has_audit_risk of this DataForListAssetClustersOutput.  # noqa: E501
        :type: bool
        """

        self._has_audit_risk = has_audit_risk

    @property
    def has_cve_risk(self):
        """Gets the has_cve_risk of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The has_cve_risk of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._has_cve_risk

    @has_cve_risk.setter
    def has_cve_risk(self, has_cve_risk):
        """Sets the has_cve_risk of this DataForListAssetClustersOutput.


        :param has_cve_risk: The has_cve_risk of this DataForListAssetClustersOutput.  # noqa: E501
        :type: bool
        """

        self._has_cve_risk = has_cve_risk

    @property
    def id(self):
        """Gets the id of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The id of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataForListAssetClustersOutput.


        :param id: The id of this DataForListAssetClustersOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def kubernetes_version(self):
        """Gets the kubernetes_version of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The kubernetes_version of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._kubernetes_version

    @kubernetes_version.setter
    def kubernetes_version(self, kubernetes_version):
        """Sets the kubernetes_version of this DataForListAssetClustersOutput.


        :param kubernetes_version: The kubernetes_version of this DataForListAssetClustersOutput.  # noqa: E501
        :type: str
        """

        self._kubernetes_version = kubernetes_version

    @property
    def name(self):
        """Gets the name of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The name of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForListAssetClustersOutput.


        :param name: The name of this DataForListAssetClustersOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def node_count(self):
        """Gets the node_count of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The node_count of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._node_count

    @node_count.setter
    def node_count(self, node_count):
        """Sets the node_count of this DataForListAssetClustersOutput.


        :param node_count: The node_count of this DataForListAssetClustersOutput.  # noqa: E501
        :type: int
        """

        self._node_count = node_count

    @property
    def region(self):
        """Gets the region of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The region of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this DataForListAssetClustersOutput.


        :param region: The region of this DataForListAssetClustersOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def region_name(self):
        """Gets the region_name of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The region_name of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_name

    @region_name.setter
    def region_name(self, region_name):
        """Sets the region_name of this DataForListAssetClustersOutput.


        :param region_name: The region_name of this DataForListAssetClustersOutput.  # noqa: E501
        :type: str
        """

        self._region_name = region_name

    @property
    def running_node_count(self):
        """Gets the running_node_count of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The running_node_count of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._running_node_count

    @running_node_count.setter
    def running_node_count(self, running_node_count):
        """Sets the running_node_count of this DataForListAssetClustersOutput.


        :param running_node_count: The running_node_count of this DataForListAssetClustersOutput.  # noqa: E501
        :type: int
        """

        self._running_node_count = running_node_count

    @property
    def status(self):
        """Gets the status of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The status of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataForListAssetClustersOutput.


        :param status: The status of this DataForListAssetClustersOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def sync_time(self):
        """Gets the sync_time of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The sync_time of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: int
        """
        return self._sync_time

    @sync_time.setter
    def sync_time(self, sync_time):
        """Sets the sync_time of this DataForListAssetClustersOutput.


        :param sync_time: The sync_time of this DataForListAssetClustersOutput.  # noqa: E501
        :type: int
        """

        self._sync_time = sync_time

    @property
    def type(self):
        """Gets the type of this DataForListAssetClustersOutput.  # noqa: E501


        :return: The type of this DataForListAssetClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DataForListAssetClustersOutput.


        :param type: The type of this DataForListAssetClustersOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListAssetClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListAssetClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListAssetClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
