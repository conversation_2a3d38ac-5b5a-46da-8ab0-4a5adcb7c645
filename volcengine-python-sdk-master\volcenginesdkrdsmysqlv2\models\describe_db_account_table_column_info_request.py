# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDbAccountTableColumnInfoRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_name': 'str',
        'column_name': 'str',
        'db_name': 'str',
        'host': 'str',
        'instance_id': 'str',
        'table_limit': 'int',
        'table_name': 'str'
    }

    attribute_map = {
        'account_name': 'AccountName',
        'column_name': 'ColumnName',
        'db_name': 'DBN<PERSON>',
        'host': 'Host',
        'instance_id': 'InstanceId',
        'table_limit': 'TableLimit',
        'table_name': 'TableName'
    }

    def __init__(self, account_name=None, column_name=None, db_name=None, host=None, instance_id=None, table_limit=None, table_name=None, _configuration=None):  # noqa: E501
        """DescribeDbAccountTableColumnInfoRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_name = None
        self._column_name = None
        self._db_name = None
        self._host = None
        self._instance_id = None
        self._table_limit = None
        self._table_name = None
        self.discriminator = None

        if account_name is not None:
            self.account_name = account_name
        if column_name is not None:
            self.column_name = column_name
        self.db_name = db_name
        if host is not None:
            self.host = host
        self.instance_id = instance_id
        if table_limit is not None:
            self.table_limit = table_limit
        if table_name is not None:
            self.table_name = table_name

    @property
    def account_name(self):
        """Gets the account_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501


        :return: The account_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this DescribeDbAccountTableColumnInfoRequest.


        :param account_name: The account_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :type: str
        """

        self._account_name = account_name

    @property
    def column_name(self):
        """Gets the column_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501


        :return: The column_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._column_name

    @column_name.setter
    def column_name(self, column_name):
        """Sets the column_name of this DescribeDbAccountTableColumnInfoRequest.


        :param column_name: The column_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :type: str
        """

        self._column_name = column_name

    @property
    def db_name(self):
        """Gets the db_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501


        :return: The db_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._db_name

    @db_name.setter
    def db_name(self, db_name):
        """Sets the db_name of this DescribeDbAccountTableColumnInfoRequest.


        :param db_name: The db_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and db_name is None:
            raise ValueError("Invalid value for `db_name`, must not be `None`")  # noqa: E501

        self._db_name = db_name

    @property
    def host(self):
        """Gets the host of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501


        :return: The host of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this DescribeDbAccountTableColumnInfoRequest.


        :param host: The host of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :type: str
        """

        self._host = host

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501


        :return: The instance_id of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeDbAccountTableColumnInfoRequest.


        :param instance_id: The instance_id of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def table_limit(self):
        """Gets the table_limit of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501


        :return: The table_limit of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :rtype: int
        """
        return self._table_limit

    @table_limit.setter
    def table_limit(self, table_limit):
        """Sets the table_limit of this DescribeDbAccountTableColumnInfoRequest.


        :param table_limit: The table_limit of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :type: int
        """

        self._table_limit = table_limit

    @property
    def table_name(self):
        """Gets the table_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501


        :return: The table_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :rtype: str
        """
        return self._table_name

    @table_name.setter
    def table_name(self, table_name):
        """Sets the table_name of this DescribeDbAccountTableColumnInfoRequest.


        :param table_name: The table_name of this DescribeDbAccountTableColumnInfoRequest.  # noqa: E501
        :type: str
        """

        self._table_name = table_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDbAccountTableColumnInfoRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDbAccountTableColumnInfoRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDbAccountTableColumnInfoRequest):
            return True

        return self.to_dict() != other.to_dict()
