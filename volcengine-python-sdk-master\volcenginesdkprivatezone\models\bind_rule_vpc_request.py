# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BindRuleVPCRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'rule_id': 'int',
        'rule_trn': 'str',
        'vpc_trns': 'list[str]',
        'vpcs': 'list[VpcForBindRuleVPCInput]'
    }

    attribute_map = {
        'rule_id': 'RuleID',
        'rule_trn': 'RuleTrn',
        'vpc_trns': 'VpcTrns',
        'vpcs': 'Vpcs'
    }

    def __init__(self, rule_id=None, rule_trn=None, vpc_trns=None, vpcs=None, _configuration=None):  # noqa: E501
        """BindRuleVPCRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._rule_id = None
        self._rule_trn = None
        self._vpc_trns = None
        self._vpcs = None
        self.discriminator = None

        self.rule_id = rule_id
        if rule_trn is not None:
            self.rule_trn = rule_trn
        if vpc_trns is not None:
            self.vpc_trns = vpc_trns
        if vpcs is not None:
            self.vpcs = vpcs

    @property
    def rule_id(self):
        """Gets the rule_id of this BindRuleVPCRequest.  # noqa: E501


        :return: The rule_id of this BindRuleVPCRequest.  # noqa: E501
        :rtype: int
        """
        return self._rule_id

    @rule_id.setter
    def rule_id(self, rule_id):
        """Sets the rule_id of this BindRuleVPCRequest.


        :param rule_id: The rule_id of this BindRuleVPCRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and rule_id is None:
            raise ValueError("Invalid value for `rule_id`, must not be `None`")  # noqa: E501

        self._rule_id = rule_id

    @property
    def rule_trn(self):
        """Gets the rule_trn of this BindRuleVPCRequest.  # noqa: E501


        :return: The rule_trn of this BindRuleVPCRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_trn

    @rule_trn.setter
    def rule_trn(self, rule_trn):
        """Sets the rule_trn of this BindRuleVPCRequest.


        :param rule_trn: The rule_trn of this BindRuleVPCRequest.  # noqa: E501
        :type: str
        """

        self._rule_trn = rule_trn

    @property
    def vpc_trns(self):
        """Gets the vpc_trns of this BindRuleVPCRequest.  # noqa: E501


        :return: The vpc_trns of this BindRuleVPCRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_trns

    @vpc_trns.setter
    def vpc_trns(self, vpc_trns):
        """Sets the vpc_trns of this BindRuleVPCRequest.


        :param vpc_trns: The vpc_trns of this BindRuleVPCRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpc_trns = vpc_trns

    @property
    def vpcs(self):
        """Gets the vpcs of this BindRuleVPCRequest.  # noqa: E501


        :return: The vpcs of this BindRuleVPCRequest.  # noqa: E501
        :rtype: list[VpcForBindRuleVPCInput]
        """
        return self._vpcs

    @vpcs.setter
    def vpcs(self, vpcs):
        """Sets the vpcs of this BindRuleVPCRequest.


        :param vpcs: The vpcs of this BindRuleVPCRequest.  # noqa: E501
        :type: list[VpcForBindRuleVPCInput]
        """

        self._vpcs = vpcs

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BindRuleVPCRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BindRuleVPCRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BindRuleVPCRequest):
            return True

        return self.to_dict() != other.to_dict()
