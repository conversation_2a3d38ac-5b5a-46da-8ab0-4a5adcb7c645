# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ExternalConfigForListClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'execution_root_dir': 'str',
        'filesystem': 'str',
        'jupyterhub_cluster_kube_config': 'str',
        'jupyterhub_endpoint': 'str',
        'jupyterhub_jwt_secret': 'str',
        'jupyterhub_user_namespace': 'str',
        'nereid_cluster_id': 'str',
        'nereid_endpoint': 'str',
        'resource_scheduler': 'str',
        's3_proxy_config': 'S3ProxyConfigForListClustersOutput',
        'wes_endpoint': 'str'
    }

    attribute_map = {
        'execution_root_dir': 'ExecutionRootDir',
        'filesystem': 'Filesystem',
        'jupyterhub_cluster_kube_config': 'JupyterhubClusterKubeConfig',
        'jupyterhub_endpoint': 'JupyterhubEndpoint',
        'jupyterhub_jwt_secret': 'JupyterhubJWTSecret',
        'jupyterhub_user_namespace': 'JupyterhubUserNamespace',
        'nereid_cluster_id': 'NereidClusterID',
        'nereid_endpoint': 'NereidEndpoint',
        'resource_scheduler': 'ResourceScheduler',
        's3_proxy_config': 'S3ProxyConfig',
        'wes_endpoint': 'WESEndpoint'
    }

    def __init__(self, execution_root_dir=None, filesystem=None, jupyterhub_cluster_kube_config=None, jupyterhub_endpoint=None, jupyterhub_jwt_secret=None, jupyterhub_user_namespace=None, nereid_cluster_id=None, nereid_endpoint=None, resource_scheduler=None, s3_proxy_config=None, wes_endpoint=None, _configuration=None):  # noqa: E501
        """ExternalConfigForListClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._execution_root_dir = None
        self._filesystem = None
        self._jupyterhub_cluster_kube_config = None
        self._jupyterhub_endpoint = None
        self._jupyterhub_jwt_secret = None
        self._jupyterhub_user_namespace = None
        self._nereid_cluster_id = None
        self._nereid_endpoint = None
        self._resource_scheduler = None
        self._s3_proxy_config = None
        self._wes_endpoint = None
        self.discriminator = None

        if execution_root_dir is not None:
            self.execution_root_dir = execution_root_dir
        if filesystem is not None:
            self.filesystem = filesystem
        if jupyterhub_cluster_kube_config is not None:
            self.jupyterhub_cluster_kube_config = jupyterhub_cluster_kube_config
        if jupyterhub_endpoint is not None:
            self.jupyterhub_endpoint = jupyterhub_endpoint
        if jupyterhub_jwt_secret is not None:
            self.jupyterhub_jwt_secret = jupyterhub_jwt_secret
        if jupyterhub_user_namespace is not None:
            self.jupyterhub_user_namespace = jupyterhub_user_namespace
        if nereid_cluster_id is not None:
            self.nereid_cluster_id = nereid_cluster_id
        if nereid_endpoint is not None:
            self.nereid_endpoint = nereid_endpoint
        if resource_scheduler is not None:
            self.resource_scheduler = resource_scheduler
        if s3_proxy_config is not None:
            self.s3_proxy_config = s3_proxy_config
        if wes_endpoint is not None:
            self.wes_endpoint = wes_endpoint

    @property
    def execution_root_dir(self):
        """Gets the execution_root_dir of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The execution_root_dir of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._execution_root_dir

    @execution_root_dir.setter
    def execution_root_dir(self, execution_root_dir):
        """Sets the execution_root_dir of this ExternalConfigForListClustersOutput.


        :param execution_root_dir: The execution_root_dir of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._execution_root_dir = execution_root_dir

    @property
    def filesystem(self):
        """Gets the filesystem of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The filesystem of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._filesystem

    @filesystem.setter
    def filesystem(self, filesystem):
        """Sets the filesystem of this ExternalConfigForListClustersOutput.


        :param filesystem: The filesystem of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._filesystem = filesystem

    @property
    def jupyterhub_cluster_kube_config(self):
        """Gets the jupyterhub_cluster_kube_config of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The jupyterhub_cluster_kube_config of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._jupyterhub_cluster_kube_config

    @jupyterhub_cluster_kube_config.setter
    def jupyterhub_cluster_kube_config(self, jupyterhub_cluster_kube_config):
        """Sets the jupyterhub_cluster_kube_config of this ExternalConfigForListClustersOutput.


        :param jupyterhub_cluster_kube_config: The jupyterhub_cluster_kube_config of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._jupyterhub_cluster_kube_config = jupyterhub_cluster_kube_config

    @property
    def jupyterhub_endpoint(self):
        """Gets the jupyterhub_endpoint of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The jupyterhub_endpoint of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._jupyterhub_endpoint

    @jupyterhub_endpoint.setter
    def jupyterhub_endpoint(self, jupyterhub_endpoint):
        """Sets the jupyterhub_endpoint of this ExternalConfigForListClustersOutput.


        :param jupyterhub_endpoint: The jupyterhub_endpoint of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._jupyterhub_endpoint = jupyterhub_endpoint

    @property
    def jupyterhub_jwt_secret(self):
        """Gets the jupyterhub_jwt_secret of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The jupyterhub_jwt_secret of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._jupyterhub_jwt_secret

    @jupyterhub_jwt_secret.setter
    def jupyterhub_jwt_secret(self, jupyterhub_jwt_secret):
        """Sets the jupyterhub_jwt_secret of this ExternalConfigForListClustersOutput.


        :param jupyterhub_jwt_secret: The jupyterhub_jwt_secret of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._jupyterhub_jwt_secret = jupyterhub_jwt_secret

    @property
    def jupyterhub_user_namespace(self):
        """Gets the jupyterhub_user_namespace of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The jupyterhub_user_namespace of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._jupyterhub_user_namespace

    @jupyterhub_user_namespace.setter
    def jupyterhub_user_namespace(self, jupyterhub_user_namespace):
        """Sets the jupyterhub_user_namespace of this ExternalConfigForListClustersOutput.


        :param jupyterhub_user_namespace: The jupyterhub_user_namespace of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._jupyterhub_user_namespace = jupyterhub_user_namespace

    @property
    def nereid_cluster_id(self):
        """Gets the nereid_cluster_id of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The nereid_cluster_id of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._nereid_cluster_id

    @nereid_cluster_id.setter
    def nereid_cluster_id(self, nereid_cluster_id):
        """Sets the nereid_cluster_id of this ExternalConfigForListClustersOutput.


        :param nereid_cluster_id: The nereid_cluster_id of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._nereid_cluster_id = nereid_cluster_id

    @property
    def nereid_endpoint(self):
        """Gets the nereid_endpoint of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The nereid_endpoint of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._nereid_endpoint

    @nereid_endpoint.setter
    def nereid_endpoint(self, nereid_endpoint):
        """Sets the nereid_endpoint of this ExternalConfigForListClustersOutput.


        :param nereid_endpoint: The nereid_endpoint of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._nereid_endpoint = nereid_endpoint

    @property
    def resource_scheduler(self):
        """Gets the resource_scheduler of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The resource_scheduler of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._resource_scheduler

    @resource_scheduler.setter
    def resource_scheduler(self, resource_scheduler):
        """Sets the resource_scheduler of this ExternalConfigForListClustersOutput.


        :param resource_scheduler: The resource_scheduler of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._resource_scheduler = resource_scheduler

    @property
    def s3_proxy_config(self):
        """Gets the s3_proxy_config of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The s3_proxy_config of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: S3ProxyConfigForListClustersOutput
        """
        return self._s3_proxy_config

    @s3_proxy_config.setter
    def s3_proxy_config(self, s3_proxy_config):
        """Sets the s3_proxy_config of this ExternalConfigForListClustersOutput.


        :param s3_proxy_config: The s3_proxy_config of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: S3ProxyConfigForListClustersOutput
        """

        self._s3_proxy_config = s3_proxy_config

    @property
    def wes_endpoint(self):
        """Gets the wes_endpoint of this ExternalConfigForListClustersOutput.  # noqa: E501


        :return: The wes_endpoint of this ExternalConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._wes_endpoint

    @wes_endpoint.setter
    def wes_endpoint(self, wes_endpoint):
        """Sets the wes_endpoint of this ExternalConfigForListClustersOutput.


        :param wes_endpoint: The wes_endpoint of this ExternalConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._wes_endpoint = wes_endpoint

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ExternalConfigForListClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ExternalConfigForListClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ExternalConfigForListClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
