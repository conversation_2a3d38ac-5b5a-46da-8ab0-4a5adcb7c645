# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateEDXBandwidthPkgRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'area': 'str',
        'billing_mode': 'str',
        'edx_instance_id': 'str',
        'name': 'str',
        'paid_mode': 'str',
        'total_bandwidth': 'int'
    }

    attribute_map = {
        'area': 'Area',
        'billing_mode': 'BillingMode',
        'edx_instance_id': 'EDXInstanceId',
        'name': 'Name',
        'paid_mode': 'PaidMode',
        'total_bandwidth': 'TotalBandwidth'
    }

    def __init__(self, area=None, billing_mode=None, edx_instance_id=None, name=None, paid_mode=None, total_bandwidth=None, _configuration=None):  # noqa: E501
        """CreateEDXBandwidthPkgRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._area = None
        self._billing_mode = None
        self._edx_instance_id = None
        self._name = None
        self._paid_mode = None
        self._total_bandwidth = None
        self.discriminator = None

        self.area = area
        self.billing_mode = billing_mode
        self.edx_instance_id = edx_instance_id
        self.name = name
        self.paid_mode = paid_mode
        self.total_bandwidth = total_bandwidth

    @property
    def area(self):
        """Gets the area of this CreateEDXBandwidthPkgRequest.  # noqa: E501


        :return: The area of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._area

    @area.setter
    def area(self, area):
        """Sets the area of this CreateEDXBandwidthPkgRequest.


        :param area: The area of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and area is None:
            raise ValueError("Invalid value for `area`, must not be `None`")  # noqa: E501

        self._area = area

    @property
    def billing_mode(self):
        """Gets the billing_mode of this CreateEDXBandwidthPkgRequest.  # noqa: E501


        :return: The billing_mode of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._billing_mode

    @billing_mode.setter
    def billing_mode(self, billing_mode):
        """Sets the billing_mode of this CreateEDXBandwidthPkgRequest.


        :param billing_mode: The billing_mode of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and billing_mode is None:
            raise ValueError("Invalid value for `billing_mode`, must not be `None`")  # noqa: E501

        self._billing_mode = billing_mode

    @property
    def edx_instance_id(self):
        """Gets the edx_instance_id of this CreateEDXBandwidthPkgRequest.  # noqa: E501


        :return: The edx_instance_id of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._edx_instance_id

    @edx_instance_id.setter
    def edx_instance_id(self, edx_instance_id):
        """Sets the edx_instance_id of this CreateEDXBandwidthPkgRequest.


        :param edx_instance_id: The edx_instance_id of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and edx_instance_id is None:
            raise ValueError("Invalid value for `edx_instance_id`, must not be `None`")  # noqa: E501

        self._edx_instance_id = edx_instance_id

    @property
    def name(self):
        """Gets the name of this CreateEDXBandwidthPkgRequest.  # noqa: E501


        :return: The name of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateEDXBandwidthPkgRequest.


        :param name: The name of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def paid_mode(self):
        """Gets the paid_mode of this CreateEDXBandwidthPkgRequest.  # noqa: E501


        :return: The paid_mode of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: str
        """
        return self._paid_mode

    @paid_mode.setter
    def paid_mode(self, paid_mode):
        """Sets the paid_mode of this CreateEDXBandwidthPkgRequest.


        :param paid_mode: The paid_mode of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and paid_mode is None:
            raise ValueError("Invalid value for `paid_mode`, must not be `None`")  # noqa: E501

        self._paid_mode = paid_mode

    @property
    def total_bandwidth(self):
        """Gets the total_bandwidth of this CreateEDXBandwidthPkgRequest.  # noqa: E501


        :return: The total_bandwidth of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :rtype: int
        """
        return self._total_bandwidth

    @total_bandwidth.setter
    def total_bandwidth(self, total_bandwidth):
        """Sets the total_bandwidth of this CreateEDXBandwidthPkgRequest.


        :param total_bandwidth: The total_bandwidth of this CreateEDXBandwidthPkgRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and total_bandwidth is None:
            raise ValueError("Invalid value for `total_bandwidth`, must not be `None`")  # noqa: E501

        self._total_bandwidth = total_bandwidth

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateEDXBandwidthPkgRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateEDXBandwidthPkgRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateEDXBandwidthPkgRequest):
            return True

        return self.to_dict() != other.to_dict()
