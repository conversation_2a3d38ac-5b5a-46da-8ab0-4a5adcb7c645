# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AwardConfigForListAwardConfigsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'activity_id': 'int',
        'award_condition': 'AwardConditionForListAwardConfigsOutput',
        'award_item_info': 'list[AwardItemInfoForListAwardConfigsOutput]',
        'award_num': 'int',
        'award_send_time': 'int',
        'award_send_type': 'int',
        'award_theme': 'str',
        'award_type': 'int',
        'barrage_pwd': 'str',
        'dead_line': 'int',
        'dead_line_second': 'int',
        'id': 'int',
        'is_hide_award_num': 'int',
        'msg_ids': 'str',
        'name': 'str',
        'open_award_time': 'int',
        'participant_count': 'int',
        'range_status': 'int',
        'send_time': 'int',
        'show_people_number': 'int',
        'show_winner_comment': 'int',
        'status': 'int',
        'winner_count': 'int'
    }

    attribute_map = {
        'activity_id': 'ActivityId',
        'award_condition': 'AwardCondition',
        'award_item_info': 'AwardItemInfo',
        'award_num': 'AwardNum',
        'award_send_time': 'AwardSendTime',
        'award_send_type': 'AwardSendType',
        'award_theme': 'AwardTheme',
        'award_type': 'AwardType',
        'barrage_pwd': 'BarragePwd',
        'dead_line': 'DeadLine',
        'dead_line_second': 'DeadLineSecond',
        'id': 'Id',
        'is_hide_award_num': 'IsHideAwardNum',
        'msg_ids': 'MsgIds',
        'name': 'Name',
        'open_award_time': 'OpenAwardTime',
        'participant_count': 'ParticipantCount',
        'range_status': 'RangeStatus',
        'send_time': 'SendTime',
        'show_people_number': 'ShowPeopleNumber',
        'show_winner_comment': 'ShowWinnerComment',
        'status': 'Status',
        'winner_count': 'WinnerCount'
    }

    def __init__(self, activity_id=None, award_condition=None, award_item_info=None, award_num=None, award_send_time=None, award_send_type=None, award_theme=None, award_type=None, barrage_pwd=None, dead_line=None, dead_line_second=None, id=None, is_hide_award_num=None, msg_ids=None, name=None, open_award_time=None, participant_count=None, range_status=None, send_time=None, show_people_number=None, show_winner_comment=None, status=None, winner_count=None, _configuration=None):  # noqa: E501
        """AwardConfigForListAwardConfigsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._activity_id = None
        self._award_condition = None
        self._award_item_info = None
        self._award_num = None
        self._award_send_time = None
        self._award_send_type = None
        self._award_theme = None
        self._award_type = None
        self._barrage_pwd = None
        self._dead_line = None
        self._dead_line_second = None
        self._id = None
        self._is_hide_award_num = None
        self._msg_ids = None
        self._name = None
        self._open_award_time = None
        self._participant_count = None
        self._range_status = None
        self._send_time = None
        self._show_people_number = None
        self._show_winner_comment = None
        self._status = None
        self._winner_count = None
        self.discriminator = None

        if activity_id is not None:
            self.activity_id = activity_id
        if award_condition is not None:
            self.award_condition = award_condition
        if award_item_info is not None:
            self.award_item_info = award_item_info
        if award_num is not None:
            self.award_num = award_num
        if award_send_time is not None:
            self.award_send_time = award_send_time
        if award_send_type is not None:
            self.award_send_type = award_send_type
        if award_theme is not None:
            self.award_theme = award_theme
        if award_type is not None:
            self.award_type = award_type
        if barrage_pwd is not None:
            self.barrage_pwd = barrage_pwd
        if dead_line is not None:
            self.dead_line = dead_line
        if dead_line_second is not None:
            self.dead_line_second = dead_line_second
        if id is not None:
            self.id = id
        if is_hide_award_num is not None:
            self.is_hide_award_num = is_hide_award_num
        if msg_ids is not None:
            self.msg_ids = msg_ids
        if name is not None:
            self.name = name
        if open_award_time is not None:
            self.open_award_time = open_award_time
        if participant_count is not None:
            self.participant_count = participant_count
        if range_status is not None:
            self.range_status = range_status
        if send_time is not None:
            self.send_time = send_time
        if show_people_number is not None:
            self.show_people_number = show_people_number
        if show_winner_comment is not None:
            self.show_winner_comment = show_winner_comment
        if status is not None:
            self.status = status
        if winner_count is not None:
            self.winner_count = winner_count

    @property
    def activity_id(self):
        """Gets the activity_id of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The activity_id of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._activity_id

    @activity_id.setter
    def activity_id(self, activity_id):
        """Sets the activity_id of this AwardConfigForListAwardConfigsOutput.


        :param activity_id: The activity_id of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._activity_id = activity_id

    @property
    def award_condition(self):
        """Gets the award_condition of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The award_condition of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: AwardConditionForListAwardConfigsOutput
        """
        return self._award_condition

    @award_condition.setter
    def award_condition(self, award_condition):
        """Sets the award_condition of this AwardConfigForListAwardConfigsOutput.


        :param award_condition: The award_condition of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: AwardConditionForListAwardConfigsOutput
        """

        self._award_condition = award_condition

    @property
    def award_item_info(self):
        """Gets the award_item_info of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The award_item_info of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: list[AwardItemInfoForListAwardConfigsOutput]
        """
        return self._award_item_info

    @award_item_info.setter
    def award_item_info(self, award_item_info):
        """Sets the award_item_info of this AwardConfigForListAwardConfigsOutput.


        :param award_item_info: The award_item_info of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: list[AwardItemInfoForListAwardConfigsOutput]
        """

        self._award_item_info = award_item_info

    @property
    def award_num(self):
        """Gets the award_num of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The award_num of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_num

    @award_num.setter
    def award_num(self, award_num):
        """Sets the award_num of this AwardConfigForListAwardConfigsOutput.


        :param award_num: The award_num of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._award_num = award_num

    @property
    def award_send_time(self):
        """Gets the award_send_time of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The award_send_time of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_send_time

    @award_send_time.setter
    def award_send_time(self, award_send_time):
        """Sets the award_send_time of this AwardConfigForListAwardConfigsOutput.


        :param award_send_time: The award_send_time of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._award_send_time = award_send_time

    @property
    def award_send_type(self):
        """Gets the award_send_type of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The award_send_type of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_send_type

    @award_send_type.setter
    def award_send_type(self, award_send_type):
        """Sets the award_send_type of this AwardConfigForListAwardConfigsOutput.


        :param award_send_type: The award_send_type of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._award_send_type = award_send_type

    @property
    def award_theme(self):
        """Gets the award_theme of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The award_theme of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._award_theme

    @award_theme.setter
    def award_theme(self, award_theme):
        """Sets the award_theme of this AwardConfigForListAwardConfigsOutput.


        :param award_theme: The award_theme of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: str
        """

        self._award_theme = award_theme

    @property
    def award_type(self):
        """Gets the award_type of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The award_type of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._award_type

    @award_type.setter
    def award_type(self, award_type):
        """Sets the award_type of this AwardConfigForListAwardConfigsOutput.


        :param award_type: The award_type of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._award_type = award_type

    @property
    def barrage_pwd(self):
        """Gets the barrage_pwd of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The barrage_pwd of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._barrage_pwd

    @barrage_pwd.setter
    def barrage_pwd(self, barrage_pwd):
        """Sets the barrage_pwd of this AwardConfigForListAwardConfigsOutput.


        :param barrage_pwd: The barrage_pwd of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: str
        """

        self._barrage_pwd = barrage_pwd

    @property
    def dead_line(self):
        """Gets the dead_line of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The dead_line of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._dead_line

    @dead_line.setter
    def dead_line(self, dead_line):
        """Sets the dead_line of this AwardConfigForListAwardConfigsOutput.


        :param dead_line: The dead_line of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._dead_line = dead_line

    @property
    def dead_line_second(self):
        """Gets the dead_line_second of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The dead_line_second of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._dead_line_second

    @dead_line_second.setter
    def dead_line_second(self, dead_line_second):
        """Sets the dead_line_second of this AwardConfigForListAwardConfigsOutput.


        :param dead_line_second: The dead_line_second of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._dead_line_second = dead_line_second

    @property
    def id(self):
        """Gets the id of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The id of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this AwardConfigForListAwardConfigsOutput.


        :param id: The id of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._id = id

    @property
    def is_hide_award_num(self):
        """Gets the is_hide_award_num of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The is_hide_award_num of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._is_hide_award_num

    @is_hide_award_num.setter
    def is_hide_award_num(self, is_hide_award_num):
        """Sets the is_hide_award_num of this AwardConfigForListAwardConfigsOutput.


        :param is_hide_award_num: The is_hide_award_num of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._is_hide_award_num = is_hide_award_num

    @property
    def msg_ids(self):
        """Gets the msg_ids of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The msg_ids of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._msg_ids

    @msg_ids.setter
    def msg_ids(self, msg_ids):
        """Sets the msg_ids of this AwardConfigForListAwardConfigsOutput.


        :param msg_ids: The msg_ids of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: str
        """

        self._msg_ids = msg_ids

    @property
    def name(self):
        """Gets the name of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The name of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this AwardConfigForListAwardConfigsOutput.


        :param name: The name of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def open_award_time(self):
        """Gets the open_award_time of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The open_award_time of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._open_award_time

    @open_award_time.setter
    def open_award_time(self, open_award_time):
        """Sets the open_award_time of this AwardConfigForListAwardConfigsOutput.


        :param open_award_time: The open_award_time of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._open_award_time = open_award_time

    @property
    def participant_count(self):
        """Gets the participant_count of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The participant_count of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._participant_count

    @participant_count.setter
    def participant_count(self, participant_count):
        """Sets the participant_count of this AwardConfigForListAwardConfigsOutput.


        :param participant_count: The participant_count of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._participant_count = participant_count

    @property
    def range_status(self):
        """Gets the range_status of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The range_status of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._range_status

    @range_status.setter
    def range_status(self, range_status):
        """Sets the range_status of this AwardConfigForListAwardConfigsOutput.


        :param range_status: The range_status of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._range_status = range_status

    @property
    def send_time(self):
        """Gets the send_time of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The send_time of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._send_time

    @send_time.setter
    def send_time(self, send_time):
        """Sets the send_time of this AwardConfigForListAwardConfigsOutput.


        :param send_time: The send_time of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._send_time = send_time

    @property
    def show_people_number(self):
        """Gets the show_people_number of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The show_people_number of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._show_people_number

    @show_people_number.setter
    def show_people_number(self, show_people_number):
        """Sets the show_people_number of this AwardConfigForListAwardConfigsOutput.


        :param show_people_number: The show_people_number of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._show_people_number = show_people_number

    @property
    def show_winner_comment(self):
        """Gets the show_winner_comment of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The show_winner_comment of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._show_winner_comment

    @show_winner_comment.setter
    def show_winner_comment(self, show_winner_comment):
        """Sets the show_winner_comment of this AwardConfigForListAwardConfigsOutput.


        :param show_winner_comment: The show_winner_comment of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._show_winner_comment = show_winner_comment

    @property
    def status(self):
        """Gets the status of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The status of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this AwardConfigForListAwardConfigsOutput.


        :param status: The status of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def winner_count(self):
        """Gets the winner_count of this AwardConfigForListAwardConfigsOutput.  # noqa: E501


        :return: The winner_count of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :rtype: int
        """
        return self._winner_count

    @winner_count.setter
    def winner_count(self, winner_count):
        """Sets the winner_count of this AwardConfigForListAwardConfigsOutput.


        :param winner_count: The winner_count of this AwardConfigForListAwardConfigsOutput.  # noqa: E501
        :type: int
        """

        self._winner_count = winner_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AwardConfigForListAwardConfigsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AwardConfigForListAwardConfigsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AwardConfigForListAwardConfigsOutput):
            return True

        return self.to_dict() != other.to_dict()
