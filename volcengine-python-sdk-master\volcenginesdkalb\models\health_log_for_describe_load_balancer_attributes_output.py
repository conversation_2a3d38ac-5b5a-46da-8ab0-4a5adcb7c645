# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class HealthLogForDescribeLoadBalancerAttributesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'enabled': 'bool',
        'project_id': 'str',
        'topic_id': 'str'
    }

    attribute_map = {
        'enabled': 'Enabled',
        'project_id': 'ProjectId',
        'topic_id': 'TopicId'
    }

    def __init__(self, enabled=None, project_id=None, topic_id=None, _configuration=None):  # noqa: E501
        """HealthLogForDescribeLoadBalancerAttributesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._enabled = None
        self._project_id = None
        self._topic_id = None
        self.discriminator = None

        if enabled is not None:
            self.enabled = enabled
        if project_id is not None:
            self.project_id = project_id
        if topic_id is not None:
            self.topic_id = topic_id

    @property
    def enabled(self):
        """Gets the enabled of this HealthLogForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The enabled of this HealthLogForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this HealthLogForDescribeLoadBalancerAttributesOutput.


        :param enabled: The enabled of this HealthLogForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    @property
    def project_id(self):
        """Gets the project_id of this HealthLogForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The project_id of this HealthLogForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_id

    @project_id.setter
    def project_id(self, project_id):
        """Sets the project_id of this HealthLogForDescribeLoadBalancerAttributesOutput.


        :param project_id: The project_id of this HealthLogForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._project_id = project_id

    @property
    def topic_id(self):
        """Gets the topic_id of this HealthLogForDescribeLoadBalancerAttributesOutput.  # noqa: E501


        :return: The topic_id of this HealthLogForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._topic_id

    @topic_id.setter
    def topic_id(self, topic_id):
        """Sets the topic_id of this HealthLogForDescribeLoadBalancerAttributesOutput.


        :param topic_id: The topic_id of this HealthLogForDescribeLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._topic_id = topic_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(HealthLogForDescribeLoadBalancerAttributesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, HealthLogForDescribeLoadBalancerAttributesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, HealthLogForDescribeLoadBalancerAttributesOutput):
            return True

        return self.to_dict() != other.to_dict()
