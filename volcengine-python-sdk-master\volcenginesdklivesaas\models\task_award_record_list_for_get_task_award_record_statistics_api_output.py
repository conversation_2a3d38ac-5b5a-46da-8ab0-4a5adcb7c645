# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'address': 'str',
        'email': 'str',
        'external_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'login_tel': 'str',
        'nickname': 'str',
        'phone': 'str',
        'receiver_name': 'str',
        'status': 'int',
        'task_award_name': 'str',
        'user_agent': 'str',
        'user_id': 'int',
        'win_time': 'str'
    }

    attribute_map = {
        'address': 'Address',
        'email': 'Email',
        'external_id': 'ExternalId',
        'extra': 'Extra',
        'ip': 'Ip',
        'login_tel': 'LoginTel',
        'nickname': 'Nickname',
        'phone': 'Phone',
        'receiver_name': 'ReceiverName',
        'status': 'Status',
        'task_award_name': 'TaskAwardName',
        'user_agent': 'UserAgent',
        'user_id': 'UserId',
        'win_time': 'WinTime'
    }

    def __init__(self, address=None, email=None, external_id=None, extra=None, ip=None, login_tel=None, nickname=None, phone=None, receiver_name=None, status=None, task_award_name=None, user_agent=None, user_id=None, win_time=None, _configuration=None):  # noqa: E501
        """TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._address = None
        self._email = None
        self._external_id = None
        self._extra = None
        self._ip = None
        self._login_tel = None
        self._nickname = None
        self._phone = None
        self._receiver_name = None
        self._status = None
        self._task_award_name = None
        self._user_agent = None
        self._user_id = None
        self._win_time = None
        self.discriminator = None

        if address is not None:
            self.address = address
        if email is not None:
            self.email = email
        if external_id is not None:
            self.external_id = external_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if login_tel is not None:
            self.login_tel = login_tel
        if nickname is not None:
            self.nickname = nickname
        if phone is not None:
            self.phone = phone
        if receiver_name is not None:
            self.receiver_name = receiver_name
        if status is not None:
            self.status = status
        if task_award_name is not None:
            self.task_award_name = task_award_name
        if user_agent is not None:
            self.user_agent = user_agent
        if user_id is not None:
            self.user_id = user_id
        if win_time is not None:
            self.win_time = win_time

    @property
    def address(self):
        """Gets the address of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The address of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._address

    @address.setter
    def address(self, address):
        """Sets the address of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param address: The address of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._address = address

    @property
    def email(self):
        """Gets the email of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The email of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param email: The email of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def external_id(self):
        """Gets the external_id of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The external_id of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param external_id: The external_id of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def extra(self):
        """Gets the extra of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The extra of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param extra: The extra of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The ip of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param ip: The ip of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def login_tel(self):
        """Gets the login_tel of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The login_tel of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._login_tel

    @login_tel.setter
    def login_tel(self, login_tel):
        """Sets the login_tel of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param login_tel: The login_tel of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._login_tel = login_tel

    @property
    def nickname(self):
        """Gets the nickname of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The nickname of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._nickname

    @nickname.setter
    def nickname(self, nickname):
        """Sets the nickname of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param nickname: The nickname of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._nickname = nickname

    @property
    def phone(self):
        """Gets the phone of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The phone of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._phone

    @phone.setter
    def phone(self, phone):
        """Sets the phone of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param phone: The phone of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._phone = phone

    @property
    def receiver_name(self):
        """Gets the receiver_name of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The receiver_name of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._receiver_name

    @receiver_name.setter
    def receiver_name(self, receiver_name):
        """Sets the receiver_name of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param receiver_name: The receiver_name of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._receiver_name = receiver_name

    @property
    def status(self):
        """Gets the status of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The status of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param status: The status of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def task_award_name(self):
        """Gets the task_award_name of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The task_award_name of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._task_award_name

    @task_award_name.setter
    def task_award_name(self, task_award_name):
        """Sets the task_award_name of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param task_award_name: The task_award_name of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._task_award_name = task_award_name

    @property
    def user_agent(self):
        """Gets the user_agent of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The user_agent of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_agent

    @user_agent.setter
    def user_agent(self, user_agent):
        """Sets the user_agent of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param user_agent: The user_agent of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_agent = user_agent

    @property
    def user_id(self):
        """Gets the user_id of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The user_id of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param user_id: The user_id of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def win_time(self):
        """Gets the win_time of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501


        :return: The win_time of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._win_time

    @win_time.setter
    def win_time(self, win_time):
        """Sets the win_time of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.


        :param win_time: The win_time of this TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput.  # noqa: E501
        :type: str
        """

        self._win_time = win_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TaskAwardRecordListForGetTaskAwardRecordStatisticsAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
