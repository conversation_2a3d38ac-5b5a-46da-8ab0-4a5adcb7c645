# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreatePrivateZoneRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'client_token': 'str',
        'line_mode': 'int',
        'project_name': 'str',
        'recursion_mode': 'bool',
        'remark': 'str',
        'tags': 'list[TagForCreatePrivateZoneInput]',
        'vpc_trns': 'list[str]',
        'vpcs': 'list[VpcForCreatePrivateZoneInput]',
        'zone_name': 'str'
    }

    attribute_map = {
        'client_token': 'ClientToken',
        'line_mode': 'LineMode',
        'project_name': 'ProjectName',
        'recursion_mode': 'RecursionMode',
        'remark': 'Remark',
        'tags': 'Tags',
        'vpc_trns': 'VpcTrns',
        'vpcs': 'Vpcs',
        'zone_name': 'ZoneName'
    }

    def __init__(self, client_token=None, line_mode=None, project_name=None, recursion_mode=None, remark=None, tags=None, vpc_trns=None, vpcs=None, zone_name=None, _configuration=None):  # noqa: E501
        """CreatePrivateZoneRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._client_token = None
        self._line_mode = None
        self._project_name = None
        self._recursion_mode = None
        self._remark = None
        self._tags = None
        self._vpc_trns = None
        self._vpcs = None
        self._zone_name = None
        self.discriminator = None

        if client_token is not None:
            self.client_token = client_token
        if line_mode is not None:
            self.line_mode = line_mode
        if project_name is not None:
            self.project_name = project_name
        if recursion_mode is not None:
            self.recursion_mode = recursion_mode
        if remark is not None:
            self.remark = remark
        if tags is not None:
            self.tags = tags
        if vpc_trns is not None:
            self.vpc_trns = vpc_trns
        if vpcs is not None:
            self.vpcs = vpcs
        self.zone_name = zone_name

    @property
    def client_token(self):
        """Gets the client_token of this CreatePrivateZoneRequest.  # noqa: E501


        :return: The client_token of this CreatePrivateZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._client_token

    @client_token.setter
    def client_token(self, client_token):
        """Sets the client_token of this CreatePrivateZoneRequest.


        :param client_token: The client_token of this CreatePrivateZoneRequest.  # noqa: E501
        :type: str
        """

        self._client_token = client_token

    @property
    def line_mode(self):
        """Gets the line_mode of this CreatePrivateZoneRequest.  # noqa: E501


        :return: The line_mode of this CreatePrivateZoneRequest.  # noqa: E501
        :rtype: int
        """
        return self._line_mode

    @line_mode.setter
    def line_mode(self, line_mode):
        """Sets the line_mode of this CreatePrivateZoneRequest.


        :param line_mode: The line_mode of this CreatePrivateZoneRequest.  # noqa: E501
        :type: int
        """

        self._line_mode = line_mode

    @property
    def project_name(self):
        """Gets the project_name of this CreatePrivateZoneRequest.  # noqa: E501


        :return: The project_name of this CreatePrivateZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreatePrivateZoneRequest.


        :param project_name: The project_name of this CreatePrivateZoneRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def recursion_mode(self):
        """Gets the recursion_mode of this CreatePrivateZoneRequest.  # noqa: E501


        :return: The recursion_mode of this CreatePrivateZoneRequest.  # noqa: E501
        :rtype: bool
        """
        return self._recursion_mode

    @recursion_mode.setter
    def recursion_mode(self, recursion_mode):
        """Sets the recursion_mode of this CreatePrivateZoneRequest.


        :param recursion_mode: The recursion_mode of this CreatePrivateZoneRequest.  # noqa: E501
        :type: bool
        """

        self._recursion_mode = recursion_mode

    @property
    def remark(self):
        """Gets the remark of this CreatePrivateZoneRequest.  # noqa: E501


        :return: The remark of this CreatePrivateZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this CreatePrivateZoneRequest.


        :param remark: The remark of this CreatePrivateZoneRequest.  # noqa: E501
        :type: str
        """

        self._remark = remark

    @property
    def tags(self):
        """Gets the tags of this CreatePrivateZoneRequest.  # noqa: E501


        :return: The tags of this CreatePrivateZoneRequest.  # noqa: E501
        :rtype: list[TagForCreatePrivateZoneInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreatePrivateZoneRequest.


        :param tags: The tags of this CreatePrivateZoneRequest.  # noqa: E501
        :type: list[TagForCreatePrivateZoneInput]
        """

        self._tags = tags

    @property
    def vpc_trns(self):
        """Gets the vpc_trns of this CreatePrivateZoneRequest.  # noqa: E501


        :return: The vpc_trns of this CreatePrivateZoneRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_trns

    @vpc_trns.setter
    def vpc_trns(self, vpc_trns):
        """Sets the vpc_trns of this CreatePrivateZoneRequest.


        :param vpc_trns: The vpc_trns of this CreatePrivateZoneRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpc_trns = vpc_trns

    @property
    def vpcs(self):
        """Gets the vpcs of this CreatePrivateZoneRequest.  # noqa: E501


        :return: The vpcs of this CreatePrivateZoneRequest.  # noqa: E501
        :rtype: list[VpcForCreatePrivateZoneInput]
        """
        return self._vpcs

    @vpcs.setter
    def vpcs(self, vpcs):
        """Sets the vpcs of this CreatePrivateZoneRequest.


        :param vpcs: The vpcs of this CreatePrivateZoneRequest.  # noqa: E501
        :type: list[VpcForCreatePrivateZoneInput]
        """

        self._vpcs = vpcs

    @property
    def zone_name(self):
        """Gets the zone_name of this CreatePrivateZoneRequest.  # noqa: E501


        :return: The zone_name of this CreatePrivateZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._zone_name

    @zone_name.setter
    def zone_name(self, zone_name):
        """Sets the zone_name of this CreatePrivateZoneRequest.


        :param zone_name: The zone_name of this CreatePrivateZoneRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and zone_name is None:
            raise ValueError("Invalid value for `zone_name`, must not be `None`")  # noqa: E501

        self._zone_name = zone_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreatePrivateZoneRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreatePrivateZoneRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreatePrivateZoneRequest):
            return True

        return self.to_dict() != other.to_dict()
