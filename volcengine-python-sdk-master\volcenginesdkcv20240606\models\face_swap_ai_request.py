# coding: utf-8

"""
    cv20240606

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FaceSwapAIRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'binary_data_base64': 'list[str]',
        'do_risk': 'bool',
        'gpen': 'float',
        'image_urls': 'list[str]',
        'logo_info': 'LogoInfoForFaceSwapAIInput',
        'req_key': 'str',
        'return_url': 'bool',
        'skin': 'float',
        'tou_repair': 'int'
    }

    attribute_map = {
        'binary_data_base64': 'binary_data_base64',
        'do_risk': 'do_risk',
        'gpen': 'gpen',
        'image_urls': 'image_urls',
        'logo_info': 'logo_info',
        'req_key': 'req_key',
        'return_url': 'return_url',
        'skin': 'skin',
        'tou_repair': 'tou_repair'
    }

    def __init__(self, binary_data_base64=None, do_risk=None, gpen=None, image_urls=None, logo_info=None, req_key=None, return_url=None, skin=None, tou_repair=None, _configuration=None):  # noqa: E501
        """FaceSwapAIRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._binary_data_base64 = None
        self._do_risk = None
        self._gpen = None
        self._image_urls = None
        self._logo_info = None
        self._req_key = None
        self._return_url = None
        self._skin = None
        self._tou_repair = None
        self.discriminator = None

        if binary_data_base64 is not None:
            self.binary_data_base64 = binary_data_base64
        if do_risk is not None:
            self.do_risk = do_risk
        self.gpen = gpen
        if image_urls is not None:
            self.image_urls = image_urls
        if logo_info is not None:
            self.logo_info = logo_info
        self.req_key = req_key
        if return_url is not None:
            self.return_url = return_url
        self.skin = skin
        if tou_repair is not None:
            self.tou_repair = tou_repair

    @property
    def binary_data_base64(self):
        """Gets the binary_data_base64 of this FaceSwapAIRequest.  # noqa: E501


        :return: The binary_data_base64 of this FaceSwapAIRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._binary_data_base64

    @binary_data_base64.setter
    def binary_data_base64(self, binary_data_base64):
        """Sets the binary_data_base64 of this FaceSwapAIRequest.


        :param binary_data_base64: The binary_data_base64 of this FaceSwapAIRequest.  # noqa: E501
        :type: list[str]
        """

        self._binary_data_base64 = binary_data_base64

    @property
    def do_risk(self):
        """Gets the do_risk of this FaceSwapAIRequest.  # noqa: E501


        :return: The do_risk of this FaceSwapAIRequest.  # noqa: E501
        :rtype: bool
        """
        return self._do_risk

    @do_risk.setter
    def do_risk(self, do_risk):
        """Sets the do_risk of this FaceSwapAIRequest.


        :param do_risk: The do_risk of this FaceSwapAIRequest.  # noqa: E501
        :type: bool
        """

        self._do_risk = do_risk

    @property
    def gpen(self):
        """Gets the gpen of this FaceSwapAIRequest.  # noqa: E501


        :return: The gpen of this FaceSwapAIRequest.  # noqa: E501
        :rtype: float
        """
        return self._gpen

    @gpen.setter
    def gpen(self, gpen):
        """Sets the gpen of this FaceSwapAIRequest.


        :param gpen: The gpen of this FaceSwapAIRequest.  # noqa: E501
        :type: float
        """
        if self._configuration.client_side_validation and gpen is None:
            raise ValueError("Invalid value for `gpen`, must not be `None`")  # noqa: E501

        self._gpen = gpen

    @property
    def image_urls(self):
        """Gets the image_urls of this FaceSwapAIRequest.  # noqa: E501


        :return: The image_urls of this FaceSwapAIRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._image_urls

    @image_urls.setter
    def image_urls(self, image_urls):
        """Sets the image_urls of this FaceSwapAIRequest.


        :param image_urls: The image_urls of this FaceSwapAIRequest.  # noqa: E501
        :type: list[str]
        """

        self._image_urls = image_urls

    @property
    def logo_info(self):
        """Gets the logo_info of this FaceSwapAIRequest.  # noqa: E501


        :return: The logo_info of this FaceSwapAIRequest.  # noqa: E501
        :rtype: LogoInfoForFaceSwapAIInput
        """
        return self._logo_info

    @logo_info.setter
    def logo_info(self, logo_info):
        """Sets the logo_info of this FaceSwapAIRequest.


        :param logo_info: The logo_info of this FaceSwapAIRequest.  # noqa: E501
        :type: LogoInfoForFaceSwapAIInput
        """

        self._logo_info = logo_info

    @property
    def req_key(self):
        """Gets the req_key of this FaceSwapAIRequest.  # noqa: E501


        :return: The req_key of this FaceSwapAIRequest.  # noqa: E501
        :rtype: str
        """
        return self._req_key

    @req_key.setter
    def req_key(self, req_key):
        """Sets the req_key of this FaceSwapAIRequest.


        :param req_key: The req_key of this FaceSwapAIRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and req_key is None:
            raise ValueError("Invalid value for `req_key`, must not be `None`")  # noqa: E501

        self._req_key = req_key

    @property
    def return_url(self):
        """Gets the return_url of this FaceSwapAIRequest.  # noqa: E501


        :return: The return_url of this FaceSwapAIRequest.  # noqa: E501
        :rtype: bool
        """
        return self._return_url

    @return_url.setter
    def return_url(self, return_url):
        """Sets the return_url of this FaceSwapAIRequest.


        :param return_url: The return_url of this FaceSwapAIRequest.  # noqa: E501
        :type: bool
        """

        self._return_url = return_url

    @property
    def skin(self):
        """Gets the skin of this FaceSwapAIRequest.  # noqa: E501


        :return: The skin of this FaceSwapAIRequest.  # noqa: E501
        :rtype: float
        """
        return self._skin

    @skin.setter
    def skin(self, skin):
        """Sets the skin of this FaceSwapAIRequest.


        :param skin: The skin of this FaceSwapAIRequest.  # noqa: E501
        :type: float
        """
        if self._configuration.client_side_validation and skin is None:
            raise ValueError("Invalid value for `skin`, must not be `None`")  # noqa: E501

        self._skin = skin

    @property
    def tou_repair(self):
        """Gets the tou_repair of this FaceSwapAIRequest.  # noqa: E501


        :return: The tou_repair of this FaceSwapAIRequest.  # noqa: E501
        :rtype: int
        """
        return self._tou_repair

    @tou_repair.setter
    def tou_repair(self, tou_repair):
        """Sets the tou_repair of this FaceSwapAIRequest.


        :param tou_repair: The tou_repair of this FaceSwapAIRequest.  # noqa: E501
        :type: int
        """

        self._tou_repair = tou_repair

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FaceSwapAIRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FaceSwapAIRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FaceSwapAIRequest):
            return True

        return self.to_dict() != other.to_dict()
