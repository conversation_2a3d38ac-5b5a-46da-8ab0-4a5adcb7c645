# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ClusterForListEDXAssociatedVGWTopologyOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alias': 'str',
        'city': 'str',
        'cluster': 'str',
        'country': 'str',
        'isp': 'str',
        'province': 'str',
        'region': 'str'
    }

    attribute_map = {
        'alias': 'Alias',
        'city': 'City',
        'cluster': 'Cluster',
        'country': 'Country',
        'isp': 'ISP',
        'province': 'Province',
        'region': 'Region'
    }

    def __init__(self, alias=None, city=None, cluster=None, country=None, isp=None, province=None, region=None, _configuration=None):  # noqa: E501
        """ClusterForListEDXAssociatedVGWTopologyOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alias = None
        self._city = None
        self._cluster = None
        self._country = None
        self._isp = None
        self._province = None
        self._region = None
        self.discriminator = None

        if alias is not None:
            self.alias = alias
        if city is not None:
            self.city = city
        if cluster is not None:
            self.cluster = cluster
        if country is not None:
            self.country = country
        if isp is not None:
            self.isp = isp
        if province is not None:
            self.province = province
        if region is not None:
            self.region = region

    @property
    def alias(self):
        """Gets the alias of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501


        :return: The alias of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :rtype: str
        """
        return self._alias

    @alias.setter
    def alias(self, alias):
        """Sets the alias of this ClusterForListEDXAssociatedVGWTopologyOutput.


        :param alias: The alias of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :type: str
        """

        self._alias = alias

    @property
    def city(self):
        """Gets the city of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501


        :return: The city of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :rtype: str
        """
        return self._city

    @city.setter
    def city(self, city):
        """Sets the city of this ClusterForListEDXAssociatedVGWTopologyOutput.


        :param city: The city of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :type: str
        """

        self._city = city

    @property
    def cluster(self):
        """Gets the cluster of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501


        :return: The cluster of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster

    @cluster.setter
    def cluster(self, cluster):
        """Sets the cluster of this ClusterForListEDXAssociatedVGWTopologyOutput.


        :param cluster: The cluster of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :type: str
        """

        self._cluster = cluster

    @property
    def country(self):
        """Gets the country of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501


        :return: The country of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :rtype: str
        """
        return self._country

    @country.setter
    def country(self, country):
        """Sets the country of this ClusterForListEDXAssociatedVGWTopologyOutput.


        :param country: The country of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :type: str
        """

        self._country = country

    @property
    def isp(self):
        """Gets the isp of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501


        :return: The isp of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this ClusterForListEDXAssociatedVGWTopologyOutput.


        :param isp: The isp of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :type: str
        """

        self._isp = isp

    @property
    def province(self):
        """Gets the province of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501


        :return: The province of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :rtype: str
        """
        return self._province

    @province.setter
    def province(self, province):
        """Sets the province of this ClusterForListEDXAssociatedVGWTopologyOutput.


        :param province: The province of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :type: str
        """

        self._province = province

    @property
    def region(self):
        """Gets the region of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501


        :return: The region of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this ClusterForListEDXAssociatedVGWTopologyOutput.


        :param region: The region of this ClusterForListEDXAssociatedVGWTopologyOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ClusterForListEDXAssociatedVGWTopologyOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ClusterForListEDXAssociatedVGWTopologyOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ClusterForListEDXAssociatedVGWTopologyOutput):
            return True

        return self.to_dict() != other.to_dict()
