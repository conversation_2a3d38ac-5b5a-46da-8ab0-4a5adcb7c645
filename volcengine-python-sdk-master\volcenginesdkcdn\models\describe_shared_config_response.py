# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeSharedConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'allow_ip_access_rule': 'AllowIpAccessRuleForDescribeSharedConfigOutput',
        'allow_referer_access_rule': 'AllowRefererAccessRuleForDescribeSharedConfigOutput',
        'common_match_list': 'CommonMatchListForDescribeSharedConfigOutput',
        'config_name': 'str',
        'config_type': 'str',
        'deny_ip_access_rule': 'DenyIpAccessRuleForDescribeSharedConfigOutput',
        'deny_referer_access_rule': 'DenyRefererAccessRuleForDescribeSharedConfigOutput',
        'error_page_rule': 'ErrorPageRuleForDescribeSharedConfigOutput',
        'project': 'str'
    }

    attribute_map = {
        'allow_ip_access_rule': 'AllowIpAccessRule',
        'allow_referer_access_rule': 'AllowRefererAccessRule',
        'common_match_list': 'CommonMatchList',
        'config_name': 'ConfigName',
        'config_type': 'ConfigType',
        'deny_ip_access_rule': 'DenyIpAccessRule',
        'deny_referer_access_rule': 'DenyRefererAccessRule',
        'error_page_rule': 'ErrorPageRule',
        'project': 'Project'
    }

    def __init__(self, allow_ip_access_rule=None, allow_referer_access_rule=None, common_match_list=None, config_name=None, config_type=None, deny_ip_access_rule=None, deny_referer_access_rule=None, error_page_rule=None, project=None, _configuration=None):  # noqa: E501
        """DescribeSharedConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._allow_ip_access_rule = None
        self._allow_referer_access_rule = None
        self._common_match_list = None
        self._config_name = None
        self._config_type = None
        self._deny_ip_access_rule = None
        self._deny_referer_access_rule = None
        self._error_page_rule = None
        self._project = None
        self.discriminator = None

        if allow_ip_access_rule is not None:
            self.allow_ip_access_rule = allow_ip_access_rule
        if allow_referer_access_rule is not None:
            self.allow_referer_access_rule = allow_referer_access_rule
        if common_match_list is not None:
            self.common_match_list = common_match_list
        if config_name is not None:
            self.config_name = config_name
        if config_type is not None:
            self.config_type = config_type
        if deny_ip_access_rule is not None:
            self.deny_ip_access_rule = deny_ip_access_rule
        if deny_referer_access_rule is not None:
            self.deny_referer_access_rule = deny_referer_access_rule
        if error_page_rule is not None:
            self.error_page_rule = error_page_rule
        if project is not None:
            self.project = project

    @property
    def allow_ip_access_rule(self):
        """Gets the allow_ip_access_rule of this DescribeSharedConfigResponse.  # noqa: E501


        :return: The allow_ip_access_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :rtype: AllowIpAccessRuleForDescribeSharedConfigOutput
        """
        return self._allow_ip_access_rule

    @allow_ip_access_rule.setter
    def allow_ip_access_rule(self, allow_ip_access_rule):
        """Sets the allow_ip_access_rule of this DescribeSharedConfigResponse.


        :param allow_ip_access_rule: The allow_ip_access_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :type: AllowIpAccessRuleForDescribeSharedConfigOutput
        """

        self._allow_ip_access_rule = allow_ip_access_rule

    @property
    def allow_referer_access_rule(self):
        """Gets the allow_referer_access_rule of this DescribeSharedConfigResponse.  # noqa: E501


        :return: The allow_referer_access_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :rtype: AllowRefererAccessRuleForDescribeSharedConfigOutput
        """
        return self._allow_referer_access_rule

    @allow_referer_access_rule.setter
    def allow_referer_access_rule(self, allow_referer_access_rule):
        """Sets the allow_referer_access_rule of this DescribeSharedConfigResponse.


        :param allow_referer_access_rule: The allow_referer_access_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :type: AllowRefererAccessRuleForDescribeSharedConfigOutput
        """

        self._allow_referer_access_rule = allow_referer_access_rule

    @property
    def common_match_list(self):
        """Gets the common_match_list of this DescribeSharedConfigResponse.  # noqa: E501


        :return: The common_match_list of this DescribeSharedConfigResponse.  # noqa: E501
        :rtype: CommonMatchListForDescribeSharedConfigOutput
        """
        return self._common_match_list

    @common_match_list.setter
    def common_match_list(self, common_match_list):
        """Sets the common_match_list of this DescribeSharedConfigResponse.


        :param common_match_list: The common_match_list of this DescribeSharedConfigResponse.  # noqa: E501
        :type: CommonMatchListForDescribeSharedConfigOutput
        """

        self._common_match_list = common_match_list

    @property
    def config_name(self):
        """Gets the config_name of this DescribeSharedConfigResponse.  # noqa: E501


        :return: The config_name of this DescribeSharedConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._config_name

    @config_name.setter
    def config_name(self, config_name):
        """Sets the config_name of this DescribeSharedConfigResponse.


        :param config_name: The config_name of this DescribeSharedConfigResponse.  # noqa: E501
        :type: str
        """

        self._config_name = config_name

    @property
    def config_type(self):
        """Gets the config_type of this DescribeSharedConfigResponse.  # noqa: E501


        :return: The config_type of this DescribeSharedConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._config_type

    @config_type.setter
    def config_type(self, config_type):
        """Sets the config_type of this DescribeSharedConfigResponse.


        :param config_type: The config_type of this DescribeSharedConfigResponse.  # noqa: E501
        :type: str
        """

        self._config_type = config_type

    @property
    def deny_ip_access_rule(self):
        """Gets the deny_ip_access_rule of this DescribeSharedConfigResponse.  # noqa: E501


        :return: The deny_ip_access_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :rtype: DenyIpAccessRuleForDescribeSharedConfigOutput
        """
        return self._deny_ip_access_rule

    @deny_ip_access_rule.setter
    def deny_ip_access_rule(self, deny_ip_access_rule):
        """Sets the deny_ip_access_rule of this DescribeSharedConfigResponse.


        :param deny_ip_access_rule: The deny_ip_access_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :type: DenyIpAccessRuleForDescribeSharedConfigOutput
        """

        self._deny_ip_access_rule = deny_ip_access_rule

    @property
    def deny_referer_access_rule(self):
        """Gets the deny_referer_access_rule of this DescribeSharedConfigResponse.  # noqa: E501


        :return: The deny_referer_access_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :rtype: DenyRefererAccessRuleForDescribeSharedConfigOutput
        """
        return self._deny_referer_access_rule

    @deny_referer_access_rule.setter
    def deny_referer_access_rule(self, deny_referer_access_rule):
        """Sets the deny_referer_access_rule of this DescribeSharedConfigResponse.


        :param deny_referer_access_rule: The deny_referer_access_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :type: DenyRefererAccessRuleForDescribeSharedConfigOutput
        """

        self._deny_referer_access_rule = deny_referer_access_rule

    @property
    def error_page_rule(self):
        """Gets the error_page_rule of this DescribeSharedConfigResponse.  # noqa: E501


        :return: The error_page_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :rtype: ErrorPageRuleForDescribeSharedConfigOutput
        """
        return self._error_page_rule

    @error_page_rule.setter
    def error_page_rule(self, error_page_rule):
        """Sets the error_page_rule of this DescribeSharedConfigResponse.


        :param error_page_rule: The error_page_rule of this DescribeSharedConfigResponse.  # noqa: E501
        :type: ErrorPageRuleForDescribeSharedConfigOutput
        """

        self._error_page_rule = error_page_rule

    @property
    def project(self):
        """Gets the project of this DescribeSharedConfigResponse.  # noqa: E501


        :return: The project of this DescribeSharedConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this DescribeSharedConfigResponse.


        :param project: The project of this DescribeSharedConfigResponse.  # noqa: E501
        :type: str
        """

        self._project = project

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeSharedConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeSharedConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeSharedConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
