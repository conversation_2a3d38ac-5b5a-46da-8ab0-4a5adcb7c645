# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListNodePoolsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_scaling': 'AutoScalingForListNodePoolsOutput',
        'cluster_id': 'str',
        'create_client_token': 'str',
        'create_time': 'str',
        'id': 'str',
        'kubernetes_config': 'KubernetesConfigForListNodePoolsOutput',
        'name': 'str',
        'node_config': 'NodeConfigForListNodePoolsOutput',
        'node_statistics': 'NodeStatisticsForListNodePoolsOutput',
        'status': 'StatusForListNodePoolsOutput',
        'tags': 'list[ConvertTagForListNodePoolsOutput]',
        'update_client_token': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'auto_scaling': 'AutoScaling',
        'cluster_id': 'ClusterId',
        'create_client_token': 'CreateClientToken',
        'create_time': 'CreateTime',
        'id': 'Id',
        'kubernetes_config': 'KubernetesConfig',
        'name': 'Name',
        'node_config': 'NodeConfig',
        'node_statistics': 'NodeStatistics',
        'status': 'Status',
        'tags': 'Tags',
        'update_client_token': 'UpdateClientToken',
        'update_time': 'UpdateTime'
    }

    def __init__(self, auto_scaling=None, cluster_id=None, create_client_token=None, create_time=None, id=None, kubernetes_config=None, name=None, node_config=None, node_statistics=None, status=None, tags=None, update_client_token=None, update_time=None, _configuration=None):  # noqa: E501
        """ItemForListNodePoolsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_scaling = None
        self._cluster_id = None
        self._create_client_token = None
        self._create_time = None
        self._id = None
        self._kubernetes_config = None
        self._name = None
        self._node_config = None
        self._node_statistics = None
        self._status = None
        self._tags = None
        self._update_client_token = None
        self._update_time = None
        self.discriminator = None

        if auto_scaling is not None:
            self.auto_scaling = auto_scaling
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if create_client_token is not None:
            self.create_client_token = create_client_token
        if create_time is not None:
            self.create_time = create_time
        if id is not None:
            self.id = id
        if kubernetes_config is not None:
            self.kubernetes_config = kubernetes_config
        if name is not None:
            self.name = name
        if node_config is not None:
            self.node_config = node_config
        if node_statistics is not None:
            self.node_statistics = node_statistics
        if status is not None:
            self.status = status
        if tags is not None:
            self.tags = tags
        if update_client_token is not None:
            self.update_client_token = update_client_token
        if update_time is not None:
            self.update_time = update_time

    @property
    def auto_scaling(self):
        """Gets the auto_scaling of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The auto_scaling of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: AutoScalingForListNodePoolsOutput
        """
        return self._auto_scaling

    @auto_scaling.setter
    def auto_scaling(self, auto_scaling):
        """Sets the auto_scaling of this ItemForListNodePoolsOutput.


        :param auto_scaling: The auto_scaling of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: AutoScalingForListNodePoolsOutput
        """

        self._auto_scaling = auto_scaling

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The cluster_id of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ItemForListNodePoolsOutput.


        :param cluster_id: The cluster_id of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def create_client_token(self):
        """Gets the create_client_token of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The create_client_token of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_client_token

    @create_client_token.setter
    def create_client_token(self, create_client_token):
        """Sets the create_client_token of this ItemForListNodePoolsOutput.


        :param create_client_token: The create_client_token of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: str
        """

        self._create_client_token = create_client_token

    @property
    def create_time(self):
        """Gets the create_time of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The create_time of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this ItemForListNodePoolsOutput.


        :param create_time: The create_time of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def id(self):
        """Gets the id of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The id of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListNodePoolsOutput.


        :param id: The id of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def kubernetes_config(self):
        """Gets the kubernetes_config of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The kubernetes_config of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: KubernetesConfigForListNodePoolsOutput
        """
        return self._kubernetes_config

    @kubernetes_config.setter
    def kubernetes_config(self, kubernetes_config):
        """Sets the kubernetes_config of this ItemForListNodePoolsOutput.


        :param kubernetes_config: The kubernetes_config of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: KubernetesConfigForListNodePoolsOutput
        """

        self._kubernetes_config = kubernetes_config

    @property
    def name(self):
        """Gets the name of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The name of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this ItemForListNodePoolsOutput.


        :param name: The name of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def node_config(self):
        """Gets the node_config of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The node_config of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: NodeConfigForListNodePoolsOutput
        """
        return self._node_config

    @node_config.setter
    def node_config(self, node_config):
        """Sets the node_config of this ItemForListNodePoolsOutput.


        :param node_config: The node_config of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: NodeConfigForListNodePoolsOutput
        """

        self._node_config = node_config

    @property
    def node_statistics(self):
        """Gets the node_statistics of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The node_statistics of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: NodeStatisticsForListNodePoolsOutput
        """
        return self._node_statistics

    @node_statistics.setter
    def node_statistics(self, node_statistics):
        """Sets the node_statistics of this ItemForListNodePoolsOutput.


        :param node_statistics: The node_statistics of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: NodeStatisticsForListNodePoolsOutput
        """

        self._node_statistics = node_statistics

    @property
    def status(self):
        """Gets the status of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The status of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: StatusForListNodePoolsOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListNodePoolsOutput.


        :param status: The status of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: StatusForListNodePoolsOutput
        """

        self._status = status

    @property
    def tags(self):
        """Gets the tags of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The tags of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: list[ConvertTagForListNodePoolsOutput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ItemForListNodePoolsOutput.


        :param tags: The tags of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: list[ConvertTagForListNodePoolsOutput]
        """

        self._tags = tags

    @property
    def update_client_token(self):
        """Gets the update_client_token of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The update_client_token of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_client_token

    @update_client_token.setter
    def update_client_token(self, update_client_token):
        """Sets the update_client_token of this ItemForListNodePoolsOutput.


        :param update_client_token: The update_client_token of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: str
        """

        self._update_client_token = update_client_token

    @property
    def update_time(self):
        """Gets the update_time of this ItemForListNodePoolsOutput.  # noqa: E501


        :return: The update_time of this ItemForListNodePoolsOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this ItemForListNodePoolsOutput.


        :param update_time: The update_time of this ItemForListNodePoolsOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListNodePoolsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListNodePoolsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListNodePoolsOutput):
            return True

        return self.to_dict() != other.to_dict()
