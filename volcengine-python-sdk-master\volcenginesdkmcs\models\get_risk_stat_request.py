# coding: utf-8

"""
    mcs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetRiskStatRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cloud_accounts': 'list[CloudAccountForGetRiskStatInput]',
        'cloud_vendors': 'list[str]',
        'display_mode': 'str',
        'risk_type': 'str',
        'time_range': 'TimeRangeForGetRiskStatInput'
    }

    attribute_map = {
        'cloud_accounts': 'CloudAccounts',
        'cloud_vendors': 'CloudVendors',
        'display_mode': 'DisplayMode',
        'risk_type': 'RiskType',
        'time_range': 'TimeRange'
    }

    def __init__(self, cloud_accounts=None, cloud_vendors=None, display_mode=None, risk_type=None, time_range=None, _configuration=None):  # noqa: E501
        """GetRiskStatRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cloud_accounts = None
        self._cloud_vendors = None
        self._display_mode = None
        self._risk_type = None
        self._time_range = None
        self.discriminator = None

        if cloud_accounts is not None:
            self.cloud_accounts = cloud_accounts
        if cloud_vendors is not None:
            self.cloud_vendors = cloud_vendors
        if display_mode is not None:
            self.display_mode = display_mode
        if risk_type is not None:
            self.risk_type = risk_type
        if time_range is not None:
            self.time_range = time_range

    @property
    def cloud_accounts(self):
        """Gets the cloud_accounts of this GetRiskStatRequest.  # noqa: E501


        :return: The cloud_accounts of this GetRiskStatRequest.  # noqa: E501
        :rtype: list[CloudAccountForGetRiskStatInput]
        """
        return self._cloud_accounts

    @cloud_accounts.setter
    def cloud_accounts(self, cloud_accounts):
        """Sets the cloud_accounts of this GetRiskStatRequest.


        :param cloud_accounts: The cloud_accounts of this GetRiskStatRequest.  # noqa: E501
        :type: list[CloudAccountForGetRiskStatInput]
        """

        self._cloud_accounts = cloud_accounts

    @property
    def cloud_vendors(self):
        """Gets the cloud_vendors of this GetRiskStatRequest.  # noqa: E501


        :return: The cloud_vendors of this GetRiskStatRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._cloud_vendors

    @cloud_vendors.setter
    def cloud_vendors(self, cloud_vendors):
        """Sets the cloud_vendors of this GetRiskStatRequest.


        :param cloud_vendors: The cloud_vendors of this GetRiskStatRequest.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["volcengine", "aliyun", "huaweicloud", "tencent"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(cloud_vendors).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `cloud_vendors` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(cloud_vendors) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._cloud_vendors = cloud_vendors

    @property
    def display_mode(self):
        """Gets the display_mode of this GetRiskStatRequest.  # noqa: E501


        :return: The display_mode of this GetRiskStatRequest.  # noqa: E501
        :rtype: str
        """
        return self._display_mode

    @display_mode.setter
    def display_mode(self, display_mode):
        """Sets the display_mode of this GetRiskStatRequest.


        :param display_mode: The display_mode of this GetRiskStatRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["date", "date-hour"]  # noqa: E501
        if (self._configuration.client_side_validation and
                display_mode not in allowed_values):
            raise ValueError(
                "Invalid value for `display_mode` ({0}), must be one of {1}"  # noqa: E501
                .format(display_mode, allowed_values)
            )

        self._display_mode = display_mode

    @property
    def risk_type(self):
        """Gets the risk_type of this GetRiskStatRequest.  # noqa: E501


        :return: The risk_type of this GetRiskStatRequest.  # noqa: E501
        :rtype: str
        """
        return self._risk_type

    @risk_type.setter
    def risk_type(self, risk_type):
        """Sets the risk_type of this GetRiskStatRequest.


        :param risk_type: The risk_type of this GetRiskStatRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["risk_model_vulnerability", "risk_model_mc_strategy", "risk_model_attack_chain_analysis"]  # noqa: E501
        if (self._configuration.client_side_validation and
                risk_type not in allowed_values):
            raise ValueError(
                "Invalid value for `risk_type` ({0}), must be one of {1}"  # noqa: E501
                .format(risk_type, allowed_values)
            )

        self._risk_type = risk_type

    @property
    def time_range(self):
        """Gets the time_range of this GetRiskStatRequest.  # noqa: E501


        :return: The time_range of this GetRiskStatRequest.  # noqa: E501
        :rtype: TimeRangeForGetRiskStatInput
        """
        return self._time_range

    @time_range.setter
    def time_range(self, time_range):
        """Sets the time_range of this GetRiskStatRequest.


        :param time_range: The time_range of this GetRiskStatRequest.  # noqa: E501
        :type: TimeRangeForGetRiskStatInput
        """

        self._time_range = time_range

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetRiskStatRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetRiskStatRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetRiskStatRequest):
            return True

        return self.to_dict() != other.to_dict()
