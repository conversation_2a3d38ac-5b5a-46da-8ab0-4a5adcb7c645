# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlannedEventForDescribePlannedEventsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'business_impact': 'str',
        'db_engine': 'str',
        'event_action': 'str',
        'event_id': 'str',
        'event_name': 'str',
        'event_type': 'str',
        'instance_id': 'str',
        'instance_name': 'str',
        'max_delay_time': 'str',
        'origin_begin_time': 'str',
        'planned_begin_time': 'str',
        'planned_end_time': 'str',
        'planned_event_description': 'str',
        'planned_event_reason': 'str',
        'planned_switch_begin_time': 'str',
        'planned_switch_end_time': 'str',
        'region': 'str',
        'status': 'str'
    }

    attribute_map = {
        'business_impact': 'BusinessImpact',
        'db_engine': 'DBEngine',
        'event_action': 'EventAction',
        'event_id': 'EventID',
        'event_name': 'EventName',
        'event_type': 'EventType',
        'instance_id': 'InstanceId',
        'instance_name': 'InstanceName',
        'max_delay_time': 'MaxDelayTime',
        'origin_begin_time': 'OriginBeginTime',
        'planned_begin_time': 'PlannedBeginTime',
        'planned_end_time': 'PlannedEndTime',
        'planned_event_description': 'PlannedEventDescription',
        'planned_event_reason': 'PlannedEventReason',
        'planned_switch_begin_time': 'PlannedSwitchBeginTime',
        'planned_switch_end_time': 'PlannedSwitchEndTime',
        'region': 'Region',
        'status': 'Status'
    }

    def __init__(self, business_impact=None, db_engine=None, event_action=None, event_id=None, event_name=None, event_type=None, instance_id=None, instance_name=None, max_delay_time=None, origin_begin_time=None, planned_begin_time=None, planned_end_time=None, planned_event_description=None, planned_event_reason=None, planned_switch_begin_time=None, planned_switch_end_time=None, region=None, status=None, _configuration=None):  # noqa: E501
        """PlannedEventForDescribePlannedEventsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._business_impact = None
        self._db_engine = None
        self._event_action = None
        self._event_id = None
        self._event_name = None
        self._event_type = None
        self._instance_id = None
        self._instance_name = None
        self._max_delay_time = None
        self._origin_begin_time = None
        self._planned_begin_time = None
        self._planned_end_time = None
        self._planned_event_description = None
        self._planned_event_reason = None
        self._planned_switch_begin_time = None
        self._planned_switch_end_time = None
        self._region = None
        self._status = None
        self.discriminator = None

        if business_impact is not None:
            self.business_impact = business_impact
        if db_engine is not None:
            self.db_engine = db_engine
        if event_action is not None:
            self.event_action = event_action
        if event_id is not None:
            self.event_id = event_id
        if event_name is not None:
            self.event_name = event_name
        if event_type is not None:
            self.event_type = event_type
        if instance_id is not None:
            self.instance_id = instance_id
        if instance_name is not None:
            self.instance_name = instance_name
        if max_delay_time is not None:
            self.max_delay_time = max_delay_time
        if origin_begin_time is not None:
            self.origin_begin_time = origin_begin_time
        if planned_begin_time is not None:
            self.planned_begin_time = planned_begin_time
        if planned_end_time is not None:
            self.planned_end_time = planned_end_time
        if planned_event_description is not None:
            self.planned_event_description = planned_event_description
        if planned_event_reason is not None:
            self.planned_event_reason = planned_event_reason
        if planned_switch_begin_time is not None:
            self.planned_switch_begin_time = planned_switch_begin_time
        if planned_switch_end_time is not None:
            self.planned_switch_end_time = planned_switch_end_time
        if region is not None:
            self.region = region
        if status is not None:
            self.status = status

    @property
    def business_impact(self):
        """Gets the business_impact of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The business_impact of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._business_impact

    @business_impact.setter
    def business_impact(self, business_impact):
        """Sets the business_impact of this PlannedEventForDescribePlannedEventsOutput.


        :param business_impact: The business_impact of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._business_impact = business_impact

    @property
    def db_engine(self):
        """Gets the db_engine of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The db_engine of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._db_engine

    @db_engine.setter
    def db_engine(self, db_engine):
        """Sets the db_engine of this PlannedEventForDescribePlannedEventsOutput.


        :param db_engine: The db_engine of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._db_engine = db_engine

    @property
    def event_action(self):
        """Gets the event_action of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The event_action of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_action

    @event_action.setter
    def event_action(self, event_action):
        """Sets the event_action of this PlannedEventForDescribePlannedEventsOutput.


        :param event_action: The event_action of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._event_action = event_action

    @property
    def event_id(self):
        """Gets the event_id of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The event_id of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_id

    @event_id.setter
    def event_id(self, event_id):
        """Sets the event_id of this PlannedEventForDescribePlannedEventsOutput.


        :param event_id: The event_id of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._event_id = event_id

    @property
    def event_name(self):
        """Gets the event_name of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The event_name of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_name

    @event_name.setter
    def event_name(self, event_name):
        """Sets the event_name of this PlannedEventForDescribePlannedEventsOutput.


        :param event_name: The event_name of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._event_name = event_name

    @property
    def event_type(self):
        """Gets the event_type of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The event_type of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_type

    @event_type.setter
    def event_type(self, event_type):
        """Sets the event_type of this PlannedEventForDescribePlannedEventsOutput.


        :param event_type: The event_type of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._event_type = event_type

    @property
    def instance_id(self):
        """Gets the instance_id of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The instance_id of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this PlannedEventForDescribePlannedEventsOutput.


        :param instance_id: The instance_id of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def instance_name(self):
        """Gets the instance_name of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The instance_name of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_name

    @instance_name.setter
    def instance_name(self, instance_name):
        """Sets the instance_name of this PlannedEventForDescribePlannedEventsOutput.


        :param instance_name: The instance_name of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._instance_name = instance_name

    @property
    def max_delay_time(self):
        """Gets the max_delay_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The max_delay_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._max_delay_time

    @max_delay_time.setter
    def max_delay_time(self, max_delay_time):
        """Sets the max_delay_time of this PlannedEventForDescribePlannedEventsOutput.


        :param max_delay_time: The max_delay_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._max_delay_time = max_delay_time

    @property
    def origin_begin_time(self):
        """Gets the origin_begin_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The origin_begin_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._origin_begin_time

    @origin_begin_time.setter
    def origin_begin_time(self, origin_begin_time):
        """Sets the origin_begin_time of this PlannedEventForDescribePlannedEventsOutput.


        :param origin_begin_time: The origin_begin_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._origin_begin_time = origin_begin_time

    @property
    def planned_begin_time(self):
        """Gets the planned_begin_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The planned_begin_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._planned_begin_time

    @planned_begin_time.setter
    def planned_begin_time(self, planned_begin_time):
        """Sets the planned_begin_time of this PlannedEventForDescribePlannedEventsOutput.


        :param planned_begin_time: The planned_begin_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._planned_begin_time = planned_begin_time

    @property
    def planned_end_time(self):
        """Gets the planned_end_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The planned_end_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._planned_end_time

    @planned_end_time.setter
    def planned_end_time(self, planned_end_time):
        """Sets the planned_end_time of this PlannedEventForDescribePlannedEventsOutput.


        :param planned_end_time: The planned_end_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._planned_end_time = planned_end_time

    @property
    def planned_event_description(self):
        """Gets the planned_event_description of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The planned_event_description of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._planned_event_description

    @planned_event_description.setter
    def planned_event_description(self, planned_event_description):
        """Sets the planned_event_description of this PlannedEventForDescribePlannedEventsOutput.


        :param planned_event_description: The planned_event_description of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._planned_event_description = planned_event_description

    @property
    def planned_event_reason(self):
        """Gets the planned_event_reason of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The planned_event_reason of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._planned_event_reason

    @planned_event_reason.setter
    def planned_event_reason(self, planned_event_reason):
        """Sets the planned_event_reason of this PlannedEventForDescribePlannedEventsOutput.


        :param planned_event_reason: The planned_event_reason of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._planned_event_reason = planned_event_reason

    @property
    def planned_switch_begin_time(self):
        """Gets the planned_switch_begin_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The planned_switch_begin_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._planned_switch_begin_time

    @planned_switch_begin_time.setter
    def planned_switch_begin_time(self, planned_switch_begin_time):
        """Sets the planned_switch_begin_time of this PlannedEventForDescribePlannedEventsOutput.


        :param planned_switch_begin_time: The planned_switch_begin_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._planned_switch_begin_time = planned_switch_begin_time

    @property
    def planned_switch_end_time(self):
        """Gets the planned_switch_end_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The planned_switch_end_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._planned_switch_end_time

    @planned_switch_end_time.setter
    def planned_switch_end_time(self, planned_switch_end_time):
        """Sets the planned_switch_end_time of this PlannedEventForDescribePlannedEventsOutput.


        :param planned_switch_end_time: The planned_switch_end_time of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._planned_switch_end_time = planned_switch_end_time

    @property
    def region(self):
        """Gets the region of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The region of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this PlannedEventForDescribePlannedEventsOutput.


        :param region: The region of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def status(self):
        """Gets the status of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501


        :return: The status of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this PlannedEventForDescribePlannedEventsOutput.


        :param status: The status of this PlannedEventForDescribePlannedEventsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlannedEventForDescribePlannedEventsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlannedEventForDescribePlannedEventsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlannedEventForDescribePlannedEventsOutput):
            return True

        return self.to_dict() != other.to_dict()
