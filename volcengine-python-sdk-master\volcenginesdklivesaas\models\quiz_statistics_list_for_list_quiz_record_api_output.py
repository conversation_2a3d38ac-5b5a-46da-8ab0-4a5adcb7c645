# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class QuizStatisticsListForListQuizRecordAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time': 'int',
        'email': 'str',
        'external_id': 'str',
        'extra': 'str',
        'ip': 'str',
        'is_right': 'bool',
        'submit_options': 'list[str]',
        'user_agent': 'str',
        'user_id': 'int',
        'user_name': 'str',
        'user_tel': 'str'
    }

    attribute_map = {
        'create_time': 'CreateTime',
        'email': 'Email',
        'external_id': 'ExternalId',
        'extra': 'Extra',
        'ip': 'Ip',
        'is_right': 'IsRight',
        'submit_options': 'SubmitOptions',
        'user_agent': 'UserAgent',
        'user_id': 'UserID',
        'user_name': 'UserName',
        'user_tel': 'UserTel'
    }

    def __init__(self, create_time=None, email=None, external_id=None, extra=None, ip=None, is_right=None, submit_options=None, user_agent=None, user_id=None, user_name=None, user_tel=None, _configuration=None):  # noqa: E501
        """QuizStatisticsListForListQuizRecordAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time = None
        self._email = None
        self._external_id = None
        self._extra = None
        self._ip = None
        self._is_right = None
        self._submit_options = None
        self._user_agent = None
        self._user_id = None
        self._user_name = None
        self._user_tel = None
        self.discriminator = None

        if create_time is not None:
            self.create_time = create_time
        if email is not None:
            self.email = email
        if external_id is not None:
            self.external_id = external_id
        if extra is not None:
            self.extra = extra
        if ip is not None:
            self.ip = ip
        if is_right is not None:
            self.is_right = is_right
        if submit_options is not None:
            self.submit_options = submit_options
        if user_agent is not None:
            self.user_agent = user_agent
        if user_id is not None:
            self.user_id = user_id
        if user_name is not None:
            self.user_name = user_name
        if user_tel is not None:
            self.user_tel = user_tel

    @property
    def create_time(self):
        """Gets the create_time of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The create_time of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param create_time: The create_time of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: int
        """

        self._create_time = create_time

    @property
    def email(self):
        """Gets the email of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The email of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._email

    @email.setter
    def email(self, email):
        """Sets the email of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param email: The email of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._email = email

    @property
    def external_id(self):
        """Gets the external_id of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The external_id of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._external_id

    @external_id.setter
    def external_id(self, external_id):
        """Sets the external_id of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param external_id: The external_id of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._external_id = external_id

    @property
    def extra(self):
        """Gets the extra of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The extra of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._extra

    @extra.setter
    def extra(self, extra):
        """Sets the extra of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param extra: The extra of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._extra = extra

    @property
    def ip(self):
        """Gets the ip of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The ip of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param ip: The ip of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def is_right(self):
        """Gets the is_right of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The is_right of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_right

    @is_right.setter
    def is_right(self, is_right):
        """Sets the is_right of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param is_right: The is_right of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: bool
        """

        self._is_right = is_right

    @property
    def submit_options(self):
        """Gets the submit_options of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The submit_options of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._submit_options

    @submit_options.setter
    def submit_options(self, submit_options):
        """Sets the submit_options of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param submit_options: The submit_options of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: list[str]
        """

        self._submit_options = submit_options

    @property
    def user_agent(self):
        """Gets the user_agent of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The user_agent of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_agent

    @user_agent.setter
    def user_agent(self, user_agent):
        """Sets the user_agent of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param user_agent: The user_agent of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_agent = user_agent

    @property
    def user_id(self):
        """Gets the user_id of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The user_id of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param user_id: The user_id of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    @property
    def user_name(self):
        """Gets the user_name of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The user_name of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_name

    @user_name.setter
    def user_name(self, user_name):
        """Sets the user_name of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param user_name: The user_name of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_name = user_name

    @property
    def user_tel(self):
        """Gets the user_tel of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501


        :return: The user_tel of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._user_tel

    @user_tel.setter
    def user_tel(self, user_tel):
        """Sets the user_tel of this QuizStatisticsListForListQuizRecordAPIOutput.


        :param user_tel: The user_tel of this QuizStatisticsListForListQuizRecordAPIOutput.  # noqa: E501
        :type: str
        """

        self._user_tel = user_tel

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(QuizStatisticsListForListQuizRecordAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, QuizStatisticsListForListQuizRecordAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, QuizStatisticsListForListQuizRecordAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
