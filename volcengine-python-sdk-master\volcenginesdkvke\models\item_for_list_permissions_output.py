# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ItemForListPermissionsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'authorized_at': 'str',
        'authorizer_id': 'int',
        'authorizer_name': 'str',
        'authorizer_type': 'str',
        'cluster_id': 'str',
        'granted_at': 'str',
        'grantee_id': 'int',
        'grantee_type': 'str',
        'id': 'str',
        'is_custom_role': 'bool',
        'kube_role_binding_name': 'str',
        'message': 'str',
        'namespace': 'str',
        'project_selector': 'str',
        'revoked_at': 'str',
        'role_name': 'str',
        'status': 'str'
    }

    attribute_map = {
        'authorized_at': 'AuthorizedAt',
        'authorizer_id': 'AuthorizerId',
        'authorizer_name': 'AuthorizerName',
        'authorizer_type': 'AuthorizerType',
        'cluster_id': 'ClusterId',
        'granted_at': 'GrantedAt',
        'grantee_id': 'GranteeId',
        'grantee_type': 'GranteeType',
        'id': 'Id',
        'is_custom_role': 'IsCustomRole',
        'kube_role_binding_name': 'KubeRoleBindingName',
        'message': 'Message',
        'namespace': 'Namespace',
        'project_selector': 'ProjectSelector',
        'revoked_at': 'RevokedAt',
        'role_name': 'RoleName',
        'status': 'Status'
    }

    def __init__(self, authorized_at=None, authorizer_id=None, authorizer_name=None, authorizer_type=None, cluster_id=None, granted_at=None, grantee_id=None, grantee_type=None, id=None, is_custom_role=None, kube_role_binding_name=None, message=None, namespace=None, project_selector=None, revoked_at=None, role_name=None, status=None, _configuration=None):  # noqa: E501
        """ItemForListPermissionsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._authorized_at = None
        self._authorizer_id = None
        self._authorizer_name = None
        self._authorizer_type = None
        self._cluster_id = None
        self._granted_at = None
        self._grantee_id = None
        self._grantee_type = None
        self._id = None
        self._is_custom_role = None
        self._kube_role_binding_name = None
        self._message = None
        self._namespace = None
        self._project_selector = None
        self._revoked_at = None
        self._role_name = None
        self._status = None
        self.discriminator = None

        if authorized_at is not None:
            self.authorized_at = authorized_at
        if authorizer_id is not None:
            self.authorizer_id = authorizer_id
        if authorizer_name is not None:
            self.authorizer_name = authorizer_name
        if authorizer_type is not None:
            self.authorizer_type = authorizer_type
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if granted_at is not None:
            self.granted_at = granted_at
        if grantee_id is not None:
            self.grantee_id = grantee_id
        if grantee_type is not None:
            self.grantee_type = grantee_type
        if id is not None:
            self.id = id
        if is_custom_role is not None:
            self.is_custom_role = is_custom_role
        if kube_role_binding_name is not None:
            self.kube_role_binding_name = kube_role_binding_name
        if message is not None:
            self.message = message
        if namespace is not None:
            self.namespace = namespace
        if project_selector is not None:
            self.project_selector = project_selector
        if revoked_at is not None:
            self.revoked_at = revoked_at
        if role_name is not None:
            self.role_name = role_name
        if status is not None:
            self.status = status

    @property
    def authorized_at(self):
        """Gets the authorized_at of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The authorized_at of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._authorized_at

    @authorized_at.setter
    def authorized_at(self, authorized_at):
        """Sets the authorized_at of this ItemForListPermissionsOutput.


        :param authorized_at: The authorized_at of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._authorized_at = authorized_at

    @property
    def authorizer_id(self):
        """Gets the authorizer_id of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The authorizer_id of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._authorizer_id

    @authorizer_id.setter
    def authorizer_id(self, authorizer_id):
        """Sets the authorizer_id of this ItemForListPermissionsOutput.


        :param authorizer_id: The authorizer_id of this ItemForListPermissionsOutput.  # noqa: E501
        :type: int
        """

        self._authorizer_id = authorizer_id

    @property
    def authorizer_name(self):
        """Gets the authorizer_name of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The authorizer_name of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._authorizer_name

    @authorizer_name.setter
    def authorizer_name(self, authorizer_name):
        """Sets the authorizer_name of this ItemForListPermissionsOutput.


        :param authorizer_name: The authorizer_name of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._authorizer_name = authorizer_name

    @property
    def authorizer_type(self):
        """Gets the authorizer_type of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The authorizer_type of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._authorizer_type

    @authorizer_type.setter
    def authorizer_type(self, authorizer_type):
        """Sets the authorizer_type of this ItemForListPermissionsOutput.


        :param authorizer_type: The authorizer_type of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._authorizer_type = authorizer_type

    @property
    def cluster_id(self):
        """Gets the cluster_id of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The cluster_id of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this ItemForListPermissionsOutput.


        :param cluster_id: The cluster_id of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def granted_at(self):
        """Gets the granted_at of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The granted_at of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._granted_at

    @granted_at.setter
    def granted_at(self, granted_at):
        """Sets the granted_at of this ItemForListPermissionsOutput.


        :param granted_at: The granted_at of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._granted_at = granted_at

    @property
    def grantee_id(self):
        """Gets the grantee_id of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The grantee_id of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: int
        """
        return self._grantee_id

    @grantee_id.setter
    def grantee_id(self, grantee_id):
        """Sets the grantee_id of this ItemForListPermissionsOutput.


        :param grantee_id: The grantee_id of this ItemForListPermissionsOutput.  # noqa: E501
        :type: int
        """

        self._grantee_id = grantee_id

    @property
    def grantee_type(self):
        """Gets the grantee_type of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The grantee_type of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._grantee_type

    @grantee_type.setter
    def grantee_type(self, grantee_type):
        """Sets the grantee_type of this ItemForListPermissionsOutput.


        :param grantee_type: The grantee_type of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._grantee_type = grantee_type

    @property
    def id(self):
        """Gets the id of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The id of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this ItemForListPermissionsOutput.


        :param id: The id of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def is_custom_role(self):
        """Gets the is_custom_role of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The is_custom_role of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._is_custom_role

    @is_custom_role.setter
    def is_custom_role(self, is_custom_role):
        """Sets the is_custom_role of this ItemForListPermissionsOutput.


        :param is_custom_role: The is_custom_role of this ItemForListPermissionsOutput.  # noqa: E501
        :type: bool
        """

        self._is_custom_role = is_custom_role

    @property
    def kube_role_binding_name(self):
        """Gets the kube_role_binding_name of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The kube_role_binding_name of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._kube_role_binding_name

    @kube_role_binding_name.setter
    def kube_role_binding_name(self, kube_role_binding_name):
        """Sets the kube_role_binding_name of this ItemForListPermissionsOutput.


        :param kube_role_binding_name: The kube_role_binding_name of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._kube_role_binding_name = kube_role_binding_name

    @property
    def message(self):
        """Gets the message of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The message of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this ItemForListPermissionsOutput.


        :param message: The message of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def namespace(self):
        """Gets the namespace of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The namespace of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._namespace

    @namespace.setter
    def namespace(self, namespace):
        """Sets the namespace of this ItemForListPermissionsOutput.


        :param namespace: The namespace of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._namespace = namespace

    @property
    def project_selector(self):
        """Gets the project_selector of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The project_selector of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._project_selector

    @project_selector.setter
    def project_selector(self, project_selector):
        """Sets the project_selector of this ItemForListPermissionsOutput.


        :param project_selector: The project_selector of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._project_selector = project_selector

    @property
    def revoked_at(self):
        """Gets the revoked_at of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The revoked_at of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._revoked_at

    @revoked_at.setter
    def revoked_at(self, revoked_at):
        """Sets the revoked_at of this ItemForListPermissionsOutput.


        :param revoked_at: The revoked_at of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._revoked_at = revoked_at

    @property
    def role_name(self):
        """Gets the role_name of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The role_name of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._role_name

    @role_name.setter
    def role_name(self, role_name):
        """Sets the role_name of this ItemForListPermissionsOutput.


        :param role_name: The role_name of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._role_name = role_name

    @property
    def status(self):
        """Gets the status of this ItemForListPermissionsOutput.  # noqa: E501


        :return: The status of this ItemForListPermissionsOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ItemForListPermissionsOutput.


        :param status: The status of this ItemForListPermissionsOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ItemForListPermissionsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ItemForListPermissionsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ItemForListPermissionsOutput):
            return True

        return self.to_dict() != other.to_dict()
