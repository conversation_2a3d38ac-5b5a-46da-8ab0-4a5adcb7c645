# coding: utf-8

# flake8: noqa
"""
    cdn

    No description provided (generated by <PERSON>wagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkcdn.models.access_action_for_add_cdn_domain_input import AccessActionForAddCdnDomainInput
from volcenginesdkcdn.models.access_action_for_batch_update_cdn_config_input import AccessActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.access_action_for_describe_cdn_config_output import AccessActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.access_action_for_update_cdn_config_input import AccessActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.actions_for_add_cdn_domain_input import ActionsForAddCdnDomainInput
from volcenginesdkcdn.models.actions_for_batch_update_cdn_config_input import ActionsForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.actions_for_describe_cdn_config_output import ActionsForDescribeCdnConfigOutput
from volcenginesdkcdn.models.actions_for_update_cdn_config_input import ActionsForUpdateCdnConfigInput
from volcenginesdkcdn.models.add_cdn_domain_request import AddCdnDomainRequest
from volcenginesdkcdn.models.add_cdn_domain_response import AddCdnDomainResponse
from volcenginesdkcdn.models.add_certificate_request import AddCertificateRequest
from volcenginesdkcdn.models.add_certificate_response import AddCertificateResponse
from volcenginesdkcdn.models.add_resource_tags_request import AddResourceTagsRequest
from volcenginesdkcdn.models.add_resource_tags_response import AddResourceTagsResponse
from volcenginesdkcdn.models.add_shared_config_request import AddSharedConfigRequest
from volcenginesdkcdn.models.add_shared_config_response import AddSharedConfigResponse
from volcenginesdkcdn.models.allow_ip_access_rule_for_add_shared_config_input import AllowIpAccessRuleForAddSharedConfigInput
from volcenginesdkcdn.models.allow_ip_access_rule_for_describe_shared_config_output import AllowIpAccessRuleForDescribeSharedConfigOutput
from volcenginesdkcdn.models.allow_ip_access_rule_for_update_shared_config_input import AllowIpAccessRuleForUpdateSharedConfigInput
from volcenginesdkcdn.models.allow_referer_access_rule_for_add_shared_config_input import AllowRefererAccessRuleForAddSharedConfigInput
from volcenginesdkcdn.models.allow_referer_access_rule_for_describe_shared_config_output import AllowRefererAccessRuleForDescribeSharedConfigOutput
from volcenginesdkcdn.models.allow_referer_access_rule_for_update_shared_config_input import AllowRefererAccessRuleForUpdateSharedConfigInput
from volcenginesdkcdn.models.area_access_rule_for_add_cdn_domain_input import AreaAccessRuleForAddCdnDomainInput
from volcenginesdkcdn.models.area_access_rule_for_batch_update_cdn_config_input import AreaAccessRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.area_access_rule_for_describe_cdn_config_output import AreaAccessRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.area_access_rule_for_update_cdn_config_input import AreaAccessRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.auth_mode_config_for_add_cdn_domain_input import AuthModeConfigForAddCdnDomainInput
from volcenginesdkcdn.models.auth_mode_config_for_batch_update_cdn_config_input import AuthModeConfigForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.auth_mode_config_for_describe_cdn_config_output import AuthModeConfigForDescribeCdnConfigOutput
from volcenginesdkcdn.models.auth_mode_config_for_update_cdn_config_input import AuthModeConfigForUpdateCdnConfigInput
from volcenginesdkcdn.models.auth_response_config_for_add_cdn_domain_input import AuthResponseConfigForAddCdnDomainInput
from volcenginesdkcdn.models.auth_response_config_for_batch_update_cdn_config_input import AuthResponseConfigForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.auth_response_config_for_describe_cdn_config_output import AuthResponseConfigForDescribeCdnConfigOutput
from volcenginesdkcdn.models.auth_response_config_for_update_cdn_config_input import AuthResponseConfigForUpdateCdnConfigInput
from volcenginesdkcdn.models.bandwidth_limit_action_for_add_cdn_domain_input import BandwidthLimitActionForAddCdnDomainInput
from volcenginesdkcdn.models.bandwidth_limit_action_for_batch_update_cdn_config_input import BandwidthLimitActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.bandwidth_limit_action_for_describe_cdn_config_output import BandwidthLimitActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.bandwidth_limit_action_for_update_cdn_config_input import BandwidthLimitActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.bandwidth_limit_for_add_cdn_domain_input import BandwidthLimitForAddCdnDomainInput
from volcenginesdkcdn.models.bandwidth_limit_for_batch_update_cdn_config_input import BandwidthLimitForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.bandwidth_limit_for_describe_cdn_config_output import BandwidthLimitForDescribeCdnConfigOutput
from volcenginesdkcdn.models.bandwidth_limit_for_update_cdn_config_input import BandwidthLimitForUpdateCdnConfigInput
from volcenginesdkcdn.models.bandwidth_limit_rule_for_add_cdn_domain_input import BandwidthLimitRuleForAddCdnDomainInput
from volcenginesdkcdn.models.bandwidth_limit_rule_for_batch_update_cdn_config_input import BandwidthLimitRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.bandwidth_limit_rule_for_describe_cdn_config_output import BandwidthLimitRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.bandwidth_limit_rule_for_update_cdn_config_input import BandwidthLimitRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.batch_deploy_cert_request import BatchDeployCertRequest
from volcenginesdkcdn.models.batch_deploy_cert_response import BatchDeployCertResponse
from volcenginesdkcdn.models.batch_update_cdn_config_request import BatchUpdateCdnConfigRequest
from volcenginesdkcdn.models.batch_update_cdn_config_response import BatchUpdateCdnConfigResponse
from volcenginesdkcdn.models.block_action_for_add_cdn_domain_input import BlockActionForAddCdnDomainInput
from volcenginesdkcdn.models.block_action_for_batch_update_cdn_config_input import BlockActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.block_action_for_describe_cdn_config_output import BlockActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.block_action_for_update_cdn_config_input import BlockActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.block_rule_for_add_cdn_domain_input import BlockRuleForAddCdnDomainInput
from volcenginesdkcdn.models.block_rule_for_batch_update_cdn_config_input import BlockRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.block_rule_for_describe_cdn_config_output import BlockRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.block_rule_for_update_cdn_config_input import BlockRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.browser_cache_for_add_cdn_domain_input import BrowserCacheForAddCdnDomainInput
from volcenginesdkcdn.models.browser_cache_for_batch_update_cdn_config_input import BrowserCacheForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.browser_cache_for_describe_cdn_config_output import BrowserCacheForDescribeCdnConfigOutput
from volcenginesdkcdn.models.browser_cache_for_update_cdn_config_input import BrowserCacheForUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_action_for_add_cdn_domain_input import CacheActionForAddCdnDomainInput
from volcenginesdkcdn.models.cache_action_for_batch_update_cdn_config_input import CacheActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_action_for_describe_cdn_config_output import CacheActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cache_action_for_update_cdn_config_input import CacheActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_for_add_cdn_domain_input import CacheForAddCdnDomainInput
from volcenginesdkcdn.models.cache_for_batch_update_cdn_config_input import CacheForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_for_describe_cdn_config_output import CacheForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cache_for_update_cdn_config_input import CacheForUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_host_action_for_add_cdn_domain_input import CacheHostActionForAddCdnDomainInput
from volcenginesdkcdn.models.cache_host_action_for_batch_update_cdn_config_input import CacheHostActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_host_action_for_describe_cdn_config_output import CacheHostActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cache_host_action_for_update_cdn_config_input import CacheHostActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_host_for_add_cdn_domain_input import CacheHostForAddCdnDomainInput
from volcenginesdkcdn.models.cache_host_for_batch_update_cdn_config_input import CacheHostForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_host_for_describe_cdn_config_output import CacheHostForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cache_host_for_update_cdn_config_input import CacheHostForUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_host_rule_for_add_cdn_domain_input import CacheHostRuleForAddCdnDomainInput
from volcenginesdkcdn.models.cache_host_rule_for_batch_update_cdn_config_input import CacheHostRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_host_rule_for_describe_cdn_config_output import CacheHostRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cache_host_rule_for_update_cdn_config_input import CacheHostRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_key_action_for_add_cdn_domain_input import CacheKeyActionForAddCdnDomainInput
from volcenginesdkcdn.models.cache_key_action_for_batch_update_cdn_config_input import CacheKeyActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_key_action_for_describe_cdn_config_output import CacheKeyActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cache_key_action_for_update_cdn_config_input import CacheKeyActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_key_component_for_add_cdn_domain_input import CacheKeyComponentForAddCdnDomainInput
from volcenginesdkcdn.models.cache_key_component_for_batch_update_cdn_config_input import CacheKeyComponentForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_key_component_for_describe_cdn_config_output import CacheKeyComponentForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cache_key_component_for_update_cdn_config_input import CacheKeyComponentForUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_key_for_add_cdn_domain_input import CacheKeyForAddCdnDomainInput
from volcenginesdkcdn.models.cache_key_for_batch_update_cdn_config_input import CacheKeyForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cache_key_for_describe_cdn_config_output import CacheKeyForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cache_key_for_update_cdn_config_input import CacheKeyForUpdateCdnConfigInput
from volcenginesdkcdn.models.cert_check_for_add_cdn_domain_input import CertCheckForAddCdnDomainInput
from volcenginesdkcdn.models.cert_check_for_batch_update_cdn_config_input import CertCheckForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cert_check_for_describe_cdn_config_output import CertCheckForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cert_check_for_update_cdn_config_input import CertCheckForUpdateCdnConfigInput
from volcenginesdkcdn.models.cert_fingerprint_for_list_cdn_cert_info_output import CertFingerprintForListCdnCertInfoOutput
from volcenginesdkcdn.models.cert_fingerprint_for_list_cert_info_output import CertFingerprintForListCertInfoOutput
from volcenginesdkcdn.models.cert_info_for_add_cdn_domain_input import CertInfoForAddCdnDomainInput
from volcenginesdkcdn.models.cert_info_for_batch_update_cdn_config_input import CertInfoForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cert_info_for_describe_cdn_config_output import CertInfoForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cert_info_for_list_cdn_cert_info_output import CertInfoForListCdnCertInfoOutput
from volcenginesdkcdn.models.cert_info_for_list_cert_info_output import CertInfoForListCertInfoOutput
from volcenginesdkcdn.models.cert_info_for_update_cdn_config_input import CertInfoForUpdateCdnConfigInput
from volcenginesdkcdn.models.cert_info_list_for_add_cdn_domain_input import CertInfoListForAddCdnDomainInput
from volcenginesdkcdn.models.cert_info_list_for_batch_update_cdn_config_input import CertInfoListForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.cert_info_list_for_describe_cdn_config_output import CertInfoListForDescribeCdnConfigOutput
from volcenginesdkcdn.models.cert_info_list_for_update_cdn_config_input import CertInfoListForUpdateCdnConfigInput
from volcenginesdkcdn.models.cert_not_config_for_describe_cert_config_output import CertNotConfigForDescribeCertConfigOutput
from volcenginesdkcdn.models.certificate_for_add_cdn_domain_input import CertificateForAddCdnDomainInput
from volcenginesdkcdn.models.certificate_for_batch_update_cdn_config_input import CertificateForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.certificate_for_describe_cdn_config_output import CertificateForDescribeCdnConfigOutput
from volcenginesdkcdn.models.certificate_for_update_cdn_config_input import CertificateForUpdateCdnConfigInput
from volcenginesdkcdn.models.common_match_list_for_add_shared_config_input import CommonMatchListForAddSharedConfigInput
from volcenginesdkcdn.models.common_match_list_for_describe_shared_config_output import CommonMatchListForDescribeSharedConfigOutput
from volcenginesdkcdn.models.common_match_list_for_update_shared_config_input import CommonMatchListForUpdateSharedConfigInput
from volcenginesdkcdn.models.common_type_for_add_cdn_domain_input import CommonTypeForAddCdnDomainInput
from volcenginesdkcdn.models.common_type_for_add_shared_config_input import CommonTypeForAddSharedConfigInput
from volcenginesdkcdn.models.common_type_for_batch_update_cdn_config_input import CommonTypeForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.common_type_for_describe_cdn_config_output import CommonTypeForDescribeCdnConfigOutput
from volcenginesdkcdn.models.common_type_for_describe_shared_config_output import CommonTypeForDescribeSharedConfigOutput
from volcenginesdkcdn.models.common_type_for_update_cdn_config_input import CommonTypeForUpdateCdnConfigInput
from volcenginesdkcdn.models.common_type_for_update_shared_config_input import CommonTypeForUpdateSharedConfigInput
from volcenginesdkcdn.models.compression_action_for_add_cdn_domain_input import CompressionActionForAddCdnDomainInput
from volcenginesdkcdn.models.compression_action_for_batch_update_cdn_config_input import CompressionActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.compression_action_for_describe_cdn_config_output import CompressionActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.compression_action_for_update_cdn_config_input import CompressionActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.compression_for_add_cdn_domain_input import CompressionForAddCdnDomainInput
from volcenginesdkcdn.models.compression_for_batch_update_cdn_config_input import CompressionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.compression_for_describe_cdn_config_output import CompressionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.compression_for_update_cdn_config_input import CompressionForUpdateCdnConfigInput
from volcenginesdkcdn.models.compression_rule_for_add_cdn_domain_input import CompressionRuleForAddCdnDomainInput
from volcenginesdkcdn.models.compression_rule_for_batch_update_cdn_config_input import CompressionRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.compression_rule_for_describe_cdn_config_output import CompressionRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.compression_rule_for_update_cdn_config_input import CompressionRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.condition_for_add_cdn_domain_input import ConditionForAddCdnDomainInput
from volcenginesdkcdn.models.condition_for_batch_update_cdn_config_input import ConditionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.condition_for_describe_cdn_config_output import ConditionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.condition_for_update_cdn_config_input import ConditionForUpdateCdnConfigInput
from volcenginesdkcdn.models.condition_group_for_add_cdn_domain_input import ConditionGroupForAddCdnDomainInput
from volcenginesdkcdn.models.condition_group_for_batch_update_cdn_config_input import ConditionGroupForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.condition_group_for_describe_cdn_config_output import ConditionGroupForDescribeCdnConfigOutput
from volcenginesdkcdn.models.condition_group_for_update_cdn_config_input import ConditionGroupForUpdateCdnConfigInput
from volcenginesdkcdn.models.condition_rule_for_add_cdn_domain_input import ConditionRuleForAddCdnDomainInput
from volcenginesdkcdn.models.condition_rule_for_batch_update_cdn_config_input import ConditionRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.condition_rule_for_describe_cdn_config_output import ConditionRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.condition_rule_for_update_cdn_config_input import ConditionRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.conditional_origin_for_add_cdn_domain_input import ConditionalOriginForAddCdnDomainInput
from volcenginesdkcdn.models.conditional_origin_for_batch_update_cdn_config_input import ConditionalOriginForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.conditional_origin_for_describe_cdn_config_output import ConditionalOriginForDescribeCdnConfigOutput
from volcenginesdkcdn.models.conditional_origin_for_update_cdn_config_input import ConditionalOriginForUpdateCdnConfigInput
from volcenginesdkcdn.models.config_data_for_list_shared_config_output import ConfigDataForListSharedConfigOutput
from volcenginesdkcdn.models.configured_domain_detail_for_list_cdn_cert_info_output import ConfiguredDomainDetailForListCdnCertInfoOutput
from volcenginesdkcdn.models.configured_domain_detail_for_list_cert_info_output import ConfiguredDomainDetailForListCertInfoOutput
from volcenginesdkcdn.models.convert_cache_action_for_add_cdn_domain_input import ConvertCacheActionForAddCdnDomainInput
from volcenginesdkcdn.models.convert_cache_action_for_batch_update_cdn_config_input import ConvertCacheActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.convert_cache_action_for_describe_cdn_config_output import ConvertCacheActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.convert_cache_action_for_update_cdn_config_input import ConvertCacheActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.convert_condition_for_add_cdn_domain_input import ConvertConditionForAddCdnDomainInput
from volcenginesdkcdn.models.convert_condition_for_batch_update_cdn_config_input import ConvertConditionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.convert_condition_for_describe_cdn_config_output import ConvertConditionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.convert_condition_for_update_cdn_config_input import ConvertConditionForUpdateCdnConfigInput
from volcenginesdkcdn.models.convert_convert_condition_for_add_cdn_domain_input import ConvertConvertConditionForAddCdnDomainInput
from volcenginesdkcdn.models.convert_convert_condition_for_batch_update_cdn_config_input import ConvertConvertConditionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.convert_convert_condition_for_describe_cdn_config_output import ConvertConvertConditionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.convert_convert_condition_for_update_cdn_config_input import ConvertConvertConditionForUpdateCdnConfigInput
from volcenginesdkcdn.models.convert_origin_line_for_add_cdn_domain_input import ConvertOriginLineForAddCdnDomainInput
from volcenginesdkcdn.models.convert_origin_line_for_batch_update_cdn_config_input import ConvertOriginLineForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.convert_origin_line_for_describe_cdn_config_output import ConvertOriginLineForDescribeCdnConfigOutput
from volcenginesdkcdn.models.convert_origin_line_for_update_cdn_config_input import ConvertOriginLineForUpdateCdnConfigInput
from volcenginesdkcdn.models.convert_timeout_action_for_add_cdn_domain_input import ConvertTimeoutActionForAddCdnDomainInput
from volcenginesdkcdn.models.convert_timeout_action_for_batch_update_cdn_config_input import ConvertTimeoutActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.convert_timeout_action_for_describe_cdn_config_output import ConvertTimeoutActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.convert_timeout_action_for_update_cdn_config_input import ConvertTimeoutActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.create_usage_report_request import CreateUsageReportRequest
from volcenginesdkcdn.models.create_usage_report_response import CreateUsageReportResponse
from volcenginesdkcdn.models.custom_error_page_for_add_cdn_domain_input import CustomErrorPageForAddCdnDomainInput
from volcenginesdkcdn.models.custom_error_page_for_batch_update_cdn_config_input import CustomErrorPageForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.custom_error_page_for_describe_cdn_config_output import CustomErrorPageForDescribeCdnConfigOutput
from volcenginesdkcdn.models.custom_error_page_for_update_cdn_config_input import CustomErrorPageForUpdateCdnConfigInput
from volcenginesdkcdn.models.custom_variable_instance_for_add_cdn_domain_input import CustomVariableInstanceForAddCdnDomainInput
from volcenginesdkcdn.models.custom_variable_instance_for_batch_update_cdn_config_input import CustomVariableInstanceForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.custom_variable_instance_for_describe_cdn_config_output import CustomVariableInstanceForDescribeCdnConfigOutput
from volcenginesdkcdn.models.custom_variable_instance_for_update_cdn_config_input import CustomVariableInstanceForUpdateCdnConfigInput
from volcenginesdkcdn.models.custom_variable_rules_for_add_cdn_domain_input import CustomVariableRulesForAddCdnDomainInput
from volcenginesdkcdn.models.custom_variable_rules_for_batch_update_cdn_config_input import CustomVariableRulesForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.custom_variable_rules_for_describe_cdn_config_output import CustomVariableRulesForDescribeCdnConfigOutput
from volcenginesdkcdn.models.custom_variable_rules_for_update_cdn_config_input import CustomVariableRulesForUpdateCdnConfigInput
from volcenginesdkcdn.models.customize_access_rule_for_add_cdn_domain_input import CustomizeAccessRuleForAddCdnDomainInput
from volcenginesdkcdn.models.customize_access_rule_for_batch_update_cdn_config_input import CustomizeAccessRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.customize_access_rule_for_describe_cdn_config_output import CustomizeAccessRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.customize_access_rule_for_update_cdn_config_input import CustomizeAccessRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.customize_instance_for_add_cdn_domain_input import CustomizeInstanceForAddCdnDomainInput
from volcenginesdkcdn.models.customize_instance_for_batch_update_cdn_config_input import CustomizeInstanceForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.customize_instance_for_describe_cdn_config_output import CustomizeInstanceForDescribeCdnConfigOutput
from volcenginesdkcdn.models.customize_instance_for_update_cdn_config_input import CustomizeInstanceForUpdateCdnConfigInput
from volcenginesdkcdn.models.customize_rule_for_add_cdn_domain_input import CustomizeRuleForAddCdnDomainInput
from volcenginesdkcdn.models.customize_rule_for_batch_update_cdn_config_input import CustomizeRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.customize_rule_for_describe_cdn_config_output import CustomizeRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.customize_rule_for_update_cdn_config_input import CustomizeRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.data_for_describe_content_block_tasks_output import DataForDescribeContentBlockTasksOutput
from volcenginesdkcdn.models.data_for_describe_content_tasks_output import DataForDescribeContentTasksOutput
from volcenginesdkcdn.models.data_for_list_cdn_domains_output import DataForListCdnDomainsOutput
from volcenginesdkcdn.models.delete_cdn_certificate_request import DeleteCdnCertificateRequest
from volcenginesdkcdn.models.delete_cdn_certificate_response import DeleteCdnCertificateResponse
from volcenginesdkcdn.models.delete_cdn_domain_request import DeleteCdnDomainRequest
from volcenginesdkcdn.models.delete_cdn_domain_response import DeleteCdnDomainResponse
from volcenginesdkcdn.models.delete_resource_tags_request import DeleteResourceTagsRequest
from volcenginesdkcdn.models.delete_resource_tags_response import DeleteResourceTagsResponse
from volcenginesdkcdn.models.delete_shared_config_request import DeleteSharedConfigRequest
from volcenginesdkcdn.models.delete_shared_config_response import DeleteSharedConfigResponse
from volcenginesdkcdn.models.delete_usage_report_request import DeleteUsageReportRequest
from volcenginesdkcdn.models.delete_usage_report_response import DeleteUsageReportResponse
from volcenginesdkcdn.models.deny_ip_access_rule_for_add_shared_config_input import DenyIpAccessRuleForAddSharedConfigInput
from volcenginesdkcdn.models.deny_ip_access_rule_for_describe_shared_config_output import DenyIpAccessRuleForDescribeSharedConfigOutput
from volcenginesdkcdn.models.deny_ip_access_rule_for_update_shared_config_input import DenyIpAccessRuleForUpdateSharedConfigInput
from volcenginesdkcdn.models.deny_referer_access_rule_for_add_shared_config_input import DenyRefererAccessRuleForAddSharedConfigInput
from volcenginesdkcdn.models.deny_referer_access_rule_for_describe_shared_config_output import DenyRefererAccessRuleForDescribeSharedConfigOutput
from volcenginesdkcdn.models.deny_referer_access_rule_for_update_shared_config_input import DenyRefererAccessRuleForUpdateSharedConfigInput
from volcenginesdkcdn.models.deploy_result_for_batch_deploy_cert_output import DeployResultForBatchDeployCertOutput
from volcenginesdkcdn.models.deploy_result_for_batch_update_cdn_config_output import DeployResultForBatchUpdateCdnConfigOutput
from volcenginesdkcdn.models.describe_billing_detail_request import DescribeBillingDetailRequest
from volcenginesdkcdn.models.describe_billing_detail_response import DescribeBillingDetailResponse
from volcenginesdkcdn.models.describe_cdn_access_log_request import DescribeCdnAccessLogRequest
from volcenginesdkcdn.models.describe_cdn_access_log_response import DescribeCdnAccessLogResponse
from volcenginesdkcdn.models.describe_cdn_config_request import DescribeCdnConfigRequest
from volcenginesdkcdn.models.describe_cdn_config_response import DescribeCdnConfigResponse
from volcenginesdkcdn.models.describe_cdn_edge_ip_request import DescribeCdnEdgeIpRequest
from volcenginesdkcdn.models.describe_cdn_edge_ip_response import DescribeCdnEdgeIpResponse
from volcenginesdkcdn.models.describe_cdn_ip_request import DescribeCdnIPRequest
from volcenginesdkcdn.models.describe_cdn_ip_response import DescribeCdnIPResponse
from volcenginesdkcdn.models.describe_cdn_region_and_isp_request import DescribeCdnRegionAndIspRequest
from volcenginesdkcdn.models.describe_cdn_region_and_isp_response import DescribeCdnRegionAndIspResponse
from volcenginesdkcdn.models.describe_cdn_service_request import DescribeCdnServiceRequest
from volcenginesdkcdn.models.describe_cdn_service_response import DescribeCdnServiceResponse
from volcenginesdkcdn.models.describe_cdn_upper_ip_request import DescribeCdnUpperIpRequest
from volcenginesdkcdn.models.describe_cdn_upper_ip_response import DescribeCdnUpperIpResponse
from volcenginesdkcdn.models.describe_cert_config_request import DescribeCertConfigRequest
from volcenginesdkcdn.models.describe_cert_config_response import DescribeCertConfigResponse
from volcenginesdkcdn.models.describe_content_block_tasks_request import DescribeContentBlockTasksRequest
from volcenginesdkcdn.models.describe_content_block_tasks_response import DescribeContentBlockTasksResponse
from volcenginesdkcdn.models.describe_content_quota_request import DescribeContentQuotaRequest
from volcenginesdkcdn.models.describe_content_quota_response import DescribeContentQuotaResponse
from volcenginesdkcdn.models.describe_content_tasks_request import DescribeContentTasksRequest
from volcenginesdkcdn.models.describe_content_tasks_response import DescribeContentTasksResponse
from volcenginesdkcdn.models.describe_district_data_request import DescribeDistrictDataRequest
from volcenginesdkcdn.models.describe_district_data_response import DescribeDistrictDataResponse
from volcenginesdkcdn.models.describe_district_ranking_request import DescribeDistrictRankingRequest
from volcenginesdkcdn.models.describe_district_ranking_response import DescribeDistrictRankingResponse
from volcenginesdkcdn.models.describe_district_summary_request import DescribeDistrictSummaryRequest
from volcenginesdkcdn.models.describe_district_summary_response import DescribeDistrictSummaryResponse
from volcenginesdkcdn.models.describe_edge_data_request import DescribeEdgeDataRequest
from volcenginesdkcdn.models.describe_edge_data_response import DescribeEdgeDataResponse
from volcenginesdkcdn.models.describe_edge_ranking_request import DescribeEdgeRankingRequest
from volcenginesdkcdn.models.describe_edge_ranking_response import DescribeEdgeRankingResponse
from volcenginesdkcdn.models.describe_edge_status_code_ranking_request import DescribeEdgeStatusCodeRankingRequest
from volcenginesdkcdn.models.describe_edge_status_code_ranking_response import DescribeEdgeStatusCodeRankingResponse
from volcenginesdkcdn.models.describe_edge_summary_request import DescribeEdgeSummaryRequest
from volcenginesdkcdn.models.describe_edge_summary_response import DescribeEdgeSummaryResponse
from volcenginesdkcdn.models.describe_origin_data_request import DescribeOriginDataRequest
from volcenginesdkcdn.models.describe_origin_data_response import DescribeOriginDataResponse
from volcenginesdkcdn.models.describe_origin_ranking_request import DescribeOriginRankingRequest
from volcenginesdkcdn.models.describe_origin_ranking_response import DescribeOriginRankingResponse
from volcenginesdkcdn.models.describe_origin_status_code_ranking_request import DescribeOriginStatusCodeRankingRequest
from volcenginesdkcdn.models.describe_origin_status_code_ranking_response import DescribeOriginStatusCodeRankingResponse
from volcenginesdkcdn.models.describe_origin_summary_request import DescribeOriginSummaryRequest
from volcenginesdkcdn.models.describe_origin_summary_response import DescribeOriginSummaryResponse
from volcenginesdkcdn.models.describe_shared_config_request import DescribeSharedConfigRequest
from volcenginesdkcdn.models.describe_shared_config_response import DescribeSharedConfigResponse
from volcenginesdkcdn.models.describe_statistical_ranking_request import DescribeStatisticalRankingRequest
from volcenginesdkcdn.models.describe_statistical_ranking_response import DescribeStatisticalRankingResponse
from volcenginesdkcdn.models.describe_user_data_request import DescribeUserDataRequest
from volcenginesdkcdn.models.describe_user_data_response import DescribeUserDataResponse
from volcenginesdkcdn.models.domain_config_for_describe_cdn_config_output import DomainConfigForDescribeCdnConfigOutput
from volcenginesdkcdn.models.domain_lock_for_describe_cert_config_output import DomainLockForDescribeCertConfigOutput
from volcenginesdkcdn.models.domain_lock_for_list_cdn_domains_output import DomainLockForListCdnDomainsOutput
from volcenginesdkcdn.models.domain_log_detail_for_describe_cdn_access_log_output import DomainLogDetailForDescribeCdnAccessLogOutput
from volcenginesdkcdn.models.download_speed_limit_action_for_add_cdn_domain_input import DownloadSpeedLimitActionForAddCdnDomainInput
from volcenginesdkcdn.models.download_speed_limit_action_for_batch_update_cdn_config_input import DownloadSpeedLimitActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.download_speed_limit_action_for_describe_cdn_config_output import DownloadSpeedLimitActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.download_speed_limit_action_for_update_cdn_config_input import DownloadSpeedLimitActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.download_speed_limit_for_add_cdn_domain_input import DownloadSpeedLimitForAddCdnDomainInput
from volcenginesdkcdn.models.download_speed_limit_for_batch_update_cdn_config_input import DownloadSpeedLimitForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.download_speed_limit_for_describe_cdn_config_output import DownloadSpeedLimitForDescribeCdnConfigOutput
from volcenginesdkcdn.models.download_speed_limit_for_update_cdn_config_input import DownloadSpeedLimitForUpdateCdnConfigInput
from volcenginesdkcdn.models.download_speed_limit_rule_for_add_cdn_domain_input import DownloadSpeedLimitRuleForAddCdnDomainInput
from volcenginesdkcdn.models.download_speed_limit_rule_for_batch_update_cdn_config_input import DownloadSpeedLimitRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.download_speed_limit_rule_for_describe_cdn_config_output import DownloadSpeedLimitRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.download_speed_limit_rule_for_update_cdn_config_input import DownloadSpeedLimitRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.error_page_action_for_add_cdn_domain_input import ErrorPageActionForAddCdnDomainInput
from volcenginesdkcdn.models.error_page_action_for_batch_update_cdn_config_input import ErrorPageActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.error_page_action_for_describe_cdn_config_output import ErrorPageActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.error_page_action_for_update_cdn_config_input import ErrorPageActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.error_page_rule_for_add_cdn_domain_input import ErrorPageRuleForAddCdnDomainInput
from volcenginesdkcdn.models.error_page_rule_for_batch_update_cdn_config_input import ErrorPageRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.error_page_rule_for_describe_cdn_config_output import ErrorPageRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.error_page_rule_for_describe_shared_config_output import ErrorPageRuleForDescribeSharedConfigOutput
from volcenginesdkcdn.models.error_page_rule_for_update_cdn_config_input import ErrorPageRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.exp_time_cap_rule_for_add_cdn_domain_input import ExpTimeCapRuleForAddCdnDomainInput
from volcenginesdkcdn.models.exp_time_cap_rule_for_batch_update_cdn_config_input import ExpTimeCapRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.exp_time_cap_rule_for_describe_cdn_config_output import ExpTimeCapRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.exp_time_cap_rule_for_update_cdn_config_input import ExpTimeCapRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.feature_config_for_describe_cdn_config_output import FeatureConfigForDescribeCdnConfigOutput
from volcenginesdkcdn.models.feature_config_for_list_cdn_domains_output import FeatureConfigForListCdnDomainsOutput
from volcenginesdkcdn.models.forced_redirect_for_add_cdn_domain_input import ForcedRedirectForAddCdnDomainInput
from volcenginesdkcdn.models.forced_redirect_for_batch_update_cdn_config_input import ForcedRedirectForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.forced_redirect_for_describe_cdn_config_output import ForcedRedirectForDescribeCdnConfigOutput
from volcenginesdkcdn.models.forced_redirect_for_update_cdn_config_input import ForcedRedirectForUpdateCdnConfigInput
from volcenginesdkcdn.models.https_for_add_cdn_domain_input import HTTPSForAddCdnDomainInput
from volcenginesdkcdn.models.https_for_batch_update_cdn_config_input import HTTPSForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.https_for_describe_cdn_config_output import HTTPSForDescribeCdnConfigOutput
from volcenginesdkcdn.models.https_for_update_cdn_config_input import HTTPSForUpdateCdnConfigInput
from volcenginesdkcdn.models.hsts_for_add_cdn_domain_input import HstsForAddCdnDomainInput
from volcenginesdkcdn.models.hsts_for_batch_update_cdn_config_input import HstsForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.hsts_for_describe_cdn_config_output import HstsForDescribeCdnConfigOutput
from volcenginesdkcdn.models.hsts_for_update_cdn_config_input import HstsForUpdateCdnConfigInput
from volcenginesdkcdn.models.http_forced_redirect_for_add_cdn_domain_input import HttpForcedRedirectForAddCdnDomainInput
from volcenginesdkcdn.models.http_forced_redirect_for_batch_update_cdn_config_input import HttpForcedRedirectForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.http_forced_redirect_for_describe_cdn_config_output import HttpForcedRedirectForDescribeCdnConfigOutput
from volcenginesdkcdn.models.http_forced_redirect_for_update_cdn_config_input import HttpForcedRedirectForUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_for_describe_cdn_ip_output import IPForDescribeCdnIPOutput
from volcenginesdkcdn.models.ipv6_for_add_cdn_domain_input import IPv6ForAddCdnDomainInput
from volcenginesdkcdn.models.ipv6_for_batch_update_cdn_config_input import IPv6ForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.ipv6_for_describe_cdn_config_output import IPv6ForDescribeCdnConfigOutput
from volcenginesdkcdn.models.ipv6_for_update_cdn_config_input import IPv6ForUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_access_rule_for_add_cdn_domain_input import IpAccessRuleForAddCdnDomainInput
from volcenginesdkcdn.models.ip_access_rule_for_batch_update_cdn_config_input import IpAccessRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_access_rule_for_describe_cdn_config_output import IpAccessRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.ip_access_rule_for_update_cdn_config_input import IpAccessRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_freq_limit_action_for_add_cdn_domain_input import IpFreqLimitActionForAddCdnDomainInput
from volcenginesdkcdn.models.ip_freq_limit_action_for_batch_update_cdn_config_input import IpFreqLimitActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_freq_limit_action_for_describe_cdn_config_output import IpFreqLimitActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.ip_freq_limit_action_for_update_cdn_config_input import IpFreqLimitActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_freq_limit_for_add_cdn_domain_input import IpFreqLimitForAddCdnDomainInput
from volcenginesdkcdn.models.ip_freq_limit_for_batch_update_cdn_config_input import IpFreqLimitForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_freq_limit_for_describe_cdn_config_output import IpFreqLimitForDescribeCdnConfigOutput
from volcenginesdkcdn.models.ip_freq_limit_for_update_cdn_config_input import IpFreqLimitForUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_freq_limit_rule_for_add_cdn_domain_input import IpFreqLimitRuleForAddCdnDomainInput
from volcenginesdkcdn.models.ip_freq_limit_rule_for_batch_update_cdn_config_input import IpFreqLimitRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_freq_limit_rule_for_describe_cdn_config_output import IpFreqLimitRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.ip_freq_limit_rule_for_update_cdn_config_input import IpFreqLimitRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.ip_speed_limit_action_for_describe_cdn_config_output import IpSpeedLimitActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.ip_speed_limit_for_describe_cdn_config_output import IpSpeedLimitForDescribeCdnConfigOutput
from volcenginesdkcdn.models.ip_speed_limit_rule_for_describe_cdn_config_output import IpSpeedLimitRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.isp_for_describe_cdn_region_and_isp_output import IspForDescribeCdnRegionAndIspOutput
from volcenginesdkcdn.models.list_cdn_cert_info_request import ListCdnCertInfoRequest
from volcenginesdkcdn.models.list_cdn_cert_info_response import ListCdnCertInfoResponse
from volcenginesdkcdn.models.list_cdn_domains_request import ListCdnDomainsRequest
from volcenginesdkcdn.models.list_cdn_domains_response import ListCdnDomainsResponse
from volcenginesdkcdn.models.list_cert_info_request import ListCertInfoRequest
from volcenginesdkcdn.models.list_cert_info_response import ListCertInfoResponse
from volcenginesdkcdn.models.list_resource_tags_request import ListResourceTagsRequest
from volcenginesdkcdn.models.list_resource_tags_response import ListResourceTagsResponse
from volcenginesdkcdn.models.list_shared_config_request import ListSharedConfigRequest
from volcenginesdkcdn.models.list_shared_config_response import ListSharedConfigResponse
from volcenginesdkcdn.models.list_usage_reports_request import ListUsageReportsRequest
from volcenginesdkcdn.models.list_usage_reports_response import ListUsageReportsResponse
from volcenginesdkcdn.models.method_denied_rule_for_add_cdn_domain_input import MethodDeniedRuleForAddCdnDomainInput
from volcenginesdkcdn.models.method_denied_rule_for_batch_update_cdn_config_input import MethodDeniedRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.method_denied_rule_for_describe_cdn_config_output import MethodDeniedRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.method_denied_rule_for_update_cdn_config_input import MethodDeniedRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.metric_data_list_for_describe_district_data_output import MetricDataListForDescribeDistrictDataOutput
from volcenginesdkcdn.models.metric_data_list_for_describe_district_summary_output import MetricDataListForDescribeDistrictSummaryOutput
from volcenginesdkcdn.models.metric_data_list_for_describe_edge_data_output import MetricDataListForDescribeEdgeDataOutput
from volcenginesdkcdn.models.metric_data_list_for_describe_edge_summary_output import MetricDataListForDescribeEdgeSummaryOutput
from volcenginesdkcdn.models.metric_data_list_for_describe_origin_data_output import MetricDataListForDescribeOriginDataOutput
from volcenginesdkcdn.models.metric_data_list_for_describe_origin_summary_output import MetricDataListForDescribeOriginSummaryOutput
from volcenginesdkcdn.models.metric_data_list_for_describe_user_data_output import MetricDataListForDescribeUserDataOutput
from volcenginesdkcdn.models.module_lock_config_for_describe_cdn_config_output import ModuleLockConfigForDescribeCdnConfigOutput
from volcenginesdkcdn.models.multi_range_for_add_cdn_domain_input import MultiRangeForAddCdnDomainInput
from volcenginesdkcdn.models.multi_range_for_batch_update_cdn_config_input import MultiRangeForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.multi_range_for_describe_cdn_config_output import MultiRangeForDescribeCdnConfigOutput
from volcenginesdkcdn.models.multi_range_for_update_cdn_config_input import MultiRangeForUpdateCdnConfigInput
from volcenginesdkcdn.models.negative_cache_for_add_cdn_domain_input import NegativeCacheForAddCdnDomainInput
from volcenginesdkcdn.models.negative_cache_for_batch_update_cdn_config_input import NegativeCacheForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.negative_cache_for_describe_cdn_config_output import NegativeCacheForDescribeCdnConfigOutput
from volcenginesdkcdn.models.negative_cache_for_update_cdn_config_input import NegativeCacheForUpdateCdnConfigInput
from volcenginesdkcdn.models.negative_cache_rule_for_add_cdn_domain_input import NegativeCacheRuleForAddCdnDomainInput
from volcenginesdkcdn.models.negative_cache_rule_for_batch_update_cdn_config_input import NegativeCacheRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.negative_cache_rule_for_describe_cdn_config_output import NegativeCacheRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.negative_cache_rule_for_update_cdn_config_input import NegativeCacheRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.offline_cache_for_add_cdn_domain_input import OfflineCacheForAddCdnDomainInput
from volcenginesdkcdn.models.offline_cache_for_batch_update_cdn_config_input import OfflineCacheForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.offline_cache_for_describe_cdn_config_output import OfflineCacheForDescribeCdnConfigOutput
from volcenginesdkcdn.models.offline_cache_for_update_cdn_config_input import OfflineCacheForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_access_rule_for_add_cdn_domain_input import OriginAccessRuleForAddCdnDomainInput
from volcenginesdkcdn.models.origin_access_rule_for_batch_update_cdn_config_input import OriginAccessRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_access_rule_for_describe_cdn_config_output import OriginAccessRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_access_rule_for_update_cdn_config_input import OriginAccessRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_action_for_add_cdn_domain_input import OriginActionForAddCdnDomainInput
from volcenginesdkcdn.models.origin_action_for_batch_update_cdn_config_input import OriginActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_action_for_describe_cdn_config_output import OriginActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_action_for_update_cdn_config_input import OriginActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_arg_action_for_add_cdn_domain_input import OriginArgActionForAddCdnDomainInput
from volcenginesdkcdn.models.origin_arg_action_for_batch_update_cdn_config_input import OriginArgActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_arg_action_for_describe_cdn_config_output import OriginArgActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_arg_action_for_update_cdn_config_input import OriginArgActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_arg_component_for_add_cdn_domain_input import OriginArgComponentForAddCdnDomainInput
from volcenginesdkcdn.models.origin_arg_component_for_batch_update_cdn_config_input import OriginArgComponentForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_arg_component_for_describe_cdn_config_output import OriginArgComponentForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_arg_component_for_update_cdn_config_input import OriginArgComponentForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_arg_for_add_cdn_domain_input import OriginArgForAddCdnDomainInput
from volcenginesdkcdn.models.origin_arg_for_batch_update_cdn_config_input import OriginArgForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_arg_for_describe_cdn_config_output import OriginArgForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_arg_for_update_cdn_config_input import OriginArgForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_cert_check_for_add_cdn_domain_input import OriginCertCheckForAddCdnDomainInput
from volcenginesdkcdn.models.origin_cert_check_for_batch_update_cdn_config_input import OriginCertCheckForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_cert_check_for_describe_cdn_config_output import OriginCertCheckForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_cert_check_for_update_cdn_config_input import OriginCertCheckForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_for_add_cdn_domain_input import OriginForAddCdnDomainInput
from volcenginesdkcdn.models.origin_for_batch_update_cdn_config_input import OriginForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_for_describe_cdn_config_output import OriginForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_for_update_cdn_config_input import OriginForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_line_for_add_cdn_domain_input import OriginLineForAddCdnDomainInput
from volcenginesdkcdn.models.origin_line_for_batch_update_cdn_config_input import OriginLineForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_line_for_describe_cdn_config_output import OriginLineForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_line_for_update_cdn_config_input import OriginLineForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_retry_for_add_cdn_domain_input import OriginRetryForAddCdnDomainInput
from volcenginesdkcdn.models.origin_retry_for_batch_update_cdn_config_input import OriginRetryForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_retry_for_describe_cdn_config_output import OriginRetryForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_retry_for_update_cdn_config_input import OriginRetryForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_rewrite_action_for_add_cdn_domain_input import OriginRewriteActionForAddCdnDomainInput
from volcenginesdkcdn.models.origin_rewrite_action_for_batch_update_cdn_config_input import OriginRewriteActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_rewrite_action_for_describe_cdn_config_output import OriginRewriteActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_rewrite_action_for_update_cdn_config_input import OriginRewriteActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_rewrite_for_add_cdn_domain_input import OriginRewriteForAddCdnDomainInput
from volcenginesdkcdn.models.origin_rewrite_for_batch_update_cdn_config_input import OriginRewriteForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_rewrite_for_describe_cdn_config_output import OriginRewriteForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_rewrite_for_update_cdn_config_input import OriginRewriteForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_rewrite_rule_for_add_cdn_domain_input import OriginRewriteRuleForAddCdnDomainInput
from volcenginesdkcdn.models.origin_rewrite_rule_for_batch_update_cdn_config_input import OriginRewriteRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_rewrite_rule_for_describe_cdn_config_output import OriginRewriteRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_rewrite_rule_for_update_cdn_config_input import OriginRewriteRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_rule_for_add_cdn_domain_input import OriginRuleForAddCdnDomainInput
from volcenginesdkcdn.models.origin_rule_for_batch_update_cdn_config_input import OriginRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_rule_for_describe_cdn_config_output import OriginRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_rule_for_update_cdn_config_input import OriginRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_sni_for_add_cdn_domain_input import OriginSniForAddCdnDomainInput
from volcenginesdkcdn.models.origin_sni_for_batch_update_cdn_config_input import OriginSniForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.origin_sni_for_describe_cdn_config_output import OriginSniForDescribeCdnConfigOutput
from volcenginesdkcdn.models.origin_sni_for_update_cdn_config_input import OriginSniForUpdateCdnConfigInput
from volcenginesdkcdn.models.other_cert_config_for_describe_cert_config_output import OtherCertConfigForDescribeCertConfigOutput
from volcenginesdkcdn.models.page_optimization_for_add_cdn_domain_input import PageOptimizationForAddCdnDomainInput
from volcenginesdkcdn.models.page_optimization_for_batch_update_cdn_config_input import PageOptimizationForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.page_optimization_for_describe_cdn_config_output import PageOptimizationForDescribeCdnConfigOutput
from volcenginesdkcdn.models.page_optimization_for_update_cdn_config_input import PageOptimizationForUpdateCdnConfigInput
from volcenginesdkcdn.models.private_bucket_auth_for_add_cdn_domain_input import PrivateBucketAuthForAddCdnDomainInput
from volcenginesdkcdn.models.private_bucket_auth_for_batch_update_cdn_config_input import PrivateBucketAuthForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.private_bucket_auth_for_describe_cdn_config_output import PrivateBucketAuthForDescribeCdnConfigOutput
from volcenginesdkcdn.models.private_bucket_auth_for_update_cdn_config_input import PrivateBucketAuthForUpdateCdnConfigInput
from volcenginesdkcdn.models.query_string_components_for_add_cdn_domain_input import QueryStringComponentsForAddCdnDomainInput
from volcenginesdkcdn.models.query_string_components_for_batch_update_cdn_config_input import QueryStringComponentsForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.query_string_components_for_describe_cdn_config_output import QueryStringComponentsForDescribeCdnConfigOutput
from volcenginesdkcdn.models.query_string_components_for_update_cdn_config_input import QueryStringComponentsForUpdateCdnConfigInput
from volcenginesdkcdn.models.query_string_instance_for_add_cdn_domain_input import QueryStringInstanceForAddCdnDomainInput
from volcenginesdkcdn.models.query_string_instance_for_batch_update_cdn_config_input import QueryStringInstanceForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.query_string_instance_for_describe_cdn_config_output import QueryStringInstanceForDescribeCdnConfigOutput
from volcenginesdkcdn.models.query_string_instance_for_update_cdn_config_input import QueryStringInstanceForUpdateCdnConfigInput
from volcenginesdkcdn.models.query_string_rules_for_add_cdn_domain_input import QueryStringRulesForAddCdnDomainInput
from volcenginesdkcdn.models.query_string_rules_for_batch_update_cdn_config_input import QueryStringRulesForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.query_string_rules_for_describe_cdn_config_output import QueryStringRulesForDescribeCdnConfigOutput
from volcenginesdkcdn.models.query_string_rules_for_update_cdn_config_input import QueryStringRulesForUpdateCdnConfigInput
from volcenginesdkcdn.models.quic_for_add_cdn_domain_input import QuicForAddCdnDomainInput
from volcenginesdkcdn.models.quic_for_batch_update_cdn_config_input import QuicForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.quic_for_describe_cdn_config_output import QuicForDescribeCdnConfigOutput
from volcenginesdkcdn.models.quic_for_update_cdn_config_input import QuicForUpdateCdnConfigInput
from volcenginesdkcdn.models.ranking_data_list_for_describe_statistical_ranking_output import RankingDataListForDescribeStatisticalRankingOutput
from volcenginesdkcdn.models.redirection_action_for_add_cdn_domain_input import RedirectionActionForAddCdnDomainInput
from volcenginesdkcdn.models.redirection_action_for_batch_update_cdn_config_input import RedirectionActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.redirection_action_for_describe_cdn_config_output import RedirectionActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.redirection_action_for_update_cdn_config_input import RedirectionActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.redirection_rewrite_for_add_cdn_domain_input import RedirectionRewriteForAddCdnDomainInput
from volcenginesdkcdn.models.redirection_rewrite_for_batch_update_cdn_config_input import RedirectionRewriteForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.redirection_rewrite_for_describe_cdn_config_output import RedirectionRewriteForDescribeCdnConfigOutput
from volcenginesdkcdn.models.redirection_rewrite_for_update_cdn_config_input import RedirectionRewriteForUpdateCdnConfigInput
from volcenginesdkcdn.models.redirection_rule_for_add_cdn_domain_input import RedirectionRuleForAddCdnDomainInput
from volcenginesdkcdn.models.redirection_rule_for_batch_update_cdn_config_input import RedirectionRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.redirection_rule_for_describe_cdn_config_output import RedirectionRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.redirection_rule_for_update_cdn_config_input import RedirectionRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.referer_access_rule_for_add_cdn_domain_input import RefererAccessRuleForAddCdnDomainInput
from volcenginesdkcdn.models.referer_access_rule_for_batch_update_cdn_config_input import RefererAccessRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.referer_access_rule_for_describe_cdn_config_output import RefererAccessRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.referer_access_rule_for_update_cdn_config_input import RefererAccessRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.referers_type_for_add_cdn_domain_input import ReferersTypeForAddCdnDomainInput
from volcenginesdkcdn.models.referers_type_for_batch_update_cdn_config_input import ReferersTypeForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.referers_type_for_describe_cdn_config_output import ReferersTypeForDescribeCdnConfigOutput
from volcenginesdkcdn.models.referers_type_for_update_cdn_config_input import ReferersTypeForUpdateCdnConfigInput
from volcenginesdkcdn.models.region_for_describe_cdn_region_and_isp_output import RegionForDescribeCdnRegionAndIspOutput
from volcenginesdkcdn.models.regular_type_for_add_cdn_domain_input import RegularTypeForAddCdnDomainInput
from volcenginesdkcdn.models.regular_type_for_batch_update_cdn_config_input import RegularTypeForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.regular_type_for_describe_cdn_config_output import RegularTypeForDescribeCdnConfigOutput
from volcenginesdkcdn.models.regular_type_for_update_cdn_config_input import RegularTypeForUpdateCdnConfigInput
from volcenginesdkcdn.models.remote_auth_for_add_cdn_domain_input import RemoteAuthForAddCdnDomainInput
from volcenginesdkcdn.models.remote_auth_for_batch_update_cdn_config_input import RemoteAuthForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.remote_auth_for_describe_cdn_config_output import RemoteAuthForDescribeCdnConfigOutput
from volcenginesdkcdn.models.remote_auth_for_update_cdn_config_input import RemoteAuthForUpdateCdnConfigInput
from volcenginesdkcdn.models.remote_auth_rule_action_for_add_cdn_domain_input import RemoteAuthRuleActionForAddCdnDomainInput
from volcenginesdkcdn.models.remote_auth_rule_action_for_batch_update_cdn_config_input import RemoteAuthRuleActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.remote_auth_rule_action_for_describe_cdn_config_output import RemoteAuthRuleActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.remote_auth_rule_action_for_update_cdn_config_input import RemoteAuthRuleActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.remote_auth_rule_for_add_cdn_domain_input import RemoteAuthRuleForAddCdnDomainInput
from volcenginesdkcdn.models.remote_auth_rule_for_batch_update_cdn_config_input import RemoteAuthRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.remote_auth_rule_for_describe_cdn_config_output import RemoteAuthRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.remote_auth_rule_for_update_cdn_config_input import RemoteAuthRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.request_block_rule_for_add_cdn_domain_input import RequestBlockRuleForAddCdnDomainInput
from volcenginesdkcdn.models.request_block_rule_for_batch_update_cdn_config_input import RequestBlockRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.request_block_rule_for_describe_cdn_config_output import RequestBlockRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.request_block_rule_for_update_cdn_config_input import RequestBlockRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_action_for_add_cdn_domain_input import RequestHeaderActionForAddCdnDomainInput
from volcenginesdkcdn.models.request_header_action_for_batch_update_cdn_config_input import RequestHeaderActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_action_for_describe_cdn_config_output import RequestHeaderActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.request_header_action_for_update_cdn_config_input import RequestHeaderActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_components_for_add_cdn_domain_input import RequestHeaderComponentsForAddCdnDomainInput
from volcenginesdkcdn.models.request_header_components_for_batch_update_cdn_config_input import RequestHeaderComponentsForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_components_for_describe_cdn_config_output import RequestHeaderComponentsForDescribeCdnConfigOutput
from volcenginesdkcdn.models.request_header_components_for_update_cdn_config_input import RequestHeaderComponentsForUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_for_add_cdn_domain_input import RequestHeaderForAddCdnDomainInput
from volcenginesdkcdn.models.request_header_for_batch_update_cdn_config_input import RequestHeaderForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_for_describe_cdn_config_output import RequestHeaderForDescribeCdnConfigOutput
from volcenginesdkcdn.models.request_header_for_update_cdn_config_input import RequestHeaderForUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_instance_for_add_cdn_domain_input import RequestHeaderInstanceForAddCdnDomainInput
from volcenginesdkcdn.models.request_header_instance_for_batch_update_cdn_config_input import RequestHeaderInstanceForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_instance_for_describe_cdn_config_output import RequestHeaderInstanceForDescribeCdnConfigOutput
from volcenginesdkcdn.models.request_header_instance_for_submit_preload_task_input import RequestHeaderInstanceForSubmitPreloadTaskInput
from volcenginesdkcdn.models.request_header_instance_for_update_cdn_config_input import RequestHeaderInstanceForUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_rules_for_add_cdn_domain_input import RequestHeaderRulesForAddCdnDomainInput
from volcenginesdkcdn.models.request_header_rules_for_batch_update_cdn_config_input import RequestHeaderRulesForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.request_header_rules_for_describe_cdn_config_output import RequestHeaderRulesForDescribeCdnConfigOutput
from volcenginesdkcdn.models.request_header_rules_for_update_cdn_config_input import RequestHeaderRulesForUpdateCdnConfigInput
from volcenginesdkcdn.models.resource_tag_for_add_cdn_domain_input import ResourceTagForAddCdnDomainInput
from volcenginesdkcdn.models.resource_tag_for_add_resource_tags_input import ResourceTagForAddResourceTagsInput
from volcenginesdkcdn.models.resource_tag_for_delete_resource_tags_input import ResourceTagForDeleteResourceTagsInput
from volcenginesdkcdn.models.resource_tag_for_list_cdn_domains_output import ResourceTagForListCdnDomainsOutput
from volcenginesdkcdn.models.resource_tag_for_list_resource_tags_output import ResourceTagForListResourceTagsOutput
from volcenginesdkcdn.models.resource_tag_for_update_resource_tags_input import ResourceTagForUpdateResourceTagsInput
from volcenginesdkcdn.models.response_action_for_add_cdn_domain_input import ResponseActionForAddCdnDomainInput
from volcenginesdkcdn.models.response_action_for_batch_update_cdn_config_input import ResponseActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.response_action_for_describe_cdn_config_output import ResponseActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.response_action_for_update_cdn_config_input import ResponseActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.response_header_action_for_add_cdn_domain_input import ResponseHeaderActionForAddCdnDomainInput
from volcenginesdkcdn.models.response_header_action_for_batch_update_cdn_config_input import ResponseHeaderActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.response_header_action_for_describe_cdn_config_output import ResponseHeaderActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.response_header_action_for_update_cdn_config_input import ResponseHeaderActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.response_header_for_add_cdn_domain_input import ResponseHeaderForAddCdnDomainInput
from volcenginesdkcdn.models.response_header_for_batch_update_cdn_config_input import ResponseHeaderForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.response_header_for_describe_cdn_config_output import ResponseHeaderForDescribeCdnConfigOutput
from volcenginesdkcdn.models.response_header_for_update_cdn_config_input import ResponseHeaderForUpdateCdnConfigInput
from volcenginesdkcdn.models.response_header_instance_for_add_cdn_domain_input import ResponseHeaderInstanceForAddCdnDomainInput
from volcenginesdkcdn.models.response_header_instance_for_batch_update_cdn_config_input import ResponseHeaderInstanceForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.response_header_instance_for_describe_cdn_config_output import ResponseHeaderInstanceForDescribeCdnConfigOutput
from volcenginesdkcdn.models.response_header_instance_for_update_cdn_config_input import ResponseHeaderInstanceForUpdateCdnConfigInput
from volcenginesdkcdn.models.rewrite_hls_for_add_cdn_domain_input import RewriteHLSForAddCdnDomainInput
from volcenginesdkcdn.models.rewrite_hls_for_batch_update_cdn_config_input import RewriteHLSForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.rewrite_hls_for_describe_cdn_config_output import RewriteHLSForDescribeCdnConfigOutput
from volcenginesdkcdn.models.rewrite_hls_for_update_cdn_config_input import RewriteHLSForUpdateCdnConfigInput
from volcenginesdkcdn.models.rewrite_m3u8_rule_for_add_cdn_domain_input import RewriteM3u8RuleForAddCdnDomainInput
from volcenginesdkcdn.models.rewrite_m3u8_rule_for_batch_update_cdn_config_input import RewriteM3u8RuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.rewrite_m3u8_rule_for_describe_cdn_config_output import RewriteM3u8RuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.rewrite_m3u8_rule_for_update_cdn_config_input import RewriteM3u8RuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.service_info_for_describe_cdn_service_output import ServiceInfoForDescribeCdnServiceOutput
from volcenginesdkcdn.models.shared_config_for_add_cdn_domain_input import SharedConfigForAddCdnDomainInput
from volcenginesdkcdn.models.shared_config_for_batch_update_cdn_config_input import SharedConfigForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.shared_config_for_describe_cdn_config_output import SharedConfigForDescribeCdnConfigOutput
from volcenginesdkcdn.models.shared_config_for_update_cdn_config_input import SharedConfigForUpdateCdnConfigInput
from volcenginesdkcdn.models.sign_cap_rule_for_add_cdn_domain_input import SignCapRuleForAddCdnDomainInput
from volcenginesdkcdn.models.sign_cap_rule_for_batch_update_cdn_config_input import SignCapRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.sign_cap_rule_for_describe_cdn_config_output import SignCapRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.sign_cap_rule_for_update_cdn_config_input import SignCapRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.sign_param_for_add_cdn_domain_input import SignParamForAddCdnDomainInput
from volcenginesdkcdn.models.sign_param_for_batch_update_cdn_config_input import SignParamForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.sign_param_for_describe_cdn_config_output import SignParamForDescribeCdnConfigOutput
from volcenginesdkcdn.models.sign_param_for_update_cdn_config_input import SignParamForUpdateCdnConfigInput
from volcenginesdkcdn.models.signed_url_auth_action_for_add_cdn_domain_input import SignedUrlAuthActionForAddCdnDomainInput
from volcenginesdkcdn.models.signed_url_auth_action_for_batch_update_cdn_config_input import SignedUrlAuthActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.signed_url_auth_action_for_describe_cdn_config_output import SignedUrlAuthActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.signed_url_auth_action_for_update_cdn_config_input import SignedUrlAuthActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.signed_url_auth_for_add_cdn_domain_input import SignedUrlAuthForAddCdnDomainInput
from volcenginesdkcdn.models.signed_url_auth_for_batch_update_cdn_config_input import SignedUrlAuthForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.signed_url_auth_for_describe_cdn_config_output import SignedUrlAuthForDescribeCdnConfigOutput
from volcenginesdkcdn.models.signed_url_auth_for_update_cdn_config_input import SignedUrlAuthForUpdateCdnConfigInput
from volcenginesdkcdn.models.signed_url_auth_rule_for_add_cdn_domain_input import SignedUrlAuthRuleForAddCdnDomainInput
from volcenginesdkcdn.models.signed_url_auth_rule_for_batch_update_cdn_config_input import SignedUrlAuthRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.signed_url_auth_rule_for_describe_cdn_config_output import SignedUrlAuthRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.signed_url_auth_rule_for_update_cdn_config_input import SignedUrlAuthRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.sort_rule_for_list_cert_info_input import SortRuleForListCertInfoInput
from volcenginesdkcdn.models.specified_cert_config_for_describe_cert_config_output import SpecifiedCertConfigForDescribeCertConfigOutput
from volcenginesdkcdn.models.speed_limit_time_for_add_cdn_domain_input import SpeedLimitTimeForAddCdnDomainInput
from volcenginesdkcdn.models.speed_limit_time_for_batch_update_cdn_config_input import SpeedLimitTimeForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.speed_limit_time_for_describe_cdn_config_output import SpeedLimitTimeForDescribeCdnConfigOutput
from volcenginesdkcdn.models.speed_limit_time_for_update_cdn_config_input import SpeedLimitTimeForUpdateCdnConfigInput
from volcenginesdkcdn.models.start_cdn_domain_request import StartCdnDomainRequest
from volcenginesdkcdn.models.start_cdn_domain_response import StartCdnDomainResponse
from volcenginesdkcdn.models.status_code_action_for_add_cdn_domain_input import StatusCodeActionForAddCdnDomainInput
from volcenginesdkcdn.models.status_code_action_for_batch_update_cdn_config_input import StatusCodeActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.status_code_action_for_describe_cdn_config_output import StatusCodeActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.status_code_action_for_update_cdn_config_input import StatusCodeActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.stop_cdn_domain_request import StopCdnDomainRequest
from volcenginesdkcdn.models.stop_cdn_domain_response import StopCdnDomainResponse
from volcenginesdkcdn.models.submit_block_task_request import SubmitBlockTaskRequest
from volcenginesdkcdn.models.submit_block_task_response import SubmitBlockTaskResponse
from volcenginesdkcdn.models.submit_preload_task_request import SubmitPreloadTaskRequest
from volcenginesdkcdn.models.submit_preload_task_response import SubmitPreloadTaskResponse
from volcenginesdkcdn.models.submit_refresh_task_request import SubmitRefreshTaskRequest
from volcenginesdkcdn.models.submit_refresh_task_response import SubmitRefreshTaskResponse
from volcenginesdkcdn.models.submit_unblock_task_request import SubmitUnblockTaskRequest
from volcenginesdkcdn.models.submit_unblock_task_response import SubmitUnblockTaskResponse
from volcenginesdkcdn.models.target_query_components_for_add_cdn_domain_input import TargetQueryComponentsForAddCdnDomainInput
from volcenginesdkcdn.models.target_query_components_for_batch_update_cdn_config_input import TargetQueryComponentsForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.target_query_components_for_describe_cdn_config_output import TargetQueryComponentsForDescribeCdnConfigOutput
from volcenginesdkcdn.models.target_query_components_for_update_cdn_config_input import TargetQueryComponentsForUpdateCdnConfigInput
from volcenginesdkcdn.models.time_out_action_for_add_cdn_domain_input import TimeOutActionForAddCdnDomainInput
from volcenginesdkcdn.models.time_out_action_for_batch_update_cdn_config_input import TimeOutActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.time_out_action_for_describe_cdn_config_output import TimeOutActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.time_out_action_for_update_cdn_config_input import TimeOutActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.timeout_for_add_cdn_domain_input import TimeoutForAddCdnDomainInput
from volcenginesdkcdn.models.timeout_for_batch_update_cdn_config_input import TimeoutForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.timeout_for_describe_cdn_config_output import TimeoutForDescribeCdnConfigOutput
from volcenginesdkcdn.models.timeout_for_update_cdn_config_input import TimeoutForUpdateCdnConfigInput
from volcenginesdkcdn.models.timeout_rule_for_add_cdn_domain_input import TimeoutRuleForAddCdnDomainInput
from volcenginesdkcdn.models.timeout_rule_for_batch_update_cdn_config_input import TimeoutRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.timeout_rule_for_describe_cdn_config_output import TimeoutRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.timeout_rule_for_update_cdn_config_input import TimeoutRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.timestamp_point_for_describe_billing_detail_output import TimestampPointForDescribeBillingDetailOutput
from volcenginesdkcdn.models.top_data_detail_for_describe_district_ranking_output import TopDataDetailForDescribeDistrictRankingOutput
from volcenginesdkcdn.models.top_data_detail_for_describe_edge_ranking_output import TopDataDetailForDescribeEdgeRankingOutput
from volcenginesdkcdn.models.top_data_detail_for_describe_edge_status_code_ranking_output import TopDataDetailForDescribeEdgeStatusCodeRankingOutput
from volcenginesdkcdn.models.top_data_detail_for_describe_origin_ranking_output import TopDataDetailForDescribeOriginRankingOutput
from volcenginesdkcdn.models.top_data_detail_for_describe_origin_status_code_ranking_output import TopDataDetailForDescribeOriginStatusCodeRankingOutput
from volcenginesdkcdn.models.tos_auth_information_for_add_cdn_domain_input import TosAuthInformationForAddCdnDomainInput
from volcenginesdkcdn.models.tos_auth_information_for_batch_update_cdn_config_input import TosAuthInformationForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.tos_auth_information_for_describe_cdn_config_output import TosAuthInformationForDescribeCdnConfigOutput
from volcenginesdkcdn.models.tos_auth_information_for_update_cdn_config_input import TosAuthInformationForUpdateCdnConfigInput
from volcenginesdkcdn.models.ua_access_rule_for_add_cdn_domain_input import UaAccessRuleForAddCdnDomainInput
from volcenginesdkcdn.models.ua_access_rule_for_batch_update_cdn_config_input import UaAccessRuleForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.ua_access_rule_for_describe_cdn_config_output import UaAccessRuleForDescribeCdnConfigOutput
from volcenginesdkcdn.models.ua_access_rule_for_update_cdn_config_input import UaAccessRuleForUpdateCdnConfigInput
from volcenginesdkcdn.models.update_cdn_config_request import UpdateCdnConfigRequest
from volcenginesdkcdn.models.update_cdn_config_response import UpdateCdnConfigResponse
from volcenginesdkcdn.models.update_resource_tags_request import UpdateResourceTagsRequest
from volcenginesdkcdn.models.update_resource_tags_response import UpdateResourceTagsResponse
from volcenginesdkcdn.models.update_shared_config_request import UpdateSharedConfigRequest
from volcenginesdkcdn.models.update_shared_config_response import UpdateSharedConfigResponse
from volcenginesdkcdn.models.uri_param_sup_for_add_cdn_domain_input import UriParamSupForAddCdnDomainInput
from volcenginesdkcdn.models.uri_param_sup_for_batch_update_cdn_config_input import UriParamSupForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.uri_param_sup_for_describe_cdn_config_output import UriParamSupForDescribeCdnConfigOutput
from volcenginesdkcdn.models.uri_param_sup_for_update_cdn_config_input import UriParamSupForUpdateCdnConfigInput
from volcenginesdkcdn.models.url_auth_custom_action_for_add_cdn_domain_input import UrlAuthCustomActionForAddCdnDomainInput
from volcenginesdkcdn.models.url_auth_custom_action_for_batch_update_cdn_config_input import UrlAuthCustomActionForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.url_auth_custom_action_for_describe_cdn_config_output import UrlAuthCustomActionForDescribeCdnConfigOutput
from volcenginesdkcdn.models.url_auth_custom_action_for_update_cdn_config_input import UrlAuthCustomActionForUpdateCdnConfigInput
from volcenginesdkcdn.models.url_normalize_for_add_cdn_domain_input import UrlNormalizeForAddCdnDomainInput
from volcenginesdkcdn.models.url_normalize_for_batch_update_cdn_config_input import UrlNormalizeForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.url_normalize_for_describe_cdn_config_output import UrlNormalizeForDescribeCdnConfigOutput
from volcenginesdkcdn.models.url_normalize_for_update_cdn_config_input import UrlNormalizeForUpdateCdnConfigInput
from volcenginesdkcdn.models.usage_reports_detail_for_list_usage_reports_output import UsageReportsDetailForListUsageReportsOutput
from volcenginesdkcdn.models.value_detail_for_describe_district_ranking_output import ValueDetailForDescribeDistrictRankingOutput
from volcenginesdkcdn.models.value_detail_for_describe_edge_ranking_output import ValueDetailForDescribeEdgeRankingOutput
from volcenginesdkcdn.models.value_detail_for_describe_origin_ranking_output import ValueDetailForDescribeOriginRankingOutput
from volcenginesdkcdn.models.value_for_describe_district_data_output import ValueForDescribeDistrictDataOutput
from volcenginesdkcdn.models.value_for_describe_edge_data_output import ValueForDescribeEdgeDataOutput
from volcenginesdkcdn.models.value_for_describe_origin_data_output import ValueForDescribeOriginDataOutput
from volcenginesdkcdn.models.video_drag_for_add_cdn_domain_input import VideoDragForAddCdnDomainInput
from volcenginesdkcdn.models.video_drag_for_batch_update_cdn_config_input import VideoDragForBatchUpdateCdnConfigInput
from volcenginesdkcdn.models.video_drag_for_describe_cdn_config_output import VideoDragForDescribeCdnConfigOutput
from volcenginesdkcdn.models.video_drag_for_update_cdn_config_input import VideoDragForUpdateCdnConfigInput
