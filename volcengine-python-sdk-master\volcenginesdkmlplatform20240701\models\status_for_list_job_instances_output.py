# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class StatusForListJobInstancesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'diagnose_message': 'str',
        'diagnose_state': 'str',
        'end_time': 'str',
        'exit_code': 'int',
        'holding_end_time': 'str',
        'message': 'str',
        'numa_status': 'str',
        'secondary_state': 'str',
        'start_time': 'str',
        'state': 'str'
    }

    attribute_map = {
        'diagnose_message': 'DiagnoseMessage',
        'diagnose_state': 'DiagnoseState',
        'end_time': 'EndTime',
        'exit_code': 'ExitCode',
        'holding_end_time': 'HoldingEndTime',
        'message': 'Message',
        'numa_status': 'NumaStatus',
        'secondary_state': 'SecondaryState',
        'start_time': 'StartTime',
        'state': 'State'
    }

    def __init__(self, diagnose_message=None, diagnose_state=None, end_time=None, exit_code=None, holding_end_time=None, message=None, numa_status=None, secondary_state=None, start_time=None, state=None, _configuration=None):  # noqa: E501
        """StatusForListJobInstancesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._diagnose_message = None
        self._diagnose_state = None
        self._end_time = None
        self._exit_code = None
        self._holding_end_time = None
        self._message = None
        self._numa_status = None
        self._secondary_state = None
        self._start_time = None
        self._state = None
        self.discriminator = None

        if diagnose_message is not None:
            self.diagnose_message = diagnose_message
        if diagnose_state is not None:
            self.diagnose_state = diagnose_state
        if end_time is not None:
            self.end_time = end_time
        if exit_code is not None:
            self.exit_code = exit_code
        if holding_end_time is not None:
            self.holding_end_time = holding_end_time
        if message is not None:
            self.message = message
        if numa_status is not None:
            self.numa_status = numa_status
        if secondary_state is not None:
            self.secondary_state = secondary_state
        if start_time is not None:
            self.start_time = start_time
        if state is not None:
            self.state = state

    @property
    def diagnose_message(self):
        """Gets the diagnose_message of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The diagnose_message of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._diagnose_message

    @diagnose_message.setter
    def diagnose_message(self, diagnose_message):
        """Sets the diagnose_message of this StatusForListJobInstancesOutput.


        :param diagnose_message: The diagnose_message of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._diagnose_message = diagnose_message

    @property
    def diagnose_state(self):
        """Gets the diagnose_state of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The diagnose_state of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._diagnose_state

    @diagnose_state.setter
    def diagnose_state(self, diagnose_state):
        """Sets the diagnose_state of this StatusForListJobInstancesOutput.


        :param diagnose_state: The diagnose_state of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._diagnose_state = diagnose_state

    @property
    def end_time(self):
        """Gets the end_time of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The end_time of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this StatusForListJobInstancesOutput.


        :param end_time: The end_time of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def exit_code(self):
        """Gets the exit_code of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The exit_code of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: int
        """
        return self._exit_code

    @exit_code.setter
    def exit_code(self, exit_code):
        """Sets the exit_code of this StatusForListJobInstancesOutput.


        :param exit_code: The exit_code of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: int
        """

        self._exit_code = exit_code

    @property
    def holding_end_time(self):
        """Gets the holding_end_time of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The holding_end_time of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._holding_end_time

    @holding_end_time.setter
    def holding_end_time(self, holding_end_time):
        """Sets the holding_end_time of this StatusForListJobInstancesOutput.


        :param holding_end_time: The holding_end_time of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._holding_end_time = holding_end_time

    @property
    def message(self):
        """Gets the message of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The message of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._message

    @message.setter
    def message(self, message):
        """Sets the message of this StatusForListJobInstancesOutput.


        :param message: The message of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._message = message

    @property
    def numa_status(self):
        """Gets the numa_status of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The numa_status of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._numa_status

    @numa_status.setter
    def numa_status(self, numa_status):
        """Sets the numa_status of this StatusForListJobInstancesOutput.


        :param numa_status: The numa_status of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._numa_status = numa_status

    @property
    def secondary_state(self):
        """Gets the secondary_state of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The secondary_state of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._secondary_state

    @secondary_state.setter
    def secondary_state(self, secondary_state):
        """Sets the secondary_state of this StatusForListJobInstancesOutput.


        :param secondary_state: The secondary_state of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._secondary_state = secondary_state

    @property
    def start_time(self):
        """Gets the start_time of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The start_time of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this StatusForListJobInstancesOutput.


        :param start_time: The start_time of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def state(self):
        """Gets the state of this StatusForListJobInstancesOutput.  # noqa: E501


        :return: The state of this StatusForListJobInstancesOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this StatusForListJobInstancesOutput.


        :param state: The state of this StatusForListJobInstancesOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(StatusForListJobInstancesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, StatusForListJobInstancesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, StatusForListJobInstancesOutput):
            return True

        return self.to_dict() != other.to_dict()
