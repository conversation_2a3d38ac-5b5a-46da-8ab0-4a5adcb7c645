# coding: utf-8

"""
    smc

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SourceImageInfoForDescribeMigrationJobsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'os': 'str',
        'platform': 'str',
        'platform_version': 'str'
    }

    attribute_map = {
        'os': 'OS',
        'platform': 'Platform',
        'platform_version': 'PlatformVersion'
    }

    def __init__(self, os=None, platform=None, platform_version=None, _configuration=None):  # noqa: E501
        """SourceImageInfoForDescribeMigrationJobsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._os = None
        self._platform = None
        self._platform_version = None
        self.discriminator = None

        if os is not None:
            self.os = os
        if platform is not None:
            self.platform = platform
        if platform_version is not None:
            self.platform_version = platform_version

    @property
    def os(self):
        """Gets the os of this SourceImageInfoForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The os of this SourceImageInfoForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._os

    @os.setter
    def os(self, os):
        """Sets the os of this SourceImageInfoForDescribeMigrationJobsOutput.


        :param os: The os of this SourceImageInfoForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._os = os

    @property
    def platform(self):
        """Gets the platform of this SourceImageInfoForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The platform of this SourceImageInfoForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this SourceImageInfoForDescribeMigrationJobsOutput.


        :param platform: The platform of this SourceImageInfoForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._platform = platform

    @property
    def platform_version(self):
        """Gets the platform_version of this SourceImageInfoForDescribeMigrationJobsOutput.  # noqa: E501


        :return: The platform_version of this SourceImageInfoForDescribeMigrationJobsOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform_version

    @platform_version.setter
    def platform_version(self, platform_version):
        """Sets the platform_version of this SourceImageInfoForDescribeMigrationJobsOutput.


        :param platform_version: The platform_version of this SourceImageInfoForDescribeMigrationJobsOutput.  # noqa: E501
        :type: str
        """

        self._platform_version = platform_version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SourceImageInfoForDescribeMigrationJobsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SourceImageInfoForDescribeMigrationJobsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SourceImageInfoForDescribeMigrationJobsOutput):
            return True

        return self.to_dict() != other.to_dict()
