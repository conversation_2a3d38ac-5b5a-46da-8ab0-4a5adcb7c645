# coding: utf-8

"""
    dns

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UpdateZoneRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'remark': 'str',
        'zid': 'int'
    }

    attribute_map = {
        'remark': 'Remark',
        'zid': 'ZID'
    }

    def __init__(self, remark=None, zid=None, _configuration=None):  # noqa: E501
        """UpdateZoneRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._remark = None
        self._zid = None
        self.discriminator = None

        if remark is not None:
            self.remark = remark
        self.zid = zid

    @property
    def remark(self):
        """Gets the remark of this UpdateZoneRequest.  # noqa: E501


        :return: The remark of this UpdateZoneRequest.  # noqa: E501
        :rtype: str
        """
        return self._remark

    @remark.setter
    def remark(self, remark):
        """Sets the remark of this UpdateZoneRequest.


        :param remark: The remark of this UpdateZoneRequest.  # noqa: E501
        :type: str
        """

        self._remark = remark

    @property
    def zid(self):
        """Gets the zid of this UpdateZoneRequest.  # noqa: E501


        :return: The zid of this UpdateZoneRequest.  # noqa: E501
        :rtype: int
        """
        return self._zid

    @zid.setter
    def zid(self, zid):
        """Sets the zid of this UpdateZoneRequest.


        :param zid: The zid of this UpdateZoneRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and zid is None:
            raise ValueError("Invalid value for `zid`, must not be `None`")  # noqa: E501

        self._zid = zid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UpdateZoneRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UpdateZoneRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UpdateZoneRequest):
            return True

        return self.to_dict() != other.to_dict()
