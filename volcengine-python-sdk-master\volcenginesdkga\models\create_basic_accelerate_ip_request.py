# coding: utf-8

"""
    ga

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateBasicAccelerateIPRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'accelerator_id': 'str',
        'edge_node_name': 'str',
        'endpoint_ids': 'list[str]',
        'ip_set_id': 'str',
        'isp': 'str',
        'mode': 'str'
    }

    attribute_map = {
        'accelerator_id': 'AcceleratorId',
        'edge_node_name': 'EdgeNodeName',
        'endpoint_ids': 'EndpointIds',
        'ip_set_id': 'IPSetId',
        'isp': 'ISP',
        'mode': 'Mode'
    }

    def __init__(self, accelerator_id=None, edge_node_name=None, endpoint_ids=None, ip_set_id=None, isp=None, mode=None, _configuration=None):  # noqa: E501
        """CreateBasicAccelerateIPRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._accelerator_id = None
        self._edge_node_name = None
        self._endpoint_ids = None
        self._ip_set_id = None
        self._isp = None
        self._mode = None
        self.discriminator = None

        self.accelerator_id = accelerator_id
        self.edge_node_name = edge_node_name
        if endpoint_ids is not None:
            self.endpoint_ids = endpoint_ids
        self.ip_set_id = ip_set_id
        self.isp = isp
        self.mode = mode

    @property
    def accelerator_id(self):
        """Gets the accelerator_id of this CreateBasicAccelerateIPRequest.  # noqa: E501


        :return: The accelerator_id of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :rtype: str
        """
        return self._accelerator_id

    @accelerator_id.setter
    def accelerator_id(self, accelerator_id):
        """Sets the accelerator_id of this CreateBasicAccelerateIPRequest.


        :param accelerator_id: The accelerator_id of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and accelerator_id is None:
            raise ValueError("Invalid value for `accelerator_id`, must not be `None`")  # noqa: E501

        self._accelerator_id = accelerator_id

    @property
    def edge_node_name(self):
        """Gets the edge_node_name of this CreateBasicAccelerateIPRequest.  # noqa: E501


        :return: The edge_node_name of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :rtype: str
        """
        return self._edge_node_name

    @edge_node_name.setter
    def edge_node_name(self, edge_node_name):
        """Sets the edge_node_name of this CreateBasicAccelerateIPRequest.


        :param edge_node_name: The edge_node_name of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and edge_node_name is None:
            raise ValueError("Invalid value for `edge_node_name`, must not be `None`")  # noqa: E501

        self._edge_node_name = edge_node_name

    @property
    def endpoint_ids(self):
        """Gets the endpoint_ids of this CreateBasicAccelerateIPRequest.  # noqa: E501


        :return: The endpoint_ids of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._endpoint_ids

    @endpoint_ids.setter
    def endpoint_ids(self, endpoint_ids):
        """Sets the endpoint_ids of this CreateBasicAccelerateIPRequest.


        :param endpoint_ids: The endpoint_ids of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :type: list[str]
        """

        self._endpoint_ids = endpoint_ids

    @property
    def ip_set_id(self):
        """Gets the ip_set_id of this CreateBasicAccelerateIPRequest.  # noqa: E501


        :return: The ip_set_id of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip_set_id

    @ip_set_id.setter
    def ip_set_id(self, ip_set_id):
        """Sets the ip_set_id of this CreateBasicAccelerateIPRequest.


        :param ip_set_id: The ip_set_id of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and ip_set_id is None:
            raise ValueError("Invalid value for `ip_set_id`, must not be `None`")  # noqa: E501

        self._ip_set_id = ip_set_id

    @property
    def isp(self):
        """Gets the isp of this CreateBasicAccelerateIPRequest.  # noqa: E501


        :return: The isp of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :rtype: str
        """
        return self._isp

    @isp.setter
    def isp(self, isp):
        """Sets the isp of this CreateBasicAccelerateIPRequest.


        :param isp: The isp of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and isp is None:
            raise ValueError("Invalid value for `isp`, must not be `None`")  # noqa: E501

        self._isp = isp

    @property
    def mode(self):
        """Gets the mode of this CreateBasicAccelerateIPRequest.  # noqa: E501


        :return: The mode of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :rtype: str
        """
        return self._mode

    @mode.setter
    def mode(self, mode):
        """Sets the mode of this CreateBasicAccelerateIPRequest.


        :param mode: The mode of this CreateBasicAccelerateIPRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and mode is None:
            raise ValueError("Invalid value for `mode`, must not be `None`")  # noqa: E501

        self._mode = mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateBasicAccelerateIPRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateBasicAccelerateIPRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateBasicAccelerateIPRequest):
            return True

        return self.to_dict() != other.to_dict()
