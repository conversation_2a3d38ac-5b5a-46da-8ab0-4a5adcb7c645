# coding: utf-8

"""
    ecs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class SpotPriceForDescribeSpotPriceHistoryOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_type_id': 'str',
        'spot_price': 'float',
        'timestamp': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'instance_type_id': 'InstanceTypeId',
        'spot_price': 'SpotPrice',
        'timestamp': 'Timestamp',
        'zone_id': 'ZoneId'
    }

    def __init__(self, instance_type_id=None, spot_price=None, timestamp=None, zone_id=None, _configuration=None):  # noqa: E501
        """SpotPriceForDescribeSpotPriceHistoryOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_type_id = None
        self._spot_price = None
        self._timestamp = None
        self._zone_id = None
        self.discriminator = None

        if instance_type_id is not None:
            self.instance_type_id = instance_type_id
        if spot_price is not None:
            self.spot_price = spot_price
        if timestamp is not None:
            self.timestamp = timestamp
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def instance_type_id(self):
        """Gets the instance_type_id of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501


        :return: The instance_type_id of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type_id

    @instance_type_id.setter
    def instance_type_id(self, instance_type_id):
        """Sets the instance_type_id of this SpotPriceForDescribeSpotPriceHistoryOutput.


        :param instance_type_id: The instance_type_id of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501
        :type: str
        """

        self._instance_type_id = instance_type_id

    @property
    def spot_price(self):
        """Gets the spot_price of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501


        :return: The spot_price of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501
        :rtype: float
        """
        return self._spot_price

    @spot_price.setter
    def spot_price(self, spot_price):
        """Sets the spot_price of this SpotPriceForDescribeSpotPriceHistoryOutput.


        :param spot_price: The spot_price of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501
        :type: float
        """

        self._spot_price = spot_price

    @property
    def timestamp(self):
        """Gets the timestamp of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501


        :return: The timestamp of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._timestamp

    @timestamp.setter
    def timestamp(self, timestamp):
        """Sets the timestamp of this SpotPriceForDescribeSpotPriceHistoryOutput.


        :param timestamp: The timestamp of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501
        :type: str
        """

        self._timestamp = timestamp

    @property
    def zone_id(self):
        """Gets the zone_id of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501


        :return: The zone_id of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this SpotPriceForDescribeSpotPriceHistoryOutput.


        :param zone_id: The zone_id of this SpotPriceForDescribeSpotPriceHistoryOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(SpotPriceForDescribeSpotPriceHistoryOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, SpotPriceForDescribeSpotPriceHistoryOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, SpotPriceForDescribeSpotPriceHistoryOutput):
            return True

        return self.to_dict() != other.to_dict()
