# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AggregationListForListRouteAggregationOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'aggregation_cidr': 'str',
        'create_time': 'str',
        'description': 'str',
        'detail_suppressed': 'bool',
        'direction': 'str',
        'enabled': 'bool'
    }

    attribute_map = {
        'aggregation_cidr': 'AggregationCidr',
        'create_time': 'CreateTime',
        'description': 'Description',
        'detail_suppressed': 'DetailSuppressed',
        'direction': 'Direction',
        'enabled': 'Enabled'
    }

    def __init__(self, aggregation_cidr=None, create_time=None, description=None, detail_suppressed=None, direction=None, enabled=None, _configuration=None):  # noqa: E501
        """AggregationListForListRouteAggregationOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._aggregation_cidr = None
        self._create_time = None
        self._description = None
        self._detail_suppressed = None
        self._direction = None
        self._enabled = None
        self.discriminator = None

        if aggregation_cidr is not None:
            self.aggregation_cidr = aggregation_cidr
        if create_time is not None:
            self.create_time = create_time
        if description is not None:
            self.description = description
        if detail_suppressed is not None:
            self.detail_suppressed = detail_suppressed
        if direction is not None:
            self.direction = direction
        if enabled is not None:
            self.enabled = enabled

    @property
    def aggregation_cidr(self):
        """Gets the aggregation_cidr of this AggregationListForListRouteAggregationOutput.  # noqa: E501


        :return: The aggregation_cidr of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :rtype: str
        """
        return self._aggregation_cidr

    @aggregation_cidr.setter
    def aggregation_cidr(self, aggregation_cidr):
        """Sets the aggregation_cidr of this AggregationListForListRouteAggregationOutput.


        :param aggregation_cidr: The aggregation_cidr of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :type: str
        """

        self._aggregation_cidr = aggregation_cidr

    @property
    def create_time(self):
        """Gets the create_time of this AggregationListForListRouteAggregationOutput.  # noqa: E501


        :return: The create_time of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this AggregationListForListRouteAggregationOutput.


        :param create_time: The create_time of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def description(self):
        """Gets the description of this AggregationListForListRouteAggregationOutput.  # noqa: E501


        :return: The description of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this AggregationListForListRouteAggregationOutput.


        :param description: The description of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def detail_suppressed(self):
        """Gets the detail_suppressed of this AggregationListForListRouteAggregationOutput.  # noqa: E501


        :return: The detail_suppressed of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :rtype: bool
        """
        return self._detail_suppressed

    @detail_suppressed.setter
    def detail_suppressed(self, detail_suppressed):
        """Sets the detail_suppressed of this AggregationListForListRouteAggregationOutput.


        :param detail_suppressed: The detail_suppressed of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :type: bool
        """

        self._detail_suppressed = detail_suppressed

    @property
    def direction(self):
        """Gets the direction of this AggregationListForListRouteAggregationOutput.  # noqa: E501


        :return: The direction of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :rtype: str
        """
        return self._direction

    @direction.setter
    def direction(self, direction):
        """Sets the direction of this AggregationListForListRouteAggregationOutput.


        :param direction: The direction of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :type: str
        """

        self._direction = direction

    @property
    def enabled(self):
        """Gets the enabled of this AggregationListForListRouteAggregationOutput.  # noqa: E501


        :return: The enabled of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :rtype: bool
        """
        return self._enabled

    @enabled.setter
    def enabled(self, enabled):
        """Sets the enabled of this AggregationListForListRouteAggregationOutput.


        :param enabled: The enabled of this AggregationListForListRouteAggregationOutput.  # noqa: E501
        :type: bool
        """

        self._enabled = enabled

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AggregationListForListRouteAggregationOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AggregationListForListRouteAggregationOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AggregationListForListRouteAggregationOutput):
            return True

        return self.to_dict() != other.to_dict()
