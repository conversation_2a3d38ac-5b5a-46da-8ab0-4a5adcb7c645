# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CachePerformanceForDescribeFileSystemsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'base_bandwidth': 'int',
        'cache_bandwidth': 'int',
        'data_flow_bandwidth': 'int'
    }

    attribute_map = {
        'base_bandwidth': 'BaseBandwidth',
        'cache_bandwidth': 'CacheBandwidth',
        'data_flow_bandwidth': 'DataFlowBandwidth'
    }

    def __init__(self, base_bandwidth=None, cache_bandwidth=None, data_flow_bandwidth=None, _configuration=None):  # noqa: E501
        """CachePerformanceForDescribeFileSystemsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._base_bandwidth = None
        self._cache_bandwidth = None
        self._data_flow_bandwidth = None
        self.discriminator = None

        if base_bandwidth is not None:
            self.base_bandwidth = base_bandwidth
        if cache_bandwidth is not None:
            self.cache_bandwidth = cache_bandwidth
        if data_flow_bandwidth is not None:
            self.data_flow_bandwidth = data_flow_bandwidth

    @property
    def base_bandwidth(self):
        """Gets the base_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.  # noqa: E501


        :return: The base_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.  # noqa: E501
        :rtype: int
        """
        return self._base_bandwidth

    @base_bandwidth.setter
    def base_bandwidth(self, base_bandwidth):
        """Sets the base_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.


        :param base_bandwidth: The base_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.  # noqa: E501
        :type: int
        """

        self._base_bandwidth = base_bandwidth

    @property
    def cache_bandwidth(self):
        """Gets the cache_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.  # noqa: E501


        :return: The cache_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.  # noqa: E501
        :rtype: int
        """
        return self._cache_bandwidth

    @cache_bandwidth.setter
    def cache_bandwidth(self, cache_bandwidth):
        """Sets the cache_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.


        :param cache_bandwidth: The cache_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.  # noqa: E501
        :type: int
        """

        self._cache_bandwidth = cache_bandwidth

    @property
    def data_flow_bandwidth(self):
        """Gets the data_flow_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.  # noqa: E501


        :return: The data_flow_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.  # noqa: E501
        :rtype: int
        """
        return self._data_flow_bandwidth

    @data_flow_bandwidth.setter
    def data_flow_bandwidth(self, data_flow_bandwidth):
        """Sets the data_flow_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.


        :param data_flow_bandwidth: The data_flow_bandwidth of this CachePerformanceForDescribeFileSystemsOutput.  # noqa: E501
        :type: int
        """

        self._data_flow_bandwidth = data_flow_bandwidth

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CachePerformanceForDescribeFileSystemsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CachePerformanceForDescribeFileSystemsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CachePerformanceForDescribeFileSystemsOutput):
            return True

        return self.to_dict() != other.to_dict()
