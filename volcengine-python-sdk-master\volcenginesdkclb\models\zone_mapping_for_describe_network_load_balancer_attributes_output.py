# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'eni_id': 'str',
        'ipv4_address': 'str',
        'ipv4_eip_address': 'str',
        'ipv4_eip_id': 'str',
        'ipv4_hc_status': 'str',
        'ipv4_local_addresses': 'list[str]',
        'ipv6_address': 'str',
        'ipv6_eip_id': 'str',
        'ipv6_hc_status': 'str',
        'ipv6_local_addresses': 'list[str]',
        'subnet_id': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'eni_id': 'EniId',
        'ipv4_address': 'Ipv4Address',
        'ipv4_eip_address': 'Ipv4EipAddress',
        'ipv4_eip_id': 'Ipv4EipId',
        'ipv4_hc_status': 'Ipv4HcStatus',
        'ipv4_local_addresses': 'Ipv4LocalAddresses',
        'ipv6_address': 'Ipv6Address',
        'ipv6_eip_id': 'Ipv6EipId',
        'ipv6_hc_status': 'Ipv6HcStatus',
        'ipv6_local_addresses': 'Ipv6LocalAddresses',
        'subnet_id': 'SubnetId',
        'zone_id': 'ZoneId'
    }

    def __init__(self, eni_id=None, ipv4_address=None, ipv4_eip_address=None, ipv4_eip_id=None, ipv4_hc_status=None, ipv4_local_addresses=None, ipv6_address=None, ipv6_eip_id=None, ipv6_hc_status=None, ipv6_local_addresses=None, subnet_id=None, zone_id=None, _configuration=None):  # noqa: E501
        """ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._eni_id = None
        self._ipv4_address = None
        self._ipv4_eip_address = None
        self._ipv4_eip_id = None
        self._ipv4_hc_status = None
        self._ipv4_local_addresses = None
        self._ipv6_address = None
        self._ipv6_eip_id = None
        self._ipv6_hc_status = None
        self._ipv6_local_addresses = None
        self._subnet_id = None
        self._zone_id = None
        self.discriminator = None

        if eni_id is not None:
            self.eni_id = eni_id
        if ipv4_address is not None:
            self.ipv4_address = ipv4_address
        if ipv4_eip_address is not None:
            self.ipv4_eip_address = ipv4_eip_address
        if ipv4_eip_id is not None:
            self.ipv4_eip_id = ipv4_eip_id
        if ipv4_hc_status is not None:
            self.ipv4_hc_status = ipv4_hc_status
        if ipv4_local_addresses is not None:
            self.ipv4_local_addresses = ipv4_local_addresses
        if ipv6_address is not None:
            self.ipv6_address = ipv6_address
        if ipv6_eip_id is not None:
            self.ipv6_eip_id = ipv6_eip_id
        if ipv6_hc_status is not None:
            self.ipv6_hc_status = ipv6_hc_status
        if ipv6_local_addresses is not None:
            self.ipv6_local_addresses = ipv6_local_addresses
        if subnet_id is not None:
            self.subnet_id = subnet_id
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def eni_id(self):
        """Gets the eni_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The eni_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._eni_id

    @eni_id.setter
    def eni_id(self, eni_id):
        """Sets the eni_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param eni_id: The eni_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._eni_id = eni_id

    @property
    def ipv4_address(self):
        """Gets the ipv4_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The ipv4_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ipv4_address

    @ipv4_address.setter
    def ipv4_address(self, ipv4_address):
        """Sets the ipv4_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param ipv4_address: The ipv4_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._ipv4_address = ipv4_address

    @property
    def ipv4_eip_address(self):
        """Gets the ipv4_eip_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The ipv4_eip_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ipv4_eip_address

    @ipv4_eip_address.setter
    def ipv4_eip_address(self, ipv4_eip_address):
        """Sets the ipv4_eip_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param ipv4_eip_address: The ipv4_eip_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._ipv4_eip_address = ipv4_eip_address

    @property
    def ipv4_eip_id(self):
        """Gets the ipv4_eip_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The ipv4_eip_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ipv4_eip_id

    @ipv4_eip_id.setter
    def ipv4_eip_id(self, ipv4_eip_id):
        """Sets the ipv4_eip_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param ipv4_eip_id: The ipv4_eip_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._ipv4_eip_id = ipv4_eip_id

    @property
    def ipv4_hc_status(self):
        """Gets the ipv4_hc_status of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The ipv4_hc_status of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ipv4_hc_status

    @ipv4_hc_status.setter
    def ipv4_hc_status(self, ipv4_hc_status):
        """Sets the ipv4_hc_status of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param ipv4_hc_status: The ipv4_hc_status of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._ipv4_hc_status = ipv4_hc_status

    @property
    def ipv4_local_addresses(self):
        """Gets the ipv4_local_addresses of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The ipv4_local_addresses of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ipv4_local_addresses

    @ipv4_local_addresses.setter
    def ipv4_local_addresses(self, ipv4_local_addresses):
        """Sets the ipv4_local_addresses of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param ipv4_local_addresses: The ipv4_local_addresses of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: list[str]
        """

        self._ipv4_local_addresses = ipv4_local_addresses

    @property
    def ipv6_address(self):
        """Gets the ipv6_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The ipv6_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_address

    @ipv6_address.setter
    def ipv6_address(self, ipv6_address):
        """Sets the ipv6_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param ipv6_address: The ipv6_address of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._ipv6_address = ipv6_address

    @property
    def ipv6_eip_id(self):
        """Gets the ipv6_eip_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The ipv6_eip_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_eip_id

    @ipv6_eip_id.setter
    def ipv6_eip_id(self, ipv6_eip_id):
        """Sets the ipv6_eip_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param ipv6_eip_id: The ipv6_eip_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._ipv6_eip_id = ipv6_eip_id

    @property
    def ipv6_hc_status(self):
        """Gets the ipv6_hc_status of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The ipv6_hc_status of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._ipv6_hc_status

    @ipv6_hc_status.setter
    def ipv6_hc_status(self, ipv6_hc_status):
        """Sets the ipv6_hc_status of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param ipv6_hc_status: The ipv6_hc_status of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._ipv6_hc_status = ipv6_hc_status

    @property
    def ipv6_local_addresses(self):
        """Gets the ipv6_local_addresses of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The ipv6_local_addresses of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._ipv6_local_addresses

    @ipv6_local_addresses.setter
    def ipv6_local_addresses(self, ipv6_local_addresses):
        """Sets the ipv6_local_addresses of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param ipv6_local_addresses: The ipv6_local_addresses of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: list[str]
        """

        self._ipv6_local_addresses = ipv6_local_addresses

    @property
    def subnet_id(self):
        """Gets the subnet_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The subnet_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._subnet_id

    @subnet_id.setter
    def subnet_id(self, subnet_id):
        """Sets the subnet_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param subnet_id: The subnet_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._subnet_id = subnet_id

    @property
    def zone_id(self):
        """Gets the zone_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501


        :return: The zone_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.


        :param zone_id: The zone_id of this ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ZoneMappingForDescribeNetworkLoadBalancerAttributesOutput):
            return True

        return self.to_dict() != other.to_dict()
