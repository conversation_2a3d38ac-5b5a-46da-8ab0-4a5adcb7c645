# coding: utf-8

"""
    kms

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ReplicateKeyRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'description': 'str',
        'key_id': 'str',
        'key_name': 'str',
        'keyring_name': 'str',
        'replica_region': 'str',
        'tags': 'list[TagForReplicateKeyInput]'
    }

    attribute_map = {
        'description': 'Description',
        'key_id': 'KeyID',
        'key_name': 'KeyName',
        'keyring_name': 'KeyringName',
        'replica_region': 'ReplicaRegion',
        'tags': 'Tags'
    }

    def __init__(self, description=None, key_id=None, key_name=None, keyring_name=None, replica_region=None, tags=None, _configuration=None):  # noqa: E501
        """ReplicateKeyRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._description = None
        self._key_id = None
        self._key_name = None
        self._keyring_name = None
        self._replica_region = None
        self._tags = None
        self.discriminator = None

        if description is not None:
            self.description = description
        if key_id is not None:
            self.key_id = key_id
        if key_name is not None:
            self.key_name = key_name
        if keyring_name is not None:
            self.keyring_name = keyring_name
        self.replica_region = replica_region
        if tags is not None:
            self.tags = tags

    @property
    def description(self):
        """Gets the description of this ReplicateKeyRequest.  # noqa: E501


        :return: The description of this ReplicateKeyRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this ReplicateKeyRequest.


        :param description: The description of this ReplicateKeyRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 8192):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `8192`")  # noqa: E501

        self._description = description

    @property
    def key_id(self):
        """Gets the key_id of this ReplicateKeyRequest.  # noqa: E501


        :return: The key_id of this ReplicateKeyRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_id

    @key_id.setter
    def key_id(self, key_id):
        """Sets the key_id of this ReplicateKeyRequest.


        :param key_id: The key_id of this ReplicateKeyRequest.  # noqa: E501
        :type: str
        """

        self._key_id = key_id

    @property
    def key_name(self):
        """Gets the key_name of this ReplicateKeyRequest.  # noqa: E501


        :return: The key_name of this ReplicateKeyRequest.  # noqa: E501
        :rtype: str
        """
        return self._key_name

    @key_name.setter
    def key_name(self, key_name):
        """Sets the key_name of this ReplicateKeyRequest.


        :param key_name: The key_name of this ReplicateKeyRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                key_name is not None and len(key_name) > 31):
            raise ValueError("Invalid value for `key_name`, length must be less than or equal to `31`")  # noqa: E501
        if (self._configuration.client_side_validation and
                key_name is not None and len(key_name) < 2):
            raise ValueError("Invalid value for `key_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._key_name = key_name

    @property
    def keyring_name(self):
        """Gets the keyring_name of this ReplicateKeyRequest.  # noqa: E501


        :return: The keyring_name of this ReplicateKeyRequest.  # noqa: E501
        :rtype: str
        """
        return self._keyring_name

    @keyring_name.setter
    def keyring_name(self, keyring_name):
        """Sets the keyring_name of this ReplicateKeyRequest.


        :param keyring_name: The keyring_name of this ReplicateKeyRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                keyring_name is not None and len(keyring_name) > 31):
            raise ValueError("Invalid value for `keyring_name`, length must be less than or equal to `31`")  # noqa: E501
        if (self._configuration.client_side_validation and
                keyring_name is not None and len(keyring_name) < 2):
            raise ValueError("Invalid value for `keyring_name`, length must be greater than or equal to `2`")  # noqa: E501

        self._keyring_name = keyring_name

    @property
    def replica_region(self):
        """Gets the replica_region of this ReplicateKeyRequest.  # noqa: E501


        :return: The replica_region of this ReplicateKeyRequest.  # noqa: E501
        :rtype: str
        """
        return self._replica_region

    @replica_region.setter
    def replica_region(self, replica_region):
        """Sets the replica_region of this ReplicateKeyRequest.


        :param replica_region: The replica_region of this ReplicateKeyRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and replica_region is None:
            raise ValueError("Invalid value for `replica_region`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                replica_region is not None and len(replica_region) > 64):
            raise ValueError("Invalid value for `replica_region`, length must be less than or equal to `64`")  # noqa: E501

        self._replica_region = replica_region

    @property
    def tags(self):
        """Gets the tags of this ReplicateKeyRequest.  # noqa: E501


        :return: The tags of this ReplicateKeyRequest.  # noqa: E501
        :rtype: list[TagForReplicateKeyInput]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this ReplicateKeyRequest.


        :param tags: The tags of this ReplicateKeyRequest.  # noqa: E501
        :type: list[TagForReplicateKeyInput]
        """

        self._tags = tags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ReplicateKeyRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ReplicateKeyRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ReplicateKeyRequest):
            return True

        return self.to_dict() != other.to_dict()
