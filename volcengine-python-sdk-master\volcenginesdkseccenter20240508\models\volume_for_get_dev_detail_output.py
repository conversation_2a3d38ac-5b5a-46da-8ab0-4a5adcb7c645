# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class VolumeForGetDevDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'id': 'str',
        'size': 'int',
        'state': 'str',
        'state_hint': 'str',
        'type': 'str',
        'used': 'str'
    }

    attribute_map = {
        'id': 'Id',
        'size': 'Size',
        'state': 'State',
        'state_hint': 'StateHint',
        'type': 'Type',
        'used': 'Used'
    }

    def __init__(self, id=None, size=None, state=None, state_hint=None, type=None, used=None, _configuration=None):  # noqa: E501
        """VolumeForGetDevDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._id = None
        self._size = None
        self._state = None
        self._state_hint = None
        self._type = None
        self._used = None
        self.discriminator = None

        if id is not None:
            self.id = id
        if size is not None:
            self.size = size
        if state is not None:
            self.state = state
        if state_hint is not None:
            self.state_hint = state_hint
        if type is not None:
            self.type = type
        if used is not None:
            self.used = used

    @property
    def id(self):
        """Gets the id of this VolumeForGetDevDetailOutput.  # noqa: E501


        :return: The id of this VolumeForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this VolumeForGetDevDetailOutput.


        :param id: The id of this VolumeForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def size(self):
        """Gets the size of this VolumeForGetDevDetailOutput.  # noqa: E501


        :return: The size of this VolumeForGetDevDetailOutput.  # noqa: E501
        :rtype: int
        """
        return self._size

    @size.setter
    def size(self, size):
        """Sets the size of this VolumeForGetDevDetailOutput.


        :param size: The size of this VolumeForGetDevDetailOutput.  # noqa: E501
        :type: int
        """

        self._size = size

    @property
    def state(self):
        """Gets the state of this VolumeForGetDevDetailOutput.  # noqa: E501


        :return: The state of this VolumeForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this VolumeForGetDevDetailOutput.


        :param state: The state of this VolumeForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._state = state

    @property
    def state_hint(self):
        """Gets the state_hint of this VolumeForGetDevDetailOutput.  # noqa: E501


        :return: The state_hint of this VolumeForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._state_hint

    @state_hint.setter
    def state_hint(self, state_hint):
        """Sets the state_hint of this VolumeForGetDevDetailOutput.


        :param state_hint: The state_hint of this VolumeForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._state_hint = state_hint

    @property
    def type(self):
        """Gets the type of this VolumeForGetDevDetailOutput.  # noqa: E501


        :return: The type of this VolumeForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this VolumeForGetDevDetailOutput.


        :param type: The type of this VolumeForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def used(self):
        """Gets the used of this VolumeForGetDevDetailOutput.  # noqa: E501


        :return: The used of this VolumeForGetDevDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._used

    @used.setter
    def used(self, used):
        """Sets the used of this VolumeForGetDevDetailOutput.


        :param used: The used of this VolumeForGetDevDetailOutput.  # noqa: E501
        :type: str
        """

        self._used = used

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(VolumeForGetDevDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, VolumeForGetDevDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, VolumeForGetDevDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
