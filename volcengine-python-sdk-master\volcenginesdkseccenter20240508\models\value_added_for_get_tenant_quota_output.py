# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ValueAddedForGetTenantQuotaOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'log_analysis_quota': 'LogAnalysisQuotaForGetTenantQuotaOutput',
        'mlp_defender_quota': 'MlpDefenderQuotaForGetTenantQuotaOutput',
        'multi_level_management': 'MultiLevelManagementForGetTenantQuotaOutput',
        'rasp_auth_quota': 'RaspAuthQuotaForGetTenantQuotaOutput'
    }

    attribute_map = {
        'log_analysis_quota': 'LogAnalysisQuota',
        'mlp_defender_quota': 'MlpDefenderQuota',
        'multi_level_management': 'MultiLevelManagement',
        'rasp_auth_quota': 'RaspAuthQuota'
    }

    def __init__(self, log_analysis_quota=None, mlp_defender_quota=None, multi_level_management=None, rasp_auth_quota=None, _configuration=None):  # noqa: E501
        """ValueAddedForGetTenantQuotaOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._log_analysis_quota = None
        self._mlp_defender_quota = None
        self._multi_level_management = None
        self._rasp_auth_quota = None
        self.discriminator = None

        if log_analysis_quota is not None:
            self.log_analysis_quota = log_analysis_quota
        if mlp_defender_quota is not None:
            self.mlp_defender_quota = mlp_defender_quota
        if multi_level_management is not None:
            self.multi_level_management = multi_level_management
        if rasp_auth_quota is not None:
            self.rasp_auth_quota = rasp_auth_quota

    @property
    def log_analysis_quota(self):
        """Gets the log_analysis_quota of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501


        :return: The log_analysis_quota of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501
        :rtype: LogAnalysisQuotaForGetTenantQuotaOutput
        """
        return self._log_analysis_quota

    @log_analysis_quota.setter
    def log_analysis_quota(self, log_analysis_quota):
        """Sets the log_analysis_quota of this ValueAddedForGetTenantQuotaOutput.


        :param log_analysis_quota: The log_analysis_quota of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501
        :type: LogAnalysisQuotaForGetTenantQuotaOutput
        """

        self._log_analysis_quota = log_analysis_quota

    @property
    def mlp_defender_quota(self):
        """Gets the mlp_defender_quota of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501


        :return: The mlp_defender_quota of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501
        :rtype: MlpDefenderQuotaForGetTenantQuotaOutput
        """
        return self._mlp_defender_quota

    @mlp_defender_quota.setter
    def mlp_defender_quota(self, mlp_defender_quota):
        """Sets the mlp_defender_quota of this ValueAddedForGetTenantQuotaOutput.


        :param mlp_defender_quota: The mlp_defender_quota of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501
        :type: MlpDefenderQuotaForGetTenantQuotaOutput
        """

        self._mlp_defender_quota = mlp_defender_quota

    @property
    def multi_level_management(self):
        """Gets the multi_level_management of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501


        :return: The multi_level_management of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501
        :rtype: MultiLevelManagementForGetTenantQuotaOutput
        """
        return self._multi_level_management

    @multi_level_management.setter
    def multi_level_management(self, multi_level_management):
        """Sets the multi_level_management of this ValueAddedForGetTenantQuotaOutput.


        :param multi_level_management: The multi_level_management of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501
        :type: MultiLevelManagementForGetTenantQuotaOutput
        """

        self._multi_level_management = multi_level_management

    @property
    def rasp_auth_quota(self):
        """Gets the rasp_auth_quota of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501


        :return: The rasp_auth_quota of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501
        :rtype: RaspAuthQuotaForGetTenantQuotaOutput
        """
        return self._rasp_auth_quota

    @rasp_auth_quota.setter
    def rasp_auth_quota(self, rasp_auth_quota):
        """Sets the rasp_auth_quota of this ValueAddedForGetTenantQuotaOutput.


        :param rasp_auth_quota: The rasp_auth_quota of this ValueAddedForGetTenantQuotaOutput.  # noqa: E501
        :type: RaspAuthQuotaForGetTenantQuotaOutput
        """

        self._rasp_auth_quota = rasp_auth_quota

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ValueAddedForGetTenantQuotaOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ValueAddedForGetTenantQuotaOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ValueAddedForGetTenantQuotaOutput):
            return True

        return self.to_dict() != other.to_dict()
