# coding: utf-8

"""
    rocketmq

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetInspectConfigResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'configs': 'list[ConfigForGetInspectConfigOutput]',
        'instance_id': 'str',
        'total': 'int',
        'trigger_limit': 'int'
    }

    attribute_map = {
        'configs': 'Configs',
        'instance_id': 'InstanceId',
        'total': 'Total',
        'trigger_limit': 'TriggerLimit'
    }

    def __init__(self, configs=None, instance_id=None, total=None, trigger_limit=None, _configuration=None):  # noqa: E501
        """GetInspectConfigResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._configs = None
        self._instance_id = None
        self._total = None
        self._trigger_limit = None
        self.discriminator = None

        if configs is not None:
            self.configs = configs
        if instance_id is not None:
            self.instance_id = instance_id
        if total is not None:
            self.total = total
        if trigger_limit is not None:
            self.trigger_limit = trigger_limit

    @property
    def configs(self):
        """Gets the configs of this GetInspectConfigResponse.  # noqa: E501


        :return: The configs of this GetInspectConfigResponse.  # noqa: E501
        :rtype: list[ConfigForGetInspectConfigOutput]
        """
        return self._configs

    @configs.setter
    def configs(self, configs):
        """Sets the configs of this GetInspectConfigResponse.


        :param configs: The configs of this GetInspectConfigResponse.  # noqa: E501
        :type: list[ConfigForGetInspectConfigOutput]
        """

        self._configs = configs

    @property
    def instance_id(self):
        """Gets the instance_id of this GetInspectConfigResponse.  # noqa: E501


        :return: The instance_id of this GetInspectConfigResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this GetInspectConfigResponse.


        :param instance_id: The instance_id of this GetInspectConfigResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def total(self):
        """Gets the total of this GetInspectConfigResponse.  # noqa: E501


        :return: The total of this GetInspectConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this GetInspectConfigResponse.


        :param total: The total of this GetInspectConfigResponse.  # noqa: E501
        :type: int
        """

        self._total = total

    @property
    def trigger_limit(self):
        """Gets the trigger_limit of this GetInspectConfigResponse.  # noqa: E501


        :return: The trigger_limit of this GetInspectConfigResponse.  # noqa: E501
        :rtype: int
        """
        return self._trigger_limit

    @trigger_limit.setter
    def trigger_limit(self, trigger_limit):
        """Sets the trigger_limit of this GetInspectConfigResponse.


        :param trigger_limit: The trigger_limit of this GetInspectConfigResponse.  # noqa: E501
        :type: int
        """

        self._trigger_limit = trigger_limit

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetInspectConfigResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetInspectConfigResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetInspectConfigResponse):
            return True

        return self.to_dict() != other.to_dict()
