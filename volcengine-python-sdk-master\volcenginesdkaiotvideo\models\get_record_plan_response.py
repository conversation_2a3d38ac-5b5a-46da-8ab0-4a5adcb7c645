# coding: utf-8

"""
    aiotvideo

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetRecordPlanResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bind_stream_num': 'int',
        'bind_template': 'str',
        'created_at': 'int',
        'description': 'str',
        'plan_id': 'str',
        'plan_name': 'str',
        'status': 'str',
        'template_info': 'TemplateInfoForGetRecordPlanOutput',
        'updated_at': 'int'
    }

    attribute_map = {
        'bind_stream_num': 'BindStreamNum',
        'bind_template': 'BindTemplate',
        'created_at': 'CreatedAt',
        'description': 'Description',
        'plan_id': 'PlanID',
        'plan_name': 'PlanName',
        'status': 'Status',
        'template_info': 'TemplateInfo',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, bind_stream_num=None, bind_template=None, created_at=None, description=None, plan_id=None, plan_name=None, status=None, template_info=None, updated_at=None, _configuration=None):  # noqa: E501
        """GetRecordPlanResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bind_stream_num = None
        self._bind_template = None
        self._created_at = None
        self._description = None
        self._plan_id = None
        self._plan_name = None
        self._status = None
        self._template_info = None
        self._updated_at = None
        self.discriminator = None

        if bind_stream_num is not None:
            self.bind_stream_num = bind_stream_num
        if bind_template is not None:
            self.bind_template = bind_template
        if created_at is not None:
            self.created_at = created_at
        if description is not None:
            self.description = description
        if plan_id is not None:
            self.plan_id = plan_id
        if plan_name is not None:
            self.plan_name = plan_name
        if status is not None:
            self.status = status
        if template_info is not None:
            self.template_info = template_info
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def bind_stream_num(self):
        """Gets the bind_stream_num of this GetRecordPlanResponse.  # noqa: E501


        :return: The bind_stream_num of this GetRecordPlanResponse.  # noqa: E501
        :rtype: int
        """
        return self._bind_stream_num

    @bind_stream_num.setter
    def bind_stream_num(self, bind_stream_num):
        """Sets the bind_stream_num of this GetRecordPlanResponse.


        :param bind_stream_num: The bind_stream_num of this GetRecordPlanResponse.  # noqa: E501
        :type: int
        """

        self._bind_stream_num = bind_stream_num

    @property
    def bind_template(self):
        """Gets the bind_template of this GetRecordPlanResponse.  # noqa: E501


        :return: The bind_template of this GetRecordPlanResponse.  # noqa: E501
        :rtype: str
        """
        return self._bind_template

    @bind_template.setter
    def bind_template(self, bind_template):
        """Sets the bind_template of this GetRecordPlanResponse.


        :param bind_template: The bind_template of this GetRecordPlanResponse.  # noqa: E501
        :type: str
        """

        self._bind_template = bind_template

    @property
    def created_at(self):
        """Gets the created_at of this GetRecordPlanResponse.  # noqa: E501


        :return: The created_at of this GetRecordPlanResponse.  # noqa: E501
        :rtype: int
        """
        return self._created_at

    @created_at.setter
    def created_at(self, created_at):
        """Sets the created_at of this GetRecordPlanResponse.


        :param created_at: The created_at of this GetRecordPlanResponse.  # noqa: E501
        :type: int
        """

        self._created_at = created_at

    @property
    def description(self):
        """Gets the description of this GetRecordPlanResponse.  # noqa: E501


        :return: The description of this GetRecordPlanResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetRecordPlanResponse.


        :param description: The description of this GetRecordPlanResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def plan_id(self):
        """Gets the plan_id of this GetRecordPlanResponse.  # noqa: E501


        :return: The plan_id of this GetRecordPlanResponse.  # noqa: E501
        :rtype: str
        """
        return self._plan_id

    @plan_id.setter
    def plan_id(self, plan_id):
        """Sets the plan_id of this GetRecordPlanResponse.


        :param plan_id: The plan_id of this GetRecordPlanResponse.  # noqa: E501
        :type: str
        """

        self._plan_id = plan_id

    @property
    def plan_name(self):
        """Gets the plan_name of this GetRecordPlanResponse.  # noqa: E501


        :return: The plan_name of this GetRecordPlanResponse.  # noqa: E501
        :rtype: str
        """
        return self._plan_name

    @plan_name.setter
    def plan_name(self, plan_name):
        """Sets the plan_name of this GetRecordPlanResponse.


        :param plan_name: The plan_name of this GetRecordPlanResponse.  # noqa: E501
        :type: str
        """

        self._plan_name = plan_name

    @property
    def status(self):
        """Gets the status of this GetRecordPlanResponse.  # noqa: E501


        :return: The status of this GetRecordPlanResponse.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetRecordPlanResponse.


        :param status: The status of this GetRecordPlanResponse.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def template_info(self):
        """Gets the template_info of this GetRecordPlanResponse.  # noqa: E501


        :return: The template_info of this GetRecordPlanResponse.  # noqa: E501
        :rtype: TemplateInfoForGetRecordPlanOutput
        """
        return self._template_info

    @template_info.setter
    def template_info(self, template_info):
        """Sets the template_info of this GetRecordPlanResponse.


        :param template_info: The template_info of this GetRecordPlanResponse.  # noqa: E501
        :type: TemplateInfoForGetRecordPlanOutput
        """

        self._template_info = template_info

    @property
    def updated_at(self):
        """Gets the updated_at of this GetRecordPlanResponse.  # noqa: E501


        :return: The updated_at of this GetRecordPlanResponse.  # noqa: E501
        :rtype: int
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this GetRecordPlanResponse.


        :param updated_at: The updated_at of this GetRecordPlanResponse.  # noqa: E501
        :type: int
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetRecordPlanResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetRecordPlanResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetRecordPlanResponse):
            return True

        return self.to_dict() != other.to_dict()
