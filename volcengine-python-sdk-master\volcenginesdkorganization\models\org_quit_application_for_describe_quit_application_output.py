# coding: utf-8

"""
    organization

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class OrgQuitApplicationForDescribeQuitApplicationOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'apply_reason': 'str',
        'audit_reason': 'str',
        'audited_time': 'str',
        'created_time': 'str',
        'delete_uk': 'str',
        'deleted_time': 'str',
        'id': 'str',
        'org_id': 'str',
        'owner': 'str',
        'status': 'int',
        'updated_time': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'apply_reason': 'ApplyReason',
        'audit_reason': 'AuditReason',
        'audited_time': 'AuditedTime',
        'created_time': 'CreatedTime',
        'delete_uk': 'DeleteUk',
        'deleted_time': 'DeletedTime',
        'id': 'ID',
        'org_id': 'OrgID',
        'owner': 'Owner',
        'status': 'Status',
        'updated_time': 'UpdatedTime'
    }

    def __init__(self, account_id=None, apply_reason=None, audit_reason=None, audited_time=None, created_time=None, delete_uk=None, deleted_time=None, id=None, org_id=None, owner=None, status=None, updated_time=None, _configuration=None):  # noqa: E501
        """OrgQuitApplicationForDescribeQuitApplicationOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._apply_reason = None
        self._audit_reason = None
        self._audited_time = None
        self._created_time = None
        self._delete_uk = None
        self._deleted_time = None
        self._id = None
        self._org_id = None
        self._owner = None
        self._status = None
        self._updated_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if apply_reason is not None:
            self.apply_reason = apply_reason
        if audit_reason is not None:
            self.audit_reason = audit_reason
        if audited_time is not None:
            self.audited_time = audited_time
        if created_time is not None:
            self.created_time = created_time
        if delete_uk is not None:
            self.delete_uk = delete_uk
        if deleted_time is not None:
            self.deleted_time = deleted_time
        if id is not None:
            self.id = id
        if org_id is not None:
            self.org_id = org_id
        if owner is not None:
            self.owner = owner
        if status is not None:
            self.status = status
        if updated_time is not None:
            self.updated_time = updated_time

    @property
    def account_id(self):
        """Gets the account_id of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The account_id of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param account_id: The account_id of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def apply_reason(self):
        """Gets the apply_reason of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The apply_reason of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._apply_reason

    @apply_reason.setter
    def apply_reason(self, apply_reason):
        """Sets the apply_reason of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param apply_reason: The apply_reason of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._apply_reason = apply_reason

    @property
    def audit_reason(self):
        """Gets the audit_reason of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The audit_reason of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._audit_reason

    @audit_reason.setter
    def audit_reason(self, audit_reason):
        """Sets the audit_reason of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param audit_reason: The audit_reason of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._audit_reason = audit_reason

    @property
    def audited_time(self):
        """Gets the audited_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The audited_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._audited_time

    @audited_time.setter
    def audited_time(self, audited_time):
        """Sets the audited_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param audited_time: The audited_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._audited_time = audited_time

    @property
    def created_time(self):
        """Gets the created_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The created_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param created_time: The created_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def delete_uk(self):
        """Gets the delete_uk of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The delete_uk of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._delete_uk

    @delete_uk.setter
    def delete_uk(self, delete_uk):
        """Sets the delete_uk of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param delete_uk: The delete_uk of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._delete_uk = delete_uk

    @property
    def deleted_time(self):
        """Gets the deleted_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The deleted_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._deleted_time

    @deleted_time.setter
    def deleted_time(self, deleted_time):
        """Sets the deleted_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param deleted_time: The deleted_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._deleted_time = deleted_time

    @property
    def id(self):
        """Gets the id of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The id of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param id: The id of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def org_id(self):
        """Gets the org_id of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The org_id of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._org_id

    @org_id.setter
    def org_id(self, org_id):
        """Sets the org_id of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param org_id: The org_id of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._org_id = org_id

    @property
    def owner(self):
        """Gets the owner of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The owner of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._owner

    @owner.setter
    def owner(self, owner):
        """Sets the owner of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param owner: The owner of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._owner = owner

    @property
    def status(self):
        """Gets the status of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The status of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param status: The status of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def updated_time(self):
        """Gets the updated_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501


        :return: The updated_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_time

    @updated_time.setter
    def updated_time(self, updated_time):
        """Sets the updated_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.


        :param updated_time: The updated_time of this OrgQuitApplicationForDescribeQuitApplicationOutput.  # noqa: E501
        :type: str
        """

        self._updated_time = updated_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(OrgQuitApplicationForDescribeQuitApplicationOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, OrgQuitApplicationForDescribeQuitApplicationOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, OrgQuitApplicationForDescribeQuitApplicationOutput):
            return True

        return self.to_dict() != other.to_dict()
