# coding: utf-8

"""
    livesaas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AdInfoForGetAdvertisementDataAPIOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'content': 'str',
        'link': 'str',
        'pv': 'int',
        'page_advertise_type': 'int',
        'title': 'str',
        'uv': 'int'
    }

    attribute_map = {
        'content': 'Content',
        'link': 'Link',
        'pv': 'PV',
        'page_advertise_type': 'PageAdvertiseType',
        'title': 'Title',
        'uv': 'UV'
    }

    def __init__(self, content=None, link=None, pv=None, page_advertise_type=None, title=None, uv=None, _configuration=None):  # noqa: E501
        """AdInfoForGetAdvertisementDataAPIOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._content = None
        self._link = None
        self._pv = None
        self._page_advertise_type = None
        self._title = None
        self._uv = None
        self.discriminator = None

        if content is not None:
            self.content = content
        if link is not None:
            self.link = link
        if pv is not None:
            self.pv = pv
        if page_advertise_type is not None:
            self.page_advertise_type = page_advertise_type
        if title is not None:
            self.title = title
        if uv is not None:
            self.uv = uv

    @property
    def content(self):
        """Gets the content of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501


        :return: The content of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._content

    @content.setter
    def content(self, content):
        """Sets the content of this AdInfoForGetAdvertisementDataAPIOutput.


        :param content: The content of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._content = content

    @property
    def link(self):
        """Gets the link of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501


        :return: The link of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._link

    @link.setter
    def link(self, link):
        """Sets the link of this AdInfoForGetAdvertisementDataAPIOutput.


        :param link: The link of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._link = link

    @property
    def pv(self):
        """Gets the pv of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501


        :return: The pv of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._pv

    @pv.setter
    def pv(self, pv):
        """Sets the pv of this AdInfoForGetAdvertisementDataAPIOutput.


        :param pv: The pv of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._pv = pv

    @property
    def page_advertise_type(self):
        """Gets the page_advertise_type of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501


        :return: The page_advertise_type of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._page_advertise_type

    @page_advertise_type.setter
    def page_advertise_type(self, page_advertise_type):
        """Sets the page_advertise_type of this AdInfoForGetAdvertisementDataAPIOutput.


        :param page_advertise_type: The page_advertise_type of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._page_advertise_type = page_advertise_type

    @property
    def title(self):
        """Gets the title of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501


        :return: The title of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :rtype: str
        """
        return self._title

    @title.setter
    def title(self, title):
        """Sets the title of this AdInfoForGetAdvertisementDataAPIOutput.


        :param title: The title of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :type: str
        """

        self._title = title

    @property
    def uv(self):
        """Gets the uv of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501


        :return: The uv of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :rtype: int
        """
        return self._uv

    @uv.setter
    def uv(self, uv):
        """Sets the uv of this AdInfoForGetAdvertisementDataAPIOutput.


        :param uv: The uv of this AdInfoForGetAdvertisementDataAPIOutput.  # noqa: E501
        :type: int
        """

        self._uv = uv

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AdInfoForGetAdvertisementDataAPIOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AdInfoForGetAdvertisementDataAPIOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AdInfoForGetAdvertisementDataAPIOutput):
            return True

        return self.to_dict() != other.to_dict()
