# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AudienceGroupUserForUpdateAudienceGroupUserConfigInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'audience_group_id': 'int',
        'audience_group_name': 'str',
        'flag_id': 'str',
        'user_id': 'int'
    }

    attribute_map = {
        'audience_group_id': 'AudienceGroupId',
        'audience_group_name': 'AudienceGroupName',
        'flag_id': 'FlagId',
        'user_id': 'UserId'
    }

    def __init__(self, audience_group_id=None, audience_group_name=None, flag_id=None, user_id=None, _configuration=None):  # noqa: E501
        """AudienceGroupUserForUpdateAudienceGroupUserConfigInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._audience_group_id = None
        self._audience_group_name = None
        self._flag_id = None
        self._user_id = None
        self.discriminator = None

        if audience_group_id is not None:
            self.audience_group_id = audience_group_id
        if audience_group_name is not None:
            self.audience_group_name = audience_group_name
        if flag_id is not None:
            self.flag_id = flag_id
        if user_id is not None:
            self.user_id = user_id

    @property
    def audience_group_id(self):
        """Gets the audience_group_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501


        :return: The audience_group_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._audience_group_id

    @audience_group_id.setter
    def audience_group_id(self, audience_group_id):
        """Sets the audience_group_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.


        :param audience_group_id: The audience_group_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501
        :type: int
        """

        self._audience_group_id = audience_group_id

    @property
    def audience_group_name(self):
        """Gets the audience_group_name of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501


        :return: The audience_group_name of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._audience_group_name

    @audience_group_name.setter
    def audience_group_name(self, audience_group_name):
        """Sets the audience_group_name of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.


        :param audience_group_name: The audience_group_name of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501
        :type: str
        """

        self._audience_group_name = audience_group_name

    @property
    def flag_id(self):
        """Gets the flag_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501


        :return: The flag_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501
        :rtype: str
        """
        return self._flag_id

    @flag_id.setter
    def flag_id(self, flag_id):
        """Sets the flag_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.


        :param flag_id: The flag_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501
        :type: str
        """

        self._flag_id = flag_id

    @property
    def user_id(self):
        """Gets the user_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501


        :return: The user_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501
        :rtype: int
        """
        return self._user_id

    @user_id.setter
    def user_id(self, user_id):
        """Sets the user_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.


        :param user_id: The user_id of this AudienceGroupUserForUpdateAudienceGroupUserConfigInput.  # noqa: E501
        :type: int
        """

        self._user_id = user_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AudienceGroupUserForUpdateAudienceGroupUserConfigInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AudienceGroupUserForUpdateAudienceGroupUserConfigInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AudienceGroupUserForUpdateAudienceGroupUserConfigInput):
            return True

        return self.to_dict() != other.to_dict()
