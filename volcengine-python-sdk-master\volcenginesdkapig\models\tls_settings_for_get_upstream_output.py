# coding: utf-8

"""
    apig

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TlsSettingsForGetUpstreamOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'sni': 'str',
        'tls_mode': 'str'
    }

    attribute_map = {
        'sni': 'Sni',
        'tls_mode': 'TlsMode'
    }

    def __init__(self, sni=None, tls_mode=None, _configuration=None):  # noqa: E501
        """TlsSettingsForGetUpstreamOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._sni = None
        self._tls_mode = None
        self.discriminator = None

        if sni is not None:
            self.sni = sni
        if tls_mode is not None:
            self.tls_mode = tls_mode

    @property
    def sni(self):
        """Gets the sni of this TlsSettingsForGetUpstreamOutput.  # noqa: E501


        :return: The sni of this TlsSettingsForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._sni

    @sni.setter
    def sni(self, sni):
        """Sets the sni of this TlsSettingsForGetUpstreamOutput.


        :param sni: The sni of this TlsSettingsForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._sni = sni

    @property
    def tls_mode(self):
        """Gets the tls_mode of this TlsSettingsForGetUpstreamOutput.  # noqa: E501


        :return: The tls_mode of this TlsSettingsForGetUpstreamOutput.  # noqa: E501
        :rtype: str
        """
        return self._tls_mode

    @tls_mode.setter
    def tls_mode(self, tls_mode):
        """Sets the tls_mode of this TlsSettingsForGetUpstreamOutput.


        :param tls_mode: The tls_mode of this TlsSettingsForGetUpstreamOutput.  # noqa: E501
        :type: str
        """

        self._tls_mode = tls_mode

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TlsSettingsForGetUpstreamOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TlsSettingsForGetUpstreamOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TlsSettingsForGetUpstreamOutput):
            return True

        return self.to_dict() != other.to_dict()
