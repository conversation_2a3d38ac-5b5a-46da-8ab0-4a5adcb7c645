# coding: utf-8

"""
    fasttrack

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeWebScraperAttributesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'web_scraper_id': 'str'
    }

    attribute_map = {
        'web_scraper_id': 'WebScraperId'
    }

    def __init__(self, web_scraper_id=None, _configuration=None):  # noqa: E501
        """DescribeWebScraperAttributesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._web_scraper_id = None
        self.discriminator = None

        self.web_scraper_id = web_scraper_id

    @property
    def web_scraper_id(self):
        """Gets the web_scraper_id of this DescribeWebScraperAttributesRequest.  # noqa: E501


        :return: The web_scraper_id of this DescribeWebScraperAttributesRequest.  # noqa: E501
        :rtype: str
        """
        return self._web_scraper_id

    @web_scraper_id.setter
    def web_scraper_id(self, web_scraper_id):
        """Sets the web_scraper_id of this DescribeWebScraperAttributesRequest.


        :param web_scraper_id: The web_scraper_id of this DescribeWebScraperAttributesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and web_scraper_id is None:
            raise ValueError("Invalid value for `web_scraper_id`, must not be `None`")  # noqa: E501

        self._web_scraper_id = web_scraper_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeWebScraperAttributesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeWebScraperAttributesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeWebScraperAttributesRequest):
            return True

        return self.to_dict() != other.to_dict()
