# coding: utf-8

"""
    escloud

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class NodeSpecsAssignForCreateInstanceInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'extra_performance': 'ExtraPerformanceForCreateInstanceInput',
        'number': 'int',
        'resource_spec_name': 'str',
        'storage_size': 'int',
        'storage_spec_name': 'str',
        'type': 'str'
    }

    attribute_map = {
        'extra_performance': 'ExtraPerformance',
        'number': 'Number',
        'resource_spec_name': 'ResourceSpecName',
        'storage_size': 'StorageSize',
        'storage_spec_name': 'StorageSpecName',
        'type': 'Type'
    }

    def __init__(self, extra_performance=None, number=None, resource_spec_name=None, storage_size=None, storage_spec_name=None, type=None, _configuration=None):  # noqa: E501
        """NodeSpecsAssignForCreateInstanceInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._extra_performance = None
        self._number = None
        self._resource_spec_name = None
        self._storage_size = None
        self._storage_spec_name = None
        self._type = None
        self.discriminator = None

        if extra_performance is not None:
            self.extra_performance = extra_performance
        if number is not None:
            self.number = number
        if resource_spec_name is not None:
            self.resource_spec_name = resource_spec_name
        if storage_size is not None:
            self.storage_size = storage_size
        if storage_spec_name is not None:
            self.storage_spec_name = storage_spec_name
        if type is not None:
            self.type = type

    @property
    def extra_performance(self):
        """Gets the extra_performance of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501


        :return: The extra_performance of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :rtype: ExtraPerformanceForCreateInstanceInput
        """
        return self._extra_performance

    @extra_performance.setter
    def extra_performance(self, extra_performance):
        """Sets the extra_performance of this NodeSpecsAssignForCreateInstanceInput.


        :param extra_performance: The extra_performance of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :type: ExtraPerformanceForCreateInstanceInput
        """

        self._extra_performance = extra_performance

    @property
    def number(self):
        """Gets the number of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501


        :return: The number of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._number

    @number.setter
    def number(self, number):
        """Sets the number of this NodeSpecsAssignForCreateInstanceInput.


        :param number: The number of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :type: int
        """

        self._number = number

    @property
    def resource_spec_name(self):
        """Gets the resource_spec_name of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501


        :return: The resource_spec_name of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._resource_spec_name

    @resource_spec_name.setter
    def resource_spec_name(self, resource_spec_name):
        """Sets the resource_spec_name of this NodeSpecsAssignForCreateInstanceInput.


        :param resource_spec_name: The resource_spec_name of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._resource_spec_name = resource_spec_name

    @property
    def storage_size(self):
        """Gets the storage_size of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501


        :return: The storage_size of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :rtype: int
        """
        return self._storage_size

    @storage_size.setter
    def storage_size(self, storage_size):
        """Sets the storage_size of this NodeSpecsAssignForCreateInstanceInput.


        :param storage_size: The storage_size of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :type: int
        """

        self._storage_size = storage_size

    @property
    def storage_spec_name(self):
        """Gets the storage_spec_name of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501


        :return: The storage_spec_name of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._storage_spec_name

    @storage_spec_name.setter
    def storage_spec_name(self, storage_spec_name):
        """Sets the storage_spec_name of this NodeSpecsAssignForCreateInstanceInput.


        :param storage_spec_name: The storage_spec_name of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :type: str
        """

        self._storage_spec_name = storage_spec_name

    @property
    def type(self):
        """Gets the type of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501


        :return: The type of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this NodeSpecsAssignForCreateInstanceInput.


        :param type: The type of this NodeSpecsAssignForCreateInstanceInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Master", "Kibana", "Hot", "Warm", "Cold", "Coordinator", "Other"]  # noqa: E501
        if (self._configuration.client_side_validation and
                type not in allowed_values):
            raise ValueError(
                "Invalid value for `type` ({0}), must be one of {1}"  # noqa: E501
                .format(type, allowed_values)
            )

        self._type = type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(NodeSpecsAssignForCreateInstanceInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, NodeSpecsAssignForCreateInstanceInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, NodeSpecsAssignForCreateInstanceInput):
            return True

        return self.to_dict() != other.to_dict()
