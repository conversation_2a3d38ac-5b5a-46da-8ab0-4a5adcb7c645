# coding: utf-8

"""
    bio

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateSubmissionRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_id': 'str',
        'data_model_id': 'str',
        'data_model_row_ids': 'list[str]',
        'description': 'str',
        'exposed_options': 'ExposedOptionsForCreateSubmissionInput',
        'inputs': 'str',
        'name': 'str',
        'outputs': 'str',
        'workflow_id': 'str',
        'workspace_id': 'str'
    }

    attribute_map = {
        'cluster_id': 'ClusterID',
        'data_model_id': 'DataModelID',
        'data_model_row_ids': 'DataModelRowIDs',
        'description': 'Description',
        'exposed_options': 'ExposedOptions',
        'inputs': 'Inputs',
        'name': 'Name',
        'outputs': 'Outputs',
        'workflow_id': 'WorkflowID',
        'workspace_id': 'WorkspaceID'
    }

    def __init__(self, cluster_id=None, data_model_id=None, data_model_row_ids=None, description=None, exposed_options=None, inputs=None, name=None, outputs=None, workflow_id=None, workspace_id=None, _configuration=None):  # noqa: E501
        """CreateSubmissionRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_id = None
        self._data_model_id = None
        self._data_model_row_ids = None
        self._description = None
        self._exposed_options = None
        self._inputs = None
        self._name = None
        self._outputs = None
        self._workflow_id = None
        self._workspace_id = None
        self.discriminator = None

        self.cluster_id = cluster_id
        if data_model_id is not None:
            self.data_model_id = data_model_id
        if data_model_row_ids is not None:
            self.data_model_row_ids = data_model_row_ids
        if description is not None:
            self.description = description
        if exposed_options is not None:
            self.exposed_options = exposed_options
        self.inputs = inputs
        self.name = name
        self.outputs = outputs
        self.workflow_id = workflow_id
        self.workspace_id = workspace_id

    @property
    def cluster_id(self):
        """Gets the cluster_id of this CreateSubmissionRequest.  # noqa: E501


        :return: The cluster_id of this CreateSubmissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this CreateSubmissionRequest.


        :param cluster_id: The cluster_id of this CreateSubmissionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_id is None:
            raise ValueError("Invalid value for `cluster_id`, must not be `None`")  # noqa: E501

        self._cluster_id = cluster_id

    @property
    def data_model_id(self):
        """Gets the data_model_id of this CreateSubmissionRequest.  # noqa: E501


        :return: The data_model_id of this CreateSubmissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._data_model_id

    @data_model_id.setter
    def data_model_id(self, data_model_id):
        """Sets the data_model_id of this CreateSubmissionRequest.


        :param data_model_id: The data_model_id of this CreateSubmissionRequest.  # noqa: E501
        :type: str
        """

        self._data_model_id = data_model_id

    @property
    def data_model_row_ids(self):
        """Gets the data_model_row_ids of this CreateSubmissionRequest.  # noqa: E501


        :return: The data_model_row_ids of this CreateSubmissionRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._data_model_row_ids

    @data_model_row_ids.setter
    def data_model_row_ids(self, data_model_row_ids):
        """Sets the data_model_row_ids of this CreateSubmissionRequest.


        :param data_model_row_ids: The data_model_row_ids of this CreateSubmissionRequest.  # noqa: E501
        :type: list[str]
        """

        self._data_model_row_ids = data_model_row_ids

    @property
    def description(self):
        """Gets the description of this CreateSubmissionRequest.  # noqa: E501


        :return: The description of this CreateSubmissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateSubmissionRequest.


        :param description: The description of this CreateSubmissionRequest.  # noqa: E501
        :type: str
        """
        if (self._configuration.client_side_validation and
                description is not None and len(description) > 1000):
            raise ValueError("Invalid value for `description`, length must be less than or equal to `1000`")  # noqa: E501

        self._description = description

    @property
    def exposed_options(self):
        """Gets the exposed_options of this CreateSubmissionRequest.  # noqa: E501


        :return: The exposed_options of this CreateSubmissionRequest.  # noqa: E501
        :rtype: ExposedOptionsForCreateSubmissionInput
        """
        return self._exposed_options

    @exposed_options.setter
    def exposed_options(self, exposed_options):
        """Sets the exposed_options of this CreateSubmissionRequest.


        :param exposed_options: The exposed_options of this CreateSubmissionRequest.  # noqa: E501
        :type: ExposedOptionsForCreateSubmissionInput
        """

        self._exposed_options = exposed_options

    @property
    def inputs(self):
        """Gets the inputs of this CreateSubmissionRequest.  # noqa: E501


        :return: The inputs of this CreateSubmissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._inputs

    @inputs.setter
    def inputs(self, inputs):
        """Sets the inputs of this CreateSubmissionRequest.


        :param inputs: The inputs of this CreateSubmissionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and inputs is None:
            raise ValueError("Invalid value for `inputs`, must not be `None`")  # noqa: E501

        self._inputs = inputs

    @property
    def name(self):
        """Gets the name of this CreateSubmissionRequest.  # noqa: E501


        :return: The name of this CreateSubmissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateSubmissionRequest.


        :param name: The name of this CreateSubmissionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) > 409):
            raise ValueError("Invalid value for `name`, length must be less than or equal to `409`")  # noqa: E501
        if (self._configuration.client_side_validation and
                name is not None and len(name) < 11):
            raise ValueError("Invalid value for `name`, length must be greater than or equal to `11`")  # noqa: E501

        self._name = name

    @property
    def outputs(self):
        """Gets the outputs of this CreateSubmissionRequest.  # noqa: E501


        :return: The outputs of this CreateSubmissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._outputs

    @outputs.setter
    def outputs(self, outputs):
        """Sets the outputs of this CreateSubmissionRequest.


        :param outputs: The outputs of this CreateSubmissionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and outputs is None:
            raise ValueError("Invalid value for `outputs`, must not be `None`")  # noqa: E501

        self._outputs = outputs

    @property
    def workflow_id(self):
        """Gets the workflow_id of this CreateSubmissionRequest.  # noqa: E501


        :return: The workflow_id of this CreateSubmissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._workflow_id

    @workflow_id.setter
    def workflow_id(self, workflow_id):
        """Sets the workflow_id of this CreateSubmissionRequest.


        :param workflow_id: The workflow_id of this CreateSubmissionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and workflow_id is None:
            raise ValueError("Invalid value for `workflow_id`, must not be `None`")  # noqa: E501

        self._workflow_id = workflow_id

    @property
    def workspace_id(self):
        """Gets the workspace_id of this CreateSubmissionRequest.  # noqa: E501


        :return: The workspace_id of this CreateSubmissionRequest.  # noqa: E501
        :rtype: str
        """
        return self._workspace_id

    @workspace_id.setter
    def workspace_id(self, workspace_id):
        """Sets the workspace_id of this CreateSubmissionRequest.


        :param workspace_id: The workspace_id of this CreateSubmissionRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and workspace_id is None:
            raise ValueError("Invalid value for `workspace_id`, must not be `None`")  # noqa: E501

        self._workspace_id = workspace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateSubmissionRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateSubmissionRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateSubmissionRequest):
            return True

        return self.to_dict() != other.to_dict()
