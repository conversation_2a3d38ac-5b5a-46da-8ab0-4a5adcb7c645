# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceConfigForCreateResourceGroupInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'compute_resources': 'list[ComputeResourceForCreateResourceGroupInput]',
        'volume_resources': 'list[VolumeResourceForCreateResourceGroupInput]'
    }

    attribute_map = {
        'compute_resources': 'ComputeResources',
        'volume_resources': 'VolumeResources'
    }

    def __init__(self, compute_resources=None, volume_resources=None, _configuration=None):  # noqa: E501
        """ResourceConfigForCreateResourceGroupInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._compute_resources = None
        self._volume_resources = None
        self.discriminator = None

        if compute_resources is not None:
            self.compute_resources = compute_resources
        if volume_resources is not None:
            self.volume_resources = volume_resources

    @property
    def compute_resources(self):
        """Gets the compute_resources of this ResourceConfigForCreateResourceGroupInput.  # noqa: E501


        :return: The compute_resources of this ResourceConfigForCreateResourceGroupInput.  # noqa: E501
        :rtype: list[ComputeResourceForCreateResourceGroupInput]
        """
        return self._compute_resources

    @compute_resources.setter
    def compute_resources(self, compute_resources):
        """Sets the compute_resources of this ResourceConfigForCreateResourceGroupInput.


        :param compute_resources: The compute_resources of this ResourceConfigForCreateResourceGroupInput.  # noqa: E501
        :type: list[ComputeResourceForCreateResourceGroupInput]
        """

        self._compute_resources = compute_resources

    @property
    def volume_resources(self):
        """Gets the volume_resources of this ResourceConfigForCreateResourceGroupInput.  # noqa: E501


        :return: The volume_resources of this ResourceConfigForCreateResourceGroupInput.  # noqa: E501
        :rtype: list[VolumeResourceForCreateResourceGroupInput]
        """
        return self._volume_resources

    @volume_resources.setter
    def volume_resources(self, volume_resources):
        """Sets the volume_resources of this ResourceConfigForCreateResourceGroupInput.


        :param volume_resources: The volume_resources of this ResourceConfigForCreateResourceGroupInput.  # noqa: E501
        :type: list[VolumeResourceForCreateResourceGroupInput]
        """

        self._volume_resources = volume_resources

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceConfigForCreateResourceGroupInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceConfigForCreateResourceGroupInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceConfigForCreateResourceGroupInput):
            return True

        return self.to_dict() != other.to_dict()
