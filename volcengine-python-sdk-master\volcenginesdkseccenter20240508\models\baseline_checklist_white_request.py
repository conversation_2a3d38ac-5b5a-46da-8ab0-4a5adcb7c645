# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BaselineChecklistWhiteRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id_list': 'list[str]',
        'asset_id_list': 'list[str]',
        'asset_type': 'str',
        'baseline_id': 'int',
        'checklist_id_list': 'list[int]',
        'conditions': 'ConditionsForBaselineChecklistWhiteInput',
        'if_white': 'bool',
        'whitelist_detail': 'str'
    }

    attribute_map = {
        'agent_id_list': 'AgentIDList',
        'asset_id_list': 'AssetIDList',
        'asset_type': 'AssetType',
        'baseline_id': 'BaselineID',
        'checklist_id_list': 'ChecklistIdList',
        'conditions': 'Conditions',
        'if_white': 'IfWhite',
        'whitelist_detail': 'WhitelistDetail'
    }

    def __init__(self, agent_id_list=None, asset_id_list=None, asset_type=None, baseline_id=None, checklist_id_list=None, conditions=None, if_white=None, whitelist_detail=None, _configuration=None):  # noqa: E501
        """BaselineChecklistWhiteRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id_list = None
        self._asset_id_list = None
        self._asset_type = None
        self._baseline_id = None
        self._checklist_id_list = None
        self._conditions = None
        self._if_white = None
        self._whitelist_detail = None
        self.discriminator = None

        if agent_id_list is not None:
            self.agent_id_list = agent_id_list
        if asset_id_list is not None:
            self.asset_id_list = asset_id_list
        if asset_type is not None:
            self.asset_type = asset_type
        if baseline_id is not None:
            self.baseline_id = baseline_id
        if checklist_id_list is not None:
            self.checklist_id_list = checklist_id_list
        if conditions is not None:
            self.conditions = conditions
        if if_white is not None:
            self.if_white = if_white
        if whitelist_detail is not None:
            self.whitelist_detail = whitelist_detail

    @property
    def agent_id_list(self):
        """Gets the agent_id_list of this BaselineChecklistWhiteRequest.  # noqa: E501


        :return: The agent_id_list of this BaselineChecklistWhiteRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._agent_id_list

    @agent_id_list.setter
    def agent_id_list(self, agent_id_list):
        """Sets the agent_id_list of this BaselineChecklistWhiteRequest.


        :param agent_id_list: The agent_id_list of this BaselineChecklistWhiteRequest.  # noqa: E501
        :type: list[str]
        """

        self._agent_id_list = agent_id_list

    @property
    def asset_id_list(self):
        """Gets the asset_id_list of this BaselineChecklistWhiteRequest.  # noqa: E501


        :return: The asset_id_list of this BaselineChecklistWhiteRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._asset_id_list

    @asset_id_list.setter
    def asset_id_list(self, asset_id_list):
        """Sets the asset_id_list of this BaselineChecklistWhiteRequest.


        :param asset_id_list: The asset_id_list of this BaselineChecklistWhiteRequest.  # noqa: E501
        :type: list[str]
        """

        self._asset_id_list = asset_id_list

    @property
    def asset_type(self):
        """Gets the asset_type of this BaselineChecklistWhiteRequest.  # noqa: E501


        :return: The asset_type of this BaselineChecklistWhiteRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_type

    @asset_type.setter
    def asset_type(self, asset_type):
        """Sets the asset_type of this BaselineChecklistWhiteRequest.


        :param asset_type: The asset_type of this BaselineChecklistWhiteRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Host", "Dev"]  # noqa: E501
        if (self._configuration.client_side_validation and
                asset_type not in allowed_values):
            raise ValueError(
                "Invalid value for `asset_type` ({0}), must be one of {1}"  # noqa: E501
                .format(asset_type, allowed_values)
            )

        self._asset_type = asset_type

    @property
    def baseline_id(self):
        """Gets the baseline_id of this BaselineChecklistWhiteRequest.  # noqa: E501


        :return: The baseline_id of this BaselineChecklistWhiteRequest.  # noqa: E501
        :rtype: int
        """
        return self._baseline_id

    @baseline_id.setter
    def baseline_id(self, baseline_id):
        """Sets the baseline_id of this BaselineChecklistWhiteRequest.


        :param baseline_id: The baseline_id of this BaselineChecklistWhiteRequest.  # noqa: E501
        :type: int
        """

        self._baseline_id = baseline_id

    @property
    def checklist_id_list(self):
        """Gets the checklist_id_list of this BaselineChecklistWhiteRequest.  # noqa: E501


        :return: The checklist_id_list of this BaselineChecklistWhiteRequest.  # noqa: E501
        :rtype: list[int]
        """
        return self._checklist_id_list

    @checklist_id_list.setter
    def checklist_id_list(self, checklist_id_list):
        """Sets the checklist_id_list of this BaselineChecklistWhiteRequest.


        :param checklist_id_list: The checklist_id_list of this BaselineChecklistWhiteRequest.  # noqa: E501
        :type: list[int]
        """

        self._checklist_id_list = checklist_id_list

    @property
    def conditions(self):
        """Gets the conditions of this BaselineChecklistWhiteRequest.  # noqa: E501


        :return: The conditions of this BaselineChecklistWhiteRequest.  # noqa: E501
        :rtype: ConditionsForBaselineChecklistWhiteInput
        """
        return self._conditions

    @conditions.setter
    def conditions(self, conditions):
        """Sets the conditions of this BaselineChecklistWhiteRequest.


        :param conditions: The conditions of this BaselineChecklistWhiteRequest.  # noqa: E501
        :type: ConditionsForBaselineChecklistWhiteInput
        """

        self._conditions = conditions

    @property
    def if_white(self):
        """Gets the if_white of this BaselineChecklistWhiteRequest.  # noqa: E501


        :return: The if_white of this BaselineChecklistWhiteRequest.  # noqa: E501
        :rtype: bool
        """
        return self._if_white

    @if_white.setter
    def if_white(self, if_white):
        """Sets the if_white of this BaselineChecklistWhiteRequest.


        :param if_white: The if_white of this BaselineChecklistWhiteRequest.  # noqa: E501
        :type: bool
        """

        self._if_white = if_white

    @property
    def whitelist_detail(self):
        """Gets the whitelist_detail of this BaselineChecklistWhiteRequest.  # noqa: E501


        :return: The whitelist_detail of this BaselineChecklistWhiteRequest.  # noqa: E501
        :rtype: str
        """
        return self._whitelist_detail

    @whitelist_detail.setter
    def whitelist_detail(self, whitelist_detail):
        """Sets the whitelist_detail of this BaselineChecklistWhiteRequest.


        :param whitelist_detail: The whitelist_detail of this BaselineChecklistWhiteRequest.  # noqa: E501
        :type: str
        """

        self._whitelist_detail = whitelist_detail

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BaselineChecklistWhiteRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BaselineChecklistWhiteRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BaselineChecklistWhiteRequest):
            return True

        return self.to_dict() != other.to_dict()
