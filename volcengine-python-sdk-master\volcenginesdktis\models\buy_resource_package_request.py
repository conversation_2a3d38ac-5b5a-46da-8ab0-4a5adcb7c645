# coding: utf-8

"""
    tis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BuyResourcePackageRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'device_name': 'str',
        'item': 'str',
        'product_key': 'str'
    }

    attribute_map = {
        'device_name': 'deviceName',
        'item': 'item',
        'product_key': 'productKey'
    }

    def __init__(self, device_name=None, item=None, product_key=None, _configuration=None):  # noqa: E501
        """BuyResourcePackageRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._device_name = None
        self._item = None
        self._product_key = None
        self.discriminator = None

        self.device_name = device_name
        self.item = item
        self.product_key = product_key

    @property
    def device_name(self):
        """Gets the device_name of this BuyResourcePackageRequest.  # noqa: E501


        :return: The device_name of this BuyResourcePackageRequest.  # noqa: E501
        :rtype: str
        """
        return self._device_name

    @device_name.setter
    def device_name(self, device_name):
        """Sets the device_name of this BuyResourcePackageRequest.


        :param device_name: The device_name of this BuyResourcePackageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and device_name is None:
            raise ValueError("Invalid value for `device_name`, must not be `None`")  # noqa: E501

        self._device_name = device_name

    @property
    def item(self):
        """Gets the item of this BuyResourcePackageRequest.  # noqa: E501


        :return: The item of this BuyResourcePackageRequest.  # noqa: E501
        :rtype: str
        """
        return self._item

    @item.setter
    def item(self, item):
        """Sets the item of this BuyResourcePackageRequest.


        :param item: The item of this BuyResourcePackageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and item is None:
            raise ValueError("Invalid value for `item`, must not be `None`")  # noqa: E501

        self._item = item

    @property
    def product_key(self):
        """Gets the product_key of this BuyResourcePackageRequest.  # noqa: E501


        :return: The product_key of this BuyResourcePackageRequest.  # noqa: E501
        :rtype: str
        """
        return self._product_key

    @product_key.setter
    def product_key(self, product_key):
        """Sets the product_key of this BuyResourcePackageRequest.


        :param product_key: The product_key of this BuyResourcePackageRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and product_key is None:
            raise ValueError("Invalid value for `product_key`, must not be `None`")  # noqa: E501

        self._product_key = product_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BuyResourcePackageRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BuyResourcePackageRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BuyResourcePackageRequest):
            return True

        return self.to_dict() != other.to_dict()
