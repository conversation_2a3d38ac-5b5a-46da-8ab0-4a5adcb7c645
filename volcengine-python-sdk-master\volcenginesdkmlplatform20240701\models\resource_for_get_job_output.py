# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResourceForGetJobOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'flexible_resource_claim': 'FlexibleResourceClaimForGetJobOutput',
        'instance_type_id': 'str',
        'type': 'str',
        'zone_id': 'str'
    }

    attribute_map = {
        'flexible_resource_claim': 'FlexibleResourceClaim',
        'instance_type_id': 'InstanceTypeId',
        'type': 'Type',
        'zone_id': 'ZoneId'
    }

    def __init__(self, flexible_resource_claim=None, instance_type_id=None, type=None, zone_id=None, _configuration=None):  # noqa: E501
        """ResourceForGetJobOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._flexible_resource_claim = None
        self._instance_type_id = None
        self._type = None
        self._zone_id = None
        self.discriminator = None

        if flexible_resource_claim is not None:
            self.flexible_resource_claim = flexible_resource_claim
        if instance_type_id is not None:
            self.instance_type_id = instance_type_id
        if type is not None:
            self.type = type
        if zone_id is not None:
            self.zone_id = zone_id

    @property
    def flexible_resource_claim(self):
        """Gets the flexible_resource_claim of this ResourceForGetJobOutput.  # noqa: E501


        :return: The flexible_resource_claim of this ResourceForGetJobOutput.  # noqa: E501
        :rtype: FlexibleResourceClaimForGetJobOutput
        """
        return self._flexible_resource_claim

    @flexible_resource_claim.setter
    def flexible_resource_claim(self, flexible_resource_claim):
        """Sets the flexible_resource_claim of this ResourceForGetJobOutput.


        :param flexible_resource_claim: The flexible_resource_claim of this ResourceForGetJobOutput.  # noqa: E501
        :type: FlexibleResourceClaimForGetJobOutput
        """

        self._flexible_resource_claim = flexible_resource_claim

    @property
    def instance_type_id(self):
        """Gets the instance_type_id of this ResourceForGetJobOutput.  # noqa: E501


        :return: The instance_type_id of this ResourceForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_type_id

    @instance_type_id.setter
    def instance_type_id(self, instance_type_id):
        """Sets the instance_type_id of this ResourceForGetJobOutput.


        :param instance_type_id: The instance_type_id of this ResourceForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._instance_type_id = instance_type_id

    @property
    def type(self):
        """Gets the type of this ResourceForGetJobOutput.  # noqa: E501


        :return: The type of this ResourceForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ResourceForGetJobOutput.


        :param type: The type of this ResourceForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def zone_id(self):
        """Gets the zone_id of this ResourceForGetJobOutput.  # noqa: E501


        :return: The zone_id of this ResourceForGetJobOutput.  # noqa: E501
        :rtype: str
        """
        return self._zone_id

    @zone_id.setter
    def zone_id(self, zone_id):
        """Sets the zone_id of this ResourceForGetJobOutput.


        :param zone_id: The zone_id of this ResourceForGetJobOutput.  # noqa: E501
        :type: str
        """

        self._zone_id = zone_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResourceForGetJobOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResourceForGetJobOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResourceForGetJobOutput):
            return True

        return self.to_dict() != other.to_dict()
