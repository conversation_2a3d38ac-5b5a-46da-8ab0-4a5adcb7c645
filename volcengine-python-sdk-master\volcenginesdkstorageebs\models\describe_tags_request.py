# coding: utf-8

"""
    storage_ebs

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeTagsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'int',
        'resource_ids': 'list[str]',
        'resource_type': 'str',
        'sys_tag_visible': 'bool',
        'tag_filters': 'list[TagFilterForDescribeTagsInput]'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'resource_ids': 'ResourceIds',
        'resource_type': 'ResourceType',
        'sys_tag_visible': 'SysTagVisible',
        'tag_filters': 'TagFilters'
    }

    def __init__(self, page_number=None, page_size=None, resource_ids=None, resource_type=None, sys_tag_visible=None, tag_filters=None, _configuration=None):  # noqa: E501
        """DescribeTagsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._resource_ids = None
        self._resource_type = None
        self._sys_tag_visible = None
        self._tag_filters = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if resource_ids is not None:
            self.resource_ids = resource_ids
        self.resource_type = resource_type
        if sys_tag_visible is not None:
            self.sys_tag_visible = sys_tag_visible
        if tag_filters is not None:
            self.tag_filters = tag_filters

    @property
    def page_number(self):
        """Gets the page_number of this DescribeTagsRequest.  # noqa: E501


        :return: The page_number of this DescribeTagsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this DescribeTagsRequest.


        :param page_number: The page_number of this DescribeTagsRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this DescribeTagsRequest.  # noqa: E501


        :return: The page_size of this DescribeTagsRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this DescribeTagsRequest.


        :param page_size: The page_size of this DescribeTagsRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def resource_ids(self):
        """Gets the resource_ids of this DescribeTagsRequest.  # noqa: E501


        :return: The resource_ids of this DescribeTagsRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._resource_ids

    @resource_ids.setter
    def resource_ids(self, resource_ids):
        """Sets the resource_ids of this DescribeTagsRequest.


        :param resource_ids: The resource_ids of this DescribeTagsRequest.  # noqa: E501
        :type: list[str]
        """

        self._resource_ids = resource_ids

    @property
    def resource_type(self):
        """Gets the resource_type of this DescribeTagsRequest.  # noqa: E501


        :return: The resource_type of this DescribeTagsRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this DescribeTagsRequest.


        :param resource_type: The resource_type of this DescribeTagsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_type is None:
            raise ValueError("Invalid value for `resource_type`, must not be `None`")  # noqa: E501

        self._resource_type = resource_type

    @property
    def sys_tag_visible(self):
        """Gets the sys_tag_visible of this DescribeTagsRequest.  # noqa: E501


        :return: The sys_tag_visible of this DescribeTagsRequest.  # noqa: E501
        :rtype: bool
        """
        return self._sys_tag_visible

    @sys_tag_visible.setter
    def sys_tag_visible(self, sys_tag_visible):
        """Sets the sys_tag_visible of this DescribeTagsRequest.


        :param sys_tag_visible: The sys_tag_visible of this DescribeTagsRequest.  # noqa: E501
        :type: bool
        """

        self._sys_tag_visible = sys_tag_visible

    @property
    def tag_filters(self):
        """Gets the tag_filters of this DescribeTagsRequest.  # noqa: E501


        :return: The tag_filters of this DescribeTagsRequest.  # noqa: E501
        :rtype: list[TagFilterForDescribeTagsInput]
        """
        return self._tag_filters

    @tag_filters.setter
    def tag_filters(self, tag_filters):
        """Sets the tag_filters of this DescribeTagsRequest.


        :param tag_filters: The tag_filters of this DescribeTagsRequest.  # noqa: E501
        :type: list[TagFilterForDescribeTagsInput]
        """

        self._tag_filters = tag_filters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeTagsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeTagsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeTagsRequest):
            return True

        return self.to_dict() != other.to_dict()
