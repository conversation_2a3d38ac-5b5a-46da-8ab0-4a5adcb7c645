# coding: utf-8

"""
    cdn

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListSharedConfigRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'config_name': 'str',
        'config_type': 'str',
        'config_type_list': 'list[str]',
        'page_num': 'int',
        'page_size': 'int',
        'project': 'str'
    }

    attribute_map = {
        'config_name': 'ConfigName',
        'config_type': 'ConfigType',
        'config_type_list': 'ConfigTypeList',
        'page_num': 'PageNum',
        'page_size': 'PageSize',
        'project': 'Project'
    }

    def __init__(self, config_name=None, config_type=None, config_type_list=None, page_num=None, page_size=None, project=None, _configuration=None):  # noqa: E501
        """ListSharedConfigRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._config_name = None
        self._config_type = None
        self._config_type_list = None
        self._page_num = None
        self._page_size = None
        self._project = None
        self.discriminator = None

        if config_name is not None:
            self.config_name = config_name
        if config_type is not None:
            self.config_type = config_type
        if config_type_list is not None:
            self.config_type_list = config_type_list
        if page_num is not None:
            self.page_num = page_num
        if page_size is not None:
            self.page_size = page_size
        if project is not None:
            self.project = project

    @property
    def config_name(self):
        """Gets the config_name of this ListSharedConfigRequest.  # noqa: E501


        :return: The config_name of this ListSharedConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._config_name

    @config_name.setter
    def config_name(self, config_name):
        """Sets the config_name of this ListSharedConfigRequest.


        :param config_name: The config_name of this ListSharedConfigRequest.  # noqa: E501
        :type: str
        """

        self._config_name = config_name

    @property
    def config_type(self):
        """Gets the config_type of this ListSharedConfigRequest.  # noqa: E501


        :return: The config_type of this ListSharedConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._config_type

    @config_type.setter
    def config_type(self, config_type):
        """Sets the config_type of this ListSharedConfigRequest.


        :param config_type: The config_type of this ListSharedConfigRequest.  # noqa: E501
        :type: str
        """

        self._config_type = config_type

    @property
    def config_type_list(self):
        """Gets the config_type_list of this ListSharedConfigRequest.  # noqa: E501


        :return: The config_type_list of this ListSharedConfigRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._config_type_list

    @config_type_list.setter
    def config_type_list(self, config_type_list):
        """Sets the config_type_list of this ListSharedConfigRequest.


        :param config_type_list: The config_type_list of this ListSharedConfigRequest.  # noqa: E501
        :type: list[str]
        """

        self._config_type_list = config_type_list

    @property
    def page_num(self):
        """Gets the page_num of this ListSharedConfigRequest.  # noqa: E501


        :return: The page_num of this ListSharedConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_num

    @page_num.setter
    def page_num(self, page_num):
        """Sets the page_num of this ListSharedConfigRequest.


        :param page_num: The page_num of this ListSharedConfigRequest.  # noqa: E501
        :type: int
        """

        self._page_num = page_num

    @property
    def page_size(self):
        """Gets the page_size of this ListSharedConfigRequest.  # noqa: E501


        :return: The page_size of this ListSharedConfigRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListSharedConfigRequest.


        :param page_size: The page_size of this ListSharedConfigRequest.  # noqa: E501
        :type: int
        """

        self._page_size = page_size

    @property
    def project(self):
        """Gets the project of this ListSharedConfigRequest.  # noqa: E501


        :return: The project of this ListSharedConfigRequest.  # noqa: E501
        :rtype: str
        """
        return self._project

    @project.setter
    def project(self, project):
        """Sets the project of this ListSharedConfigRequest.


        :param project: The project of this ListSharedConfigRequest.  # noqa: E501
        :type: str
        """

        self._project = project

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListSharedConfigRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListSharedConfigRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListSharedConfigRequest):
            return True

        return self.to_dict() != other.to_dict()
