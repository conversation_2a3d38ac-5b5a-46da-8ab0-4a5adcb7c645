# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListQuotaApplicationsResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'applications': 'list[ApplicationForListQuotaApplicationsOutput]',
        'next_token': 'str',
        'results_num': 'int',
        'total_count': 'int'
    }

    attribute_map = {
        'applications': 'Applications',
        'next_token': 'NextToken',
        'results_num': 'ResultsNum',
        'total_count': 'TotalCount'
    }

    def __init__(self, applications=None, next_token=None, results_num=None, total_count=None, _configuration=None):  # noqa: E501
        """ListQuotaApplicationsResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._applications = None
        self._next_token = None
        self._results_num = None
        self._total_count = None
        self.discriminator = None

        if applications is not None:
            self.applications = applications
        if next_token is not None:
            self.next_token = next_token
        if results_num is not None:
            self.results_num = results_num
        if total_count is not None:
            self.total_count = total_count

    @property
    def applications(self):
        """Gets the applications of this ListQuotaApplicationsResponse.  # noqa: E501


        :return: The applications of this ListQuotaApplicationsResponse.  # noqa: E501
        :rtype: list[ApplicationForListQuotaApplicationsOutput]
        """
        return self._applications

    @applications.setter
    def applications(self, applications):
        """Sets the applications of this ListQuotaApplicationsResponse.


        :param applications: The applications of this ListQuotaApplicationsResponse.  # noqa: E501
        :type: list[ApplicationForListQuotaApplicationsOutput]
        """

        self._applications = applications

    @property
    def next_token(self):
        """Gets the next_token of this ListQuotaApplicationsResponse.  # noqa: E501


        :return: The next_token of this ListQuotaApplicationsResponse.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListQuotaApplicationsResponse.


        :param next_token: The next_token of this ListQuotaApplicationsResponse.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def results_num(self):
        """Gets the results_num of this ListQuotaApplicationsResponse.  # noqa: E501


        :return: The results_num of this ListQuotaApplicationsResponse.  # noqa: E501
        :rtype: int
        """
        return self._results_num

    @results_num.setter
    def results_num(self, results_num):
        """Sets the results_num of this ListQuotaApplicationsResponse.


        :param results_num: The results_num of this ListQuotaApplicationsResponse.  # noqa: E501
        :type: int
        """

        self._results_num = results_num

    @property
    def total_count(self):
        """Gets the total_count of this ListQuotaApplicationsResponse.  # noqa: E501


        :return: The total_count of this ListQuotaApplicationsResponse.  # noqa: E501
        :rtype: int
        """
        return self._total_count

    @total_count.setter
    def total_count(self, total_count):
        """Sets the total_count of this ListQuotaApplicationsResponse.


        :param total_count: The total_count of this ListQuotaApplicationsResponse.  # noqa: E501
        :type: int
        """

        self._total_count = total_count

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListQuotaApplicationsResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListQuotaApplicationsResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListQuotaApplicationsResponse):
            return True

        return self.to_dict() != other.to_dict()
