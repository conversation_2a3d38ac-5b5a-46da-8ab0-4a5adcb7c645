# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AffectPackForGetVulnDetailOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'affected_version': 'str',
        'fixed_version': 'str',
        'pkg_name': 'str',
        'vendor': 'str',
        'vendor_vuln_id': 'str'
    }

    attribute_map = {
        'affected_version': 'AffectedVersion',
        'fixed_version': 'FixedVersion',
        'pkg_name': 'PkgName',
        'vendor': 'Vendor',
        'vendor_vuln_id': 'VendorVulnID'
    }

    def __init__(self, affected_version=None, fixed_version=None, pkg_name=None, vendor=None, vendor_vuln_id=None, _configuration=None):  # noqa: E501
        """AffectPackForGetVulnDetailOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._affected_version = None
        self._fixed_version = None
        self._pkg_name = None
        self._vendor = None
        self._vendor_vuln_id = None
        self.discriminator = None

        if affected_version is not None:
            self.affected_version = affected_version
        if fixed_version is not None:
            self.fixed_version = fixed_version
        if pkg_name is not None:
            self.pkg_name = pkg_name
        if vendor is not None:
            self.vendor = vendor
        if vendor_vuln_id is not None:
            self.vendor_vuln_id = vendor_vuln_id

    @property
    def affected_version(self):
        """Gets the affected_version of this AffectPackForGetVulnDetailOutput.  # noqa: E501


        :return: The affected_version of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._affected_version

    @affected_version.setter
    def affected_version(self, affected_version):
        """Sets the affected_version of this AffectPackForGetVulnDetailOutput.


        :param affected_version: The affected_version of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._affected_version = affected_version

    @property
    def fixed_version(self):
        """Gets the fixed_version of this AffectPackForGetVulnDetailOutput.  # noqa: E501


        :return: The fixed_version of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._fixed_version

    @fixed_version.setter
    def fixed_version(self, fixed_version):
        """Sets the fixed_version of this AffectPackForGetVulnDetailOutput.


        :param fixed_version: The fixed_version of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._fixed_version = fixed_version

    @property
    def pkg_name(self):
        """Gets the pkg_name of this AffectPackForGetVulnDetailOutput.  # noqa: E501


        :return: The pkg_name of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._pkg_name

    @pkg_name.setter
    def pkg_name(self, pkg_name):
        """Sets the pkg_name of this AffectPackForGetVulnDetailOutput.


        :param pkg_name: The pkg_name of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._pkg_name = pkg_name

    @property
    def vendor(self):
        """Gets the vendor of this AffectPackForGetVulnDetailOutput.  # noqa: E501


        :return: The vendor of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._vendor

    @vendor.setter
    def vendor(self, vendor):
        """Sets the vendor of this AffectPackForGetVulnDetailOutput.


        :param vendor: The vendor of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._vendor = vendor

    @property
    def vendor_vuln_id(self):
        """Gets the vendor_vuln_id of this AffectPackForGetVulnDetailOutput.  # noqa: E501


        :return: The vendor_vuln_id of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :rtype: str
        """
        return self._vendor_vuln_id

    @vendor_vuln_id.setter
    def vendor_vuln_id(self, vendor_vuln_id):
        """Sets the vendor_vuln_id of this AffectPackForGetVulnDetailOutput.


        :param vendor_vuln_id: The vendor_vuln_id of this AffectPackForGetVulnDetailOutput.  # noqa: E501
        :type: str
        """

        self._vendor_vuln_id = vendor_vuln_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AffectPackForGetVulnDetailOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AffectPackForGetVulnDetailOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AffectPackForGetVulnDetailOutput):
            return True

        return self.to_dict() != other.to_dict()
