# coding: utf-8

"""
    clb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ResultForDescribeListenerHealthOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'ip': 'str',
        'port': 'int',
        'rule_number': 'int',
        'server_group_id': 'str',
        'server_id': 'str',
        'status': 'str',
        'type': 'str',
        'updated_at': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'ip': 'Ip',
        'port': 'Port',
        'rule_number': 'RuleNumber',
        'server_group_id': 'ServerGroupId',
        'server_id': 'ServerId',
        'status': 'Status',
        'type': 'Type',
        'updated_at': 'UpdatedAt'
    }

    def __init__(self, instance_id=None, ip=None, port=None, rule_number=None, server_group_id=None, server_id=None, status=None, type=None, updated_at=None, _configuration=None):  # noqa: E501
        """ResultForDescribeListenerHealthOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._ip = None
        self._port = None
        self._rule_number = None
        self._server_group_id = None
        self._server_id = None
        self._status = None
        self._type = None
        self._updated_at = None
        self.discriminator = None

        if instance_id is not None:
            self.instance_id = instance_id
        if ip is not None:
            self.ip = ip
        if port is not None:
            self.port = port
        if rule_number is not None:
            self.rule_number = rule_number
        if server_group_id is not None:
            self.server_group_id = server_group_id
        if server_id is not None:
            self.server_id = server_id
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type
        if updated_at is not None:
            self.updated_at = updated_at

    @property
    def instance_id(self):
        """Gets the instance_id of this ResultForDescribeListenerHealthOutput.  # noqa: E501


        :return: The instance_id of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ResultForDescribeListenerHealthOutput.


        :param instance_id: The instance_id of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def ip(self):
        """Gets the ip of this ResultForDescribeListenerHealthOutput.  # noqa: E501


        :return: The ip of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ResultForDescribeListenerHealthOutput.


        :param ip: The ip of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def port(self):
        """Gets the port of this ResultForDescribeListenerHealthOutput.  # noqa: E501


        :return: The port of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: int
        """
        return self._port

    @port.setter
    def port(self, port):
        """Sets the port of this ResultForDescribeListenerHealthOutput.


        :param port: The port of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :type: int
        """

        self._port = port

    @property
    def rule_number(self):
        """Gets the rule_number of this ResultForDescribeListenerHealthOutput.  # noqa: E501


        :return: The rule_number of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: int
        """
        return self._rule_number

    @rule_number.setter
    def rule_number(self, rule_number):
        """Sets the rule_number of this ResultForDescribeListenerHealthOutput.


        :param rule_number: The rule_number of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :type: int
        """

        self._rule_number = rule_number

    @property
    def server_group_id(self):
        """Gets the server_group_id of this ResultForDescribeListenerHealthOutput.  # noqa: E501


        :return: The server_group_id of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_group_id

    @server_group_id.setter
    def server_group_id(self, server_group_id):
        """Sets the server_group_id of this ResultForDescribeListenerHealthOutput.


        :param server_group_id: The server_group_id of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._server_group_id = server_group_id

    @property
    def server_id(self):
        """Gets the server_id of this ResultForDescribeListenerHealthOutput.  # noqa: E501


        :return: The server_id of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._server_id

    @server_id.setter
    def server_id(self, server_id):
        """Sets the server_id of this ResultForDescribeListenerHealthOutput.


        :param server_id: The server_id of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._server_id = server_id

    @property
    def status(self):
        """Gets the status of this ResultForDescribeListenerHealthOutput.  # noqa: E501


        :return: The status of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ResultForDescribeListenerHealthOutput.


        :param status: The status of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this ResultForDescribeListenerHealthOutput.  # noqa: E501


        :return: The type of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this ResultForDescribeListenerHealthOutput.


        :param type: The type of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def updated_at(self):
        """Gets the updated_at of this ResultForDescribeListenerHealthOutput.  # noqa: E501


        :return: The updated_at of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_at

    @updated_at.setter
    def updated_at(self, updated_at):
        """Sets the updated_at of this ResultForDescribeListenerHealthOutput.


        :param updated_at: The updated_at of this ResultForDescribeListenerHealthOutput.  # noqa: E501
        :type: str
        """

        self._updated_at = updated_at

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ResultForDescribeListenerHealthOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ResultForDescribeListenerHealthOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ResultForDescribeListenerHealthOutput):
            return True

        return self.to_dict() != other.to_dict()
