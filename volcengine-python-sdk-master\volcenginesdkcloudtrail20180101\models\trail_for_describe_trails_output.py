# coding: utf-8

"""
    cloud_trail20180101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TrailForDescribeTrailsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'created_time': 'str',
        'creator_id': 'int',
        'event_rw': 'str',
        'event_sources': 'list[str]',
        'latest_delivery_error': 'str',
        'latest_delivery_time': 'str',
        'logging_status': 'str',
        'organization_id': 'int',
        'start_logging_time': 'str',
        'stop_logging_time': 'str',
        'tls_project_name': 'str',
        'tls_project_region': 'str',
        'tls_topic_name': 'str',
        'tos_bucket_name': 'str',
        'tos_bucket_region': 'str',
        'tos_key_prefix': 'str',
        'trail_name': 'str',
        'trail_type': 'int',
        'updated_time': 'str'
    }

    attribute_map = {
        'created_time': 'CreatedTime',
        'creator_id': 'CreatorID',
        'event_rw': 'EventRW',
        'event_sources': 'EventSources',
        'latest_delivery_error': 'LatestDeliveryError',
        'latest_delivery_time': 'LatestDeliveryTime',
        'logging_status': 'LoggingStatus',
        'organization_id': 'OrganizationID',
        'start_logging_time': 'StartLoggingTime',
        'stop_logging_time': 'StopLoggingTime',
        'tls_project_name': 'TlsProjectName',
        'tls_project_region': 'TlsProjectRegion',
        'tls_topic_name': 'TlsTopicName',
        'tos_bucket_name': 'TosBucketName',
        'tos_bucket_region': 'TosBucketRegion',
        'tos_key_prefix': 'TosKeyPrefix',
        'trail_name': 'TrailName',
        'trail_type': 'TrailType',
        'updated_time': 'UpdatedTime'
    }

    def __init__(self, created_time=None, creator_id=None, event_rw=None, event_sources=None, latest_delivery_error=None, latest_delivery_time=None, logging_status=None, organization_id=None, start_logging_time=None, stop_logging_time=None, tls_project_name=None, tls_project_region=None, tls_topic_name=None, tos_bucket_name=None, tos_bucket_region=None, tos_key_prefix=None, trail_name=None, trail_type=None, updated_time=None, _configuration=None):  # noqa: E501
        """TrailForDescribeTrailsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._created_time = None
        self._creator_id = None
        self._event_rw = None
        self._event_sources = None
        self._latest_delivery_error = None
        self._latest_delivery_time = None
        self._logging_status = None
        self._organization_id = None
        self._start_logging_time = None
        self._stop_logging_time = None
        self._tls_project_name = None
        self._tls_project_region = None
        self._tls_topic_name = None
        self._tos_bucket_name = None
        self._tos_bucket_region = None
        self._tos_key_prefix = None
        self._trail_name = None
        self._trail_type = None
        self._updated_time = None
        self.discriminator = None

        if created_time is not None:
            self.created_time = created_time
        if creator_id is not None:
            self.creator_id = creator_id
        if event_rw is not None:
            self.event_rw = event_rw
        if event_sources is not None:
            self.event_sources = event_sources
        if latest_delivery_error is not None:
            self.latest_delivery_error = latest_delivery_error
        if latest_delivery_time is not None:
            self.latest_delivery_time = latest_delivery_time
        if logging_status is not None:
            self.logging_status = logging_status
        if organization_id is not None:
            self.organization_id = organization_id
        if start_logging_time is not None:
            self.start_logging_time = start_logging_time
        if stop_logging_time is not None:
            self.stop_logging_time = stop_logging_time
        if tls_project_name is not None:
            self.tls_project_name = tls_project_name
        if tls_project_region is not None:
            self.tls_project_region = tls_project_region
        if tls_topic_name is not None:
            self.tls_topic_name = tls_topic_name
        if tos_bucket_name is not None:
            self.tos_bucket_name = tos_bucket_name
        if tos_bucket_region is not None:
            self.tos_bucket_region = tos_bucket_region
        if tos_key_prefix is not None:
            self.tos_key_prefix = tos_key_prefix
        if trail_name is not None:
            self.trail_name = trail_name
        if trail_type is not None:
            self.trail_type = trail_type
        if updated_time is not None:
            self.updated_time = updated_time

    @property
    def created_time(self):
        """Gets the created_time of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The created_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._created_time

    @created_time.setter
    def created_time(self, created_time):
        """Sets the created_time of this TrailForDescribeTrailsOutput.


        :param created_time: The created_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._created_time = created_time

    @property
    def creator_id(self):
        """Gets the creator_id of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The creator_id of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: int
        """
        return self._creator_id

    @creator_id.setter
    def creator_id(self, creator_id):
        """Sets the creator_id of this TrailForDescribeTrailsOutput.


        :param creator_id: The creator_id of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: int
        """

        self._creator_id = creator_id

    @property
    def event_rw(self):
        """Gets the event_rw of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The event_rw of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._event_rw

    @event_rw.setter
    def event_rw(self, event_rw):
        """Sets the event_rw of this TrailForDescribeTrailsOutput.


        :param event_rw: The event_rw of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._event_rw = event_rw

    @property
    def event_sources(self):
        """Gets the event_sources of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The event_sources of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._event_sources

    @event_sources.setter
    def event_sources(self, event_sources):
        """Sets the event_sources of this TrailForDescribeTrailsOutput.


        :param event_sources: The event_sources of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: list[str]
        """

        self._event_sources = event_sources

    @property
    def latest_delivery_error(self):
        """Gets the latest_delivery_error of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The latest_delivery_error of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._latest_delivery_error

    @latest_delivery_error.setter
    def latest_delivery_error(self, latest_delivery_error):
        """Sets the latest_delivery_error of this TrailForDescribeTrailsOutput.


        :param latest_delivery_error: The latest_delivery_error of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._latest_delivery_error = latest_delivery_error

    @property
    def latest_delivery_time(self):
        """Gets the latest_delivery_time of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The latest_delivery_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._latest_delivery_time

    @latest_delivery_time.setter
    def latest_delivery_time(self, latest_delivery_time):
        """Sets the latest_delivery_time of this TrailForDescribeTrailsOutput.


        :param latest_delivery_time: The latest_delivery_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._latest_delivery_time = latest_delivery_time

    @property
    def logging_status(self):
        """Gets the logging_status of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The logging_status of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._logging_status

    @logging_status.setter
    def logging_status(self, logging_status):
        """Sets the logging_status of this TrailForDescribeTrailsOutput.


        :param logging_status: The logging_status of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._logging_status = logging_status

    @property
    def organization_id(self):
        """Gets the organization_id of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The organization_id of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: int
        """
        return self._organization_id

    @organization_id.setter
    def organization_id(self, organization_id):
        """Sets the organization_id of this TrailForDescribeTrailsOutput.


        :param organization_id: The organization_id of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: int
        """

        self._organization_id = organization_id

    @property
    def start_logging_time(self):
        """Gets the start_logging_time of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The start_logging_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_logging_time

    @start_logging_time.setter
    def start_logging_time(self, start_logging_time):
        """Sets the start_logging_time of this TrailForDescribeTrailsOutput.


        :param start_logging_time: The start_logging_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._start_logging_time = start_logging_time

    @property
    def stop_logging_time(self):
        """Gets the stop_logging_time of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The stop_logging_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._stop_logging_time

    @stop_logging_time.setter
    def stop_logging_time(self, stop_logging_time):
        """Sets the stop_logging_time of this TrailForDescribeTrailsOutput.


        :param stop_logging_time: The stop_logging_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._stop_logging_time = stop_logging_time

    @property
    def tls_project_name(self):
        """Gets the tls_project_name of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The tls_project_name of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._tls_project_name

    @tls_project_name.setter
    def tls_project_name(self, tls_project_name):
        """Sets the tls_project_name of this TrailForDescribeTrailsOutput.


        :param tls_project_name: The tls_project_name of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._tls_project_name = tls_project_name

    @property
    def tls_project_region(self):
        """Gets the tls_project_region of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The tls_project_region of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._tls_project_region

    @tls_project_region.setter
    def tls_project_region(self, tls_project_region):
        """Sets the tls_project_region of this TrailForDescribeTrailsOutput.


        :param tls_project_region: The tls_project_region of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._tls_project_region = tls_project_region

    @property
    def tls_topic_name(self):
        """Gets the tls_topic_name of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The tls_topic_name of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._tls_topic_name

    @tls_topic_name.setter
    def tls_topic_name(self, tls_topic_name):
        """Sets the tls_topic_name of this TrailForDescribeTrailsOutput.


        :param tls_topic_name: The tls_topic_name of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._tls_topic_name = tls_topic_name

    @property
    def tos_bucket_name(self):
        """Gets the tos_bucket_name of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The tos_bucket_name of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._tos_bucket_name

    @tos_bucket_name.setter
    def tos_bucket_name(self, tos_bucket_name):
        """Sets the tos_bucket_name of this TrailForDescribeTrailsOutput.


        :param tos_bucket_name: The tos_bucket_name of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._tos_bucket_name = tos_bucket_name

    @property
    def tos_bucket_region(self):
        """Gets the tos_bucket_region of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The tos_bucket_region of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._tos_bucket_region

    @tos_bucket_region.setter
    def tos_bucket_region(self, tos_bucket_region):
        """Sets the tos_bucket_region of this TrailForDescribeTrailsOutput.


        :param tos_bucket_region: The tos_bucket_region of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._tos_bucket_region = tos_bucket_region

    @property
    def tos_key_prefix(self):
        """Gets the tos_key_prefix of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The tos_key_prefix of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._tos_key_prefix

    @tos_key_prefix.setter
    def tos_key_prefix(self, tos_key_prefix):
        """Sets the tos_key_prefix of this TrailForDescribeTrailsOutput.


        :param tos_key_prefix: The tos_key_prefix of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._tos_key_prefix = tos_key_prefix

    @property
    def trail_name(self):
        """Gets the trail_name of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The trail_name of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._trail_name

    @trail_name.setter
    def trail_name(self, trail_name):
        """Sets the trail_name of this TrailForDescribeTrailsOutput.


        :param trail_name: The trail_name of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._trail_name = trail_name

    @property
    def trail_type(self):
        """Gets the trail_type of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The trail_type of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: int
        """
        return self._trail_type

    @trail_type.setter
    def trail_type(self, trail_type):
        """Sets the trail_type of this TrailForDescribeTrailsOutput.


        :param trail_type: The trail_type of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: int
        """

        self._trail_type = trail_type

    @property
    def updated_time(self):
        """Gets the updated_time of this TrailForDescribeTrailsOutput.  # noqa: E501


        :return: The updated_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :rtype: str
        """
        return self._updated_time

    @updated_time.setter
    def updated_time(self, updated_time):
        """Sets the updated_time of this TrailForDescribeTrailsOutput.


        :param updated_time: The updated_time of this TrailForDescribeTrailsOutput.  # noqa: E501
        :type: str
        """

        self._updated_time = updated_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TrailForDescribeTrailsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TrailForDescribeTrailsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TrailForDescribeTrailsOutput):
            return True

        return self.to_dict() != other.to_dict()
