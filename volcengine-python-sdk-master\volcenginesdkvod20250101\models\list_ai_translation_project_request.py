# coding: utf-8

"""
    vod20250101

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListAITranslationProjectRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'page_number': 'int',
        'page_size': 'str',
        'project_id_or_title_filter': 'str',
        'space_name': 'str',
        'status_filter': 'str'
    }

    attribute_map = {
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'project_id_or_title_filter': 'ProjectIdOrTitleFilter',
        'space_name': 'SpaceName',
        'status_filter': 'StatusFilter'
    }

    def __init__(self, page_number=None, page_size=None, project_id_or_title_filter=None, space_name=None, status_filter=None, _configuration=None):  # noqa: E501
        """ListAITranslationProjectRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._page_number = None
        self._page_size = None
        self._project_id_or_title_filter = None
        self._space_name = None
        self._status_filter = None
        self.discriminator = None

        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if project_id_or_title_filter is not None:
            self.project_id_or_title_filter = project_id_or_title_filter
        self.space_name = space_name
        if status_filter is not None:
            self.status_filter = status_filter

    @property
    def page_number(self):
        """Gets the page_number of this ListAITranslationProjectRequest.  # noqa: E501


        :return: The page_number of this ListAITranslationProjectRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListAITranslationProjectRequest.


        :param page_number: The page_number of this ListAITranslationProjectRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListAITranslationProjectRequest.  # noqa: E501


        :return: The page_size of this ListAITranslationProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListAITranslationProjectRequest.


        :param page_size: The page_size of this ListAITranslationProjectRequest.  # noqa: E501
        :type: str
        """

        self._page_size = page_size

    @property
    def project_id_or_title_filter(self):
        """Gets the project_id_or_title_filter of this ListAITranslationProjectRequest.  # noqa: E501


        :return: The project_id_or_title_filter of this ListAITranslationProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_id_or_title_filter

    @project_id_or_title_filter.setter
    def project_id_or_title_filter(self, project_id_or_title_filter):
        """Sets the project_id_or_title_filter of this ListAITranslationProjectRequest.


        :param project_id_or_title_filter: The project_id_or_title_filter of this ListAITranslationProjectRequest.  # noqa: E501
        :type: str
        """

        self._project_id_or_title_filter = project_id_or_title_filter

    @property
    def space_name(self):
        """Gets the space_name of this ListAITranslationProjectRequest.  # noqa: E501


        :return: The space_name of this ListAITranslationProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._space_name

    @space_name.setter
    def space_name(self, space_name):
        """Sets the space_name of this ListAITranslationProjectRequest.


        :param space_name: The space_name of this ListAITranslationProjectRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and space_name is None:
            raise ValueError("Invalid value for `space_name`, must not be `None`")  # noqa: E501

        self._space_name = space_name

    @property
    def status_filter(self):
        """Gets the status_filter of this ListAITranslationProjectRequest.  # noqa: E501


        :return: The status_filter of this ListAITranslationProjectRequest.  # noqa: E501
        :rtype: str
        """
        return self._status_filter

    @status_filter.setter
    def status_filter(self, status_filter):
        """Sets the status_filter of this ListAITranslationProjectRequest.


        :param status_filter: The status_filter of this ListAITranslationProjectRequest.  # noqa: E501
        :type: str
        """

        self._status_filter = status_filter

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListAITranslationProjectRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListAITranslationProjectRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListAITranslationProjectRequest):
            return True

        return self.to_dict() != other.to_dict()
