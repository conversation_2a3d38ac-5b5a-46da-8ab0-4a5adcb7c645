# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetModelCustomizationJobMetricsRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'model_customization_job_id': 'str'
    }

    attribute_map = {
        'model_customization_job_id': 'ModelCustomizationJobId'
    }

    def __init__(self, model_customization_job_id=None, _configuration=None):  # noqa: E501
        """GetModelCustomizationJobMetricsRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._model_customization_job_id = None
        self.discriminator = None

        self.model_customization_job_id = model_customization_job_id

    @property
    def model_customization_job_id(self):
        """Gets the model_customization_job_id of this GetModelCustomizationJobMetricsRequest.  # noqa: E501


        :return: The model_customization_job_id of this GetModelCustomizationJobMetricsRequest.  # noqa: E501
        :rtype: str
        """
        return self._model_customization_job_id

    @model_customization_job_id.setter
    def model_customization_job_id(self, model_customization_job_id):
        """Sets the model_customization_job_id of this GetModelCustomizationJobMetricsRequest.


        :param model_customization_job_id: The model_customization_job_id of this GetModelCustomizationJobMetricsRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and model_customization_job_id is None:
            raise ValueError("Invalid value for `model_customization_job_id`, must not be `None`")  # noqa: E501

        self._model_customization_job_id = model_customization_job_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetModelCustomizationJobMetricsRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetModelCustomizationJobMetricsRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetModelCustomizationJobMetricsRequest):
            return True

        return self.to_dict() != other.to_dict()
