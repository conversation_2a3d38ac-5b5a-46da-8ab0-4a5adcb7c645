# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ShardCapacitySpecForDescribeDBInstanceSpecsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'default_bandwidth_per_shard': 'int',
        'max_additional_bandwidth_per_shard': 'int',
        'shard_capacity': 'int'
    }

    attribute_map = {
        'default_bandwidth_per_shard': 'DefaultBandwidthPerShard',
        'max_additional_bandwidth_per_shard': 'MaxAdditionalBandwidthPerShard',
        'shard_capacity': 'ShardCapacity'
    }

    def __init__(self, default_bandwidth_per_shard=None, max_additional_bandwidth_per_shard=None, shard_capacity=None, _configuration=None):  # noqa: E501
        """ShardCapacitySpecForDescribeDBInstanceSpecsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._default_bandwidth_per_shard = None
        self._max_additional_bandwidth_per_shard = None
        self._shard_capacity = None
        self.discriminator = None

        if default_bandwidth_per_shard is not None:
            self.default_bandwidth_per_shard = default_bandwidth_per_shard
        if max_additional_bandwidth_per_shard is not None:
            self.max_additional_bandwidth_per_shard = max_additional_bandwidth_per_shard
        if shard_capacity is not None:
            self.shard_capacity = shard_capacity

    @property
    def default_bandwidth_per_shard(self):
        """Gets the default_bandwidth_per_shard of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The default_bandwidth_per_shard of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._default_bandwidth_per_shard

    @default_bandwidth_per_shard.setter
    def default_bandwidth_per_shard(self, default_bandwidth_per_shard):
        """Sets the default_bandwidth_per_shard of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.


        :param default_bandwidth_per_shard: The default_bandwidth_per_shard of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._default_bandwidth_per_shard = default_bandwidth_per_shard

    @property
    def max_additional_bandwidth_per_shard(self):
        """Gets the max_additional_bandwidth_per_shard of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The max_additional_bandwidth_per_shard of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._max_additional_bandwidth_per_shard

    @max_additional_bandwidth_per_shard.setter
    def max_additional_bandwidth_per_shard(self, max_additional_bandwidth_per_shard):
        """Sets the max_additional_bandwidth_per_shard of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.


        :param max_additional_bandwidth_per_shard: The max_additional_bandwidth_per_shard of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._max_additional_bandwidth_per_shard = max_additional_bandwidth_per_shard

    @property
    def shard_capacity(self):
        """Gets the shard_capacity of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.  # noqa: E501


        :return: The shard_capacity of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :rtype: int
        """
        return self._shard_capacity

    @shard_capacity.setter
    def shard_capacity(self, shard_capacity):
        """Sets the shard_capacity of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.


        :param shard_capacity: The shard_capacity of this ShardCapacitySpecForDescribeDBInstanceSpecsOutput.  # noqa: E501
        :type: int
        """

        self._shard_capacity = shard_capacity

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ShardCapacitySpecForDescribeDBInstanceSpecsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ShardCapacitySpecForDescribeDBInstanceSpecsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ShardCapacitySpecForDescribeDBInstanceSpecsOutput):
            return True

        return self.to_dict() != other.to_dict()
