# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PlusAlarmInfo356ForGetHidsAlarmInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'fd_name': 'str',
        'flags': 'str'
    }

    attribute_map = {
        'fd_name': 'FdName',
        'flags': 'Flags'
    }

    def __init__(self, fd_name=None, flags=None, _configuration=None):  # noqa: E501
        """PlusAlarmInfo356ForGetHidsAlarmInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._fd_name = None
        self._flags = None
        self.discriminator = None

        if fd_name is not None:
            self.fd_name = fd_name
        if flags is not None:
            self.flags = flags

    @property
    def fd_name(self):
        """Gets the fd_name of this PlusAlarmInfo356ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The fd_name of this PlusAlarmInfo356ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._fd_name

    @fd_name.setter
    def fd_name(self, fd_name):
        """Sets the fd_name of this PlusAlarmInfo356ForGetHidsAlarmInfoOutput.


        :param fd_name: The fd_name of this PlusAlarmInfo356ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._fd_name = fd_name

    @property
    def flags(self):
        """Gets the flags of this PlusAlarmInfo356ForGetHidsAlarmInfoOutput.  # noqa: E501


        :return: The flags of this PlusAlarmInfo356ForGetHidsAlarmInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._flags

    @flags.setter
    def flags(self, flags):
        """Sets the flags of this PlusAlarmInfo356ForGetHidsAlarmInfoOutput.


        :param flags: The flags of this PlusAlarmInfo356ForGetHidsAlarmInfoOutput.  # noqa: E501
        :type: str
        """

        self._flags = flags

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PlusAlarmInfo356ForGetHidsAlarmInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PlusAlarmInfo356ForGetHidsAlarmInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PlusAlarmInfo356ForGetHidsAlarmInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
