# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateClusterRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'application_extras': 'list[ApplicationExtraForCreateClusterInput]',
        'application_names': 'list[str]',
        'bootstrap_scripts': 'list[BootstrapScriptForCreateClusterInput]',
        'charge_pre_config': 'ChargePreConfigForCreateClusterInput',
        'charge_type': 'str',
        'cluster_name': 'str',
        'cluster_type': 'str',
        'deploy_mode': 'str',
        'history_server_mode': 'str',
        'node_attribute': 'NodeAttributeForCreateClusterInput',
        'node_group_attributes': 'list[NodeGroupAttributeForCreateClusterInput]',
        'project_name': 'str',
        'release_version': 'str',
        'security_group_id': 'str',
        'security_mode': 'str',
        'tags': 'list[str]',
        'vpc_id': 'str'
    }

    attribute_map = {
        'application_extras': 'ApplicationExtras',
        'application_names': 'ApplicationNames',
        'bootstrap_scripts': 'BootstrapScripts',
        'charge_pre_config': 'ChargePreConfig',
        'charge_type': 'ChargeType',
        'cluster_name': 'ClusterName',
        'cluster_type': 'ClusterType',
        'deploy_mode': 'DeployMode',
        'history_server_mode': 'HistoryServerMode',
        'node_attribute': 'NodeAttribute',
        'node_group_attributes': 'NodeGroupAttributes',
        'project_name': 'ProjectName',
        'release_version': 'ReleaseVersion',
        'security_group_id': 'SecurityGroupId',
        'security_mode': 'SecurityMode',
        'tags': 'Tags',
        'vpc_id': 'VpcId'
    }

    def __init__(self, application_extras=None, application_names=None, bootstrap_scripts=None, charge_pre_config=None, charge_type=None, cluster_name=None, cluster_type=None, deploy_mode=None, history_server_mode=None, node_attribute=None, node_group_attributes=None, project_name=None, release_version=None, security_group_id=None, security_mode=None, tags=None, vpc_id=None, _configuration=None):  # noqa: E501
        """CreateClusterRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._application_extras = None
        self._application_names = None
        self._bootstrap_scripts = None
        self._charge_pre_config = None
        self._charge_type = None
        self._cluster_name = None
        self._cluster_type = None
        self._deploy_mode = None
        self._history_server_mode = None
        self._node_attribute = None
        self._node_group_attributes = None
        self._project_name = None
        self._release_version = None
        self._security_group_id = None
        self._security_mode = None
        self._tags = None
        self._vpc_id = None
        self.discriminator = None

        if application_extras is not None:
            self.application_extras = application_extras
        if application_names is not None:
            self.application_names = application_names
        if bootstrap_scripts is not None:
            self.bootstrap_scripts = bootstrap_scripts
        if charge_pre_config is not None:
            self.charge_pre_config = charge_pre_config
        self.charge_type = charge_type
        self.cluster_name = cluster_name
        self.cluster_type = cluster_type
        if deploy_mode is not None:
            self.deploy_mode = deploy_mode
        if history_server_mode is not None:
            self.history_server_mode = history_server_mode
        if node_attribute is not None:
            self.node_attribute = node_attribute
        if node_group_attributes is not None:
            self.node_group_attributes = node_group_attributes
        if project_name is not None:
            self.project_name = project_name
        self.release_version = release_version
        self.security_group_id = security_group_id
        if security_mode is not None:
            self.security_mode = security_mode
        if tags is not None:
            self.tags = tags
        self.vpc_id = vpc_id

    @property
    def application_extras(self):
        """Gets the application_extras of this CreateClusterRequest.  # noqa: E501


        :return: The application_extras of this CreateClusterRequest.  # noqa: E501
        :rtype: list[ApplicationExtraForCreateClusterInput]
        """
        return self._application_extras

    @application_extras.setter
    def application_extras(self, application_extras):
        """Sets the application_extras of this CreateClusterRequest.


        :param application_extras: The application_extras of this CreateClusterRequest.  # noqa: E501
        :type: list[ApplicationExtraForCreateClusterInput]
        """

        self._application_extras = application_extras

    @property
    def application_names(self):
        """Gets the application_names of this CreateClusterRequest.  # noqa: E501


        :return: The application_names of this CreateClusterRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._application_names

    @application_names.setter
    def application_names(self, application_names):
        """Sets the application_names of this CreateClusterRequest.


        :param application_names: The application_names of this CreateClusterRequest.  # noqa: E501
        :type: list[str]
        """

        self._application_names = application_names

    @property
    def bootstrap_scripts(self):
        """Gets the bootstrap_scripts of this CreateClusterRequest.  # noqa: E501


        :return: The bootstrap_scripts of this CreateClusterRequest.  # noqa: E501
        :rtype: list[BootstrapScriptForCreateClusterInput]
        """
        return self._bootstrap_scripts

    @bootstrap_scripts.setter
    def bootstrap_scripts(self, bootstrap_scripts):
        """Sets the bootstrap_scripts of this CreateClusterRequest.


        :param bootstrap_scripts: The bootstrap_scripts of this CreateClusterRequest.  # noqa: E501
        :type: list[BootstrapScriptForCreateClusterInput]
        """

        self._bootstrap_scripts = bootstrap_scripts

    @property
    def charge_pre_config(self):
        """Gets the charge_pre_config of this CreateClusterRequest.  # noqa: E501


        :return: The charge_pre_config of this CreateClusterRequest.  # noqa: E501
        :rtype: ChargePreConfigForCreateClusterInput
        """
        return self._charge_pre_config

    @charge_pre_config.setter
    def charge_pre_config(self, charge_pre_config):
        """Sets the charge_pre_config of this CreateClusterRequest.


        :param charge_pre_config: The charge_pre_config of this CreateClusterRequest.  # noqa: E501
        :type: ChargePreConfigForCreateClusterInput
        """

        self._charge_pre_config = charge_pre_config

    @property
    def charge_type(self):
        """Gets the charge_type of this CreateClusterRequest.  # noqa: E501


        :return: The charge_type of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this CreateClusterRequest.


        :param charge_type: The charge_type of this CreateClusterRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and charge_type is None:
            raise ValueError("Invalid value for `charge_type`, must not be `None`")  # noqa: E501

        self._charge_type = charge_type

    @property
    def cluster_name(self):
        """Gets the cluster_name of this CreateClusterRequest.  # noqa: E501


        :return: The cluster_name of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this CreateClusterRequest.


        :param cluster_name: The cluster_name of this CreateClusterRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_name is None:
            raise ValueError("Invalid value for `cluster_name`, must not be `None`")  # noqa: E501

        self._cluster_name = cluster_name

    @property
    def cluster_type(self):
        """Gets the cluster_type of this CreateClusterRequest.  # noqa: E501


        :return: The cluster_type of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._cluster_type

    @cluster_type.setter
    def cluster_type(self, cluster_type):
        """Sets the cluster_type of this CreateClusterRequest.


        :param cluster_type: The cluster_type of this CreateClusterRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and cluster_type is None:
            raise ValueError("Invalid value for `cluster_type`, must not be `None`")  # noqa: E501

        self._cluster_type = cluster_type

    @property
    def deploy_mode(self):
        """Gets the deploy_mode of this CreateClusterRequest.  # noqa: E501


        :return: The deploy_mode of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._deploy_mode

    @deploy_mode.setter
    def deploy_mode(self, deploy_mode):
        """Sets the deploy_mode of this CreateClusterRequest.


        :param deploy_mode: The deploy_mode of this CreateClusterRequest.  # noqa: E501
        :type: str
        """

        self._deploy_mode = deploy_mode

    @property
    def history_server_mode(self):
        """Gets the history_server_mode of this CreateClusterRequest.  # noqa: E501


        :return: The history_server_mode of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._history_server_mode

    @history_server_mode.setter
    def history_server_mode(self, history_server_mode):
        """Sets the history_server_mode of this CreateClusterRequest.


        :param history_server_mode: The history_server_mode of this CreateClusterRequest.  # noqa: E501
        :type: str
        """

        self._history_server_mode = history_server_mode

    @property
    def node_attribute(self):
        """Gets the node_attribute of this CreateClusterRequest.  # noqa: E501


        :return: The node_attribute of this CreateClusterRequest.  # noqa: E501
        :rtype: NodeAttributeForCreateClusterInput
        """
        return self._node_attribute

    @node_attribute.setter
    def node_attribute(self, node_attribute):
        """Sets the node_attribute of this CreateClusterRequest.


        :param node_attribute: The node_attribute of this CreateClusterRequest.  # noqa: E501
        :type: NodeAttributeForCreateClusterInput
        """

        self._node_attribute = node_attribute

    @property
    def node_group_attributes(self):
        """Gets the node_group_attributes of this CreateClusterRequest.  # noqa: E501


        :return: The node_group_attributes of this CreateClusterRequest.  # noqa: E501
        :rtype: list[NodeGroupAttributeForCreateClusterInput]
        """
        return self._node_group_attributes

    @node_group_attributes.setter
    def node_group_attributes(self, node_group_attributes):
        """Sets the node_group_attributes of this CreateClusterRequest.


        :param node_group_attributes: The node_group_attributes of this CreateClusterRequest.  # noqa: E501
        :type: list[NodeGroupAttributeForCreateClusterInput]
        """

        self._node_group_attributes = node_group_attributes

    @property
    def project_name(self):
        """Gets the project_name of this CreateClusterRequest.  # noqa: E501


        :return: The project_name of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._project_name

    @project_name.setter
    def project_name(self, project_name):
        """Sets the project_name of this CreateClusterRequest.


        :param project_name: The project_name of this CreateClusterRequest.  # noqa: E501
        :type: str
        """

        self._project_name = project_name

    @property
    def release_version(self):
        """Gets the release_version of this CreateClusterRequest.  # noqa: E501


        :return: The release_version of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._release_version

    @release_version.setter
    def release_version(self, release_version):
        """Sets the release_version of this CreateClusterRequest.


        :param release_version: The release_version of this CreateClusterRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and release_version is None:
            raise ValueError("Invalid value for `release_version`, must not be `None`")  # noqa: E501

        self._release_version = release_version

    @property
    def security_group_id(self):
        """Gets the security_group_id of this CreateClusterRequest.  # noqa: E501


        :return: The security_group_id of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._security_group_id

    @security_group_id.setter
    def security_group_id(self, security_group_id):
        """Sets the security_group_id of this CreateClusterRequest.


        :param security_group_id: The security_group_id of this CreateClusterRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and security_group_id is None:
            raise ValueError("Invalid value for `security_group_id`, must not be `None`")  # noqa: E501

        self._security_group_id = security_group_id

    @property
    def security_mode(self):
        """Gets the security_mode of this CreateClusterRequest.  # noqa: E501


        :return: The security_mode of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._security_mode

    @security_mode.setter
    def security_mode(self, security_mode):
        """Sets the security_mode of this CreateClusterRequest.


        :param security_mode: The security_mode of this CreateClusterRequest.  # noqa: E501
        :type: str
        """

        self._security_mode = security_mode

    @property
    def tags(self):
        """Gets the tags of this CreateClusterRequest.  # noqa: E501


        :return: The tags of this CreateClusterRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this CreateClusterRequest.


        :param tags: The tags of this CreateClusterRequest.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def vpc_id(self):
        """Gets the vpc_id of this CreateClusterRequest.  # noqa: E501


        :return: The vpc_id of this CreateClusterRequest.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this CreateClusterRequest.


        :param vpc_id: The vpc_id of this CreateClusterRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and vpc_id is None:
            raise ValueError("Invalid value for `vpc_id`, must not be `None`")  # noqa: E501

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateClusterRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateClusterRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateClusterRequest):
            return True

        return self.to_dict() != other.to_dict()
