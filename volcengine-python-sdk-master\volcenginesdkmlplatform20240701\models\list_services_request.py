# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListServicesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'create_time_after': 'str',
        'create_time_before': 'str',
        'name_contains': 'str',
        'page_number': 'int',
        'page_size': 'int',
        'sort_by': 'str',
        'sort_order': 'str',
        'state': 'str',
        'update_time_after': 'str',
        'update_time_before': 'str'
    }

    attribute_map = {
        'create_time_after': 'CreateTimeAfter',
        'create_time_before': 'CreateTimeBefore',
        'name_contains': 'NameContains',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'state': 'State',
        'update_time_after': 'UpdateTimeAfter',
        'update_time_before': 'UpdateTimeBefore'
    }

    def __init__(self, create_time_after=None, create_time_before=None, name_contains=None, page_number=None, page_size=None, sort_by=None, sort_order=None, state=None, update_time_after=None, update_time_before=None, _configuration=None):  # noqa: E501
        """ListServicesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._create_time_after = None
        self._create_time_before = None
        self._name_contains = None
        self._page_number = None
        self._page_size = None
        self._sort_by = None
        self._sort_order = None
        self._state = None
        self._update_time_after = None
        self._update_time_before = None
        self.discriminator = None

        if create_time_after is not None:
            self.create_time_after = create_time_after
        if create_time_before is not None:
            self.create_time_before = create_time_before
        if name_contains is not None:
            self.name_contains = name_contains
        if page_number is not None:
            self.page_number = page_number
        if page_size is not None:
            self.page_size = page_size
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if state is not None:
            self.state = state
        if update_time_after is not None:
            self.update_time_after = update_time_after
        if update_time_before is not None:
            self.update_time_before = update_time_before

    @property
    def create_time_after(self):
        """Gets the create_time_after of this ListServicesRequest.  # noqa: E501


        :return: The create_time_after of this ListServicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_after

    @create_time_after.setter
    def create_time_after(self, create_time_after):
        """Sets the create_time_after of this ListServicesRequest.


        :param create_time_after: The create_time_after of this ListServicesRequest.  # noqa: E501
        :type: str
        """

        self._create_time_after = create_time_after

    @property
    def create_time_before(self):
        """Gets the create_time_before of this ListServicesRequest.  # noqa: E501


        :return: The create_time_before of this ListServicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._create_time_before

    @create_time_before.setter
    def create_time_before(self, create_time_before):
        """Sets the create_time_before of this ListServicesRequest.


        :param create_time_before: The create_time_before of this ListServicesRequest.  # noqa: E501
        :type: str
        """

        self._create_time_before = create_time_before

    @property
    def name_contains(self):
        """Gets the name_contains of this ListServicesRequest.  # noqa: E501


        :return: The name_contains of this ListServicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._name_contains

    @name_contains.setter
    def name_contains(self, name_contains):
        """Sets the name_contains of this ListServicesRequest.


        :param name_contains: The name_contains of this ListServicesRequest.  # noqa: E501
        :type: str
        """

        self._name_contains = name_contains

    @property
    def page_number(self):
        """Gets the page_number of this ListServicesRequest.  # noqa: E501


        :return: The page_number of this ListServicesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListServicesRequest.


        :param page_number: The page_number of this ListServicesRequest.  # noqa: E501
        :type: int
        """

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListServicesRequest.  # noqa: E501


        :return: The page_size of this ListServicesRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListServicesRequest.


        :param page_size: The page_size of this ListServicesRequest.  # noqa: E501
        :type: int
        """
        if (self._configuration.client_side_validation and
                page_size is not None and page_size > 100):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value less than or equal to `100`")  # noqa: E501
        if (self._configuration.client_side_validation and
                page_size is not None and page_size < 10):  # noqa: E501
            raise ValueError("Invalid value for `page_size`, must be a value greater than or equal to `10`")  # noqa: E501

        self._page_size = page_size

    @property
    def sort_by(self):
        """Gets the sort_by of this ListServicesRequest.  # noqa: E501


        :return: The sort_by of this ListServicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListServicesRequest.


        :param sort_by: The sort_by of this ListServicesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["CreateTime"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_by not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_by` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_by, allowed_values)
            )

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListServicesRequest.  # noqa: E501


        :return: The sort_order of this ListServicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListServicesRequest.


        :param sort_order: The sort_order of this ListServicesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Ascend", "Descend"]  # noqa: E501
        if (self._configuration.client_side_validation and
                sort_order not in allowed_values):
            raise ValueError(
                "Invalid value for `sort_order` ({0}), must be one of {1}"  # noqa: E501
                .format(sort_order, allowed_values)
            )

        self._sort_order = sort_order

    @property
    def state(self):
        """Gets the state of this ListServicesRequest.  # noqa: E501


        :return: The state of this ListServicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._state

    @state.setter
    def state(self, state):
        """Sets the state of this ListServicesRequest.


        :param state: The state of this ListServicesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["None", "Creating", "Inservice", "Stopping", "Stopped", "Deleting", "Abnormal", "Staging", "Deleted"]  # noqa: E501
        if (self._configuration.client_side_validation and
                state not in allowed_values):
            raise ValueError(
                "Invalid value for `state` ({0}), must be one of {1}"  # noqa: E501
                .format(state, allowed_values)
            )

        self._state = state

    @property
    def update_time_after(self):
        """Gets the update_time_after of this ListServicesRequest.  # noqa: E501


        :return: The update_time_after of this ListServicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._update_time_after

    @update_time_after.setter
    def update_time_after(self, update_time_after):
        """Sets the update_time_after of this ListServicesRequest.


        :param update_time_after: The update_time_after of this ListServicesRequest.  # noqa: E501
        :type: str
        """

        self._update_time_after = update_time_after

    @property
    def update_time_before(self):
        """Gets the update_time_before of this ListServicesRequest.  # noqa: E501


        :return: The update_time_before of this ListServicesRequest.  # noqa: E501
        :rtype: str
        """
        return self._update_time_before

    @update_time_before.setter
    def update_time_before(self, update_time_before):
        """Sets the update_time_before of this ListServicesRequest.


        :param update_time_before: The update_time_before of this ListServicesRequest.  # noqa: E501
        :type: str
        """

        self._update_time_before = update_time_before

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListServicesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListServicesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListServicesRequest):
            return True

        return self.to_dict() != other.to_dict()
