# coding: utf-8

"""
    quota

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateAlarmRuleRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_type': 'str',
        'description': 'str',
        'dimensions': 'list[DimensionForCreateAlarmRuleInput]',
        'metric_unit': 'str',
        'provider_code': 'str',
        'quota_code': 'str',
        'rule_name': 'str',
        'silence_time': 'int',
        'threshold': 'str'
    }

    attribute_map = {
        'alarm_type': 'AlarmType',
        'description': 'Description',
        'dimensions': 'Dimensions',
        'metric_unit': 'MetricUnit',
        'provider_code': 'ProviderCode',
        'quota_code': 'QuotaCode',
        'rule_name': 'RuleName',
        'silence_time': 'SilenceTime',
        'threshold': 'Threshold'
    }

    def __init__(self, alarm_type=None, description=None, dimensions=None, metric_unit=None, provider_code=None, quota_code=None, rule_name=None, silence_time=None, threshold=None, _configuration=None):  # noqa: E501
        """CreateAlarmRuleRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_type = None
        self._description = None
        self._dimensions = None
        self._metric_unit = None
        self._provider_code = None
        self._quota_code = None
        self._rule_name = None
        self._silence_time = None
        self._threshold = None
        self.discriminator = None

        self.alarm_type = alarm_type
        if description is not None:
            self.description = description
        if dimensions is not None:
            self.dimensions = dimensions
        self.metric_unit = metric_unit
        self.provider_code = provider_code
        self.quota_code = quota_code
        self.rule_name = rule_name
        if silence_time is not None:
            self.silence_time = silence_time
        self.threshold = threshold

    @property
    def alarm_type(self):
        """Gets the alarm_type of this CreateAlarmRuleRequest.  # noqa: E501


        :return: The alarm_type of this CreateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._alarm_type

    @alarm_type.setter
    def alarm_type(self, alarm_type):
        """Sets the alarm_type of this CreateAlarmRuleRequest.


        :param alarm_type: The alarm_type of this CreateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and alarm_type is None:
            raise ValueError("Invalid value for `alarm_type`, must not be `None`")  # noqa: E501

        self._alarm_type = alarm_type

    @property
    def description(self):
        """Gets the description of this CreateAlarmRuleRequest.  # noqa: E501


        :return: The description of this CreateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateAlarmRuleRequest.


        :param description: The description of this CreateAlarmRuleRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def dimensions(self):
        """Gets the dimensions of this CreateAlarmRuleRequest.  # noqa: E501


        :return: The dimensions of this CreateAlarmRuleRequest.  # noqa: E501
        :rtype: list[DimensionForCreateAlarmRuleInput]
        """
        return self._dimensions

    @dimensions.setter
    def dimensions(self, dimensions):
        """Sets the dimensions of this CreateAlarmRuleRequest.


        :param dimensions: The dimensions of this CreateAlarmRuleRequest.  # noqa: E501
        :type: list[DimensionForCreateAlarmRuleInput]
        """

        self._dimensions = dimensions

    @property
    def metric_unit(self):
        """Gets the metric_unit of this CreateAlarmRuleRequest.  # noqa: E501


        :return: The metric_unit of this CreateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._metric_unit

    @metric_unit.setter
    def metric_unit(self, metric_unit):
        """Sets the metric_unit of this CreateAlarmRuleRequest.


        :param metric_unit: The metric_unit of this CreateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and metric_unit is None:
            raise ValueError("Invalid value for `metric_unit`, must not be `None`")  # noqa: E501

        self._metric_unit = metric_unit

    @property
    def provider_code(self):
        """Gets the provider_code of this CreateAlarmRuleRequest.  # noqa: E501


        :return: The provider_code of this CreateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._provider_code

    @provider_code.setter
    def provider_code(self, provider_code):
        """Sets the provider_code of this CreateAlarmRuleRequest.


        :param provider_code: The provider_code of this CreateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and provider_code is None:
            raise ValueError("Invalid value for `provider_code`, must not be `None`")  # noqa: E501

        self._provider_code = provider_code

    @property
    def quota_code(self):
        """Gets the quota_code of this CreateAlarmRuleRequest.  # noqa: E501


        :return: The quota_code of this CreateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._quota_code

    @quota_code.setter
    def quota_code(self, quota_code):
        """Sets the quota_code of this CreateAlarmRuleRequest.


        :param quota_code: The quota_code of this CreateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and quota_code is None:
            raise ValueError("Invalid value for `quota_code`, must not be `None`")  # noqa: E501

        self._quota_code = quota_code

    @property
    def rule_name(self):
        """Gets the rule_name of this CreateAlarmRuleRequest.  # noqa: E501


        :return: The rule_name of this CreateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this CreateAlarmRuleRequest.


        :param rule_name: The rule_name of this CreateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and rule_name is None:
            raise ValueError("Invalid value for `rule_name`, must not be `None`")  # noqa: E501

        self._rule_name = rule_name

    @property
    def silence_time(self):
        """Gets the silence_time of this CreateAlarmRuleRequest.  # noqa: E501


        :return: The silence_time of this CreateAlarmRuleRequest.  # noqa: E501
        :rtype: int
        """
        return self._silence_time

    @silence_time.setter
    def silence_time(self, silence_time):
        """Sets the silence_time of this CreateAlarmRuleRequest.


        :param silence_time: The silence_time of this CreateAlarmRuleRequest.  # noqa: E501
        :type: int
        """

        self._silence_time = silence_time

    @property
    def threshold(self):
        """Gets the threshold of this CreateAlarmRuleRequest.  # noqa: E501


        :return: The threshold of this CreateAlarmRuleRequest.  # noqa: E501
        :rtype: str
        """
        return self._threshold

    @threshold.setter
    def threshold(self, threshold):
        """Sets the threshold of this CreateAlarmRuleRequest.


        :param threshold: The threshold of this CreateAlarmRuleRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and threshold is None:
            raise ValueError("Invalid value for `threshold`, must not be `None`")  # noqa: E501

        self._threshold = threshold

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateAlarmRuleRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateAlarmRuleRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateAlarmRuleRequest):
            return True

        return self.to_dict() != other.to_dict()
