# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InstanceShardForDescribeDBInstanceShardsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'node_number': 'int',
        'server_nodes': 'list[ServerNodeForDescribeDBInstanceShardsOutput]',
        'shard_id': 'str'
    }

    attribute_map = {
        'node_number': 'NodeNumber',
        'server_nodes': 'ServerNodes',
        'shard_id': 'ShardId'
    }

    def __init__(self, node_number=None, server_nodes=None, shard_id=None, _configuration=None):  # noqa: E501
        """InstanceShardForDescribeDBInstanceShardsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._node_number = None
        self._server_nodes = None
        self._shard_id = None
        self.discriminator = None

        if node_number is not None:
            self.node_number = node_number
        if server_nodes is not None:
            self.server_nodes = server_nodes
        if shard_id is not None:
            self.shard_id = shard_id

    @property
    def node_number(self):
        """Gets the node_number of this InstanceShardForDescribeDBInstanceShardsOutput.  # noqa: E501


        :return: The node_number of this InstanceShardForDescribeDBInstanceShardsOutput.  # noqa: E501
        :rtype: int
        """
        return self._node_number

    @node_number.setter
    def node_number(self, node_number):
        """Sets the node_number of this InstanceShardForDescribeDBInstanceShardsOutput.


        :param node_number: The node_number of this InstanceShardForDescribeDBInstanceShardsOutput.  # noqa: E501
        :type: int
        """

        self._node_number = node_number

    @property
    def server_nodes(self):
        """Gets the server_nodes of this InstanceShardForDescribeDBInstanceShardsOutput.  # noqa: E501


        :return: The server_nodes of this InstanceShardForDescribeDBInstanceShardsOutput.  # noqa: E501
        :rtype: list[ServerNodeForDescribeDBInstanceShardsOutput]
        """
        return self._server_nodes

    @server_nodes.setter
    def server_nodes(self, server_nodes):
        """Sets the server_nodes of this InstanceShardForDescribeDBInstanceShardsOutput.


        :param server_nodes: The server_nodes of this InstanceShardForDescribeDBInstanceShardsOutput.  # noqa: E501
        :type: list[ServerNodeForDescribeDBInstanceShardsOutput]
        """

        self._server_nodes = server_nodes

    @property
    def shard_id(self):
        """Gets the shard_id of this InstanceShardForDescribeDBInstanceShardsOutput.  # noqa: E501


        :return: The shard_id of this InstanceShardForDescribeDBInstanceShardsOutput.  # noqa: E501
        :rtype: str
        """
        return self._shard_id

    @shard_id.setter
    def shard_id(self, shard_id):
        """Sets the shard_id of this InstanceShardForDescribeDBInstanceShardsOutput.


        :param shard_id: The shard_id of this InstanceShardForDescribeDBInstanceShardsOutput.  # noqa: E501
        :type: str
        """

        self._shard_id = shard_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InstanceShardForDescribeDBInstanceShardsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InstanceShardForDescribeDBInstanceShardsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InstanceShardForDescribeDBInstanceShardsOutput):
            return True

        return self.to_dict() != other.to_dict()
