# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class FilterForListAddonsInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cluster_ids': 'list[str]',
        'create_client_token': 'str',
        'deploy_modes': 'list[str]',
        'deploy_node_types': 'list[str]',
        'names': 'list[str]',
        'statuses': 'list[StatusForListAddonsInput]',
        'update_client_token': 'str'
    }

    attribute_map = {
        'cluster_ids': 'ClusterIds',
        'create_client_token': 'CreateClientToken',
        'deploy_modes': 'DeployModes',
        'deploy_node_types': 'DeployNodeTypes',
        'names': 'Names',
        'statuses': 'Statuses',
        'update_client_token': 'UpdateClientToken'
    }

    def __init__(self, cluster_ids=None, create_client_token=None, deploy_modes=None, deploy_node_types=None, names=None, statuses=None, update_client_token=None, _configuration=None):  # noqa: E501
        """FilterForListAddonsInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cluster_ids = None
        self._create_client_token = None
        self._deploy_modes = None
        self._deploy_node_types = None
        self._names = None
        self._statuses = None
        self._update_client_token = None
        self.discriminator = None

        if cluster_ids is not None:
            self.cluster_ids = cluster_ids
        if create_client_token is not None:
            self.create_client_token = create_client_token
        if deploy_modes is not None:
            self.deploy_modes = deploy_modes
        if deploy_node_types is not None:
            self.deploy_node_types = deploy_node_types
        if names is not None:
            self.names = names
        if statuses is not None:
            self.statuses = statuses
        if update_client_token is not None:
            self.update_client_token = update_client_token

    @property
    def cluster_ids(self):
        """Gets the cluster_ids of this FilterForListAddonsInput.  # noqa: E501


        :return: The cluster_ids of this FilterForListAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._cluster_ids

    @cluster_ids.setter
    def cluster_ids(self, cluster_ids):
        """Sets the cluster_ids of this FilterForListAddonsInput.


        :param cluster_ids: The cluster_ids of this FilterForListAddonsInput.  # noqa: E501
        :type: list[str]
        """

        self._cluster_ids = cluster_ids

    @property
    def create_client_token(self):
        """Gets the create_client_token of this FilterForListAddonsInput.  # noqa: E501


        :return: The create_client_token of this FilterForListAddonsInput.  # noqa: E501
        :rtype: str
        """
        return self._create_client_token

    @create_client_token.setter
    def create_client_token(self, create_client_token):
        """Sets the create_client_token of this FilterForListAddonsInput.


        :param create_client_token: The create_client_token of this FilterForListAddonsInput.  # noqa: E501
        :type: str
        """

        self._create_client_token = create_client_token

    @property
    def deploy_modes(self):
        """Gets the deploy_modes of this FilterForListAddonsInput.  # noqa: E501


        :return: The deploy_modes of this FilterForListAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._deploy_modes

    @deploy_modes.setter
    def deploy_modes(self, deploy_modes):
        """Sets the deploy_modes of this FilterForListAddonsInput.


        :param deploy_modes: The deploy_modes of this FilterForListAddonsInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Managed", "Unmanaged"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(deploy_modes).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `deploy_modes` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(deploy_modes) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._deploy_modes = deploy_modes

    @property
    def deploy_node_types(self):
        """Gets the deploy_node_types of this FilterForListAddonsInput.  # noqa: E501


        :return: The deploy_node_types of this FilterForListAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._deploy_node_types

    @deploy_node_types.setter
    def deploy_node_types(self, deploy_node_types):
        """Sets the deploy_node_types of this FilterForListAddonsInput.


        :param deploy_node_types: The deploy_node_types of this FilterForListAddonsInput.  # noqa: E501
        :type: list[str]
        """
        allowed_values = ["Node", "VirtualNode", "EdgeNode"]  # noqa: E501
        if (self._configuration.client_side_validation and
                not set(deploy_node_types).issubset(set(allowed_values))):  # noqa: E501
            raise ValueError(
                "Invalid values for `deploy_node_types` [{0}], must be a subset of [{1}]"  # noqa: E501
                .format(", ".join(map(str, set(deploy_node_types) - set(allowed_values))),  # noqa: E501
                        ", ".join(map(str, allowed_values)))
            )

        self._deploy_node_types = deploy_node_types

    @property
    def names(self):
        """Gets the names of this FilterForListAddonsInput.  # noqa: E501


        :return: The names of this FilterForListAddonsInput.  # noqa: E501
        :rtype: list[str]
        """
        return self._names

    @names.setter
    def names(self, names):
        """Sets the names of this FilterForListAddonsInput.


        :param names: The names of this FilterForListAddonsInput.  # noqa: E501
        :type: list[str]
        """

        self._names = names

    @property
    def statuses(self):
        """Gets the statuses of this FilterForListAddonsInput.  # noqa: E501


        :return: The statuses of this FilterForListAddonsInput.  # noqa: E501
        :rtype: list[StatusForListAddonsInput]
        """
        return self._statuses

    @statuses.setter
    def statuses(self, statuses):
        """Sets the statuses of this FilterForListAddonsInput.


        :param statuses: The statuses of this FilterForListAddonsInput.  # noqa: E501
        :type: list[StatusForListAddonsInput]
        """

        self._statuses = statuses

    @property
    def update_client_token(self):
        """Gets the update_client_token of this FilterForListAddonsInput.  # noqa: E501


        :return: The update_client_token of this FilterForListAddonsInput.  # noqa: E501
        :rtype: str
        """
        return self._update_client_token

    @update_client_token.setter
    def update_client_token(self, update_client_token):
        """Sets the update_client_token of this FilterForListAddonsInput.


        :param update_client_token: The update_client_token of this FilterForListAddonsInput.  # noqa: E501
        :type: str
        """

        self._update_client_token = update_client_token

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(FilterForListAddonsInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, FilterForListAddonsInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, FilterForListAddonsInput):
            return True

        return self.to_dict() != other.to_dict()
