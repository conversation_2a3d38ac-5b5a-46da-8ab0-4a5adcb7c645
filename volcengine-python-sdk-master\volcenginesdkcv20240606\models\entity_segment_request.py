# coding: utf-8

"""
    cv20240606

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class EntitySegmentRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'binary_data_base64': 'list[str]',
        'image_urls': 'list[str]',
        'max_entity': 'int',
        'refine_mask': 'int',
        'req_key': 'str',
        'return_format': 'int'
    }

    attribute_map = {
        'binary_data_base64': 'binary_data_base64',
        'image_urls': 'image_urls',
        'max_entity': 'max_entity',
        'refine_mask': 'refine_mask',
        'req_key': 'req_key',
        'return_format': 'return_format'
    }

    def __init__(self, binary_data_base64=None, image_urls=None, max_entity=None, refine_mask=None, req_key=None, return_format=None, _configuration=None):  # noqa: E501
        """EntitySegmentRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._binary_data_base64 = None
        self._image_urls = None
        self._max_entity = None
        self._refine_mask = None
        self._req_key = None
        self._return_format = None
        self.discriminator = None

        if binary_data_base64 is not None:
            self.binary_data_base64 = binary_data_base64
        if image_urls is not None:
            self.image_urls = image_urls
        if max_entity is not None:
            self.max_entity = max_entity
        if refine_mask is not None:
            self.refine_mask = refine_mask
        self.req_key = req_key
        if return_format is not None:
            self.return_format = return_format

    @property
    def binary_data_base64(self):
        """Gets the binary_data_base64 of this EntitySegmentRequest.  # noqa: E501


        :return: The binary_data_base64 of this EntitySegmentRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._binary_data_base64

    @binary_data_base64.setter
    def binary_data_base64(self, binary_data_base64):
        """Sets the binary_data_base64 of this EntitySegmentRequest.


        :param binary_data_base64: The binary_data_base64 of this EntitySegmentRequest.  # noqa: E501
        :type: list[str]
        """

        self._binary_data_base64 = binary_data_base64

    @property
    def image_urls(self):
        """Gets the image_urls of this EntitySegmentRequest.  # noqa: E501


        :return: The image_urls of this EntitySegmentRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._image_urls

    @image_urls.setter
    def image_urls(self, image_urls):
        """Sets the image_urls of this EntitySegmentRequest.


        :param image_urls: The image_urls of this EntitySegmentRequest.  # noqa: E501
        :type: list[str]
        """

        self._image_urls = image_urls

    @property
    def max_entity(self):
        """Gets the max_entity of this EntitySegmentRequest.  # noqa: E501


        :return: The max_entity of this EntitySegmentRequest.  # noqa: E501
        :rtype: int
        """
        return self._max_entity

    @max_entity.setter
    def max_entity(self, max_entity):
        """Sets the max_entity of this EntitySegmentRequest.


        :param max_entity: The max_entity of this EntitySegmentRequest.  # noqa: E501
        :type: int
        """

        self._max_entity = max_entity

    @property
    def refine_mask(self):
        """Gets the refine_mask of this EntitySegmentRequest.  # noqa: E501


        :return: The refine_mask of this EntitySegmentRequest.  # noqa: E501
        :rtype: int
        """
        return self._refine_mask

    @refine_mask.setter
    def refine_mask(self, refine_mask):
        """Sets the refine_mask of this EntitySegmentRequest.


        :param refine_mask: The refine_mask of this EntitySegmentRequest.  # noqa: E501
        :type: int
        """

        self._refine_mask = refine_mask

    @property
    def req_key(self):
        """Gets the req_key of this EntitySegmentRequest.  # noqa: E501


        :return: The req_key of this EntitySegmentRequest.  # noqa: E501
        :rtype: str
        """
        return self._req_key

    @req_key.setter
    def req_key(self, req_key):
        """Sets the req_key of this EntitySegmentRequest.


        :param req_key: The req_key of this EntitySegmentRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and req_key is None:
            raise ValueError("Invalid value for `req_key`, must not be `None`")  # noqa: E501

        self._req_key = req_key

    @property
    def return_format(self):
        """Gets the return_format of this EntitySegmentRequest.  # noqa: E501


        :return: The return_format of this EntitySegmentRequest.  # noqa: E501
        :rtype: int
        """
        return self._return_format

    @return_format.setter
    def return_format(self, return_format):
        """Sets the return_format of this EntitySegmentRequest.


        :param return_format: The return_format of this EntitySegmentRequest.  # noqa: E501
        :type: int
        """

        self._return_format = return_format

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(EntitySegmentRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, EntitySegmentRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, EntitySegmentRequest):
            return True

        return self.to_dict() != other.to_dict()
