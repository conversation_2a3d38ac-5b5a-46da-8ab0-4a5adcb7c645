# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetServiceResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'apig_config': 'ApigConfigForGetServiceOutput',
        'clb_config': 'CLBConfigForGetServiceOutput',
        'create_time': 'str',
        'created_by': 'str',
        'deployments': 'list[DeploymentForGetServiceOutput]',
        'description': 'str',
        'history_versions': 'list[HistoryVersionForGetServiceOutput]',
        'id': 'str',
        'name': 'str',
        'ports': 'list[PortForGetServiceOutput]',
        'status': 'ConvertStatusForGetServiceOutput',
        'traffic_config': 'ConvertTrafficConfigForGetServiceOutput',
        'update_time': 'str',
        'vpc_id': 'str'
    }

    attribute_map = {
        'apig_config': 'ApigConfig',
        'clb_config': 'CLBConfig',
        'create_time': 'CreateTime',
        'created_by': 'CreatedBy',
        'deployments': 'Deployments',
        'description': 'Description',
        'history_versions': 'HistoryVersions',
        'id': 'Id',
        'name': 'Name',
        'ports': 'Ports',
        'status': 'Status',
        'traffic_config': 'TrafficConfig',
        'update_time': 'UpdateTime',
        'vpc_id': 'VpcId'
    }

    def __init__(self, apig_config=None, clb_config=None, create_time=None, created_by=None, deployments=None, description=None, history_versions=None, id=None, name=None, ports=None, status=None, traffic_config=None, update_time=None, vpc_id=None, _configuration=None):  # noqa: E501
        """GetServiceResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._apig_config = None
        self._clb_config = None
        self._create_time = None
        self._created_by = None
        self._deployments = None
        self._description = None
        self._history_versions = None
        self._id = None
        self._name = None
        self._ports = None
        self._status = None
        self._traffic_config = None
        self._update_time = None
        self._vpc_id = None
        self.discriminator = None

        if apig_config is not None:
            self.apig_config = apig_config
        if clb_config is not None:
            self.clb_config = clb_config
        if create_time is not None:
            self.create_time = create_time
        if created_by is not None:
            self.created_by = created_by
        if deployments is not None:
            self.deployments = deployments
        if description is not None:
            self.description = description
        if history_versions is not None:
            self.history_versions = history_versions
        if id is not None:
            self.id = id
        if name is not None:
            self.name = name
        if ports is not None:
            self.ports = ports
        if status is not None:
            self.status = status
        if traffic_config is not None:
            self.traffic_config = traffic_config
        if update_time is not None:
            self.update_time = update_time
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def apig_config(self):
        """Gets the apig_config of this GetServiceResponse.  # noqa: E501


        :return: The apig_config of this GetServiceResponse.  # noqa: E501
        :rtype: ApigConfigForGetServiceOutput
        """
        return self._apig_config

    @apig_config.setter
    def apig_config(self, apig_config):
        """Sets the apig_config of this GetServiceResponse.


        :param apig_config: The apig_config of this GetServiceResponse.  # noqa: E501
        :type: ApigConfigForGetServiceOutput
        """

        self._apig_config = apig_config

    @property
    def clb_config(self):
        """Gets the clb_config of this GetServiceResponse.  # noqa: E501


        :return: The clb_config of this GetServiceResponse.  # noqa: E501
        :rtype: CLBConfigForGetServiceOutput
        """
        return self._clb_config

    @clb_config.setter
    def clb_config(self, clb_config):
        """Sets the clb_config of this GetServiceResponse.


        :param clb_config: The clb_config of this GetServiceResponse.  # noqa: E501
        :type: CLBConfigForGetServiceOutput
        """

        self._clb_config = clb_config

    @property
    def create_time(self):
        """Gets the create_time of this GetServiceResponse.  # noqa: E501


        :return: The create_time of this GetServiceResponse.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this GetServiceResponse.


        :param create_time: The create_time of this GetServiceResponse.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def created_by(self):
        """Gets the created_by of this GetServiceResponse.  # noqa: E501


        :return: The created_by of this GetServiceResponse.  # noqa: E501
        :rtype: str
        """
        return self._created_by

    @created_by.setter
    def created_by(self, created_by):
        """Sets the created_by of this GetServiceResponse.


        :param created_by: The created_by of this GetServiceResponse.  # noqa: E501
        :type: str
        """

        self._created_by = created_by

    @property
    def deployments(self):
        """Gets the deployments of this GetServiceResponse.  # noqa: E501


        :return: The deployments of this GetServiceResponse.  # noqa: E501
        :rtype: list[DeploymentForGetServiceOutput]
        """
        return self._deployments

    @deployments.setter
    def deployments(self, deployments):
        """Sets the deployments of this GetServiceResponse.


        :param deployments: The deployments of this GetServiceResponse.  # noqa: E501
        :type: list[DeploymentForGetServiceOutput]
        """

        self._deployments = deployments

    @property
    def description(self):
        """Gets the description of this GetServiceResponse.  # noqa: E501


        :return: The description of this GetServiceResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetServiceResponse.


        :param description: The description of this GetServiceResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def history_versions(self):
        """Gets the history_versions of this GetServiceResponse.  # noqa: E501


        :return: The history_versions of this GetServiceResponse.  # noqa: E501
        :rtype: list[HistoryVersionForGetServiceOutput]
        """
        return self._history_versions

    @history_versions.setter
    def history_versions(self, history_versions):
        """Sets the history_versions of this GetServiceResponse.


        :param history_versions: The history_versions of this GetServiceResponse.  # noqa: E501
        :type: list[HistoryVersionForGetServiceOutput]
        """

        self._history_versions = history_versions

    @property
    def id(self):
        """Gets the id of this GetServiceResponse.  # noqa: E501


        :return: The id of this GetServiceResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetServiceResponse.


        :param id: The id of this GetServiceResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def name(self):
        """Gets the name of this GetServiceResponse.  # noqa: E501


        :return: The name of this GetServiceResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetServiceResponse.


        :param name: The name of this GetServiceResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def ports(self):
        """Gets the ports of this GetServiceResponse.  # noqa: E501


        :return: The ports of this GetServiceResponse.  # noqa: E501
        :rtype: list[PortForGetServiceOutput]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this GetServiceResponse.


        :param ports: The ports of this GetServiceResponse.  # noqa: E501
        :type: list[PortForGetServiceOutput]
        """

        self._ports = ports

    @property
    def status(self):
        """Gets the status of this GetServiceResponse.  # noqa: E501


        :return: The status of this GetServiceResponse.  # noqa: E501
        :rtype: ConvertStatusForGetServiceOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetServiceResponse.


        :param status: The status of this GetServiceResponse.  # noqa: E501
        :type: ConvertStatusForGetServiceOutput
        """

        self._status = status

    @property
    def traffic_config(self):
        """Gets the traffic_config of this GetServiceResponse.  # noqa: E501


        :return: The traffic_config of this GetServiceResponse.  # noqa: E501
        :rtype: ConvertTrafficConfigForGetServiceOutput
        """
        return self._traffic_config

    @traffic_config.setter
    def traffic_config(self, traffic_config):
        """Sets the traffic_config of this GetServiceResponse.


        :param traffic_config: The traffic_config of this GetServiceResponse.  # noqa: E501
        :type: ConvertTrafficConfigForGetServiceOutput
        """

        self._traffic_config = traffic_config

    @property
    def update_time(self):
        """Gets the update_time of this GetServiceResponse.  # noqa: E501


        :return: The update_time of this GetServiceResponse.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this GetServiceResponse.


        :param update_time: The update_time of this GetServiceResponse.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    @property
    def vpc_id(self):
        """Gets the vpc_id of this GetServiceResponse.  # noqa: E501


        :return: The vpc_id of this GetServiceResponse.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this GetServiceResponse.


        :param vpc_id: The vpc_id of this GetServiceResponse.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetServiceResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetServiceResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetServiceResponse):
            return True

        return self.to_dict() != other.to_dict()
