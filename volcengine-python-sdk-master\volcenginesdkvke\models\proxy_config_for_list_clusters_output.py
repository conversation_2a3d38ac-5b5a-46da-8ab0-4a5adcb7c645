# coding: utf-8

"""
    vke

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ProxyConfigForListClustersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'acl_enabled': 'bool',
        'acl_ip_whitelist': 'list[str]',
        'api_server_endpoints': 'ApiServerEndpointsForListClustersOutput',
        'api_server_public_access_config': 'ApiServerPublicAccessConfigForListClustersOutput',
        'api_server_public_access_enabled': 'bool',
        'subnet_ids': 'list[str]',
        'vpc_id': 'str'
    }

    attribute_map = {
        'acl_enabled': 'AclEnabled',
        'acl_ip_whitelist': 'AclIpWhitelist',
        'api_server_endpoints': 'ApiServerEndpoints',
        'api_server_public_access_config': 'ApiServerPublicAccessConfig',
        'api_server_public_access_enabled': 'ApiServerPublicAccessEnabled',
        'subnet_ids': 'SubnetIds',
        'vpc_id': 'VpcId'
    }

    def __init__(self, acl_enabled=None, acl_ip_whitelist=None, api_server_endpoints=None, api_server_public_access_config=None, api_server_public_access_enabled=None, subnet_ids=None, vpc_id=None, _configuration=None):  # noqa: E501
        """ProxyConfigForListClustersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._acl_enabled = None
        self._acl_ip_whitelist = None
        self._api_server_endpoints = None
        self._api_server_public_access_config = None
        self._api_server_public_access_enabled = None
        self._subnet_ids = None
        self._vpc_id = None
        self.discriminator = None

        if acl_enabled is not None:
            self.acl_enabled = acl_enabled
        if acl_ip_whitelist is not None:
            self.acl_ip_whitelist = acl_ip_whitelist
        if api_server_endpoints is not None:
            self.api_server_endpoints = api_server_endpoints
        if api_server_public_access_config is not None:
            self.api_server_public_access_config = api_server_public_access_config
        if api_server_public_access_enabled is not None:
            self.api_server_public_access_enabled = api_server_public_access_enabled
        if subnet_ids is not None:
            self.subnet_ids = subnet_ids
        if vpc_id is not None:
            self.vpc_id = vpc_id

    @property
    def acl_enabled(self):
        """Gets the acl_enabled of this ProxyConfigForListClustersOutput.  # noqa: E501


        :return: The acl_enabled of this ProxyConfigForListClustersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._acl_enabled

    @acl_enabled.setter
    def acl_enabled(self, acl_enabled):
        """Sets the acl_enabled of this ProxyConfigForListClustersOutput.


        :param acl_enabled: The acl_enabled of this ProxyConfigForListClustersOutput.  # noqa: E501
        :type: bool
        """

        self._acl_enabled = acl_enabled

    @property
    def acl_ip_whitelist(self):
        """Gets the acl_ip_whitelist of this ProxyConfigForListClustersOutput.  # noqa: E501


        :return: The acl_ip_whitelist of this ProxyConfigForListClustersOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._acl_ip_whitelist

    @acl_ip_whitelist.setter
    def acl_ip_whitelist(self, acl_ip_whitelist):
        """Sets the acl_ip_whitelist of this ProxyConfigForListClustersOutput.


        :param acl_ip_whitelist: The acl_ip_whitelist of this ProxyConfigForListClustersOutput.  # noqa: E501
        :type: list[str]
        """

        self._acl_ip_whitelist = acl_ip_whitelist

    @property
    def api_server_endpoints(self):
        """Gets the api_server_endpoints of this ProxyConfigForListClustersOutput.  # noqa: E501


        :return: The api_server_endpoints of this ProxyConfigForListClustersOutput.  # noqa: E501
        :rtype: ApiServerEndpointsForListClustersOutput
        """
        return self._api_server_endpoints

    @api_server_endpoints.setter
    def api_server_endpoints(self, api_server_endpoints):
        """Sets the api_server_endpoints of this ProxyConfigForListClustersOutput.


        :param api_server_endpoints: The api_server_endpoints of this ProxyConfigForListClustersOutput.  # noqa: E501
        :type: ApiServerEndpointsForListClustersOutput
        """

        self._api_server_endpoints = api_server_endpoints

    @property
    def api_server_public_access_config(self):
        """Gets the api_server_public_access_config of this ProxyConfigForListClustersOutput.  # noqa: E501


        :return: The api_server_public_access_config of this ProxyConfigForListClustersOutput.  # noqa: E501
        :rtype: ApiServerPublicAccessConfigForListClustersOutput
        """
        return self._api_server_public_access_config

    @api_server_public_access_config.setter
    def api_server_public_access_config(self, api_server_public_access_config):
        """Sets the api_server_public_access_config of this ProxyConfigForListClustersOutput.


        :param api_server_public_access_config: The api_server_public_access_config of this ProxyConfigForListClustersOutput.  # noqa: E501
        :type: ApiServerPublicAccessConfigForListClustersOutput
        """

        self._api_server_public_access_config = api_server_public_access_config

    @property
    def api_server_public_access_enabled(self):
        """Gets the api_server_public_access_enabled of this ProxyConfigForListClustersOutput.  # noqa: E501


        :return: The api_server_public_access_enabled of this ProxyConfigForListClustersOutput.  # noqa: E501
        :rtype: bool
        """
        return self._api_server_public_access_enabled

    @api_server_public_access_enabled.setter
    def api_server_public_access_enabled(self, api_server_public_access_enabled):
        """Sets the api_server_public_access_enabled of this ProxyConfigForListClustersOutput.


        :param api_server_public_access_enabled: The api_server_public_access_enabled of this ProxyConfigForListClustersOutput.  # noqa: E501
        :type: bool
        """

        self._api_server_public_access_enabled = api_server_public_access_enabled

    @property
    def subnet_ids(self):
        """Gets the subnet_ids of this ProxyConfigForListClustersOutput.  # noqa: E501


        :return: The subnet_ids of this ProxyConfigForListClustersOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._subnet_ids

    @subnet_ids.setter
    def subnet_ids(self, subnet_ids):
        """Sets the subnet_ids of this ProxyConfigForListClustersOutput.


        :param subnet_ids: The subnet_ids of this ProxyConfigForListClustersOutput.  # noqa: E501
        :type: list[str]
        """

        self._subnet_ids = subnet_ids

    @property
    def vpc_id(self):
        """Gets the vpc_id of this ProxyConfigForListClustersOutput.  # noqa: E501


        :return: The vpc_id of this ProxyConfigForListClustersOutput.  # noqa: E501
        :rtype: str
        """
        return self._vpc_id

    @vpc_id.setter
    def vpc_id(self, vpc_id):
        """Sets the vpc_id of this ProxyConfigForListClustersOutput.


        :param vpc_id: The vpc_id of this ProxyConfigForListClustersOutput.  # noqa: E501
        :type: str
        """

        self._vpc_id = vpc_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ProxyConfigForListClustersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ProxyConfigForListClustersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ProxyConfigForListClustersOutput):
            return True

        return self.to_dict() != other.to_dict()
