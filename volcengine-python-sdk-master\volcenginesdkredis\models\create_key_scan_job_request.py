# coding: utf-8

"""
    redis

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateKeyScanJobRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'scan_key_num_per_second': 'int',
        'timeout_minutes': 'int'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'scan_key_num_per_second': 'ScanKeyNumPerSecond',
        'timeout_minutes': 'TimeoutMinutes'
    }

    def __init__(self, instance_id=None, scan_key_num_per_second=None, timeout_minutes=None, _configuration=None):  # noqa: E501
        """CreateKeyScanJobRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._scan_key_num_per_second = None
        self._timeout_minutes = None
        self.discriminator = None

        self.instance_id = instance_id
        self.scan_key_num_per_second = scan_key_num_per_second
        self.timeout_minutes = timeout_minutes

    @property
    def instance_id(self):
        """Gets the instance_id of this CreateKeyScanJobRequest.  # noqa: E501


        :return: The instance_id of this CreateKeyScanJobRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this CreateKeyScanJobRequest.


        :param instance_id: The instance_id of this CreateKeyScanJobRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def scan_key_num_per_second(self):
        """Gets the scan_key_num_per_second of this CreateKeyScanJobRequest.  # noqa: E501


        :return: The scan_key_num_per_second of this CreateKeyScanJobRequest.  # noqa: E501
        :rtype: int
        """
        return self._scan_key_num_per_second

    @scan_key_num_per_second.setter
    def scan_key_num_per_second(self, scan_key_num_per_second):
        """Sets the scan_key_num_per_second of this CreateKeyScanJobRequest.


        :param scan_key_num_per_second: The scan_key_num_per_second of this CreateKeyScanJobRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and scan_key_num_per_second is None:
            raise ValueError("Invalid value for `scan_key_num_per_second`, must not be `None`")  # noqa: E501

        self._scan_key_num_per_second = scan_key_num_per_second

    @property
    def timeout_minutes(self):
        """Gets the timeout_minutes of this CreateKeyScanJobRequest.  # noqa: E501


        :return: The timeout_minutes of this CreateKeyScanJobRequest.  # noqa: E501
        :rtype: int
        """
        return self._timeout_minutes

    @timeout_minutes.setter
    def timeout_minutes(self, timeout_minutes):
        """Sets the timeout_minutes of this CreateKeyScanJobRequest.


        :param timeout_minutes: The timeout_minutes of this CreateKeyScanJobRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and timeout_minutes is None:
            raise ValueError("Invalid value for `timeout_minutes`, must not be `None`")  # noqa: E501

        self._timeout_minutes = timeout_minutes

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateKeyScanJobRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateKeyScanJobRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateKeyScanJobRequest):
            return True

        return self.to_dict() != other.to_dict()
