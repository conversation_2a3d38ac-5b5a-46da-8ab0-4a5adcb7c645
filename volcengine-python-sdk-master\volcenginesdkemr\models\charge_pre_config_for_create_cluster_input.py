# coding: utf-8

"""
    emr

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ChargePreConfigForCreateClusterInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_renew': 'bool',
        'auto_renew_period': 'int',
        'auto_renew_period_unit': 'str',
        'charge_period': 'int',
        'charge_period_unit': 'str',
        'charge_type': 'str'
    }

    attribute_map = {
        'auto_renew': 'AutoRenew',
        'auto_renew_period': 'AutoRenewPeriod',
        'auto_renew_period_unit': 'AutoRenewPeriodUnit',
        'charge_period': 'ChargePeriod',
        'charge_period_unit': 'ChargePeriodUnit',
        'charge_type': 'ChargeType'
    }

    def __init__(self, auto_renew=None, auto_renew_period=None, auto_renew_period_unit=None, charge_period=None, charge_period_unit=None, charge_type=None, _configuration=None):  # noqa: E501
        """ChargePreConfigForCreateClusterInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_renew = None
        self._auto_renew_period = None
        self._auto_renew_period_unit = None
        self._charge_period = None
        self._charge_period_unit = None
        self._charge_type = None
        self.discriminator = None

        if auto_renew is not None:
            self.auto_renew = auto_renew
        if auto_renew_period is not None:
            self.auto_renew_period = auto_renew_period
        if auto_renew_period_unit is not None:
            self.auto_renew_period_unit = auto_renew_period_unit
        if charge_period is not None:
            self.charge_period = charge_period
        if charge_period_unit is not None:
            self.charge_period_unit = charge_period_unit
        if charge_type is not None:
            self.charge_type = charge_type

    @property
    def auto_renew(self):
        """Gets the auto_renew of this ChargePreConfigForCreateClusterInput.  # noqa: E501


        :return: The auto_renew of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :rtype: bool
        """
        return self._auto_renew

    @auto_renew.setter
    def auto_renew(self, auto_renew):
        """Sets the auto_renew of this ChargePreConfigForCreateClusterInput.


        :param auto_renew: The auto_renew of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :type: bool
        """

        self._auto_renew = auto_renew

    @property
    def auto_renew_period(self):
        """Gets the auto_renew_period of this ChargePreConfigForCreateClusterInput.  # noqa: E501


        :return: The auto_renew_period of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :rtype: int
        """
        return self._auto_renew_period

    @auto_renew_period.setter
    def auto_renew_period(self, auto_renew_period):
        """Sets the auto_renew_period of this ChargePreConfigForCreateClusterInput.


        :param auto_renew_period: The auto_renew_period of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :type: int
        """

        self._auto_renew_period = auto_renew_period

    @property
    def auto_renew_period_unit(self):
        """Gets the auto_renew_period_unit of this ChargePreConfigForCreateClusterInput.  # noqa: E501


        :return: The auto_renew_period_unit of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._auto_renew_period_unit

    @auto_renew_period_unit.setter
    def auto_renew_period_unit(self, auto_renew_period_unit):
        """Sets the auto_renew_period_unit of this ChargePreConfigForCreateClusterInput.


        :param auto_renew_period_unit: The auto_renew_period_unit of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Month", "Year"]  # noqa: E501
        if (self._configuration.client_side_validation and
                auto_renew_period_unit not in allowed_values):
            raise ValueError(
                "Invalid value for `auto_renew_period_unit` ({0}), must be one of {1}"  # noqa: E501
                .format(auto_renew_period_unit, allowed_values)
            )

        self._auto_renew_period_unit = auto_renew_period_unit

    @property
    def charge_period(self):
        """Gets the charge_period of this ChargePreConfigForCreateClusterInput.  # noqa: E501


        :return: The charge_period of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :rtype: int
        """
        return self._charge_period

    @charge_period.setter
    def charge_period(self, charge_period):
        """Sets the charge_period of this ChargePreConfigForCreateClusterInput.


        :param charge_period: The charge_period of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :type: int
        """

        self._charge_period = charge_period

    @property
    def charge_period_unit(self):
        """Gets the charge_period_unit of this ChargePreConfigForCreateClusterInput.  # noqa: E501


        :return: The charge_period_unit of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._charge_period_unit

    @charge_period_unit.setter
    def charge_period_unit(self, charge_period_unit):
        """Sets the charge_period_unit of this ChargePreConfigForCreateClusterInput.


        :param charge_period_unit: The charge_period_unit of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["Month", "Year"]  # noqa: E501
        if (self._configuration.client_side_validation and
                charge_period_unit not in allowed_values):
            raise ValueError(
                "Invalid value for `charge_period_unit` ({0}), must be one of {1}"  # noqa: E501
                .format(charge_period_unit, allowed_values)
            )

        self._charge_period_unit = charge_period_unit

    @property
    def charge_type(self):
        """Gets the charge_type of this ChargePreConfigForCreateClusterInput.  # noqa: E501


        :return: The charge_type of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this ChargePreConfigForCreateClusterInput.


        :param charge_type: The charge_type of this ChargePreConfigForCreateClusterInput.  # noqa: E501
        :type: str
        """
        allowed_values = ["PRE", "POST"]  # noqa: E501
        if (self._configuration.client_side_validation and
                charge_type not in allowed_values):
            raise ValueError(
                "Invalid value for `charge_type` ({0}), must be one of {1}"  # noqa: E501
                .format(charge_type, allowed_values)
            )

        self._charge_type = charge_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ChargePreConfigForCreateClusterInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ChargePreConfigForCreateClusterInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ChargePreConfigForCreateClusterInput):
            return True

        return self.to_dict() != other.to_dict()
