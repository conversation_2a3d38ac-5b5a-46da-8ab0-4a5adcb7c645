# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListDevBasicInfosRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'asset_id': 'str',
        'asset_ids': 'list[str]',
        'asset_name': 'str',
        'ip': 'str',
        'is_all': 'bool',
        'page_number': 'int',
        'page_size': 'int',
        'platforms': 'list[str]',
        'regions': 'list[str]',
        'risk': 'RiskForListDevBasicInfosInput',
        'sort_by': 'str',
        'sort_order': 'str',
        'status': 'list[str]'
    }

    attribute_map = {
        'asset_id': 'AssetId',
        'asset_ids': 'AssetIds',
        'asset_name': 'AssetName',
        'ip': 'IP',
        'is_all': 'IsAll',
        'page_number': 'PageNumber',
        'page_size': 'PageSize',
        'platforms': 'Platforms',
        'regions': 'Regions',
        'risk': 'Risk',
        'sort_by': 'SortBy',
        'sort_order': 'SortOrder',
        'status': 'Status'
    }

    def __init__(self, asset_id=None, asset_ids=None, asset_name=None, ip=None, is_all=None, page_number=None, page_size=None, platforms=None, regions=None, risk=None, sort_by=None, sort_order=None, status=None, _configuration=None):  # noqa: E501
        """ListDevBasicInfosRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._asset_id = None
        self._asset_ids = None
        self._asset_name = None
        self._ip = None
        self._is_all = None
        self._page_number = None
        self._page_size = None
        self._platforms = None
        self._regions = None
        self._risk = None
        self._sort_by = None
        self._sort_order = None
        self._status = None
        self.discriminator = None

        if asset_id is not None:
            self.asset_id = asset_id
        if asset_ids is not None:
            self.asset_ids = asset_ids
        if asset_name is not None:
            self.asset_name = asset_name
        if ip is not None:
            self.ip = ip
        if is_all is not None:
            self.is_all = is_all
        self.page_number = page_number
        self.page_size = page_size
        if platforms is not None:
            self.platforms = platforms
        if regions is not None:
            self.regions = regions
        if risk is not None:
            self.risk = risk
        if sort_by is not None:
            self.sort_by = sort_by
        if sort_order is not None:
            self.sort_order = sort_order
        if status is not None:
            self.status = status

    @property
    def asset_id(self):
        """Gets the asset_id of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The asset_id of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this ListDevBasicInfosRequest.


        :param asset_id: The asset_id of this ListDevBasicInfosRequest.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_ids(self):
        """Gets the asset_ids of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The asset_ids of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._asset_ids

    @asset_ids.setter
    def asset_ids(self, asset_ids):
        """Sets the asset_ids of this ListDevBasicInfosRequest.


        :param asset_ids: The asset_ids of this ListDevBasicInfosRequest.  # noqa: E501
        :type: list[str]
        """

        self._asset_ids = asset_ids

    @property
    def asset_name(self):
        """Gets the asset_name of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The asset_name of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this ListDevBasicInfosRequest.


        :param asset_name: The asset_name of this ListDevBasicInfosRequest.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def ip(self):
        """Gets the ip of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The ip of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: str
        """
        return self._ip

    @ip.setter
    def ip(self, ip):
        """Sets the ip of this ListDevBasicInfosRequest.


        :param ip: The ip of this ListDevBasicInfosRequest.  # noqa: E501
        :type: str
        """

        self._ip = ip

    @property
    def is_all(self):
        """Gets the is_all of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The is_all of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: bool
        """
        return self._is_all

    @is_all.setter
    def is_all(self, is_all):
        """Sets the is_all of this ListDevBasicInfosRequest.


        :param is_all: The is_all of this ListDevBasicInfosRequest.  # noqa: E501
        :type: bool
        """

        self._is_all = is_all

    @property
    def page_number(self):
        """Gets the page_number of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The page_number of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_number

    @page_number.setter
    def page_number(self, page_number):
        """Sets the page_number of this ListDevBasicInfosRequest.


        :param page_number: The page_number of this ListDevBasicInfosRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_number is None:
            raise ValueError("Invalid value for `page_number`, must not be `None`")  # noqa: E501

        self._page_number = page_number

    @property
    def page_size(self):
        """Gets the page_size of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The page_size of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: int
        """
        return self._page_size

    @page_size.setter
    def page_size(self, page_size):
        """Sets the page_size of this ListDevBasicInfosRequest.


        :param page_size: The page_size of this ListDevBasicInfosRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and page_size is None:
            raise ValueError("Invalid value for `page_size`, must not be `None`")  # noqa: E501

        self._page_size = page_size

    @property
    def platforms(self):
        """Gets the platforms of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The platforms of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._platforms

    @platforms.setter
    def platforms(self, platforms):
        """Sets the platforms of this ListDevBasicInfosRequest.


        :param platforms: The platforms of this ListDevBasicInfosRequest.  # noqa: E501
        :type: list[str]
        """

        self._platforms = platforms

    @property
    def regions(self):
        """Gets the regions of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The regions of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._regions

    @regions.setter
    def regions(self, regions):
        """Sets the regions of this ListDevBasicInfosRequest.


        :param regions: The regions of this ListDevBasicInfosRequest.  # noqa: E501
        :type: list[str]
        """

        self._regions = regions

    @property
    def risk(self):
        """Gets the risk of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The risk of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: RiskForListDevBasicInfosInput
        """
        return self._risk

    @risk.setter
    def risk(self, risk):
        """Sets the risk of this ListDevBasicInfosRequest.


        :param risk: The risk of this ListDevBasicInfosRequest.  # noqa: E501
        :type: RiskForListDevBasicInfosInput
        """

        self._risk = risk

    @property
    def sort_by(self):
        """Gets the sort_by of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The sort_by of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_by

    @sort_by.setter
    def sort_by(self, sort_by):
        """Sets the sort_by of this ListDevBasicInfosRequest.


        :param sort_by: The sort_by of this ListDevBasicInfosRequest.  # noqa: E501
        :type: str
        """

        self._sort_by = sort_by

    @property
    def sort_order(self):
        """Gets the sort_order of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The sort_order of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: str
        """
        return self._sort_order

    @sort_order.setter
    def sort_order(self, sort_order):
        """Sets the sort_order of this ListDevBasicInfosRequest.


        :param sort_order: The sort_order of this ListDevBasicInfosRequest.  # noqa: E501
        :type: str
        """

        self._sort_order = sort_order

    @property
    def status(self):
        """Gets the status of this ListDevBasicInfosRequest.  # noqa: E501


        :return: The status of this ListDevBasicInfosRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListDevBasicInfosRequest.


        :param status: The status of this ListDevBasicInfosRequest.  # noqa: E501
        :type: list[str]
        """

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListDevBasicInfosRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListDevBasicInfosRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListDevBasicInfosRequest):
            return True

        return self.to_dict() != other.to_dict()
