# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class RelationForListInvitationOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_alias': 'str',
        'auth_info': 'list[AuthInfoForListInvitationOutput]',
        'filiation': 'int',
        'filiation_desc': 'str',
        'major_account_id': 'int',
        'major_account_name': 'str',
        'relation': 'int',
        'relation_desc': 'str',
        'relation_id': 'str',
        'status': 'int',
        'status_desc': 'str',
        'sub_account_id': 'int',
        'sub_account_name': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'account_alias': 'AccountAlias',
        'auth_info': 'AuthInfo',
        'filiation': 'Filiation',
        'filiation_desc': 'FiliationDesc',
        'major_account_id': 'MajorAccountID',
        'major_account_name': 'MajorAccountName',
        'relation': 'Relation',
        'relation_desc': 'RelationDesc',
        'relation_id': 'RelationID',
        'status': 'Status',
        'status_desc': 'StatusDesc',
        'sub_account_id': 'SubAccountID',
        'sub_account_name': 'SubAccountName',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_alias=None, auth_info=None, filiation=None, filiation_desc=None, major_account_id=None, major_account_name=None, relation=None, relation_desc=None, relation_id=None, status=None, status_desc=None, sub_account_id=None, sub_account_name=None, update_time=None, _configuration=None):  # noqa: E501
        """RelationForListInvitationOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_alias = None
        self._auth_info = None
        self._filiation = None
        self._filiation_desc = None
        self._major_account_id = None
        self._major_account_name = None
        self._relation = None
        self._relation_desc = None
        self._relation_id = None
        self._status = None
        self._status_desc = None
        self._sub_account_id = None
        self._sub_account_name = None
        self._update_time = None
        self.discriminator = None

        if account_alias is not None:
            self.account_alias = account_alias
        if auth_info is not None:
            self.auth_info = auth_info
        if filiation is not None:
            self.filiation = filiation
        if filiation_desc is not None:
            self.filiation_desc = filiation_desc
        if major_account_id is not None:
            self.major_account_id = major_account_id
        if major_account_name is not None:
            self.major_account_name = major_account_name
        if relation is not None:
            self.relation = relation
        if relation_desc is not None:
            self.relation_desc = relation_desc
        if relation_id is not None:
            self.relation_id = relation_id
        if status is not None:
            self.status = status
        if status_desc is not None:
            self.status_desc = status_desc
        if sub_account_id is not None:
            self.sub_account_id = sub_account_id
        if sub_account_name is not None:
            self.sub_account_name = sub_account_name
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_alias(self):
        """Gets the account_alias of this RelationForListInvitationOutput.  # noqa: E501


        :return: The account_alias of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_alias

    @account_alias.setter
    def account_alias(self, account_alias):
        """Sets the account_alias of this RelationForListInvitationOutput.


        :param account_alias: The account_alias of this RelationForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._account_alias = account_alias

    @property
    def auth_info(self):
        """Gets the auth_info of this RelationForListInvitationOutput.  # noqa: E501


        :return: The auth_info of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: list[AuthInfoForListInvitationOutput]
        """
        return self._auth_info

    @auth_info.setter
    def auth_info(self, auth_info):
        """Sets the auth_info of this RelationForListInvitationOutput.


        :param auth_info: The auth_info of this RelationForListInvitationOutput.  # noqa: E501
        :type: list[AuthInfoForListInvitationOutput]
        """

        self._auth_info = auth_info

    @property
    def filiation(self):
        """Gets the filiation of this RelationForListInvitationOutput.  # noqa: E501


        :return: The filiation of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._filiation

    @filiation.setter
    def filiation(self, filiation):
        """Sets the filiation of this RelationForListInvitationOutput.


        :param filiation: The filiation of this RelationForListInvitationOutput.  # noqa: E501
        :type: int
        """

        self._filiation = filiation

    @property
    def filiation_desc(self):
        """Gets the filiation_desc of this RelationForListInvitationOutput.  # noqa: E501


        :return: The filiation_desc of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._filiation_desc

    @filiation_desc.setter
    def filiation_desc(self, filiation_desc):
        """Sets the filiation_desc of this RelationForListInvitationOutput.


        :param filiation_desc: The filiation_desc of this RelationForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._filiation_desc = filiation_desc

    @property
    def major_account_id(self):
        """Gets the major_account_id of this RelationForListInvitationOutput.  # noqa: E501


        :return: The major_account_id of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._major_account_id

    @major_account_id.setter
    def major_account_id(self, major_account_id):
        """Sets the major_account_id of this RelationForListInvitationOutput.


        :param major_account_id: The major_account_id of this RelationForListInvitationOutput.  # noqa: E501
        :type: int
        """

        self._major_account_id = major_account_id

    @property
    def major_account_name(self):
        """Gets the major_account_name of this RelationForListInvitationOutput.  # noqa: E501


        :return: The major_account_name of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._major_account_name

    @major_account_name.setter
    def major_account_name(self, major_account_name):
        """Sets the major_account_name of this RelationForListInvitationOutput.


        :param major_account_name: The major_account_name of this RelationForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._major_account_name = major_account_name

    @property
    def relation(self):
        """Gets the relation of this RelationForListInvitationOutput.  # noqa: E501


        :return: The relation of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._relation

    @relation.setter
    def relation(self, relation):
        """Sets the relation of this RelationForListInvitationOutput.


        :param relation: The relation of this RelationForListInvitationOutput.  # noqa: E501
        :type: int
        """

        self._relation = relation

    @property
    def relation_desc(self):
        """Gets the relation_desc of this RelationForListInvitationOutput.  # noqa: E501


        :return: The relation_desc of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._relation_desc

    @relation_desc.setter
    def relation_desc(self, relation_desc):
        """Sets the relation_desc of this RelationForListInvitationOutput.


        :param relation_desc: The relation_desc of this RelationForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._relation_desc = relation_desc

    @property
    def relation_id(self):
        """Gets the relation_id of this RelationForListInvitationOutput.  # noqa: E501


        :return: The relation_id of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._relation_id

    @relation_id.setter
    def relation_id(self, relation_id):
        """Sets the relation_id of this RelationForListInvitationOutput.


        :param relation_id: The relation_id of this RelationForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._relation_id = relation_id

    @property
    def status(self):
        """Gets the status of this RelationForListInvitationOutput.  # noqa: E501


        :return: The status of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this RelationForListInvitationOutput.


        :param status: The status of this RelationForListInvitationOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def status_desc(self):
        """Gets the status_desc of this RelationForListInvitationOutput.  # noqa: E501


        :return: The status_desc of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._status_desc

    @status_desc.setter
    def status_desc(self, status_desc):
        """Sets the status_desc of this RelationForListInvitationOutput.


        :param status_desc: The status_desc of this RelationForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._status_desc = status_desc

    @property
    def sub_account_id(self):
        """Gets the sub_account_id of this RelationForListInvitationOutput.  # noqa: E501


        :return: The sub_account_id of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: int
        """
        return self._sub_account_id

    @sub_account_id.setter
    def sub_account_id(self, sub_account_id):
        """Sets the sub_account_id of this RelationForListInvitationOutput.


        :param sub_account_id: The sub_account_id of this RelationForListInvitationOutput.  # noqa: E501
        :type: int
        """

        self._sub_account_id = sub_account_id

    @property
    def sub_account_name(self):
        """Gets the sub_account_name of this RelationForListInvitationOutput.  # noqa: E501


        :return: The sub_account_name of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._sub_account_name

    @sub_account_name.setter
    def sub_account_name(self, sub_account_name):
        """Sets the sub_account_name of this RelationForListInvitationOutput.


        :param sub_account_name: The sub_account_name of this RelationForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._sub_account_name = sub_account_name

    @property
    def update_time(self):
        """Gets the update_time of this RelationForListInvitationOutput.  # noqa: E501


        :return: The update_time of this RelationForListInvitationOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this RelationForListInvitationOutput.


        :param update_time: The update_time of this RelationForListInvitationOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(RelationForListInvitationOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, RelationForListInvitationOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, RelationForListInvitationOutput):
            return True

        return self.to_dict() != other.to_dict()
