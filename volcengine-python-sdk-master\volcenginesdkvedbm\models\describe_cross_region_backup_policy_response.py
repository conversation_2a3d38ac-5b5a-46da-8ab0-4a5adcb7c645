# coding: utf-8

"""
    vedbm

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeCrossRegionBackupPolicyResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'cross_region_backup': 'bool',
        'instance_id': 'str',
        'retention': 'int',
        'target_region': 'str'
    }

    attribute_map = {
        'cross_region_backup': 'CrossRegionBackup',
        'instance_id': 'InstanceId',
        'retention': 'Retention',
        'target_region': 'TargetRegion'
    }

    def __init__(self, cross_region_backup=None, instance_id=None, retention=None, target_region=None, _configuration=None):  # noqa: E501
        """DescribeCrossRegionBackupPolicyResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._cross_region_backup = None
        self._instance_id = None
        self._retention = None
        self._target_region = None
        self.discriminator = None

        if cross_region_backup is not None:
            self.cross_region_backup = cross_region_backup
        if instance_id is not None:
            self.instance_id = instance_id
        if retention is not None:
            self.retention = retention
        if target_region is not None:
            self.target_region = target_region

    @property
    def cross_region_backup(self):
        """Gets the cross_region_backup of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501


        :return: The cross_region_backup of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501
        :rtype: bool
        """
        return self._cross_region_backup

    @cross_region_backup.setter
    def cross_region_backup(self, cross_region_backup):
        """Sets the cross_region_backup of this DescribeCrossRegionBackupPolicyResponse.


        :param cross_region_backup: The cross_region_backup of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501
        :type: bool
        """

        self._cross_region_backup = cross_region_backup

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501


        :return: The instance_id of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeCrossRegionBackupPolicyResponse.


        :param instance_id: The instance_id of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def retention(self):
        """Gets the retention of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501


        :return: The retention of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501
        :rtype: int
        """
        return self._retention

    @retention.setter
    def retention(self, retention):
        """Sets the retention of this DescribeCrossRegionBackupPolicyResponse.


        :param retention: The retention of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501
        :type: int
        """

        self._retention = retention

    @property
    def target_region(self):
        """Gets the target_region of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501


        :return: The target_region of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501
        :rtype: str
        """
        return self._target_region

    @target_region.setter
    def target_region(self, target_region):
        """Sets the target_region of this DescribeCrossRegionBackupPolicyResponse.


        :param target_region: The target_region of this DescribeCrossRegionBackupPolicyResponse.  # noqa: E501
        :type: str
        """

        self._target_region = target_region

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeCrossRegionBackupPolicyResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeCrossRegionBackupPolicyResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeCrossRegionBackupPolicyResponse):
            return True

        return self.to_dict() != other.to_dict()
