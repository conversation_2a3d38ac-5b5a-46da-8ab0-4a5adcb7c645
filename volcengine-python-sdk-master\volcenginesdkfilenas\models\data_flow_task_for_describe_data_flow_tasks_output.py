# coding: utf-8

"""
    filenas

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataFlowTaskForDescribeDataFlowTasksOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bucket_name': 'str',
        'bucket_prefix': 'str',
        'create_time': 'str',
        'data_flow_id': 'str',
        'end_time': 'str',
        'entry_list_file_key': 'str',
        'entry_list_file_name': 'str',
        'entry_list_file_url': 'str',
        'evict_policy': 'EvictPolicyForDescribeDataFlowTasksOutput',
        'export_policy': 'ExportPolicyForDescribeDataFlowTasksOutput',
        'file_system_id': 'str',
        'file_system_path': 'str',
        'id': 'str',
        'import_policy': 'ImportPolicyForDescribeDataFlowTasksOutput',
        'queue_exec': 'int',
        'queue_failed': 'int',
        'queue_len': 'int',
        'same_name_file_policy': 'str',
        'start_time': 'str',
        'status': 'str',
        'type': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'bucket_name': 'BucketName',
        'bucket_prefix': 'BucketPrefix',
        'create_time': 'CreateTime',
        'data_flow_id': 'DataFlowId',
        'end_time': 'EndTime',
        'entry_list_file_key': 'EntryListFileKey',
        'entry_list_file_name': 'EntryListFileName',
        'entry_list_file_url': 'EntryListFileUrl',
        'evict_policy': 'EvictPolicy',
        'export_policy': 'ExportPolicy',
        'file_system_id': 'FileSystemId',
        'file_system_path': 'FileSystemPath',
        'id': 'Id',
        'import_policy': 'ImportPolicy',
        'queue_exec': 'QueueExec',
        'queue_failed': 'QueueFailed',
        'queue_len': 'QueueLen',
        'same_name_file_policy': 'SameNameFilePolicy',
        'start_time': 'StartTime',
        'status': 'Status',
        'type': 'Type',
        'update_time': 'UpdateTime'
    }

    def __init__(self, bucket_name=None, bucket_prefix=None, create_time=None, data_flow_id=None, end_time=None, entry_list_file_key=None, entry_list_file_name=None, entry_list_file_url=None, evict_policy=None, export_policy=None, file_system_id=None, file_system_path=None, id=None, import_policy=None, queue_exec=None, queue_failed=None, queue_len=None, same_name_file_policy=None, start_time=None, status=None, type=None, update_time=None, _configuration=None):  # noqa: E501
        """DataFlowTaskForDescribeDataFlowTasksOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bucket_name = None
        self._bucket_prefix = None
        self._create_time = None
        self._data_flow_id = None
        self._end_time = None
        self._entry_list_file_key = None
        self._entry_list_file_name = None
        self._entry_list_file_url = None
        self._evict_policy = None
        self._export_policy = None
        self._file_system_id = None
        self._file_system_path = None
        self._id = None
        self._import_policy = None
        self._queue_exec = None
        self._queue_failed = None
        self._queue_len = None
        self._same_name_file_policy = None
        self._start_time = None
        self._status = None
        self._type = None
        self._update_time = None
        self.discriminator = None

        if bucket_name is not None:
            self.bucket_name = bucket_name
        if bucket_prefix is not None:
            self.bucket_prefix = bucket_prefix
        if create_time is not None:
            self.create_time = create_time
        if data_flow_id is not None:
            self.data_flow_id = data_flow_id
        if end_time is not None:
            self.end_time = end_time
        if entry_list_file_key is not None:
            self.entry_list_file_key = entry_list_file_key
        if entry_list_file_name is not None:
            self.entry_list_file_name = entry_list_file_name
        if entry_list_file_url is not None:
            self.entry_list_file_url = entry_list_file_url
        if evict_policy is not None:
            self.evict_policy = evict_policy
        if export_policy is not None:
            self.export_policy = export_policy
        if file_system_id is not None:
            self.file_system_id = file_system_id
        if file_system_path is not None:
            self.file_system_path = file_system_path
        if id is not None:
            self.id = id
        if import_policy is not None:
            self.import_policy = import_policy
        if queue_exec is not None:
            self.queue_exec = queue_exec
        if queue_failed is not None:
            self.queue_failed = queue_failed
        if queue_len is not None:
            self.queue_len = queue_len
        if same_name_file_policy is not None:
            self.same_name_file_policy = same_name_file_policy
        if start_time is not None:
            self.start_time = start_time
        if status is not None:
            self.status = status
        if type is not None:
            self.type = type
        if update_time is not None:
            self.update_time = update_time

    @property
    def bucket_name(self):
        """Gets the bucket_name of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The bucket_name of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._bucket_name

    @bucket_name.setter
    def bucket_name(self, bucket_name):
        """Sets the bucket_name of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param bucket_name: The bucket_name of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._bucket_name = bucket_name

    @property
    def bucket_prefix(self):
        """Gets the bucket_prefix of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The bucket_prefix of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._bucket_prefix

    @bucket_prefix.setter
    def bucket_prefix(self, bucket_prefix):
        """Sets the bucket_prefix of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param bucket_prefix: The bucket_prefix of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._bucket_prefix = bucket_prefix

    @property
    def create_time(self):
        """Gets the create_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The create_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_time

    @create_time.setter
    def create_time(self, create_time):
        """Sets the create_time of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param create_time: The create_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._create_time = create_time

    @property
    def data_flow_id(self):
        """Gets the data_flow_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The data_flow_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_flow_id

    @data_flow_id.setter
    def data_flow_id(self, data_flow_id):
        """Sets the data_flow_id of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param data_flow_id: The data_flow_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._data_flow_id = data_flow_id

    @property
    def end_time(self):
        """Gets the end_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The end_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._end_time

    @end_time.setter
    def end_time(self, end_time):
        """Sets the end_time of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param end_time: The end_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._end_time = end_time

    @property
    def entry_list_file_key(self):
        """Gets the entry_list_file_key of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The entry_list_file_key of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._entry_list_file_key

    @entry_list_file_key.setter
    def entry_list_file_key(self, entry_list_file_key):
        """Sets the entry_list_file_key of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param entry_list_file_key: The entry_list_file_key of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._entry_list_file_key = entry_list_file_key

    @property
    def entry_list_file_name(self):
        """Gets the entry_list_file_name of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The entry_list_file_name of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._entry_list_file_name

    @entry_list_file_name.setter
    def entry_list_file_name(self, entry_list_file_name):
        """Sets the entry_list_file_name of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param entry_list_file_name: The entry_list_file_name of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._entry_list_file_name = entry_list_file_name

    @property
    def entry_list_file_url(self):
        """Gets the entry_list_file_url of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The entry_list_file_url of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._entry_list_file_url

    @entry_list_file_url.setter
    def entry_list_file_url(self, entry_list_file_url):
        """Sets the entry_list_file_url of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param entry_list_file_url: The entry_list_file_url of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._entry_list_file_url = entry_list_file_url

    @property
    def evict_policy(self):
        """Gets the evict_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The evict_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: EvictPolicyForDescribeDataFlowTasksOutput
        """
        return self._evict_policy

    @evict_policy.setter
    def evict_policy(self, evict_policy):
        """Sets the evict_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param evict_policy: The evict_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: EvictPolicyForDescribeDataFlowTasksOutput
        """

        self._evict_policy = evict_policy

    @property
    def export_policy(self):
        """Gets the export_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The export_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: ExportPolicyForDescribeDataFlowTasksOutput
        """
        return self._export_policy

    @export_policy.setter
    def export_policy(self, export_policy):
        """Sets the export_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param export_policy: The export_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: ExportPolicyForDescribeDataFlowTasksOutput
        """

        self._export_policy = export_policy

    @property
    def file_system_id(self):
        """Gets the file_system_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The file_system_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_id

    @file_system_id.setter
    def file_system_id(self, file_system_id):
        """Sets the file_system_id of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param file_system_id: The file_system_id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._file_system_id = file_system_id

    @property
    def file_system_path(self):
        """Gets the file_system_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The file_system_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._file_system_path

    @file_system_path.setter
    def file_system_path(self, file_system_path):
        """Sets the file_system_path of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param file_system_path: The file_system_path of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._file_system_path = file_system_path

    @property
    def id(self):
        """Gets the id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param id: The id of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def import_policy(self):
        """Gets the import_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The import_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: ImportPolicyForDescribeDataFlowTasksOutput
        """
        return self._import_policy

    @import_policy.setter
    def import_policy(self, import_policy):
        """Sets the import_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param import_policy: The import_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: ImportPolicyForDescribeDataFlowTasksOutput
        """

        self._import_policy = import_policy

    @property
    def queue_exec(self):
        """Gets the queue_exec of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The queue_exec of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._queue_exec

    @queue_exec.setter
    def queue_exec(self, queue_exec):
        """Sets the queue_exec of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param queue_exec: The queue_exec of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: int
        """

        self._queue_exec = queue_exec

    @property
    def queue_failed(self):
        """Gets the queue_failed of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The queue_failed of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._queue_failed

    @queue_failed.setter
    def queue_failed(self, queue_failed):
        """Sets the queue_failed of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param queue_failed: The queue_failed of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: int
        """

        self._queue_failed = queue_failed

    @property
    def queue_len(self):
        """Gets the queue_len of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The queue_len of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: int
        """
        return self._queue_len

    @queue_len.setter
    def queue_len(self, queue_len):
        """Sets the queue_len of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param queue_len: The queue_len of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: int
        """

        self._queue_len = queue_len

    @property
    def same_name_file_policy(self):
        """Gets the same_name_file_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The same_name_file_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._same_name_file_policy

    @same_name_file_policy.setter
    def same_name_file_policy(self, same_name_file_policy):
        """Sets the same_name_file_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param same_name_file_policy: The same_name_file_policy of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._same_name_file_policy = same_name_file_policy

    @property
    def start_time(self):
        """Gets the start_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The start_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._start_time

    @start_time.setter
    def start_time(self, start_time):
        """Sets the start_time of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param start_time: The start_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._start_time = start_time

    @property
    def status(self):
        """Gets the status of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The status of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param status: The status of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def type(self):
        """Gets the type of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The type of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._type

    @type.setter
    def type(self, type):
        """Sets the type of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param type: The type of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._type = type

    @property
    def update_time(self):
        """Gets the update_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501


        :return: The update_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataFlowTaskForDescribeDataFlowTasksOutput.


        :param update_time: The update_time of this DataFlowTaskForDescribeDataFlowTasksOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataFlowTaskForDescribeDataFlowTasksOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataFlowTaskForDescribeDataFlowTasksOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataFlowTaskForDescribeDataFlowTasksOutput):
            return True

        return self.to_dict() != other.to_dict()
