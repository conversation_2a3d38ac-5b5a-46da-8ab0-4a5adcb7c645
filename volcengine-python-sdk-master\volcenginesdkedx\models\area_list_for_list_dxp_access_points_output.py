# coding: utf-8

"""
    edx

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AreaListForListDXPAccessPointsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'area': 'str',
        'city_list': 'list[CityListForListDXPAccessPointsOutput]'
    }

    attribute_map = {
        'area': 'Area',
        'city_list': 'CityList'
    }

    def __init__(self, area=None, city_list=None, _configuration=None):  # noqa: E501
        """AreaListForListDXPAccessPointsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._area = None
        self._city_list = None
        self.discriminator = None

        if area is not None:
            self.area = area
        if city_list is not None:
            self.city_list = city_list

    @property
    def area(self):
        """Gets the area of this AreaListForListDXPAccessPointsOutput.  # noqa: E501


        :return: The area of this AreaListForListDXPAccessPointsOutput.  # noqa: E501
        :rtype: str
        """
        return self._area

    @area.setter
    def area(self, area):
        """Sets the area of this AreaListForListDXPAccessPointsOutput.


        :param area: The area of this AreaListForListDXPAccessPointsOutput.  # noqa: E501
        :type: str
        """

        self._area = area

    @property
    def city_list(self):
        """Gets the city_list of this AreaListForListDXPAccessPointsOutput.  # noqa: E501


        :return: The city_list of this AreaListForListDXPAccessPointsOutput.  # noqa: E501
        :rtype: list[CityListForListDXPAccessPointsOutput]
        """
        return self._city_list

    @city_list.setter
    def city_list(self, city_list):
        """Sets the city_list of this AreaListForListDXPAccessPointsOutput.


        :param city_list: The city_list of this AreaListForListDXPAccessPointsOutput.  # noqa: E501
        :type: list[CityListForListDXPAccessPointsOutput]
        """

        self._city_list = city_list

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AreaListForListDXPAccessPointsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AreaListForListDXPAccessPointsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AreaListForListDXPAccessPointsOutput):
            return True

        return self.to_dict() != other.to_dict()
