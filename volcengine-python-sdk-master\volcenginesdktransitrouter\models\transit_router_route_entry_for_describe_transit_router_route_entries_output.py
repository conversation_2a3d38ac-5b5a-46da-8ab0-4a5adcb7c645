# coding: utf-8

"""
    transitrouter

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'as_path': 'list[str]',
        'creation_time': 'str',
        'description': 'str',
        'destination_cidr_block': 'str',
        'status': 'str',
        'transit_router_route_entry_id': 'str',
        'transit_router_route_entry_name': 'str',
        'transit_router_route_entry_next_hop_id': 'str',
        'transit_router_route_entry_next_hop_type': 'str',
        'transit_router_route_entry_type': 'str',
        'update_time': 'str'
    }

    attribute_map = {
        'as_path': 'AsPath',
        'creation_time': 'CreationTime',
        'description': 'Description',
        'destination_cidr_block': 'DestinationCidrBlock',
        'status': 'Status',
        'transit_router_route_entry_id': 'TransitRouterRouteEntryId',
        'transit_router_route_entry_name': 'TransitRouterRouteEntryName',
        'transit_router_route_entry_next_hop_id': 'TransitRouterRouteEntryNextHopId',
        'transit_router_route_entry_next_hop_type': 'TransitRouterRouteEntryNextHopType',
        'transit_router_route_entry_type': 'TransitRouterRouteEntryType',
        'update_time': 'UpdateTime'
    }

    def __init__(self, as_path=None, creation_time=None, description=None, destination_cidr_block=None, status=None, transit_router_route_entry_id=None, transit_router_route_entry_name=None, transit_router_route_entry_next_hop_id=None, transit_router_route_entry_next_hop_type=None, transit_router_route_entry_type=None, update_time=None, _configuration=None):  # noqa: E501
        """TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._as_path = None
        self._creation_time = None
        self._description = None
        self._destination_cidr_block = None
        self._status = None
        self._transit_router_route_entry_id = None
        self._transit_router_route_entry_name = None
        self._transit_router_route_entry_next_hop_id = None
        self._transit_router_route_entry_next_hop_type = None
        self._transit_router_route_entry_type = None
        self._update_time = None
        self.discriminator = None

        if as_path is not None:
            self.as_path = as_path
        if creation_time is not None:
            self.creation_time = creation_time
        if description is not None:
            self.description = description
        if destination_cidr_block is not None:
            self.destination_cidr_block = destination_cidr_block
        if status is not None:
            self.status = status
        if transit_router_route_entry_id is not None:
            self.transit_router_route_entry_id = transit_router_route_entry_id
        if transit_router_route_entry_name is not None:
            self.transit_router_route_entry_name = transit_router_route_entry_name
        if transit_router_route_entry_next_hop_id is not None:
            self.transit_router_route_entry_next_hop_id = transit_router_route_entry_next_hop_id
        if transit_router_route_entry_next_hop_type is not None:
            self.transit_router_route_entry_next_hop_type = transit_router_route_entry_next_hop_type
        if transit_router_route_entry_type is not None:
            self.transit_router_route_entry_type = transit_router_route_entry_type
        if update_time is not None:
            self.update_time = update_time

    @property
    def as_path(self):
        """Gets the as_path of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The as_path of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._as_path

    @as_path.setter
    def as_path(self, as_path):
        """Sets the as_path of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param as_path: The as_path of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: list[str]
        """

        self._as_path = as_path

    @property
    def creation_time(self):
        """Gets the creation_time of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The creation_time of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._creation_time

    @creation_time.setter
    def creation_time(self, creation_time):
        """Sets the creation_time of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param creation_time: The creation_time of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._creation_time = creation_time

    @property
    def description(self):
        """Gets the description of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The description of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param description: The description of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def destination_cidr_block(self):
        """Gets the destination_cidr_block of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The destination_cidr_block of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._destination_cidr_block

    @destination_cidr_block.setter
    def destination_cidr_block(self, destination_cidr_block):
        """Sets the destination_cidr_block of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param destination_cidr_block: The destination_cidr_block of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._destination_cidr_block = destination_cidr_block

    @property
    def status(self):
        """Gets the status of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The status of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param status: The status of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._status = status

    @property
    def transit_router_route_entry_id(self):
        """Gets the transit_router_route_entry_id of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The transit_router_route_entry_id of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_entry_id

    @transit_router_route_entry_id.setter
    def transit_router_route_entry_id(self, transit_router_route_entry_id):
        """Sets the transit_router_route_entry_id of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param transit_router_route_entry_id: The transit_router_route_entry_id of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_entry_id = transit_router_route_entry_id

    @property
    def transit_router_route_entry_name(self):
        """Gets the transit_router_route_entry_name of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The transit_router_route_entry_name of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_entry_name

    @transit_router_route_entry_name.setter
    def transit_router_route_entry_name(self, transit_router_route_entry_name):
        """Sets the transit_router_route_entry_name of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param transit_router_route_entry_name: The transit_router_route_entry_name of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_entry_name = transit_router_route_entry_name

    @property
    def transit_router_route_entry_next_hop_id(self):
        """Gets the transit_router_route_entry_next_hop_id of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The transit_router_route_entry_next_hop_id of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_entry_next_hop_id

    @transit_router_route_entry_next_hop_id.setter
    def transit_router_route_entry_next_hop_id(self, transit_router_route_entry_next_hop_id):
        """Sets the transit_router_route_entry_next_hop_id of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param transit_router_route_entry_next_hop_id: The transit_router_route_entry_next_hop_id of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_entry_next_hop_id = transit_router_route_entry_next_hop_id

    @property
    def transit_router_route_entry_next_hop_type(self):
        """Gets the transit_router_route_entry_next_hop_type of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The transit_router_route_entry_next_hop_type of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_entry_next_hop_type

    @transit_router_route_entry_next_hop_type.setter
    def transit_router_route_entry_next_hop_type(self, transit_router_route_entry_next_hop_type):
        """Sets the transit_router_route_entry_next_hop_type of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param transit_router_route_entry_next_hop_type: The transit_router_route_entry_next_hop_type of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_entry_next_hop_type = transit_router_route_entry_next_hop_type

    @property
    def transit_router_route_entry_type(self):
        """Gets the transit_router_route_entry_type of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The transit_router_route_entry_type of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._transit_router_route_entry_type

    @transit_router_route_entry_type.setter
    def transit_router_route_entry_type(self, transit_router_route_entry_type):
        """Sets the transit_router_route_entry_type of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param transit_router_route_entry_type: The transit_router_route_entry_type of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._transit_router_route_entry_type = transit_router_route_entry_type

    @property
    def update_time(self):
        """Gets the update_time of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501


        :return: The update_time of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.


        :param update_time: The update_time of this TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput.  # noqa: E501
        :type: str
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TransitRouterRouteEntryForDescribeTransitRouterRouteEntriesOutput):
            return True

        return self.to_dict() != other.to_dict()
