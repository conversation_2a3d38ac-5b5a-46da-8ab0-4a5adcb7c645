# coding: utf-8

"""
    alb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class PopLocationForDescribeLoadBalancersOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'pop_id': 'str',
        'pop_name': 'str'
    }

    attribute_map = {
        'pop_id': 'PopId',
        'pop_name': 'PopName'
    }

    def __init__(self, pop_id=None, pop_name=None, _configuration=None):  # noqa: E501
        """PopLocationForDescribeLoadBalancersOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._pop_id = None
        self._pop_name = None
        self.discriminator = None

        if pop_id is not None:
            self.pop_id = pop_id
        if pop_name is not None:
            self.pop_name = pop_name

    @property
    def pop_id(self):
        """Gets the pop_id of this PopLocationForDescribeLoadBalancersOutput.  # noqa: E501


        :return: The pop_id of this PopLocationForDescribeLoadBalancersOutput.  # noqa: E501
        :rtype: str
        """
        return self._pop_id

    @pop_id.setter
    def pop_id(self, pop_id):
        """Sets the pop_id of this PopLocationForDescribeLoadBalancersOutput.


        :param pop_id: The pop_id of this PopLocationForDescribeLoadBalancersOutput.  # noqa: E501
        :type: str
        """

        self._pop_id = pop_id

    @property
    def pop_name(self):
        """Gets the pop_name of this PopLocationForDescribeLoadBalancersOutput.  # noqa: E501


        :return: The pop_name of this PopLocationForDescribeLoadBalancersOutput.  # noqa: E501
        :rtype: str
        """
        return self._pop_name

    @pop_name.setter
    def pop_name(self, pop_name):
        """Sets the pop_name of this PopLocationForDescribeLoadBalancersOutput.


        :param pop_name: The pop_name of this PopLocationForDescribeLoadBalancersOutput.  # noqa: E501
        :type: str
        """

        self._pop_name = pop_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(PopLocationForDescribeLoadBalancersOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, PopLocationForDescribeLoadBalancersOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, PopLocationForDescribeLoadBalancersOutput):
            return True

        return self.to_dict() != other.to_dict()
