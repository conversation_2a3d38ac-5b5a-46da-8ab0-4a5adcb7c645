# coding: utf-8

"""
    iam

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CertificateForListSAMLProviderCertificatesOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'certificate_id': 'str',
        'create_date': 'str',
        'issuer': 'str',
        'not_after': 'str',
        'not_before': 'str',
        'serial_number': 'str',
        'signature_algorithm': 'str',
        'subject': 'str',
        'update_date': 'str',
        'version': 'str'
    }

    attribute_map = {
        'certificate_id': 'CertificateId',
        'create_date': 'CreateDate',
        'issuer': 'Issuer',
        'not_after': 'NotAfter',
        'not_before': 'NotBefore',
        'serial_number': 'SerialNumber',
        'signature_algorithm': 'SignatureAlgorithm',
        'subject': 'Subject',
        'update_date': 'UpdateDate',
        'version': 'Version'
    }

    def __init__(self, certificate_id=None, create_date=None, issuer=None, not_after=None, not_before=None, serial_number=None, signature_algorithm=None, subject=None, update_date=None, version=None, _configuration=None):  # noqa: E501
        """CertificateForListSAMLProviderCertificatesOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._certificate_id = None
        self._create_date = None
        self._issuer = None
        self._not_after = None
        self._not_before = None
        self._serial_number = None
        self._signature_algorithm = None
        self._subject = None
        self._update_date = None
        self._version = None
        self.discriminator = None

        if certificate_id is not None:
            self.certificate_id = certificate_id
        if create_date is not None:
            self.create_date = create_date
        if issuer is not None:
            self.issuer = issuer
        if not_after is not None:
            self.not_after = not_after
        if not_before is not None:
            self.not_before = not_before
        if serial_number is not None:
            self.serial_number = serial_number
        if signature_algorithm is not None:
            self.signature_algorithm = signature_algorithm
        if subject is not None:
            self.subject = subject
        if update_date is not None:
            self.update_date = update_date
        if version is not None:
            self.version = version

    @property
    def certificate_id(self):
        """Gets the certificate_id of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The certificate_id of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._certificate_id

    @certificate_id.setter
    def certificate_id(self, certificate_id):
        """Sets the certificate_id of this CertificateForListSAMLProviderCertificatesOutput.


        :param certificate_id: The certificate_id of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._certificate_id = certificate_id

    @property
    def create_date(self):
        """Gets the create_date of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The create_date of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._create_date

    @create_date.setter
    def create_date(self, create_date):
        """Sets the create_date of this CertificateForListSAMLProviderCertificatesOutput.


        :param create_date: The create_date of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._create_date = create_date

    @property
    def issuer(self):
        """Gets the issuer of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The issuer of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._issuer

    @issuer.setter
    def issuer(self, issuer):
        """Sets the issuer of this CertificateForListSAMLProviderCertificatesOutput.


        :param issuer: The issuer of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._issuer = issuer

    @property
    def not_after(self):
        """Gets the not_after of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The not_after of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._not_after

    @not_after.setter
    def not_after(self, not_after):
        """Sets the not_after of this CertificateForListSAMLProviderCertificatesOutput.


        :param not_after: The not_after of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._not_after = not_after

    @property
    def not_before(self):
        """Gets the not_before of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The not_before of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._not_before

    @not_before.setter
    def not_before(self, not_before):
        """Sets the not_before of this CertificateForListSAMLProviderCertificatesOutput.


        :param not_before: The not_before of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._not_before = not_before

    @property
    def serial_number(self):
        """Gets the serial_number of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The serial_number of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._serial_number

    @serial_number.setter
    def serial_number(self, serial_number):
        """Sets the serial_number of this CertificateForListSAMLProviderCertificatesOutput.


        :param serial_number: The serial_number of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._serial_number = serial_number

    @property
    def signature_algorithm(self):
        """Gets the signature_algorithm of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The signature_algorithm of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._signature_algorithm

    @signature_algorithm.setter
    def signature_algorithm(self, signature_algorithm):
        """Sets the signature_algorithm of this CertificateForListSAMLProviderCertificatesOutput.


        :param signature_algorithm: The signature_algorithm of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._signature_algorithm = signature_algorithm

    @property
    def subject(self):
        """Gets the subject of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The subject of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._subject

    @subject.setter
    def subject(self, subject):
        """Sets the subject of this CertificateForListSAMLProviderCertificatesOutput.


        :param subject: The subject of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._subject = subject

    @property
    def update_date(self):
        """Gets the update_date of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The update_date of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._update_date

    @update_date.setter
    def update_date(self, update_date):
        """Sets the update_date of this CertificateForListSAMLProviderCertificatesOutput.


        :param update_date: The update_date of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._update_date = update_date

    @property
    def version(self):
        """Gets the version of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501


        :return: The version of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :rtype: str
        """
        return self._version

    @version.setter
    def version(self, version):
        """Sets the version of this CertificateForListSAMLProviderCertificatesOutput.


        :param version: The version of this CertificateForListSAMLProviderCertificatesOutput.  # noqa: E501
        :type: str
        """

        self._version = version

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CertificateForListSAMLProviderCertificatesOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CertificateForListSAMLProviderCertificatesOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CertificateForListSAMLProviderCertificatesOutput):
            return True

        return self.to_dict() != other.to_dict()
