# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BindVPCForQueryPrivateZoneOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'id': 'str',
        'region': 'str',
        'region_name': 'str'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'id': 'ID',
        'region': 'Region',
        'region_name': 'RegionName'
    }

    def __init__(self, account_id=None, id=None, region=None, region_name=None, _configuration=None):  # noqa: E501
        """BindVPCForQueryPrivateZoneOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._id = None
        self._region = None
        self._region_name = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if id is not None:
            self.id = id
        if region is not None:
            self.region = region
        if region_name is not None:
            self.region_name = region_name

    @property
    def account_id(self):
        """Gets the account_id of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501


        :return: The account_id of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this BindVPCForQueryPrivateZoneOutput.


        :param account_id: The account_id of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def id(self):
        """Gets the id of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501


        :return: The id of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this BindVPCForQueryPrivateZoneOutput.


        :param id: The id of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def region(self):
        """Gets the region of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501


        :return: The region of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this BindVPCForQueryPrivateZoneOutput.


        :param region: The region of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def region_name(self):
        """Gets the region_name of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501


        :return: The region_name of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501
        :rtype: str
        """
        return self._region_name

    @region_name.setter
    def region_name(self, region_name):
        """Sets the region_name of this BindVPCForQueryPrivateZoneOutput.


        :param region_name: The region_name of this BindVPCForQueryPrivateZoneOutput.  # noqa: E501
        :type: str
        """

        self._region_name = region_name

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BindVPCForQueryPrivateZoneOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BindVPCForQueryPrivateZoneOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BindVPCForQueryPrivateZoneOutput):
            return True

        return self.to_dict() != other.to_dict()
