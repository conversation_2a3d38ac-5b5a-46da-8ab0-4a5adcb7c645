# coding: utf-8

"""
    mongodb

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DescribeDBInstanceSSLResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'instance_id': 'str',
        'is_valid': 'bool',
        'ssl_enable': 'bool',
        'ssl_expired_time': 'str'
    }

    attribute_map = {
        'instance_id': 'InstanceId',
        'is_valid': 'IsValid',
        'ssl_enable': 'SSLEnable',
        'ssl_expired_time': 'SSLExpiredTime'
    }

    def __init__(self, instance_id=None, is_valid=None, ssl_enable=None, ssl_expired_time=None, _configuration=None):  # noqa: E501
        """DescribeDBInstanceSSLResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._instance_id = None
        self._is_valid = None
        self._ssl_enable = None
        self._ssl_expired_time = None
        self.discriminator = None

        if instance_id is not None:
            self.instance_id = instance_id
        if is_valid is not None:
            self.is_valid = is_valid
        if ssl_enable is not None:
            self.ssl_enable = ssl_enable
        if ssl_expired_time is not None:
            self.ssl_expired_time = ssl_expired_time

    @property
    def instance_id(self):
        """Gets the instance_id of this DescribeDBInstanceSSLResponse.  # noqa: E501


        :return: The instance_id of this DescribeDBInstanceSSLResponse.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this DescribeDBInstanceSSLResponse.


        :param instance_id: The instance_id of this DescribeDBInstanceSSLResponse.  # noqa: E501
        :type: str
        """

        self._instance_id = instance_id

    @property
    def is_valid(self):
        """Gets the is_valid of this DescribeDBInstanceSSLResponse.  # noqa: E501


        :return: The is_valid of this DescribeDBInstanceSSLResponse.  # noqa: E501
        :rtype: bool
        """
        return self._is_valid

    @is_valid.setter
    def is_valid(self, is_valid):
        """Sets the is_valid of this DescribeDBInstanceSSLResponse.


        :param is_valid: The is_valid of this DescribeDBInstanceSSLResponse.  # noqa: E501
        :type: bool
        """

        self._is_valid = is_valid

    @property
    def ssl_enable(self):
        """Gets the ssl_enable of this DescribeDBInstanceSSLResponse.  # noqa: E501


        :return: The ssl_enable of this DescribeDBInstanceSSLResponse.  # noqa: E501
        :rtype: bool
        """
        return self._ssl_enable

    @ssl_enable.setter
    def ssl_enable(self, ssl_enable):
        """Sets the ssl_enable of this DescribeDBInstanceSSLResponse.


        :param ssl_enable: The ssl_enable of this DescribeDBInstanceSSLResponse.  # noqa: E501
        :type: bool
        """

        self._ssl_enable = ssl_enable

    @property
    def ssl_expired_time(self):
        """Gets the ssl_expired_time of this DescribeDBInstanceSSLResponse.  # noqa: E501


        :return: The ssl_expired_time of this DescribeDBInstanceSSLResponse.  # noqa: E501
        :rtype: str
        """
        return self._ssl_expired_time

    @ssl_expired_time.setter
    def ssl_expired_time(self, ssl_expired_time):
        """Sets the ssl_expired_time of this DescribeDBInstanceSSLResponse.


        :param ssl_expired_time: The ssl_expired_time of this DescribeDBInstanceSSLResponse.  # noqa: E501
        :type: str
        """

        self._ssl_expired_time = ssl_expired_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DescribeDBInstanceSSLResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DescribeDBInstanceSSLResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DescribeDBInstanceSSLResponse):
            return True

        return self.to_dict() != other.to_dict()
