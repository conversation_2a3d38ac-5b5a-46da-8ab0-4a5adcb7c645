# coding: utf-8

"""
    translate20250301

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class TextBlockForTranslateImageOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'back_color': 'list[int]',
        'detected_language': 'str',
        'fore_color': 'list[int]',
        'points': 'list[PointForTranslateImageOutput]',
        'text': 'str',
        'translation': 'str'
    }

    attribute_map = {
        'back_color': 'BackColor',
        'detected_language': 'DetectedLanguage',
        'fore_color': 'ForeColor',
        'points': 'Points',
        'text': 'Text',
        'translation': 'Translation'
    }

    def __init__(self, back_color=None, detected_language=None, fore_color=None, points=None, text=None, translation=None, _configuration=None):  # noqa: E501
        """TextBlockForTranslateImageOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._back_color = None
        self._detected_language = None
        self._fore_color = None
        self._points = None
        self._text = None
        self._translation = None
        self.discriminator = None

        if back_color is not None:
            self.back_color = back_color
        if detected_language is not None:
            self.detected_language = detected_language
        if fore_color is not None:
            self.fore_color = fore_color
        if points is not None:
            self.points = points
        if text is not None:
            self.text = text
        if translation is not None:
            self.translation = translation

    @property
    def back_color(self):
        """Gets the back_color of this TextBlockForTranslateImageOutput.  # noqa: E501


        :return: The back_color of this TextBlockForTranslateImageOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._back_color

    @back_color.setter
    def back_color(self, back_color):
        """Sets the back_color of this TextBlockForTranslateImageOutput.


        :param back_color: The back_color of this TextBlockForTranslateImageOutput.  # noqa: E501
        :type: list[int]
        """

        self._back_color = back_color

    @property
    def detected_language(self):
        """Gets the detected_language of this TextBlockForTranslateImageOutput.  # noqa: E501


        :return: The detected_language of this TextBlockForTranslateImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._detected_language

    @detected_language.setter
    def detected_language(self, detected_language):
        """Sets the detected_language of this TextBlockForTranslateImageOutput.


        :param detected_language: The detected_language of this TextBlockForTranslateImageOutput.  # noqa: E501
        :type: str
        """

        self._detected_language = detected_language

    @property
    def fore_color(self):
        """Gets the fore_color of this TextBlockForTranslateImageOutput.  # noqa: E501


        :return: The fore_color of this TextBlockForTranslateImageOutput.  # noqa: E501
        :rtype: list[int]
        """
        return self._fore_color

    @fore_color.setter
    def fore_color(self, fore_color):
        """Sets the fore_color of this TextBlockForTranslateImageOutput.


        :param fore_color: The fore_color of this TextBlockForTranslateImageOutput.  # noqa: E501
        :type: list[int]
        """

        self._fore_color = fore_color

    @property
    def points(self):
        """Gets the points of this TextBlockForTranslateImageOutput.  # noqa: E501


        :return: The points of this TextBlockForTranslateImageOutput.  # noqa: E501
        :rtype: list[PointForTranslateImageOutput]
        """
        return self._points

    @points.setter
    def points(self, points):
        """Sets the points of this TextBlockForTranslateImageOutput.


        :param points: The points of this TextBlockForTranslateImageOutput.  # noqa: E501
        :type: list[PointForTranslateImageOutput]
        """

        self._points = points

    @property
    def text(self):
        """Gets the text of this TextBlockForTranslateImageOutput.  # noqa: E501


        :return: The text of this TextBlockForTranslateImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._text

    @text.setter
    def text(self, text):
        """Sets the text of this TextBlockForTranslateImageOutput.


        :param text: The text of this TextBlockForTranslateImageOutput.  # noqa: E501
        :type: str
        """

        self._text = text

    @property
    def translation(self):
        """Gets the translation of this TextBlockForTranslateImageOutput.  # noqa: E501


        :return: The translation of this TextBlockForTranslateImageOutput.  # noqa: E501
        :rtype: str
        """
        return self._translation

    @translation.setter
    def translation(self, translation):
        """Sets the translation of this TextBlockForTranslateImageOutput.


        :param translation: The translation of this TextBlockForTranslateImageOutput.  # noqa: E501
        :type: str
        """

        self._translation = translation

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(TextBlockForTranslateImageOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, TextBlockForTranslateImageOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, TextBlockForTranslateImageOutput):
            return True

        return self.to_dict() != other.to_dict()
