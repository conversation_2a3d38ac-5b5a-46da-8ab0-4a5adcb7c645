# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class CreateDevInstanceRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'credential': 'CredentialForCreateDevInstanceInput',
        'description': 'str',
        'image': 'ImageForCreateDevInstanceInput',
        'name': 'str',
        'node_affinity_spec': 'NodeAffinitySpecForCreateDevInstanceInput',
        'numa_affinity': 'str',
        'ports': 'list[PortForCreateDevInstanceInput]',
        'resource_claim': 'ResourceClaimForCreateDevInstanceInput',
        'resource_queue_id': 'str',
        'ssh_public_key': 'str',
        'storages': 'list[StorageForCreateDevInstanceInput]',
        'volume': 'VolumeForCreateDevInstanceInput'
    }

    attribute_map = {
        'credential': 'Credential',
        'description': 'Description',
        'image': 'Image',
        'name': 'Name',
        'node_affinity_spec': 'NodeAffinitySpec',
        'numa_affinity': 'NumaAffinity',
        'ports': 'Ports',
        'resource_claim': 'ResourceClaim',
        'resource_queue_id': 'ResourceQueueId',
        'ssh_public_key': 'SshPublicKey',
        'storages': 'Storages',
        'volume': 'Volume'
    }

    def __init__(self, credential=None, description=None, image=None, name=None, node_affinity_spec=None, numa_affinity=None, ports=None, resource_claim=None, resource_queue_id=None, ssh_public_key=None, storages=None, volume=None, _configuration=None):  # noqa: E501
        """CreateDevInstanceRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._credential = None
        self._description = None
        self._image = None
        self._name = None
        self._node_affinity_spec = None
        self._numa_affinity = None
        self._ports = None
        self._resource_claim = None
        self._resource_queue_id = None
        self._ssh_public_key = None
        self._storages = None
        self._volume = None
        self.discriminator = None

        if credential is not None:
            self.credential = credential
        if description is not None:
            self.description = description
        if image is not None:
            self.image = image
        self.name = name
        if node_affinity_spec is not None:
            self.node_affinity_spec = node_affinity_spec
        if numa_affinity is not None:
            self.numa_affinity = numa_affinity
        if ports is not None:
            self.ports = ports
        if resource_claim is not None:
            self.resource_claim = resource_claim
        self.resource_queue_id = resource_queue_id
        if ssh_public_key is not None:
            self.ssh_public_key = ssh_public_key
        if storages is not None:
            self.storages = storages
        if volume is not None:
            self.volume = volume

    @property
    def credential(self):
        """Gets the credential of this CreateDevInstanceRequest.  # noqa: E501


        :return: The credential of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: CredentialForCreateDevInstanceInput
        """
        return self._credential

    @credential.setter
    def credential(self, credential):
        """Sets the credential of this CreateDevInstanceRequest.


        :param credential: The credential of this CreateDevInstanceRequest.  # noqa: E501
        :type: CredentialForCreateDevInstanceInput
        """

        self._credential = credential

    @property
    def description(self):
        """Gets the description of this CreateDevInstanceRequest.  # noqa: E501


        :return: The description of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this CreateDevInstanceRequest.


        :param description: The description of this CreateDevInstanceRequest.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def image(self):
        """Gets the image of this CreateDevInstanceRequest.  # noqa: E501


        :return: The image of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: ImageForCreateDevInstanceInput
        """
        return self._image

    @image.setter
    def image(self, image):
        """Sets the image of this CreateDevInstanceRequest.


        :param image: The image of this CreateDevInstanceRequest.  # noqa: E501
        :type: ImageForCreateDevInstanceInput
        """

        self._image = image

    @property
    def name(self):
        """Gets the name of this CreateDevInstanceRequest.  # noqa: E501


        :return: The name of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this CreateDevInstanceRequest.


        :param name: The name of this CreateDevInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and name is None:
            raise ValueError("Invalid value for `name`, must not be `None`")  # noqa: E501

        self._name = name

    @property
    def node_affinity_spec(self):
        """Gets the node_affinity_spec of this CreateDevInstanceRequest.  # noqa: E501


        :return: The node_affinity_spec of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: NodeAffinitySpecForCreateDevInstanceInput
        """
        return self._node_affinity_spec

    @node_affinity_spec.setter
    def node_affinity_spec(self, node_affinity_spec):
        """Sets the node_affinity_spec of this CreateDevInstanceRequest.


        :param node_affinity_spec: The node_affinity_spec of this CreateDevInstanceRequest.  # noqa: E501
        :type: NodeAffinitySpecForCreateDevInstanceInput
        """

        self._node_affinity_spec = node_affinity_spec

    @property
    def numa_affinity(self):
        """Gets the numa_affinity of this CreateDevInstanceRequest.  # noqa: E501


        :return: The numa_affinity of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._numa_affinity

    @numa_affinity.setter
    def numa_affinity(self, numa_affinity):
        """Sets the numa_affinity of this CreateDevInstanceRequest.


        :param numa_affinity: The numa_affinity of this CreateDevInstanceRequest.  # noqa: E501
        :type: str
        """

        self._numa_affinity = numa_affinity

    @property
    def ports(self):
        """Gets the ports of this CreateDevInstanceRequest.  # noqa: E501


        :return: The ports of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: list[PortForCreateDevInstanceInput]
        """
        return self._ports

    @ports.setter
    def ports(self, ports):
        """Sets the ports of this CreateDevInstanceRequest.


        :param ports: The ports of this CreateDevInstanceRequest.  # noqa: E501
        :type: list[PortForCreateDevInstanceInput]
        """

        self._ports = ports

    @property
    def resource_claim(self):
        """Gets the resource_claim of this CreateDevInstanceRequest.  # noqa: E501


        :return: The resource_claim of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: ResourceClaimForCreateDevInstanceInput
        """
        return self._resource_claim

    @resource_claim.setter
    def resource_claim(self, resource_claim):
        """Sets the resource_claim of this CreateDevInstanceRequest.


        :param resource_claim: The resource_claim of this CreateDevInstanceRequest.  # noqa: E501
        :type: ResourceClaimForCreateDevInstanceInput
        """

        self._resource_claim = resource_claim

    @property
    def resource_queue_id(self):
        """Gets the resource_queue_id of this CreateDevInstanceRequest.  # noqa: E501


        :return: The resource_queue_id of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_queue_id

    @resource_queue_id.setter
    def resource_queue_id(self, resource_queue_id):
        """Sets the resource_queue_id of this CreateDevInstanceRequest.


        :param resource_queue_id: The resource_queue_id of this CreateDevInstanceRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_queue_id is None:
            raise ValueError("Invalid value for `resource_queue_id`, must not be `None`")  # noqa: E501

        self._resource_queue_id = resource_queue_id

    @property
    def ssh_public_key(self):
        """Gets the ssh_public_key of this CreateDevInstanceRequest.  # noqa: E501


        :return: The ssh_public_key of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: str
        """
        return self._ssh_public_key

    @ssh_public_key.setter
    def ssh_public_key(self, ssh_public_key):
        """Sets the ssh_public_key of this CreateDevInstanceRequest.


        :param ssh_public_key: The ssh_public_key of this CreateDevInstanceRequest.  # noqa: E501
        :type: str
        """

        self._ssh_public_key = ssh_public_key

    @property
    def storages(self):
        """Gets the storages of this CreateDevInstanceRequest.  # noqa: E501


        :return: The storages of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: list[StorageForCreateDevInstanceInput]
        """
        return self._storages

    @storages.setter
    def storages(self, storages):
        """Sets the storages of this CreateDevInstanceRequest.


        :param storages: The storages of this CreateDevInstanceRequest.  # noqa: E501
        :type: list[StorageForCreateDevInstanceInput]
        """

        self._storages = storages

    @property
    def volume(self):
        """Gets the volume of this CreateDevInstanceRequest.  # noqa: E501


        :return: The volume of this CreateDevInstanceRequest.  # noqa: E501
        :rtype: VolumeForCreateDevInstanceInput
        """
        return self._volume

    @volume.setter
    def volume(self, volume):
        """Sets the volume of this CreateDevInstanceRequest.


        :param volume: The volume of this CreateDevInstanceRequest.  # noqa: E501
        :type: VolumeForCreateDevInstanceInput
        """

        self._volume = volume

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(CreateDevInstanceRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, CreateDevInstanceRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, CreateDevInstanceRequest):
            return True

        return self.to_dict() != other.to_dict()
