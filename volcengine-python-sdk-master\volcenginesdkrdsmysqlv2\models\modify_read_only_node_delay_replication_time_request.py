# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ModifyReadOnlyNodeDelayReplicationTimeRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'delay_replication_time': 'int',
        'instance_id': 'str',
        'node_id': 'str',
        'update_endpoint_ids': 'list[str]'
    }

    attribute_map = {
        'delay_replication_time': 'DelayReplicationTime',
        'instance_id': 'InstanceId',
        'node_id': 'NodeId',
        'update_endpoint_ids': 'UpdateEndpointIds'
    }

    def __init__(self, delay_replication_time=None, instance_id=None, node_id=None, update_endpoint_ids=None, _configuration=None):  # noqa: E501
        """ModifyReadOnlyNodeDelayReplicationTimeRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._delay_replication_time = None
        self._instance_id = None
        self._node_id = None
        self._update_endpoint_ids = None
        self.discriminator = None

        self.delay_replication_time = delay_replication_time
        self.instance_id = instance_id
        self.node_id = node_id
        if update_endpoint_ids is not None:
            self.update_endpoint_ids = update_endpoint_ids

    @property
    def delay_replication_time(self):
        """Gets the delay_replication_time of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501


        :return: The delay_replication_time of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501
        :rtype: int
        """
        return self._delay_replication_time

    @delay_replication_time.setter
    def delay_replication_time(self, delay_replication_time):
        """Sets the delay_replication_time of this ModifyReadOnlyNodeDelayReplicationTimeRequest.


        :param delay_replication_time: The delay_replication_time of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and delay_replication_time is None:
            raise ValueError("Invalid value for `delay_replication_time`, must not be `None`")  # noqa: E501

        self._delay_replication_time = delay_replication_time

    @property
    def instance_id(self):
        """Gets the instance_id of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501


        :return: The instance_id of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501
        :rtype: str
        """
        return self._instance_id

    @instance_id.setter
    def instance_id(self, instance_id):
        """Sets the instance_id of this ModifyReadOnlyNodeDelayReplicationTimeRequest.


        :param instance_id: The instance_id of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and instance_id is None:
            raise ValueError("Invalid value for `instance_id`, must not be `None`")  # noqa: E501

        self._instance_id = instance_id

    @property
    def node_id(self):
        """Gets the node_id of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501


        :return: The node_id of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this ModifyReadOnlyNodeDelayReplicationTimeRequest.


        :param node_id: The node_id of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and node_id is None:
            raise ValueError("Invalid value for `node_id`, must not be `None`")  # noqa: E501

        self._node_id = node_id

    @property
    def update_endpoint_ids(self):
        """Gets the update_endpoint_ids of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501


        :return: The update_endpoint_ids of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._update_endpoint_ids

    @update_endpoint_ids.setter
    def update_endpoint_ids(self, update_endpoint_ids):
        """Sets the update_endpoint_ids of this ModifyReadOnlyNodeDelayReplicationTimeRequest.


        :param update_endpoint_ids: The update_endpoint_ids of this ModifyReadOnlyNodeDelayReplicationTimeRequest.  # noqa: E501
        :type: list[str]
        """

        self._update_endpoint_ids = update_endpoint_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ModifyReadOnlyNodeDelayReplicationTimeRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ModifyReadOnlyNodeDelayReplicationTimeRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ModifyReadOnlyNodeDelayReplicationTimeRequest):
            return True

        return self.to_dict() != other.to_dict()
