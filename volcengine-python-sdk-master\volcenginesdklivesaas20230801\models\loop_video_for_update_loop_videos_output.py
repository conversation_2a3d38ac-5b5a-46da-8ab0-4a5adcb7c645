# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class LoopVideoForUpdateLoopVideosOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'index': 'int',
        'interaction_script_enabled': 'bool',
        'interaction_script_id': 'int',
        'offset': 'int',
        'review_status': 'int',
        'video_cover_image': 'str',
        'video_name': 'str',
        'video_vid': 'str'
    }

    attribute_map = {
        'index': 'Index',
        'interaction_script_enabled': 'InteractionScriptEnabled',
        'interaction_script_id': 'InteractionScriptId',
        'offset': 'Offset',
        'review_status': 'ReviewStatus',
        'video_cover_image': 'VideoCoverImage',
        'video_name': 'VideoName',
        'video_vid': 'VideoVid'
    }

    def __init__(self, index=None, interaction_script_enabled=None, interaction_script_id=None, offset=None, review_status=None, video_cover_image=None, video_name=None, video_vid=None, _configuration=None):  # noqa: E501
        """LoopVideoForUpdateLoopVideosOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._index = None
        self._interaction_script_enabled = None
        self._interaction_script_id = None
        self._offset = None
        self._review_status = None
        self._video_cover_image = None
        self._video_name = None
        self._video_vid = None
        self.discriminator = None

        if index is not None:
            self.index = index
        if interaction_script_enabled is not None:
            self.interaction_script_enabled = interaction_script_enabled
        if interaction_script_id is not None:
            self.interaction_script_id = interaction_script_id
        if offset is not None:
            self.offset = offset
        if review_status is not None:
            self.review_status = review_status
        if video_cover_image is not None:
            self.video_cover_image = video_cover_image
        if video_name is not None:
            self.video_name = video_name
        if video_vid is not None:
            self.video_vid = video_vid

    @property
    def index(self):
        """Gets the index of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501


        :return: The index of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :rtype: int
        """
        return self._index

    @index.setter
    def index(self, index):
        """Sets the index of this LoopVideoForUpdateLoopVideosOutput.


        :param index: The index of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :type: int
        """

        self._index = index

    @property
    def interaction_script_enabled(self):
        """Gets the interaction_script_enabled of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501


        :return: The interaction_script_enabled of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :rtype: bool
        """
        return self._interaction_script_enabled

    @interaction_script_enabled.setter
    def interaction_script_enabled(self, interaction_script_enabled):
        """Sets the interaction_script_enabled of this LoopVideoForUpdateLoopVideosOutput.


        :param interaction_script_enabled: The interaction_script_enabled of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :type: bool
        """

        self._interaction_script_enabled = interaction_script_enabled

    @property
    def interaction_script_id(self):
        """Gets the interaction_script_id of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501


        :return: The interaction_script_id of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :rtype: int
        """
        return self._interaction_script_id

    @interaction_script_id.setter
    def interaction_script_id(self, interaction_script_id):
        """Sets the interaction_script_id of this LoopVideoForUpdateLoopVideosOutput.


        :param interaction_script_id: The interaction_script_id of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :type: int
        """

        self._interaction_script_id = interaction_script_id

    @property
    def offset(self):
        """Gets the offset of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501


        :return: The offset of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :rtype: int
        """
        return self._offset

    @offset.setter
    def offset(self, offset):
        """Sets the offset of this LoopVideoForUpdateLoopVideosOutput.


        :param offset: The offset of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :type: int
        """

        self._offset = offset

    @property
    def review_status(self):
        """Gets the review_status of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501


        :return: The review_status of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :rtype: int
        """
        return self._review_status

    @review_status.setter
    def review_status(self, review_status):
        """Sets the review_status of this LoopVideoForUpdateLoopVideosOutput.


        :param review_status: The review_status of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :type: int
        """

        self._review_status = review_status

    @property
    def video_cover_image(self):
        """Gets the video_cover_image of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501


        :return: The video_cover_image of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :rtype: str
        """
        return self._video_cover_image

    @video_cover_image.setter
    def video_cover_image(self, video_cover_image):
        """Sets the video_cover_image of this LoopVideoForUpdateLoopVideosOutput.


        :param video_cover_image: The video_cover_image of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :type: str
        """

        self._video_cover_image = video_cover_image

    @property
    def video_name(self):
        """Gets the video_name of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501


        :return: The video_name of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :rtype: str
        """
        return self._video_name

    @video_name.setter
    def video_name(self, video_name):
        """Sets the video_name of this LoopVideoForUpdateLoopVideosOutput.


        :param video_name: The video_name of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :type: str
        """

        self._video_name = video_name

    @property
    def video_vid(self):
        """Gets the video_vid of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501


        :return: The video_vid of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :rtype: str
        """
        return self._video_vid

    @video_vid.setter
    def video_vid(self, video_vid):
        """Sets the video_vid of this LoopVideoForUpdateLoopVideosOutput.


        :param video_vid: The video_vid of this LoopVideoForUpdateLoopVideosOutput.  # noqa: E501
        :type: str
        """

        self._video_vid = video_vid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(LoopVideoForUpdateLoopVideosOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, LoopVideoForUpdateLoopVideosOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, LoopVideoForUpdateLoopVideosOutput):
            return True

        return self.to_dict() != other.to_dict()
