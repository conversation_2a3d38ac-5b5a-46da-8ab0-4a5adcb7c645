# coding: utf-8

"""
    ml_platform20240701

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class GetResourceGroupResponse(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'auto_renew_period': 'int',
        'charge_type': 'str',
        'description': 'str',
        'expire_time': 'str',
        'id': 'str',
        'm_gpu_enabled': 'bool',
        'name': 'str',
        'period_unit': 'str',
        'remain_auto_renew_times': 'int',
        'renew_type': 'int',
        'resource_allocated': 'ResourceAllocatedForGetResourceGroupOutput',
        'resource_capability': 'ResourceCapabilityForGetResourceGroupOutput',
        'status': 'StatusForGetResourceGroupOutput',
        'storage_config': 'StorageConfigForGetResourceGroupOutput',
        'workload_network_config': 'WorkloadNetworkConfigForGetResourceGroupOutput',
        'workload_network_mode': 'str',
        'zone_ids': 'list[str]'
    }

    attribute_map = {
        'auto_renew_period': 'AutoRenewPeriod',
        'charge_type': 'ChargeType',
        'description': 'Description',
        'expire_time': 'ExpireTime',
        'id': 'Id',
        'm_gpu_enabled': 'MGpuEnabled',
        'name': 'Name',
        'period_unit': 'PeriodUnit',
        'remain_auto_renew_times': 'RemainAutoRenewTimes',
        'renew_type': 'RenewType',
        'resource_allocated': 'ResourceAllocated',
        'resource_capability': 'ResourceCapability',
        'status': 'Status',
        'storage_config': 'StorageConfig',
        'workload_network_config': 'WorkloadNetworkConfig',
        'workload_network_mode': 'WorkloadNetworkMode',
        'zone_ids': 'ZoneIds'
    }

    def __init__(self, auto_renew_period=None, charge_type=None, description=None, expire_time=None, id=None, m_gpu_enabled=None, name=None, period_unit=None, remain_auto_renew_times=None, renew_type=None, resource_allocated=None, resource_capability=None, status=None, storage_config=None, workload_network_config=None, workload_network_mode=None, zone_ids=None, _configuration=None):  # noqa: E501
        """GetResourceGroupResponse - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._auto_renew_period = None
        self._charge_type = None
        self._description = None
        self._expire_time = None
        self._id = None
        self._m_gpu_enabled = None
        self._name = None
        self._period_unit = None
        self._remain_auto_renew_times = None
        self._renew_type = None
        self._resource_allocated = None
        self._resource_capability = None
        self._status = None
        self._storage_config = None
        self._workload_network_config = None
        self._workload_network_mode = None
        self._zone_ids = None
        self.discriminator = None

        if auto_renew_period is not None:
            self.auto_renew_period = auto_renew_period
        if charge_type is not None:
            self.charge_type = charge_type
        if description is not None:
            self.description = description
        if expire_time is not None:
            self.expire_time = expire_time
        if id is not None:
            self.id = id
        if m_gpu_enabled is not None:
            self.m_gpu_enabled = m_gpu_enabled
        if name is not None:
            self.name = name
        if period_unit is not None:
            self.period_unit = period_unit
        if remain_auto_renew_times is not None:
            self.remain_auto_renew_times = remain_auto_renew_times
        if renew_type is not None:
            self.renew_type = renew_type
        if resource_allocated is not None:
            self.resource_allocated = resource_allocated
        if resource_capability is not None:
            self.resource_capability = resource_capability
        if status is not None:
            self.status = status
        if storage_config is not None:
            self.storage_config = storage_config
        if workload_network_config is not None:
            self.workload_network_config = workload_network_config
        if workload_network_mode is not None:
            self.workload_network_mode = workload_network_mode
        if zone_ids is not None:
            self.zone_ids = zone_ids

    @property
    def auto_renew_period(self):
        """Gets the auto_renew_period of this GetResourceGroupResponse.  # noqa: E501


        :return: The auto_renew_period of this GetResourceGroupResponse.  # noqa: E501
        :rtype: int
        """
        return self._auto_renew_period

    @auto_renew_period.setter
    def auto_renew_period(self, auto_renew_period):
        """Sets the auto_renew_period of this GetResourceGroupResponse.


        :param auto_renew_period: The auto_renew_period of this GetResourceGroupResponse.  # noqa: E501
        :type: int
        """

        self._auto_renew_period = auto_renew_period

    @property
    def charge_type(self):
        """Gets the charge_type of this GetResourceGroupResponse.  # noqa: E501


        :return: The charge_type of this GetResourceGroupResponse.  # noqa: E501
        :rtype: str
        """
        return self._charge_type

    @charge_type.setter
    def charge_type(self, charge_type):
        """Sets the charge_type of this GetResourceGroupResponse.


        :param charge_type: The charge_type of this GetResourceGroupResponse.  # noqa: E501
        :type: str
        """

        self._charge_type = charge_type

    @property
    def description(self):
        """Gets the description of this GetResourceGroupResponse.  # noqa: E501


        :return: The description of this GetResourceGroupResponse.  # noqa: E501
        :rtype: str
        """
        return self._description

    @description.setter
    def description(self, description):
        """Sets the description of this GetResourceGroupResponse.


        :param description: The description of this GetResourceGroupResponse.  # noqa: E501
        :type: str
        """

        self._description = description

    @property
    def expire_time(self):
        """Gets the expire_time of this GetResourceGroupResponse.  # noqa: E501


        :return: The expire_time of this GetResourceGroupResponse.  # noqa: E501
        :rtype: str
        """
        return self._expire_time

    @expire_time.setter
    def expire_time(self, expire_time):
        """Sets the expire_time of this GetResourceGroupResponse.


        :param expire_time: The expire_time of this GetResourceGroupResponse.  # noqa: E501
        :type: str
        """

        self._expire_time = expire_time

    @property
    def id(self):
        """Gets the id of this GetResourceGroupResponse.  # noqa: E501


        :return: The id of this GetResourceGroupResponse.  # noqa: E501
        :rtype: str
        """
        return self._id

    @id.setter
    def id(self, id):
        """Sets the id of this GetResourceGroupResponse.


        :param id: The id of this GetResourceGroupResponse.  # noqa: E501
        :type: str
        """

        self._id = id

    @property
    def m_gpu_enabled(self):
        """Gets the m_gpu_enabled of this GetResourceGroupResponse.  # noqa: E501


        :return: The m_gpu_enabled of this GetResourceGroupResponse.  # noqa: E501
        :rtype: bool
        """
        return self._m_gpu_enabled

    @m_gpu_enabled.setter
    def m_gpu_enabled(self, m_gpu_enabled):
        """Sets the m_gpu_enabled of this GetResourceGroupResponse.


        :param m_gpu_enabled: The m_gpu_enabled of this GetResourceGroupResponse.  # noqa: E501
        :type: bool
        """

        self._m_gpu_enabled = m_gpu_enabled

    @property
    def name(self):
        """Gets the name of this GetResourceGroupResponse.  # noqa: E501


        :return: The name of this GetResourceGroupResponse.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this GetResourceGroupResponse.


        :param name: The name of this GetResourceGroupResponse.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def period_unit(self):
        """Gets the period_unit of this GetResourceGroupResponse.  # noqa: E501


        :return: The period_unit of this GetResourceGroupResponse.  # noqa: E501
        :rtype: str
        """
        return self._period_unit

    @period_unit.setter
    def period_unit(self, period_unit):
        """Sets the period_unit of this GetResourceGroupResponse.


        :param period_unit: The period_unit of this GetResourceGroupResponse.  # noqa: E501
        :type: str
        """

        self._period_unit = period_unit

    @property
    def remain_auto_renew_times(self):
        """Gets the remain_auto_renew_times of this GetResourceGroupResponse.  # noqa: E501


        :return: The remain_auto_renew_times of this GetResourceGroupResponse.  # noqa: E501
        :rtype: int
        """
        return self._remain_auto_renew_times

    @remain_auto_renew_times.setter
    def remain_auto_renew_times(self, remain_auto_renew_times):
        """Sets the remain_auto_renew_times of this GetResourceGroupResponse.


        :param remain_auto_renew_times: The remain_auto_renew_times of this GetResourceGroupResponse.  # noqa: E501
        :type: int
        """

        self._remain_auto_renew_times = remain_auto_renew_times

    @property
    def renew_type(self):
        """Gets the renew_type of this GetResourceGroupResponse.  # noqa: E501


        :return: The renew_type of this GetResourceGroupResponse.  # noqa: E501
        :rtype: int
        """
        return self._renew_type

    @renew_type.setter
    def renew_type(self, renew_type):
        """Sets the renew_type of this GetResourceGroupResponse.


        :param renew_type: The renew_type of this GetResourceGroupResponse.  # noqa: E501
        :type: int
        """

        self._renew_type = renew_type

    @property
    def resource_allocated(self):
        """Gets the resource_allocated of this GetResourceGroupResponse.  # noqa: E501


        :return: The resource_allocated of this GetResourceGroupResponse.  # noqa: E501
        :rtype: ResourceAllocatedForGetResourceGroupOutput
        """
        return self._resource_allocated

    @resource_allocated.setter
    def resource_allocated(self, resource_allocated):
        """Sets the resource_allocated of this GetResourceGroupResponse.


        :param resource_allocated: The resource_allocated of this GetResourceGroupResponse.  # noqa: E501
        :type: ResourceAllocatedForGetResourceGroupOutput
        """

        self._resource_allocated = resource_allocated

    @property
    def resource_capability(self):
        """Gets the resource_capability of this GetResourceGroupResponse.  # noqa: E501


        :return: The resource_capability of this GetResourceGroupResponse.  # noqa: E501
        :rtype: ResourceCapabilityForGetResourceGroupOutput
        """
        return self._resource_capability

    @resource_capability.setter
    def resource_capability(self, resource_capability):
        """Sets the resource_capability of this GetResourceGroupResponse.


        :param resource_capability: The resource_capability of this GetResourceGroupResponse.  # noqa: E501
        :type: ResourceCapabilityForGetResourceGroupOutput
        """

        self._resource_capability = resource_capability

    @property
    def status(self):
        """Gets the status of this GetResourceGroupResponse.  # noqa: E501


        :return: The status of this GetResourceGroupResponse.  # noqa: E501
        :rtype: StatusForGetResourceGroupOutput
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this GetResourceGroupResponse.


        :param status: The status of this GetResourceGroupResponse.  # noqa: E501
        :type: StatusForGetResourceGroupOutput
        """

        self._status = status

    @property
    def storage_config(self):
        """Gets the storage_config of this GetResourceGroupResponse.  # noqa: E501


        :return: The storage_config of this GetResourceGroupResponse.  # noqa: E501
        :rtype: StorageConfigForGetResourceGroupOutput
        """
        return self._storage_config

    @storage_config.setter
    def storage_config(self, storage_config):
        """Sets the storage_config of this GetResourceGroupResponse.


        :param storage_config: The storage_config of this GetResourceGroupResponse.  # noqa: E501
        :type: StorageConfigForGetResourceGroupOutput
        """

        self._storage_config = storage_config

    @property
    def workload_network_config(self):
        """Gets the workload_network_config of this GetResourceGroupResponse.  # noqa: E501


        :return: The workload_network_config of this GetResourceGroupResponse.  # noqa: E501
        :rtype: WorkloadNetworkConfigForGetResourceGroupOutput
        """
        return self._workload_network_config

    @workload_network_config.setter
    def workload_network_config(self, workload_network_config):
        """Sets the workload_network_config of this GetResourceGroupResponse.


        :param workload_network_config: The workload_network_config of this GetResourceGroupResponse.  # noqa: E501
        :type: WorkloadNetworkConfigForGetResourceGroupOutput
        """

        self._workload_network_config = workload_network_config

    @property
    def workload_network_mode(self):
        """Gets the workload_network_mode of this GetResourceGroupResponse.  # noqa: E501


        :return: The workload_network_mode of this GetResourceGroupResponse.  # noqa: E501
        :rtype: str
        """
        return self._workload_network_mode

    @workload_network_mode.setter
    def workload_network_mode(self, workload_network_mode):
        """Sets the workload_network_mode of this GetResourceGroupResponse.


        :param workload_network_mode: The workload_network_mode of this GetResourceGroupResponse.  # noqa: E501
        :type: str
        """

        self._workload_network_mode = workload_network_mode

    @property
    def zone_ids(self):
        """Gets the zone_ids of this GetResourceGroupResponse.  # noqa: E501


        :return: The zone_ids of this GetResourceGroupResponse.  # noqa: E501
        :rtype: list[str]
        """
        return self._zone_ids

    @zone_ids.setter
    def zone_ids(self, zone_ids):
        """Sets the zone_ids of this GetResourceGroupResponse.


        :param zone_ids: The zone_ids of this GetResourceGroupResponse.  # noqa: E501
        :type: list[str]
        """

        self._zone_ids = zone_ids

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(GetResourceGroupResponse, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, GetResourceGroupResponse):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, GetResourceGroupResponse):
            return True

        return self.to_dict() != other.to_dict()
