# coding: utf-8

"""
    rds_mysql_v2

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AccountForDescribeDBAccountsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_desc': 'str',
        'account_name': 'str',
        'account_privileges': 'list[AccountPrivilegeForDescribeDBAccountsOutput]',
        'account_privileges_sql': 'list[str]',
        'account_status': 'str',
        'account_type': 'str',
        'global_account_privileges': 'list[str]',
        'has_table_column_privilege_db_names': 'list[str]',
        'host': 'str'
    }

    attribute_map = {
        'account_desc': 'AccountDesc',
        'account_name': 'AccountName',
        'account_privileges': 'AccountPrivileges',
        'account_privileges_sql': 'AccountPrivilegesSQL',
        'account_status': 'AccountStatus',
        'account_type': 'AccountType',
        'global_account_privileges': 'GlobalAccountPrivileges',
        'has_table_column_privilege_db_names': 'HasTableColumnPrivilegeDBNames',
        'host': 'Host'
    }

    def __init__(self, account_desc=None, account_name=None, account_privileges=None, account_privileges_sql=None, account_status=None, account_type=None, global_account_privileges=None, has_table_column_privilege_db_names=None, host=None, _configuration=None):  # noqa: E501
        """AccountForDescribeDBAccountsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_desc = None
        self._account_name = None
        self._account_privileges = None
        self._account_privileges_sql = None
        self._account_status = None
        self._account_type = None
        self._global_account_privileges = None
        self._has_table_column_privilege_db_names = None
        self._host = None
        self.discriminator = None

        if account_desc is not None:
            self.account_desc = account_desc
        if account_name is not None:
            self.account_name = account_name
        if account_privileges is not None:
            self.account_privileges = account_privileges
        if account_privileges_sql is not None:
            self.account_privileges_sql = account_privileges_sql
        if account_status is not None:
            self.account_status = account_status
        if account_type is not None:
            self.account_type = account_type
        if global_account_privileges is not None:
            self.global_account_privileges = global_account_privileges
        if has_table_column_privilege_db_names is not None:
            self.has_table_column_privilege_db_names = has_table_column_privilege_db_names
        if host is not None:
            self.host = host

    @property
    def account_desc(self):
        """Gets the account_desc of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The account_desc of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_desc

    @account_desc.setter
    def account_desc(self, account_desc):
        """Sets the account_desc of this AccountForDescribeDBAccountsOutput.


        :param account_desc: The account_desc of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: str
        """

        self._account_desc = account_desc

    @property
    def account_name(self):
        """Gets the account_name of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The account_name of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_name

    @account_name.setter
    def account_name(self, account_name):
        """Sets the account_name of this AccountForDescribeDBAccountsOutput.


        :param account_name: The account_name of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: str
        """

        self._account_name = account_name

    @property
    def account_privileges(self):
        """Gets the account_privileges of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The account_privileges of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: list[AccountPrivilegeForDescribeDBAccountsOutput]
        """
        return self._account_privileges

    @account_privileges.setter
    def account_privileges(self, account_privileges):
        """Sets the account_privileges of this AccountForDescribeDBAccountsOutput.


        :param account_privileges: The account_privileges of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: list[AccountPrivilegeForDescribeDBAccountsOutput]
        """

        self._account_privileges = account_privileges

    @property
    def account_privileges_sql(self):
        """Gets the account_privileges_sql of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The account_privileges_sql of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._account_privileges_sql

    @account_privileges_sql.setter
    def account_privileges_sql(self, account_privileges_sql):
        """Sets the account_privileges_sql of this AccountForDescribeDBAccountsOutput.


        :param account_privileges_sql: The account_privileges_sql of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: list[str]
        """

        self._account_privileges_sql = account_privileges_sql

    @property
    def account_status(self):
        """Gets the account_status of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The account_status of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_status

    @account_status.setter
    def account_status(self, account_status):
        """Sets the account_status of this AccountForDescribeDBAccountsOutput.


        :param account_status: The account_status of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: str
        """

        self._account_status = account_status

    @property
    def account_type(self):
        """Gets the account_type of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The account_type of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_type

    @account_type.setter
    def account_type(self, account_type):
        """Sets the account_type of this AccountForDescribeDBAccountsOutput.


        :param account_type: The account_type of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: str
        """

        self._account_type = account_type

    @property
    def global_account_privileges(self):
        """Gets the global_account_privileges of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The global_account_privileges of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._global_account_privileges

    @global_account_privileges.setter
    def global_account_privileges(self, global_account_privileges):
        """Sets the global_account_privileges of this AccountForDescribeDBAccountsOutput.


        :param global_account_privileges: The global_account_privileges of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: list[str]
        """

        self._global_account_privileges = global_account_privileges

    @property
    def has_table_column_privilege_db_names(self):
        """Gets the has_table_column_privilege_db_names of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The has_table_column_privilege_db_names of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._has_table_column_privilege_db_names

    @has_table_column_privilege_db_names.setter
    def has_table_column_privilege_db_names(self, has_table_column_privilege_db_names):
        """Sets the has_table_column_privilege_db_names of this AccountForDescribeDBAccountsOutput.


        :param has_table_column_privilege_db_names: The has_table_column_privilege_db_names of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: list[str]
        """

        self._has_table_column_privilege_db_names = has_table_column_privilege_db_names

    @property
    def host(self):
        """Gets the host of this AccountForDescribeDBAccountsOutput.  # noqa: E501


        :return: The host of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :rtype: str
        """
        return self._host

    @host.setter
    def host(self, host):
        """Sets the host of this AccountForDescribeDBAccountsOutput.


        :param host: The host of this AccountForDescribeDBAccountsOutput.  # noqa: E501
        :type: str
        """

        self._host = host

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AccountForDescribeDBAccountsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AccountForDescribeDBAccountsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AccountForDescribeDBAccountsOutput):
            return True

        return self.to_dict() != other.to_dict()
