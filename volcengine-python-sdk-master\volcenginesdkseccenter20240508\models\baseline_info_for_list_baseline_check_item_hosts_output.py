# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class BaselineInfoForListBaselineCheckItemHostsOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'agent_id': 'str',
        'asset_id': 'str',
        'asset_name': 'str',
        'check_level': 'str',
        'cluster_id': 'str',
        'cluster_name': 'str',
        'detect_status': 'str',
        'eip_address': 'str',
        'failed_detail': 'str',
        'hostname': 'str',
        'node_id': 'str',
        'node_ip': 'str',
        'node_name': 'str',
        'primary_ip_address': 'str',
        'region': 'str',
        'result': 'str',
        'tag': 'list[str]',
        'whitelist_detail': 'str',
        'whitelist_status': 'bool'
    }

    attribute_map = {
        'agent_id': 'AgentID',
        'asset_id': 'AssetID',
        'asset_name': 'AssetName',
        'check_level': 'CheckLevel',
        'cluster_id': 'ClusterID',
        'cluster_name': 'ClusterName',
        'detect_status': 'DetectStatus',
        'eip_address': 'EipAddress',
        'failed_detail': 'FailedDetail',
        'hostname': 'Hostname',
        'node_id': 'NodeID',
        'node_ip': 'NodeIP',
        'node_name': 'NodeName',
        'primary_ip_address': 'PrimaryIpAddress',
        'region': 'Region',
        'result': 'Result',
        'tag': 'Tag',
        'whitelist_detail': 'WhitelistDetail',
        'whitelist_status': 'WhitelistStatus'
    }

    def __init__(self, agent_id=None, asset_id=None, asset_name=None, check_level=None, cluster_id=None, cluster_name=None, detect_status=None, eip_address=None, failed_detail=None, hostname=None, node_id=None, node_ip=None, node_name=None, primary_ip_address=None, region=None, result=None, tag=None, whitelist_detail=None, whitelist_status=None, _configuration=None):  # noqa: E501
        """BaselineInfoForListBaselineCheckItemHostsOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._agent_id = None
        self._asset_id = None
        self._asset_name = None
        self._check_level = None
        self._cluster_id = None
        self._cluster_name = None
        self._detect_status = None
        self._eip_address = None
        self._failed_detail = None
        self._hostname = None
        self._node_id = None
        self._node_ip = None
        self._node_name = None
        self._primary_ip_address = None
        self._region = None
        self._result = None
        self._tag = None
        self._whitelist_detail = None
        self._whitelist_status = None
        self.discriminator = None

        if agent_id is not None:
            self.agent_id = agent_id
        if asset_id is not None:
            self.asset_id = asset_id
        if asset_name is not None:
            self.asset_name = asset_name
        if check_level is not None:
            self.check_level = check_level
        if cluster_id is not None:
            self.cluster_id = cluster_id
        if cluster_name is not None:
            self.cluster_name = cluster_name
        if detect_status is not None:
            self.detect_status = detect_status
        if eip_address is not None:
            self.eip_address = eip_address
        if failed_detail is not None:
            self.failed_detail = failed_detail
        if hostname is not None:
            self.hostname = hostname
        if node_id is not None:
            self.node_id = node_id
        if node_ip is not None:
            self.node_ip = node_ip
        if node_name is not None:
            self.node_name = node_name
        if primary_ip_address is not None:
            self.primary_ip_address = primary_ip_address
        if region is not None:
            self.region = region
        if result is not None:
            self.result = result
        if tag is not None:
            self.tag = tag
        if whitelist_detail is not None:
            self.whitelist_detail = whitelist_detail
        if whitelist_status is not None:
            self.whitelist_status = whitelist_status

    @property
    def agent_id(self):
        """Gets the agent_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The agent_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param agent_id: The agent_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def asset_id(self):
        """Gets the asset_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The asset_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_id

    @asset_id.setter
    def asset_id(self, asset_id):
        """Sets the asset_id of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param asset_id: The asset_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._asset_id = asset_id

    @property
    def asset_name(self):
        """Gets the asset_name of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The asset_name of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._asset_name

    @asset_name.setter
    def asset_name(self, asset_name):
        """Sets the asset_name of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param asset_name: The asset_name of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._asset_name = asset_name

    @property
    def check_level(self):
        """Gets the check_level of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The check_level of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._check_level

    @check_level.setter
    def check_level(self, check_level):
        """Sets the check_level of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param check_level: The check_level of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._check_level = check_level

    @property
    def cluster_id(self):
        """Gets the cluster_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The cluster_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_id

    @cluster_id.setter
    def cluster_id(self, cluster_id):
        """Sets the cluster_id of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param cluster_id: The cluster_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_id = cluster_id

    @property
    def cluster_name(self):
        """Gets the cluster_name of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The cluster_name of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._cluster_name

    @cluster_name.setter
    def cluster_name(self, cluster_name):
        """Sets the cluster_name of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param cluster_name: The cluster_name of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._cluster_name = cluster_name

    @property
    def detect_status(self):
        """Gets the detect_status of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The detect_status of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._detect_status

    @detect_status.setter
    def detect_status(self, detect_status):
        """Sets the detect_status of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param detect_status: The detect_status of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._detect_status = detect_status

    @property
    def eip_address(self):
        """Gets the eip_address of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The eip_address of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._eip_address

    @eip_address.setter
    def eip_address(self, eip_address):
        """Sets the eip_address of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param eip_address: The eip_address of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._eip_address = eip_address

    @property
    def failed_detail(self):
        """Gets the failed_detail of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The failed_detail of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._failed_detail

    @failed_detail.setter
    def failed_detail(self, failed_detail):
        """Sets the failed_detail of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param failed_detail: The failed_detail of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._failed_detail = failed_detail

    @property
    def hostname(self):
        """Gets the hostname of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The hostname of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._hostname

    @hostname.setter
    def hostname(self, hostname):
        """Sets the hostname of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param hostname: The hostname of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._hostname = hostname

    @property
    def node_id(self):
        """Gets the node_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The node_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_id

    @node_id.setter
    def node_id(self, node_id):
        """Sets the node_id of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param node_id: The node_id of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._node_id = node_id

    @property
    def node_ip(self):
        """Gets the node_ip of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The node_ip of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_ip

    @node_ip.setter
    def node_ip(self, node_ip):
        """Sets the node_ip of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param node_ip: The node_ip of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._node_ip = node_ip

    @property
    def node_name(self):
        """Gets the node_name of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The node_name of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._node_name

    @node_name.setter
    def node_name(self, node_name):
        """Sets the node_name of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param node_name: The node_name of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._node_name = node_name

    @property
    def primary_ip_address(self):
        """Gets the primary_ip_address of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The primary_ip_address of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._primary_ip_address

    @primary_ip_address.setter
    def primary_ip_address(self, primary_ip_address):
        """Sets the primary_ip_address of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param primary_ip_address: The primary_ip_address of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._primary_ip_address = primary_ip_address

    @property
    def region(self):
        """Gets the region of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The region of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._region

    @region.setter
    def region(self, region):
        """Sets the region of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param region: The region of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._region = region

    @property
    def result(self):
        """Gets the result of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The result of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._result

    @result.setter
    def result(self, result):
        """Sets the result of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param result: The result of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._result = result

    @property
    def tag(self):
        """Gets the tag of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The tag of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tag

    @tag.setter
    def tag(self, tag):
        """Sets the tag of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param tag: The tag of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: list[str]
        """

        self._tag = tag

    @property
    def whitelist_detail(self):
        """Gets the whitelist_detail of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The whitelist_detail of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: str
        """
        return self._whitelist_detail

    @whitelist_detail.setter
    def whitelist_detail(self, whitelist_detail):
        """Sets the whitelist_detail of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param whitelist_detail: The whitelist_detail of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: str
        """

        self._whitelist_detail = whitelist_detail

    @property
    def whitelist_status(self):
        """Gets the whitelist_status of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501


        :return: The whitelist_status of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :rtype: bool
        """
        return self._whitelist_status

    @whitelist_status.setter
    def whitelist_status(self, whitelist_status):
        """Sets the whitelist_status of this BaselineInfoForListBaselineCheckItemHostsOutput.


        :param whitelist_status: The whitelist_status of this BaselineInfoForListBaselineCheckItemHostsOutput.  # noqa: E501
        :type: bool
        """

        self._whitelist_status = whitelist_status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(BaselineInfoForListBaselineCheckItemHostsOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, BaselineInfoForListBaselineCheckItemHostsOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, BaselineInfoForListBaselineCheckItemHostsOutput):
            return True

        return self.to_dict() != other.to_dict()
