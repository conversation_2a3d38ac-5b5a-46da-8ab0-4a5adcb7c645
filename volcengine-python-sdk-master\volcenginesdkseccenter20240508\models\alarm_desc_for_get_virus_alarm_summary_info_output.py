# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class AlarmDescForGetVirusAlarmSummaryInfoOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'alarm_handle_result': 'int',
        'alarm_id': 'str',
        'alert_desc': 'str',
        'alert_detail': 'str',
        'alert_tags': 'list[str]',
        'alert_type': 'str',
        'alert_type_us': 'str',
        'analyze_report': 'str',
        'analyze_report_name': 'str',
        'attack_id': 'str',
        'attack_id_list': 'str',
        'data_type_str': 'str',
        'desc': 'str',
        'docker': 'str',
        'error_reason': 'str',
        'handle_time': 'int',
        'handle_user': 'str',
        'hids_level': 'str',
        'insert_time': 'int',
        'kube_level': 'str',
        'rasp_config_name': 'str',
        'rasp_level': 'str',
        'related_config_id': 'str',
        'risk_desc': 'str',
        'rule_name': 'str',
        'status': 'int',
        'suggestion': 'str',
        'trace_id': 'str'
    }

    attribute_map = {
        'alarm_handle_result': 'AlarmHandleResult',
        'alarm_id': 'AlarmID',
        'alert_desc': 'AlertDesc',
        'alert_detail': 'AlertDetail',
        'alert_tags': 'AlertTags',
        'alert_type': 'AlertType',
        'alert_type_us': 'AlertTypeUs',
        'analyze_report': 'AnalyzeReport',
        'analyze_report_name': 'AnalyzeReportName',
        'attack_id': 'AttackID',
        'attack_id_list': 'AttackIDList',
        'data_type_str': 'DataTypeStr',
        'desc': 'Desc',
        'docker': 'Docker',
        'error_reason': 'ErrorReason',
        'handle_time': 'HandleTime',
        'handle_user': 'HandleUser',
        'hids_level': 'HidsLevel',
        'insert_time': 'InsertTime',
        'kube_level': 'KubeLevel',
        'rasp_config_name': 'RaspConfigName',
        'rasp_level': 'RaspLevel',
        'related_config_id': 'RelatedConfigID',
        'risk_desc': 'RiskDesc',
        'rule_name': 'RuleName',
        'status': 'Status',
        'suggestion': 'Suggestion',
        'trace_id': 'TraceID'
    }

    def __init__(self, alarm_handle_result=None, alarm_id=None, alert_desc=None, alert_detail=None, alert_tags=None, alert_type=None, alert_type_us=None, analyze_report=None, analyze_report_name=None, attack_id=None, attack_id_list=None, data_type_str=None, desc=None, docker=None, error_reason=None, handle_time=None, handle_user=None, hids_level=None, insert_time=None, kube_level=None, rasp_config_name=None, rasp_level=None, related_config_id=None, risk_desc=None, rule_name=None, status=None, suggestion=None, trace_id=None, _configuration=None):  # noqa: E501
        """AlarmDescForGetVirusAlarmSummaryInfoOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._alarm_handle_result = None
        self._alarm_id = None
        self._alert_desc = None
        self._alert_detail = None
        self._alert_tags = None
        self._alert_type = None
        self._alert_type_us = None
        self._analyze_report = None
        self._analyze_report_name = None
        self._attack_id = None
        self._attack_id_list = None
        self._data_type_str = None
        self._desc = None
        self._docker = None
        self._error_reason = None
        self._handle_time = None
        self._handle_user = None
        self._hids_level = None
        self._insert_time = None
        self._kube_level = None
        self._rasp_config_name = None
        self._rasp_level = None
        self._related_config_id = None
        self._risk_desc = None
        self._rule_name = None
        self._status = None
        self._suggestion = None
        self._trace_id = None
        self.discriminator = None

        if alarm_handle_result is not None:
            self.alarm_handle_result = alarm_handle_result
        if alarm_id is not None:
            self.alarm_id = alarm_id
        if alert_desc is not None:
            self.alert_desc = alert_desc
        if alert_detail is not None:
            self.alert_detail = alert_detail
        if alert_tags is not None:
            self.alert_tags = alert_tags
        if alert_type is not None:
            self.alert_type = alert_type
        if alert_type_us is not None:
            self.alert_type_us = alert_type_us
        if analyze_report is not None:
            self.analyze_report = analyze_report
        if analyze_report_name is not None:
            self.analyze_report_name = analyze_report_name
        if attack_id is not None:
            self.attack_id = attack_id
        if attack_id_list is not None:
            self.attack_id_list = attack_id_list
        if data_type_str is not None:
            self.data_type_str = data_type_str
        if desc is not None:
            self.desc = desc
        if docker is not None:
            self.docker = docker
        if error_reason is not None:
            self.error_reason = error_reason
        if handle_time is not None:
            self.handle_time = handle_time
        if handle_user is not None:
            self.handle_user = handle_user
        if hids_level is not None:
            self.hids_level = hids_level
        if insert_time is not None:
            self.insert_time = insert_time
        if kube_level is not None:
            self.kube_level = kube_level
        if rasp_config_name is not None:
            self.rasp_config_name = rasp_config_name
        if rasp_level is not None:
            self.rasp_level = rasp_level
        if related_config_id is not None:
            self.related_config_id = related_config_id
        if risk_desc is not None:
            self.risk_desc = risk_desc
        if rule_name is not None:
            self.rule_name = rule_name
        if status is not None:
            self.status = status
        if suggestion is not None:
            self.suggestion = suggestion
        if trace_id is not None:
            self.trace_id = trace_id

    @property
    def alarm_handle_result(self):
        """Gets the alarm_handle_result of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The alarm_handle_result of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._alarm_handle_result

    @alarm_handle_result.setter
    def alarm_handle_result(self, alarm_handle_result):
        """Sets the alarm_handle_result of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param alarm_handle_result: The alarm_handle_result of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: int
        """

        self._alarm_handle_result = alarm_handle_result

    @property
    def alarm_id(self):
        """Gets the alarm_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The alarm_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._alarm_id

    @alarm_id.setter
    def alarm_id(self, alarm_id):
        """Sets the alarm_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param alarm_id: The alarm_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._alarm_id = alarm_id

    @property
    def alert_desc(self):
        """Gets the alert_desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The alert_desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_desc

    @alert_desc.setter
    def alert_desc(self, alert_desc):
        """Sets the alert_desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param alert_desc: The alert_desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._alert_desc = alert_desc

    @property
    def alert_detail(self):
        """Gets the alert_detail of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The alert_detail of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_detail

    @alert_detail.setter
    def alert_detail(self, alert_detail):
        """Sets the alert_detail of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param alert_detail: The alert_detail of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._alert_detail = alert_detail

    @property
    def alert_tags(self):
        """Gets the alert_tags of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The alert_tags of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._alert_tags

    @alert_tags.setter
    def alert_tags(self, alert_tags):
        """Sets the alert_tags of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param alert_tags: The alert_tags of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: list[str]
        """

        self._alert_tags = alert_tags

    @property
    def alert_type(self):
        """Gets the alert_type of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The alert_type of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_type

    @alert_type.setter
    def alert_type(self, alert_type):
        """Sets the alert_type of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param alert_type: The alert_type of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._alert_type = alert_type

    @property
    def alert_type_us(self):
        """Gets the alert_type_us of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The alert_type_us of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._alert_type_us

    @alert_type_us.setter
    def alert_type_us(self, alert_type_us):
        """Sets the alert_type_us of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param alert_type_us: The alert_type_us of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._alert_type_us = alert_type_us

    @property
    def analyze_report(self):
        """Gets the analyze_report of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The analyze_report of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._analyze_report

    @analyze_report.setter
    def analyze_report(self, analyze_report):
        """Sets the analyze_report of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param analyze_report: The analyze_report of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._analyze_report = analyze_report

    @property
    def analyze_report_name(self):
        """Gets the analyze_report_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The analyze_report_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._analyze_report_name

    @analyze_report_name.setter
    def analyze_report_name(self, analyze_report_name):
        """Sets the analyze_report_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param analyze_report_name: The analyze_report_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._analyze_report_name = analyze_report_name

    @property
    def attack_id(self):
        """Gets the attack_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The attack_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._attack_id

    @attack_id.setter
    def attack_id(self, attack_id):
        """Sets the attack_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param attack_id: The attack_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._attack_id = attack_id

    @property
    def attack_id_list(self):
        """Gets the attack_id_list of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The attack_id_list of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._attack_id_list

    @attack_id_list.setter
    def attack_id_list(self, attack_id_list):
        """Sets the attack_id_list of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param attack_id_list: The attack_id_list of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._attack_id_list = attack_id_list

    @property
    def data_type_str(self):
        """Gets the data_type_str of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The data_type_str of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._data_type_str

    @data_type_str.setter
    def data_type_str(self, data_type_str):
        """Sets the data_type_str of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param data_type_str: The data_type_str of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._data_type_str = data_type_str

    @property
    def desc(self):
        """Gets the desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._desc

    @desc.setter
    def desc(self, desc):
        """Sets the desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param desc: The desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._desc = desc

    @property
    def docker(self):
        """Gets the docker of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The docker of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._docker

    @docker.setter
    def docker(self, docker):
        """Sets the docker of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param docker: The docker of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._docker = docker

    @property
    def error_reason(self):
        """Gets the error_reason of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The error_reason of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._error_reason

    @error_reason.setter
    def error_reason(self, error_reason):
        """Sets the error_reason of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param error_reason: The error_reason of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._error_reason = error_reason

    @property
    def handle_time(self):
        """Gets the handle_time of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The handle_time of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._handle_time

    @handle_time.setter
    def handle_time(self, handle_time):
        """Sets the handle_time of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param handle_time: The handle_time of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: int
        """

        self._handle_time = handle_time

    @property
    def handle_user(self):
        """Gets the handle_user of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The handle_user of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._handle_user

    @handle_user.setter
    def handle_user(self, handle_user):
        """Sets the handle_user of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param handle_user: The handle_user of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._handle_user = handle_user

    @property
    def hids_level(self):
        """Gets the hids_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The hids_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._hids_level

    @hids_level.setter
    def hids_level(self, hids_level):
        """Sets the hids_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param hids_level: The hids_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._hids_level = hids_level

    @property
    def insert_time(self):
        """Gets the insert_time of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The insert_time of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._insert_time

    @insert_time.setter
    def insert_time(self, insert_time):
        """Sets the insert_time of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param insert_time: The insert_time of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: int
        """

        self._insert_time = insert_time

    @property
    def kube_level(self):
        """Gets the kube_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The kube_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._kube_level

    @kube_level.setter
    def kube_level(self, kube_level):
        """Sets the kube_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param kube_level: The kube_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._kube_level = kube_level

    @property
    def rasp_config_name(self):
        """Gets the rasp_config_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The rasp_config_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._rasp_config_name

    @rasp_config_name.setter
    def rasp_config_name(self, rasp_config_name):
        """Sets the rasp_config_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param rasp_config_name: The rasp_config_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._rasp_config_name = rasp_config_name

    @property
    def rasp_level(self):
        """Gets the rasp_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The rasp_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._rasp_level

    @rasp_level.setter
    def rasp_level(self, rasp_level):
        """Sets the rasp_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param rasp_level: The rasp_level of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._rasp_level = rasp_level

    @property
    def related_config_id(self):
        """Gets the related_config_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The related_config_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._related_config_id

    @related_config_id.setter
    def related_config_id(self, related_config_id):
        """Sets the related_config_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param related_config_id: The related_config_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._related_config_id = related_config_id

    @property
    def risk_desc(self):
        """Gets the risk_desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The risk_desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._risk_desc

    @risk_desc.setter
    def risk_desc(self, risk_desc):
        """Sets the risk_desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param risk_desc: The risk_desc of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._risk_desc = risk_desc

    @property
    def rule_name(self):
        """Gets the rule_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The rule_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._rule_name

    @rule_name.setter
    def rule_name(self, rule_name):
        """Sets the rule_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param rule_name: The rule_name of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._rule_name = rule_name

    @property
    def status(self):
        """Gets the status of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The status of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: int
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param status: The status of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: int
        """

        self._status = status

    @property
    def suggestion(self):
        """Gets the suggestion of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The suggestion of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._suggestion

    @suggestion.setter
    def suggestion(self, suggestion):
        """Sets the suggestion of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param suggestion: The suggestion of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._suggestion = suggestion

    @property
    def trace_id(self):
        """Gets the trace_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501


        :return: The trace_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :rtype: str
        """
        return self._trace_id

    @trace_id.setter
    def trace_id(self, trace_id):
        """Sets the trace_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.


        :param trace_id: The trace_id of this AlarmDescForGetVirusAlarmSummaryInfoOutput.  # noqa: E501
        :type: str
        """

        self._trace_id = trace_id

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(AlarmDescForGetVirusAlarmSummaryInfoOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, AlarmDescForGetVirusAlarmSummaryInfoOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, AlarmDescForGetVirusAlarmSummaryInfoOutput):
            return True

        return self.to_dict() != other.to_dict()
