# coding: utf-8

"""
    livesaas20230801

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class UserTrackDataForGetAccountUserTrackDataOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'interact_data': 'InteractDataForGetAccountUserTrackDataOutput',
        'pay_data': 'PayDataForGetAccountUserTrackDataOutput',
        'user_track_sum': 'UserTrackSumForGetAccountUserTrackDataOutput',
        'user_tracks': 'list[UserTrackForGetAccountUserTrackDataOutput]'
    }

    attribute_map = {
        'interact_data': 'InteractData',
        'pay_data': 'PayData',
        'user_track_sum': 'UserTrackSum',
        'user_tracks': 'UserTracks'
    }

    def __init__(self, interact_data=None, pay_data=None, user_track_sum=None, user_tracks=None, _configuration=None):  # noqa: E501
        """UserTrackDataForGetAccountUserTrackDataOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._interact_data = None
        self._pay_data = None
        self._user_track_sum = None
        self._user_tracks = None
        self.discriminator = None

        if interact_data is not None:
            self.interact_data = interact_data
        if pay_data is not None:
            self.pay_data = pay_data
        if user_track_sum is not None:
            self.user_track_sum = user_track_sum
        if user_tracks is not None:
            self.user_tracks = user_tracks

    @property
    def interact_data(self):
        """Gets the interact_data of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The interact_data of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: InteractDataForGetAccountUserTrackDataOutput
        """
        return self._interact_data

    @interact_data.setter
    def interact_data(self, interact_data):
        """Sets the interact_data of this UserTrackDataForGetAccountUserTrackDataOutput.


        :param interact_data: The interact_data of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: InteractDataForGetAccountUserTrackDataOutput
        """

        self._interact_data = interact_data

    @property
    def pay_data(self):
        """Gets the pay_data of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The pay_data of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: PayDataForGetAccountUserTrackDataOutput
        """
        return self._pay_data

    @pay_data.setter
    def pay_data(self, pay_data):
        """Sets the pay_data of this UserTrackDataForGetAccountUserTrackDataOutput.


        :param pay_data: The pay_data of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: PayDataForGetAccountUserTrackDataOutput
        """

        self._pay_data = pay_data

    @property
    def user_track_sum(self):
        """Gets the user_track_sum of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The user_track_sum of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: UserTrackSumForGetAccountUserTrackDataOutput
        """
        return self._user_track_sum

    @user_track_sum.setter
    def user_track_sum(self, user_track_sum):
        """Sets the user_track_sum of this UserTrackDataForGetAccountUserTrackDataOutput.


        :param user_track_sum: The user_track_sum of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: UserTrackSumForGetAccountUserTrackDataOutput
        """

        self._user_track_sum = user_track_sum

    @property
    def user_tracks(self):
        """Gets the user_tracks of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501


        :return: The user_tracks of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501
        :rtype: list[UserTrackForGetAccountUserTrackDataOutput]
        """
        return self._user_tracks

    @user_tracks.setter
    def user_tracks(self, user_tracks):
        """Sets the user_tracks of this UserTrackDataForGetAccountUserTrackDataOutput.


        :param user_tracks: The user_tracks of this UserTrackDataForGetAccountUserTrackDataOutput.  # noqa: E501
        :type: list[UserTrackForGetAccountUserTrackDataOutput]
        """

        self._user_tracks = user_tracks

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(UserTrackDataForGetAccountUserTrackDataOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, UserTrackDataForGetAccountUserTrackDataOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, UserTrackDataForGetAccountUserTrackDataOutput):
            return True

        return self.to_dict() != other.to_dict()
