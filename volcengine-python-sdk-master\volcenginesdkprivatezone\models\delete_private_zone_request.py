# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DeletePrivateZoneRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'delete_when_empty': 'bool',
        'zid': 'int'
    }

    attribute_map = {
        'delete_when_empty': 'DeleteWhenEmpty',
        'zid': 'ZID'
    }

    def __init__(self, delete_when_empty=None, zid=None, _configuration=None):  # noqa: E501
        """DeletePrivateZoneRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._delete_when_empty = None
        self._zid = None
        self.discriminator = None

        self.delete_when_empty = delete_when_empty
        self.zid = zid

    @property
    def delete_when_empty(self):
        """Gets the delete_when_empty of this DeletePrivateZoneRequest.  # noqa: E501


        :return: The delete_when_empty of this DeletePrivateZoneRequest.  # noqa: E501
        :rtype: bool
        """
        return self._delete_when_empty

    @delete_when_empty.setter
    def delete_when_empty(self, delete_when_empty):
        """Sets the delete_when_empty of this DeletePrivateZoneRequest.


        :param delete_when_empty: The delete_when_empty of this DeletePrivateZoneRequest.  # noqa: E501
        :type: bool
        """
        if self._configuration.client_side_validation and delete_when_empty is None:
            raise ValueError("Invalid value for `delete_when_empty`, must not be `None`")  # noqa: E501

        self._delete_when_empty = delete_when_empty

    @property
    def zid(self):
        """Gets the zid of this DeletePrivateZoneRequest.  # noqa: E501


        :return: The zid of this DeletePrivateZoneRequest.  # noqa: E501
        :rtype: int
        """
        return self._zid

    @zid.setter
    def zid(self, zid):
        """Sets the zid of this DeletePrivateZoneRequest.


        :param zid: The zid of this DeletePrivateZoneRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and zid is None:
            raise ValueError("Invalid value for `zid`, must not be `None`")  # noqa: E501

        self._zid = zid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DeletePrivateZoneRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DeletePrivateZoneRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DeletePrivateZoneRequest):
            return True

        return self.to_dict() != other.to_dict()
