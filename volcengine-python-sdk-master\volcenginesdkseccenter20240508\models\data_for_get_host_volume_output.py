# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForGetHostVolumeOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'free': 'str',
        'fstype': 'str',
        'mount_point': 'str',
        'name': 'str',
        'total': 'str',
        'usage': 'str',
        'used': 'str'
    }

    attribute_map = {
        'free': 'Free',
        'fstype': 'Fstype',
        'mount_point': 'MountPoint',
        'name': 'Name',
        'total': 'Total',
        'usage': 'Usage',
        'used': 'Used'
    }

    def __init__(self, free=None, fstype=None, mount_point=None, name=None, total=None, usage=None, used=None, _configuration=None):  # noqa: E501
        """DataForGetHostVolumeOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._free = None
        self._fstype = None
        self._mount_point = None
        self._name = None
        self._total = None
        self._usage = None
        self._used = None
        self.discriminator = None

        if free is not None:
            self.free = free
        if fstype is not None:
            self.fstype = fstype
        if mount_point is not None:
            self.mount_point = mount_point
        if name is not None:
            self.name = name
        if total is not None:
            self.total = total
        if usage is not None:
            self.usage = usage
        if used is not None:
            self.used = used

    @property
    def free(self):
        """Gets the free of this DataForGetHostVolumeOutput.  # noqa: E501


        :return: The free of this DataForGetHostVolumeOutput.  # noqa: E501
        :rtype: str
        """
        return self._free

    @free.setter
    def free(self, free):
        """Sets the free of this DataForGetHostVolumeOutput.


        :param free: The free of this DataForGetHostVolumeOutput.  # noqa: E501
        :type: str
        """

        self._free = free

    @property
    def fstype(self):
        """Gets the fstype of this DataForGetHostVolumeOutput.  # noqa: E501


        :return: The fstype of this DataForGetHostVolumeOutput.  # noqa: E501
        :rtype: str
        """
        return self._fstype

    @fstype.setter
    def fstype(self, fstype):
        """Sets the fstype of this DataForGetHostVolumeOutput.


        :param fstype: The fstype of this DataForGetHostVolumeOutput.  # noqa: E501
        :type: str
        """

        self._fstype = fstype

    @property
    def mount_point(self):
        """Gets the mount_point of this DataForGetHostVolumeOutput.  # noqa: E501


        :return: The mount_point of this DataForGetHostVolumeOutput.  # noqa: E501
        :rtype: str
        """
        return self._mount_point

    @mount_point.setter
    def mount_point(self, mount_point):
        """Sets the mount_point of this DataForGetHostVolumeOutput.


        :param mount_point: The mount_point of this DataForGetHostVolumeOutput.  # noqa: E501
        :type: str
        """

        self._mount_point = mount_point

    @property
    def name(self):
        """Gets the name of this DataForGetHostVolumeOutput.  # noqa: E501


        :return: The name of this DataForGetHostVolumeOutput.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this DataForGetHostVolumeOutput.


        :param name: The name of this DataForGetHostVolumeOutput.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def total(self):
        """Gets the total of this DataForGetHostVolumeOutput.  # noqa: E501


        :return: The total of this DataForGetHostVolumeOutput.  # noqa: E501
        :rtype: str
        """
        return self._total

    @total.setter
    def total(self, total):
        """Sets the total of this DataForGetHostVolumeOutput.


        :param total: The total of this DataForGetHostVolumeOutput.  # noqa: E501
        :type: str
        """

        self._total = total

    @property
    def usage(self):
        """Gets the usage of this DataForGetHostVolumeOutput.  # noqa: E501


        :return: The usage of this DataForGetHostVolumeOutput.  # noqa: E501
        :rtype: str
        """
        return self._usage

    @usage.setter
    def usage(self, usage):
        """Sets the usage of this DataForGetHostVolumeOutput.


        :param usage: The usage of this DataForGetHostVolumeOutput.  # noqa: E501
        :type: str
        """

        self._usage = usage

    @property
    def used(self):
        """Gets the used of this DataForGetHostVolumeOutput.  # noqa: E501


        :return: The used of this DataForGetHostVolumeOutput.  # noqa: E501
        :rtype: str
        """
        return self._used

    @used.setter
    def used(self, used):
        """Sets the used of this DataForGetHostVolumeOutput.


        :param used: The used of this DataForGetHostVolumeOutput.  # noqa: E501
        :type: str
        """

        self._used = used

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForGetHostVolumeOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForGetHostVolumeOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForGetHostVolumeOutput):
            return True

        return self.to_dict() != other.to_dict()
