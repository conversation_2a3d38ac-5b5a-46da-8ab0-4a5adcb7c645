# coding: utf-8

# flake8: noqa
"""
    bytehouse_ce20240831

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


from __future__ import absolute_import

# import models into model package
from volcenginesdkbytehousece20240831.models.assign_role_request import AssignRoleRequest
from volcenginesdkbytehousece20240831.models.assign_role_response import AssignRoleResponse
from volcenginesdkbytehousece20240831.models.assignment_for_update_grants_for_user_input import AssignmentForUpdateGrantsForUserInput
from volcenginesdkbytehousece20240831.models.create_role_request import CreateRoleRequest
from volcenginesdkbytehousece20240831.models.create_role_response import CreateRoleResponse
from volcenginesdkbytehousece20240831.models.data_for_assign_role_output import DataForAssignRoleOutput
from volcenginesdkbytehousece20240831.models.data_for_create_role_output import DataForCreateRoleOutput
from volcenginesdkbytehousece20240831.models.data_for_delete_role_output import DataForDeleteRoleOutput
from volcenginesdkbytehousece20240831.models.data_for_delete_user_output import DataForDeleteUserOutput
from volcenginesdkbytehousece20240831.models.data_for_get_user_detail_output import DataForGetUserDetailOutput
from volcenginesdkbytehousece20240831.models.data_for_get_user_initial_password_output import DataForGetUserInitialPasswordOutput
from volcenginesdkbytehousece20240831.models.data_for_grant_privileges_to_user_by_id_output import DataForGrantPrivilegesToUserByIDOutput
from volcenginesdkbytehousece20240831.models.data_for_grant_privileges_to_user_by_name_output import DataForGrantPrivilegesToUserByNameOutput
from volcenginesdkbytehousece20240831.models.data_for_list_grant_users_for_role_output import DataForListGrantUsersForRoleOutput
from volcenginesdkbytehousece20240831.models.data_for_list_roles_output import DataForListRolesOutput
from volcenginesdkbytehousece20240831.models.data_for_list_users_output import DataForListUsersOutput
from volcenginesdkbytehousece20240831.models.data_for_revoke_privileges_for_user_by_id_output import DataForRevokePrivilegesForUserByIDOutput
from volcenginesdkbytehousece20240831.models.data_for_revoke_privileges_from_user_by_name_output import DataForRevokePrivilegesFromUserByNameOutput
from volcenginesdkbytehousece20240831.models.data_for_show_privileges_for_role_output import DataForShowPrivilegesForRoleOutput
from volcenginesdkbytehousece20240831.models.data_for_show_privileges_for_user_by_id_output import DataForShowPrivilegesForUserByIDOutput
from volcenginesdkbytehousece20240831.models.data_for_show_privileges_for_user_by_name_output import DataForShowPrivilegesForUserByNameOutput
from volcenginesdkbytehousece20240831.models.data_for_show_roles_for_user_output import DataForShowRolesForUserOutput
from volcenginesdkbytehousece20240831.models.data_for_unassign_role_output import DataForUnassignRoleOutput
from volcenginesdkbytehousece20240831.models.data_for_update_grants_for_role_output import DataForUpdateGrantsForRoleOutput
from volcenginesdkbytehousece20240831.models.data_for_update_grants_for_user_output import DataForUpdateGrantsForUserOutput
from volcenginesdkbytehousece20240831.models.data_for_update_user_output import DataForUpdateUserOutput
from volcenginesdkbytehousece20240831.models.delete_role_request import DeleteRoleRequest
from volcenginesdkbytehousece20240831.models.delete_role_response import DeleteRoleResponse
from volcenginesdkbytehousece20240831.models.delete_user_request import DeleteUserRequest
from volcenginesdkbytehousece20240831.models.delete_user_response import DeleteUserResponse
from volcenginesdkbytehousece20240831.models.delete_volc_instance_request import DeleteVolcInstanceRequest
from volcenginesdkbytehousece20240831.models.delete_volc_instance_response import DeleteVolcInstanceResponse
from volcenginesdkbytehousece20240831.models.get_user_detail_request import GetUserDetailRequest
from volcenginesdkbytehousece20240831.models.get_user_detail_response import GetUserDetailResponse
from volcenginesdkbytehousece20240831.models.get_user_initial_password_request import GetUserInitialPasswordRequest
from volcenginesdkbytehousece20240831.models.get_user_initial_password_response import GetUserInitialPasswordResponse
from volcenginesdkbytehousece20240831.models.grant_for_grant_privileges_to_user_by_id_input import GrantForGrantPrivilegesToUserByIDInput
from volcenginesdkbytehousece20240831.models.grant_for_grant_privileges_to_user_by_name_input import GrantForGrantPrivilegesToUserByNameInput
from volcenginesdkbytehousece20240831.models.grant_for_list_grant_users_for_role_output import GrantForListGrantUsersForRoleOutput
from volcenginesdkbytehousece20240831.models.grant_for_revoke_privileges_for_user_by_id_input import GrantForRevokePrivilegesForUserByIDInput
from volcenginesdkbytehousece20240831.models.grant_for_revoke_privileges_from_user_by_name_input import GrantForRevokePrivilegesFromUserByNameInput
from volcenginesdkbytehousece20240831.models.grant_for_show_privileges_for_role_output import GrantForShowPrivilegesForRoleOutput
from volcenginesdkbytehousece20240831.models.grant_for_show_privileges_for_user_by_id_output import GrantForShowPrivilegesForUserByIDOutput
from volcenginesdkbytehousece20240831.models.grant_for_show_privileges_for_user_by_name_output import GrantForShowPrivilegesForUserByNameOutput
from volcenginesdkbytehousece20240831.models.grant_for_update_grants_for_role_input import GrantForUpdateGrantsForRoleInput
from volcenginesdkbytehousece20240831.models.grant_for_update_grants_for_user_input import GrantForUpdateGrantsForUserInput
from volcenginesdkbytehousece20240831.models.grant_privileges_to_user_by_id_request import GrantPrivilegesToUserByIDRequest
from volcenginesdkbytehousece20240831.models.grant_privileges_to_user_by_id_response import GrantPrivilegesToUserByIDResponse
from volcenginesdkbytehousece20240831.models.grant_privileges_to_user_by_name_request import GrantPrivilegesToUserByNameRequest
from volcenginesdkbytehousece20240831.models.grant_privileges_to_user_by_name_response import GrantPrivilegesToUserByNameResponse
from volcenginesdkbytehousece20240831.models.list_grant_users_for_role_request import ListGrantUsersForRoleRequest
from volcenginesdkbytehousece20240831.models.list_grant_users_for_role_response import ListGrantUsersForRoleResponse
from volcenginesdkbytehousece20240831.models.list_roles_request import ListRolesRequest
from volcenginesdkbytehousece20240831.models.list_roles_response import ListRolesResponse
from volcenginesdkbytehousece20240831.models.list_users_request import ListUsersRequest
from volcenginesdkbytehousece20240831.models.list_users_response import ListUsersResponse
from volcenginesdkbytehousece20240831.models.meta_for_list_users_output import MetaForListUsersOutput
from volcenginesdkbytehousece20240831.models.privilege_for_grant_privileges_to_user_by_id_input import PrivilegeForGrantPrivilegesToUserByIDInput
from volcenginesdkbytehousece20240831.models.privilege_for_grant_privileges_to_user_by_name_input import PrivilegeForGrantPrivilegesToUserByNameInput
from volcenginesdkbytehousece20240831.models.privilege_for_revoke_privileges_for_user_by_id_input import PrivilegeForRevokePrivilegesForUserByIDInput
from volcenginesdkbytehousece20240831.models.privilege_for_revoke_privileges_from_user_by_name_input import PrivilegeForRevokePrivilegesFromUserByNameInput
from volcenginesdkbytehousece20240831.models.privilege_for_show_privileges_for_role_output import PrivilegeForShowPrivilegesForRoleOutput
from volcenginesdkbytehousece20240831.models.privilege_for_show_privileges_for_user_by_id_output import PrivilegeForShowPrivilegesForUserByIDOutput
from volcenginesdkbytehousece20240831.models.privilege_for_show_privileges_for_user_by_name_output import PrivilegeForShowPrivilegesForUserByNameOutput
from volcenginesdkbytehousece20240831.models.privilege_for_update_grants_for_role_input import PrivilegeForUpdateGrantsForRoleInput
from volcenginesdkbytehousece20240831.models.privilege_for_update_grants_for_user_input import PrivilegeForUpdateGrantsForUserInput
from volcenginesdkbytehousece20240831.models.revoke_privileges_for_user_by_id_request import RevokePrivilegesForUserByIDRequest
from volcenginesdkbytehousece20240831.models.revoke_privileges_for_user_by_id_response import RevokePrivilegesForUserByIDResponse
from volcenginesdkbytehousece20240831.models.revoke_privileges_from_user_by_name_request import RevokePrivilegesFromUserByNameRequest
from volcenginesdkbytehousece20240831.models.revoke_privileges_from_user_by_name_response import RevokePrivilegesFromUserByNameResponse
from volcenginesdkbytehousece20240831.models.role_for_create_role_output import RoleForCreateRoleOutput
from volcenginesdkbytehousece20240831.models.role_for_list_roles_output import RoleForListRolesOutput
from volcenginesdkbytehousece20240831.models.role_for_show_roles_for_user_output import RoleForShowRolesForUserOutput
from volcenginesdkbytehousece20240831.models.show_privileges_for_role_request import ShowPrivilegesForRoleRequest
from volcenginesdkbytehousece20240831.models.show_privileges_for_role_response import ShowPrivilegesForRoleResponse
from volcenginesdkbytehousece20240831.models.show_privileges_for_user_by_id_request import ShowPrivilegesForUserByIDRequest
from volcenginesdkbytehousece20240831.models.show_privileges_for_user_by_id_response import ShowPrivilegesForUserByIDResponse
from volcenginesdkbytehousece20240831.models.show_privileges_for_user_by_name_request import ShowPrivilegesForUserByNameRequest
from volcenginesdkbytehousece20240831.models.show_privileges_for_user_by_name_response import ShowPrivilegesForUserByNameResponse
from volcenginesdkbytehousece20240831.models.show_roles_for_user_request import ShowRolesForUserRequest
from volcenginesdkbytehousece20240831.models.show_roles_for_user_response import ShowRolesForUserResponse
from volcenginesdkbytehousece20240831.models.tag_for_tag_resources_input import TagForTagResourcesInput
from volcenginesdkbytehousece20240831.models.tag_resources_request import TagResourcesRequest
from volcenginesdkbytehousece20240831.models.tag_resources_response import TagResourcesResponse
from volcenginesdkbytehousece20240831.models.target_for_grant_privileges_to_user_by_id_input import TargetForGrantPrivilegesToUserByIDInput
from volcenginesdkbytehousece20240831.models.target_for_grant_privileges_to_user_by_name_input import TargetForGrantPrivilegesToUserByNameInput
from volcenginesdkbytehousece20240831.models.target_for_revoke_privileges_for_user_by_id_input import TargetForRevokePrivilegesForUserByIDInput
from volcenginesdkbytehousece20240831.models.target_for_revoke_privileges_from_user_by_name_input import TargetForRevokePrivilegesFromUserByNameInput
from volcenginesdkbytehousece20240831.models.target_for_update_grants_for_role_input import TargetForUpdateGrantsForRoleInput
from volcenginesdkbytehousece20240831.models.target_for_update_grants_for_user_input import TargetForUpdateGrantsForUserInput
from volcenginesdkbytehousece20240831.models.unassign_role_request import UnassignRoleRequest
from volcenginesdkbytehousece20240831.models.unassign_role_response import UnassignRoleResponse
from volcenginesdkbytehousece20240831.models.untag_resources_request import UntagResourcesRequest
from volcenginesdkbytehousece20240831.models.untag_resources_response import UntagResourcesResponse
from volcenginesdkbytehousece20240831.models.update_grants_for_role_request import UpdateGrantsForRoleRequest
from volcenginesdkbytehousece20240831.models.update_grants_for_role_response import UpdateGrantsForRoleResponse
from volcenginesdkbytehousece20240831.models.update_grants_for_user_request import UpdateGrantsForUserRequest
from volcenginesdkbytehousece20240831.models.update_grants_for_user_response import UpdateGrantsForUserResponse
from volcenginesdkbytehousece20240831.models.update_user_request import UpdateUserRequest
from volcenginesdkbytehousece20240831.models.update_user_response import UpdateUserResponse
from volcenginesdkbytehousece20240831.models.update_volc_instance_request import UpdateVolcInstanceRequest
from volcenginesdkbytehousece20240831.models.update_volc_instance_response import UpdateVolcInstanceResponse
from volcenginesdkbytehousece20240831.models.user_for_assign_role_input import UserForAssignRoleInput
from volcenginesdkbytehousece20240831.models.user_for_create_role_input import UserForCreateRoleInput
from volcenginesdkbytehousece20240831.models.user_for_get_user_detail_output import UserForGetUserDetailOutput
from volcenginesdkbytehousece20240831.models.user_for_get_user_initial_password_output import UserForGetUserInitialPasswordOutput
from volcenginesdkbytehousece20240831.models.user_for_list_users_output import UserForListUsersOutput
from volcenginesdkbytehousece20240831.models.user_for_unassign_role_input import UserForUnassignRoleInput
