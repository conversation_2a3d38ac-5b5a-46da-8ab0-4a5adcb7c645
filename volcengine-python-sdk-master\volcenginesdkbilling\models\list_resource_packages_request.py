# coding: utf-8

"""
    billing

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class ListResourcePackagesRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'effective_time_begin': 'str',
        'effective_time_end': 'str',
        'max_results': 'str',
        'next_token': 'str',
        'product': 'str',
        'resource_type': 'str',
        'status': 'str'
    }

    attribute_map = {
        'effective_time_begin': 'EffectiveTimeBegin',
        'effective_time_end': 'EffectiveTimeEnd',
        'max_results': 'MaxResults',
        'next_token': 'NextToken',
        'product': 'Product',
        'resource_type': 'ResourceType',
        'status': 'Status'
    }

    def __init__(self, effective_time_begin=None, effective_time_end=None, max_results=None, next_token=None, product=None, resource_type=None, status=None, _configuration=None):  # noqa: E501
        """ListResourcePackagesRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._effective_time_begin = None
        self._effective_time_end = None
        self._max_results = None
        self._next_token = None
        self._product = None
        self._resource_type = None
        self._status = None
        self.discriminator = None

        if effective_time_begin is not None:
            self.effective_time_begin = effective_time_begin
        if effective_time_end is not None:
            self.effective_time_end = effective_time_end
        self.max_results = max_results
        if next_token is not None:
            self.next_token = next_token
        if product is not None:
            self.product = product
        self.resource_type = resource_type
        if status is not None:
            self.status = status

    @property
    def effective_time_begin(self):
        """Gets the effective_time_begin of this ListResourcePackagesRequest.  # noqa: E501


        :return: The effective_time_begin of this ListResourcePackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._effective_time_begin

    @effective_time_begin.setter
    def effective_time_begin(self, effective_time_begin):
        """Sets the effective_time_begin of this ListResourcePackagesRequest.


        :param effective_time_begin: The effective_time_begin of this ListResourcePackagesRequest.  # noqa: E501
        :type: str
        """

        self._effective_time_begin = effective_time_begin

    @property
    def effective_time_end(self):
        """Gets the effective_time_end of this ListResourcePackagesRequest.  # noqa: E501


        :return: The effective_time_end of this ListResourcePackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._effective_time_end

    @effective_time_end.setter
    def effective_time_end(self, effective_time_end):
        """Sets the effective_time_end of this ListResourcePackagesRequest.


        :param effective_time_end: The effective_time_end of this ListResourcePackagesRequest.  # noqa: E501
        :type: str
        """

        self._effective_time_end = effective_time_end

    @property
    def max_results(self):
        """Gets the max_results of this ListResourcePackagesRequest.  # noqa: E501


        :return: The max_results of this ListResourcePackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._max_results

    @max_results.setter
    def max_results(self, max_results):
        """Sets the max_results of this ListResourcePackagesRequest.


        :param max_results: The max_results of this ListResourcePackagesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and max_results is None:
            raise ValueError("Invalid value for `max_results`, must not be `None`")  # noqa: E501

        self._max_results = max_results

    @property
    def next_token(self):
        """Gets the next_token of this ListResourcePackagesRequest.  # noqa: E501


        :return: The next_token of this ListResourcePackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._next_token

    @next_token.setter
    def next_token(self, next_token):
        """Sets the next_token of this ListResourcePackagesRequest.


        :param next_token: The next_token of this ListResourcePackagesRequest.  # noqa: E501
        :type: str
        """

        self._next_token = next_token

    @property
    def product(self):
        """Gets the product of this ListResourcePackagesRequest.  # noqa: E501


        :return: The product of this ListResourcePackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._product

    @product.setter
    def product(self, product):
        """Sets the product of this ListResourcePackagesRequest.


        :param product: The product of this ListResourcePackagesRequest.  # noqa: E501
        :type: str
        """

        self._product = product

    @property
    def resource_type(self):
        """Gets the resource_type of this ListResourcePackagesRequest.  # noqa: E501


        :return: The resource_type of this ListResourcePackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._resource_type

    @resource_type.setter
    def resource_type(self, resource_type):
        """Sets the resource_type of this ListResourcePackagesRequest.


        :param resource_type: The resource_type of this ListResourcePackagesRequest.  # noqa: E501
        :type: str
        """
        if self._configuration.client_side_validation and resource_type is None:
            raise ValueError("Invalid value for `resource_type`, must not be `None`")  # noqa: E501
        allowed_values = ["Package", "RI", "RSC"]  # noqa: E501
        if (self._configuration.client_side_validation and
                resource_type not in allowed_values):
            raise ValueError(
                "Invalid value for `resource_type` ({0}), must be one of {1}"  # noqa: E501
                .format(resource_type, allowed_values)
            )

        self._resource_type = resource_type

    @property
    def status(self):
        """Gets the status of this ListResourcePackagesRequest.  # noqa: E501


        :return: The status of this ListResourcePackagesRequest.  # noqa: E501
        :rtype: str
        """
        return self._status

    @status.setter
    def status(self, status):
        """Sets the status of this ListResourcePackagesRequest.


        :param status: The status of this ListResourcePackagesRequest.  # noqa: E501
        :type: str
        """
        allowed_values = ["Effective", "NotEffective", "FailedToCreate", "UsedUp", "Expired", "Refunded"]  # noqa: E501
        if (self._configuration.client_side_validation and
                status not in allowed_values):
            raise ValueError(
                "Invalid value for `status` ({0}), must be one of {1}"  # noqa: E501
                .format(status, allowed_values)
            )

        self._status = status

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(ListResourcePackagesRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, ListResourcePackagesRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, ListResourcePackagesRequest):
            return True

        return self.to_dict() != other.to_dict()
