# coding: utf-8

"""
    ark

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class InputFileTosLocationForCreateBatchInferenceJobInput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'bucket_name': 'str',
        'object_key': 'str'
    }

    attribute_map = {
        'bucket_name': 'BucketName',
        'object_key': 'ObjectKey'
    }

    def __init__(self, bucket_name=None, object_key=None, _configuration=None):  # noqa: E501
        """InputFileTosLocationForCreateBatchInferenceJobInput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._bucket_name = None
        self._object_key = None
        self.discriminator = None

        if bucket_name is not None:
            self.bucket_name = bucket_name
        if object_key is not None:
            self.object_key = object_key

    @property
    def bucket_name(self):
        """Gets the bucket_name of this InputFileTosLocationForCreateBatchInferenceJobInput.  # noqa: E501


        :return: The bucket_name of this InputFileTosLocationForCreateBatchInferenceJobInput.  # noqa: E501
        :rtype: str
        """
        return self._bucket_name

    @bucket_name.setter
    def bucket_name(self, bucket_name):
        """Sets the bucket_name of this InputFileTosLocationForCreateBatchInferenceJobInput.


        :param bucket_name: The bucket_name of this InputFileTosLocationForCreateBatchInferenceJobInput.  # noqa: E501
        :type: str
        """

        self._bucket_name = bucket_name

    @property
    def object_key(self):
        """Gets the object_key of this InputFileTosLocationForCreateBatchInferenceJobInput.  # noqa: E501


        :return: The object_key of this InputFileTosLocationForCreateBatchInferenceJobInput.  # noqa: E501
        :rtype: str
        """
        return self._object_key

    @object_key.setter
    def object_key(self, object_key):
        """Sets the object_key of this InputFileTosLocationForCreateBatchInferenceJobInput.


        :param object_key: The object_key of this InputFileTosLocationForCreateBatchInferenceJobInput.  # noqa: E501
        :type: str
        """

        self._object_key = object_key

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(InputFileTosLocationForCreateBatchInferenceJobInput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, InputFileTosLocationForCreateBatchInferenceJobInput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, InputFileTosLocationForCreateBatchInferenceJobInput):
            return True

        return self.to_dict() != other.to_dict()
