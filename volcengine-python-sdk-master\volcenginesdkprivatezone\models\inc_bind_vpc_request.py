# coding: utf-8

"""
    private_zone

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class IncBindVPCRequest(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'binds': 'list[BindForIncBindVPCInput]',
        'unbinds': 'list[UnbindForIncBindVPCInput]',
        'vpc_trns': 'list[str]',
        'zid': 'int'
    }

    attribute_map = {
        'binds': 'Binds',
        'unbinds': 'Unbinds',
        'vpc_trns': 'VpcTrns',
        'zid': 'ZID'
    }

    def __init__(self, binds=None, unbinds=None, vpc_trns=None, zid=None, _configuration=None):  # noqa: E501
        """IncBindVPCRequest - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._binds = None
        self._unbinds = None
        self._vpc_trns = None
        self._zid = None
        self.discriminator = None

        if binds is not None:
            self.binds = binds
        if unbinds is not None:
            self.unbinds = unbinds
        if vpc_trns is not None:
            self.vpc_trns = vpc_trns
        self.zid = zid

    @property
    def binds(self):
        """Gets the binds of this IncBindVPCRequest.  # noqa: E501


        :return: The binds of this IncBindVPCRequest.  # noqa: E501
        :rtype: list[BindForIncBindVPCInput]
        """
        return self._binds

    @binds.setter
    def binds(self, binds):
        """Sets the binds of this IncBindVPCRequest.


        :param binds: The binds of this IncBindVPCRequest.  # noqa: E501
        :type: list[BindForIncBindVPCInput]
        """

        self._binds = binds

    @property
    def unbinds(self):
        """Gets the unbinds of this IncBindVPCRequest.  # noqa: E501


        :return: The unbinds of this IncBindVPCRequest.  # noqa: E501
        :rtype: list[UnbindForIncBindVPCInput]
        """
        return self._unbinds

    @unbinds.setter
    def unbinds(self, unbinds):
        """Sets the unbinds of this IncBindVPCRequest.


        :param unbinds: The unbinds of this IncBindVPCRequest.  # noqa: E501
        :type: list[UnbindForIncBindVPCInput]
        """

        self._unbinds = unbinds

    @property
    def vpc_trns(self):
        """Gets the vpc_trns of this IncBindVPCRequest.  # noqa: E501


        :return: The vpc_trns of this IncBindVPCRequest.  # noqa: E501
        :rtype: list[str]
        """
        return self._vpc_trns

    @vpc_trns.setter
    def vpc_trns(self, vpc_trns):
        """Sets the vpc_trns of this IncBindVPCRequest.


        :param vpc_trns: The vpc_trns of this IncBindVPCRequest.  # noqa: E501
        :type: list[str]
        """

        self._vpc_trns = vpc_trns

    @property
    def zid(self):
        """Gets the zid of this IncBindVPCRequest.  # noqa: E501


        :return: The zid of this IncBindVPCRequest.  # noqa: E501
        :rtype: int
        """
        return self._zid

    @zid.setter
    def zid(self, zid):
        """Sets the zid of this IncBindVPCRequest.


        :param zid: The zid of this IncBindVPCRequest.  # noqa: E501
        :type: int
        """
        if self._configuration.client_side_validation and zid is None:
            raise ValueError("Invalid value for `zid`, must not be `None`")  # noqa: E501

        self._zid = zid

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(IncBindVPCRequest, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, IncBindVPCRequest):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, IncBindVPCRequest):
            return True

        return self.to_dict() != other.to_dict()
