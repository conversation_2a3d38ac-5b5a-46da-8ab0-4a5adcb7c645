# coding: utf-8

"""
    seccenter20240508

    No description provided (generated by Swagger Codegen https://github.com/swagger-api/swagger-codegen)  # noqa: E501

    OpenAPI spec version: common-version
    
    Generated by: https://github.com/swagger-api/swagger-codegen.git
"""


import pprint
import re  # noqa: F401

import six

from volcenginesdkcore.configuration import Configuration


class DataForListRaspConfigAgentInfosOutput(object):
    """NOTE: This class is auto generated by the swagger code generator program.

    Do not edit the class manually.
    """

    """
    Attributes:
      swagger_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    swagger_types = {
        'account_id': 'str',
        'agent_id': 'str',
        'cloud_provider': 'str',
        'ecs_instance': 'EcsInstanceForListRaspConfigAgentInfosOutput',
        'enable_status': 'str',
        'group_id': 'str',
        'group_path': 'str',
        'hit_white_process_count': 'int',
        'insert_time': 'int',
        'manual_close': 'bool',
        'platform': 'str',
        'process_count': 'int',
        'protect_abnormal_process_count': 'int',
        'protected_process_count': 'int',
        'rasp_configs': 'list[str]',
        'reason': 'str',
        'relate_config_name_list': 'list[str]',
        'tags': 'list[str]',
        'to_be_protected_process_count': 'int',
        'top_group_id': 'str',
        'update_time': 'int'
    }

    attribute_map = {
        'account_id': 'AccountID',
        'agent_id': 'AgentID',
        'cloud_provider': 'CloudProvider',
        'ecs_instance': 'EcsInstance',
        'enable_status': 'EnableStatus',
        'group_id': 'GroupID',
        'group_path': 'GroupPath',
        'hit_white_process_count': 'HitWhiteProcessCount',
        'insert_time': 'InsertTime',
        'manual_close': 'ManualClose',
        'platform': 'Platform',
        'process_count': 'ProcessCount',
        'protect_abnormal_process_count': 'ProtectAbnormalProcessCount',
        'protected_process_count': 'ProtectedProcessCount',
        'rasp_configs': 'RaspConfigs',
        'reason': 'Reason',
        'relate_config_name_list': 'RelateConfigNameList',
        'tags': 'Tags',
        'to_be_protected_process_count': 'ToBeProtectedProcessCount',
        'top_group_id': 'TopGroupID',
        'update_time': 'UpdateTime'
    }

    def __init__(self, account_id=None, agent_id=None, cloud_provider=None, ecs_instance=None, enable_status=None, group_id=None, group_path=None, hit_white_process_count=None, insert_time=None, manual_close=None, platform=None, process_count=None, protect_abnormal_process_count=None, protected_process_count=None, rasp_configs=None, reason=None, relate_config_name_list=None, tags=None, to_be_protected_process_count=None, top_group_id=None, update_time=None, _configuration=None):  # noqa: E501
        """DataForListRaspConfigAgentInfosOutput - a model defined in Swagger"""  # noqa: E501
        if _configuration is None:
            _configuration = Configuration()
        self._configuration = _configuration

        self._account_id = None
        self._agent_id = None
        self._cloud_provider = None
        self._ecs_instance = None
        self._enable_status = None
        self._group_id = None
        self._group_path = None
        self._hit_white_process_count = None
        self._insert_time = None
        self._manual_close = None
        self._platform = None
        self._process_count = None
        self._protect_abnormal_process_count = None
        self._protected_process_count = None
        self._rasp_configs = None
        self._reason = None
        self._relate_config_name_list = None
        self._tags = None
        self._to_be_protected_process_count = None
        self._top_group_id = None
        self._update_time = None
        self.discriminator = None

        if account_id is not None:
            self.account_id = account_id
        if agent_id is not None:
            self.agent_id = agent_id
        if cloud_provider is not None:
            self.cloud_provider = cloud_provider
        if ecs_instance is not None:
            self.ecs_instance = ecs_instance
        if enable_status is not None:
            self.enable_status = enable_status
        if group_id is not None:
            self.group_id = group_id
        if group_path is not None:
            self.group_path = group_path
        if hit_white_process_count is not None:
            self.hit_white_process_count = hit_white_process_count
        if insert_time is not None:
            self.insert_time = insert_time
        if manual_close is not None:
            self.manual_close = manual_close
        if platform is not None:
            self.platform = platform
        if process_count is not None:
            self.process_count = process_count
        if protect_abnormal_process_count is not None:
            self.protect_abnormal_process_count = protect_abnormal_process_count
        if protected_process_count is not None:
            self.protected_process_count = protected_process_count
        if rasp_configs is not None:
            self.rasp_configs = rasp_configs
        if reason is not None:
            self.reason = reason
        if relate_config_name_list is not None:
            self.relate_config_name_list = relate_config_name_list
        if tags is not None:
            self.tags = tags
        if to_be_protected_process_count is not None:
            self.to_be_protected_process_count = to_be_protected_process_count
        if top_group_id is not None:
            self.top_group_id = top_group_id
        if update_time is not None:
            self.update_time = update_time

    @property
    def account_id(self):
        """Gets the account_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The account_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._account_id

    @account_id.setter
    def account_id(self, account_id):
        """Sets the account_id of this DataForListRaspConfigAgentInfosOutput.


        :param account_id: The account_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: str
        """

        self._account_id = account_id

    @property
    def agent_id(self):
        """Gets the agent_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The agent_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._agent_id

    @agent_id.setter
    def agent_id(self, agent_id):
        """Sets the agent_id of this DataForListRaspConfigAgentInfosOutput.


        :param agent_id: The agent_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: str
        """

        self._agent_id = agent_id

    @property
    def cloud_provider(self):
        """Gets the cloud_provider of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The cloud_provider of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._cloud_provider

    @cloud_provider.setter
    def cloud_provider(self, cloud_provider):
        """Sets the cloud_provider of this DataForListRaspConfigAgentInfosOutput.


        :param cloud_provider: The cloud_provider of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: str
        """

        self._cloud_provider = cloud_provider

    @property
    def ecs_instance(self):
        """Gets the ecs_instance of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The ecs_instance of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: EcsInstanceForListRaspConfigAgentInfosOutput
        """
        return self._ecs_instance

    @ecs_instance.setter
    def ecs_instance(self, ecs_instance):
        """Sets the ecs_instance of this DataForListRaspConfigAgentInfosOutput.


        :param ecs_instance: The ecs_instance of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: EcsInstanceForListRaspConfigAgentInfosOutput
        """

        self._ecs_instance = ecs_instance

    @property
    def enable_status(self):
        """Gets the enable_status of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The enable_status of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._enable_status

    @enable_status.setter
    def enable_status(self, enable_status):
        """Sets the enable_status of this DataForListRaspConfigAgentInfosOutput.


        :param enable_status: The enable_status of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: str
        """

        self._enable_status = enable_status

    @property
    def group_id(self):
        """Gets the group_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The group_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_id

    @group_id.setter
    def group_id(self, group_id):
        """Sets the group_id of this DataForListRaspConfigAgentInfosOutput.


        :param group_id: The group_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: str
        """

        self._group_id = group_id

    @property
    def group_path(self):
        """Gets the group_path of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The group_path of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._group_path

    @group_path.setter
    def group_path(self, group_path):
        """Sets the group_path of this DataForListRaspConfigAgentInfosOutput.


        :param group_path: The group_path of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: str
        """

        self._group_path = group_path

    @property
    def hit_white_process_count(self):
        """Gets the hit_white_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The hit_white_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: int
        """
        return self._hit_white_process_count

    @hit_white_process_count.setter
    def hit_white_process_count(self, hit_white_process_count):
        """Sets the hit_white_process_count of this DataForListRaspConfigAgentInfosOutput.


        :param hit_white_process_count: The hit_white_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: int
        """

        self._hit_white_process_count = hit_white_process_count

    @property
    def insert_time(self):
        """Gets the insert_time of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The insert_time of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: int
        """
        return self._insert_time

    @insert_time.setter
    def insert_time(self, insert_time):
        """Sets the insert_time of this DataForListRaspConfigAgentInfosOutput.


        :param insert_time: The insert_time of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: int
        """

        self._insert_time = insert_time

    @property
    def manual_close(self):
        """Gets the manual_close of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The manual_close of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: bool
        """
        return self._manual_close

    @manual_close.setter
    def manual_close(self, manual_close):
        """Sets the manual_close of this DataForListRaspConfigAgentInfosOutput.


        :param manual_close: The manual_close of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: bool
        """

        self._manual_close = manual_close

    @property
    def platform(self):
        """Gets the platform of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The platform of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._platform

    @platform.setter
    def platform(self, platform):
        """Sets the platform of this DataForListRaspConfigAgentInfosOutput.


        :param platform: The platform of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: str
        """

        self._platform = platform

    @property
    def process_count(self):
        """Gets the process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: int
        """
        return self._process_count

    @process_count.setter
    def process_count(self, process_count):
        """Sets the process_count of this DataForListRaspConfigAgentInfosOutput.


        :param process_count: The process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: int
        """

        self._process_count = process_count

    @property
    def protect_abnormal_process_count(self):
        """Gets the protect_abnormal_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The protect_abnormal_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: int
        """
        return self._protect_abnormal_process_count

    @protect_abnormal_process_count.setter
    def protect_abnormal_process_count(self, protect_abnormal_process_count):
        """Sets the protect_abnormal_process_count of this DataForListRaspConfigAgentInfosOutput.


        :param protect_abnormal_process_count: The protect_abnormal_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: int
        """

        self._protect_abnormal_process_count = protect_abnormal_process_count

    @property
    def protected_process_count(self):
        """Gets the protected_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The protected_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: int
        """
        return self._protected_process_count

    @protected_process_count.setter
    def protected_process_count(self, protected_process_count):
        """Sets the protected_process_count of this DataForListRaspConfigAgentInfosOutput.


        :param protected_process_count: The protected_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: int
        """

        self._protected_process_count = protected_process_count

    @property
    def rasp_configs(self):
        """Gets the rasp_configs of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The rasp_configs of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._rasp_configs

    @rasp_configs.setter
    def rasp_configs(self, rasp_configs):
        """Sets the rasp_configs of this DataForListRaspConfigAgentInfosOutput.


        :param rasp_configs: The rasp_configs of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: list[str]
        """

        self._rasp_configs = rasp_configs

    @property
    def reason(self):
        """Gets the reason of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The reason of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._reason

    @reason.setter
    def reason(self, reason):
        """Sets the reason of this DataForListRaspConfigAgentInfosOutput.


        :param reason: The reason of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: str
        """

        self._reason = reason

    @property
    def relate_config_name_list(self):
        """Gets the relate_config_name_list of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The relate_config_name_list of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._relate_config_name_list

    @relate_config_name_list.setter
    def relate_config_name_list(self, relate_config_name_list):
        """Sets the relate_config_name_list of this DataForListRaspConfigAgentInfosOutput.


        :param relate_config_name_list: The relate_config_name_list of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: list[str]
        """

        self._relate_config_name_list = relate_config_name_list

    @property
    def tags(self):
        """Gets the tags of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The tags of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: list[str]
        """
        return self._tags

    @tags.setter
    def tags(self, tags):
        """Sets the tags of this DataForListRaspConfigAgentInfosOutput.


        :param tags: The tags of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: list[str]
        """

        self._tags = tags

    @property
    def to_be_protected_process_count(self):
        """Gets the to_be_protected_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The to_be_protected_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: int
        """
        return self._to_be_protected_process_count

    @to_be_protected_process_count.setter
    def to_be_protected_process_count(self, to_be_protected_process_count):
        """Sets the to_be_protected_process_count of this DataForListRaspConfigAgentInfosOutput.


        :param to_be_protected_process_count: The to_be_protected_process_count of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: int
        """

        self._to_be_protected_process_count = to_be_protected_process_count

    @property
    def top_group_id(self):
        """Gets the top_group_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The top_group_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: str
        """
        return self._top_group_id

    @top_group_id.setter
    def top_group_id(self, top_group_id):
        """Sets the top_group_id of this DataForListRaspConfigAgentInfosOutput.


        :param top_group_id: The top_group_id of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: str
        """

        self._top_group_id = top_group_id

    @property
    def update_time(self):
        """Gets the update_time of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501


        :return: The update_time of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :rtype: int
        """
        return self._update_time

    @update_time.setter
    def update_time(self, update_time):
        """Sets the update_time of this DataForListRaspConfigAgentInfosOutput.


        :param update_time: The update_time of this DataForListRaspConfigAgentInfosOutput.  # noqa: E501
        :type: int
        """

        self._update_time = update_time

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.swagger_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value
        if issubclass(DataForListRaspConfigAgentInfosOutput, dict):
            for key, value in self.items():
                result[key] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, DataForListRaspConfigAgentInfosOutput):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, DataForListRaspConfigAgentInfosOutput):
            return True

        return self.to_dict() != other.to_dict()
